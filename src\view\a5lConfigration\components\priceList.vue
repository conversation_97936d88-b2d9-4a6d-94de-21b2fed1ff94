<template>
  <div class="wrapper" @touchend="onTouchend">
    <div>
      <div class="name">车型 | {{ modelName }}</div>
      <div>{{ modelPrice | finalFormatPriceDesc }}</div>
    </div>

    <div>
      <div class="name">外饰 | {{ outColorName }}</div>
      <div>{{ outColorPrice | finalFormatPriceDescPrefixPlus }}</div>
    </div>

    <div>
      <div class="name">外饰 | {{ hubName }}</div>
      <div>{{ hubPrice | finalFormatPriceDescPrefixPlus }}</div>
    </div>

    <div v-show="!isEmptyObj(currentII)">
      <div class="name">内饰 | {{ currentII.packet ? currentII.packet.labelValueNameZh : currentII.externalFeatureNameZh }}</div>
      {{ currentII.packet ? currentII.packet.featurePrice : currentII.featurePrice | finalFormatPriceDescPrefixPlus }}
    </div>

    <div v-show="!isEmptyObj(currentEih)">
      <div class="name">内饰 | {{ eihName }}</div>
      <div>{{ eihPrice | finalFormatPriceDescPrefixPlus }}</div>
    </div>

    <div v-for="item in currentOptions" :key="item.featureCode">
      <div class="name">选装 | {{ item.externalFeatureNameZh }}</div>
      <div class="relative">
        {{ item.featurePrice | finalFormatPriceDescPrefixPlus }}
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import { isEmptyObj } from "@/utils";

export default {
  data() {
    return {};
  },
  computed: {
    ...mapGetters([]),
    ...mapState({
      userCoupon: (state) => state.configration.userCoupon,

      currentModelLineData: (state) => state.configration.currentModelLineData,
      carIdx: (state) => state.configration.carIdx,
      currentVersion: (state) => state.configration.currentVersion,
      currentII: (state) => state.configration.currentII,
      currentEih: (state) => state.configration.currentEih,

      modelName: (state) => state.configration.currentModelLineData?.externalFullModelName ?? "",
      modelPrice: (state) => state.configration.currentModelLineData?.msrp ?? 0,

      outColorName: (state) => state.configration.currentExterior.externalFeatureNameZh ?? "",
      outColorPrice: (state) => state.configration.currentExterior?.featurePrice ?? 0,

      hubName: (state) => state.configration.currentHub.packet ? state.configration.currentHub.packet.labelValueNameZh : state.configration.currentHub.externalFeatureNameZh,
      hubPrice: (state) => state.configration.currentHub.packet ? state.configration.currentHub.packet.featurePrice : state.configration.currentHub.featurePrice,

      eihName: (state) => state.configration.currentEih?.externalFeatureNameZh ?? "",
      eihPrice: (state) => state.configration.currentEih?.featurePrice ?? 0,

      currentOptions: (state) => state.configration.selectedOptions,
    }),
  },

  mounted() {
  },
  methods: {
    isEmptyObj,
    onTouchend() {
      // 埋点
      this.clickPopupSensors("滑动价格明细");
    },

    // 埋点
    clickPopupSensors(operationType) {
      const {
        engine, customSeriesName
      } = this.currentModelLineData;
      const param = {
        source_module: "H5",
        car_series: "A5L",
        car_type: "C",
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: "定制交付", // 快速交付|定制交付
        operation_type: operationType
      };
      this.$sensors.track("CC_CarConfiguration_Operate", param);
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";

.wrapper {
  margin-top: 16px;
  max-height: 230px;
  overflow-y: auto;

  > div {
    line-height: 20px;
    margin-bottom: 12px;
    .c-flex-between;
  }
}

.name {
  color: #000000;
  opacity: 0.5;
  max-width: 72%;
  .c-font12 ;
}

.relative {
  position: relative;

  > .down-price {
    position: absolute;
    right: 130%;
    top: 50%;
    transform: translateY(-50%);
    color: #CCCCCC;
    font-size: 12px;
    text-decoration: line-through;
  }
}

</style>
