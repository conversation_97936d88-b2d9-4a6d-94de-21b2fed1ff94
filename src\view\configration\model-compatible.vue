<template>
  <div class="model-compatible">
    <div class="_title">
      兼容机型可能更新不及时，以下信息仅供参考
    </div>
    <div style="padding: 0 16px;">
      <div
        class="_list"
        v-for="(item,index) in modelArray"
        :key="index"
      >
        {{ item }}
      </div>
    </div>
    <div class="_foot-tag">
      鉴于技术开发状态的调整，支持机型可能更新
    </div>
  </div>
</template>

<script>
import { getModelCompatibleList } from '@/api/api'

export default {
  data() {
    return {
      modelArray: []
    }
  },
  async mounted() {
    const { data } = await getModelCompatibleList()
    if (data.code === '00' && data.data.configValue) {
      this.modelArray = data.data.configValue.split(',')
    }
  },
  methods: {

  }
}
</script>

<style lang='less' scoped>
  .model-compatible {
    box-sizing: border-box;
    position: relative;

    ._title {
      border-bottom: 1px #E5E5E5 solid;
      height: 48px;
      width: 100%;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #666666;
      padding: 0 16px;
    }

    ._list {
      width: 100%;
      height: 56px;
      border-bottom: 1px #E5E5E5 solid;
      display: flex;
      align-items: center;
      font-size: 16px;

    }

    ._list:last-child {
      border-bottom: none
    }

    ._foot-tag {
      border-top: 1px #E5E5E5 solid;
      height: 48px;
      width: 100%;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #333;
      padding: 0 16px;
    }
  }
</style>
