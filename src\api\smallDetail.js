import request from '../router/axios'
import api from '../config/url'
import {
  getToken
} from '../utils/auth'

const baseUrl = api.BaseApiUrl

export const getCarConfigDetail = () => request({
  url: `${baseUrl}/api-wap/audi-page/api/floors?pageAccessType=2&pageFrontCode=order-cc-detail`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

export const getRightsDetail = () => request({
  url: `${baseUrl}/api-wap/audi-page/api/floors?pageAccessType=2&pageFrontCode=audi-cc-reserve-offers`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})
