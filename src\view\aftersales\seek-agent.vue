<template>
  <div class="container" style="position: relative">
    <navigation :back-type="backType">
      <div @click="onMap">
        <img
          class="title_img"
          slot="icon"
          :src="isShowMap ? activeIcon : inactiveIcon"
        />
      </div>
    </navigation>
    <div
      v-show="isShowMap"
      style="
        position: fixed;
        right: 0;
        z-index: 90;
        margin-top: 216px;
        margin-right: 6px;
      "
      @click="onPositioning"
    >
      <img
        style="width: 30px; height: 30px"
        src="../../assets/img/icon-map-centered.png"
      />
    </div>
    <div
      id="container"
      style="width: 100vw; height: 350px"
      v-show="isShowMap"
    />

    <van-action-sheet
      v-model="isShowMap"
      :round="false"
      :overlay="false"
      style="height: 350px"
    >
      <div class="line">
        <div class="title">城市</div>
        <div class="btn-change" @click="showPicker = true">
          <div class="change-name">
            {{ value }}
          </div>
          <van-icon class="btn-icon" name="arrow" size="16px" />
        </div>
      </div>

      <div
        v-for="(item, idx) in cityAddressList"
        :key="idx"
      >
        <div class="item-wrapper">
          <div>
            <img
              class="img-wrapper"
              :src="
                ((item.thumbnailUrl || '').includes('http')
                  ? item.thumbnailUrl
                  : ossUrl + item.thumbnailUrl) | audiwebp
              "
              alt=""
            />
          </div>

          <div class="content-wrapper flex1">
            <div class="c-font16 c-bold">
              {{ item.dealerName }}
            </div>
            <div style="margin-top: 4px" />
            <div class="c-font12">
              {{ item.cityName + item.areaName }}
            </div>

            <div style="margin-top: 15px" />
            <div class="c-font12">
              <img
                class="phone-icon"
                :src="require('@/assets/img/dealer-phone.png')"
                alt=""
              />
              {{ item.dealerPhone }}
            </div>
          </div>

          <div class="navgation-wrapper">
            <div>
              <div
                class="nav-icon"
                v-show="item.distance !== 0"
                @click.stop="
                  toNavigation(item.longitude, item.latitude, item.dealerName)
                "
              >
                <img :src="require('@/assets/img/nav.png')" alt="" />
              </div>
              <span class="c-font12">
                {{ item.distance | formatDistance }}
              </span>
            </div>
          </div>
        </div>
        <!-- <div class="item_bottom">
          <div v-if="item.provideSaicAudiPickUpService === '1'" class="item_btn" style="width: 50px; height: 24px; margin-right: 10px;">
            取送车
            </div>
          <div v-if="item.businessStatus === '3'" class="item_btn" style="width: 60px; height: 24px;">
            NEV服务
          </div>
        </div> -->
      </div>
    </van-action-sheet>

    <div v-if="!isShowMap">
      <div
        v-for="(item, idx) in addressAllList"
        :key="idx"
      >
        <div class="item-wrapper">
          <div>
            <img
              class="img-wrapper"
              :src="
                (item.thumbnailUrl || '').includes('http')
                  ? item.thumbnailUrl
                  : ossUrl + item.thumbnailUrl
              "
              alt=""
            />
          </div>

          <div class="content-wrapper flex1">
            <div class="c-font16 c-bold">
              {{ item.dealerName }}
            </div>
            <div style="margin-top: 4px" />
            <div class="c-font12">
              {{ item.cityName + item.areaName }}
            </div>

            <div style="margin-top: 15px" />
            <div class="c-font12">
              <img
                class="phone-icon"
                :src="require('@/assets/img/dealer-phone.png')"
                alt=""
              />
              {{ item.dealerPhone }}
            </div>
          </div>

          <div class="navgation-wrapper">
            <div>
              <div
                class="nav-icon"
                v-show="item.distance !== 0"
                @click.stop="
                  toNavigation(item.longitude, item.latitude, item.dealerName)
                "
              >
                <img :src="require('@/assets/img/nav.png')" alt="" />
              </div>
              <span class="c-font12">
                {{ item.distance | formatDistance }}
              </span>
            </div>
          </div>
        </div>
        <!-- <div class="item_bottom">
          <div v-if="item.provideSaicAudiPickUpService === '1'" class="item_btn" style="width: 50px; height: 24px; margin-right: 10px;">
            取送车
            </div>
          <div v-if="item.businessStatus === '3'" class="item_btn" style="width: 60px; height: 24px; ">
            NEV服务
          </div>
        </div> -->
      </div>
    </div>

    <van-popup v-model="showPicker" round position="bottom">
      <van-picker
        show-toolbar
        :visible-item-count="5"
        :columns="columns"
        value-key="name"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </van-popup>
  </div>
</template>

<script>
import { Promise } from "q";
import { mapState } from "vuex";
import Vue from "vue";
import { ActionSheet, Picker, Popup } from "vant";
import { getDealerList, getAgentCityList } from "@/api/api";
import baseUrl from "@/config/url";
import { callNative, getdiscount2 } from "@/utils";
import navigation from "../../components/navigation.vue";
import storage from "../../utils/storage";

Vue.use(ActionSheet).use(Picker).use(Popup);

const latitude = "31.2304";
const longitude = "121.4737";

export default {
  components: { navigation },
  name: "Name",
  data() {
    return {
      ossUrl: baseUrl.BaseOssHost,
      addressAllList: [], // 全部地址
      location: "", // 模拟本地的经纬度信息.
      map: null,
      infoWindow: null,

      backType: "app",
      isShowMap: true,
      activeIcon: require("../../assets/img/icon-cut1.png"),
      inactiveIcon: require("../../assets/img/icon-cut2.png"),

      cityAddressList: [], // 城市地址
      value: "",
      showPicker: false,
      columns: [],
      initCityName: "", // 初始进来的城市
    };
  },
  computed: {},
  async mounted() {
    const map = new AMap.Map(document.getElementById("container"), {
      zoom: 26, // 显示范围
      resizeEnable: true,
    });
    this.map = map;
    var infoWindow = new AMap.InfoWindow({ offset: new AMap.Pixel(-0, -31) });
    this.infoWindow = infoWindow;

    const data = await callNative("getLocationCity", {});
    this.location = data.location || `${latitude},${longitude}`;
    console.log("getLocationCity", data);

    this.initCityName = data.city;

    this.getAgentCityList();
    this.getDealerList();
  },

  methods: {
    onPositioning() {
      this.map.panTo([
        this.location.split(",")[1],
        this.location.split(",")[0],
      ]);
    },
    onMap() {
      this.isShowMap = !this.isShowMap;
    },
    // 获取服务商城市
    async getAgentCityList() {
      const { data } = await getAgentCityList();
      this.columns = data.data;
      // 没有定位城市先取第一条
      if (this.initCityName === undefined) {
        this.value = this.columns[0].name;
        this.getAgentList(this.columns[0]);
      } else {
        const item = this.columns.find((i) => i.name === this.initCityName);
        if (item) {
          this.value = item.name;
          this.getAgentList(item);
        } else {
          this.value = this.columns[0].name;
          this.getAgentList(this.columns[0]);
        }
      }

      console.log("cityCode", this.cityCode);
    },
    async getDealerList() {
      this.$store.commit("showLoading");
      const { data } = await getDealerList({
        // businessStatus: 0,
      });
      if (data.code === "00") {
        this.addressAllList = data.data;
        // 高德api计算两点之间的距离
        const location = this.location.split(",");

        const results = await Promise.all(
          this.addressAllList.map(async (dealer) => {
            if (dealer.latitude && dealer.longitude) {
              console.log(
                "距离计算 ",
                location[1],
                location[0],
                dealer.longitude,
                dealer.latitude
              );
              return getdiscount2(
                location[1],
                location[0],
                dealer.longitude,
                dealer.latitude
              );
            }
            return 0;
          })
        );

        this.addressAllList.forEach((dealer, i) => {
          dealer.distance = results[i];
        });
        console.log(this.addressAllList);
        this.addressAllList.sort(this.compare("distance"));
        this.addressAllList = this.resort(this.addressAllList);
        console.log(this.addressAllList);
        this.$store.commit("hideLoading");
      }
    },

    getOtherPoint({ latitude, longitude }) {
      if (latitude && longitude) {
        return `${latitude},${longitude}`;
      }
      return `${latitude},${longitude}`;
    },

    compare(prop) {
      return function (obj1, obj2) {
        const val1 = obj1[prop];
        const val2 = obj2[prop];
        if (val1 < val2) {
          return -1;
        }
        if (val1 > val2) {
          return 1;
        }
        return 0;
      };
    },

    resort(addressAllList) {
      const idx = addressAllList.findIndex((item) => item.distance !== 0);
      const truncate = addressAllList.slice(0, idx);
      addressAllList.splice(0, idx);
      return addressAllList.concat(truncate);
    },

    toNavigation(longitude, latitude, dealerName) {
      callNative("navigationMap", {
        lat: latitude.toString(),
        long: longitude.toString(),
        des: dealerName,
      });
    },

    // 选择城市
    onConfirm(value) {
      this.value = value.name;
      this.showPicker = false;
      this.getAgentList(value);
    },

    async getAgentList(value) {
      this.$store.commit("showLoading");
      const { data } = await getDealerList({
        cityCode: value.cityCode,
      });
      if (data.code === "00") {
        this.cityAddressList = data.data;
        // 高德api计算两点之间的距离
        const location = this.location.split(",");

        const results = await Promise.all(
          this.cityAddressList.map(async (dealer) => {
            if (dealer.latitude && dealer.longitude) {
              console.log(
                "距离计算 ",
                location[1],
                location[0],
                dealer.longitude,
                dealer.latitude
              );
              return getdiscount2(
                location[1],
                location[0],
                dealer.longitude,
                dealer.latitude
              );
            }
            return 0;
          })
        );

        this.cityAddressList.forEach((dealer, i) => {
          dealer.distance = results[i];
        });
        console.log(this.cityAddressList);
        this.cityAddressList.sort(this.compare("distance"));
        this.cityAddressList = this.resort(this.cityAddressList);
        console.log(this.cityAddressList);
        this.$store.commit("hideLoading");
        this.mapAddress();
      }
    },
    // 初始化坐标
    mapAddress() {
      const icon2 = new AMap.Icon({
        // 自定义图标
        size: new AMap.Size(33, 33),
        image: require("../../assets/img/icon-map-position.png"),
        imageSize: new AMap.Size(33, 33),
        anchor: "center",
      });

      const marker2 = new AMap.Marker({
        // 插点
        icon: icon2,
        position: new AMap.LngLat(
          this.location.split(",")[1],
          this.location.split(",")[0]
        ),
        offset: new AMap.Pixel(-13, -30)
      });
      this.map.add(marker2);
      const icon = new AMap.Icon({
        // 自定义图标
        size: new AMap.Size(33, 33),
        image: require("../../assets/img/map-index.png"),
        imageSize: new AMap.Size(33, 33),
        anchor: "center",
      });
      const markerList = [];
      for (let i = 0; i < this.cityAddressList.length; i++) {
        if (this.cityAddressList[i].longitude > 0) {
          const marker1 = new AMap.Marker({
            // 插点
            icon: icon,
            position: [
              this.cityAddressList[i].longitude,
              this.cityAddressList[i].latitude,
            ],
            offset: new AMap.Pixel(-13, -30)
          });
          marker1.content = this.cityAddressList[i].dealerName;
          marker1.on("click", this.markerClick);
          markerList.push(marker1);
        }
      }

      this.map.add(markerList);
      this.map.setFitView();
    },
    markerClick(e) {
      this.infoWindow.setContent(e.target.content);
      this.infoWindow.open(this.map, e.target.getPosition());
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.container {
  width: 100vw;
  overflow: hidden;
}
.title_img {
  width: 20px;
  height: 20px;
}
.flex1 {
  flex: 1;
}
.transparent {
  background-color: transparent;
}
.item-wrapper {
  .c-flex-between;
  // border-bottom: 1px solid #e5e5e5;
  padding: 16px;
}

.phone-icon {
  width: 12px;
  vertical-align: baseline;
}

.img-wrapper {
  width: 80px;
  height: 80px;
}
.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  margin-left: 15px;
  padding: 5px 0;
}
.navgation-wrapper {
  .c-font14;
  .c-flex-center;
  color: #b2b2b2;
  text-align: center;
  .nav-icon {
    width: 24px;
    margin: 0 auto 5px auto;
  }
}

.item_bottom {
  display: flex;
  margin-left: 114px;

  .item_btn {
    border: 1px solid #999999;
    text-align: center;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 12px;
  }
}

.line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  .title {
    font-size: 16px;
    color: #000;
    margin: 5px 5px;
  }
  .btn-change {
    display: flex;
    align-items: center;
    .change-name {
      font-size: 16px;
      color: #000;
      margin-right: 10px;
    }
    .btn-icon {
      align-items: center;
      font-size: 12px;
      color: #a3a3a3;
    }
  }
}
</style>
