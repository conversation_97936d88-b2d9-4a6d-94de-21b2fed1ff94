<template>
  <div
    class="private-order"
    ref="private"
  >
    <div class="private-order-main">
      <div
        v-for="item in getList"
        :key="item.optionId"
        class="private-order-cardbox"
      >
        <div v-if="!('FH,FF'.includes(currentSibColorInterieur.interieurOptionCode) && item.optionCode === '6NQ')">
          <option-card v-show="!item.hideBool"
            @setTop="setTop"
            :option="item"
            :current-index="currentIndex"
          />
        </div>
      </div>
    </div>
    <div
      class="perwin"
      v-show="showPerwin"
    >
      <div class="main">
        <div class="main_title">
          您必须对以下装备进行选择：
        </div>
        <div class="main_content">
          <div
            class="main_item"
            v-for="item in getListForV2"
            :key="item.optionId+'ccc'"
          >
            <img
              class="img"
              :src="BaseConfigrationOssHost + item.imageUrlList | imgFix(640, true)"
            >
            <div class="name">
              {{ item.optionName }}
            </div>
            <div class="price">
              {{ item.price|finalFormatPrice }}
            </div>
          </div>
        </div>
        <div
          class="main_ok"
          @click="main_ok"
        >
          确认
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import url from '@/config/url'
import {
  checkV2
} from '@/utils'
import OptionCard from '@/components/option-card.vue'

import {
  defPatchesFn03,patchesPS1, patchesPS138, 
} from '@/view/configration/fix_configration/ccFn.js'
export default {
  props: {
    currentIndex: {
      type: String,
      required: true
    }
  },
  components: { OptionCard },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost
    }
  },
  watch: {
    modelScrollTop(val) {
      // if (window.location.hash.includes('configration')) return
      setTimeout(() => {
        this.$refs.private.scrollTo({ top: val })
      })
    },
  },
  computed: {
    ...mapState(['idx', 'selectCarInfo', 'privateOrderList', 'currentSibColorInterieur', 'currentOptionsList', 'modelScrollTop', 'allPrivateOrderList', 'showPerwin']),
    getList() {
      /**
       * !checkV2 高定
       * 498B2Y005 奥迪A7L 先行版
       * 498B2Y006 奥迪A7L 先见版
       * 498B2Y007  奥迪A7L 55TFSI quattro 黑武士
       * 498B2Y008  奥迪A7L 55TFSI quattro 影武士
       * 498BZY008  奥迪A7L 45TFSI quattro 影武士
       */
      if (!checkV2()) {
        if (['498B2Y005','498B2Y006', '498B2Y007', '498B2Y008', '498BZY008'].includes(this.selectCarInfo.modelLineCode)) {
          return this.arrayUnique(this.allPrivateOrderList)
        }
        if (this.currentSibColorInterieur.sibInterieurRelates) {
          const conflictsall = []
          for (const val of this.currentSibColorInterieur.sibInterieurRelates) {
            if (this.idx === '1') {
              if ('conflict'.includes(val.relateType)) {
                conflictsall.push(val.optionRelateCode)
              }
            } else {
              conflictsall.push(val.optionRelateCode)
            }
          }
          /**
           * G4IBF3001 Audi Q5 e-tron 50 quattro Roadjet 荣耀型 锦衣套装
           * G4ICF3001 Audi Q5 e-tron 50 quattro Roadjet 荣耀型 机甲套装
           * G4ICF3007 Audi Q5 e-tron 50 quattro Roadjet 荣耀型 机甲套装 2023款
           */
          if (this.idx === '1' && !'G4IBF3001,G4ICF3001,G4ICF3007'.includes(this.selectCarInfo.modelLineCode)) {
            const newArrays1 = this.allPrivateOrderList.filter((e) => !conflictsall.includes(e.optionCode))
            this.$store.commit('setPrivateOrderList', newArrays1)
          }
        }
      }
      //  这个规则删了
      // defPatchesFn01(this) // 补丁01
      defPatchesFn03(this) // 补丁03
      // sibName: "Perlnappa真皮混搭翻毛皮"
      let sibName =  this.$store.state.currentSibColorInterieur?.sibName == "Perlnappa真皮混搭翻毛皮"
      let modelLineCode = this.selectCarInfo.modelLineCode == 'G4ICF3001'
      if (sibName && modelLineCode) {
        const arr = this.allPrivateOrderList.filter((e) => e.optionCode !== '4D3')
        this.$store.commit('setPrivateOrderList', arr)
      }

      if (this.idx == 2) {
        let sib = this.currentSibColorInterieur?.sibInterieurCode
        this.privateOrderList?.forEach(e => {
          if (e.optionRelates && e.optionRelates.length > 0 && e.optionRelates.find(o => {
            return (
              (o.optionRelateCategory == "COLOR_INTERIEUR" || o.optionRelateCategory == "F_SIB_COLOR_INTERIEUR") && 
              o.relateType == "conflict" && sib && sib == o.optionRelateCode
            )
            })) {
              e.conflict = "此装备与当前所选内饰冲突，请选中其他内饰后尝试"
          } else {
            e.conflict = ""
          }
        });
        patchesPS1(this)
      }
      return this.privateOrderList
    },
    getListForV2() {
      const list = this.privateOrderList.filter((element) => !this.currentOptionsList.some((e) => e.optionId === element.optionId))
      return list || []
    }
  },
  methods: {
    setTop0() {
      this.$store.commit('setScrollTop', 0)
    },
    setTop() {
      this.$store.commit('setScrollTop', this.$refs.private.scrollTop)
    },
    arrayUnique(arr) {
      const res = new Map()
      return arr.filter(
        (list) => !res.has(list.optionCode) && res.set(list.optionCode, 1)
      )
    },
    main_ok() {
      this.$store.commit('setShowPerwin', false)
    }
  },
  mounted() {}
}
</script>

<style lang="less" scoped>
.private-order-cardbox {
  margin-bottom: 16px;
}
.private-order {
  height: calc(100vh - 190px);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  .private-order-main {
    padding: 16px;
  }
  .perwin{
    width: 100vw;height: 100vh;
    position: fixed;
    z-index: 9999;
    top: 0;left: 0;
    background-color: rgba(0,0,0,0.3);
    .main{
      width: calc(100vw - 32px);
      background-color: #fff;
      padding: 16px;
      box-sizing: border-box;
      position: relative;
      top: 50%;left: 50%;
      transform: translate(-50%,-50%);
      .main_title{
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 16px;
      }
      .main_content{
        overflow-y: auto;
        max-height: calc(100vh - 360px);
        font-size: 14px;
        border-top: solid 1px #e5e5e5;
        border-bottom: solid 1px #e5e5e5;
        box-sizing: border-box;
        .main_item{
          border-bottom: solid 1px #e5e5e5;
          display: flex;
          align-items: center;
          padding: 16px 0;
          .img{
            display: block;
            width: 16vw;height: 16vw;
            object-fit: cover;
          }
          .name{
            flex: 1;
            margin-left: 10px;
          }
          .price{
            margin-left: 10px;
            font-size: 12px;
          }
        }
      }
      .main_ok{
        margin: 32px auto 0 auto;
        background-color: #000;
        color: #fff;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
