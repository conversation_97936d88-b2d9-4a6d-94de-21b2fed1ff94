<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-02 17:23:29
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-25 14:25:56
 * @FilePath     : \src\view\order\pay-status\succeed\succeed.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['pay-status-succeed-wrapper',
       'page-wrapper',
       `pay-status-succeed-${skin}`,
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      background-color="transparent"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      :listening-emit-back="true"
      @handleLeftBack="handleLeftBackAction"
    >
      <div class="title-color" slot="middle">支付成功</div>
  </header-custom>
    <succeed-common
      :header-fixed="headerFixed"
      :header-out-height="headerOutHeight"
    />
  </div>
</template>

<script>
import HeaderCustom from '@/components/header-custom.vue'
import SucceedCommon from './common.vue'
import { getCarOrderInfoH5 } from '@/api/api'
import { callNative } from '@/utils'

const PAY_SUCCEED_SKIN = {
  common: 'common'
}

export default {
  components: {
    'header-custom': HeaderCustom,
    'succeed-common': SucceedCommon
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      skin: PAY_SUCCEED_SKIN.common,
      orderId: ''
    }
  },
  created() {
    const { $route: { query: { orderId }, query } } = this
    this.orderId = orderId

    console.log('%c [ pay-status-succeed query ]-61', 'font-size:14px; background:#cf222e; color:#fff;', this.$route.query)
  },
  methods: {
    handleProcessData(orderId) {},
    async handleLeftBackAction() {
      const { orderId } = this
      const {data} = await getCarOrderInfoH5()
      callNative('audiOpen', {
          path: `${data.data.configValue}order/new-money-detail?orderId=${orderId}&fromPage=successStatus`
       })
      // $router.push({ name: 'new-money-detail', query: { orderId,fromPage:'successStatus' } })
      sessionStorage.removeItem('orderId')
    }
  }

}
</script>

<style lang="less" scoped>
.page-wrapper {
  height: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
}
.title-color{
  color: #000 !important;
}
</style>
