<template>
  <div name="name" class="container-address" v-show="value1.length">
    <!-- fist1:{{ massage1 }}
    fist2:{{ massage2 }} -->
    <div class="map_sear">
      <div class="_city">
        <van-dropdown-menu :overlay="false" z-index="9999">
          <van-dropdown-item v-model="value1" :options="option1" @change="changeDropdown"/>
        </van-dropdown-menu>
        <van-icon class="_icon" name="arrow-down" size="15px" />
      </div>
      <div class="_sear">
        <van-icon name="search" size="17px" />
        <input
          id="tipinput"
          placeholder="请输入您的地址"
          type="text"
          v-model="keyword"
        />
      </div>
    </div>
    <div v-if="isShowSearchList">
      <div v-for="(item, idx) in searchList" :key="idx">
        <div class="eticket" @click="onSearchList(item)">
          <div>
            <div class="eticket-title">
              {{ item.name }}
            </div>
            <div style="color: #999; font-size: 12px; margin-top: 6px">
              {{ item.address2 }}
            </div>
          </div>

          <div class="eticket-btn-change">
            <span class="c-font12" style="color: #999; font-size: 14px">
              {{ item.distance | selectAddress }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div style=" position: fixed; right: 0; z-index: 90;margin-top: 216px;margin-right:6px" @click="onPositioning">
        <img
           style="width: 30px; height: 30px;"
          src="../../assets/img/icon-map-centered.png"
        />
      </div>
    <div
      v-show="!isShowSearchList"
      id="container-address"
      style="width: 100vw; height: 330px"
    />

    <div
      v-if="!isShowSearchList"
      class="eticket_title"
    >
      <div v-for="(item, idx) in poiAddressList" :key="idx">

        <div class="eticket">

          <div>
            <div class="eticket-title">
              {{ item.name }}
            </div>

            <div style="color: #999; font-size: 12px; margin-top: 6px">
              {{ item.address2 }}
            </div>
            <span class="c-font12" style="color: #999; font-size: 14px">
              {{ item.distance | selectAddress }}
            </span>
          </div>

          <div class="eticket-btn-change">
            <van-checkbox
              class="eticket-btn-icon"
              v-model="item.checked"
              @click="onCheckbox(item)"
            >
              <img
                style="height: 18px; width: 18px"
                slot="icon"
                :src="item.checked ? activeRadioIcon : inactiveRadioIcon"
              />
            </van-checkbox>
          </div>
        </div>
      </div>

      <div v-if="!poiAddressList.length" class="notList">{{mapTipMsg}}</div>
      <div class="btn-height" />
      <div class="bottom_style">
        <div class="btn-delete-wrapper" v-if="isLocate">
          <AudiButton
            @click="onEticketSelect"
            :text="'完成'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
        <div class="btn-delete-wrapper" v-else>
          <AudiButton
            @click="openLocate"
            :text="'去开启定位'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

import Vue from "vue";
import { DropdownMenu, DropdownItem, ActionSheet, Checkbox } from "vant";
import { afterServiceScPlaceText, getDeliverCarQueryCityList, afterServiceScGeocodeGeo } from '@/api/api'
import { callNative, getLocationAddress, getdiscount2 } from "@/utils";
import AudiButton from "@/components/audi-button";
import storage from "../../utils/storage";

Vue.use(DropdownMenu);
Vue.use(DropdownItem);
Vue.use(ActionSheet);
Vue.use(Checkbox);

export default {
  components: {
    AudiButton,
  },
  name: "Name",
  data() {
    return {
      value1: "",
      option1: [],
      type: "",
      takeAddress: "",
      sendAddress: "",
      keyword: "",
      latitude: "",
      longitude: "",
      cityCode: "",
      cityName: "",
      AMap: null,
      map: null,
      poiAddressList: [],
      searchList: [],
      show: true,
      isShowSearchList: false, //是否显示搜索列表

      activeRadioIcon: require("../../assets/img/radio_checked.png"),
      inactiveRadioIcon: require("../../assets/img/radio_normal.png"),
      markerList: [],

      dealerLatitude: 0, // 服务商经纬度
      dealerLongitude: 0, // 服务商经纬度

      massage1:"",
      massage2:"",
      // 是否开启定位
      isLocate: false,

      mapTipMsg: ''
    };
  },
  computed: {
    ...mapState({
    }),
  },
  watch: {
    value1(value, old) {
      // console.log('value1',value)
      if(value && this.option1.length > 0){
        const item = this.option1.find((i) => i.value === value);
        this.cityName = item.text;
        this.getAddress();
      }
    },
    keyword(value) {
      if (value.length > 0) {
        this.getAddress();
      }
    },
  },
  async mounted() {
    var dealerModel = storage.get("dealerCarModel") || "{}";
    this.dealerLatitude = JSON.parse(dealerModel).latitude || 0; // 服务商经纬度
    this.dealerLongitude = JSON.parse(dealerModel).longitude || 0; // 服务商经纬度

    console.log("路由参数信息：",this.$route.query)

    const { type, takeAddress, sendAddress, latitude, longitude, cityCode } =
      this.$route.query;
    this.type = type;
    this.takeAddress = takeAddress;
    this.sendAddress = sendAddress;
    this.latitude = latitude;
    this.longitude = longitude;
    this.cityCode = cityCode;
    console.log(takeAddress,111111);
    this.getDeliverCarQueryCityList();
    console.log('cityCode',cityCode)
    // const map = new AMap.Map(document.getElementById("container-address"), {
    //   zoom: 26, // 显示范围
    //   resizeEnable: true,
    // });

    // this.map = map;

    this.initAddress(latitude, longitude);
  },
  methods: {
    changeDropdown (value) {
      const item = this.option1.find((i) => i.value === value);
      // 记录上次的城市
      localStorage.setItem("changeSelectCityCode", item.text)
      afterServiceScGeocodeGeo({
        address: item.text + `市中心`
      }).then(res => {
        console.info('🚀 ~ file:select-user-address method: line:223 -----', res.data.data.data)
        let result = res.data.data.data
        this.latitude = result.geocodes[0].location.split(',')[1]
        this.longitude = result.geocodes[0].location.split(',')[0]

        this.mapPositionPicker(result.geocodes[0].location.split(',')[0], result.geocodes[0].location.split(',')[1]);
      })
    },
    async openLocate () {
      const localData = await callNative("getLocationCity", {deniedAndNeverShow: false});
      console.info('🚀 ~ file:openLocate -----', localData)
      this.isLocate = Boolean(localData.location && (localData.location != '0.0,0.0'))
      if (!Boolean(localData.location && (localData.location != '0.0,0.0'))) {
        this.mapTipMsg = '请开启定位，为您展示附近地址'
      }
      console.info('🚀 ~ 重启开启定位的判断 -----', Boolean(localData.location && (localData.location != '0.0,0.0')))
      if (localData.location && (localData.location != '0.0,0.0')) {
        this.latitude = localData.location.split(',')[0]
        this.longitude = localData.location.split(',')[1]
        let findData = this.option1.find((idex) => idex.text.indexOf(localData.city) > -1);
        this.value1 = findData.value
        this.location = localData.location;
        this.$nextTick(() => {
          this.mapPositionPicker(localData.location.split(',')[1], localData.location.split(',')[0]);
        })
      }
    },
    async initAddress(latitude, longitude) {
      const data = await callNative("getLocationCity", {});
      console.info('🚀 ~ file:select-user-address method:initAddress line:247 -----', data)
      this.isLocate = Boolean(data.location && (data.location != '0.0,0.0'))
      if (!Boolean(data.location && (data.location != '0.0,0.0'))) {
        this.mapTipMsg = '请开启定位，为您展示附近地址'
      }
      console.info('🚀 ~ file:select-user-address method:initAddress line:248 -----', this.isLocate)
      this.location = data.location;
      let latitudeStr = latitude
      let longitudeStr = longitude
      if ((latitude == '0.0' && longitude == '0.0') || (latitude == '' && longitude == '')) {
        if (data.location && data.location != '0.0,0.0') {
          latitudeStr = data.location.split(',')[0]
          longitudeStr = data.location.split(',')[1]
        } else {
          latitudeStr = ''
          longitudeStr = ''
        }
        this.latitude = latitudeStr
        this.longitude = longitudeStr
      }
      this.mapPositionPicker(longitudeStr, latitudeStr);
    },
    onPositioning(){
      this.map.panTo([this.longitude, this.latitude]);
    },
    //地图拖拽事件
    async mapPositionPicker(lng, lat) {
      const this1 = this;
      new AMapUI.loadUI(["misc/PositionPicker"], function (PositionPicker) {
        var map = new AMap.Map("container-address", {
          zoom: 16,
          scrollWheel: false,
          center: [(lng ? lng : ''), (lat ? lat : '')],
        });
        this1.map = map;
        var positionPicker = new PositionPicker({
          mode: "dragMap",
          map: map,
          iconStyle: {
            //自定义外观
            url: require("../../assets/img/map-index.png"),
            ancher: [24, 36],
            size: [40, 40],
          },
        });

        positionPicker.on("success", function (positionResult) {
          if (this1.isLocate) {
            this1.getLocationAddress(positionResult.position);
          }
        });
        positionPicker.start();
      });
    },

    async getLocationAddress(lnglat) {
      console.info('🚀 ~ file:select-user-address method:getLocationAddress line:310 -----', [lnglat.lng, lnglat.lat])
      let address = await getLocationAddress([lnglat.lng, lnglat.lat]);
      console.info('🚀 ~ file:select-user-address method:getLocationAddress line:311 -----', address)
      this.getAllAddress(address);
    },
    // 通过搜索值拉取附近地点
    getAllAddress(address) {
      const that = this;
      afterServiceScPlaceText( {
        keywords: address,
        pageNum: 1,
        pageSize: 10
      }).then(res => {
        console.info('🚀 ~ file:select-user-address method: line:316 -----', res.data.data.data)
        let result = res.data.data.data
        console.log("是这里出问题1", result.pois);

        that.massage1 = result;

        if (!result.pois.length) {
          that.show = false;
          that.poiAddressList = [];
          this.mapTipMsg = '附近暂无数据，请手动输入关键位置或拖动地图重新选择'
          return;
        }
        that.show = true;
        if (result.pois.length > 0) {
          // console.log("11111111");

          result.pois.forEach(async (element, index) => {
            const address = await that.getAddressInfo(
              element.location.split(',')[0],
              element.location.split(',')[1]
            );
            if (index === 0) {
              this.$set(element, "checked", true);
            } else {
              this.$set(element, "checked", false);
            }
            this.$set(element, "address2", address);
          });

          // that.poiAddressList = result.pois;
          that.calculationDistance1(result)
        } else {
          this.mapTipMsg = '附近暂无数据，请手动输入关键位置或拖动地图重新选择'
        }
      })
    },
    //计算距离
    async calculationDistance1(result) {
      this.poiAddressList = result.pois;

      const results = await Promise.all(
        this.poiAddressList.map(async (dealer) => {
          return getdiscount2(
            this.dealerLongitude,
            this.dealerLatitude,
            dealer.location.split(',')[0],
            dealer.location.split(',')[1]
          );
        })
      );

      this.poiAddressList.forEach(async (dealer, i) => {
        dealer.distance = results[i];
        if (i === 0) {
          this.$set(dealer, "checked", true);
        } else {
          this.$set(dealer, "checked", false);
        }
      });
    },
    //获取详细地址
    async getAddressInfo(lng, lat) {
      const address = await getLocationAddress([lng, lat]);
      console.log("地址", address);
      return address;
    },
    // 获取开通取送车城市
    async getDeliverCarQueryCityList() {
      const { data } = await getDeliverCarQueryCityList();
      if (data.code === "200") {
        if (data.data) {
          for (let i = 0; i < data.data.length; i++) {
            this.option1.push({
              text: data.data[i].cityName,
              value: data.data[i].cityCode,
            });
          }
          console.log('getDeliverCarQueryCityList', this.cityCode)
          const item = this.option1.find((idex) => idex.text === this.cityCode);
          let newText = ''
          if (item !== undefined) {
            this.value1 = item.value;
            newText = item.value;
          } else {
            let beiJin = '北京'
            if (localStorage.changeSelectCityCode) {
              beiJin = localStorage.changeSelectCityCode
            }
            let findBJ = this.option1.find((idex) => idex.text.indexOf(beiJin) > -1);
            if (findBJ) {
              this.value1 = findBJ.value;
              newText = findBJ.text;
            } else {
              this.value1 = this.option1[0].value;
              newText = this.option1[0].text;
            }
          }
          if (!this.latitude || !this.longitude) {
            console.info('🚀 ~ file:getDeliverCarQueryCityList line:411 -----', '执行')
            afterServiceScGeocodeGeo({
              address: `${newText}中心`
            }).then(res => {
              console.info('🚀 ~ file:select-user-address method: line:223 -----', res.data.data.data)
              let result = res.data.data.data
              this.latitude = result.geocodes[0].location.split(',')[1]
              this.longitude = result.geocodes[0].location.split(',')[0]

              this.mapPositionPicker(result.geocodes[0].location.split(',')[0], result.geocodes[0].location.split(',')[1]);
            })
          }
        }
      }
    },

    // 通过搜索值拉取附近地点
    getAddress() {
      if (this.keyword) {
        const that = this;
        afterServiceScPlaceText( {
          keywords: this.keyword,
          pageNum: 1,
          pageSize: 10,
          region: this.cityName, // 兴趣点城市
          city_limit: true, // 是否强制限制在设置的城市内搜索
        }).then(res => {
          let result = res.data.data.data
          that.massage2 = result;

          if (!result.pois?.length) {
            that.isShowSearchList = false;
            return;
          }
          that.isShowSearchList = true;

          that.calculationDistance(result);
        })
      }
    },
    //计算距离
    async calculationDistance(result) {
      this.searchList = result.pois;

      const results = await Promise.all(
        this.searchList.map(async (dealer) => {
          return getdiscount2(
            this.dealerLongitude,
            this.dealerLatitude,
            dealer.location.split(',')[0],
            dealer.location.split(',')[1]
          );
        })
      );

      this.searchList.forEach(async (dealer, i) => {
        dealer.distance = results[i];
        const address = await this.getAddressInfo(
          dealer.location.split(',')[0],
          dealer.location.split(',')[1]
        );

        this.$set(dealer, "checked", false);

        this.$set(dealer, "address2", address);
      });
    },
    //点击搜索列表
    onSearchList(item) {
      this.closeView(item);
    },
    // 选择地址
    onCheckbox(item) {
      for (let i = 0; i < this.poiAddressList.length; i++) {
        if (
          item.checked === true &&
          item.name === this.poiAddressList[i].name
        ) {
          this.poiAddressList[i].checked = true;
        } else {
          this.poiAddressList[i].checked = false;
        }
      }
      console.log();
      if(item.checked) {
        this.$store.state.oldtakeAddress = item.address2
      }
    },

    // 选择好的地址
    onEticketSelect() {
      // this.show = false;
      this.poiAddressList.forEach((element) => {
        if (element.checked) {
          window.console.log("xxx:", element);
          this.closeView(element);
        }
      });
    },

    // 关闭页面
    async closeView(poi) {
      // const { env } = this.$route.query;
      // if (!env) {
      //   callNative('close', { type: 'address', params:`${poi.address2}#${poi.location.lat}#${poi.location.lng}#${this.value1}`, // 取车地址
      //   })
      // } else {
        if (this.type === "takeAddress") {
          this.$store.commit("saveTakeAddress", {
            takeAddress: poi.address2, // 取车地址
            takeLat: poi.location.split(',')[1], // 取车纬度
            takeLng: poi.location.split(',')[0], // 送车经度
            takeCityCode: this.cityName, // 选择的城市
          });
        } else {
          this.$store.commit("saveSendDAddress", {
            sendAddress: poi.address2, // 送车地址
            sendLat: poi.location.split(',')[1], // 送车纬度
            sendLng: poi.location.split(',')[0], // 送车经度
            sendCityCode: this.cityName, // 选择的城市
          });
        }
        window.console.info('🚀 ~ 执行下一步 ~ ')
        this.$router.back(-1);
      // }
    },
  },
};
</script>

<style lang='less' scoped>
.container-address {
  width: 100vw;
  overflow: hidden;
}

.map_sear {
  width: 100vw;
  height: 100%;
  background: #ffffff;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding-top: 8px;
  padding-bottom: 8px;

  ._city {
    display: flex;
    width: 88px;
    align-items: center;
    position: relative;

    ._name {
      font-size: 14px;
      font-family: "Audi-Normal";
      color: #000000;
      margin-right: 5px;
    }

    ._icon {
      position: absolute;
      right: 10px;
      top: 50%;
      margin-top: -7px;
    }
  }

  ._sear {
    display: flex;
    align-items: center;
    width: calc(100% - 70px);
    height: 40px;
    border-bottom: 1px solid #808080;

    input {
      border: none;
      padding-left: 5px;
      font-size: 14px;
      width: 100%;
    }
  }
}

::v-deep .van-dropdown-menu__title::after {
  display: none;
}

::v-deep .van-dropdown-item__option--active {
  color: #000;
}

::v-deep .van-cell__value {
  display: none;
}

::v-deep .van-popup--top {
  width: 80px;
  left: 10px;
}

::v-deep .van-dropdown-menu__item {
  width: 100% !important;
  text-align: left;
  justify-content: flex-start;
}

::v-deep .van-dropdown-menu {
  width: 70px;
  overflow: hidden;
}

::v-deep .van-dropdown-item__option {
  padding: 0;
  display: flex;
  align-items: center;
  width: 80px;
  text-align: center;
  padding: 6px 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .van-dropdown-menu__bar {
  box-shadow: none;
}
.eticket_title{
  overflow: auto;
  height: calc(100vh - 450px);
}
.eticket {
  height: 82px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px;
  padding-right: 8px;
  padding-left: 8px;
  // box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
  border-bottom: 1px solid #e5e5e5;

  .eticket-title {
    font-size: 16px;
    color: #000;
    font-family: "Audi-Normal";
  }

  .eticket-btn-change {
    display: flex;
    align-items: center;
    height: 100%;
    .eticket-change-name {
      font-size: 16px;
      color: #000;
      margin-right: 10px;
      font-family: "Audi-Normal";
    }
    .eticket-btn-icon {
      align-items: center;
      width: 22px;
      height: 22px;
    }
  }
}
.btn-height {
  height: 70px;
}
.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  // bottom: 0px;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
  bottom: env(safe-area-inset-bottom);
}
.btn-delete-wrapper {
  padding: 0 16px;
  background: #ffffff;
}

.notList{
  text-align: center;
  font-size: 14px;
  margin-top: 120px;
}
</style>
