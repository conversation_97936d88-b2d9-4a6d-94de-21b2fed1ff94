import request from '@/router/axios'
import api from '@/config/url'
import URL from './url'

const baseUrl = api.BaseApiUrl

export const judgeChuangshi = (params) => request({
  url: `${baseUrl + URL.judgeChuangshi}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})
export const getCustomSeries = (params) => request({
  url: `${baseUrl + URL.getCustomSeries}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})
export const getModelList = (params) => request({
  url: `${baseUrl + URL.getModelList}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})
export const createTestDriver = (params) => request({
  url: `${baseUrl + URL.createTestDriver}`,
  method: 'POST',
  data: {
    ...params
  },
  channel: 'oneapp'
})
export const createTouristYouke = (params) => request({
  url: `${baseUrl + URL.createTouristYouke}`,
  method: 'POST',
  data: {
    ...params
  },
  channel: 'oneapp'
})
export const createInviteTestDriver = (params) => request({
  url: `${baseUrl + URL.createInviteTestDriver}`,
  method: 'POST',
  data: {
    ...params
  },
  channel: 'oneapp'
})
export const getOrgsForAppointmentList = (params) => request({
  url: `${baseUrl + URL.getOrgsForAppointmentList}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const testDriverDetail = (params) => request({
  url: `${baseUrl + URL.testDriverDetail}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const getListResource = (params) => request({
  url: `${baseUrl + URL.getListResource}`,
  method: 'POST',
  data: {
    ...params
  },
  channel: 'oneapp'
})

export const getListTestDrive = (params) => request({
  url: `${baseUrl + URL.getListTestDrive}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})


export const testDriverList = (params) => request({
  url: `${baseUrl + URL.testDriverList}`,
  method: 'POST',
  data: {
    ...params
  },
  channel: 'oneapp'
})


export const updateTestDrive = (params) => request({
  url: `${baseUrl + URL.updateTestDrive}`,
  method: 'PUT',
  data: {
    ...params
  },
  channel: 'oneapp'
})

export const cancelOrUpdate = (params) => request({
  url: `${baseUrl + URL.cancelOrUpdate}`,
  method: 'PUT',
  data: {
    ...params
  },
  channel: 'oneapp'
})
export const identifyIdCard = (params) => request({
  url: `${baseUrl + URL.identifyIdCard}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})
export const identifyDrivingLicence = (params) => request({
  url: `${baseUrl + URL.identifyDrivingLicence}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})
export const getShareReport = (params) => request({
  url: `${baseUrl + URL.getShareReport}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const syncReport = (params) => request({
  url: `${baseUrl + URL.syncReport}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})
export const shareReport = (params) => request({
  url: `${baseUrl + URL.shareReport}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const getVeryLongReservationDealerList = (params) => request({
  url: `${baseUrl + URL.getVeryLongReservationDealerList}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const getVeryLongReservationListResource = (params) => request({
  url: `${baseUrl + URL.getVeryLongReservationListResource}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})

export const getVeryLongReservationTestDriveDetailByAppoid = (params) => request({
  url: `${baseUrl + URL.getVeryLongReservationTestDriveDetailByAppoid}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const getVeryLongReservationUrl = (params) => request({
  url: `${baseUrl + URL.getVeryLongReservationUrl}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const getVeryLongReservationEarlyEnd = (params) => request({
  url: `${baseUrl + URL.getVeryLongReservationEarlyEnd}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const getVeryLongReservationTestDriveReport = (params) => request({
  url: `${baseUrl + URL.getVeryLongReservationTestDriveReport}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})

export const postVeryLongReservationCancel = (params) => request({
  url: `${baseUrl + URL.postVeryLongReservationCancel}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})
export const postVeryLongReservationSubmit = (params) => request({
  url: `${baseUrl + URL.postVeryLongReservationSubmit}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})
