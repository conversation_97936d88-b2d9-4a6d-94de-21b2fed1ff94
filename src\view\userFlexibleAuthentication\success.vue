<template>
    <div class="container">
      <!-- <navigation
        title=""
        :back-type="backType"
      /> -->
      <navigation
      title=""
      :isFamily="'medium'"
      :titleSize="'16'"
      :isBorder="false"
    />
      <div class="connter">
        <div class="card-img">
          <img src="../../assets/img/contract-success.png">
        </div>
        <p>审核成功</p>
      </div>
      <div class="bottom_style">
        <div class="btn-delete-wrapper">
          <AudiButton
            @click="onSubmit"
            :text="'再次认证'"
            color="white"
            font-size="16px"
            height="55px"
          />
        </div>
        <div class="btn-delete-wrapper">
            <AudiButton
            @click="onView"
            :text="'查看认证信息'"
            color="black"
            font-size="16px"
            height="55px"
          />
        </div>

        
      </div>
    </div>
  </template>
  
  <script>
  import Vue from 'vue'
  import navigation from '../../components/navigation'
  import AudiButton from '@/components/audi-button'
  import { callNative } from '@/utils'
  
  export default {
    components: { navigation, AudiButton },
    data() {
      return {
        backType: 'app'
      }
    },
    mounted() {
      // 判断跳转来源
      const { backType } = this.$route.query
      if (backType) {
        this.backType = backType
      } else {
        this.backType = 'app'
      }
    },
    methods: {
      onSubmit() {
        this.$router.push({
            name: 'userFlexibleAuthentication-information',
            query:{
              tag:this.$route.query.tag
            }
          })
      },
      onView() {
        this.$router.push({
            name: 'userFlexibleAuthentication-successView',
            query:{
              tag:this.$route.query.tag,
              id:this.$route.query.id
            }
          })
      }
    }
  }
  </script>
  
  <style lang="less" scoped>
  .container{
      display: flex;
      flex-wrap: wrap;
      height: auto;
      .connter{
      padding: 0 16px;
     width: 100%;
     
         p{
          text-align: center;
          margin:0;
          line-height:32px;
          font-size: 18px;
          margin-bottom: 16px;
          font-family: 'Audi-Normal';
          color:#000000;
      }
      .card-img{
          flex:1;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          margin-top: 116px;
  
          img{
              display: block;
              width:72px;
              height: 72px;
              margin-bottom: 24px;
          }
      }
  
  }
      .bottom_style {
          width: 91%;
          background: #ffffff;
          display: flex;
        //   flex-direction: column;
          justify-content: space-between;
          position: absolute;
          bottom: 0px;
          padding: 0 16px;
          left: 0px;
      }
      .btn-delete-wrapper {
         margin-bottom: 39px;
         width: 49%;
      }
  }
  
  </style>
  