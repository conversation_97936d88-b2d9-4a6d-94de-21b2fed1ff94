<template>
  <div class="unsubscribeSucceeds">
    <div class="connent">
      <div class="connent-img">
        <img src="../../assets/error.png" />
      </div>
      <h3>认证失败</h3>
      <p>{{ msg || '您所选的车辆类型与证件不一致，请确认车辆类型并重新上传证件' }}</p>
    </div>
    <div class="bottom_style">
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="goBack"
          text="完成"
          color="#000"
          font-size="15px"
          height="48px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { callNative } from '@/utils'
import AudiButton from '@/components/audi-button'
import api from '@/config/url'

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      msg: ''
    }
  },
  mounted() {
    const { msg } = this.$route.query
    this.msg = msg
    // this.drivingLicenseInfo(idx)
  },
  methods: {
    goBack() {
      callNative('close', {})
    }
  }
}
</script>

<style scoped lang="less">
// @import url("../../assets/style/scroll.less");
// @import url("../../assets/style/buttons.less");

.unsubscribeSucceeds {
  padding: 0 16px;
  height: 100%;
  display: flex;
  flex-wrap: wrap;

  .connent {
    flex: 1;
    padding-top: 80px;
  }

  .connent-img {
    display: flex;
    justify-content: center;
    width: 100%;

    img {
      width: 121px;
      height: 121px;
    }
  }
  h3 {
    text-align: center;
    margin-bottom: 12px;
    font-size: 16px;
    color: #1a1a1a;
    line-height: 24px;
  }
  p {
    padding: 0 20px;
    text-align: center;
    font-size: 14px;
    color: #999999;
    line-height: 22px;
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  padding-bottom: 38px;
  left: 0px;

  .checkbox_button {
    margin-left: 16px;
    margin-bottom: 5px;
    width: 18px;
    height: 18px;
    color: #000;
  }

  .checkbox_style {
    display: flex;
    height: 20px;
    justify-content: space-between;
    font-family: 'Audi-Normal';
    color: #999999;
    width: 100%;
    font-size: 16px;
    margin-bottom: 16px;

    span {
      font-size: 17px;
      color: #000;
      font-family: 'Audi-Normal';
    }

    .checkbox_styleRight {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #000;
      margin-right: 16px;
    }
  }
}

.btn-delete-wrapper {
  margin: 0 16px;
}
</style>
