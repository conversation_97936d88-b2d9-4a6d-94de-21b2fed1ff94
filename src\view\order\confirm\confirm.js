/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-03 09:39:03
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-23 10:00:18
 * @FilePath     : \src\view\order\confirm\confirm.js
 * @Descripttion :
 */

import Vue from 'vue'
import {
  Button, Toast, Popup
} from 'vant'
import HeaderCustom from '@/components/header-custom.vue'
import {
  getMyOrders, getCarConfig, canXdUpdateInfo, getAPITimeOutTesting, refreshCcid, checkRefreshStockCar, getDealerByCode
} from '@/api/api'
import store from '@/store/index'
import {
  modifyBuyType, certificateJson, paymentMethod
} from '@/config/constant'
import ORDER_PAGE_DATA from '@/config/order-page.data'
import { ORDER_STATUS_DISTRICT } from '@/config/conf.data'
import {
  checkType
} from '@/utils/'
import network from '@/components/network.vue'
import { mapGetters } from 'vuex'
import { createOrder, payOrder } from '../payments/payments'
import OrderBuyer from '../buyer/buyer.vue'
import OrderOption from '../option/option.vue'


Vue.use(Button).use(Popup)

export default {
  components: {
    'header-custom': HeaderCustom,
    'order-option': OrderOption,
    'order-buyer': OrderBuyer,
    network
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      goods: {},
      optionList: [],
      buyerInfo: {},
      operatingBtn: false,
      ORDER_PAGE_DATA,
      orderId: '',
      fields: '',
      banEdit: false,
      orderStatus: '',
      ORDER_STATUS_DISTRICT,
      unOrderInfo: {},
      payLoadingText: '',
      payLoading: false,
      ccid: '',
      readed: '',
      popPayShow: false,
      isOneStep: true,
      minipay: 0
    }
  },
  created() {
    this.handleProcessData()
    window.console.log('---------执行------')
    this.$EventBus.$on('audi_api_order_detail_pay_timeout', () => {
      this.payLoadingText = ''
      // 网球请求
      localStorage.setItem('oldCcid', 'oldCcid')
    })
  },
  methods: {
    ...mapGetters(['getDevice']),
    handleProcessData() {
      const { $route: { query: { orderId, minipay, env } } } = this
      if(env != 'minip') {
        this.orderId = orderId || sessionStorage.getItem('orderId') || ''
      }else {
        this.orderId = orderId
      }
      
      this.minipay = minipay
      // 有orderId是为大定确认订单
      this.getMineOrderDetails(orderId || '')
      if (orderId) {
        this.canXdUpdateInfo(orderId)
      }
      getAPITimeOutTesting()
      Toast.clear()
    },
    getPageDataInfo(buyType, moreContact, carOwnerCertificateType, carBuyerInfo, dealerInfo, carCustomInfo, ccid) {
      const buyTypeName = modifyBuyType?.filter((b) => b.type === buyType)[0]?.text || ''
      const [IDType, IDNumber] = moreContact?.split(',') || ['', '']
      const IDTypeName = certificateJson[IDType] || ''
      const carOwnerCertificateTypeName = certificateJson[carOwnerCertificateType] || ''
      this.buyerInfo = {
        ...carBuyerInfo, ...dealerInfo, ...carCustomInfo, buyTypeName, IDTypeName, IDNumber, carOwnerCertificateTypeName
      }
      console.log('%c [ this.buyerInfo ]-60', 'font-size:14px; background:#cf222e; color:#fff;', this.buyerInfo, carBuyerInfo)
      if (ccid) {
        this.getCarsConfigData(ccid)
      }
    },
    async getMineOrderDetails(orderId) {
      if (orderId) {
        const { data: { data, code } } = await getMyOrders({ orderId })
        if (code === '00' && data && Object.keys(data).length) {
          const {
            carBuyerInfo, carBuyerInfo: { buyType, moreContact, carOwnerCertificateType }, dealerInfo, carCustomInfo,
            carCustomInfo: { carCustomId: ccid },  orderStatus, payList
          } = data
          if (!ORDER_STATUS_DISTRICT.DA_DING_BEFORE.includes(orderStatus) && +this.minipay !== 1) {
            this.$router.push({
              name: 'new-money-detail',
              query: { orderId }
            })
          }
          this.isOneStep = false
          if (payList?.length) {
            const [{ payPhase }] = payList || [{ payPhase: '' }]
            this.isOneStep = payPhase === '11'
          }
          ccid && (this.ccid = ccid)
          this.orderStatus = orderStatus
          this.getPageDataInfo(buyType, moreContact, carOwnerCertificateType, carBuyerInfo, dealerInfo, carCustomInfo, ccid)
        }
      } else {
        const data = await store.dispatch('GetOrder')
        let carLicenseCityNameStr = (data.dealerInfo?.provinceName + '/' + data.dealerInfo?.cityName)
        let cityCode = data.dealerInfo?.cityCode
        this.unOrderInfo = {
          ...data,
          carLicenseCityName: carLicenseCityNameStr ? carLicenseCityNameStr : data.carLicenseCityName,
          carLicenseCityCode: cityCode ? cityCode : data.carLicenseCityCode
        }
        const {
          carBuyerInfo, carBuyerInfo: { buyType, moreContact, carOwnerCertificateType }, carCustomId: ccid,
          dealerInfo, carLicenseCityName
        } = data
        this.getPageDataInfo(buyType, moreContact, carOwnerCertificateType, carBuyerInfo, dealerInfo, { carLicenseCityName: carLicenseCityNameStr ? carLicenseCityNameStr : carLicenseCityName }, ccid)
      }
    },
    async getCarsConfigData(ccid) {
      const { data: { data, code } } = await getCarConfig({ ccid, page: 'orderDetailConfirm' })
      if (code === '00' && data && Object.keys(data).length) {
        const {
          depositType, configDetail: {
            totalPrice,
            carModel: {
              modelCode, modelNameCn, modelLineCode, modelPrice
            }, carSeries: { seriesCode }, optionList,
            outsideColor,
            insideColor
          }
        } = data || {}
        const { orderStatus } = this
        this.goods = {
          totalPrice,
          modelNameCn,
          modelPrice,
          modelLineCode,
          seriesCode,
          orderStatus,
          ccid
        }

        this.optionList = [insideColor, outsideColor, optionList]
        this.operatingBtn = true

        const { env, minipay, orderId } = this.$route.query
        if (env === 'minip' && +minipay === 1 && orderId && +localStorage.getItem('minipay') === 1) {
          this.$nextTick(() => {
            const [formsBox] = document.getElementsByClassName('affirm-order-info-box')
            formsBox.scrollIntoView({
              behavior: 'instant',
              block: 'start'
            })
            this.popPayShow = true
          })
          // this.handlePayOrderAction()
        }
      }
    },
    // 小订后是否可以更新订单的信息
    async canXdUpdateInfo(orderId) {
      const { data: { data: canXdStatus } } = await canXdUpdateInfo({ orderId })
      this.canXdStatus = canXdStatus
      const { ORDER_PAGE_DATA: { BOOK_ORDER_UPDATE_BUYER_INFO } } = this
      const index = checkType(canXdStatus) !== 'Number' ? 0 : canXdStatus
      let fields = ''
      if (index && BOOK_ORDER_UPDATE_BUYER_INFO?.length) {
        const BUYER_INFO = BOOK_ORDER_UPDATE_BUYER_INFO[index] ?? ''
        if (BUYER_INFO?.length && checkType(BUYER_INFO) === 'Array') {
          fields = BUYER_INFO.join(',')
        } else if (BUYER_INFO && checkType(BUYER_INFO) === 'Boolean') {
          fields = 'all'
        }
      } else {
        this.banEdit = true
      }
      this.fields = fields

      console.log('%c [ update info fields ]-157', 'font-size:14px; background:#cf222e; color:#fff;', fields)
    },
    goToEditOrderData() {
      const { orderId, fields } = this
      const { dealerCode } = this.$route.query
      this.$router.push({
        path: '/order-info-modify',
        query: { from: orderId ? 'order' : 'forms', fields: orderId ? fields : 'all', orderId,dealerCode:dealerCode ||'' }
      })
    },
    async handlePayOrderAction(a = 0) {
      if (this.popPayShow) {
        this.popPayShow = false
      }
      const { orderId,ccid, goods, buyerInfo, orderStatus, getDevice, isOneStep
      } = this || ''
      const {
        env, minipay, oos
      } = this.$route.query
      const step = isOneStep ? 1 : 0
      const { nativeApp } = getDevice() || { nativeApp: false }
      // eslint-disable-next-line no-nested-ternary
      const device = env === 'minip' ? env : (nativeApp ? 'native' : 'html5')
      const location = window.location
      let { unOrderInfo } = this || ''
      if (device === 'minip') localStorage.setItem('minipay', a)

      

      if (orderId) {
        if ((orderStatus === '30' && device !== 'minip') || oos === '30') {
          const queryData = goods
          delete queryData.modelNameCn
          return this.$router.push({
            path: '/buycar-agreement',
            query: {
              orderId,
              buyType: buyerInfo.buyType,
              ...queryData
            }
          })
          // payOrder(orderId, goods)
        }
        const {
          goods: { seriesCode, modelLineCode }
        } = this
        this.payLoadingText = '支付中 ...'
        return payOrder(
          orderId,
          {
            seriesCode,
            modelLineCode,
            orderStatus: oos || orderStatus,
            ccid
          },
          {
            device,
            location,
            minipay,
            step
          },
          this.buyerInfo?.mobile
        )
      }
      this.payLoadingText = '支付中 ...'
      // store.commit('showLoading')
      if (unOrderInfo) {
        unOrderInfo = await this.newCcidHandler(unOrderInfo)
        console.log(
          '%c [ unOrderInfo.dealerInfo ]-212',
          'font-size:14px; background:#cf222e; color:#fff;',
          unOrderInfo.dealerInfo,
          unOrderInfo.dealerInfo && Object.keys(unOrderInfo.dealerInfo)?.length
        )
        if (
          unOrderInfo.dealerInfo &&
          Object.keys(unOrderInfo.dealerInfo)?.length
        ) {
          delete unOrderInfo.dealerInfo
        }
        if (
          !(await createOrder(
            unOrderInfo,
            goods,
            {
              device,
              location,
              minipay,
              step
            },
            this.buyerInfo?.mobile
          ))
        ) {
          // store.commit('hideLoading')
          this.payLoadingText = ''
        }
      }
    },

    async newCcidHandler(unOrderInfo) {
      const ccid = unOrderInfo.carCustomId
      const oldCcid = localStorage.getItem('oldCcid')
      if (oldCcid) {
        const { error, newCcid } = await this.newCarconfig(ccid) || ' '
        console.log('%c [ error, newCcid ]-2051', 'font-size:14px; background:#cf222e; color:#fff;', error, newCcid)
        if (error) {
          return Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: error || '网络请求错误',
            forbidClick: true,
            duration: 800
          })
        }
        if (newCcid) {
          // 由于用户前面已下单直接返回当前页面，等场景下，需要刷新ccid下单，这里新增业务只是仅限通知服务端
          const { data: { code, message } } = await checkRefreshStockCar({
            ccid: newCcid,
            oldccid: ccid
          }) || ''

          if (code !== '00') {
            return Toast({
              className: 'toast-dark-mini toast-pos-middle',
              message: message || '网络请求错误',
              forbidClick: true,
              duration: 800
            })
          }

          unOrderInfo.carCustomId = newCcid
        }
      }
      return unOrderInfo
    },

    async newCarconfig(ccid) {
      const { data } = await refreshCcid({ ccid })

      console.log('%c [ refreshCcid ]-2016', 'font-size:14px; background:#cf222e; color:#fff;', data)
      const newCcid = data.data || ''
      if (!newCcid) {
        return { error: data.message || '切换ccid 出错' }
      }
      this.$store.commit('updateCcid', newCcid)
      localStorage.setItem('ccid', newCcid)
      return { newCcid }
      // this.placeAnOrder()
    },
    handleToOrderDetail() {
      localStorage.removeItem('minipay')
      if (this.popPayShow) {
        this.popPayShow = false
      }
      const { orderId } = this.$route.query
      this.$router.push({
        name: 'new-money-detail',
        query: { orderId }
      })
    },
    networkReload() {
      this.handleProcessData()
    }

  },
  destroyed() {}
}
