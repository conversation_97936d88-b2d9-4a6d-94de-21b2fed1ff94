<template>
  <div class="_container">
    <div v-if="typeTitle" class="_box-fixed" :style="{ 'top': headerHeight + 'px' }">
      <div class="_title-box" style="padding: 0 15px;">
        <div class="_title">
          选择日期
        </div>
        <div class="_date-indicator">
          <van-icon name="arrow-left" @click="getmonthIndex('-')" :color="navIndex === 0 ? '#B3B3B3' : ''" />
          <div v-for="(item, index) in timeData" :key="index">
            <div class="_month" v-if="index === navIndex">
              {{ item.month }}月
            </div>
          </div>
          <van-icon name="arrow" @click="getmonthIndex('+')" :color="navIndex === timeData.length - 1 ? '#B3B3B3' : ''" />
        </div>
      </div>

      <div>
        <van-swipe ref="swipe" class="my-swipe" @change="onChange" :autoplay="0" :loop="false" :show-indicators="false">
          <div v-for="(item, index) in timeData" :key="index">
            <van-swipe-item>
              <div class="_calendar-box">
                <div class="_week-heard">
                  <div :class="[index2 === 0 || index2 === 6 ? 'text-glay' : '']" style="width: calc(100% / 7)" v-for="(val, index2) in weeklist" :key="index2">
                    {{ val }}
                  </div>
                </div>
                <div class="_day-content">
                  <div :class="[val.status === 2 ? 'text-glay':'']" v-for="(val,index2) in item.value" :key="index2" @click="getDateIndex(index,index2)">
                    <!-- <div class="_tips" v-if="val.mType === true">
                      {{ val.month }}月
                    </div> -->
                    <div class="_date" :class="[monthIndex === index && dayIndex === index2 &&  !val.man? 'select-black' : '']">
                      {{ val.day }}
                    </div>
                    <img v-if="val.man === true && typeTitle === 'appointmentTime'" class="_man" src="../../assets/img/icon-man.png">
                  </div>
                </div>
              </div>
            </van-swipe-item>
          </div>
        </van-swipe>
        <div ref="calendarbox"></div>

      </div>
    </div>

    <div class="_time-list" :style="{'margin-top': offsetTop + 'px'}">
      <div class="_title-box">
        <div class="_title">
          选择时间
        </div>
      </div>
      <van-collapse v-model="activeNames" accordion>
        <div v-for="(item, index) in dayData" :key="index">
          <van-collapse-item :title="item.title" :title-class="item.status === 2 && 'text-glay'" :name="index">
            <div class="_time-box">
              <div :class="[ val.status === 2 ? 'text-glay' : '', timeIndex1 === index && timeIndex2 === index2 ? 'select-black' : '' ]"
                v-for="(val, index2) in item.value" :key="index2" @click="getTimeIndex(index,index2)">
                <p>{{ val.time }}</p>
                <img v-if="val.status === 2 && typeTitle === 'appointmentTime'" class="_man" src="../../assets/img/icon-man.png">
              </div>
            </div>
          </van-collapse-item>
        </div>
      </van-collapse>
    </div>

    <div class="bottom_style">
      <div class="btn-delete-wrapper">
        <AudiButton @click="onBtnNext" :text="'下一步'" color="black" font-size="16px" height="50px" />
      </div>
    </div>
  </div>
</template>

<script>
  import dayjs from 'dayjs'
  import Vue from 'vue'
  import {
    Collapse,
    CollapseItem,
    Swipe,
    SwipeItem
  } from 'vant'
  import { callNative } from '@/utils'
  // import { timeDemo } from "./timeDemo.js";
  import AudiButton from '@/components/audi-button'

  import {
    getAfterSaleListResource
  } from '@/api/api'

  Vue.use(Swipe)
  Vue.use(SwipeItem)
  Vue.use(Collapse)
  Vue.use(CollapseItem)
  export default {
    components: {
      AudiButton
    },
    name: 'Name',
    data() {
      return {
        timeData: [],
        weeklist: ['日', '一', '二', '三', '四', '五', '六'],
        dayIndex: 0, // 选中的日期下标
        monthIndex: 0, // 选中月份下标
        navIndex: 0, // 月份下标

        dayData: [], // 选中的时间数据
        timeIndex1: null, // 时间段下标
        timeIndex2: null, // 事件下标
        activeNames: [], // 时间展开下标

        selectDate: '', // 选择的日期
        selectTime: '', // 选择的时间
        typeTitle: 'appointmentTime',
        takeDate: '', // 选择的取车时间
        sendDate: '', // 选择的送车时间
        timeCode:null,
        headerHeight: 50,
        offsetTop: 310, //上边距
      }
    },
    props: {
      timelist: {
        type: Array,
        default: () => []

      },
    },
    watch: {
      timelist: {
        handler(newValue, oldValue) {
          this.getSelectDateData()
        },
        deep: true // 深度判断

      }
    },

    mounted() {
      this.typeTitle = this.$route.query?.type
      this.takeDate = this.$route.query?.takeDate
      this.sendDate = this.$route.query?.sendDate
      if (this.typeTitle === 'TakeDatetime') {
        this.$store.state.title = '取车时间'
      } else if (this.typeTitle === 'SendDatetime') {
        this.$store.state.title = '送车时间'
      }

      this.$nextTick(i => {
        setTimeout(() => {
          this.offsetTop = this.$refs.calendarbox.offsetTop
          if(this.offsetTop < 100){
            this.offsetTop = 310
          }
        }, 1000)
      })

      this.getHeight()
    },

    methods: {
      //获取日期的高度
      getOffsetTop() {

      },
      // 获取导航栏高度
      async getHeight() {

        const data = await callNative('navigationBarHeight', {})
        this.headerHeight = `${data.statusBarHeight + data.navigationBarHeight}`
      },
      // beginDate:开始时间 endDate:结束时间
      async getSelectDateData() {
        const timeDemo = this.timelist
        console.log(timeDemo)
        // 将获取的时间数据 提取月份
        const timeDataDemo = [
          ...new Set(timeDemo.map((i) => dayjs(i.date).month() + 1))
        ].map((i) => ({ month: i, value: [] }))
        // 将所有时间的数据按月份拆开 重组成行的数组
        timeDemo.forEach((i, index) => {
          i = { ...i, status: 2, ...i.resources.find((z) => z.status === 1) }
          timeDataDemo.forEach((j) => {
            if (j.month === dayjs(i.date).month() + 1) {
              j.value.push({ ...i, day: dayjs(i.date).date() })
            }
          })
        })
        // 增加是否 满 的标识
        timeDataDemo.forEach((i) => {
          i.value.forEach((j) => {
            if (j.status === 2) {
              if (this.typeTitle === 'appointmentTime') {
                if (j.resources?.length) {
                  j.man = true
                }
              } else {
                j.man = true
              }
            }
          })
        })
        // 头一个月的日期操作
        if (timeDataDemo.length > 0) {
          // 判断时间区间中最小的日期是星期几  0-6  0星期天
          const startDay = timeDataDemo[0].value[0].date // 开始日期
          const endDay = timeDataDemo[0].value[timeDataDemo[0].value.length - 1].date // 结束日期
          let dateStart = dayjs(startDay).date()
          const weekStart = dayjs(startDay).day()
          if(dateStart !== 1){
            dateStart = dayjs(startDay).subtract(1, 'date').format('D')
          }else{
            dateStart -= 1
          }
          // 最开始的日期不是星期天就需要追加置灰的日期  头一个月的排列开始
          // if (dateStart > 0) {
          let fillDay = new Array(parseInt(dateStart)).fill({})
          fillDay.forEach((i, index) => {
            const dayValue = dayjs(startDay).subtract(index + 1, 'day').format('D')
            fillDay[index] = {
              day: dayValue,
              status: 2
            }
          })
          const yearStart = dayjs(startDay).year()
          const monthStart = dayjs(startDay).month() + 1
          const weekOne = dayjs(yearStart + '-' + monthStart + '-01').day()  //获取当月1号的星期数
          let fillDay2 = new Array(weekOne).fill({})
          fillDay.push(...fillDay2)

          fillDay = fillDay.reverse()
          this.dayIndex = fillDay.length // 赋值最初选中的天数，然后用来拿这一天的可选时间列表
          // 判断前面的天数是否满了 满了就往后排
          const endDate1 = dayjs(startDay).add(1, 'month').format('YYYY-MM-DD')
          const endDate2 = dayjs(endDate1).subtract(1, 'd').format('D')
          timeDataDemo[0].value.forEach((i, index) => {
            if (i.status === 2 && this.dayIndex === index) {
              this.dayIndex += 1
              if (this.dayIndex > endDate2) {
                this.dayIndex = 1
              }
            }
          })
          timeDataDemo[0].value.unshift(...fillDay)
          // }
          // 头一个月的排列结束
          // const weekEnd = dayjs(endDay).day() // 获取末尾的时间是星期几
          // if (weekEnd !== 6) {
          //   const fillDay = new Array(6 - weekEnd).fill({})
          //   fillDay.forEach((i, index) => {
          //     const day = dayjs(endDay).add(index + 1, 'day').format('D')
          //     fillDay[index] = {
          //       day: day,
          //       month: dayjs(startDay).add(1, 'month').format('MM'),
          //       status: 2
          //     }
          //     if (day === '1') fillDay[index].mType = true
          //   })
          //   timeDataDemo[0].value.push(...fillDay)
          // }
        }
        // 判断是否有两个月
        if (timeDataDemo.length > 1) {
          // 操作和之前类似
          const startDay = timeDataDemo[1].value[0].date // 开始日期
          const endDay = timeDataDemo[1].value[timeDataDemo[1].value.length - 1].date // 结束日期
          const weekStart = dayjs(startDay).day()
          if (weekStart > 0) {
            let fillDay = new Array(weekStart).fill({})
            fillDay.forEach((i, index) => {
              fillDay[index] = {
                // day: dayjs(startDay).subtract(index + 1, 'day').format('D'),
                month: dayjs(startDay).subtract(1, 'month').format('MM'),
                status: 2
              }
            })
            fillDay[0].mType = true
            fillDay = fillDay.reverse()
            timeDataDemo[1].value.unshift(...fillDay)
          }
          const dateEnd = dayjs(endDay).date() // 获取末尾的时间是星期几
          const endDate1 = dayjs(startDay).add(1, 'month').format('YYYY-MM-DD')
          const endDate2 = dayjs(endDate1).subtract(1, 'd').format('D')
          const fillDay = new Array(endDate2 - dateEnd).fill({})
          fillDay.forEach((i, index) => {
            fillDay[index] = {
              day: dayjs(endDay).add(index + 1, 'day').format('D'),
              status: 2
            }
          })
          timeDataDemo[1].value.push(...fillDay)
        }

        // 赋值  //跳过排满的日期
        this.timeData = timeDataDemo
        let dayData = null
        console.log(timeDataDemo)
        this.timeData.forEach((i, index1) => {
          if (index1 === this.monthIndex) {
            i.value.forEach((j, index2) => {
              if (!dayData) {
                if (j.status === 1 && index2 === this.dayIndex) {
                  dayData = j
                }
                if (j.status === 2 && index2 === this.dayIndex) {
                  this.dayIndex = this.dayIndex + 1
                }
              }
            })
            if (!dayData && this.monthIndex !== this.timeData.length) {
              this.monthIndex = this.monthIndex + 1
            }
          }
        })
        if (this.dayIndex > 31) { //一个合适的都没有的情况下 保证页面渲染正常
          this.monthIndex = 0
          const resources = this.timeData[0].value.find(i => i.resources)
          this.timeData[0].value.forEach((i, index) => {
            if (resources.date === i.date) this.dayIndex = index
          })
        }
        this.getTimeData(this.dayIndex)

      },

      onChange(e) { //滑动切换
        this.navIndex = e
        // this.dayIndex = -1
      },

      getmonthIndex(type) { //点击切换
        // 选择日期 左右箭头点击切换月份事件
        let monthIndex = this.monthIndex
        if (type === '-' && this.monthIndex !== 0) {
          monthIndex -= 1
        }
        if (type === '+' && this.monthIndex !== this.timeData.length - 1) {
          monthIndex += 1
        }
        this.navIndex = monthIndex
        // this.dayIndex = -1
        this.$refs.swipe.swipeTo(monthIndex, false)
      },

      getDateIndex(monthIndex, dayIndex) { // 获取某日的下标
        if (this.timeData[monthIndex].value[dayIndex].status === 1) {
          this.monthIndex = monthIndex
          this.dayIndex = dayIndex
          this.getTimeData(dayIndex)
        }
      },

      getTimeIndex(timeIndex1, timeIndex2) { // 获取具体时间的下标
        if (this.dayData[timeIndex1].value[timeIndex2].status === 1) {
          this.timeIndex1 = timeIndex1
          this.timeIndex2 = timeIndex2

          this.selectTime = this.dayData[this.timeIndex1].value[this.timeIndex2].startAt
          this.timeCode = this.dayData[this.timeIndex1].value[this.timeIndex2].timeCode
        }
      },

      getTimeData(index) {
        // 获取选中时间
        const asdasd = this.timeData
        const aca = this.timeData[this.monthIndex]
        const dayData = this.timeData[this.monthIndex].value[index]
        this.selectDate = dayData.date
        // 转化时间 并重组数组
        const hourMinList = dayData.resources.map((i) => ({
          ...i,
          time: dayjs(`${dayData.date} ${i.startAt}`).format('HH:mm')
        }))

        if (this.typeTitle === 'appointmentTime') { //服务预约

          // 获取三个区间的时间数据
          const time1 = {
            title: '09:00 —— 11:00',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 8 && hour < 12
            })
          }
          const time2 = {
            title: '12:00 —— 14:00',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 11 && hour < 15
            })
          }
          const time3 = {
            title: '15:00 —— 17:00',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 14 && hour <= 17
            })
          }
          // 赋值
          this.dayData = [time1, time2, time3]
           //判断时间范围标题是否需要置灰
          this.dayData.forEach(i=>{
            const val = i.value.find(j=> j.status === 1)
            if(val)i.status = 1
            else i.status = 2
          })
          this.activeNames = this.dayData.map((i, index) => index)
        } else { //····取送车
          // 获取三个区间的时间数据
          const time0 = {
            title: '08:00 —— 08:55',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 7 && hour < 9
            })
          }
          const time1 = {
            title: '09:00 —— 09:55',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 8 && hour < 10
            })
          }
          const time2 = {
            title: '10:00 —— 10:55',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 9 && hour < 11
            })
          }
          const time3 = {
            title: '11:00 —— 11:55',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 10 && hour < 12
            })
          }
          const time4 = {
            title: '12:00 —— 12:55',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 11 && hour < 13
            })
          }
          const time5 = {
            title: '13:00 —— 14:00',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 12 && hour < 14
            })
          }
          const time6 = {
            title: '14:00 —— 14:55',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 13 && hour < 15
            })
          }
          const time7 = {
            title: '15:00 —— 15:55',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 14 && hour < 16
            })
          }
          const time8 = {
            title: '16:00 —— 16:55',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 15 && hour < 17
            })
          }
          const time9 = {
            title: '17:00',
            value: hourMinList.filter((i) => {
              const hour = dayjs(`${dayData.date} ${i.time}`).format('HH')
              return hour > 16 && hour < 18
            })
          }

          // 赋值
          this.dayData = [time0,time1, time2, time3, time4, time5, time6, time7, time8,time9]
          //判断时间范围标题是否需要置灰
          this.dayData.forEach(i=>{
            const val = i.value.find(j=> j.status === 1)
            if(val)i.status = 1
            else i.status = 2
          })
          this.activeNames = this.dayData.map((i, index) => index)
        }
      },

      // 下一步
      onBtnNext() {
        if (this.selectTime === '') {
          callNative('toast', { type: 'fail', message: '请选择预约时间' })
          return
        }

        if (this.typeTitle === 'appointmentTime') {
          this.$store.commit('saveSelectServiceTime', { appointmentTime: `${this.selectDate} ${this.selectTime}`,timeCode:this.timeCode})
        }
        if (this.typeTitle === 'TakeDatetime') {
          if (this.sendDate !== '') {
            var time1 = new Date((`${this.sendDate}:00`).replace(/-/g, '/')).getTime()
            var time2 = new Date((`${this.selectDate} ${this.selectTime}` + ':00').replace(/-/g, '/')).getTime()
            var str = time1 - time2 // 得到毫秒数
            console.log('相差时间小时', str / 3600000)
            if (str / 3600000 < 3) {
              this.showDialog('爱车服务和取送路程都需要时间，取车只能送车3小时以前，请重新选择')
              return
            }
          }
          this.$store.commit('saveTakeDatetime', { appointmentTime: `${this.selectDate} ${this.selectTime}`,timeCode:this.timeCode })
        }
        if (this.typeTitle === 'SendDatetime') {
          // 计算取送车时间间隔
          if (this.takeDate !== '') {
            var time2 = new Date((`${this.takeDate}:00`).replace(/-/g, '/')).getTime()
            var time1 = new Date((`${this.selectDate} ${this.selectTime}` + ':00').replace(/-/g, '/')).getTime()
            var str = time1 - time2 // 得到毫秒数
            console.log('相差时间小时', str / 3600000)
            if (str / 3600000 < 3) {
              this.showDialog('爱车服务和取送路程都需要时间，送车只能取车3小时以后，请重新选择')
              return
            }
          }
          this.$store.commit('saveSendDatetime', { appointmentTime: `${this.selectDate} ${this.selectTime}`,timeCode:this.timeCode })
        }
        this.$router.back(-1)
      },

      showDialog(text) {
        // 调用APP Dialog
        callNative('popup', {
          type: 'alert',
          alertparams: {
            title: '',
            desc: text,
            actions: [{
              type: 'fill',
              title: '确定'
            }]
          }
        }).then((data) => {
          if (data.type === 'fill') {

          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  div {
    font-family: "Audi-Normal";
    box-sizing: border-box;
  }

  ._container {
    padding: 0 15px;
    width: 100%;
    margin-bottom: 80px;

    ._box-fixed {
      position: fixed;
      width: 100%;
      left: 0;
      background-color: #fff;
      z-index: 1099;
    }

    ._title-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;

      ._title {
        font-size: 16px;
        width: 100%;
        color: #000000;
      }

      ._date-indicator {
        display: flex;
        align-items: center;

        ._month {
          width: 90px;
          text-align: center;
          font-size: 16px;
          font-family: "Audi-WideBold";
          font-weight: normal;
          color: #000000;
        }
      }
    }
  }

  ._calendar-box {
    padding: 0 10px;
    display: flex;
    flex-flow: column;
    width: 100%;

    ._week-heard {
      width: 100%;
      display: flex;
      align-items: center;

      div {
        height: 30px;
        margin: 5px 0;
        font-family: "Audi-WideBold";
        font-weight: normal;
        color: #333333;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
      }
    }

    ._day-content {
      width: 100%;
      display: flex;
      align-items: center;
      flex-flow: wrap;

      div {
        width: calc(100% / 7);
        height: 30px;
        margin: 5px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        position: relative;
        box-sizing: border-box;

        ._tips {
          width: 100%;
          height: 10px;
          font-size: 9px;
          color: #666666;
          text-align: center;
          position: absolute;
          top: -12px;
          left: 0;
        }

        ._date {
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
        }

        ._man {
          width: 14px;
          height: 14px;
          position: absolute;
          top: -5px;
          right: 0;
        }
      }
    }
  }

  ._time-box {
    width: 100%;
    display: flex;
    align-items: center;
    flex-flow: wrap;

    div {
      width: 25%;
      height: 30px;
      margin: 5px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #000;
      position: relative;
      font-family: "Audi-Normal";

      ._man {
        width: 14px;
        height: 14px;
        position: absolute;
        top: -5px;
        right: 0;
      }
    }
  }

  .select-black {
    background-color: #000000;
    color: #fff !important;
  }

  ::v-deep .van-cell {
    padding: 10px 0;
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;
  }

  ::v-deep .van-cell__title {
    line-height: 32px;
    font-size: 16px;
    color: #000;
    font-family: "Audi-WideBold";
  }

  ::v-deep .van-cell__right-icon {
    line-height: 32px !important;
  }

  ::v-deep .van-cell__right-icon {
    color: #000;
  }

  ::v-deep .van-cell::after {
    border: none;
  }

  ::v-deep .van-collapse-item--border::after {
    border: none;
  }

  ::v-deep .van-hairline--top-bottom::after,
  .van-hairline-unset--top-bottom::after {
    border: none !important;
  }

  .bottom_style {
    width: 100%;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: fixed;
    bottom: 0px;
    padding-top: 16px;
    padding-bottom: 16px;
    left: 0px;
  }

  .btn-delete-wrapper {
    margin: 0 16px;
  }

</style>
<style>
  .text-glay{
    color: #b3b3b3 !important;
  }
</style>
