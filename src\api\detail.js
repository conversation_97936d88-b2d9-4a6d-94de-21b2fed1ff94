import request from '../router/axios'
import api from '../config/url'
import {
  getToken
} from '../utils/auth'

const baseUrl = api.BaseApiUrl
// 获取当前用户信息
export const getUserInfo = (row) => request({
  url: `${baseUrl}/api-wap/cop-auth/api/v1/vip/users`,
  method: 'POST',
  data: row,
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// nrEHVRz8SRoACwNu_t0y57CmEYrA7i1Y
// 获取NGA城市地址
export const getCity = (data) => {
  const param = {}
  if (data.code) {
    param.parentCode = data.code
  }
  return request({
    url: `${baseUrl}/api-wap/cop-uaa-adapter/api/v1/districts`,
    method: 'GET',
    headers: {
      'x-access-token': localStorage.getItem('token')
    },
    params: param
  })
}
// 获取先行权益介绍
export const getFloors = () => request({
  url: `${baseUrl}/api-wap/audi-page/api/floors?pageAccessType=2&pageFrontCode=audi-reserve-offers`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 根据SKUID查看商品详情

export const getCopProdQuery = (params) => request({
  url: `${baseUrl}/api-wap/cop-prod-query/api/v1/products/sku-product/${params.skuid}`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  params : { page : 'orderConfirm' }
})
// 根据车系查看商品SKUID
export const getSKUID = (params) => request({
  url: `${baseUrl}/api-wap/cop-prod-query/api/v1/otds?seriesId=E413&useType=1`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  params: {
    ...params
  }
})

// 预下单接口
export const advance = (data) => request({
  url: `${baseUrl}/api-wap/cop-order-query/api/v1/pre-orders`,
  method: 'POST',
  data: {
    ...data
  },
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 获取submit-token

export const getSubmitToken = () => request({
  url: `${baseUrl}/api-wap/cop-auth/api/v1/submit-tokens`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// NGA车辆下单（带购车人信息）
export const carPlaceOrder = async (data) => {
  const res = await getSubmitToken()
  if (['200', '00'].includes(res.data.code)) {
    return request({
      url: `${baseUrl}/api-wap/audi-eshop/api/v1/orders`,
      method: 'POST',
      headers: {
        'x-access-token': localStorage.getItem('token'),
        'x-submit-token': res.data.data
      },
      data: {
        ...data
      }
    })
  }
}

//

// 车配信息查看
export const lookCar = () => request({
  url: `${baseUrl}/api-wap/cop-system/api/v1/config/audi.carconfig.h5`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 查询限量号是否发好了

export const getOrderLimitedNumberStatus = (params) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/v1/getOrderLimitedNumberStatus`,
  method: 'POST',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  params: {
    orderId: params.orderId
  }
})
// 获取新的车配code
export const newCarconfig = (params) => request({
  url: `${baseUrl}/api-wap/audi-car-config/api/v1/carconfig/accb?ccid=${params.ccid}`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 更细ccid
export const uploadCcid = (data) => request({
  url: `${baseUrl}/api-wap/audi-car-config/api/v1/carconfig?customColorId=${data.customColorId}&customModelld=${
    data.customModelId}`,
  method: 'POST',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  data: {
    customColorId: data.customColorId,
    customModelId: data.customModelId
  }
})
