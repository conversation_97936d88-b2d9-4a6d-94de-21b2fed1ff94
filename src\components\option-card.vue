<template>
  <div class="option-card">
    <div class="option-card-main">
      <div
        class="option-card-imgcard"
        @click="goOptionDetail(option)"
      >
        <img
          :src="BaseConfigrationOssHost + option.imageUrl | imgFix(640,true)"
          alt=""
        >
      </div>
      <div class="option-card-info">
        <div class="option-card-info-left">
          <word-detail
            :title="option.optionName || ''"
            :fontsize="14"
            :id="'option_' + option.optionId"
            :show="currentIndex === '4'"
          />
          <!-- <span>{{ option.optionName }}</span> -->
          <span v-if="!'YEG,YEA,CCPRO-YEB'.includes(option.optionCode)">{{ option.price | finalFormatPrice }}</span>
          <!--          <div-->
          <!--              class="_tips"-->
          <!--              v-if="option.optionName.indexOf('数字钥匙') !== -1"-->
          <!--          >-->
          <!--            *点击<P @click="toRoute()">-->
          <!--            查看兼容机型-->
          <!--          </P>-->
          <!--            查看手机是否兼容-->
          <!--          </div>-->
          <div
            class="_tips"
            v-if="option.optionName.indexOf('数字钥匙') !== -1"
          >
            由于芯片供应因素，交付时间待定
          </div>
          <div
            class="_tips"
            v-if="option.optionCode === 'PCY'"
          >
            预计2022年3月1日起交付
          </div>

          <div
            class="_tips"
            v-if="('498BZY001,498BZY002,498B2Y001,498B2Y002').includes(selectCarInfo.modelLineCode)&&option.optionCode === 'PS6'"
          >
            受产能限制，计划于01月17日起暂停供应
          </div>
           <div v-if="option.conflict" style="color: #333333;font-size: 11px;">{{option.conflict}}</div>
        </div>
        <div class="option-card-info-right">
          <img
            class="uncheck"
            v-if="option.optionName.indexOf('数字钥匙') !== -1"
            src="../assets/img/checkbox_gray.png"
            alt=""
          >
          <img
            v-else-if="
              currentOptionsList.find(
                (item) => item.optionId === option.optionId
              )
            "
            @click="unCheckOption(option)"
            src="../assets/img/checkbox_checked.png"
            alt=""
          >
          <img
            class="uncheck"
            v-else-if="option.status === 1"
            src="../assets/img/checkbox_uncheck.png"
            alt=""
          >
          <img
            v-else
            @click="checkOption(option)"
            src="../assets/img/checkbox_normal.png"
            alt=""
          >
        </div>
      </div>
      <div style="font-size: 11px;color: #333333;padding: 0 0 10px 10px;" v-if="option.optionCode == 'PDE'">
        观云型建议选装DCC动态自适应悬架，未选该配置车型资源紧张
      </div>
    </div>
    <van-popup v-model:show="show">
      <div class="ex-popup">
        <main>
          <span>{{ popupDesc }}</span>
        </main>
        <footer
          @click="iknow"
        >
          <div
            class="popup-iknow"
          >
            我知道了
          </div>
        </footer>
      </div>
    </van-popup>
    <van-popup v-model:show="show1">
      <div class="ex-popup">
        <main>
          <span>{{ popupDesc1 }}</span>
        </main>
        <footer
          @click="iknow1"
        >
          <div
            class="popup-iknow"
          >
            我知道了
          </div>
        </footer>
      </div>
    </van-popup>

    <van-popup  v-model:show="visible">
      <div class="ex-popup">
        <main style="text-align: left">
          <span>{{ popupIntro }}</span>
        </main>
        <footer
          @click="handleOk"
        >
          <div
            class="popup-iknow"
          >
            我知道了
          </div>
        </footer>
      </div>
    </van-popup>

    <van-dialog
      v-model="showPDE"
      title="未选择DCC自适应动态悬架，预计交付周期大于16周"
    >
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div
          style="border: 1px solid;padding: 15px 0;width: 50%"
          @click="handlePDE(0)"
        >
        取消加装
        </div>
        <div class="okB"
          @click="handlePDE(1)"
        >
          确认加装
        </div>
      </div>
    </van-dialog>

  </div>
</template>

<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import { Popup } from 'vant'
import url from '@/config/url'
import {
  checkV2
} from '@/utils'
import WordDetail from './word-detail.vue'

import {
  defPatchesFn02, patchesChangeVR6Sport, patchesChangeSport, patchesChangeFabric, patchesPS138, handelWA3 
} from '@/view/configration/fix_configration/ccFn.js'

Vue.use(Popup)
export default {
  components: { WordDetail },
  props: {
    option: {
      type: Object,
      required: true
    },
    currentIndex: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      showPDE: false,
      visible: false,
      popupIntro: '',
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      show: false,
      popupDesc: '',
      show1: false,
      popupDesc1: 'haha',
      extraProcessObj: {
        checkOption: {},
        extraProcessList: []
      }
    }
  },
  computed: {
    ...mapState([
      'idx',
      'clickOption',
      'selectCarInfo',
      'currentOptionsList',
      'packetOptionsInfoList',
      'allPrivateOrderList',
      'privateOrderList',
      'currentSibColorInterieur'
    ])
  },
  methods: {
    handlePDE(e = '') {
      this.showPDE = false
      if (e) return
      this.option.cancel = true
      this.unCheckOptionFun(this.option)
    },
    // selectedHub: function (hub) {
    //   this.$store.commit('setCurrentModelHub', hub)
    // }
    iknow() {
      this.show = false
      if (this.popupDesc === '选装超级运动座椅套装 需选装黑色车内顶棚') {
        this.checkOptionFun(this.option)
      } else if (this.popupDesc === '若取消黑色车内顶棚选装，之前选择的超级运动座椅套装会同时取消') {
        this.unCheckOptionFun(this.option)
        const superChair = this.allPrivateOrderList.find((res) => res.optionCode === 'PS6')
        if (superChair) {
          this.unCheckOptionFun(superChair)
        }
      } else if ('G4IBC3001,G4ICC3001,G4IBC3004,G4ICC3007'.includes(this.selectCarInfo.modelLineCode) && 'WE8,4A4,8I6+PV3+4A4,4D3+PV3+4A4,8I6+WE8,4D3+WE8'.includes(this.option.optionCode)) {
        this.$store.commit('setCurrentOptionsList', [
          ...this.currentOptionsList,
          this.option
        ])
        // 这六个只能六选一
        const sour = ['WE8', '4A4', '8I6+PV3+4A4', '4D3+PV3+4A4', '8I6+WE8', '4D3+WE8']
        const flagindex1 = this.currentOptionsList.findIndex((e) => sour.includes(e.optionCode) && this.option.optionCode !== e.optionCode)
        if (flagindex1 !== -1) {
          this.$store.commit('setDelOption', flagindex1) //六选一
          // this.unCheckOptionFun(this.option) // 取消选中
        }
      } else {
        this.checkOptionFun(this.extraProcessObj.checkOption)
        for (const item of this.extraProcessObj.extraProcessList) {
          this.unCheckOptionFun(item)
        }
      }
    },
    iknow1() {
      this.show1 = false
    },
    async goOptionDetail(option) {
      if (option.optionName.indexOf('数字钥匙') !== -1) {
        return
      }
      this.$store.commit('setClickOption', option)
      if (option.optionType === 'packet') {
        // await this.$store.dispatch('getPacketItemInfo')
      }
      this.$emit('setTop')

      let  {
        selectCarInfo,
        currentExColor,
        currentModelHub,
        currentInteriorChair,
        currentInteriorEih,
        currentSibColorInterieur,
        currentV2popupItem,
        currentOptionsList,
        currentMeasureConfigCode
      } = this.$store.state
      let p = {
        selectCarInfo,
        currentOptionsList,
      }
      if (this.$storage.getPlus('semi-definite') == '私人高定') {
        this.$storage.setPlus("cc_data", p)
      }

      this.$router.push({
        path: '/optiondetail',
        query: {
          ...this.$route.query,
        }
      })
    },
    handleOk() {
      this.visible = false
    },
    async checkOption(option) {
      if (!checkV2()) {

        if (this.idx == 2 && option.conflict) {
          return
        }

        /**
         * 7.星耀锦衣和星耀机甲，添加规则（需前端处理）
         * 
         * 星耀机甲/星耀锦衣-->面料是n4x（灰翎-冰痕棕、橙翎-冰痕灰、红翎-冰痕黑），点击私人定制后，列表里一定会有4a4，--> 选中PV3时, 若没有选中4a4--> 提示 请先更换面料
         * 
         *  @conditionCar 当前选中车型 星耀锦衣 "G4IBC3001" || 星耀机甲 "G4ICC3001"
         *  @conditionN4X 当前选中内饰+面料
         *  记忆守护套装：PV3 
         */
        let conditionN4X = this.currentOptionsList.find(e => e.sibOptionCode === "N4X")
        let conditionCar = ["G4IBC3001", "G4ICC3001", "G4IBC3004", "G4ICC3007"].includes(this.selectCarInfo.modelLineCode)
        let has4A4 =  this.privateOrderList.find(e => e.optionCode === "4A4")
        if (conditionCar && !conditionN4X && option.optionCode === "PV3" && has4A4) {
            this.visible = true
            this.popupIntro = '当前所选配置若要单独选择记忆守护套装，请先修改您选择的面料'
            this.checkOptionFun(option, { isCancel: true })  /** @isCancel true 取消显示原有弹窗 */
            this.unCheckOption(option) // 取消选中
            return
        }

        /**
         *  6件互斥装备中，选择某个座舱，例：选择“舒享通风座舱”，然后再去选择PV3记忆守护套装，
         此时会提示互斥并移除“舒享通风座舱”，并判断4A4没有选中并提示用户修改面料
        */
        let condition4a4 = this.currentOptionsList.find(e => e.optionCode === "4A4")
        if (option.optionCode === "PV3" && conditionCar && !condition4a4 && this.currentSibColorInterieur.sibOptionCode === "N4X") {
            this.visible = true
            this.popupIntro = '当前所选配置若要单独选择记忆守护套装，请先修改您选择的面料'
            this.checkOptionFun(option, { isCancel: true })  /** @isCancel true 取消显示原有弹窗 */
            this.unCheckOption(option) // 取消选中
            
            return
        }

        if (option.optionName.indexOf('数字钥匙') !== -1) {
          return
        }
        if ('G4IBC3001,G4ICC3001'.includes(this.selectCarInfo.modelLineCode) && 'PV3'.includes(option.optionCode)) {
          const tmp = this.currentOptionsList.find((res) => res.optionCode === 'WE8')
          if (tmp) {
            this.popupDesc1 = `当前所选面料${this.currentSibColorInterieur.interieurName}，绑定${tmp.optionName}${tmp.category === 'PACKET' ? '' : '功能'}，若要加装${option.optionName}，请先修改您选择的面料`
            this.show1 = true
            return
          }
        }
        if ('G4IBC3001,G4ICC3001'.includes(this.selectCarInfo.modelLineCode) && 'WE8'.includes(option.optionCode)) {
          const tmpindex = this.currentOptionsList.findIndex((res) => res.optionCode === 'PV3')
          if (tmpindex !== -1) {
            this.$store.commit('setDelOption', tmpindex)
          }
        }
       
     
        // 选中选装包时，若选装包中包含的某个选装件被单独选中
        if (option.category === 'PACKET' && option.packetItems && option.packetItems.length > 0) {
          const temp = []
          for (const item of option.packetItems) {
            const a = this.currentOptionsList.find((res) => res.optionCode === item.optionCode)
            a && temp.push(a)
          }
          if (temp.length > 0) {
            const tempOptionNames = temp.map((res) => res.optionName).join(',')
            this.extraProcessObj.checkOption = option
            this.extraProcessObj.extraProcessList = temp

            let chairArr = ['WE8', '4A4', '8I6+PV3+4A4', '4D3+PV3+4A4', '8I6+WE8', '4D3+WE8'] //座椅六选
            let carArr = ['G4IBC3001', 'G4ICC3001'] //星耀锦衣和星耀机甲
            let condition = carArr.includes(this.selectCarInfo.modelLineCode)
            if (condition && option.optionCode === "4A4") {
              defPatchesFn02(this)
              return 
            } 
            let condition1 = chairArr.includes(option.optionCode)
            if (condition && condition1) {
              this.checkOptionFun(option)
              return 
            } 

            this.popupDesc = `此套装中包含${tempOptionNames}；将自动为您取消${tempOptionNames}`
            this.show = true
            return
          }
        } else {
          // 选中选装件时，若选装件已被选中的某个选装包包含
          let temp
          let packet
          for (const item of this.currentOptionsList) {
            temp = item.packetItems && item.packetItems.find((res) => res.optionCode === option.optionCode)
            if (temp) {
              packet = item
              break
            }
          }
          if (temp) {
            this.extraProcessObj.checkOption = option
            this.extraProcessObj.extraProcessList = [packet]

            if (temp.optionCode === "4A4") {
              this.checkOptionFun(option)
              return 
            }

            this.popupDesc = `您已选择的${packet.optionName}中包含此装备，将为您取消${packet.optionName}的选择`
            this.show = true
            return
          }
        }

        if ('498BZY001,498B2Y001'.includes(this.selectCarInfo.modelLineCode) && option.optionCode === 'PS6') {
          this.popupDesc = '选装超级运动座椅套装 需选装黑色车内顶棚'
          this.show = true
          return
        }
      }
// debugger
      this.checkOptionFun(option)
    },
    /**
     * @name checkOptionFun 
     * @myDefObj 自定义传参 */
    async checkOptionFun(option, myDefObj = {}) {
      this.$store.commit('setClickOption', option)
      if (this.idx == 2) {
        this.$store.commit('setOtherClickOption', option)
      } 
      if (!checkV2()) {
        const findOption = this.currentOptionsList.find(
          (item) => item.optionId === option.optionId
        )
        if (option.optionType === "packet-equity" || option.optionType === 'packet' || option.optionType === 'personal') {
          // 查询是否有依赖项，当前选中的选装包中是否有互斥项，有则弹窗
          let flag = option.optionRelates
              && option.optionRelates.find((val) => val.relateType === 'depend')
          // 当前选中的选装包中是否有互斥项，
          this.currentOptionsList.forEach((item) => {
            const temp = option.optionRelates
                && option.optionRelates.find(
                  (val) => val.optionRelateId === item.optionId
                )
            if (temp) {
              flag = temp
            }
          })
          // 若存在依赖，或当前选装存在互斥，则弹窗

          // 选择：前排座椅按摩（8I6）
          // 提示用户并隐藏：
          // 前排座椅通风（4D3）
          // modelLineName:"Audi Q5 e-tron 50 quattro Roadjet 荣耀型 锦衣套装"

          if ("G4IBF3003".includes(this.selectCarInfo.modelLineCode) && option.optionCode == "8I6") {
            this.$store.commit('setShowOptionPopup', true)
            return

            // const arr = []
            // this.allPrivateOrderList.forEach(f => {
            //   f.hideBool = f.optionCode === "4D3" 
            //   arr.push(f)
            // })
            // this.$store.commit('setPrivateOrderList', arr)
          }
          

          // G4ICF3007 
          // 选择：前排座椅通风（4D3）
          // 提示用户并隐藏：
          // 前排座椅按摩（8I6）
          // 取消后放出 8I6
          if ("G4ICF3007".includes(this.selectCarInfo.modelLineCode) && option.optionCode == "4D3") {
            this.$store.commit('setShowOptionPopup', true)
            return
          }

          if (option.optionRelates && option.optionRelates.length > 0 && flag && !myDefObj.isCancel) {
            this.$store.commit('setShowOptionPopup', true)
          } else {
            /* @optionCode 前排座椅按摩 8I6 */
            /* @optionCode 前排座椅通风 4D3 */
            // 当选中这两个座椅时暂不更新价格先弹窗，再弹窗中点击确定后更新价格
            // if (option.optionCode === '8I6' || option.optionCode === '4D3') {
            if ('G4IBF3001,G4ICF3001'.includes(this.selectCarInfo.modelLineCode) && "8I6,4D3".includes(option.optionCode) && !checkV2()) {
               this.$store.commit('setShowOptionPopup', true)
              // debugger
              // return
            }else {
              // checkBox状态选中时更新价格
              this.$store.commit('setCurrentOptionsList', [
                ...this.currentOptionsList,
                option
              ])
            }
          }
        } else {
          if (!findOption) {
            this.$store.commit('setCurrentOptionsList', [
              ...this.currentOptionsList,
              option
            ])
          }
        }
      } else {
        this.$store.commit('setCurrentOptionsList', [
          ...this.currentOptionsList,
          option
        ])
      }
    },
    async unCheckOption(option) {
      if (!checkV2()) {
      const chairArr = ['WE8', '4A4', '8I6+PV3+4A4', '4D3+PV3+4A4', '8I6+WE8', '4D3+WE8'] // 座椅六选一
      const carArr = ['G4IBC3001', 'G4ICC3001'] // 星耀锦衣和星耀机甲
      // 取消六选一时让它提示
      let conditionCar = carArr.includes(this.selectCarInfo.modelLineCode)
      let conditionChair = chairArr.includes(option.optionCode)
      if (conditionCar && conditionChair) {
          this.visible = true
          this.popupIntro = "当前所选配置若要取消选中，请先修改您选择的面料"
          return
      }
       
        if ((this.idx == 2 || this.idx == 1) && this.currentSibColorInterieur?.sibInterieurRelates && this.currentSibColorInterieur?.sibInterieurRelates.length > 0) {
          let dependObj =  this.currentSibColorInterieur.sibInterieurRelates.find(e => e.optionRelateCode == option.optionCode)
          if (dependObj && dependObj?.relateType == "depend") {
            this.visible = true
            this.popupIntro = "当前所选配置若要取消选中，请先修改您选择的面料"
            return
          }
        }
         
        if ('G4IBC3001,G4ICC3001'.includes(this.selectCarInfo.modelLineCode) && '4A4,WE8'.includes(option.optionCode)) {
          this.popupDesc1 = `当前所选面料${this.currentSibColorInterieur.interieurName}，为${option.optionName}${option.category === 'PACKET' ? '' : '功能'}绑定面料，若要取消，请先修改您选择的面料`
          this.show1 = true
          return
        }
        this.$store.commit('setClickOption', option)

        if (option.status === 1) {
          return
        }
        this.$store.commit('setClickOption', option)
        if ('498BZY001,498B2Y001'.includes(this.selectCarInfo.modelLineCode) && option.optionCode === '6NQ' && this.currentOptionsList.find((res) => res.optionCode === 'PS6')) {
          this.popupDesc = '若取消黑色车内顶棚选装，之前选择的超级运动座椅套装会同时取消'
          this.show = true
          return
        }
        // 荣耀锦衣和荣耀机甲取消选中8I6+4D0时显示4D3+7P1
        if ('G4IBF3001,G4ICF3001'.includes(this.selectCarInfo.modelLineCode) && (option.optionCode === '8I6+4D0' || option.optionCode === '4D3+7P1')) {
          this.$store.commit('setPrivateOrderList', this.allPrivateOrderList)
        }

        let boo = 'G4IBF3001,G4ICF3001'.includes(this.selectCarInfo.modelLineCode)
        if (boo) {
          this.$store.commit('setPrivateOrderList', this.allPrivateOrderList)
          this.$store.commit('setCurrentOptionsList', [
            ...this.currentOptionsList,
          ])
          // this.unCheckOptionFun(this.option)
          // debugger
        }
      }

      let PDE = option.optionCode == "PDE" //optionName "DCC自适应动态悬架"
      if(this.idx == 2 && PDE && this.selectCarInfo.modelLineName.includes("观云型")) {
        this.showPDE = true
        return
      }
      
      this.unCheckOptionFun(this.option)
    },
    async unCheckOptionFun(option) {
      let arr = []
      if (this.currentOptionsList.find(e => e.optionCode == option.optionCode)) {
        this.currentOptionsList.forEach(e => {
            if (e.optionCode != option.optionCode) {
              arr.push(e)
            }
        })
        this.$store.commit('setCurrentOptionsList', arr)
      }
      
      // G4IBF3003
      // 选择：前排座椅按摩（8I6）
      // 提示用户并隐藏：
      // 前排座椅通风（4D3）
      // 取消后放出 4D3
      if ("G4IBF3003".includes(this.selectCarInfo.modelLineCode) && option.optionCode == "8I6") {
        const arr = []
        this.allPrivateOrderList.forEach(f => {
          if(f.optionCode === "4D3") f.hideBool = false
          arr.push(f)
        })
        this.$store.commit('setPrivateOrderList', arr)
      }

      // G4ICF3007
      // 选择：前排座椅通风（4D3）
      // 提示用户并隐藏：
      // 前排座椅按摩（8I6）
      // 取消后放出 8I6
      if ("G4ICF3007".includes(this.selectCarInfo.modelLineCode) && option.optionCode == "4D3") {
        const arr = []
        this.allPrivateOrderList.forEach(f => {
          if(f.optionCode === "8I6") f.hideBool = false
          arr.push(f)
        })
        this.$store.commit('setPrivateOrderList', arr)
      }
    },
    toRoute() {
      this.$sensors.track('viewCompatibleModels', {
        page_name: `装备详情-${this.clickOption.optionName}`
      })
      this.$router.push({
        path: '/configration/modelCompatible'
      })
    }
  }
}
</script>

<style lang="less" scoped>
  @import url("../assets/style/dialog.less");

  /deep/ .van-dialog__footer {
    display: none;
  }
 

  .dialog-title {
    text-align: left;
    margin-bottom: 20px;
  }

  /deep/.van-dialog {
    overflow-y: auto;
    max-height: 80%;
    padding: 15px 16px 16px;
    top: 52% !important;
    z-index: 33336;

    h3 {
      margin: 0;
    }

    .item {
      color: #000;
      font-size: 14px;
      text-align: left;
      margin-bottom: 24px;

      .title {
        line-height: 24px;
      }

      .itemCotent {
        display: flex;
        line-height: 17px;

        div {
          margin-top: 8px;
        }
      }
    }
  }

.okB {
  background: black;
  color: white;
  padding: 15px 0;
  width: 50%;
  margin-left: 2px;
}

.option-card {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);

  .option-card-main {
    width: 100%;
    min-height: 190px;

    .option-card-imgcard {
      height: 130px;
      width: 100%;

      img {
        height: 130px;
        min-width: 100%;
        object-fit: cover;
      }
    }

    .option-card-info {
      padding: 8px 11px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .option-card-info-left {
        // max-width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        > span {
          font-size: 12px;
          font-weight: 400;
          color: #333333;
          line-height: 20px;
          margin-top: 7px;
        }

        ._tips {
          color: #999999;
          font-size: 12px;
          display: flex;
          align-items: center;
          margin-top: 10px;

          p {
            color: #1a1a1a;
            font-weight: bold;
            margin: 0 3px;
          }
        }
      }

      .option-card-info-right {
        > img {
          width: 16px;
          height: 16px;
        }
        .uncheck{
          opacity: 0.7;
        }
      }
    }
  }
}
.ex-popup {
  width: calc(100vw - 32px);
  // height: 200px;
  background-color: #fff;
  padding: 32px;
  box-sizing: border-box;
  position: relative;
  main {
    font-size: 16px;
    color: #333333;
    line-height: 25px;
    text-align: center;
  }
  footer {
    width: 100%;
    height: 56px;
    background: #000000;
    border: 1px solid #000000;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    // position: absolute;
    margin-top: 32px;
  }
}
</style>
