<template>
  <div
    name="name"
    class="container"
  >
    <automobile-item
      :order-data.sync="orderInfo"
      :is-do-verification="1"
      page-name="balance"
    />
    <div class="_pay">
      <div class="_pay-title">
        请选择支付方式
      </div>
      <van-collapse v-model="activeNames">
        <van-collapse-item
          title="线下支付"
          v-if="orderInfo.buyType === '01'"
          name="1"
        >
          <div class="_pay-qrcode">
            <div class="_tips">
              向商家出示二维码，进行信息保存
            </div>
            <div
              id="qrcode"
              ref="qrcode"
              style="vertical-align: top;"
            />
            <!-- <van-image width="116" lazy-load src="http://images.669pic.com/element_pic/60/61/0/12/c249acc7126ab07da8beb60546ba50d4.jpg" /> -->
          </div>
        </van-collapse-item>
        <van-collapse-item
          title="转账支付"
          name="2"
        >
          <div class="_pay-transfer">
            <div class="_item-box">
              <!-- <van-image
                width="20"
                :src="require('../../assets/img/icon_one.png')"
              /> -->
              <div class="">
                复制卡号
              </div>
              <div
                class="_width-xs"
                data-block
                data-flex="main:right"
              >
                <span class="number">1001 2155 2930 3611 134</span>
                <van-image
                  width="24"
                  @click="onCopy"
                  :src="require('../../assets/img/icon-copy.png')"
                />
              </div>
            </div>
            <div
              class="_item-box"
              @click="goUploader()"
            >
              <!-- <van-image
                width="20"
                :src="require('../../assets/img/icon_two.png')"
              /> -->
              <div class="">
                上传凭证
              </div>
              <div class="_width-xs" />
              <van-image
                width="24"
                :src="require('../../assets/img/icon-arrow.png')"
              />
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <model
      :modalshow.sync="modalShow"
      :column="false"
      confirm-text="重新上传"
      @update:modalshow="toUploader"
      title="审核失败原因"
      :content="contentText"
    />
    <van-popup
      v-model="deliveryDialog"
      class="popup-custom"
      :close-on-click-overlay="false"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="text align-center">
            {{ couponErrorsMessage || errorsMessage || '网络请求错误' }}
          </div>
        </div>
        <div
          class="popup-custom-btn"
        >
          <audi-button
            text="我知道了"
            color="black"
            height="56px"
            @click="handleClosedDeliveryDialog"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Image as VanImage, Collapse, CollapseItem, Lazyload, Toast
} from 'vant'
import QRCode from 'qrcodejs2'
import { delay, InsertUrlShearPlate } from '@/utils'

import {
  getMyOrders,
  getCarPaymentInfo,
  getBalanceQrcode,
  refreshBalanceQrcode,
  getOrderBalancePaymentCertificate
} from '@/api/api.js'
import api from '@/config/url'
import model from '@/components/model.vue'
import AudiButton from '@/components/audi-button.vue'
import AutomobileItem from './components/automobile-item.vue'

const baseOssHost = api.BaseOssHost

Vue.use(Collapse)
Vue.use(CollapseItem)
Vue.use(VanImage)
Vue.use(Lazyload)
Vue.use(Toast)

export default {
  components: {
    model,
    'automobile-item': AutomobileItem,
    'audi-button': AudiButton
  },
  data() {
    return {
      detail: null, // 订单详情
      paymentMethod: '10', // 贷款20 全款10
      loanPropor: 0, // 贷款比例
      modelNameCn: '', // 车名
      buyType: null, // 个人
      activeNames: ['1'], // 弹出框状态
      allPayPrice: 0, // 已支付
      totalPrice: 0, // 总价
      balancePrice: 0, // 尾款
      qrIntance: null, // 二维码实例
      modalShow: false,
      contentText: '',
      orderInfo: {
        paySn: '',
        buyType: ''
      },
      errorsMessage: '',
      deliveryDialog: false,
      couponErrorsMessage: ''
    }
  },
  watch: {
    orderInfo: {
      handler({ buyType, paySn }) {
        if (buyType === '01') {
          paySn ? this.refreshQrCode(paySn) : this.creatQrCode()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.$EventBus.$on('HANDLE_COLLECTION_ERROR', this.handleCollectionError)
    // this.getOrderInfo()
  },
  methods: {
    handleClosedDeliveryDialog() {
      this.deliveryDialog = false
      this.$router.go(-1)
    },
    handleCollectionError(error) {
      if (error) {
        this.couponErrorsMessage = error
        if (!this.deliveryDialog) this.deliveryDialog = true
      }
      console.log('%c [ handleCollectionError ]-192', 'font-size:14px; background:#cf222e; color:#fff;', error)
    },
    onCopy() {
      const status = InsertUrlShearPlate('1001 2155 2930 3611 134')
      if (status) {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '复制成功',
          forbidClick: true,
          duration: 800
        })
      } else {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '复制失败',
          forbidClick: true,
          duration: 800
        })
      }
    },

    async getOrderInfo() {
      // 订单信息
      const { orderId, ccid } = this.$route.query
      const { data } = await getMyOrders({ orderId })
      this.detail = data.data
      this.paymentMethod = data.data?.extInfo?.paymentMethod

      this.buyType = data.data?.carBuyerInfo.buyType

      // 尾款信息
      const payData = await getCarPaymentInfo({ orderId })
      const payInfo = payData.data.data
      console.log(payData)
      this.modelNameCn = payInfo.modelNameCn
      this.totalPrice = payInfo.configTotalPrice.toString()
      this.allPayPrice = payInfo.confirmPayAmount / 100
      if (this.buyType === '01') { // 个人
        this.balancePrice = this.totalPrice - this.allPayPrice
      } else { // 企业
        this.balancePrice = this.totalPrice
      }
      if (this.paymentMethod === '20' && payInfo.loanInfo) { // 贷款
        // if(this.buyType === '01'){
        this.balancePrice = (payInfo?.loanInfo?.downPayments - this.allPayPrice).toFixed(2)
        // } else {
        //   this.balancePrice  = payInfo?.loanInfo?.downPayments.toFixed(2)
        // }
        this.loanPropor = ((payInfo?.loanInfo?.downPayments / payInfo?.configTotalPrice) * 100).toFixed(2)
        console.log(this.loanPropor)
      }
      const payPhase = data.data.payList.find((i) => i.payPhase === '20')

      if (this.buyType === '01') { // 个人才需要获取二维码
        if (payPhase?.payPhase === '20') { // 已经获取过一次二维码 需刷新
          this.refreshQrCode(payPhase.paySn)
        } else {
          this.creatQrCode()
        }
      }
    },

    // 刷新二维码
    async refreshQrCode(paysn) {
      const params = { paysn }
      const { data } = await refreshBalanceQrcode(params)
      if (data.data) {
        this.getQrCode(data.data)
      }
    },

    // 生成二维码
    async creatQrCode() {
      const { orderId } = this.$route.query
      const params = { orderId, payType: 'POS' }
      const {
        data: {
          code, message, data: resData
        }
      } = await getBalanceQrcode(params)
      console.log('%c [ resData ]-222', 'font-size:14px; background:#cf222e; color:#fff;', resData)
      if (!['00', '200', 200].includes(code)) {
        this.errorsMessage = message.indexOf('卡券') !== -1 ? '该卡券不适用该订单，请您联系奥迪专属管家' : message
        this.deliveryDialog = true
        return
      }
      if (resData.forwardUrl) {
        this.getQrCode(resData.forwardUrl)
      }
    },

    getQrCode(text) {
      this.$nextTick(() => {
        this.qrIntance = new QRCode(this.$refs.qrcode, {
          text: text,
          width: 116,
          height: 116,
          colorDark: '#333333', // 二维码颜色
          colorLight: '#ffffff', // 二维码背景色
          correctLevel: QRCode.CorrectLevel.L // 容错率，L/M/H
        })
      })
    },

    async goUploader() {
      // 获取尾款审核信息
      const { orderId } = this.$route.query
      const { data } = await getOrderBalancePaymentCertificate({ orderId })
      if (data?.data?.balancePaymentStatus === 3) {
        this.modalShow = true
        this.contentText = data.data.auditRemark
      } else {
        this.$router.push({
          path: '/order/balance-uploader',
          query: {
            orderId
          }
        })
      }
    },
    toUploader(val) {
      if (val) {
        const { orderId } = this.$route.query
        this.$router.push({
          path: '/order/balance-uploader',
          query: {
            orderId
          }
        })
      }
      this.modalShow = false
    }
  }
}
</script>

<style lang='less' scoped>
  ._order {
    border-top: 1px solid #E5E5E5;
    width: 100%;
    padding: 8px 16px;
    box-sizing: border-box;
    font-family: "Audi-Normal";

    &-car {
      width: 100%;
      display: flex;
      padding: 8px 0;

      ._img {
        width: 88px;
      }

      ._name {
        font-size: 14px;
        font-family: 'Audi-WideBold';
        font-weight: normal;
        color: #000000;
        line-height: 24px;
        margin: 0 16px;
        max-width: 125px;
      }

      ._price {
        font-size: 14px;
        color: #000000;
        line-height: 22px;
        min-width: 100px;
        display: flex;
        justify-content: flex-end;
      }
    }

    &-price {
      width: 100%;

      ._deposit {
        display: flex;
        justify-content: flex-end;
        margin: 4px 0;
        font-size: 14px;
        color: #666666;
        line-height: 22px;
      }

      ._balance {
        display: flex;
        justify-content: flex-end;
        font-size: 14px;
        font-family: 'Audi-WideBold';
        font-weight: normal;
        color: #000000;
        line-height: 22px;
      }
    }
  }

  ._pay {
    padding: 10px 0 11px;
    border-top: 8px solid #F2F2F2;

    &-title {
      padding: 12px 16px;
      font-family: 'Audi-WideBold';
      color: #333;
      line-height: 24px;
    }

    &-qrcode {
      display: flex;
      flex-flow: column;
      align-items: flex-end;
      width: calc(100% - 40px);
      padding: 16px 24px 0 16px;

      ._tips {
        margin-bottom: 16px;
        margin-top: 8px;
        font-size: 14px;
        color: #999999;
        line-height: 24px;
      }
    }

    &-transfer {
      width: 100%;
      padding: 10px 0;

      ._item-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        color: #333;
        line-height: 24px;
        font-family: 'Audi-WideBold';

        ._width-xs {
          width: 220px;
          text-align: right;
        }
      }
    }
    /deep/ .van-collapse-item__content {
      padding: 0;
    }
    /deep/ .van-collapse-item__wrapper {
      height: auto;
    }
    ._pay-transfer {
      padding: 0;

      ._item-box {
        padding: 24px 16px;
        font-size: 16px;
        line-height: 24px;
        font-family: "Audi-ExtendedBold";
        border-bottom: solid .5px #F2F2F2;
        ._width-xs {
          font-family: "Audi-Normal";
          font-size: 14px;
          .number {
            margin-right: 4px;
            color: #000;
          }
        }
        &:first-child {
          padding-top: 16px;
        }
      }
    }
  }

  .margin-top-40 {
    margin-top: 40px;
  }
  ._item-box {

  }

  ::v-deep .van-cell {
    line-height: 24px;
    padding: 15px 28px 15px 24px;
    border-top: 1px solid #E5E5E5;
    border-bottom: 1px solid #E5E5E5;
  }

  ::v-deep .van-cell__title {
    line-height: 24px;
    font-size: 16px;
    color: #000;
    font-family: "Audi-Normal";
  }

  // ::v-deep .van-cell__right-icon {
  //   line-height: 32px !important;
  // }

  ::v-deep .van-cell__right-icon {
    color: #000;
  }

  ::v-deep .van-cell::after {
    border: none;
  }

  ::v-deep .van-collapse-item {
    margin-bottom: 16px;
  }
  ::v-deep .van-collapse-item--border::after {
    border: none;
  }

  ::v-deep .van-hairline--top-bottom::after,
  .van-hairline-unset--top-bottom::after {
    border: none !important;
  }
.container {
  /deep/.tips-banner {
    margin-bottom: -24px;
  }
}
</style>
