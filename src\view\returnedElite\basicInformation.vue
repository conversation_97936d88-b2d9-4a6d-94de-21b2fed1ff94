<template>
  <div class="_container">
    <navigation
      title=""
      :back-type="backType"
    />
    <div style="flex:1">
      <van-field
        v-model="formData.userFullName"
        label="姓名"
        placeholder="请输入您的姓名"
        @input="onInput0"
        :error-message="message0"
        :readonly="readonly"
      />
      <van-field
        v-model="formData.idCard"
        label="身份证号"
        @input="onInput1"

        :error-message="message1"
        placeholder="请输入您的身份证号"
        :readonly="readonly"
      />
      <!-- <van-field
        v-model="formData.employerName"
        label="工作单位"
        @input="onInput2"

        :error-message="message2"
        placeholder="请输入您的工作单位"
        :readonly="readonly"
      /> -->
      <van-field
        v-model="formData.phoneNumber"
        label="手机号"
        maxlength="11"
        type="number"
        @input="onInput3"
        :readonly="readonly"

        :error-message="message3"

        placeholder="请输入您的手机号"
      />
      <van-field
        v-model="formData.handlingMethod"
        is-link
        label="办理方式"
        :readonly="readonly"

        :error-message="message7"

        placeholder="请选择办理方式"
        @click="readonly?typeShow = false:typeShow = true"
      />
      <van-popup v-model="typeShow" position="bottom"  :style="{ height: '50%' }">
        <van-picker
          show-toolbar
          title="办理方式"
          :columns="columns"
          value-key='handlingMethod'
          @confirm="onConfirm"
          @cancel="typeShow = false"
        />
      </van-popup>
      <div class="immediate-list">
        <p>是否直系亲属购车</p>
        <!-- <van-switch
        v-model="formData.additionalBuyers"
        active-color="#0da20d"
        inactive-color="#E5E5E5"
        size="24px"
        :disabled="readonly"
      /> -->
        <img
          v-if="!formData.additionalBuyers"
          style="width: 58px;
        height: 32px;"
          src="../../assets/img/Switch1.png"
          @click="isShowSwitch"
        >
        <img
          v-else
          style="width: 58px;
        height: 32px;"
          src="../../assets/img/Switch2.png"
          @click="isShowSwitch"
        >
      </div>
      <div v-if="formData.additionalBuyers">
        <van-field
          v-model="formData.additionalBuyersName"
          label="姓名"
          :error-message="message4"
          @input="onInput4"
          :readonly="readonly"

          placeholder="请输入直系亲属姓名"
        />
        <van-field
          v-model="formData.additionalBuyersIdCard"
          label="身份证号"
          placeholder="请输入直系亲属身份证号"
          :error-message="message5"
          @input="onInput5"
          :readonly="readonly"
        />
        <van-field
          v-model="formData.additionalBuyersPhoneNumber"
          label="手机号"
          maxlength="11"
          type="number"
          :error-message="message6"
          @input="onInput6"
          :readonly="readonly"

          placeholder="请输入直系亲属手机号"
        />
      </div>
      <!-- <div class="btn-delete-height" /> -->
    </div>
    <div class="bottom_style">
      <p>
        上汽奥迪归国精英购车计划咨询电话
      </p>
      <p>
        021-695-56182
      </p>
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onSubmit"
          :text="'下一步'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
    <div v-if="isSupplierEmployees">
      <div class="wrapper">
        <div class="block">
          <p class="block-text">
            您填写的工作单位信息有误，单位不属于上汽大众的供应商企业，请确认后再尝试继续操作
          </p>
        </div>
      </div>
    </div>
    <van-overlay :show="isShowMessageId">
      <div class="wrapper-ids">
        <div class="block-ids">
          您已重新提交过资料
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Button,
  ActionSheet,
  Cell,
  Popup,
  Area,
  Toast,
  Checkbox,
  CheckboxGroup,
  Overlay,
  Switch,
  Picker
} from 'vant'
import AudiButton from '@/components/audi-button'
import navigation from '../../components/navigation'
import {
  queryReturnedEliteInformation,
  getSupplierCheckSupplierEmployees,
  queryReturnedEliteHandlingMethods,
  queryReturnedEliteInformationById
} from '@/api/api'

Vue.use(Form)
  .use(Field)
  .use(Button)
  .use(ActionSheet)
  .use(Cell)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Area)
  .use(Switch)
  .use(Overlay)
  .use(Toast)
  .use(Picker)
export default {
  components: {
    AudiButton,
    navigation
  },
  data() {
    return {
      formData: {
        userFullName: '',
        idCard: '',
        employerName: '',
        phoneNumber: '',
        additionalBuyers: false,
        additionalBuyersName: '',
        additionalBuyersPhoneNumber: '',
        additionalBuyersIdCard: '',
        handlingMethod:''
      },
      backType: 'app',
      message0: '',
      message1: '',
      message2: '',
      message3: '',
      message4: '',
      message5: '',
      message6: '',
      message7: '',
      readonly: false,
      isSupplierEmployees: false,
      cascaderValue:'',
      typeShow:false,
      columns: [],
      isShowMessageId: false
    }
  },

  watch: {
    'formData.additionalBuyers': {
      handler(val) {
        console.log(val)
        if (!val) {
          this.formData.additionalBuyersName = ''
          this.formData.additionalBuyersPhoneNumber = ''
          this.formData.additionalBuyersIdCard = ''
        }
      }
    }
  },

  mounted() {
    const ismessageId = this.$route.query.messageId
    const params = {
      messageId: ismessageId
    }
    if (ismessageId) {
      queryReturnedEliteInformationById(params).then((res) => {
        if (res.data.data && res.data.data.informationStatus === 2) {
          this.isShowMessageId = false
          this.queryReturnedEliteInformation()
    this.queryReturnedEliteHandlingMethods()
        } else {
          this.isShowMessageId = true
        }
      })
    } else {
      this.queryReturnedEliteInformation()
    this.queryReturnedEliteHandlingMethods()
    }
    

  },
  methods: {
     onConfirm(item, index) {
      // Toast(`当前值：${value}, 当前索引：${index}`);
      console.log(1111111111111111)
      this.formData.handlingMethod = item.handlingMethod
      this.typeShow = false
    },
    onCancel() {
      // Toast('取消');
      // this.typeShow = false
    },
    isShowSwitch() {
      // if (!this.readonly) {
        this.formData.additionalBuyers = !this.formData.additionalBuyers
      // }
    },
    onSubmit() {
      if (!this.formData.additionalBuyers) {
        if (this.testName(this.formData.userFullName)
         && this.testIdcard(this.formData.idCard)
          // && this.testOrder(this.formData.employerName)
          && this.testMobile(this.formData.phoneNumber)
          && this.testfieldValue(this.formData.handlingMethod)) {
          // const params = {
          //   nameZh: this.formData.employerName
          // }
          // getSupplierCheckSupplierEmployees(params).then((res) => {
          //   if (res.data.data) {
             
          //   }
          //   this.isSupplierEmployees = true
          //   setTimeout(() => {
          //     this.isSupplierEmployees = false
          //   }, 3000)
          // })
           localStorage.setItem('returnedEliteElite', JSON.stringify(this.formData))
              this.$router.push({
                name: 'returnedElite-data-upload'
              })
        }
      } else {
        if (this.testName(this.formData.userFullName)
         && this.testIdcard(this.formData.idCard)
          // && this.testOrder(this.formData.employerName)
          &&this.testfieldValue(this.formData.handlingMethod)
           && this.testMobile(this.formData.phoneNumber)
           && this.testNameRelatives(this.formData.additionalBuyersName)
           && this.testIdcardRelatives(this.formData.additionalBuyersIdCard)
           && this.testMobileRelatives(this.formData.additionalBuyersPhoneNumber)) {
             localStorage.setItem('returnedEliteElite', JSON.stringify(this.formData))
              this.$router.push({
                name: 'returnedElite-data-upload'
              })
          // const params = {
          //   nameZh: this.formData.employerName
          // }
          // getSupplierCheckSupplierEmployees(params).then((res) => {
          //   if (res.data.data) {
             
          //   }
          //   this.isSupplierEmployees = true
          //   setTimeout(() => {
          //     this.isSupplierEmployees = false
          //   }, 3000)
          // })
        }
      }
    },

    CheckSupplierEmployees() {
      this.isSupplierEmployees = false
      // this.formData.employerName = ''
    },

    queryReturnedEliteHandlingMethods(){
      queryReturnedEliteHandlingMethods().then(res=>{
        // console.log(res.data.data,'ssssssss')
        this.columns=res.data.data
      })
    },

    queryReturnedEliteInformation() {
      queryReturnedEliteInformation().then((res) => {
        const dataFrom = JSON.parse(localStorage.getItem('returnedEliteElite'))
          if (dataFrom) {
            this.formData = {
              ...dataFrom
            }
            if (dataFrom.additionalBuyers == 1) {
              this.formData.additionalBuyers = true
            } else {
              this.formData.additionalBuyers = false
            }
          }else {
          if (res.data.data) {
            this.readonly = false
            this.formData = {
              ...res.data.data
            }
            if (res.data.data.additionalBuyers == 1) {
              this.formData.additionalBuyers = true
            } else {
              this.formData.additionalBuyers = false
            }
          } 
        }
      })
    },

    onInput0(val) {
      this.testName(val)
    },
    onInput1(val) {
      this.testIdcard(val)
    },
    onInput2(val) {
      this.testOrder(val)
    },
    onInput3(val) {
      this.testMobile(val)
    },
    onInput4(val) {
      this.testNameRelatives(val)
    },
    onInput5(val) {
      this.testIdcardRelatives(val)
    },
    onInput6(val) {
      this.testMobileRelatives(val)
    },
    testName(val) {
      if (val.length > 0) {
        this.message0 = ''
        return true
      }
      this.message0 = '请输入姓名'
      return false
    },

    testOrder(val) {
      if (val.length > 0) {
        this.message2 = ''
        return true
      }
      this.message2 = '请输入工作单位'
      return false
    },
    testMobile(val) {
      const re = /^1\d{10}$/
      if (re.test(val)) {
        this.message3 = ''
        return true
      }
      this.message3 = '手机号格式错误'
      return false
    },
    testfieldValue(val){
     if (val) {
        this.message7 = ''
        return true
      }
      this.message7 = '请选择办理当时'
      return false
    },
    testNameRelatives(val) {
      if (val.length > 0) {
        this.message4 = ''
        return true
      }
      this.message4 = '请输入直系亲属姓名'
      return false
    },
    testIdcard(val) {
      const regs = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/
      if (val.length <= 0 || !regs.test(val)) {
        this.message1 = '身份证号错误'
        return false
      }
      this.message1 = ''
      return true
    },
    testIdcardRelatives(val) {
      const regs = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/
      if (val.length <= 0 || !regs.test(val)) {
        this.message5 = '身份证号错误'
        return false
      }
      this.message5 = ''
      return true
    },
    testMobileRelatives(val) {
      const re = /^1\d{10}$/
      if (re.test(val)) {
        this.message6 = ''
        return true
      }
      this.message6 = '手机号格式错误'
      return false
    }

  }
}
</script>

<style lang='less' scoped>
::v-deep .van-cell {
  padding: 16px;
}

::v-deep .van-cell::after {
  border-bottom: 1px #e5e5e5 solid;
}

::v-deep .van-button {
  border-radius: 0;
}

::v-deep .van-field__label {
  font-size: 16px;
  color: #000;
  width: 70px;
}
.immediate-list{
    display:flex;
    align-items: center;
    justify-content:space-between;
    padding:0 16px
}

::v-deep .van-field {
  .van-field__body {
    textarea {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }

    input {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }
  }
}

._container{
height: 100vh;
    display: flex;
    flex-direction: column;
}
.btn-delete-height {
  height: 80px;
}
.btn-delete-wrapper {
  margin: 16px;
}
.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  p{
      margin:0;
      text-align: center;
      font-size: 12px;
  // font-family: 'Audi-Normal';
  line-height:20px;
  color:#000000
  }
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
}

.block {
  width: 73%;
  text-align: center;
  background-color: #000000;
  display:flex;
  flex-wrap: wrap;
  justify-content: center;

}

.block-text{
margin-bottom:24px;
margin-top: 24px;
padding:0 16px;
line-height: 22px;
color: #FFFFFF;
font-size: 14px;
}
.block-btn{
  width: 90%;
    color: #fff;
    background-color: #000;
    line-height: 56px;
    margin: 0;
    height: 56px;
}
 .van-picker {
    /deep/.van-picker__title {
      font-weight: 600;
    }
    /deep/.van-picker__confirm {
      color: #000;
    }
    /deep/.van-picker__cancel {
      color: #fff;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        background: url(../../assets/img/icon05.png) no-repeat 50% / contain;
      }
    }
    /deep/.van-picker-column__item--selected {
      font-weight: 600;
    }
  }
  .wrapper-ids {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
  }

  .block-ids {
    width: 90%;
    height: 120px;
    line-height:120px;
    text-align: center;
    background-color: #fff;
  }
</style>
