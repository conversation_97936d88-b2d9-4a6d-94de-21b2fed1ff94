<template>
  <div style="position: absolute;left: -500px;">
    <div
      class="_card"
      id="heardImg"
    >
      <p  :class="[ carSeries.seriesCode === 'G6' ? 'overB' : '']" >{{ title }}</p>
      <img :style="{'width': carSeries.seriesCode === 'G4' ? '80%' : '90%'}" :class="[ carSeries.seriesCode === 'G6' ? 'top' : '']" :src="imgurl +'?t=' + time">
    </div>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import { uploadImg } from '../api/upload-documents'
import { callNative } from '@/utils/index'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      time: Date.now()
    }
  },
  props: {
    imgurl: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: 'A7L edition one 配置单'
    }
  },
  computed: {
    ...mapState({
      carSeries: (state) => state.carSeries,
    })
  },
  mounted() {
    console.log(this.imgurl)
  },
  methods: {
    getCanvasImg() {
      callNative('toggleLoading', { show: '1' })
      this.time = Date.now()
      html2canvas(document.getElementById('heardImg'), {
        logging: true,
        useCORS: true,
        allowTaint: true
        // dpi: 500
      }).then((canvas) => {
        const dataURL = canvas.toDataURL('image/png')
        const base64ToBlob = function (dataURL) {
          const arr = dataURL.split(',')
          const fileType = arr[0].match(/:(.*?);/)[1]
          const bstr = atob(arr[1])
          let l = bstr.length
          const u8Arr = new Uint8Array(l)

          while (l--) {
            u8Arr[l] = bstr.charCodeAt(l)
          }
          return new Blob([u8Arr], {
            type: fileType
          })
        }
        const blobToFile = function (newBlob, fileName) {
          const file = new File([newBlob], fileName, { type: 'image/png', lastModified: Date.now() })

          return file
        }
        // 调用
        const blob = base64ToBlob(dataURL)
        const timestamp = Date.parse(new Date())
        const file = blobToFile(blob, `${timestamp}.png`)

        const formData = new FormData()
        formData.append('file', file)
        formData.append('fileType', 1)
        formData.append('categoryType', 1)
        formData.append('fileCategoryId', 1)
        this.postOssImage(formData)
      })
    },
    async postOssImage(formData) {
      uploadImg(formData).then((res) => {
        callNative('toggleLoading', { show: '0' })
        if (res.data.code === '00') {
          this.$emit('getCanvasImg', res.data.data[0])
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
  ._card {
    width: 100vw;
    box-sizing: border-box;
    height: 220px;
    display: flex;
    flex-flow: column;
    align-items: center;
    font-family: 'WideBold';
    font-size: 20px;
    padding: 0px;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
  
    p {
      font-size: 17px;
      font-family: 'Audi-WideBold';
    }
  
    img {
      position: absolute;
      left: 5%;
      bottom: 0;
      // transform:scale(0.8);
    }
    .top {
      top: 30px;
    }
  }

  .overB {
    // white-space: nowrap;
    // width: 90vw;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // height: 33px;
    // line-height: 33px;
  }
</style>
