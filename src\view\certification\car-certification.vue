<template>
  <div class="idcertification">
    <div v-if="isPhotograph" @click="onUploadOcrIdCard(2)">
      <img class="upload-item-img" src="../../assets/img/icon-identity4.png" />
      <div class="upload-item-title-bold">添加发票</div>
    </div>

    <div
      v-if="boolUsedCar === 1 && !carMemberRelId && isPhotograph"
      class="usedcar-upload"
      @click="usedCarShow = true"
    >
      <h3>其他方式</h3>
      <p>若没有发票，点击上传行驶证</p>
    </div>

    <div
      v-if="boolUsedCar === 0 && !carMemberRelId && isPhotograph"
      class="usedcar-driving-upload"
      @click="usedCarShow = true"
    >
      <span>其他方式认证</span>
      <van-icon name="arrow" />
    </div>

    <!-- 上传成功页面-->
    <div v-if="isFileOK" style="padding-bottom: 146px">
      <img class="item-img" :src="base64" />
      <div class="line_content">
        <div class="s_line" />
        <div class="text_center">请确认识别结果（可修改）</div>
        <div class="e_line" />
      </div>

      <div class="line-border-connent">
        <div class="line-border-connent-title">身份信息</div>
        <div class="line-border-bottom">
          <div class="name">购买方</div>
          <div class="str" v-if="!isChange">
            {{ ocrInvoiceVehiclelicenseModel.buyer }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleInvoiceBuyer }}
          </div>
        </div>
        <div
          class="line-border-bottom"
          style="padding-bottom: 4px; padding-top: 8px"
        >
          <div class="name" v-html="`证件号/<br>机构代码`" />
          <div class="str" v-if="!isChange">
            {{ ocrInvoiceVehiclelicenseModel.buyerId }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleInvoiceBuyerId }}
          </div>
        </div>
        <p class="is-text-wang" v-if="isWrong || isWrong1">
          <img src="../../assets/wall/icon11.png" />

          {{ isWrong ? '证件号位数有误，请修改' : '您的证件号有误，请修改' }}
        </p>
        <div class="line-border-bottom">
          <div class="name">VIN</div>
          <div class="str" v-if="!isChange">
            {{ ocrInvoiceVehiclelicenseModel.vin }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleInvoiceCarVin }}
          </div>
        </div>
        <p class="is-text-wang" v-if="isWrong2">
          <img src="../../assets/wall/icon11.png" />
          VIN码位数有误，请修改
        </p>
        <div class="line-border-bottom">
          <div class="name">开票日期</div>
          <div class="str" v-if="!isChange">
            {{ ocrInvoiceVehiclelicenseModel.billingDate }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleInvoiceIssueDate }}
          </div>
        </div>
        <p class="is-text-wang" v-if="isWrong3">
          <img src="../../assets/wall/icon11.png" />
          开票日期格式应为yyyy-mm-dd，请修改
        </p>
      </div>
      <div class="bottom_styles">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="onReturnUpdate"
            :text="'修改信息'"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="onConfirm"
            :text="'确认提交'"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </div>

    <input
      class="hide_file"
      ref="leftFile"
      id="upload"
      type="file"
      @change="getFile($event)"
      capture="camera"
      accept="image/camera"
    />
    <!--拍照提示-->
    <van-action-sheet
      v-model="show"
      :safe-area-inset-bottom="true"
      :round="false"
    >
      <div style="padding-right: 16px; padding-left: 16px">
        <div
          class="item-title-bold"
          style="
            text-align: center;
            justify-content: center;
            font-size: 16px;
            margin-top: 18px;
          "
        >
          证件上传示例
        </div>
        <div
          style="
            text-align: center;
            justify-content: center;
            font-size: 14px;
            margin-top: 16px;
            color: #666;
            padding-right: 26px;
            padding-left: 26px;
          "
        >
          请于光线充足的环境下，纯色背景下，四角对齐，横向拍照
        </div>
        <img class="item-img" src="../../assets/img/icon-identity3.png" />
      </div>
      <van-grid :border="false" :column-num="4" :gutter="16">
        <van-grid-item>
          <img src="../../assets/img/icon-identity5.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">缺失</div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity6.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">模糊</div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity7.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">过滤</div>
        </van-grid-item>

        <van-grid-item text="">
          <img src="../../assets/img/icon-identity8.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">
            背景模糊
          </div>
        </van-grid-item>
      </van-grid>
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onUploadingImg"
          :text="'知道了'"
          color="black"
          font-size="15px"
          height="48px"
        />
      </div>
    </van-action-sheet>

    <!--确认提交提示-->
    <van-popup
      v-model="showConfirmsubmitAction"
      :style="{ width: '100%' }"
      position="bottom"
      :close-on-click-overlay="false"
    >
      <div class="item-title-bold-revise">修改信息</div>

      <div>
        <div class="line-border-bottom-updata">
          <div class="name">购买方</div>
          <van-field
            class="str"
            type="text"
            v-model="updateVehicleInvoiceBuyer"
            rows="1"
          />
        </div>

        <div class="line-border-bottom-updata">
          <div class="name" v-html="`证件号/<br>机构代码`" />
          <van-field
            class="str"
            type="text"
            v-model="updateVehicleInvoiceBuyerId"
          />
        </div>
        <p class="is-text-wangs" v-if="isWrong || isWrong1">
          <img src="../../assets/wall/icon11.png" />

          {{ isWrong ? '证件号位数有误，请修改' : '您的证件号有误，请修改' }}
        </p>
        <div class="line-border-bottom-updata">
          <div class="name">VIN</div>
          <van-field
            class="str"
            type="text"
            v-model="updateVehicleInvoiceCarVin"
            @input="formValidatorNames"
          />
        </div>
        <p class="is-text-wangs" v-if="isWrong2">
          <img src="../../assets/wall/icon11.png" />
          VIN码位数有误，请修改
        </p>
        <div class="line-border-bottom-updata">
          <div class="name">开票日期</div>
          <van-field
            class="str"
            type="text"
            v-model="updateVehicleInvoiceIssueDate"
            @input="formValidatorDates"
            rows="1"
          />
        </div>
        <p class="is-text-wangs" v-if="isWrong3">
          <img src="../../assets/wall/icon11.png" />
          开票日期格式应为yyyy-mm-dd，请修改
        </p>
      </div>

      <div class="bottom_style">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="afterConfirmation"
            text="取消修改"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="beforeConfirmation"
            text="确认修改"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </van-popup>

    <van-popup
      v-if="modalshow"
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal">
        <div class="modal-title">
          {{
            showBindingTypeAndcarOwn === 1 ? '请选择车辆类别' : '请选择车辆关系'
          }}
        </div>

        <div>
          <div
            class="service-line"
            style="margin-top: 8px"
            v-if="showBindingTypeAndcarOwn === 1"
          >
            <div class="ch-change" @click.stop="onCarOwnType(1)">
              <img
                class="ch-icon"
                :src="carOwnType === 1 ? activeIcon : inactiveIcon"
              />
            </div>
            <div class="title-bold">私家车</div>
          </div>

          <div
            class="service-line"
            style="margin-top: 8px"
            v-if="showBindingTypeAndcarOwn === 1"
          >
            <div class="ch-change" @click.stop="onCarOwnType(0)">
              <img
                class="ch-icon"
                :src="carOwnType === 0 ? activeIcon : inactiveIcon"
              />
            </div>
            <div class="title-bold">公务车</div>
          </div>

          <div
            class="service-line"
            style="margin-top: 8px"
            v-if="showBindingTypeAndcarOwn === 0"
          >
            <div class="ch-change" @click.stop="onBindingType(1)">
              <img
                class="ch-icon"
                :src="bindingType === 1 ? activeIcon : inactiveIcon"
              />
            </div>
            <div class="title-bold">我是车主</div>
          </div>
          <div
            class="service-line"
            style="margin-top: 8px"
            v-if="showBindingTypeAndcarOwn === 0"
          >
            <div class="ch-change" @click.stop="onBindingType(2)">
              <img
                class="ch-icon"
                :src="bindingType === 2 ? activeIcon : inactiveIcon"
              />
            </div>
            <div class="title-bold">我是用车人</div>
          </div>
        </div>
        <div
          class="modal-confirm center"
          @click.stop="onConfirmBindingTypeAndcarOwn"
        >
          确定
        </div>
      </div>
    </van-popup>
    <van-popup v-model="modalshowInvoice" :close-on-click-overlay="false">
      <div class="_modal invoice-modal">
        <div class="modal-title">请选择车辆类型</div>

        <div class="modal-content">
          <div class="service-line" style="margin-top: 8px">
            <div class="ch-change" @click.stop="onCarOwnInvoiceType(0)">
              <img
                class="ch-icon"
                :src="boolUsedCar === 0 ? activeRadioIcon : inactiveRadioIcon"
              />
            </div>
            <div class="title-bold">新车</div>
          </div>

          <div class="service-line" style="margin-top: 8px">
            <div class="ch-change" @click.stop="onCarOwnInvoiceType(1)">
              <img
                class="ch-icon"
                :src="boolUsedCar === 1 ? activeRadioIcon : inactiveRadioIcon"
              />
            </div>
            <div class="title-bold">二手车</div>
          </div>
        </div>
        <div
          class="modal-confirm center"
          @click.stop="onConfirmBindingTypeInvoice"
        >
          确定
        </div>
      </div>
    </van-popup>

    <!--确认提交提示-->
    <van-popup
      v-model="beforeConfirmationsubmitAction"
      :style="{ width: '90%' }"
    >
      <div
        style="
          background: #f2f2f2;
          height: 126px;
          padding-left: 26px;
          padding-right: 26px;
          margin-bottom: 12px;
        "
      >
        <div class="item-title-bold-point-out">重要提示</div>
        <div class="item-title-bold-point-out-text">
          确认提交后无法进行修改，如信息有误，将影响你的认证，无法获得相关权益
        </div>
      </div>

      <div>
        <div class="line-border-bottom-affirm">
          <div class="name">购买方</div>
          <div class="str" v-if="!isChange">
            {{ ocrInvoiceVehiclelicenseModel.buyer }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleInvoiceBuyer }}
          </div>
        </div>
        <div class="line-border-bottom-affirm">
          <div class="name" v-html="`证件号/<br>机构代码`" />
          <div class="str" v-if="!isChange">
            {{ ocrInvoiceVehiclelicenseModel.buyerId }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleInvoiceBuyerId }}
          </div>
        </div>
        <div class="line-border-bottom-affirm">
          <div class="name">VIN</div>
          <div class="str" v-if="!isChange">
            {{ ocrInvoiceVehiclelicenseModel.vin }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleInvoiceCarVin }}
          </div>
        </div>
        <div
          class="line-border-bottom-affirm line-border-bottom-affirm-billingDate"
        >
          <div class="name">开票日期</div>
          <div class="str" v-if="!isChange">
            {{ ocrInvoiceVehiclelicenseModel.billingDate }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleInvoiceIssueDate }}
          </div>
        </div>
      </div>

      <div class="bottom_style on_return_update">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="cancel"
            text="取消"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="onConfirmsubmit"
            text="确认"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </van-popup>
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols lan-dialog-content-f12"
      v-model="dialogShow"
      cancel-button-text="取消"
      confirm-button-text="确定"
      :title="`上汽奥迪申请获取相机权限`"
      show-cancel-button
      :message="`上汽大众需要申请相机权限，以便通过扫一扫、拍摄照片或视频为您提供上传头像、车辆图片、绑车/会员认证、专属桩绑定、发动态、提问题、写文章、评论、扫码充电、形象定制、AI助手相关服务。拒绝或取消授权不影响使用其他服务。`"
      @confirm="dialogConfirm"
      @cancel="dialogCancel"
    />

    <van-dialog
      class="dialog lan-dialog-custom line-two-cols lan-dialog-content-f12"
      v-model="usedCarShow"
      cancel-button-text="上传行驶证"
      confirm-button-text="上传发票"
      show-cancel-button
      message="推荐使用发票认证车辆，仅使用行驶证认证可能会影响权益的发放"
      @confirm="usedCarShow = false"
      @cancel="usedCarCancel"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Image as VanImage,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover,
  ActionSheet,
  Grid,
  GridItem
} from 'vant'
import HtmlImageCompress from 'html-image-compress'
import { callNative, compressCertificationImg } from '@/utils'

import {
  postOcrVehiclelicense,
  postOcrInvoiceVehiclelicense,
  postVehicleInvoiceRecognition,
  getManCarMemberInfo,
  getFindCustomerCarList,
  postusedCarVehicleInvoice,
  postCheckVehicleInvoice
} from '@/api/api'
import AudiButton from '@/components/audi-button'

import { noticeScBindCar } from '@/api/certification'

import checkSocialCreditCode from '@/utils/enterprise.code'
import validateIdCard from '@/utils/idcard'
import storage from '../../utils/storage'
import dayjs from 'dayjs'

Vue.use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)
  .use(ActionSheet)
  .use(Grid)
  .use(GridItem)
  .use(VanImage)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      show: false, // 上传拍照提示
      showConfirmsubmitAction: false,
      uploadType: 2, // 上传识别类型 1=行驶证，2= 发票
      isPhotograph: true, // 拍照页面
      isFileOK: false, // 上传成功页面
      isChange: false, // 修改页面

      ocrVehiclelicenseModel: {}, // 行驶证识别结果

      updateVehicleLicenseMainOwner: '', //	修改后-所有人【必填】		false
      updateVehicleLicenseMainPlateNum: '', //	修改后-车牌号码【必填】		false
      updateVehicleLicenseMainRegisterDate: '', //	修改后-注册日期【必填】		false
      updateVehicleLicenseMainVin: '', //	修改后-车辆识别代号【必填】		false

      ocrInvoiceVehiclelicenseModel: {}, // 发票识别结果
      updateVehicleInvoiceBuyer: '', //	修改后-购买方名称【必填】		false
      updateVehicleInvoiceBuyerId: '', //	修改后-购买方名称的证件号号或组织机构代码【必填】		false
      updateVehicleInvoiceCarVin: '', //	修改后-车辆识别代号/车架号【必填】		false
      updateVehicleInvoiceIssueDate: '', //	修改后-开票日期【必填】

      carMemberInfoModel: {}, // 会员信息

      base64: '',
      bindingType: 1, // 绑定类别，1:车主 2:用车人【必填】
      carOwnType: 1, // 车辆拥有类别，0：非私家车，1：私家车【必填】
      showBindingTypeAndcarOwn: 1, // 0显示绑定类别，1显示车辆拥有类别
      modalshow: false,
      activeIcon: require('@/assets/img/checkbox_checked.png'),
      inactiveIcon: require('@/assets/img/checkbox_normal.png'),
      activeRadioIcon: require('@/assets/img/icon-radio-cur1.png'),
      inactiveRadioIcon: require('@/assets/img/icon-radio1.png'),
      customerCarList: [],
      modalshowInvoice: false, // 发票类型弹框
      boolUsedCar: 0, // 发票类型，0：新增发票，1：二手车发票
      onUploadOcrIdCardType: '',
      beforeConfirmationsubmitAction: false,
      isWrong: false,
      isWrong1: false,
      isWrong2: false,
      isWrong3: false,
      dialogShow: false,
      usedCarShow: false,
      carMemberRelId: ''
    }
  },
  created() {
    if (this.$route.query.carMemberRelId) {
      // 补传的驾驶证不需要证件
      this.carMemberRelId = this.$route.query.carMemberRelId
    } else {
      // this.modalshowInvoice = true
    }
    this.modalshowInvoice = true

    // 调试
    // this.isPhotograph = false
    // this.isFileOK = true
    // this.isChange = false
    // this.ocrInvoiceVehiclelicenseModel = {
    //   "type": "机动车销售统一发票",
    //   "buyer": "黄帅",
    //   "vehicleInvoiceBuyerTaxId": "",
    //   "buyerId": "*****************",
    //   "carModel": "寸捷1984CC插电式越野车",
    //   "carMadePlace": "斯洛伐克",
    //   "certId": "",
    //   "engineId": "DFK007990",
    //   "vin": "LSVDE6F29R2036526",
    //   "totalPrice": "玖拾捌万圆整",
    //   "totalPriceDigits": "980000.00",
    //   "pageNumber": "第一联发票联",
    //   "priceWithoutTax": "867256.64",
    //   "taxRate": "13%",
    //   "taxAmount": "112743.36",
    //   "telephone": "0592-7166911",
    //   "billingDate": "-2019-10-15",
    //   "daima": "135",
    //   "haoma": "06126",
    //   "dealer": "厦门宾捷汽车有限公司",
    //   "jidaDaima": "************",
    //   "vehicle_invoice_jida_haoma": "********",
    //   "machineId": "************",
    //   "taxAuthorithId": "*********",
    //   "taxAuthorId": "*********",
    //   "taxAuthorith": "国家税务总局厦门市翔安区税务局马巷税务所",
    //   "taxAuthorName": "国家税务总局厦门市翔安区税务局马巷税务所",
    //   "sellerBankName": "中国银行建发大厦支行",
    //   "sellerBankAccount": "************",
    //   "sellerTaxId": "9135020055621707XF",
    //   "commodityInspectionId": "*********",
    //   "importCertificateId": "H22190164489",
    //   "sellerAddress": "厦门市翔安区民安大道2815号",
    //   "vehicleType": "",
    //   "tonnage": "",
    //   "taxCode": "034>65*/2061764->523*></<7/3-65/9->>-731*>>85*80/800<<-43>73/7*409/6239354+*-4*264<437*37693-4*264<437*376+52+>*3/>3-7*79*4/3504*327410<228+>690/9/+7181/+4928/78->*<6/->00<****************70",
    //   "drawer": "王金川"
    // }
    // this.updateVehicleInvoiceBuyer =
    //   this.ocrInvoiceVehiclelicenseModel.buyer //	修改后-购买方名称【必填】		false
    // this.updateVehicleInvoiceBuyerId =
    //   this.ocrInvoiceVehiclelicenseModel.buyerId //	修改后-购买方名称的证件号号或组织机构代码【必填】		false
    // this.updateVehicleInvoiceCarVin = this.ocrInvoiceVehiclelicenseModel.vin //	修改后-车辆识别代号/车架号【必填】		false
    // this.updateVehicleInvoiceIssueDate =
    //   this.ocrInvoiceVehiclelicenseModel.billingDate //	修改后-开票日期【必填】
  },
  mounted() {
    this.getFindCustomerCarList()
  },
  methods: {
    async getFindCustomerCarList() {
      // 查询车辆是否绑定
      const { data } = await getFindCustomerCarList({
        bindingStatus: 4,
        bindingType: 0
      })
      if (data.data.length > 0) {
        this.customerCarList = data.data
      }
    },

    async getFile(e) {
      const file = e.target.files[0]

      const reader = new FileReader()
      if (file) {
        // 通过文件流将文件转换成Base64字符串
        reader.readAsDataURL(file)
        // 转换成功后
        const this1 = this
        reader.onloadend = function () {
          // 输出结果
          this1.formData(file, reader.result)
        }
      }
    },
    async formData(file, base64) {
      if (file) {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('fileType', 1)
        formData.append('categoryType', 1)
        formData.append('fileCategoryId', 1)

        this.postOcrInvoiceVehiclelicense(formData)
        // 通过文件流将文件转换成Base64字符串

        const params = {
          content: base64,
          file: file
        }
        const fileTemp = await compressCertificationImg(params)
        const readers = new FileReader()
        readers.readAsDataURL(fileTemp)
        // 转换成功后
        const this2 = this
        readers.onloadend = function () {
          // 输出结果
          this2.base64 = readers.result
        }
      }
    },
    // 识别行驶证
    async postOcrVehiclelicense(formData) {
      this.show = false
      this.$store.commit('showLoading')
      const { data } = await postOcrVehiclelicense(formData)
      this.$store.commit('hideLoading')

      if (data.code === '00') {
        this.isPhotograph = false
        this.isFileOK = true
        this.isChange = false
        this.ocrVehiclelicenseModel = data.data
        this.updateVehicleLicenseMainOwner = this.ocrVehiclelicenseModel.owner
        this.updateVehicleLicenseMainPlateNum =
          this.ocrVehiclelicenseModel.plateNum
        this.updateVehicleLicenseMainRegisterDate =
          this.ocrVehiclelicenseModel.registerDate
        this.updateVehicleLicenseMainVin = this.ocrVehiclelicenseModel.vin
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    // 识别发票
    async postOcrInvoiceVehiclelicense(formData) {
      this.show = false

      this.$store.commit('showLoading')
      const { data } =
        this.boolUsedCar == 0
          ? await postOcrInvoiceVehiclelicense(formData)
          : await postusedCarVehicleInvoice(formData)

      console.log('发票识别信息', data)
      this.$store.commit('hideLoading')
      if (data.code === '00') {
        this.isPhotograph = false
        this.isFileOK = true
        this.isChange = false
        this.ocrInvoiceVehiclelicenseModel = data.data
        this.updateVehicleInvoiceBuyer =
          this.ocrInvoiceVehiclelicenseModel.buyer //	修改后-购买方名称【必填】		false
        this.updateVehicleInvoiceBuyerId =
          this.ocrInvoiceVehiclelicenseModel.buyerId //	修改后-购买方名称的证件号号或组织机构代码【必填】		false
        this.updateVehicleInvoiceCarVin = this.ocrInvoiceVehiclelicenseModel.vin //	修改后-车辆识别代号/车架号【必填】		false
        this.updateVehicleInvoiceIssueDate =
          this.ocrInvoiceVehiclelicenseModel.billingDate //	修改后-开票日期【必填】
      } else {
        // callNative('toast', { type: 'fail', message: data.message })
        this.$router.push({
          path: '/certification/car-certification-error',
          query: {}
        })
      }
    },
    // 修改
    onReturnUpdate() {
      // 修改状态点击是重新上传
      // if (this.isChange) {
      //   this.isPhotograph = true
      //   this.isFileOK = false
      //   this.isChange = false
      // } else {
      //   this.isPhotograph = false
      //   this.isFileOK = false
      //   this.isChange = true
      // }

      // this.showConfirmsubmitAction = false
      // this.isChange = true
      this.showConfirmsubmitAction = true
    },

    cancel() {
      this.beforeConfirmationsubmitAction = false
      this.showConfirmsubmitAction = false
      // this.isChange=false
    },

    onUploadOcrIdCard(type) {
      // this.modalshowInvoice = true
      this.onUploadOcrIdCardType = type
      this.uploadType = type
      this.show = true
    },

    // 上传图
    async onUploadingImg() {
      const data = await callNative('albumCameraEvent', { type: 1 })
      console.log(data, '是否开启相册权限')
      if (data.status) {
        this.$refs.leftFile.click()
      } else {
        this.dialogShow = true
      }
    },
    dialogCancel() {},
    dialogConfirm() {
      callNative('openpage', {})
    },
    formValidatorNames(name) {
      if (name.length !== 17) {
        this.isWrong2 = true
      } else {
        this.isWrong2 = false
      }
    },

    isValidDateFormat(dateStr) {
      // 正则表达式严格匹配 YYYY-MM-DD 格式
      const regex = /^\d{4}-\d{2}-\d{2}$/;
      if (!regex.test(dateStr)) return false;

      // 使用 dayjs 验证日期是否有效
      return dayjs(dateStr, 'YYYY-MM-DD', true).isValid();
    },
    formValidatorDates(dataTime) {
      if (!this.isValidDateFormat(dataTime)) {
        this.isWrong3 = true
      } else {
        this.isWrong3 = false
      }
    },

    // 提交
    onConfirm() {
      if (this.updateVehicleInvoiceCarVin.length !== 17) {
        callNative('toast', { type: 'fail', message: 'VIN码位数有误，请修改' })
        this.isWrong2 = true
        return
      }
      if (!this.isValidDateFormat(this.updateVehicleInvoiceIssueDate)) {
        callNative('toast', { type: 'fail', message: '开票日期格式应为yyyy-mm-dd，请修改' })
        this.isWrong3 = true
        return
      }
      this.isWrong2 = false
      this.isWrong3 = false

      this.beforeConfirmationsubmitAction = true
    },
    // 选择车辆类别
    onCarOwnType(type) {
      this.carOwnType = type
    },
    // 选择用车人
    onBindingType(type) {
      this.bindingType = type
    },

    onConfirmBindingTypeAndcarOwn() {
      // 提交
      this.modalshow = false
      this.showBindingTypeAndcarOwn = 1

      this.postVehicleInvoiceRecognition()

      // }
    },
    beforeConfirmation() {
      if (this.updateVehicleInvoiceCarVin.length !== 17) {
        callNative('toast', { type: 'fail', message: 'VIN码位数有误，请修改' })
        return
      }
      if (!this.isValidDateFormat(this.updateVehicleInvoiceIssueDate)) {
        callNative('toast', { type: 'fail', message: '开票日期格式应为yyyy-mm-dd，请修改' })
        this.isWrong3 = true
        return
      }
      this.showConfirmsubmitAction = false
      // this.beforeConfirmationsubmitAction = true
      this.isChange = true
    },

    afterConfirmation() {
      this.updateVehicleInvoiceBuyer = this.ocrInvoiceVehiclelicenseModel.buyer //	修改后-购买方名称【必填】		false
      this.updateVehicleInvoiceBuyerId =
        this.ocrInvoiceVehiclelicenseModel.buyerId //	修改后-购买方名称的证件号号或组织机构代码【必填】		false
      this.updateVehicleInvoiceCarVin = this.ocrInvoiceVehiclelicenseModel.vin //	修改后-车辆识别代号/车架号【必填】		false
      this.updateVehicleInvoiceIssueDate =
        this.ocrInvoiceVehiclelicenseModel.billingDate //	修改后-开票日期【必填】
      this.beforeConfirmationsubmitAction = false
      this.showConfirmsubmitAction = false
      this.isChange = false
      this.isWrong = false
      this.isWrong1 = false
      this.isWrong2 = false
      this.isWrong3 = false
    },
    // 确认提交
    async onConfirmsubmit() {
      const { data } = await getManCarMemberInfo({})
      // 会员信息
      this.carMemberInfoModel = data.data

      // 发票
      if (
        this.carMemberInfoModel.name === this.updateVehicleInvoiceBuyer &&
        this.carMemberInfoModel.gid === this.updateVehicleInvoiceBuyerId
      ) {
        // this.postVehicleInvoiceRecognition()
        const params = {
          ownerId: this.updateVehicleInvoiceBuyerId,
          ownerName: this.updateVehicleInvoiceBuyer,
          vin: this.updateVehicleInvoiceCarVin,
          boolUsedCar: this.boolUsedCar
        }
        postCheckVehicleInvoice(params).then((res) => {
          if (res.data.code === '01') {
            // 不一致需要弹窗选择
            this.beforeConfirmationsubmitAction = false
            this.modalshow = true
          } else {
            if (res.data.data.bindingType && res.data.data.buyType) {
              const params = {
                ...res.data.data
              }
              this.postVehicleInvoiceRecognition(params)
            } else {
              this.beforeConfirmationsubmitAction = false
              this.modalshow = true
            }
          }
        })
      } else {
        if (this.updateVehicleInvoiceBuyer === '') {
          callNative('toast', { type: 'fail', message: '请填写购买方' })
          return
        }
        if (!this.updateVehicleInvoiceBuyerId) {
          callNative('toast', {
            type: 'fail',
            message: '请填写您的身份证号/机构代码'
          })
          return
        }
        if (this.updateVehicleInvoiceCarVin === '') {
          callNative('toast', { type: 'fail', message: '请填写VIN码' })
          return
        }
        if (this.updateVehicleInvoiceIssueDate === '') {
          callNative('toast', { type: 'fail', message: '请填写开票日期' })
          return
        }
        if (this.updateVehicleInvoiceCarVin.length !== 17) {
          callNative('toast', {
            type: 'fail',
            message: 'VIN码位数有误，请修改'
          })
          return
        }
        const params = {
          ownerId: this.updateVehicleInvoiceBuyerId,
          ownerName: this.updateVehicleInvoiceBuyer,
          vin: this.updateVehicleInvoiceCarVin,
          boolUsedCar: this.boolUsedCar
        }
        postCheckVehicleInvoice(params).then((res) => {
          if (res.data.code === '01') {
            // 不一致需要弹窗选择
            this.beforeConfirmationsubmitAction = false
            this.modalshow = true
          } else {
            if (res.data.data.bindingType && res.data.data.buyType) {
              const params = {
                ...res.data.data
              }
              this.postVehicleInvoiceRecognition(params)
            } else {
              this.beforeConfirmationsubmitAction = false
              this.modalshow = true
            }
          }
        })
      }
    },
    // 上传发票认证
    async postVehicleInvoiceRecognition(val) {
      const param = {
        authType: !this.carMemberRelId ? 1 : 0,
        // bindingType: val?val.bindingType:this.carOwnType == 0 ? 2 : 1,
        bindingType: val
          ? val.bindingType
          : this.carMemberInfoModel.name === this.updateVehicleInvoiceBuyer
          ? 1
          : 2,
        carOwnType: val ? val.buyType : this.carOwnType,
        channelCode: '05',
        image: this.base64,
        boolUsedCar: this.boolUsedCar,

        vehicleInvoiceBuyer: this.ocrInvoiceVehiclelicenseModel.buyer,
        vehicleInvoiceBuyerId: this.ocrInvoiceVehiclelicenseModel.buyerId,
        vehicleInvoiceCarVin: this.ocrInvoiceVehiclelicenseModel.vin,
        vehicleInvoiceIssueDate: this.ocrInvoiceVehiclelicenseModel.billingDate,
        // vehicleInvoiceVin: this.ocrInvoiceVehiclelicenseModel.vin,

        updateVehicleInvoiceBuyer: this.isChange
          ? this.updateVehicleInvoiceBuyer
          : this.ocrInvoiceVehiclelicenseModel.buyer,
        updateVehicleInvoiceBuyerId: this.isChange
          ? this.updateVehicleInvoiceBuyerId
          : this.ocrInvoiceVehiclelicenseModel.buyerId,
        updateVehicleInvoiceCarVin: this.isChange
          ? this.updateVehicleInvoiceCarVin
          : this.ocrInvoiceVehiclelicenseModel.vin,
        updateVehicleInvoiceIssueDate: this.isChange
          ? this.updateVehicleInvoiceIssueDate
          : this.ocrInvoiceVehiclelicenseModel.billingDate,
        // 补传需要的参数
        ...(this.carMemberRelId ? { carMemberRelId: this.carMemberRelId } : {})
        // updateVehicleInvoiceVin: this.isChange
        //   ? this.updateVehicleInvoiceCarVin
        //   : this.ocrInvoiceVehiclelicenseModel.vin
      }
      console.log('上传发票信息====>', param)
      this.$store.commit('showLoading')
      const { data } = await postVehicleInvoiceRecognition(param)
      this.showConfirmsubmitAction = false
      this.$store.commit('hideLoading')
      if (data.code === '00' || data.code === '44' || data.code === '200') {
        storage.set(
          'certificationList',
          JSON.stringify({
            reviewStatus: data.data[0].reviewStatus,
            name: data.data[0].name,
            plateNumber: data.data[0].plateNumber,
            vin: data.data[0].vin,
            ocrOrderDate: data.data[0].ocrOrderDate
          })
        )
        try {
          const scInfo = await callNative('getUserInfo', {})
          console.log('scInfoUserInfo', scInfo)
          noticeScBindCar(
            this.isChange
              ? this.updateVehicleInvoiceCarVin
              : this.ocrInvoiceVehiclelicenseModel.vin,
            scInfo.userId
          )
        } catch (e) {
          console.log(e)
        }

        this.$router.push({
          path: '/certification/car-certification-in',
          query: {
            carMemberRelId: this.carMemberRelId || ''
          }
        })
      } else {
        if (data.code === '78') {
          callNative('toast', {
            type: 'fail',
            message: '证件类型有误，请重新上传正确的证件类型'
          })
        } else if (data.code === '53') {
          callNative('toast', {
            type: 'fail',
            message: '证件有误，请重新上传正确的证件'
          })
        } else if (data.code === '61') {
          callNative('toast', {
            type: 'fail',
            message: '请确定信息正确性，并填写完整'
          })
        } else if (data.code === '51') {
          callNative('toast', {
            type: 'fail',
            message: '车辆识别代码(VIN)位数有误，请修改或重新识别证件'
          })
        } else if (data.code === '05') {
          callNative('toast', {
            type: 'fail',
            message: '该证件非二手车行驶证，请更换证件重新认证'
          })
        } else if (data.code === '49') {
          callNative('toast', {
            type: 'fail',
            message: '系统正在处理，请勿重复提交'
          })
        } else if (data.code === '10') {
          callNative('toast', {
            type: 'fail',
            message: '车辆识别代码(VIN)有误，请修改或重新识别证件'
          })
        } else if (data.code === '81') {
          callNative('toast', {
            type: 'fail',
            message: '证件号码有误，请修改或重新识别证件'
          })
        } else if (data.code === '82') {
          callNative('toast', {
            type: 'fail',
            message: '证件号码有误，请修改或重新识别证件'
          })
        } else if (data.code === '85') {
          callNative('toast', {
            type: 'fail',
            message:
              '该车辆尚未交车，请联系购车人前往APP整车订单完成交车确认后再进行车辆认证'
          })
        } else if (data.code === '86') {
          callNative('toast', {
            type: 'fail',
            message:
              '该车辆尚未交车，请前往APP整车订单完成交车确认后再进行车辆认证'
          })
        } else if (data.code === '48') {
          callNative('toast', {
            type: 'fail',
            message: '车辆识别代码(VIN)有误，请修改或重新识别证件'
          })
        } else if (data.code === '63') {
          callNative('toast', {
            type: 'fail',
            message: '车辆类别有误，请确认车辆类别是私家车或公务车'
          })
        } else if (data.code === '65') {
          callNative('toast', {
            type: 'fail',
            message:
              '车主姓名有误，请保证与当前已认证的车主姓名一致，若有误请修改或重新认证'
          })
        } else if (data.code === '64') {
          callNative('toast', {
            type: 'fail',
            message:
              '认证该车辆的账户数量已满，暂时无法认证。可联系其他车辆认证账户解除绑定'
          })
        } else if (data.code === '42') {
          callNative('toast', {
            type: 'fail',
            message: '证件号或姓名有误，请修改或重新识别证件'
          })
        } else if (data.code === '52') {
          callNative('toast', {
            type: 'fail',
            message: '车辆识别码VIN有误，请与当前已认证的车辆识别码保持一致'
          })
        } else if (data.code === '20') {
          callNative('toast', {
            type: 'fail',
            message: '您已绑定过该车，无需重复绑定'
          })
        } else if (data.code === '23') {
          callNative('toast', {
            type: 'fail',
            message: '该车辆为私家车，无法认证为公务车，请重新提交'
          })
        } else if (data.code === '83') {
          callNative('toast', {
            type: 'fail',
            message: '证件号码有误，请修改或重新识别证件'
          })
        } else if (data.code === '84') {
          callNative('toast', {
            type: 'fail',
            message: '证件日期有误，请修改或重新识别证件'
          })
        } else if (data.code === '57') {
          callNative('toast', {
            type: 'fail',
            message: '车牌号有误，请修改或重新识别证件'
          })
        } else if (data.code === '72') {
          callNative('toast', {
            type: 'fail',
            message: '请确定信息正确性，并填写完整'
          })
        } else {
          callNative('toast', { type: 'fail', message: data.message })
        }
      }
    },
    // 选择发票类型
    onCarOwnInvoiceType(type) {
      this.boolUsedCar = type
    },

    onConfirmBindingTypeInvoice() {
      this.modalshowInvoice = false
    },
    usedCarCancel() {
      this.$router.push({
        path: '/certification/driving-certification',
        query: {
          carType: this.boolUsedCar
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.idcertification {
  height: 92%;
  padding: 16px;
  // padding-bottom: 80px;
  padding-top: 0;
}
.upload-item-title {
  color: #333333;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 14px;
}

.upload-item-img {
  width: 100%;
  aspect-ratio: 343/210;
}
.upload-item-title-bold {
  margin-top: -22vw;
  padding-left: 2%;
  text-align: center;
  color: #000;
  font-size: 13px;
}
.item-img {
  width: 100%;
  height: 200px;
  margin-top: 16px;
  margin-bottom: 16px;
}

.item-title-bold {
  width: 100%;
  font-size: 16px;
  color: #000;
  font-family: 'Audi-WideBold';
  margin-bottom: 16px;
}

.usedcar-upload {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  margin-top: calc(22vw + 25px);
  padding: 0 16px;
  width: 100%;
  height: 100px;
  background: linear-gradient(25deg, #eaedef, #f0f3f5);
  box-sizing: border-box;
  h3 {
    margin: 0;
    margin-bottom: 4px;
    font-weight: normal;
    font-size: 15px;
    color: #1a1a1a;
    line-height: 24px;
  }
  p {
    margin: 0;
    font-size: 13px;
    color: #666666;
    line-height: 20px;
  }
}

.line_content {
  margin-bottom: 23px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .s_line {
    width: 100%;
    height: 1px;
    margin-right: 16px;
    background: #e5e5e5;
  }

  .text_center {
    text-align: center;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #999999;
    font-size: 14px;
    line-height: 24px;
  }

  .e_line {
    width: 100%;
    height: 1px;
    margin-left: 16px;
    background: #e5e5e5;
  }
}

.line-border-connent-title {
  font-size: 16px;
  color: #000000;
  font-family: 'Audi-WideBold';
  line-height: 24px;
  margin-bottom: 16px;
}
.interval_line {
  margin-top: 30px;
  margin-left: -16px;
  margin-right: -16px;
  height: 8px;
  background: #f2f2f2;
}

.line {
  padding-top: 16px;
  padding-bottom: 16px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 16px;
    color: #000;
  }

  .item_btn {
    color: #666;
    font-size: 14px;
  }

  .btn-change {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn-icon {
      align-items: center;
      font-size: 12px;
      color: #000;
    }
  }
}

.btn-delete-wrapper {
  margin: 16px;
}

.hide_file {
  display: none;
}

.is-text-wang {
  padding: 0;
  line-height: 24px;
  border-top: 1px solid #eb0d3f;
  color: #eb0d3f;
  font-size: 12px;
  margin-top: -16px;
  > img {
    width: 24px;
    height: 24px;
    // margin-right: 4px;
  }
}

.is-text-wangs {
  padding: 0;
  line-height: 24px;
  border-top: 1px solid #eb0d3f;
  color: #eb0d3f;
  font-size: 12px;
  margin-top: -16px;
  margin-left: 16px;
  margin-right: 16px;
  > img {
    width: 24px;
    height: 24px;
    // margin-right: 4px;
  }
}

.line-border-bottom {
  // width: 100%;
  display: flex;
  align-items: center;
  // margin-top: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f2f2f2;
  margin-bottom: 16px;
  .name {
    width: 90px;
    font-size: 16px;
    color: #000;
    font-family: 'Audi-Normal';
    line-height: 24px;
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #000;
    line-height: 24px;
  }
}
.item-title-bold-revise {
  font-size: 16px;
  padding-left: 16px;
  margin-top: 24px;
  color: #000000;
  margin-bottom: 16px;
  width: 90%;
  font-family: 'Audi-WideBold';
  line-height: 24px;
}
.line-border-bottom-updata {
  // padding: 6px 0;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
  padding: 16px 0;
  margin: 0 16px;
  // padding:0;
  // padding-bottom: 16px;
  // height: 24px;
  // margin-left: 16px;
  // margin-right: 16px;
  margin-bottom: 16px;
  .name {
    width: 90px;
    font-size: 16px;
    color: #000;
    font-family: 'Audi-Normal';
    line-height: 24px;
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #000;
    line-height: 24px;
    padding: 0;
  }
}
.item-title-bold-point-out {
  text-align: center;
  justify-content: center;
  font-size: 18px;
  padding-top: 24px;
  line-height: 26px;
  margin-bottom: 8px;
  font-family: 'Audi-WideBold';
}
.item-title-bold-point-out-text {
  font-size: 14px;
  line-height: 22px;
  color: #333333;
  font-family: 'Audi-Normal';
  text-align: center;
}

.line-border-bottom-affirm {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
  padding: 16px 0;
  margin: 0 16px;
  margin-bottom: 11px;
  .name {
    width: 90px;
    font-size: 16px;
    color: #000000;
    font-family: 'Audi-Normal';
    line-height: 24px;
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    padding: 0;
  }
}
.line-border-bottom-affirm-billingDate {
  margin-bottom: 25px;
}
.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // bottom: 0px;
  padding-bottom: 38px;
  // left: 0px;
  // position: fixed;

  .div-btn {
    width: 100%;
    margin: 0 2px;
  }
}

.bottom_styles {
  width: 100%;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0px;
  padding-bottom: 38px;
  left: 0px;
  position: fixed;

  .div-btn {
    width: 100%;
    margin: 0 2px;
  }
}
.on_return_update {
  padding-bottom: 25px;
}

._modal {
  width: 343px;
  background: #ffffff;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;

  .modal-title {
    font-size: 15px;
    color: #333333;
    margin-bottom: 16px;
    font-family: 'Audi-WideBold';
  }

  .service-line {
    width: 295px;
    height: 48px;
    display: flex;
    align-items: center;

    .ch-change {
      padding-right: 6px;

      .ch-icon {
        width: 20px;
        height: 20px;
      }
    }

    .title-bold {
      font-size: 16px;
      color: #999;
      font-weight: normal;
      font-family: 'Audi-Normal';
    }
  }

  &.invoice-modal {
    .modal-content {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .service-line {
        display: inline-flex;
        justify-content: center;
        width: 50%;
      }
    }
  }

  .modal-confirm {
    margin-top: 24px;
    width: 85%;
    height: 50px;
    background: #1a1a1a;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.usedcar-driving-upload {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 48px;
  width: 100%;
  padding: 0 16px;
  text-align: center;
  box-sizing: border-box;
  color: #a1a1a1;
  font-size: 14px;
  span{
    margin-right: 4px;
  }
}
</style>
