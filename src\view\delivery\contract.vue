<template>
  <div class="container">
    <div class="img-wrapper">
      <img
        v-for="item in imgUrlArr"
        :key="item"
        :src="item"
        alt="合同加载失败"
      >
    </div>

   <div class="btn-botm"  v-if="(showSignButton || showDownloadButton || showUploadButton) && orderType != '07'">
    <div
      class="btn-wrapper"
      v-if="showSignButton"
    >
      <AudiButton
        text="去签署"
        color="black"
        height="56px"
        font-size="16px"
        @click="clickSignCaontract"
      />
    </div>
     <div
      class="btn-wrapper"
      v-if="showDownloadButton"
    >
      <AudiButton
        text="下载合同"
        color="black"
        height="56px"
        font-size="16px"
        @click="downloadContract"
      />
    </div>

    <div
      class="btn-wrapper"
      v-if="showUploadButton"
    >
      <AudiButton
        text="上传合同"
        color="black"
        height="56px"
        font-size="16px"
        @click="toUploadPage"
      />
    </div>
   </div>
  </div>
</template>

<script>

import { Toast } from 'vant'
import Vue from 'vue'
import wx from 'weixin-js-sdk'
import {
  getContractInfo, verifyFadada, loginFadada, enterpriseVerifyFadada, enterpriseLoginFadada, signContract, createOfflineContract, getOfflineContractImage, getSignedContractImage, getMyOrders, getContractPreviewImgs, getContractState, getCarOrderInfoH5
} from '@/api/api'
import AudiButton from '@/components/audi-button'
import {
  Obj2UrlParam, callNative, handleTimeout, isIos
} from '@/utils'

Vue.use(Toast)
// 大定的code
const BIZCODE = '000003'

export default {
  components: { AudiButton },
  data() {
    return {
      contractInfo: {},
      imgUrlArr: [],
      showDownloadButton: false,
      showSignButton: false,
      showUploadButton: false,
      boolReceivePurchaseInvoice: false,
      orderType: ''
    }
  },
  created() {
    const { orderId } = this.$route.query
    if (!orderId) {
      console.error(`contract-info page, orderId:${orderId}`)
    }
  },

  mounted() {
    console.log('合同mounted')
    this.toggleHeaderVisible(false)
    this.initData()
  },


  methods: {
    // 是否签署成功
    _isSignedContract(contractInfo) {
      return contractInfo?.deliveryContractStatus === '2' || contractInfo?.deliveryContractStatus === '1'
    },


    async initData() {
      /**
     * carBuyerInfo.buyType:
     * 01 个人订单: 个人订单在验证信息不通过的时候执行线下流程
     * 02 企业订单:只能执行线下签署流程:
     * 线下流程: 显示下载/上传按钮
     */

      const { orderId } = this.$route.query
      const { data } = await getMyOrders({ orderId })
      this.orderType = data.data.orderType
      this.contractInfo = data.data.contractInfo
      this.boolReceivePurchaseInvoice = data.data.extInfo.boolReceivePurchaseInvoice
      // 显示预览的图片
      this.previewImg(data.data.contractInfo)

      // 如果签署过就不执行验签的流程
      if (this._isSignedContract(data.data.contractInfo)) {
        this.$store.state.title = '交车确认书'
        document.title = '交车确认书'
        return
      }
      this.$store.state.title = '交车确认书模板'
      document.title = '交车确认书模板'


      // data.contractInfo.purchaseContractBoolOnline 为 false 走线下合同
      // if (data.data.contractInfo?.purchaseContractBoolOnline === false) {
      //   this.doOfflineContract()
      //   return
      // }

      // // 企业合同走线下签署流程
      // if (data.data.carBuyerInfo?.buyType === '02') {
      //   // TODO:
      //   // 未签署则执行线上验签流程
      //   const priseRes = await enterpriseVerifyFadada({ orderId })

      //   // 如果验签返回个人验证失败
      //   if (priseRes.data.code === '500' && priseRes.data.message === '没有找到用户') {
      //     // 先进行个人认证校验
      //     const pRes = await verifyFadada({ orderId })

      //     if (typeof pRes.data.data?.verified === 'undefined') {
      //       return console.error('contract page/ verifyFadada 验证合同错误')
      //     }

      //     // 个人验签失败去fadada 认证
      //     if (!pRes.data.data.verified) {
      //       this.loginFadada()
      //     }
      //     return
      //   }

      //   const { userVerified, enterpriseVerified } = priseRes.data

      //   // 是否验证过
      //   if (userVerified && enterpriseVerified && this.boolReceivePurchaseInvoice && data.data.orderStatus != '90') {
      //     this.showSignButton = true
      //   } else if (!userVerified) {
      //     // 个人验签未通过
      //     this.loginFadada()
      //   } else {
      //     // 企业验签未通过
      //     this.enterpriseLoginFadada()
      //   }
      //   return
      // }


      // 个人合同
      if (data.data.carBuyerInfo?.buyType === '01' || data.data.carBuyerInfo?.buyType === '02') {
        // 未签署则执行线上验签流程
        const { data } = await verifyFadada({ orderId })

        if (typeof data.data?.verified === 'undefined') {
          return console.error('contract page/ verifyFadada 验证合同错误')
        }

        // 是否验证过
        if (data.data.verified && this.boolReceivePurchaseInvoice && data.data.orderStatus != '90') {
          this.showSignButton = true
        } else {
          this.loginFadada()
        }
      }
    },

    // 对ios做特殊处理
    routeToSignContract(path) {
      // if (isIos) {
      //   try {
      //     window.webViewJavascriptBridge.callHandler('clearCache', {}, (err, data) => {
      //       setTimeout(() => {
      //         window.location.href = path
      //       }, 1000)
      //     })
      //   } catch (err) {
      //     window.location.href = path
      //   }
      // } else {
      //   window.location.href = path
      // }
      window.location.href = path
    },

    // 执行线下签署流程(下载合同)
    async doOfflineContract() {
      this.showDownloadButton = true

      // download
      if (this.contractInfo?.deliveryContractId) {
        const contractId = this.contractInfo?.deliveryContractId
        const { data } = await getContractState({
          contractId
        })

        // 0: 未上传, 1:自己上传过, 2: 运营侧上传过 3: 合同已完成
        if (data.data.status === 0 || data.data.status === 1) {
          this.showUploadButton = true
        }
      }
      const { orderId } = this.$route.query
      const status = sessionStorage.getItem(orderId)
      if (status == 1 && !this.showUploadButton) {
        this.showUploadButton = true
      }
    },

    // 获取显示预览图片
    async previewImg(contractInfo) {
      if (!contractInfo) {
        console.warn(`perview img function contractInfo: ${contractInfo}`)
      }
      /**
       * 订单是否签署,
       * 如果签署就使用contractId 获取签署后的预览合同图片展示
       * 是否已经签订合同 purchaseContractStatus: 0:未签署, 1: 用户(线下)已签署 2:线上合同已签署, 10: 客户已签署
       * 目前只判断 1 或 2
       */
      if (this._isSignedContract(contractInfo)) {
        const contractId = contractInfo?.deliveryContractId
        // 第一次进入 contractId 为null
        if (contractId) {
          // 这里数量写死, 据说个人或企业合同数量都为 4
          const count = 1
          const idxArr = Array.from({ length: count }, (v, k) => k)
          const paramArr = idxArr.map((i, idx) => Obj2UrlParam({ contractId, pageNo: idx + 1 }))

          if (contractInfo?.purchaseContractBoolOnline) {
            // 线上合同预览
            this.imgUrlArr = paramArr.map((i) => getSignedContractImage(`?${i}`))
          } else {
            // 线下合同预览
            this.imgUrlArr = paramArr.map((i) => getOfflineContractImage(`?${i}`))
          }
        }
      } else {
        // 获取签署前的合同预览图片, 分为个人或企业
        const { orderId } = this.$route.query
        const { data } = await getContractPreviewImgs({ orderId, contractType: '30' })
        const imgs = data.data
        if (imgs.length === 0) {
          console.error(`合同图片数量为 :${imgs.length}`)
        }
        this.imgUrlArr = imgs
      }
    },

    // 去验证登录
    async loginFadada() {
      const { orderId } = this.$route.query
      const { data } = await loginFadada({
        orderId,
        redirectUrl: window.location.href
      })

      // if (data.code === '201') {
      //   // 如果给定的订单号查询的信息, 身份证号和姓名身份不一致,则会返回201
      //   // 此时执行线下合同下载的流程
      //   this.doOfflineContract()
      // }

      if (data.data?.verifyRedirectUrl) {
        // 身份验证通过跳转到法大大签合同界面去登录
        window.location.href = data.data.verifyRedirectUrl
        this.toggleHeaderVisible(true)
      }
    },

    // 去验证登录
    async enterpriseLoginFadada() {
      const { orderId } = this.$route.query
      const { data } = await enterpriseLoginFadada({
        orderId,
        redirectUrl: window.location.href
      })

      if (data.data?.verifyRedirectUrl) {
        // 身份验证通过跳转到法大大签合同界面去登录
        window.location.href = data.data.verifyRedirectUrl
        this.toggleHeaderVisible(true)
      }
    },

    // 线上签署
    async clickSignCaontract() {
      const { orderId, orderType, env } = this.$route.query
      // 埋点
      this.$sensors.track('signTheContract', {
        page_name: '购车合同',
        order_id: orderId,
        contractType: '30'
      })


      this.$store.commit('showLoading')
      // 生成线上合同
      const res = await getContractInfo({ orderId, contractType: '30' })
      if (!res.data.data?.sampleId) {
        return console.error('生成线上合同 sampleid 失败')
      }

      const orderInfoH5 = await getCarOrderInfoH5()
      const orderh5BaseUrl = orderInfoH5.data.data.configValue
      const token = localStorage.getItem('token') || ''
      const refreshToken = localStorage.getItem('refreshToken')
      let redirectUrl = ''
      if (env == 'minip') {
        redirectUrl = `${orderh5BaseUrl}order/new-money-detail?orderId=${orderId}&env=${env}&token=${token}&refreshToken=${refreshToken}&signStatus=2&fromPage=successStatus&type=1`
      } else {
        redirectUrl = `${orderh5BaseUrl}order/new-money-detail?orderId=${orderId}&signStatus=2&fromPage=successStatus&type=1`
      }
      const param = {
        orderId,
        contractType: '30',
        sampleId: res.data.data.sampleId,
        // 这里是设置为合同签署后的状态的页面
        redirectUrl
      }
      const { data } = await signContract(param)
      if (data.code === '00') {
        if (data.data.signUrl) {
          this.$store.commit('hideLoading')
          // 去签署合同(跳转到法大大的)
          this.routeToSignContract(data.data.signUrl)
          console.log('法大大地址:', data.data.signUrl)
          this.toggleHeaderVisible(true)
        }
      } else {
        console.error(data.data)
      }
    },

    // 下载合同(调用native)
    async downloadContract() {
      const { orderId, env } = this.$route.query
      // 埋点
      this.$sensors.track('downloadContract', {
        page_name: '交车合同',
        order_id: orderId
      })


      const { data } = await createOfflineContract({ orderId, contractType: '30' })
      const sampleId = data.data?.sampleId

      if (!sampleId) {
        return console.error(`sampleId 获取失败, sampleId:${sampleId}`)
      }
      if (env == 'minip') {
        wx.miniProgram.navigateTo({
          url: `/pages/order/download/index?sampleId=${sampleId}`
        })
        sessionStorage.setItem(orderId, 1)
        this.showUploadButton = true
        return
      }
      const param = {
        callFunc: {
          functionName: 'downloadContract',
          functionParams: { sampleId }
        },
        bizCode: BIZCODE
      }

      const res = await Promise.race([
        callNative('business', param),
        handleTimeout({
          bizCode: BIZCODE,
          text: '超时数据'
        }, 2000)
      ])

      console.log('business function', res)
      if (res.bizCode === BIZCODE) {
        this.showUploadButton = true
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '下载成功',
          forbidClick: true,
          duration: 800
        })
      }
    },

    // 显示原生header
    async toggleHeaderVisible(visible) {
      await callNative('business', {
        callFunc: {
          functionName: 'toggleNavigation',
          functionParams: {
            show: visible
          }
        },
        bizCode: BIZCODE
      })
    },


    // 跳转到上传合同的页面
    async toUploadPage() {
      const { orderId, orderPageType } = this.$route.query
      // 埋点
      this.$sensors.track('confirmUpload', {
        page_name: '上传合同',
        order_id: orderId
      })

      const { data } = await createOfflineContract({ orderId, contractType: '30' })
      const paperId = data.data?.paperId

      if (!paperId) {
        return console.error(`上传合同获取 paperId 失败: ${paperId}`)
      }

      this.$router.push({
        path: '/delivery-upload',
        query: {
          contractId: paperId,
          orderId,
          orderPageType
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.container{
  padding-bottom: 30px;
}
.btn-wrapper {
  padding: 0 17px;
  margin-top: 20px;
}

.img-wrapper {
  padding-bottom: 156px;
}
.btn-botm{
  width: 100%;
  position: fixed;
  bottom: 10px;
  left: 0;
  z-index: 9;
}

</style>
