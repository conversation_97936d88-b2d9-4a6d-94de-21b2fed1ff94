/* eslint-disable vue/no-dupe-keys */
<template>
  <div class="badge-wall-detail">
    <div class="header">
      <div class="title">
        <div
          class="btn-back"
          @click="toBack"
        />
        <div class="name">
          <span>{{ this.$route.params.levelName }}</span>
        </div>
      </div>
    </div>

    <!-- 徽章信息-->
    <div class="badge-info">
      <van-image
        class="image"
        :src="badgeInfo.iconUrl"
      />
      <span class="badge-name">{{ badgeInfo.levelName }}</span>
      <span class="detail-info">{{ badgeInfo.obtainCondition }}</span>
    </div>
    <!-- 徽章等级 -->
    <div
      class="badge-level"
    >
      <div
        v-for="item in badgeInfos"
        :key="item.id"
        @click="levelClick(item.id)"
      >
        <van-image
          class="level-imag"
          :src="item.iconUrl"
        />
        <span>LV.{{ item.level }}</span>
      </div>
    </div>
    <!-- 时间 -->
    <div
      class="lightTime"
      v-if="badgeInfo.lightStatus === 1"
    >
      {{ currentBadge.orderTime|data_format }}
    </div>
    <!-- 按钮 -->
    <div class="btnWrapper">
      <!-- /v-if="badgeInfo.lightStatus === 1 && userInfo.id === pageUserId" -->
      <div
        class="btn"
        v-if="badgeInfo.lightStatus === 1 && userInfo.id === pageUserId"
        @click="wearClick"
      >
        设置主页展示
      </div>
    </div>

    <Header
      ref="header"
      v-show="false"
    />
  </div>
</template>

<script>
import { Image, Button } from 'vant'
import Vue from 'vue'
import { mapMutations } from 'vuex'
import {
  wearBadge, getUserAboutInfo, getAllBadges, getUserInfo
} from '@/api/wall'
import Header from '@/components/header.vue'

Vue.use(Image, Button)

export default {

  data() {
    return {
      screeHeight: document.body.clientHeight,
      badgeInfo: '', // 徽章信息
      badgeInfos: '', // 获取所有的等级徽章
      allBadges: '',
      userId: this.$route.params.userId,
      currentBadge: '', // 当前点亮徽章
      badgeId: parseInt(this.$route.params.badgeId),
      userInfo: '', // 登录用户信息
      pageUserId: this.$route.params.userId // 访问页面用户id
    }
  },
  components: {
    Header
  },
  created() {
    this.getBadgeInfo()
    this.defaultWearBadge()
    this.getUserInfo()
    this.setHeaderVisible(false)
  },
  methods: {
    ...mapMutations(['setHeaderVisible']),
    async  getBadgeInfo() {
      try {
        const levelName = this.$route.params.levelName
        const result = await getAllBadges(2)
        this.allBadges = result.data.data
        const badges = this.allBadges.filter((item) => item.id === this.badgeId)
        this.badgeInfo = badges[0]
        // 获取所有同type类型不亮徽章
        const notLightBadges = this.allBadges.filter((item) => item.type === this.badgeInfo.type && item.lightStatus === 0)
        // 用户点亮的徽章
        const userResult = await getUserAboutInfo(this.userId)
        const successLightBadges = userResult.data.data.allBadge.filter((item) => item.category === 1)
        // 获取所有同type类型亮徽章
        const lightBadges = []
        for (let i = 0; i < this.allBadges.length; i++) {
          successLightBadges.forEach((item) => {
            if (this.allBadges[i].id === parseInt(item.orderId)) {
              lightBadges.push(this.allBadges[i])
            }
          })
        }
        // 亮替换不亮
        for (let i = 0; i < notLightBadges.length; i++) {
          lightBadges.forEach((item) => {
            if (notLightBadges[i].level === item.level && notLightBadges[i].type === item.type) {
              notLightBadges[i] = item
            }
          })
        }
        this.badgeInfos = notLightBadges
      } catch (error) {}
    },
    async levelClick(badgeId) {
      try {
        const badge = this.allBadges.filter((item) => item.id === badgeId)
        this.badgeInfo = badge[0]
        const result = await getUserAboutInfo(this.userId)
        const userBadges = result.data.data.allBadge
        const currentBadges = userBadges.filter((item) => parseInt(item.orderId) === badgeId)
        this.currentBadge = currentBadges[0]
      } catch (error) {}
    },
    async defaultWearBadge() {
      try {
        const result = await getUserAboutInfo(this.userId)
        const currentBadges = result.data.data.allBadge.filter((item) => parseInt(item.orderId) === this.badgeId)
        this.currentBadge = currentBadges[0]
      } catch (error) {}
    },
    async wearClick() {
      try {
        const result = await wearBadge(this.currentBadge.carSeriesId, this.currentBadge.orderId, 1)
        const { code, message } = result.data
        this.$router.push({
          name: 'badge-wall',
          params: {
            code: code
          }
        })
      } catch (error) {}
    },
    async getUserInfo() {
      try {
        const result = await getUserInfo()
        this.userInfo = result.data.data
      } catch (error) {}
    },
    toBack() {
      this.$refs.header.toBack()
    }
  },
  filters: {
    data_format(lightTime) {
      if (lightTime !== null && lightTime !== undefined) {
        const dataArr = lightTime.split(' ')[0].split('-')
        return `点亮 于${dataArr[0]}年${dataArr[1]}月${dataArr[2]}日`
      }
    }
  }
}
</script>

<style scoped lang="less">

.header{
  margin-top: 44px;
.title{
        height: 44px;
        display: flex;
        padding: 12px 12px 11px 12px;
        box-sizing:border-box;
        .btn-back{
          width: 20px;
          height: 21px;
          background: url('~@/assets/wall/icon01.png') center/contain no-repeat;
        }
        .name{
          display: flex;
          width: 100%;
          justify-content: center;
          font-size: 16px;
        }
     }
}
    // 徽章信息
     .badge-info{
       display: flex;
       flex-direction: column;
       justify-content: center;
       align-items: center;
       margin-top: 45px;
       margin-bottom: 42px;
       .image{
         width: 208px;
         height: 208px;
       }
       .badge-name{
          margin: 10px 0px 16px;
          font-size: 18px;
          font-weight: 700;
       }
       .detail-info{
         font-size: 14px;
       }
     }

    //  徽章等级信息
    .badge-level{
      display: flex;
      flex: 1;
      justify-content: space-around;
      margin-bottom: 50px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.5);
      div{
          display: flex;
          flex-direction:column;
          align-items: center;
          // width: 60px;
          .level-imag{
            width: 60px;
            height: 60px;
            fit:'cover'
          }
      }
    }

    // 按钮
    .btnWrapper{
      width: 100%;
      height: 56px;
      position:fixed;
      bottom: 15px;
      color: #ffffff;
      .btn{
        width: calc(100% - 32px);
        line-height: 56px;
        text-align: center;
        margin: 0 auto;
        background-color: #000;
      }
    }
    .lightTime{
      margin-top: 90px;
      text-align: center;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.5)
    }
</style>
