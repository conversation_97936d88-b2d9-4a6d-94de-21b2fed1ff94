<template>
  <div class="finace-wrapper" v-show="financeImgUrl" :class="{
    expand: isExpand
  }">
    <img :src="financeImgUrl" alt="">
    <div class='btn' @click='toggleHeight'>
      <van-icon :name="isExpand ? 'arrow-down' : 'arrow-up'" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useStore } from '../util/vueApi.js'
import { getPowerPageImg } from '@/configratorApi'
import url from '@/config/url'
import { getQueryParam } from '@/utils'

const ossUrl = url.BaseConfigrationOssHost

const financeImgUrl = ref('')
const isExpand = ref(true)// 是否展开

const toggleHeight = () => {
  isExpand.value = !isExpand.value
}

// 获取权益图片
const getFloorFinanceImg = async () => {
  const res = await getPowerPageImg()
  const mapping = {
    0: 'carBuyingAdvertisementA7L',
    1: 'carBuyingAdvertisementQ5e',
    2: 'carBuyingAdvertisementQ6'
  }
  if (res.data.code !== '00') return

  const carIdx = getQueryParam('idx')
  if (!carIdx) {
    return console.error('URL 参数里无 idx, 无法获取权益图片')
  }
  if (res.data.data) {
    const financeData = res.data.data.find((i) => i.floorFrontCode === mapping[carIdx])
    if (!financeData) {
      return console.error('/api-wap/audi-page/api/floors 未找到对应车型的权益图片')
    }
    const img = `${ossUrl}/${financeData.content.contentDetailList[0].imageUrl}`
    financeImgUrl.value = img
  }
}

onMounted(() => {
  getFloorFinanceImg()
})

</script>

<style lang="less" scoped>
.finace-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin-bottom: 20px;
  box-sizing: border-box;
  overflow: hidden;
  transition: height 0.3s;
  height: 50px;

  &.expand {
    height: 110px;
  }

  .btn {
    position: absolute;
    right: 16px;
    top: 16px;
    padding: 6px;
    color: #fff;
    font-size: 20px;
  }

  img {
    width: 100%;
  }
}
</style>
