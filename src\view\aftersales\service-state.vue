<template>
  <div>
    <div class="border-title" />
    <div style="position: relative;">
      <div class="box-left">
        <div :style="{ background: index === 0 ? '#F2F2F2' : '#fff' }" class="border-bottom" v-for="(item, index) in titleArray" :key="index">
          {{ item }}
        </div>
      </div>
      
      <div class="contrast">
        <div class="car_item border-bottom" v-for="(item, index) in contentArray" :key="index" :style="{
            width: contentArray[0].show.length * 100 + 'px',
            background: index === 0 ? '#F2F2F2' : '#fff',
          }">
          <div class="item_val border-left"  v-for="(item, index2) in item.show" :key="index2">
            <div v-if="index === 0">{{ item.mileage / 10000 + "万公里" }}</div>
            <!-- <div v-if="index !== 0 && item.mileage > 0">{{  }}</div> -->
            <img v-if="index !== 0 && item.mileage > 0" src="../../assets/img/icon-selected.png" />
          </div>
        </div>
      </div>
      
    </div>
    <!-- <p class="item-title-bold">保养说明</p> -->
    <!-- <div class="contentdiv">
      1、保养项目仅供参考。实际保养需求请参考提示和车
      辆实际使用情况。详情请咨询当地Audi服务商。
    </div>
    <div class="contentdiv">2、所有套餐价格均包含零件及工时费用。</div>
    <div class="contentdiv">
      3、表内机油机滤保养套餐价格，均已5W30LL-01机油
      为基准。如需使用其它机油（如0W30机油，或适合带
      有颗粒捕捉器车辆(GPF)的机油等），价格会相应调整，
      最终价格请以服务商店内价格为准。
    </div> -->
  </div>
</template>

<script>
  import { getReportList } from "@/api/api";

  export default {
    data() {
      return {
        titleArray: [],
        contentArray: [],
      };
    },
    mounted() {
      const { packageType, seriesCode ,modelCode} = this.$route.query;

      this.getReportList(packageType, seriesCode,modelCode);
    },
    methods: {
      async getReportList(packageType, seriesCode,modelCode) {
        const { data } = await getReportList({
          packageType: packageType,
          seriesCode: seriesCode,
          modelCode:modelCode,
        });
        if (data.code === "200") {
          this.titleArray.push("保养套餐");
          data.data.contents.forEach((item) => {
            this.titleArray.push(item.packageName);
          });

          const itemList = {
            show: [],
          };

          data.data.titles.forEach((item) => {
            itemList.show.push(item);
          });
          this.contentArray.push(itemList);

          data.data.contents.forEach((item) => {
            if (item.items.length === data.data.titles.length) {
              itemList.show = item.items;
              this.contentArray.push(itemList);
            } else {
              const itemList2 = {
                show: [],
              };
              data.data.titles.forEach((item1) => {
                let mileage = this.dataHandle(item.items, item1.mileage);

                itemList2.show.push({ mileage: mileage });
              });

              console.log(itemList2.show);

              this.contentArray.push(itemList2);
            }
          });
        }
      },

      dataHandle(list, value) {
        const item = list.find((i) => i.mileage === value);
        if (item !== undefined) {
          return item.mileage;
        } else {
          return 0;
        }
      },
    },
  };
</script>

<style scoped lang="less">
  @import url("../../assets/style/scroll.less");

  .box-left {
    width: 140px;
    height: 100%;
    display: flex;
    flex-flow: column;
    position: absolute;
    border-right: 1px solid #f2f2f2;
    left: 0;

    div {
      display: flex;
      align-items: center;
      font-size: 12px;
      font-family: "Audi-WideBold";
      color: #333;
      background: #fff;
    }
  }

  .contrast {
    stroke-width: 100%;
    overflow-y: scroll;
    margin-left: 140px;

    .car_item {
      display: flex;
      // width: 100%;
      // position: relative;

      .item_val {
        width: 125px;
        display: flex;
        align-items: center;
        justify-content: center;

        div {
          // align-items: center;
          font-size: 12px;
          color: #000000;
          font-family: "Audi-Normal";
          justify-content: center;
        }

        img {
          height: 18px;
          width: 18px;
          justify-content: center;
        }
      }
    }
  }

  .border-title {
    border-bottom: 1px solid #f2f2f2;
  }

  .border-bottom {
    height: 50px;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    justify-content: center;
    font-family: "Audi-WideBold";
  }

  .border-left {
    border-right: 1px solid #f2f2f2;
    

  }

  .item-title-bold {
    padding-left: 16px;
    font-size: 16px;
    color: #000;
    font-weight: normal;
    font-family: "Audi-WideBold";
  }

  .contentdiv {
    padding-left: 16px;
    padding-right: 16px;
    font-size: 14px;
    color: #000000;
    line-height: 22px;
    margin-bottom: 12px;
  }
</style>
