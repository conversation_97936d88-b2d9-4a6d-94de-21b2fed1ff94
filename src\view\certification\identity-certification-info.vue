<template>
  <div class="unsubscribeSucceed">
    <div class="line-border-bottom">
      <div class="name">状态</div>
      <div class="str">
        {{ showMember() }}
      </div>
    </div>

    <div class="line-border-bottom">
      <div class="name">姓名</div>
      <div class="str" v-if="!showName">
        {{ memberInfoModel.name ? encryptionName(memberInfoModel.name) : '' }}
      </div>
      <div class="str" v-else>
        {{ memberInfoModel.name}}
      </div>
      <van-icon
      :name="showName ? 'closed-eye' : 'eye-o'"
      size="24"
      @click="showName = !showName"
    />
    </div>
    <div class="line-border-bottom">
      <div class="name">证件</div>
      <div class="str">
        {{ showGidType() }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">证件号</div>
      <div class="str">
        <span>
          {{
            showCardNum && memberInfoModel.gid
              ? memberInfoModel.gid
              : '**************' + memberInfoModel.gid.substring(14)
          }}
        </span>

        <van-icon
          :name="showCardNum ? 'closed-eye' : 'eye-o'"
          size="24"
          @click="showCardNum = !showCardNum"
        />
      </div>
    </div>

    <!-- <div class="btnWarp">
      <div @click="onCarUnbinding">解除绑定 ></div>
    </div> -->
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Image as VanImage,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover,
  ActionSheet,
  Grid,
  GridItem
} from 'vant'
import { callNative } from '@/utils'
import { getManCarMemberInfo } from '@/api/api'
import AudiButton from '@/components/audi-button'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)
  .use(ActionSheet)
  .use(Grid)
  .use(GridItem)
  .use(VanImage)
export default {
  components: {
    AudiButton
  },
  data() {
    return {
      memberInfoModel: {},
      showCardNum: false,
      showName: false
    }
  },
  mounted() {
    this.getManCarMemberInfo()
  },

  methods: {
    encryptionName(fullName) {
      if (!fullName) {
        return ''
      }
      if (fullName.length < 2) {
        return fullName
      }
      const lastName = fullName.substring(0, 1)
      const otherCharsCount = fullName.length - lastName.length
      const maskedPart = '*'.repeat(otherCharsCount)
      return lastName + maskedPart
    },
    // 获取会员信息
    async getManCarMemberInfo() {
      this.$store.commit('showLoading')
      const { data } = await getManCarMemberInfo({})
      this.$store.commit('hideLoading')
      this.memberInfoModel = data.data
      // 未认证跳转至认证页面
      if (
        this.memberInfoModel.certificationStatus == 2
        || this.memberInfoModel.certificationStatus == 4
      ) {
        this.$router.replace({
          path:
            `/certification/my-confirm?certificationStatus=${
              this.memberInfoModel.certificationStatus}`
        })
      }
    },
    showMember() {
      if (this.memberInfoModel.certificationStatus == 1) {
        return '已认证'
      }
      if (this.memberInfoModel.certificationStatus == 2) {
        return '未认证'
      }
      if (this.memberInfoModel.certificationStatus == 3) {
        return '审核中'
      }
      if (this.memberInfoModel.certificationStatus == 4) {
        return '审核被拒'
      }
    },

    showGidType() {
      if (this.memberInfoModel.gidType == '1') {
        return '身份证'
      }
      if (this.memberInfoModel.gidType == '2') {
        return '驾驶证'
      }
      if (this.memberInfoModel.gidType == '101') {
        return '外国人护照'
      }
      if (this.memberInfoModel.gidType == '102') {
        return '港澳台居民往内地通行证（回乡证）'
      }
      if (this.memberInfoModel.gidType == '103') {
        return '台湾居民来往大陆通行证（台胞证）'
      }
      if (this.memberInfoModel.gidType == '104') {
        return '港澳居民居住证'
      }
      if (this.memberInfoModel.gidType == '105') {
        return '台湾居民居住证'
      }
      if (this.memberInfoModel.gidType == '106') {
        return '中国人民解放军军官证（军官证）'
      }
      if (this.memberInfoModel.gidType == '107') {
        return '中国人民武装警察部队警官证（武警证）'
      }
      if (this.memberInfoModel.gidType == '108') {
        return '外国人永久居留居住证（外国人永居证）'
      }
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/buttons.less');

.unsubscribeSucceed {
  padding: 16px;
  img {
    margin-top: 10px;
    width: 48px;
    height: 48px;
  }

  p {
    text-align: center;
    font-size: 16px;
    color: #000000;
    margin-top: 8px;
  }
  .content {
    text-align: center;
    font-size: 14px;
    color: #666;
  }

  .interval_line {
    margin-left: -16px;
    margin-right: -16px;
    height: 8px;
    background: #f2f2f2;
  }
  .line-border-bottom {
    width: 100%;
    display: flex;
    //   align-items: center;
    margin-top: 30px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f2f2f2;

    .name {
      width: 80px;
      font-size: 16px;
      color: #000;
    }
    .str {
      flex: 1;
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
      font-family: 'Audi-Normal';
      font-size: 16px;
      color: #333;
    }
  }

  .line {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
    white-space: nowrap;
  }

  .btnWarp {
    position: fixed;
    z-index: 2;
    height: 30px;
    width: 100%;
    bottom: 0;
    text-align: center;
    padding-bottom: 30px;
    background: #fff;
  }
}
._modal {
  width: 343px;
  background: #ffffff;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;

  .modal-title {
    font-size: 18px;
    font-weight: 500;
    color: #1a1a1a;

    font-family: 'Audi-WideBold';
  }

  .modal-line {
    width: 80%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
    white-space: nowrap;
  }
}
</style>
