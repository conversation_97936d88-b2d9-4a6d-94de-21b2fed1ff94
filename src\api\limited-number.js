/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-05-06 14:21:13
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-09-27 11:19:45
 * @FilePath     : \src\api\limited-number.js
 * @Descripttion : 典藏号
 */
import request from '@/router/axios'
import api from '@/config/url'
import URL from './url'

const baseUrl = api.BaseApiUrl

// 查询限量号(处于哪个)阶段
export const getMineLimitedNumberStep = (params, nickCode = '') => request({
  url: `${baseUrl + URL.getMineLimitedNumberStep + nickCode}`,
  method: 'GET',
  params
})

// 查询当前号池剩余数量
export const getLimitedNumberCount = (params, nickCode = '') => request({
  url: `${baseUrl + URL.getLimitedNumberCount + nickCode}`,
  method: 'GET',
  params
})

// 查询限量号列表
export const getLimitedNumberList = (params, nickCode = '') => request({
  url: `${baseUrl + URL.getLimitedNumberList + nickCode}`,
  method: 'GET',
  params
})


// 绑定限量号
export const bindLimitedNumber = (params, nickCode = '') => request({
  url: `${baseUrl + URL.bindLimitedNumber + nickCode}`,
  method: 'GET',
  params
})

// 获取限量号
export const getMineLimitedNumber = (params, nickCode = '') => request({
  url: `${baseUrl + URL.getMineLimitedNumber + nickCode}`,
  method: 'GET',
  params
})


// 获取限量号
export const pitchOnLimitedNumber = (params, nickCode = '') => request({
  url: `${baseUrl + URL.pitchOnLimitedNumber + nickCode}`,
  method: 'GET',
  params
})
