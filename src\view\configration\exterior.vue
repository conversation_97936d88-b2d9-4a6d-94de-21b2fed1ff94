<template>
  <div class="exterior-wrapper">
    <img
      @click="handleShow3D(1)"
      @load="endLoad"
      class="exterior-3dIcon"
      src="../../assets/img/icon02.png"
      v-if="!isMinip && has3D"
      alt=""
    >
    <div
      style="    height: 220px;
    position: relative;
    overflow: hidden;"
    >
      <img-swiper :img-list="currentConfig2dImgList" />
    </div>
    <div class="exterior-main">
      <div class="main-colorNameCn">
        {{ currentExColor.optionName }}
      </div>
      <div
        class="main-descText"
        v-if="hasPrice"
      >
        {{ currentExColor.price | finalFormatPrice }}
      </div>
      <div class="main-colorBox">
        <div
          v-for="item in exteriorList"
          :key="item.optionId"
          @click="selectedEx(item)"
          :class="['main-imgItem',item.optionId === currentExColor.optionId ? 'selected': '']"
        >
          <img
            :src="BaseConfigrationOssHost + item.imageUrl | imgFix(140)"
            alt=""
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ImgSwiper from './imgSwiper.vue'
import url from '@/config/url'
import {
  callNative, getUrlParamObj, checkV2
} from '../../utils'
import { getCarConfigcc, getQ5EConfigcc, getQ6cc } from '@/api/api'

const { env } = getUrlParamObj()
const isMinip = env === 'minip'
const OSS_URL = url.BaseConfigrationOssHost
const MAIN_IMG_SIX_MAP = [
  'Front45.png',
  'Side.png',
  'Rear45.png',
  'Rear.png',
  'Front.png',
  'Top.png'
]

export default {
  props: {
    currentIndex: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      isMinip: isMinip,
      has3D: true,
      hasPrice: true
    }
  },
  async created() {
    this.$store.commit('showLoading')
  },
  watch: {
    'selectCarInfo.modelLineId': {
      async handler(next) {
        const { from } = this.$route.query

        if ((this.idx !== '1' && from !== 'hot') || (this.idx === '1' && !checkV2() && from !== 'hot')) return

        this.$store.commit('setCurrentV2popupItem', -1)
        if (next) {
          // 查询库存 暂未用
          // await this.$store.dispatch('measureQueryBefore')
          if (checkV2()) {
            await this.$store.dispatch('measureQuery', { step: '1' })
          } else {
            const fromPath = this.$store.state.fromPath
            if (fromPath.includes('optiondetail') && this.currentTabIndex === '4') {
              const p = this.$storage.getPlus('cc_data')
              this.$store.commit('setSelectCarIfo', p.selectCarInfo)
              this.$store.commit('setCurrentOptionsList', p.currentOptionsList)
            } else {
              this.$store.dispatch('getExteriorList')
              this.$store.dispatch('getModelHubList')
              this.$store.dispatch('getAllInterior')
              this.$store.dispatch('getPrivateOrderList', { idx: this.idx })
            }
          }
        }
      },
      immediate: true
    },
    async currentExColor(next) {
      if (next) {
        if (checkV2()) {
          await this.$store.dispatch('measureQuery', { step: '2' })
        }
      }
    }
  },
  computed: {
    ...mapState([
      'idx',
      'currentTabIndex',
      'iframeUrl',
      'exteriorList',
      'selectCarInfo',
      'currentExColor',
      'currentModelHub',
      'currentInteriorChair',
      'currentInteriorEih',
      'currentSibColorInterieur',
      'currentOptionsList',
      'currentPrice',
      'allPrivateOrderList',
    ]),

    // 外观2d图
    currentConfig2dImgList() {
      const currentSeriesName = ['a7l', 'q5e', 'q6'][this.idx]
      const modelLineCode = this.selectCarInfo.modelLineCode
      const { currentExColor, currentModelHub, exteriorList } = this

      if (!currentExColor.optionCode || !currentModelHub.optionCode) return []
      const exist = exteriorList.find((i) => i.optionCode === currentExColor.optionCode)
      if (!exist) return []

      // example img: https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/q5e/carImages/G4IBC3004/2T2T/56E/Front.png
      const base = `${OSS_URL}/ccpro-backend/${currentSeriesName}/carImages/${modelLineCode}/${currentExColor.optionCode}/${currentModelHub.optionCode}/`

      const imgUrlList = MAIN_IMG_SIX_MAP.map((name) => base + name)
      return imgUrlList
    }

  },
  methods: {
    endLoad() {
      this.$store.commit('hideLoading')
    },
    selectedEx: function (color) {
      this.$store.commit('setSelectExColor', color)

      const personalTailor = this.currentOptionsList.map((res) => res.optionName).join('/')


      // 埋点
      let interiorType = ''
      if (this.currentInteriorChair && this.currentInteriorChair.optionName) {
        interiorType += (`${this.currentInteriorChair.optionName}/`)
      }
      this.$sensors.track('colorSelection', {
        model_name: this.selectCarInfo.modelLineName,
        price: this.currentPrice,
        cars_appearance: color.optionName,
        hub_model: this.currentModelHub.optionName,
        interior_type: `${interiorType}${this.currentSibColorInterieur?.sibName}/${this.currentSibColorInterieur.interieurName}/${this.currentInteriorEih.optionName}`,
        personal_tailor: personalTailor
      })
    },
    handleShow3D: async function (k) {
      this.$store.commit('showLoading')
      const { seriesName } = this.selectCarInfo
      const series = seriesName.toLowerCase()

      const currentModelCode = this.selectCarInfo.modelLineCode
      const currentExCode = this.currentExColor.optionCode
      let currentInCode = []
      if (series === 'a7l') {
        this.currentInteriorChair && currentInCode.push(this.currentInteriorChair.optionCode)
        this.currentSibColorInterieur && currentInCode.push(this.currentSibColorInterieur.sibOptionCode)
        this.currentSibColorInterieur && currentInCode.push(this.currentSibColorInterieur.interieurOptionCode)
      } else {
        this.currentSibColorInterieur && currentInCode.push(this.currentSibColorInterieur.interieurOptionCode)
        this.currentSibColorInterieur && currentInCode.push(this.currentSibColorInterieur.sibOptionCode)
        // 这几个面料默认7座，选了WE8就是6座
        if ('G4IBC3001,G4ICC3001'.includes(currentModelCode) && 'EO-N4X,DS-N4X,AJ-N4X,TO-N7K'.includes(this.currentSibColorInterieur.sibInterieurCode)) {
          if (!this.currentOptionsList.find((option) => option.optionCode === 'WE8')) {
            currentInCode.push('seat7')
          }
        }
      }
      currentInCode = currentInCode.join('+')
      const currentHub = this.currentModelHub.optionCode

      const currentOptions = this.currentOptionsList.map((item) => item.optionCode) || []
      currentOptions.push('HSP') // iframe隐藏热点
      if (series === 'a7l') {

        // 6NQ 黑色顶棚
        const BLACK_TOP = '6NQ'
        const SPEC_CAR = ['498B2Y002','498B2Y004','498B2Y005','498BZY002','498BZY003'] // a7l 耀黑套装

        const optionExist = this.currentOptionsList.find((i) => i.optionCode === BLACK_TOP)   //选装包数据
        const privateExist = this.allPrivateOrderList.find(i=>i.optionCode === BLACK_TOP && i.status === 1)   // 查找私人订制数据,如果有6NQ的标装,则添加
        
        if (!optionExist) {
          if (SPEC_CAR.includes(currentModelCode) || privateExist) {
            currentOptions.push(BLACK_TOP) // 曜黑配置,默认添加黑顶选装
          } else {
            currentOptions.push('N6NQ') // 若没有黑色顶棚,则手动添加自创白色顶棚的code
          }
        }
      }

      currentOptions.push(this.currentInteriorEih.optionCode)
      const currentCar = {
        currentModelCode,
        currentExCode,
        currentInCode,
        currentHub,
        currentOptions,
        platform: 'app',
        showCloseBtn: true,
        showInnerHotspot: '0',
        currentView: 'out',
        currentScene: 'chengshi' // 当前场景，值为“shanghai”和“chengshi”和“guangzhou”
      }
      let url = 'https://testcc.fontre.com/audi-a7l0.0.6/index.html'
      const { data } = series === 'a7l' ? await getCarConfigcc() : (series == 'q6' ? await getQ6cc() : await getQ5EConfigcc())
      url = data.data.configValue
      if (data.code !== '00') return console.error('获取url出错')
      const currentCarBase64 = window.btoa(JSON.stringify(currentCar))
      const str = url.includes('?') ? '&' : '?'
      const iframeUrl = `${url + str}hidePanel=1&currentCar=${currentCarBase64}`
      this.$store.commit('hideLoading')
      await callNative('audiOpen', {
        path: iframeUrl,
        params: 'test',
        pageOrientation: 'landscape'
      })
    }
  },
  components: { ImgSwiper }
}
</script>

<style lang="less" scoped>
.exterior-wrapper {
  height: calc(100vh - 190px);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  .exterior-3dIcon {
    width: 52px;
    height: 52px;
    position: absolute;
    top: 18px;
    left: 24px;
    z-index: 2;
  }

  iframe {
    width: 100%;
    height: 220px;
  }

  .exterior-main {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;
    line-height: 20px;

    // padding: 0 16px;
    .main-colorNameCn {
      color: #000000;
      margin-top: 62px;
      margin-bottom: 16px;
    }

    .main-descText {
      color: #333333;
      margin-bottom: 32px;
    }

    .main-colorBox {
      width: 100%;
      display: inline-flex;
      overflow-x: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      // display: grid;
      // grid-template-columns: repeat(4,70px);
      // grid-row-gap: 20px;
      // grid-column-gap: 20px;
      .main-imgItem {
        box-sizing: border-box;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 70px;
        height: 70px;
        padding: 6px;
        margin-right: 16px;
        border: solid 1px #fff;

        &:first-of-type {
          margin-left: 16px;
        }

        &:last-of-type {
          margin-right: 16px;
        }

        &.selected {
          border: solid 1px #000;
        }

        >img {
          width: 58px;
          height: 58px;
        }
      }
    }
  }
}
</style>
