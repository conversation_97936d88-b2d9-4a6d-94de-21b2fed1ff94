# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@amap/amap-jsapi-loader@^1.0.1":
  "integrity" "sha512-nPyLKt7Ow/ThHLkSvn2etQlUzqxmTVgK7bIgwdBRTg2HK5668oN7xVxkaiRe3YZEzGzfV2XgH5Jmu2T73ljejw=="
  "resolved" "https://registry.npmjs.org/@amap/amap-jsapi-loader/-/amap-jsapi-loader-1.0.1.tgz"
  "version" "1.0.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.0.0-beta.40", "@babel/code-frame@^7.12.13":
  "integrity" "sha1-3PyCa+72XnXFDiHTg319lXmN1lg="
  "resolved" "https://registry.npm.taobao.org/@babel/code-frame/download/@babel/code-frame-7.12.13.tgz?cache=0&sync_timestamp=1612314635887&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/highlight" "^7.12.13"

"@babel/code-frame@7.12.11":
  "integrity" "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8="
  "resolved" "https://registry.nlark.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz?cache=0&sync_timestamp=1623280853270&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.12.11.tgz"
  "version" "7.12.11"
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.13.15", "@babel/compat-data@^7.14.0":
  "integrity" "sha1-qQESi84q0CVl35Xm7L8ZXPlGWRk="
  "resolved" "https://registry.nlark.com/@babel/compat-data/download/@babel/compat-data-7.14.0.tgz?cache=0&sync_timestamp=1619727430608&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fcompat-data%2Fdownload%2F%40babel%2Fcompat-data-7.14.0.tgz"
  "version" "7.14.0"

"@babel/core@^7.11.0":
  "integrity" "sha1-U5XjBAXwd2Bn+9nPCITxW/t3Cjg="
  "resolved" "https://registry.nlark.com/@babel/core/download/@babel/core-7.14.3.tgz"
  "version" "7.14.3"
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@babel/generator" "^7.14.3"
    "@babel/helper-compilation-targets" "^7.13.16"
    "@babel/helper-module-transforms" "^7.14.2"
    "@babel/helpers" "^7.14.0"
    "@babel/parser" "^7.14.3"
    "@babel/template" "^7.12.13"
    "@babel/traverse" "^7.14.2"
    "@babel/types" "^7.14.2"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.1.2"
    "semver" "^6.3.0"
    "source-map" "^0.5.0"

"@babel/generator@^7.14.2", "@babel/generator@^7.14.3":
  "integrity" "sha1-DCZS2R973at8zMa6gVfk9A3O25E="
  "resolved" "https://registry.nlark.com/@babel/generator/download/@babel/generator-7.14.3.tgz"
  "version" "7.14.3"
  dependencies:
    "@babel/types" "^7.14.2"
    "jsesc" "^2.5.1"
    "source-map" "^0.5.0"

"@babel/helper-annotate-as-pure@^7.12.13":
  "integrity" "sha1-D1jobfxLs7H819uAZXDhd9Q5tqs="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.12.13.tgz?cache=0&sync_timestamp=1612314636125&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-annotate-as-pure%2Fdownload%2F%40babel%2Fhelper-annotate-as-pure-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/types" "^7.12.13"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.12.13":
  "integrity" "sha1-a8IDYciLCnTQUTemXKyNPL9vYfw="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.12.13"
    "@babel/types" "^7.12.13"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.13.16", "@babel/helper-compilation-targets@^7.9.6":
  "integrity" "sha1-bpHczxXj9D5VVt/+MthgEJiHVjw="
  "resolved" "https://registry.nlark.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.13.16.tgz"
  "version" "7.13.16"
  dependencies:
    "@babel/compat-data" "^7.13.15"
    "@babel/helper-validator-option" "^7.12.17"
    "browserslist" "^4.14.5"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.13.0", "@babel/helper-create-class-features-plugin@^7.14.0", "@babel/helper-create-class-features-plugin@^7.14.2", "@babel/helper-create-class-features-plugin@^7.14.3":
  "integrity" "sha1-gyERvPT1fKV6TFsaAA/BJavGVUo="
  "resolved" "https://registry.nlark.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.14.3.tgz"
  "version" "7.14.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.12.13"
    "@babel/helper-function-name" "^7.14.2"
    "@babel/helper-member-expression-to-functions" "^7.13.12"
    "@babel/helper-optimise-call-expression" "^7.12.13"
    "@babel/helper-replace-supers" "^7.14.3"
    "@babel/helper-split-export-declaration" "^7.12.13"

"@babel/helper-create-regexp-features-plugin@^7.12.13":
  "integrity" "sha1-FJqm14wBbjGMQ+JAmgrpwTaoZog="
  "resolved" "https://registry.nlark.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.14.3.tgz"
  "version" "7.14.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.12.13"
    "regexpu-core" "^4.7.1"

"@babel/helper-define-polyfill-provider@^0.2.1":
  "integrity" "sha1-5vX0pu3DciFSwhNZGQ3mf8bPZk0="
  "resolved" "https://registry.nlark.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-explode-assignable-expression@^7.12.13":
  "integrity" "sha1-F7XFn/Rz2flW9A71cM86dsoSZX8="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/types" "^7.13.0"

"@babel/helper-function-name@^7.12.13", "@babel/helper-function-name@^7.14.2":
  "integrity" "sha1-OXaItZB2C273cltfCGDIJCfrqsI="
  "resolved" "https://registry.nlark.com/@babel/helper-function-name/download/@babel/helper-function-name-7.14.2.tgz?cache=0&sync_timestamp=1620839583330&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-function-name%2Fdownload%2F%40babel%2Fhelper-function-name-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-get-function-arity" "^7.12.13"
    "@babel/template" "^7.12.13"
    "@babel/types" "^7.14.2"

"@babel/helper-get-function-arity@^7.12.13":
  "integrity" "sha1-vGNFHUA6OzCCuX4diz/lvUCR5YM="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.12.13.tgz?cache=0&sync_timestamp=1612314686467&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-get-function-arity%2Fdownload%2F%40babel%2Fhelper-get-function-arity-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/types" "^7.12.13"

"@babel/helper-hoist-variables@^7.13.0":
  "integrity" "sha1-GxZRJJ6UtR+PDTNDmEPjPjl3WzA="
  "resolved" "https://registry.nlark.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.13.16.tgz"
  "version" "7.13.16"
  dependencies:
    "@babel/traverse" "^7.13.15"
    "@babel/types" "^7.13.16"

"@babel/helper-member-expression-to-functions@^7.13.12":
  "integrity" "sha1-3+No8m1CagcpnY1lE4IXaCFubXI="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.13.12.tgz?cache=0&sync_timestamp=1616428120148&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-member-expression-to-functions%2Fdownload%2F%40babel%2Fhelper-member-expression-to-functions-7.13.12.tgz"
  "version" "7.13.12"
  dependencies:
    "@babel/types" "^7.13.12"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.13.12", "@babel/helper-module-imports@^7.8.3":
  "integrity" "sha1-xqNppvNiHLJdoBQHhoTakZa2GXc="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-module-imports/download/@babel/helper-module-imports-7.13.12.tgz"
  "version" "7.13.12"
  dependencies:
    "@babel/types" "^7.13.12"

"@babel/helper-module-imports@7.0.0-beta.35":
  "integrity" "sha1-MI41DnMXUs200PBY3x1wSSXGTgo="
  "resolved" "https://registry.nlark.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.0.0-beta.35.tgz"
  "version" "7.0.0-beta.35"
  dependencies:
    "@babel/types" "7.0.0-beta.35"
    "lodash" "^4.2.0"

"@babel/helper-module-transforms@^7.13.0", "@babel/helper-module-transforms@^7.14.0", "@babel/helper-module-transforms@^7.14.2":
  "integrity" "sha1-rBzDDuR7lF4+DE2xL6DFOJUJ3+U="
  "resolved" "https://registry.nlark.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.14.2.tgz?cache=0&sync_timestamp=1620839457149&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-module-transforms%2Fdownload%2F%40babel%2Fhelper-module-transforms-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-module-imports" "^7.13.12"
    "@babel/helper-replace-supers" "^7.13.12"
    "@babel/helper-simple-access" "^7.13.12"
    "@babel/helper-split-export-declaration" "^7.12.13"
    "@babel/helper-validator-identifier" "^7.14.0"
    "@babel/template" "^7.12.13"
    "@babel/traverse" "^7.14.2"
    "@babel/types" "^7.14.2"

"@babel/helper-optimise-call-expression@^7.12.13":
  "integrity" "sha1-XALRcbTIYVsecWP4iMHIHDCiquo="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.12.13.tgz?cache=0&sync_timestamp=1612314636446&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-optimise-call-expression%2Fdownload%2F%40babel%2Fhelper-optimise-call-expression-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/types" "^7.12.13"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha1-gGUmzhJa7QM3O8QWqCgyHjpqM68="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.13.0.tgz"
  "version" "7.13.0"

"@babel/helper-plugin-utils@^7.14.5":
  "integrity" "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak="
  "resolved" "https://registry.nlark.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.14.5.tgz?cache=0&sync_timestamp=1623280305577&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-plugin-utils%2Fdownload%2F%40babel%2Fhelper-plugin-utils-7.14.5.tgz"
  "version" "7.14.5"

"@babel/helper-remap-async-to-generator@^7.13.0":
  "integrity" "sha1-N2p2DZ97SyB3qd0Fqpw5J8rbIgk="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.13.0.tgz?cache=0&sync_timestamp=1614035622741&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-remap-async-to-generator%2Fdownload%2F%40babel%2Fhelper-remap-async-to-generator-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.12.13"
    "@babel/helper-wrap-function" "^7.13.0"
    "@babel/types" "^7.13.0"

"@babel/helper-replace-supers@^7.12.13", "@babel/helper-replace-supers@^7.13.12", "@babel/helper-replace-supers@^7.14.3":
  "integrity" "sha1-yhezGLhZ0Qfw6bci1YzxLZRDZgA="
  "resolved" "https://registry.nlark.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.14.3.tgz"
  "version" "7.14.3"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.13.12"
    "@babel/helper-optimise-call-expression" "^7.12.13"
    "@babel/traverse" "^7.14.2"
    "@babel/types" "^7.14.2"

"@babel/helper-simple-access@^7.13.12":
  "integrity" "sha1-3WxTivthgZ0gWgEsMXkqOcel6vY="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-simple-access/download/@babel/helper-simple-access-7.13.12.tgz?cache=0&sync_timestamp=1616428103561&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-simple-access%2Fdownload%2F%40babel%2Fhelper-simple-access-7.13.12.tgz"
  "version" "7.13.12"
  dependencies:
    "@babel/types" "^7.13.12"

"@babel/helper-skip-transparent-expression-wrappers@^7.12.1":
  "integrity" "sha1-Ri3GOn5DWt6EaDhcY9K4TM5LPL8="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.12.1.tgz"
  "version" "7.12.1"
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.14.5":
  "integrity" "sha1-lvSGrAUMqfRLAJ++W305TKs6DuQ="
  "resolved" "https://registry.nlark.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.14.5.tgz?cache=0&sync_timestamp=1623280361594&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-skip-transparent-expression-wrappers%2Fdownload%2F%40babel%2Fhelper-skip-transparent-expression-wrappers-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-split-export-declaration@^7.12.13":
  "integrity" "sha1-6UML4AuvPoiw4T5vnU6vITY3KwU="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.12.13.tgz?cache=0&sync_timestamp=1612314636310&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-split-export-declaration%2Fdownload%2F%40babel%2Fhelper-split-export-declaration-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/types" "^7.12.13"

"@babel/helper-validator-identifier@^7.12.11", "@babel/helper-validator-identifier@^7.14.0":
  "integrity" "sha1-0mytikfGUoaxXfFUcxml0Lzycog="
  "resolved" "https://registry.nlark.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.14.0.tgz?cache=0&sync_timestamp=1619727388937&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-validator-identifier%2Fdownload%2F%40babel%2Fhelper-validator-identifier-7.14.0.tgz"
  "version" "7.14.0"

"@babel/helper-validator-identifier@^7.14.9":
  "integrity" "sha1-ZlTRcbICT22O4VG/JQlpmRkTHUg="
  "resolved" "https://registry.nlark.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.14.9.tgz?cache=0&sync_timestamp=1627804408187&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-validator-identifier%2Fdownload%2F%40babel%2Fhelper-validator-identifier-7.14.9.tgz"
  "version" "7.14.9"

"@babel/helper-validator-option@^7.12.17":
  "integrity" "sha1-0fvwEuGnm37rv9xtJwuq+NnrmDE="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-validator-option/download/@babel/helper-validator-option-7.12.17.tgz?cache=0&sync_timestamp=1613662281943&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-validator-option%2Fdownload%2F%40babel%2Fhelper-validator-option-7.12.17.tgz"
  "version" "7.12.17"

"@babel/helper-wrap-function@^7.13.0":
  "integrity" "sha1-vbXGb9qFJuwjWriUrVOhI1x5/MQ="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-function-name" "^7.12.13"
    "@babel/template" "^7.12.13"
    "@babel/traverse" "^7.13.0"
    "@babel/types" "^7.13.0"

"@babel/helpers@^7.14.0":
  "integrity" "sha1-6ptr6UeKE9b5Ydu182v3Xi87j2I="
  "resolved" "https://registry.nlark.com/@babel/helpers/download/@babel/helpers-7.14.0.tgz?cache=0&sync_timestamp=1619727392870&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelpers%2Fdownload%2F%40babel%2Fhelpers-7.14.0.tgz"
  "version" "7.14.0"
  dependencies:
    "@babel/template" "^7.12.13"
    "@babel/traverse" "^7.14.0"
    "@babel/types" "^7.14.0"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.12.13":
  "integrity" "sha1-MZfjdXEe9r+DTmfQ2uyI5PRhE88="
  "resolved" "https://registry.nlark.com/@babel/highlight/download/@babel/highlight-7.14.0.tgz"
  "version" "7.14.0"
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.0"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.12.13", "@babel/parser@^7.14.2", "@babel/parser@^7.14.3":
  "integrity" "sha1-m1MO7LBx/QyTUZ3yXF/58UdZ8pg="
  "resolved" "https://registry.nlark.com/@babel/parser/download/@babel/parser-7.14.3.tgz?cache=0&sync_timestamp=1621286379821&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fparser%2Fdownload%2F%40babel%2Fparser-7.14.3.tgz"
  "version" "7.14.3"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.13.12":
  "integrity" "sha1-o0hNhNC1SfP8kWuZ7keD8m+rrSo="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.13.12.tgz"
  "version" "7.13.12"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.12.1"
    "@babel/plugin-proposal-optional-chaining" "^7.13.12"

"@babel/plugin-proposal-async-generator-functions@^7.14.2":
  "integrity" "sha1-OiCFq79dX5YtSA28gTRzhe1i6x4="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.14.2.tgz?cache=0&sync_timestamp=1620839583406&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-async-generator-functions%2Fdownload%2F%40babel%2Fplugin-proposal-async-generator-functions-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/helper-remap-async-to-generator" "^7.13.0"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.13.0", "@babel/plugin-proposal-class-properties@^7.8.3":
  "integrity" "sha1-FGN2AAuU79AB5XpAqIpSWvqrnzc="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.13.0.tgz?cache=0&sync_timestamp=1614035617977&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-class-properties%2Fdownload%2F%40babel%2Fplugin-proposal-class-properties-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.13.0"
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-proposal-class-static-block@^7.13.11":
  "integrity" "sha1-WlJ+LK5KR1MRnDo+f2TsrozPE2A="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-class-static-block/download/@babel/plugin-proposal-class-static-block-7.14.3.tgz"
  "version" "7.14.3"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.3"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-class-static-block" "^7.12.13"

"@babel/plugin-proposal-decorators@^7.8.3":
  "integrity" "sha1-5ow8XkpqCINEVlaCVvw+cbk1kM8="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.14.2.tgz?cache=0&sync_timestamp=1620840028217&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-decorators%2Fdownload%2F%40babel%2Fplugin-proposal-decorators-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.2"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-decorators" "^7.12.13"

"@babel/plugin-proposal-dynamic-import@^7.14.2":
  "integrity" "sha1-Aeur18OBz/Ix+kPjApOaneW+nZ8="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.14.2":
  "integrity" "sha1-YlQvlKqc6Pbbp57saYryIRIlN5E="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.14.2.tgz?cache=0&sync_timestamp=1620839583318&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-export-namespace-from%2Fdownload%2F%40babel%2Fplugin-proposal-export-namespace-from-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.14.2":
  "integrity" "sha1-gwtOJCanguiyh4+/4suoW3DL+Yw="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.14.2":
  "integrity" "sha1-IiNIwIChZ44OdOpj/nbydYgtH9c="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-logical-assignment-operators/download/@babel/plugin-proposal-logical-assignment-operators-7.14.2.tgz?cache=0&sync_timestamp=1620839583487&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-logical-assignment-operators%2Fdownload%2F%40babel%2Fplugin-proposal-logical-assignment-operators-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.14.2", "@babel/plugin-proposal-nullish-coalescing-operator@^7.14.5":
  "integrity" "sha1-7jhYnOAOLMWbKZ7D6kBvzToP2vY="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.14.2":
  "integrity" "sha1-grTMBlcRQ/r1BiYQSzNd1xuqT54="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.14.2.tgz?cache=0&sync_timestamp=1620839583606&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-numeric-separator%2Fdownload%2F%40babel%2Fplugin-proposal-numeric-separator-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.14.2":
  "integrity" "sha1-4X1Bj4HMED/t1M4DfhgcgFYiWrw="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/compat-data" "^7.14.0"
    "@babel/helper-compilation-targets" "^7.13.16"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.14.2"

"@babel/plugin-proposal-optional-catch-binding@^7.14.2":
  "integrity" "sha1-FQ1OWOUlsWqaFDG9UybE7thw1xc="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.13.12", "@babel/plugin-proposal-optional-chaining@^7.14.2", "@babel/plugin-proposal-optional-chaining@^7.14.5":
  "integrity" "sha1-+oNlHmCjYOPxN5fu8AuNUZaVtgM="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.14.5"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.13.0":
  "integrity" "sha1-BL1MbUD25rv6L1fi2AlLrZAO94c="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-private-methods/download/@babel/plugin-proposal-private-methods-7.13.0.tgz?cache=0&sync_timestamp=1614035636845&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-private-methods%2Fdownload%2F%40babel%2Fplugin-proposal-private-methods-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.13.0"
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-proposal-private-property-in-object@^7.14.0":
  "integrity" "sha1-saHyAwWGudNInMJhedLrWIMndjY="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.14.0.tgz"
  "version" "7.14.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.12.13"
    "@babel/helper-create-class-features-plugin" "^7.14.0"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.0"

"@babel/plugin-proposal-unicode-property-regex@^7.12.13", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha1-vr3lEzm+gpwXqqrO0YZB3rYrObo="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz?cache=0&sync_timestamp=1612314770269&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-class-properties%2Fdownload%2F%40babel%2Fplugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.12.13":
  "integrity" "sha1-jj1nSwYT5nl1zqwndsl7YMr8XJw="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-decorators@^7.12.13":
  "integrity" "sha1-+sgpvzx+9KG8kWJXtAPljGva9kg="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.12.13.tgz?cache=0&sync_timestamp=1612314770687&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-decorators%2Fdownload%2F%40babel%2Fplugin-syntax-decorators-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha1-AolkqbqA28CUyRXEh618TnpmRlo="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.8.3":
  "integrity" "sha1-BE+4HrrWaY/mLEeIdVdby7m3DxU="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.12.13.tgz?cache=0&sync_timestamp=1612314775704&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-jsx%2Fdownload%2F%40babel%2Fplugin-syntax-jsx-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha1-ypHvRjA1MESLkGZSusLp/plB9pk="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.0":
  "integrity" "sha1-dipLq+xhF2/sbIhIDexANysUDAs="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.0.tgz"
  "version" "7.14.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-syntax-top-level-await@^7.12.13":
  "integrity" "sha1-xfD6biSfW3OXJ/kjVAz3qAYTAXg="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.12.13.tgz?cache=0&sync_timestamp=1612314769908&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-top-level-await%2Fdownload%2F%40babel%2Fplugin-syntax-top-level-await-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-arrow-functions@^7.13.0":
  "integrity" "sha1-EKWb661S1jegJ6+mkujVzv9ePa4="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-transform-async-to-generator@^7.13.0":
  "integrity" "sha1-jhEr9ncbgr8el05eJoBsXJmqUW8="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.13.0.tgz?cache=0&sync_timestamp=1614035651263&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-async-to-generator%2Fdownload%2F%40babel%2Fplugin-transform-async-to-generator-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/helper-remap-async-to-generator" "^7.13.0"

"@babel/plugin-transform-block-scoped-functions@^7.12.13":
  "integrity" "sha1-qb8YNvKjm062zwmWdzneKepL9MQ="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.12.13.tgz?cache=0&sync_timestamp=1612314776735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-block-scoped-functions%2Fdownload%2F%40babel%2Fplugin-transform-block-scoped-functions-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-block-scoping@^7.14.2":
  "integrity" "sha1-dhyxKrWojWQK1K9KqB+CDmtf31w="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.14.2.tgz?cache=0&sync_timestamp=1620839583504&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-block-scoping%2Fdownload%2F%40babel%2Fplugin-transform-block-scoping-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-transform-classes@^7.14.2":
  "integrity" "sha1-PxGWxXCfBkwlKtBWIH2Ht66y0D0="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.14.2.tgz?cache=0&sync_timestamp=1620840027682&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-classes%2Fdownload%2F%40babel%2Fplugin-transform-classes-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.12.13"
    "@babel/helper-function-name" "^7.14.2"
    "@babel/helper-optimise-call-expression" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/helper-replace-supers" "^7.13.12"
    "@babel/helper-split-export-declaration" "^7.12.13"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.13.0":
  "integrity" "sha1-hFxui5u1U3ax+guS7wvcjqBmRO0="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-transform-destructuring@^7.13.17":
  "integrity" "sha1-Z42WV2Y4wZ1bNrMyUE0/1uBt6ic="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.13.17.tgz?cache=0&sync_timestamp=1618960864725&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-destructuring%2Fdownload%2F%40babel%2Fplugin-transform-destructuring-7.13.17.tgz"
  "version" "7.13.17"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-transform-dotall-regex@^7.12.13", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha1-PxYBzCmQW/y2f1ORDxl66v67Ja0="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-duplicate-keys@^7.12.13":
  "integrity" "sha1-bwa4eouAP9ko5UuBwljwoAM5BN4="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-exponentiation-operator@^7.12.13":
  "integrity" "sha1-TVI5C5onPmUeSrpq7knvQOgM0KE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-for-of@^7.13.0":
  "integrity" "sha1-x5n4gagJGsJrVIZ6hFw+l9JpYGI="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-transform-function-name@^7.12.13":
  "integrity" "sha1-uwJEUvmq7YYdN0yOeiQlLOOlAFE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-function-name" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-literals@^7.12.13":
  "integrity" "sha1-LKRbr+SoIBl88xV5Sk0mVg/kvbk="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.12.13.tgz?cache=0&sync_timestamp=1612314767825&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-literals%2Fdownload%2F%40babel%2Fplugin-transform-literals-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-member-expression-literals@^7.12.13":
  "integrity" "sha1-X/pmzVm54ZExTJ8fgDuTjowIHkA="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.12.13.tgz?cache=0&sync_timestamp=1612314777089&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-member-expression-literals%2Fdownload%2F%40babel%2Fplugin-transform-member-expression-literals-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-modules-amd@^7.14.2":
  "integrity" "sha1-ZiKAb+GnwHoTiERCIu+VNfLKF7A="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.14.2.tgz?cache=0&sync_timestamp=1620839583679&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-modules-amd%2Fdownload%2F%40babel%2Fplugin-transform-modules-amd-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-module-transforms" "^7.14.2"
    "@babel/helper-plugin-utils" "^7.13.0"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.14.0":
  "integrity" "sha1-UrwZnLWB4Jku26Dw+ANWRnWH8WE="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.14.0.tgz?cache=0&sync_timestamp=1619727039991&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-modules-commonjs%2Fdownload%2F%40babel%2Fplugin-transform-modules-commonjs-7.14.0.tgz"
  "version" "7.14.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.14.0"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/helper-simple-access" "^7.13.12"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.13.8":
  "integrity" "sha1-bQZu4r/zx7PWC/KN7Baa2ZODGuM="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.13.8.tgz"
  "version" "7.13.8"
  dependencies:
    "@babel/helper-hoist-variables" "^7.13.0"
    "@babel/helper-module-transforms" "^7.13.0"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/helper-validator-identifier" "^7.12.11"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.14.0":
  "integrity" "sha1-L4F50bvJJjZlzkpl8wVSay6orDQ="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.14.0.tgz?cache=0&sync_timestamp=1619727039991&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-modules-umd%2Fdownload%2F%40babel%2Fplugin-transform-modules-umd-7.14.0.tgz"
  "version" "7.14.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.14.0"
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-transform-named-capturing-groups-regex@^7.12.13":
  "integrity" "sha1-IhNyWl9bu+NktQw7pZmMlZnFydk="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.13"

"@babel/plugin-transform-new-target@^7.12.13":
  "integrity" "sha1-4i2MOvJLFQ3VKMvW5oXnmb8cNRw="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.12.13.tgz?cache=0&sync_timestamp=1612314768286&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-new-target%2Fdownload%2F%40babel%2Fplugin-transform-new-target-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-object-super@^7.12.13":
  "integrity" "sha1-tEFqLWO4974xTz00m9VanBtRcfc="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"
    "@babel/helper-replace-supers" "^7.12.13"

"@babel/plugin-transform-parameters@^7.14.2":
  "integrity" "sha1-5CkPcuDp6DEADQZkJ8RmcJjezDE="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.14.2.tgz?cache=0&sync_timestamp=1620839583383&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-parameters%2Fdownload%2F%40babel%2Fplugin-transform-parameters-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-transform-property-literals@^7.12.13":
  "integrity" "sha1-TmqeN4ZNjxs7wOLc57+IV9uLGoE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.12.13.tgz?cache=0&sync_timestamp=1612314768626&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-property-literals%2Fdownload%2F%40babel%2Fplugin-transform-property-literals-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-regenerator@^7.13.15":
  "integrity" "sha1-5esolFv4tlY+f4GJRflmqNKZfzk="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.13.15.tgz?cache=0&sync_timestamp=1617897172824&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-regenerator%2Fdownload%2F%40babel%2Fplugin-transform-regenerator-7.13.15.tgz"
  "version" "7.13.15"
  dependencies:
    "regenerator-transform" "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.12.13":
  "integrity" "sha1-fZmI1PBuD+aX6h2YAxiKoYtHJpU="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.12.13.tgz?cache=0&sync_timestamp=1612314781130&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-reserved-words%2Fdownload%2F%40babel%2Fplugin-transform-reserved-words-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-runtime@^7.11.0":
  "integrity" "sha1-H9iFotDeHTwiN5Wk6b5ywttFFc8="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.14.3.tgz"
  "version" "7.14.3"
  dependencies:
    "@babel/helper-module-imports" "^7.13.12"
    "@babel/helper-plugin-utils" "^7.13.0"
    "babel-plugin-polyfill-corejs2" "^0.2.0"
    "babel-plugin-polyfill-corejs3" "^0.2.0"
    "babel-plugin-polyfill-regenerator" "^0.2.0"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.12.13":
  "integrity" "sha1-23VXMrcMU51QTGOQ2c6Q/mSv960="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-spread@^7.13.0":
  "integrity" "sha1-hIh3EOJzwYFaznrkWfb0Kl0x1f0="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.12.1"

"@babel/plugin-transform-sticky-regex@^7.12.13":
  "integrity" "sha1-dg/9k2+s5z+GCuZG+4bugvPQbR8="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-template-literals@^7.13.0":
  "integrity" "sha1-o2BJEnl3rZRDje50Q1mNHO/fQJ0="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.13.0.tgz"
  "version" "7.13.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.13.0"

"@babel/plugin-transform-typeof-symbol@^7.12.13":
  "integrity" "sha1-eF3Weh8upXnZwr5yLejITLhfWn8="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-unicode-escapes@^7.12.13":
  "integrity" "sha1-hAztO4FtO1En3R0S3O3F3q0aXnQ="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.12.13.tgz?cache=0&sync_timestamp=1612314780432&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-unicode-escapes%2Fdownload%2F%40babel%2Fplugin-transform-unicode-escapes-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-transform-unicode-regex@^7.12.13":
  "integrity" "sha1-tSUhaFgE4VWxIC6D/BiNNLtw9aw="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/preset-env@^7.11.0":
  "integrity" "sha1-6AYSll2nNXnIStL5Y8I1nHFSTtU="
  "resolved" "https://registry.nlark.com/@babel/preset-env/download/@babel/preset-env-7.14.2.tgz?cache=0&sync_timestamp=1620839457830&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fpreset-env%2Fdownload%2F%40babel%2Fpreset-env-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/compat-data" "^7.14.0"
    "@babel/helper-compilation-targets" "^7.13.16"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/helper-validator-option" "^7.12.17"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.13.12"
    "@babel/plugin-proposal-async-generator-functions" "^7.14.2"
    "@babel/plugin-proposal-class-properties" "^7.13.0"
    "@babel/plugin-proposal-class-static-block" "^7.13.11"
    "@babel/plugin-proposal-dynamic-import" "^7.14.2"
    "@babel/plugin-proposal-export-namespace-from" "^7.14.2"
    "@babel/plugin-proposal-json-strings" "^7.14.2"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.14.2"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.14.2"
    "@babel/plugin-proposal-numeric-separator" "^7.14.2"
    "@babel/plugin-proposal-object-rest-spread" "^7.14.2"
    "@babel/plugin-proposal-optional-catch-binding" "^7.14.2"
    "@babel/plugin-proposal-optional-chaining" "^7.14.2"
    "@babel/plugin-proposal-private-methods" "^7.13.0"
    "@babel/plugin-proposal-private-property-in-object" "^7.14.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.12.13"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.12.13"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.0"
    "@babel/plugin-syntax-top-level-await" "^7.12.13"
    "@babel/plugin-transform-arrow-functions" "^7.13.0"
    "@babel/plugin-transform-async-to-generator" "^7.13.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.12.13"
    "@babel/plugin-transform-block-scoping" "^7.14.2"
    "@babel/plugin-transform-classes" "^7.14.2"
    "@babel/plugin-transform-computed-properties" "^7.13.0"
    "@babel/plugin-transform-destructuring" "^7.13.17"
    "@babel/plugin-transform-dotall-regex" "^7.12.13"
    "@babel/plugin-transform-duplicate-keys" "^7.12.13"
    "@babel/plugin-transform-exponentiation-operator" "^7.12.13"
    "@babel/plugin-transform-for-of" "^7.13.0"
    "@babel/plugin-transform-function-name" "^7.12.13"
    "@babel/plugin-transform-literals" "^7.12.13"
    "@babel/plugin-transform-member-expression-literals" "^7.12.13"
    "@babel/plugin-transform-modules-amd" "^7.14.2"
    "@babel/plugin-transform-modules-commonjs" "^7.14.0"
    "@babel/plugin-transform-modules-systemjs" "^7.13.8"
    "@babel/plugin-transform-modules-umd" "^7.14.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.12.13"
    "@babel/plugin-transform-new-target" "^7.12.13"
    "@babel/plugin-transform-object-super" "^7.12.13"
    "@babel/plugin-transform-parameters" "^7.14.2"
    "@babel/plugin-transform-property-literals" "^7.12.13"
    "@babel/plugin-transform-regenerator" "^7.13.15"
    "@babel/plugin-transform-reserved-words" "^7.12.13"
    "@babel/plugin-transform-shorthand-properties" "^7.12.13"
    "@babel/plugin-transform-spread" "^7.13.0"
    "@babel/plugin-transform-sticky-regex" "^7.12.13"
    "@babel/plugin-transform-template-literals" "^7.13.0"
    "@babel/plugin-transform-typeof-symbol" "^7.12.13"
    "@babel/plugin-transform-unicode-escapes" "^7.12.13"
    "@babel/plugin-transform-unicode-regex" "^7.12.13"
    "@babel/preset-modules" "^0.1.4"
    "@babel/types" "^7.14.2"
    "babel-plugin-polyfill-corejs2" "^0.2.0"
    "babel-plugin-polyfill-corejs3" "^0.2.0"
    "babel-plugin-polyfill-regenerator" "^0.2.0"
    "core-js-compat" "^3.9.0"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.4":
  "integrity" "sha1-Ni8raMZihClw/bXiVP/I/BwuQV4="
  "resolved" "https://registry.npm.taobao.org/@babel/preset-modules/download/@babel/preset-modules-0.1.4.tgz?cache=0&sync_timestamp=1598549645892&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fpreset-modules%2Fdownload%2F%40babel%2Fpreset-modules-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.11.0", "@babel/runtime@^7.8.4", "@babel/runtime@7.x":
  "integrity" "sha1-RnlLwgthLF915i3QceJN/ZXxy+Y="
  "resolved" "https://registry.nlark.com/@babel/runtime/download/@babel/runtime-7.14.0.tgz?cache=0&sync_timestamp=1619727389508&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.14.0.tgz"
  "version" "7.14.0"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/template@^7.0.0", "@babel/template@^7.12.13":
  "integrity" "sha1-UwJlvooliduzdSOETFvLVZR/syc="
  "resolved" "https://registry.npm.taobao.org/@babel/template/download/@babel/template-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@babel/parser" "^7.12.13"
    "@babel/types" "^7.12.13"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.0.0-beta.40", "@babel/traverse@^7.13.0", "@babel/traverse@^7.13.15", "@babel/traverse@^7.14.0", "@babel/traverse@^7.14.2":
  "integrity" "sha1-kgGo2RJyOoMcJnnH678v4UFtdls="
  "resolved" "https://registry.nlark.com/@babel/traverse/download/@babel/traverse-7.14.2.tgz?cache=0&sync_timestamp=1620839457171&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@babel/generator" "^7.14.2"
    "@babel/helper-function-name" "^7.14.2"
    "@babel/helper-split-export-declaration" "^7.12.13"
    "@babel/parser" "^7.14.2"
    "@babel/types" "^7.14.2"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.0.0-beta.40", "@babel/types@^7.12.1", "@babel/types@^7.12.13", "@babel/types@^7.13.0", "@babel/types@^7.13.12", "@babel/types@^7.13.16", "@babel/types@^7.14.0", "@babel/types@^7.14.2", "@babel/types@^7.4.4":
  "integrity" "sha1-QgiuADEH74oFfqgzPlbrZNL2osM="
  "resolved" "https://registry.nlark.com/@babel/types/download/@babel/types-7.14.2.tgz?cache=0&sync_timestamp=1620839445517&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.0"
    "to-fast-properties" "^2.0.0"

"@babel/types@^7.14.5":
  "integrity" "sha1-Ya8R8ihsTpxpyo3rX0N1pzxy3L0="
  "resolved" "https://registry.nlark.com/@babel/types/download/@babel/types-7.15.0.tgz?cache=0&sync_timestamp=1628111608723&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.15.0.tgz"
  "version" "7.15.0"
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.9"
    "to-fast-properties" "^2.0.0"

"@babel/types@7.0.0-beta.35":
  "integrity" "sha1-z5M6mpo4SEynJLM1uI2Dcm1auWA="
  "resolved" "https://registry.nlark.com/@babel/types/download/@babel/types-7.0.0-beta.35.tgz"
  "version" "7.0.0-beta.35"
  dependencies:
    "esutils" "^2.0.2"
    "lodash" "^4.2.0"
    "to-fast-properties" "^2.0.0"

"@eslint/eslintrc@^0.4.3":
  "integrity" "sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw="
  "resolved" "https://registry.nlark.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz?cache=0&sync_timestamp=1628296487039&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40eslint%2Feslintrc%2Fdownload%2F%40eslint%2Feslintrc-0.4.3.tgz"
  "version" "0.4.3"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.1.1"
    "espree" "^7.3.0"
    "globals" "^13.9.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.2.1"
    "js-yaml" "^3.13.1"
    "minimatch" "^3.0.4"
    "strip-json-comments" "^3.1.1"

"@hapi/address@2.x.x":
  "integrity" "sha1-XWftQ/P9QaadS5/3tW58DR0KgeU="
  "resolved" "https://registry.npm.taobao.org/@hapi/address/download/@hapi/address-2.1.4.tgz"
  "version" "2.1.4"

"@hapi/bourne@1.x.x":
  "integrity" "sha1-CnCVreoGckPOMoPhtWuKj0U7JCo="
  "resolved" "https://registry.npm.taobao.org/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz"
  "version" "1.3.2"

"@hapi/hoek@^8.3.0", "@hapi/hoek@8.x.x":
  "integrity" "sha1-/elgZMpEbeyMVajC8TCVewcMbgY="
  "resolved" "https://registry.npm.taobao.org/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz?cache=0&sync_timestamp=1618694154257&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hapi%2Fhoek%2Fdownload%2F%40hapi%2Fhoek-8.5.1.tgz"
  "version" "8.5.1"

"@hapi/joi@^15.0.1":
  "integrity" "sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc="
  "resolved" "https://registry.npm.taobao.org/@hapi/joi/download/@hapi/joi-15.1.1.tgz"
  "version" "15.1.1"
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  "integrity" "sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck="
  "resolved" "https://registry.npm.taobao.org/@hapi/topo/download/@hapi/topo-3.1.6.tgz?cache=0&sync_timestamp=1593916080558&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hapi%2Ftopo%2Fdownload%2F%40hapi%2Ftopo-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@humanwhocodes/config-array@^0.5.0":
  "integrity" "sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk="
  "resolved" "https://registry.nlark.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz?cache=0&sync_timestamp=1625264021699&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40humanwhocodes%2Fconfig-array%2Fdownload%2F%40humanwhocodes%2Fconfig-array-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    "debug" "^4.1.1"
    "minimatch" "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  "integrity" "sha1-h956+cIxgm/daKxyWPd8Qp4OX88="
  "resolved" "https://registry.nlark.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.0.tgz?cache=0&sync_timestamp=1625264051240&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40humanwhocodes%2Fobject-schema%2Fdownload%2F%40humanwhocodes%2Fobject-schema-1.2.0.tgz"
  "version" "1.2.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  "integrity" "sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg="
  "resolved" "https://registry.npm.taobao.org/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "cssnano" "^4.0.0"
    "cssnano-preset-default" "^4.0.0"
    "postcss" "^7.0.0"

"@mrmlnc/readdir-enhanced@^2.2.1":
  "integrity" "sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4="
  "resolved" "https://registry.npm.taobao.org/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "call-me-maybe" "^1.0.1"
    "glob-to-regexp" "^0.3.0"

"@nodelib/fs.stat@^1.1.2":
  "integrity" "sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs="
  "resolved" "https://registry.npm.taobao.org/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz?cache=0&sync_timestamp=1609076382094&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40nodelib%2Ffs.stat%2Fdownload%2F%40nodelib%2Ffs.stat-1.1.3.tgz"
  "version" "1.1.3"

"@popperjs/core@^2.9.2":
  "integrity" "sha1-rep7aVPLs0ZRdmsFSEaOdDxqI1M="
  "resolved" "https://registry.npm.taobao.org/@popperjs/core/download/@popperjs/core-2.9.2.tgz?cache=0&sync_timestamp=1617291042068&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40popperjs%2Fcore%2Fdownload%2F%40popperjs%2Fcore-2.9.2.tgz"
  "version" "2.9.2"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  "integrity" "sha1-hHUdgqkwGdXJLAzw5FrFkIfNIkA="
  "resolved" "https://registry.npm.taobao.org/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.8.0.tgz?cache=0&sync_timestamp=1607927418007&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40soda%2Ffriendly-errors-webpack-plugin%2Fdownload%2F%40soda%2Ffriendly-errors-webpack-plugin-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "chalk" "^2.4.2"
    "error-stack-parser" "^2.0.2"
    "string-width" "^2.0.0"
    "strip-ansi" "^5"

"@soda/get-current-script@^1.0.0":
  "integrity" "sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc="
  "resolved" "https://registry.npm.taobao.org/@soda/get-current-script/download/@soda/get-current-script-1.0.2.tgz"
  "version" "1.0.2"

"@types/body-parser@*":
  "integrity" "sha1-BoWzxH6zAG/+0RfN1VFkth+AU48="
  "resolved" "https://registry.nlark.com/@types/body-parser/download/@types/body-parser-1.19.0.tgz?cache=0&sync_timestamp=1621240672784&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fbody-parser%2Fdownload%2F%40types%2Fbody-parser-1.19.0.tgz"
  "version" "1.19.0"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect-history-api-fallback@*":
  "integrity" "sha1-jA8Obl2CUraZ9aZi9Rvfgv2di7g="
  "resolved" "https://registry.nlark.com/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.3.4.tgz?cache=0&sync_timestamp=1621240807633&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fconnect-history-api-fallback%2Fdownload%2F%40types%2Fconnect-history-api-fallback-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  "integrity" "sha1-FwpAIjptZmAG2TyhKK8r6x2bGQE="
  "resolved" "https://registry.nlark.com/@types/connect/download/@types/connect-3.4.34.tgz"
  "version" "3.4.34"
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  "integrity" "sha1-AKz8FjLnKaysTxUw6eFvbdFQih0="
  "resolved" "https://registry.nlark.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.19.tgz?cache=0&sync_timestamp=1621241002664&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fexpress-serve-static-core%2Fdownload%2F%40types%2Fexpress-serve-static-core-4.17.19.tgz"
  "version" "4.17.19"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  "integrity" "sha1-3r48qm+OX82pa0e9VOL0DE7llUU="
  "resolved" "https://registry.nlark.com/@types/express/download/@types/express-4.17.11.tgz?cache=0&sync_timestamp=1621240982576&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fexpress%2Fdownload%2F%40types%2Fexpress-4.17.11.tgz"
  "version" "4.17.11"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/glob@^7.1.1":
  "integrity" "sha1-5rqA82t9qtLGhazZJmOC5omFwYM="
  "resolved" "https://registry.nlark.com/@types/glob/download/@types/glob-7.1.3.tgz"
  "version" "7.1.3"
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/http-proxy@^1.17.5":
  "integrity" "sha1-Ytw/reIn1qwoYsjxnuDanan9hhY="
  "resolved" "https://registry.nlark.com/@types/http-proxy/download/@types/http-proxy-1.17.6.tgz?cache=0&sync_timestamp=1621034259660&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fhttp-proxy%2Fdownload%2F%40types%2Fhttp-proxy-1.17.6.tgz"
  "version" "1.17.6"
  dependencies:
    "@types/node" "*"

"@types/json-schema@^7.0.5":
  "integrity" "sha1-mKmTUWyFnrDVxMjwmDF6nqaNua0="
  "resolved" "https://registry.nlark.com/@types/json-schema/download/@types/json-schema-7.0.7.tgz"
  "version" "7.0.7"

"@types/json-schema@^7.0.8":
  "integrity" "sha512-qcUXuemtEu+E5wZSJHNxUXeCZhAfXKQ41D+duX+VYPde7xyEVZci+/oXKJL13tnRs9lR2pr4fod59GT6/X1/yQ=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz"
  "version" "7.0.9"

"@types/mime@^1":
  "integrity" "sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o="
  "resolved" "https://registry.nlark.com/@types/mime/download/@types/mime-1.3.2.tgz"
  "version" "1.3.2"

"@types/minimatch@*":
  "integrity" "sha1-8Owl2/Lw5LGGRzE6wDETTKWySyE="
  "resolved" "https://registry.nlark.com/@types/minimatch/download/@types/minimatch-3.0.4.tgz"
  "version" "3.0.4"

"@types/minimist@^1.2.0":
  "integrity" "sha1-KD9mn/dte4Jg34q3pCYsyD2YglY="
  "resolved" "https://registry.nlark.com/@types/minimist/download/@types/minimist-1.2.1.tgz"
  "version" "1.2.1"

"@types/node@*":
  "integrity" "sha1-8N3KWmHlJifJ3LdxpgOdRGlFl7w="
  "resolved" "https://registry.nlark.com/@types/node/download/@types/node-15.6.0.tgz?cache=0&sync_timestamp=1621593402962&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-15.6.0.tgz"
  "version" "15.6.0"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4="
  "resolved" "https://registry.nlark.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz?cache=0&sync_timestamp=1621242064742&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fnormalize-package-data%2Fdownload%2F%40types%2Fnormalize-package-data-2.4.0.tgz"
  "version" "2.4.0"

"@types/parse-json@^4.0.0":
  "integrity" "sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA="
  "resolved" "https://registry.nlark.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz"
  "version" "4.0.0"

"@types/q@^1.5.1":
  "integrity" "sha1-FZJUFOCtLNdlv+9YhC9+JqesyyQ="
  "resolved" "https://registry.nlark.com/@types/q/download/@types/q-1.5.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fq%2Fdownload%2F%40types%2Fq-1.5.4.tgz"
  "version" "1.5.4"

"@types/qs@*":
  "integrity" "sha1-35w8izGiR+wxXmmWVmvjFx30s7E="
  "resolved" "https://registry.nlark.com/@types/qs/download/@types/qs-6.9.6.tgz?cache=0&sync_timestamp=1621242292262&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fqs%2Fdownload%2F%40types%2Fqs-6.9.6.tgz"
  "version" "6.9.6"

"@types/range-parser@*":
  "integrity" "sha1-fuMwunyq+5gJC+zoal7kQRWQTCw="
  "resolved" "https://registry.nlark.com/@types/range-parser/download/@types/range-parser-1.2.3.tgz?cache=0&sync_timestamp=1621242291785&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Frange-parser%2Fdownload%2F%40types%2Frange-parser-1.2.3.tgz"
  "version" "1.2.3"

"@types/serve-static@*":
  "integrity" "sha1-qs8oqFoF7imhH7fD6tk1rFbzPk4="
  "resolved" "https://registry.nlark.com/@types/serve-static/download/@types/serve-static-1.13.9.tgz?cache=0&sync_timestamp=1621242704905&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fserve-static%2Fdownload%2F%40types%2Fserve-static-1.13.9.tgz"
  "version" "1.13.9"
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/source-list-map@*":
  "integrity" "sha1-AHiDYGP/rxdBI0m7o2QIfgrALsk="
  "resolved" "https://registry.nlark.com/@types/source-list-map/download/@types/source-list-map-0.1.2.tgz"
  "version" "0.1.2"

"@types/tapable@^1":
  "integrity" "sha1-VFFYNC+Uno/Tv9gTIklx7N3D+sQ="
  "resolved" "https://registry.nlark.com/@types/tapable/download/@types/tapable-1.0.7.tgz"
  "version" "1.0.7"

"@types/uglify-js@*":
  "integrity" "sha1-HK2N8fsLFDxaugjeVxLqnR/3ESQ="
  "resolved" "https://registry.nlark.com/@types/uglify-js/download/@types/uglify-js-3.13.0.tgz"
  "version" "3.13.0"
  dependencies:
    "source-map" "^0.6.1"

"@types/webpack-dev-server@^3.11.0":
  "integrity" "sha1-kNR91mC2ltQJQxq4wen6NhUQOgc="
  "resolved" "https://registry.nlark.com/@types/webpack-dev-server/download/@types/webpack-dev-server-3.11.4.tgz?cache=0&sync_timestamp=1621243863291&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fwebpack-dev-server%2Fdownload%2F%40types%2Fwebpack-dev-server-3.11.4.tgz"
  "version" "3.11.4"
  dependencies:
    "@types/connect-history-api-fallback" "*"
    "@types/express" "*"
    "@types/serve-static" "*"
    "@types/webpack" "^4"
    "http-proxy-middleware" "^1.0.0"

"@types/webpack-sources@*":
  "integrity" "sha1-iIKwvWLR4M5i8YPQ0Bty5ugujBA="
  "resolved" "https://registry.nlark.com/@types/webpack-sources/download/@types/webpack-sources-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    "source-map" "^0.7.3"

"@types/webpack@^4", "@types/webpack@^4.0.0":
  "integrity" "sha1-LmbB3oIjxEA2ZGlBXFCkfZdiV3M="
  "resolved" "https://registry.nlark.com/@types/webpack/download/@types/webpack-4.41.29.tgz?cache=0&sync_timestamp=1621535470280&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fwebpack%2Fdownload%2F%40types%2Fwebpack-4.41.29.tgz"
  "version" "4.41.29"
  dependencies:
    "@types/node" "*"
    "@types/tapable" "^1"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    "anymatch" "^3.0.0"
    "source-map" "^0.6.0"

"@vant/area-data@^1.1.1":
  "integrity" "sha512-MmXTuSh6YYWSvC112iK2/rZhEfe+3vfh6G9TD1sOTOrtw+H9bAnw8ZOiHdkL6hTUCXV+AJEDbFyFsNLpJxqkoA=="
  "resolved" "https://registry.npmjs.org/@vant/area-data/-/area-data-1.1.1.tgz"
  "version" "1.1.1"

"@vant/icons@^1.5.3":
  "integrity" "sha1-Pbfrf5Y/UaKghnZyDVr5xMNRL+s="
  "resolved" "https://registry.nlark.com/@vant/icons/download/@vant/icons-1.6.0.tgz?cache=0&sync_timestamp=1621321922501&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40vant%2Ficons%2Fdownload%2F%40vant%2Ficons-1.6.0.tgz"
  "version" "1.6.0"

"@vant/popperjs@^1.0.0":
  "integrity" "sha1-tO3uW7+m+xhwWYbjE9T9XxeUKg8="
  "resolved" "https://registry.npm.taobao.org/@vant/popperjs/download/@vant/popperjs-1.1.0.tgz?cache=0&sync_timestamp=1617713442527&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vant%2Fpopperjs%2Fdownload%2F%40vant%2Fpopperjs-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@popperjs/core" "^2.9.2"

"@vue/babel-helper-vue-jsx-merge-props@^1.0.0", "@vue/babel-helper-vue-jsx-merge-props@^1.2.1":
  "integrity" "sha1-MWJKelBfsU2h1YAjclpMXycOaoE="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.2.1.tgz?cache=0&sync_timestamp=1602851135129&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-helper-vue-jsx-merge-props%2Fdownload%2F%40vue%2Fbabel-helper-vue-jsx-merge-props-1.2.1.tgz"
  "version" "1.2.1"

"@vue/babel-helper-vue-transform-on@^1.0.2":
  "integrity" "sha1-m5xpHNBvyFUiGiR1w8yDHXdLx9w="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.0.2.tgz"
  "version" "1.0.2"

"@vue/babel-plugin-jsx@^1.0.3":
  "integrity" "sha1-GEvzVBq279vlB5q4sgwZ4q8QC/s="
  "resolved" "https://registry.nlark.com/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "@vue/babel-helper-vue-transform-on" "^1.0.2"
    "camelcase" "^6.0.0"
    "html-tags" "^3.1.0"
    "svg-tags" "^1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.2.1":
  "integrity" "sha1-ZGBGxlLC8CQnJ/NFGdkXsGQEHtc="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "html-tags" "^2.0.0"
    "lodash.kebabcase" "^4.1.1"
    "svg-tags" "^1.0.0"

"@vue/babel-preset-app@^4.5.13":
  "integrity" "sha1-y0dTIeTHP38RDawppIwqnLgK/rY="
  "resolved" "https://registry.nlark.com/@vue/babel-preset-app/download/@vue/babel-preset-app-4.5.13.tgz?cache=0&sync_timestamp=1620981723230&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40vue%2Fbabel-preset-app%2Fdownload%2F%40vue%2Fbabel-preset-app-4.5.13.tgz"
  "version" "4.5.13"
  dependencies:
    "@babel/core" "^7.11.0"
    "@babel/helper-compilation-targets" "^7.9.6"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/plugin-proposal-class-properties" "^7.8.3"
    "@babel/plugin-proposal-decorators" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.8.3"
    "@babel/plugin-transform-runtime" "^7.11.0"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.0"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.2.4"
    "babel-plugin-dynamic-import-node" "^2.3.3"
    "core-js" "^3.6.5"
    "core-js-compat" "^3.6.5"
    "semver" "^6.1.0"

"@vue/babel-preset-jsx@^1.2.4":
  "integrity" "sha1-kv6nnbbxOwHoDToAmeKSS9y+Toc="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.2.4.tgz?cache=0&sync_timestamp=1603806812399&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-preset-jsx%2Fdownload%2F%40vue%2Fbabel-preset-jsx-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    "@vue/babel-sugar-composition-api-inject-h" "^1.2.1"
    "@vue/babel-sugar-composition-api-render-instance" "^1.2.4"
    "@vue/babel-sugar-functional-vue" "^1.2.2"
    "@vue/babel-sugar-inject-h" "^1.2.2"
    "@vue/babel-sugar-v-model" "^1.2.3"
    "@vue/babel-sugar-v-on" "^1.2.3"

"@vue/babel-sugar-composition-api-inject-h@^1.2.1":
  "integrity" "sha1-BdbgxDJxDjdYKyvppgSbaJtvA+s="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-composition-api-inject-h/download/@vue/babel-sugar-composition-api-inject-h-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.2.4":
  "integrity" "sha1-5MvGmXw0T6wnF4WteikyXFHWjRk="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-composition-api-render-instance/download/@vue/babel-sugar-composition-api-render-instance-1.2.4.tgz?cache=0&sync_timestamp=1603806817702&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-composition-api-render-instance%2Fdownload%2F%40vue%2Fbabel-sugar-composition-api-render-instance-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.2.2":
  "integrity" "sha1-JnqayNeHyW7b8Dzj85LEnam9Jlg="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.2.2.tgz?cache=0&sync_timestamp=1602929602326&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-functional-vue%2Fdownload%2F%40vue%2Fbabel-sugar-functional-vue-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.2.2":
  "integrity" "sha1-1zjTyJM2fshJHcu2abAAkZKT46o="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.2.3":
  "integrity" "sha1-+h8pulHr8KoabDX6ZtU5vEWaGPI="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    "camelcase" "^5.0.0"
    "html-tags" "^2.0.0"
    "svg-tags" "^1.0.0"

"@vue/babel-sugar-v-on@^1.2.3":
  "integrity" "sha1-NCNnF4WGpp85LwS/ujICHQKROto="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.2.3.tgz?cache=0&sync_timestamp=1603181829700&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-v-on%2Fdownload%2F%40vue%2Fbabel-sugar-v-on-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    "camelcase" "^5.0.0"

"@vue/cli-overlay@^4.5.13":
  "integrity" "sha1-Tx/SFhvo9p1suoB58/DX3E3uR6c="
  "resolved" "https://registry.nlark.com/@vue/cli-overlay/download/@vue/cli-overlay-4.5.13.tgz"
  "version" "4.5.13"

"@vue/cli-plugin-babel@^4.1.1":
  "integrity" "sha1-qJxILtzE6h0TVkXOxQKn9f1MMOc="
  "resolved" "https://registry.nlark.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-4.5.13.tgz"
  "version" "4.5.13"
  dependencies:
    "@babel/core" "^7.11.0"
    "@vue/babel-preset-app" "^4.5.13"
    "@vue/cli-shared-utils" "^4.5.13"
    "babel-loader" "^8.1.0"
    "cache-loader" "^4.1.0"
    "thread-loader" "^2.1.3"
    "webpack" "^4.0.0"

"@vue/cli-plugin-eslint@^4.1.1":
  "integrity" "sha1-i68i0NltdnIMdQZka5b09iwFvfo="
  "resolved" "https://registry.nlark.com/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-4.5.13.tgz"
  "version" "4.5.13"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.13"
    "eslint-loader" "^2.2.1"
    "globby" "^9.2.0"
    "inquirer" "^7.1.0"
    "webpack" "^4.0.0"
    "yorkie" "^2.0.0"

"@vue/cli-plugin-router@^4.5.13":
  "integrity" "sha1-C2fIiYor8TKUGRmiouXzqsvZ/74="
  "resolved" "https://registry.nlark.com/@vue/cli-plugin-router/download/@vue/cli-plugin-router-4.5.13.tgz"
  "version" "4.5.13"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.13"

"@vue/cli-plugin-vuex@^4.5.13":
  "integrity" "sha1-mGRti8HmnPbGpsui/tPqzgNWw2A="
  "resolved" "https://registry.nlark.com/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-4.5.13.tgz"
  "version" "4.5.13"

"@vue/cli-service@^4.1.1":
  "integrity" "sha1-oJ5oSoAWhLbiTlQUrTBlCXDuye0="
  "resolved" "https://registry.nlark.com/@vue/cli-service/download/@vue/cli-service-4.5.13.tgz?cache=0&sync_timestamp=1620981723722&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40vue%2Fcli-service%2Fdownload%2F%40vue%2Fcli-service-4.5.13.tgz"
  "version" "4.5.13"
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@soda/get-current-script" "^1.0.0"
    "@types/minimist" "^1.2.0"
    "@types/webpack" "^4.0.0"
    "@types/webpack-dev-server" "^3.11.0"
    "@vue/cli-overlay" "^4.5.13"
    "@vue/cli-plugin-router" "^4.5.13"
    "@vue/cli-plugin-vuex" "^4.5.13"
    "@vue/cli-shared-utils" "^4.5.13"
    "@vue/component-compiler-utils" "^3.1.2"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    "acorn" "^7.4.0"
    "acorn-walk" "^7.1.1"
    "address" "^1.1.2"
    "autoprefixer" "^9.8.6"
    "browserslist" "^4.12.0"
    "cache-loader" "^4.1.0"
    "case-sensitive-paths-webpack-plugin" "^2.3.0"
    "cli-highlight" "^2.1.4"
    "clipboardy" "^2.3.0"
    "cliui" "^6.0.0"
    "copy-webpack-plugin" "^5.1.1"
    "css-loader" "^3.5.3"
    "cssnano" "^4.1.10"
    "debug" "^4.1.1"
    "default-gateway" "^5.0.5"
    "dotenv" "^8.2.0"
    "dotenv-expand" "^5.1.0"
    "file-loader" "^4.2.0"
    "fs-extra" "^7.0.1"
    "globby" "^9.2.0"
    "hash-sum" "^2.0.0"
    "html-webpack-plugin" "^3.2.0"
    "launch-editor-middleware" "^2.2.1"
    "lodash.defaultsdeep" "^4.6.1"
    "lodash.mapvalues" "^4.6.0"
    "lodash.transform" "^4.6.0"
    "mini-css-extract-plugin" "^0.9.0"
    "minimist" "^1.2.5"
    "pnp-webpack-plugin" "^1.6.4"
    "portfinder" "^1.0.26"
    "postcss-loader" "^3.0.0"
    "ssri" "^8.0.1"
    "terser-webpack-plugin" "^1.4.4"
    "thread-loader" "^2.1.3"
    "url-loader" "^2.2.0"
    "vue-loader" "^15.9.2"
    "vue-style-loader" "^4.1.2"
    "webpack" "^4.0.0"
    "webpack-bundle-analyzer" "^3.8.0"
    "webpack-chain" "^6.4.0"
    "webpack-dev-server" "^3.11.0"
    "webpack-merge" "^4.2.2"
  optionalDependencies:
    "vue-loader-v16" "npm:vue-loader@^16.1.0"

"@vue/cli-shared-utils@^4.5.13":
  "integrity" "sha1-rNQPMbR5DxY0KSvapfypXcHg/1A="
  "resolved" "https://registry.nlark.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-4.5.13.tgz?cache=0&sync_timestamp=1620981722781&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40vue%2Fcli-shared-utils%2Fdownload%2F%40vue%2Fcli-shared-utils-4.5.13.tgz"
  "version" "4.5.13"
  dependencies:
    "@hapi/joi" "^15.0.1"
    "chalk" "^2.4.2"
    "execa" "^1.0.0"
    "launch-editor" "^2.2.1"
    "lru-cache" "^5.1.1"
    "node-ipc" "^9.1.1"
    "open" "^6.3.0"
    "ora" "^3.4.0"
    "read-pkg" "^5.1.1"
    "request" "^2.88.2"
    "semver" "^6.1.0"
    "strip-ansi" "^6.0.0"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.1.2":
  "integrity" "sha1-j4UYLO7Sjps8dTE95mn4MWbRHl0="
  "resolved" "https://registry.npm.taobao.org/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "consolidate" "^0.15.1"
    "hash-sum" "^1.0.2"
    "lru-cache" "^4.1.2"
    "merge-source-map" "^1.1.0"
    "postcss" "^7.0.14"
    "postcss-selector-parser" "^6.0.2"
    "source-map" "~0.6.1"
    "vue-template-es2015-compiler" "^1.9.0"
  optionalDependencies:
    "prettier" "^1.18.2"

"@vue/eslint-config-airbnb@^5.3.0":
  "integrity" "sha1-iWVR1gCBagbf8T/dfQT9UVM3mBc="
  "resolved" "https://registry.nlark.com/@vue/eslint-config-airbnb/download/@vue/eslint-config-airbnb-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "eslint-config-airbnb-base" "^14.0.0"
    "eslint-import-resolver-node" "^0.3.4"
    "eslint-import-resolver-webpack" "^0.13.0"
    "eslint-plugin-import" "^2.21.2"

"@vue/preload-webpack-plugin@^1.1.0":
  "integrity" "sha1-zrkktOyzucQ4ccekKaAvhCPmIas="
  "resolved" "https://registry.npm.taobao.org/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.2.tgz"
  "version" "1.1.2"

"@vue/web-component-wrapper@^1.2.0":
  "integrity" "sha1-trQKdiVCnSvXwigd26YB7QXcfxo="
  "resolved" "https://registry.npm.taobao.org/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.3.0.tgz"
  "version" "1.3.0"

"@webassemblyjs/ast@1.9.0":
  "integrity" "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz?cache=0&sync_timestamp=1610041386122&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fast%2Fdownload%2F%40webassemblyjs%2Fast-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  "integrity" "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz?cache=0&sync_timestamp=1610041388005&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Ffloating-point-hex-parser%2Fdownload%2F%40webassemblyjs%2Ffloating-point-hex-parser-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-api-error@1.9.0":
  "integrity" "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz?cache=0&sync_timestamp=1610041389547&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-api-error%2Fdownload%2F%40webassemblyjs%2Fhelper-api-error-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-buffer@1.9.0":
  "integrity" "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz?cache=0&sync_timestamp=1610041388939&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-buffer%2Fdownload%2F%40webassemblyjs%2Fhelper-buffer-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-code-frame@1.9.0":
  "integrity" "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz?cache=0&sync_timestamp=1610041390925&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-code-frame%2Fdownload%2F%40webassemblyjs%2Fhelper-code-frame-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  "integrity" "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz?cache=0&sync_timestamp=1610041389208&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-fsm%2Fdownload%2F%40webassemblyjs%2Fhelper-fsm-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-module-context@1.9.0":
  "integrity" "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz?cache=0&sync_timestamp=1601756234776&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-module-context%2Fdownload%2F%40webassemblyjs%2Fhelper-module-context-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  "integrity" "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz?cache=0&sync_timestamp=1610041389085&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-bytecode%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-bytecode-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-wasm-section@1.9.0":
  "integrity" "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz?cache=0&sync_timestamp=1610041387398&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-section%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-section-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  "integrity" "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz?cache=0&sync_timestamp=1610041389648&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fieee754%2Fdownload%2F%40webassemblyjs%2Fieee754-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  "integrity" "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz?cache=0&sync_timestamp=1610041389439&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fleb128%2Fdownload%2F%40webassemblyjs%2Fleb128-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  "integrity" "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz?cache=0&sync_timestamp=1610041389747&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Futf8%2Fdownload%2F%40webassemblyjs%2Futf8-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/wasm-edit@1.9.0":
  "integrity" "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz?cache=0&sync_timestamp=1610041387713&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-edit%2Fdownload%2F%40webassemblyjs%2Fwasm-edit-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  "integrity" "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz?cache=0&sync_timestamp=1610041387011&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-gen%2Fdownload%2F%40webassemblyjs%2Fwasm-gen-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  "integrity" "sha1-IhEYHlsxMmRDzIES658LkChyGmE="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz?cache=0&sync_timestamp=1610041387249&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-opt%2Fdownload%2F%40webassemblyjs%2Fwasm-opt-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  "integrity" "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz?cache=0&sync_timestamp=1610041386641&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-parser%2Fdownload%2F%40webassemblyjs%2Fwasm-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  "integrity" "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz?cache=0&sync_timestamp=1610041387557&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwast-parser%2Fdownload%2F%40webassemblyjs%2Fwast-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  "integrity" "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz?cache=0&sync_timestamp=1610041386456&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwast-printer%2Fdownload%2F%40webassemblyjs%2Fwast-printer-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A="
  "resolved" "https://registry.npm.taobao.org/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0="
  "resolved" "https://registry.npm.taobao.org/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  "version" "4.2.2"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.7":
  "integrity" "sha1-UxvHJlF6OytB+FACHGzBXqq1B80="
  "resolved" "https://registry.npm.taobao.org/accepts/download/accepts-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "mime-types" "~2.1.24"
    "negotiator" "0.6.2"

"acorn-jsx@^5.3.1":
  "integrity" "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc="
  "resolved" "https://registry.nlark.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^7.1.1":
  "integrity" "sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w="
  "resolved" "https://registry.nlark.com/acorn-walk/download/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn@^6.4.1":
  "integrity" "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY="
  "resolved" "https://registry.nlark.com/acorn/download/acorn-6.4.2.tgz?cache=0&sync_timestamp=1620134156200&other_urls=https%3A%2F%2Fregistry.nlark.com%2Facorn%2Fdownload%2Facorn-6.4.2.tgz"
  "version" "6.4.2"

"acorn@^7.1.1":
  "integrity" "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="
  "resolved" "https://registry.nlark.com/acorn/download/acorn-7.4.1.tgz?cache=0&sync_timestamp=1620134156200&other_urls=https%3A%2F%2Fregistry.nlark.com%2Facorn%2Fdownload%2Facorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^7.4.0":
  "integrity" "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="
  "resolved" "https://registry.nlark.com/acorn/download/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^8.7.0":
  "integrity" "sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ=="
  "resolved" "https://registry.npmmirror.com/acorn/-/acorn-8.7.0.tgz"
  "version" "8.7.0"

"address@^1.1.2":
  "integrity" "sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY="
  "resolved" "https://registry.npm.taobao.org/address/download/address-1.1.2.tgz"
  "version" "1.1.2"

"aggregate-error@^3.0.0":
  "integrity" "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo="
  "resolved" "https://registry.nlark.com/aggregate-error/download/aggregate-error-3.1.0.tgz?cache=0&sync_timestamp=1618847154458&other_urls=https%3A%2F%2Fregistry.nlark.com%2Faggregate-error%2Fdownload%2Faggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-errors@^1.0.0":
  "integrity" "sha1-81mGrOuRr63sQQL72FAUlQzvpk0="
  "resolved" "https://registry.npm.taobao.org/ajv-errors/download/ajv-errors-1.0.1.tgz?cache=0&sync_timestamp=1616886640262&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv-errors%2Fdownload%2Fajv-errors-1.0.1.tgz"
  "version" "1.0.1"

"ajv-keywords@^3.1.0", "ajv-keywords@^3.4.1", "ajv-keywords@^3.5.2":
  "integrity" "sha1-MfKdpatuANHC0yms97WSlhTVAU0="
  "resolved" "https://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-3.5.2.tgz?cache=0&sync_timestamp=1616882441894&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv-keywords%2Fdownload%2Fajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv@^6.1.0", "ajv@^6.10.0", "ajv@^6.10.2", "ajv@^6.12.3", "ajv@^6.12.4", "ajv@^6.12.5":
  "integrity" "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ="
  "resolved" "https://registry.nlark.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1621517642931&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.1":
  "integrity" "sha1-L7ReDl/LwIEzJsHD2lNdGIG7BXE="
  "resolved" "https://registry.nlark.com/ajv/download/ajv-8.6.2.tgz?cache=0&sync_timestamp=1626380017209&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fajv%2Fdownload%2Fajv-8.6.2.tgz"
  "version" "8.6.2"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"alphanum-sort@^1.0.0":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://registry.npm.taobao.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-colors@^3.0.0":
  "integrity" "sha1-46PaS/uubIapwoViXeEkojQCb78="
  "resolved" "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-3.2.4.tgz"
  "version" "3.2.4"

"ansi-colors@^4.1.1":
  "integrity" "sha1-y7muJWv3UK8eqzRPIpqif+lLo0g="
  "resolved" "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-4.1.1.tgz"
  "version" "4.1.1"

"ansi-escapes@^4.2.1", "ansi-escapes@^4.3.0":
  "integrity" "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4="
  "resolved" "https://registry.npm.taobao.org/ansi-escapes/download/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-html@0.0.7":
  "integrity" "sha1-gTWEAhliqenm/QOflA0S9WynhZ4="
  "resolved" "https://registry.npm.taobao.org/ansi-html/download/ansi-html-0.0.7.tgz"
  "version" "0.0.7"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz?cache=0&sync_timestamp=1618552950108&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&sync_timestamp=1618552950108&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz"
  "version" "3.0.0"

"ansi-regex@^4.1.0":
  "integrity" "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz?cache=0&sync_timestamp=1618552950108&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-4.1.0.tgz"
  "version" "4.1.0"

"ansi-regex@^5.0.0":
  "integrity" "sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-5.0.0.tgz?cache=0&sync_timestamp=1618552950108&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.0.tgz"
  "version" "5.0.0"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://registry.nlark.com/ansi-styles/download/ansi-styles-2.2.1.tgz?cache=0&sync_timestamp=1618995547052&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-styles%2Fdownload%2Fansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.0", "ansi-styles@^3.2.1":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1618995547052&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0":
  "integrity" "sha1-7dgDYornHATIWuegkG7a00tkiTc="
  "resolved" "https://registry.nlark.com/ansi-styles/download/ansi-styles-4.3.0.tgz?cache=0&sync_timestamp=1618995547052&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-styles%2Fdownload%2Fansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"any-promise@^1.0.0":
  "integrity" "sha1-q8av7tzqUugJzcA3au0845Y10X8="
  "resolved" "https://registry.npm.taobao.org/any-promise/download/any-promise-1.3.0.tgz"
  "version" "1.3.0"

"anymatch@^2.0.0":
  "integrity" "sha1-vLJLTzeTTZqnrBe0ra+J58du8us="
  "resolved" "https://registry.npm.taobao.org/anymatch/download/anymatch-2.0.0.tgz?cache=0&sync_timestamp=1617747806715&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fanymatch%2Fdownload%2Fanymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.0", "anymatch@~3.1.1":
  "integrity" "sha1-wFV8CWrzLxBhmPT04qODU343hxY="
  "resolved" "https://registry.npm.taobao.org/anymatch/download/anymatch-3.1.2.tgz?cache=0&sync_timestamp=1617747806715&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fanymatch%2Fdownload%2Fanymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"aproba@^1.1.1":
  "integrity" "sha1-aALmJk79GMeQobDVF/DyYnvyyUo="
  "resolved" "https://registry.npm.taobao.org/aproba/download/aproba-1.2.0.tgz"
  "version" "1.2.0"

"arch@^2.1.1":
  "integrity" "sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE="
  "resolved" "https://registry.npm.taobao.org/arch/download/arch-2.2.0.tgz"
  "version" "2.2.0"

"argparse@^1.0.7":
  "integrity" "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE="
  "resolved" "https://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://registry.npm.taobao.org/arr-diff/download/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
  "resolved" "https://registry.npm.taobao.org/arr-flatten/download/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://registry.npm.taobao.org/arr-union/download/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-find@^1.0.0":
  "integrity" "sha1-bI4obRHtdoMn+OYuzuhzU8o+eLg="
  "resolved" "https://registry.npm.taobao.org/array-find/download/array-find-1.0.0.tgz"
  "version" "1.0.0"

"array-flatten@^2.1.0":
  "integrity" "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk="
  "resolved" "https://registry.npm.taobao.org/array-flatten/download/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-includes@^3.1.3":
  "integrity" "sha1-x/YZs4KtKvr1Mmzd/cCvxhr3aQo="
  "resolved" "https://registry.nlark.com/array-includes/download/array-includes-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.2"
    "get-intrinsic" "^1.1.1"
    "is-string" "^1.0.5"

"array-union@^1.0.1", "array-union@^1.0.2":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "https://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz?cache=0&sync_timestamp=1614624861176&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-union%2Fdownload%2Farray-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "https://registry.nlark.com/array-uniq/download/array-uniq-1.0.3.tgz?cache=0&sync_timestamp=1620042244764&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farray-uniq%2Fdownload%2Farray-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://registry.npm.taobao.org/array-unique/download/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"array.prototype.flat@^1.2.4":
  "integrity" "sha1-bvY4tDMSvUAbTGGZ/ex+LcnpoSM="
  "resolved" "https://registry.npm.taobao.org/array.prototype.flat/download/array.prototype.flat-1.2.4.tgz?cache=0&sync_timestamp=1605688463641&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray.prototype.flat%2Fdownload%2Farray.prototype.flat-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.1"

"asn1.js@^5.2.0":
  "integrity" "sha1-EamAuE67kXgc41sP3C7ilON4Pwc="
  "resolved" "https://registry.npm.taobao.org/asn1.js/download/asn1.js-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "safer-buffer" "^2.1.0"

"asn1@~0.2.3":
  "integrity" "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY="
  "resolved" "https://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
  "resolved" "https://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@^1.1.1":
  "integrity" "sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs="
  "resolved" "https://registry.npm.taobao.org/assert/download/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "^4.1.1"
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^2.0.0":
  "integrity" "sha1-SDFDxWeu7UeFdZwIZXhtx319LjE="
  "resolved" "https://registry.npm.taobao.org/astral-regex/download/astral-regex-2.0.0.tgz"
  "version" "2.0.0"

"async-each@^1.0.1":
  "integrity" "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8="
  "resolved" "https://registry.npm.taobao.org/async-each/download/async-each-1.0.3.tgz"
  "version" "1.0.3"

"async-limiter@~1.0.0":
  "integrity" "sha1-3TeelPDbgxCwgpH51kwyCXZmF/0="
  "resolved" "https://registry.npm.taobao.org/async-limiter/download/async-limiter-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync-limiter%2Fdownload%2Fasync-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async-validator@~1.8.1":
  "integrity" "sha512-tXBM+1m056MAX0E8TL2iCjg8WvSyXu0Zc8LNtYqrVeyoL3+esHRZ4SieE9fKQyyU09uONjnMEjrNBMqT0mbvmA=="
  "resolved" "https://registry.npmjs.org/async-validator/-/async-validator-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "babel-runtime" "6.x"

"async@^2.6.2":
  "integrity" "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8="
  "resolved" "https://registry.npm.taobao.org/async/download/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
  "resolved" "https://registry.npm.taobao.org/atob/download/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^9.8.6":
  "integrity" "sha1-O3NZTKG/kmYyDFrPFYjXTep0IQ8="
  "resolved" "https://registry.npm.taobao.org/autoprefixer/download/autoprefixer-9.8.6.tgz?cache=0&sync_timestamp=1614956824768&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fautoprefixer%2Fdownload%2Fautoprefixer-9.8.6.tgz"
  "version" "9.8.6"
  dependencies:
    "browserslist" "^4.12.0"
    "caniuse-lite" "^1.0.30001109"
    "colorette" "^1.2.1"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "postcss" "^7.0.32"
    "postcss-value-parser" "^4.1.0"

"aws-sign2@~0.7.0":
  "integrity" "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="
  "resolved" "https://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "integrity" "sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk="
  "resolved" "https://registry.npm.taobao.org/aws4/download/aws4-1.11.0.tgz?cache=0&sync_timestamp=1604101210422&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faws4%2Fdownload%2Faws4-1.11.0.tgz"
  "version" "1.11.0"

"axios@^0.21.1":
  "integrity" "sha1-IlY0gZYvTWvemnbVFu8OXTwJsrg="
  "resolved" "https://registry.npm.taobao.org/axios/download/axios-0.21.1.tgz"
  "version" "0.21.1"
  dependencies:
    "follow-redirects" "^1.10.0"

"babel-code-frame@^6.26.0":
  "integrity" "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s="
  "resolved" "https://registry.npm.taobao.org/babel-code-frame/download/babel-code-frame-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "chalk" "^1.1.3"
    "esutils" "^2.0.2"
    "js-tokens" "^3.0.2"

"babel-eslint@8.2.2":
  "integrity" "sha512-Qt2lz2egBxNYWqN9JIO2z4NOOf8i4b5JS6CFoYrOZZTDssueiV1jH/jsefyg+86SeNY3rB361/mi3kE1WK2WYQ=="
  "resolved" "https://registry.npmmirror.com/babel-eslint/-/babel-eslint-8.2.2.tgz"
  "version" "8.2.2"
  dependencies:
    "@babel/code-frame" "^7.0.0-beta.40"
    "@babel/traverse" "^7.0.0-beta.40"
    "@babel/types" "^7.0.0-beta.40"
    "babylon" "^7.0.0-beta.40"
    "eslint-scope" "~3.7.1"
    "eslint-visitor-keys" "^1.0.0"

"babel-helper-call-delegate@^6.24.1":
  "integrity" "sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340="
  "resolved" "https://registry.npm.taobao.org/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-define-map@^6.24.1":
  "integrity" "sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8="
  "resolved" "https://registry.npm.taobao.org/babel-helper-define-map/download/babel-helper-define-map-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-function-name@^6.24.1":
  "integrity" "sha1-00dbjAPtmCQqJbSDUasYOZ01gKk="
  "resolved" "https://registry.npm.taobao.org/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-get-function-arity@^6.24.1":
  "integrity" "sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0="
  "resolved" "https://registry.npm.taobao.org/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-hoist-variables@^6.24.1":
  "integrity" "sha1-HssnaJydJVE+rbyZFKc/VAi+enY="
  "resolved" "https://registry.npm.taobao.org/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-optimise-call-expression@^6.24.1":
  "integrity" "sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc="
  "resolved" "https://registry.npm.taobao.org/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-regex@^6.24.1":
  "integrity" "sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI="
  "resolved" "https://registry.npm.taobao.org/babel-helper-regex/download/babel-helper-regex-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-replace-supers@^6.24.1":
  "integrity" "sha1-v22/5Dk40XNpohPKiov3S2qQqxo="
  "resolved" "https://registry.npm.taobao.org/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-vue-jsx-merge-props@^2.0.0":
  "integrity" "sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg=="
  "resolved" "https://registry.npmjs.org/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  "version" "2.0.3"

"babel-loader@^8.1.0":
  "integrity" "sha1-k2POhMEMmkDmx1N0jhRBtgyKC4E="
  "resolved" "https://registry.npm.taobao.org/babel-loader/download/babel-loader-8.2.2.tgz?cache=0&sync_timestamp=1606424647115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-loader%2Fdownload%2Fbabel-loader-8.2.2.tgz"
  "version" "8.2.2"
  dependencies:
    "find-cache-dir" "^3.3.1"
    "loader-utils" "^1.4.0"
    "make-dir" "^3.1.0"
    "schema-utils" "^2.6.5"

"babel-messages@^6.23.0":
  "integrity" "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4="
  "resolved" "https://registry.npm.taobao.org/babel-messages/download/babel-messages-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-check-es2015-constants@^6.22.0":
  "integrity" "sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-component@^1.1.1":
  "integrity" "sha1-mwI6I/9cmq4P1WxaGLnKuMTUXuo="
  "resolved" "https://registry.nlark.com/babel-plugin-component/download/babel-plugin-component-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@babel/helper-module-imports" "7.0.0-beta.35"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-import@^1.12.0":
  "integrity" "sha1-nbu6fRrHK9QSkXqDDUReAJQdJtc="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-import/download/babel-plugin-import-1.13.3.tgz?cache=0&sync_timestamp=1606209841293&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-import%2Fdownload%2Fbabel-plugin-import-1.13.3.tgz"
  "version" "1.13.3"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

"babel-plugin-polyfill-corejs2@^0.2.0":
  "integrity" "sha1-riz21vGqfA7c8EolGA6IVqbRGE8="
  "resolved" "https://registry.nlark.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.2.1.tgz?cache=0&sync_timestamp=1621636230708&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-polyfill-corejs2%2Fdownload%2Fbabel-plugin-polyfill-corejs2-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.2.1"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.2.0":
  "integrity" "sha1-eG9AIYBAAw8O3s/Ujm5Z8e6b71M="
  "resolved" "https://registry.nlark.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.2.1.tgz?cache=0&sync_timestamp=1621635783444&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-polyfill-corejs3%2Fdownload%2Fbabel-plugin-polyfill-corejs3-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.2.1"
    "core-js-compat" "^3.9.1"

"babel-plugin-polyfill-regenerator@^0.2.0":
  "integrity" "sha1-ypWV19Xzr+/sLYMSYUi5DbdRoJE="
  "resolved" "https://registry.nlark.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.2.1.tgz?cache=0&sync_timestamp=1621635782723&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-polyfill-regenerator%2Fdownload%2Fbabel-plugin-polyfill-regenerator-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.2.1"

"babel-plugin-syntax-dynamic-import@^6.18.0":
  "integrity" "sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo="
  "resolved" "https://registry.npmjs.org/babel-plugin-syntax-dynamic-import/-/babel-plugin-syntax-dynamic-import-6.18.0.tgz"
  "version" "6.18.0"

"babel-plugin-transform-es2015-arrow-functions@^6.22.0":
  "integrity" "sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoped-functions@^6.22.0":
  "integrity" "sha1-u8UbSflk1wy42OC5ToICRs46YUE="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoping@^6.24.1":
  "integrity" "sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-plugin-transform-es2015-classes@^6.24.1":
  "integrity" "sha1-WkxYpQyclGHlZLSyo7+ryXolhNs="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-define-map" "^6.24.1"
    "babel-helper-function-name" "^6.24.1"
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-helper-replace-supers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-computed-properties@^6.24.1":
  "integrity" "sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-destructuring@^6.22.0":
  "integrity" "sha1-mXux8auWf2gtKwh2/jWNYOdlxW0="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-duplicate-keys@^6.24.1":
  "integrity" "sha1-c+s9MQypaePvnskcU3QabxV2Qj4="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-for-of@^6.22.0":
  "integrity" "sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-function-name@^6.24.1":
  "integrity" "sha1-g0yJhTvDaxrw86TF26qU/Y6sqos="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-literals@^6.22.0":
  "integrity" "sha1-T1SgLWzWbPkVKAAZox0xklN3yi4="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-modules-amd@^6.24.1":
  "integrity" "sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-commonjs" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-commonjs@^6.24.1":
  "integrity" "sha1-WKeThjqefKhwvcWogRF/+sJ9tvM="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  "version" "6.26.2"
  dependencies:
    "babel-plugin-transform-strict-mode" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-types" "^6.26.0"

"babel-plugin-transform-es2015-modules-systemjs@^6.24.1":
  "integrity" "sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-umd@^6.24.1":
  "integrity" "sha1-rJl+YoXNGO1hdq22B9YCNErThGg="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-amd" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-object-super@^6.24.1":
  "integrity" "sha1-JM72muIcuDp/hgPa0CH1cusnj40="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-replace-supers" "^6.24.1"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-parameters@^6.24.1":
  "integrity" "sha1-V6w1GrScrxSpfNE7CfZv3wpiXys="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-call-delegate" "^6.24.1"
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-shorthand-properties@^6.24.1":
  "integrity" "sha1-JPh11nIch2YbvZmkYi5R8U3jiqA="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-spread@^6.22.0":
  "integrity" "sha1-1taKmfia7cRTbIGlQujdnxdG+NE="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-sticky-regex@^6.24.1":
  "integrity" "sha1-AMHNsaynERLN8M9hJsLta0V8zbw="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-template-literals@^6.22.0":
  "integrity" "sha1-qEs0UPfp+PH2g51taH2oS7EjbY0="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-typeof-symbol@^6.22.0":
  "integrity" "sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-unicode-regex@^6.24.1":
  "integrity" "sha1-04sS9C6nMj9yk4fxinxa4frrNek="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "regexpu-core" "^2.0.0"

"babel-plugin-transform-regenerator@^6.24.1":
  "integrity" "sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "regenerator-transform" "^0.10.0"

"babel-plugin-transform-strict-mode@^6.24.1":
  "integrity" "sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-preset-es2015@^6.24.1":
  "integrity" "sha1-1EBQ1rwsn+6nAqrzjXJ6AhBTiTk="
  "resolved" "https://registry.npm.taobao.org/babel-preset-es2015/download/babel-preset-es2015-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-check-es2015-constants" "^6.22.0"
    "babel-plugin-transform-es2015-arrow-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoped-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoping" "^6.24.1"
    "babel-plugin-transform-es2015-classes" "^6.24.1"
    "babel-plugin-transform-es2015-computed-properties" "^6.24.1"
    "babel-plugin-transform-es2015-destructuring" "^6.22.0"
    "babel-plugin-transform-es2015-duplicate-keys" "^6.24.1"
    "babel-plugin-transform-es2015-for-of" "^6.22.0"
    "babel-plugin-transform-es2015-function-name" "^6.24.1"
    "babel-plugin-transform-es2015-literals" "^6.22.0"
    "babel-plugin-transform-es2015-modules-amd" "^6.24.1"
    "babel-plugin-transform-es2015-modules-commonjs" "^6.24.1"
    "babel-plugin-transform-es2015-modules-systemjs" "^6.24.1"
    "babel-plugin-transform-es2015-modules-umd" "^6.24.1"
    "babel-plugin-transform-es2015-object-super" "^6.24.1"
    "babel-plugin-transform-es2015-parameters" "^6.24.1"
    "babel-plugin-transform-es2015-shorthand-properties" "^6.24.1"
    "babel-plugin-transform-es2015-spread" "^6.22.0"
    "babel-plugin-transform-es2015-sticky-regex" "^6.24.1"
    "babel-plugin-transform-es2015-template-literals" "^6.22.0"
    "babel-plugin-transform-es2015-typeof-symbol" "^6.22.0"
    "babel-plugin-transform-es2015-unicode-regex" "^6.24.1"
    "babel-plugin-transform-regenerator" "^6.24.1"

"babel-runtime@^6.18.0", "babel-runtime@^6.22.0", "babel-runtime@^6.26.0", "babel-runtime@6.x":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"babel-template@^6.24.1", "babel-template@^6.26.0":
  "integrity" "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI="
  "resolved" "https://registry.npm.taobao.org/babel-template/download/babel-template-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "lodash" "^4.17.4"

"babel-traverse@^6.24.1", "babel-traverse@^6.26.0":
  "integrity" "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4="
  "resolved" "https://registry.npm.taobao.org/babel-traverse/download/babel-traverse-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "debug" "^2.6.8"
    "globals" "^9.18.0"
    "invariant" "^2.2.2"
    "lodash" "^4.17.4"

"babel-types@^6.19.0", "babel-types@^6.24.1", "babel-types@^6.26.0":
  "integrity" "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc="
  "resolved" "https://registry.npm.taobao.org/babel-types/download/babel-types-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "esutils" "^2.0.2"
    "lodash" "^4.17.4"
    "to-fast-properties" "^1.0.3"

"babylon@^6.18.0":
  "integrity" "sha1-ry87iPpvXB5MY00aD46sT1WzleM="
  "resolved" "https://registry.npm.taobao.org/babylon/download/babylon-6.18.0.tgz"
  "version" "6.18.0"

"babylon@^7.0.0-beta.40":
  "integrity" "sha512-+rq2cr4GDhtToEzKFD6KZZMDBXhjFAr9JjPw9pAppZACeEWqNM294j+NdBzkSHYXwzzBmVjZ3nEVJlOhbR2gOQ=="
  "resolved" "https://registry.npmmirror.com/babylon/-/babylon-7.0.0-beta.47.tgz"
  "version" "7.0.0-beta.47"

"balanced-match@^1.0.0":
  "integrity" "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="
  "resolved" "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbalanced-match%2Fdownload%2Fbalanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
  "resolved" "https://registry.npm.taobao.org/base/download/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-arraybuffer@^0.2.0":
  "integrity" "sha1-S5RPrAGRqlkHr+LYyZnMxXzoD0U="
  "resolved" "https://registry.nlark.com/base64-arraybuffer/download/base64-arraybuffer-0.2.0.tgz?cache=0&sync_timestamp=1628588917496&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbase64-arraybuffer%2Fdownload%2Fbase64-arraybuffer-0.2.0.tgz"
  "version" "0.2.0"

"base64-arraybuffer@^1.0.1":
  "integrity" "sha1-h70TUlYm20qYOOAKUIwrc+/PNIw="
  "resolved" "https://registry.nlark.com/base64-arraybuffer/download/base64-arraybuffer-1.0.1.tgz?cache=0&sync_timestamp=1628588917496&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbase64-arraybuffer%2Fdownload%2Fbase64-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"base64-js@^1.0.2":
  "integrity" "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="
  "resolved" "https://registry.npm.taobao.org/base64-js/download/base64-js-1.5.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbase64-js%2Fdownload%2Fbase64-js-1.5.1.tgz"
  "version" "1.5.1"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://registry.npm.taobao.org/batch/download/batch-0.6.1.tgz"
  "version" "0.6.1"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
  "resolved" "https://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"bfj@^6.1.1":
  "integrity" "sha1-MlyGGoIryzWKQceKM7jm4ght3n8="
  "resolved" "https://registry.npm.taobao.org/bfj/download/bfj-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "bluebird" "^3.5.5"
    "check-types" "^8.0.3"
    "hoopy" "^0.1.4"
    "tryer" "^1.0.1"

"big.js@^3.1.3":
  "integrity" "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4="
  "resolved" "https://registry.nlark.com/big.js/download/big.js-3.2.0.tgz"
  "version" "3.2.0"

"big.js@^5.2.2":
  "integrity" "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg="
  "resolved" "https://registry.nlark.com/big.js/download/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U="
  "resolved" "https://registry.npm.taobao.org/binary-extensions/download/binary-extensions-1.13.1.tgz?cache=0&sync_timestamp=1610299640881&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbinary-extensions%2Fdownload%2Fbinary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"binary-extensions@^2.0.0":
  "integrity" "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0="
  "resolved" "https://registry.npm.taobao.org/binary-extensions/download/binary-extensions-2.2.0.tgz?cache=0&sync_timestamp=1610299640881&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbinary-extensions%2Fdownload%2Fbinary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"bindings@^1.5.0":
  "integrity" "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8="
  "resolved" "https://registry.npm.taobao.org/bindings/download/bindings-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "file-uri-to-path" "1.0.0"

"bluebird@^3.1.1", "bluebird@^3.5.5":
  "integrity" "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="
  "resolved" "https://registry.npm.taobao.org/bluebird/download/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bn.js@^4.0.0":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.1.0":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.11.9":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^5.0.0", "bn.js@^5.1.1":
  "integrity" "sha1-NYhgZ0OWxpl3canQUfzBtX1K4AI="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-5.2.0.tgz"
  "version" "5.2.0"

"body-parser@1.19.0":
  "integrity" "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io="
  "resolved" "https://registry.npm.taobao.org/body-parser/download/body-parser-1.19.0.tgz"
  "version" "1.19.0"
  dependencies:
    "bytes" "3.1.0"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "on-finished" "~2.3.0"
    "qs" "6.7.0"
    "raw-body" "2.4.0"
    "type-is" "~1.6.17"

"bonjour@^3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://registry.npm.taobao.org/bonjour/download/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://registry.npm.taobao.org/boolbase/download/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "https://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^2.3.1", "braces@^2.3.2":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.1":
  "integrity" "sha1-NFThpGLujVmeI23zNs2epPiv4Qc="
  "resolved" "https://registry.npm.taobao.org/braces/download/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"braces@~3.0.2":
  "integrity" "sha1-NFThpGLujVmeI23zNs2epPiv4Qc="
  "resolved" "https://registry.npm.taobao.org/braces/download/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"brorand@^1.0.1", "brorand@^1.1.0":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "https://registry.npm.taobao.org/brorand/download/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g="
  "resolved" "https://registry.npm.taobao.org/browserify-aes/download/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA="
  "resolved" "https://registry.npm.taobao.org/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw="
  "resolved" "https://registry.npm.taobao.org/browserify-des/download/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0", "browserify-rsa@^4.0.1":
  "integrity" "sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0="
  "resolved" "https://registry.npm.taobao.org/browserify-rsa/download/browserify-rsa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "bn.js" "^5.0.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM="
  "resolved" "https://registry.npm.taobao.org/browserify-sign/download/browserify-sign-4.2.1.tgz?cache=0&sync_timestamp=1596557798339&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbrowserify-sign%2Fdownload%2Fbrowserify-sign-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "bn.js" "^5.1.1"
    "browserify-rsa" "^4.0.1"
    "create-hash" "^1.2.0"
    "create-hmac" "^1.1.7"
    "elliptic" "^6.5.3"
    "inherits" "^2.0.4"
    "parse-asn1" "^5.1.5"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8="
  "resolved" "https://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^4.0.0", "browserslist@^4.12.0", "browserslist@^4.14.5", "browserslist@^4.16.6":
  "integrity" "sha1-15ASd6WojlVO0wWxg+ybDAj2b6I="
  "resolved" "https://registry.nlark.com/browserslist/download/browserslist-4.16.6.tgz?cache=0&sync_timestamp=1619789101558&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbrowserslist%2Fdownload%2Fbrowserslist-4.16.6.tgz"
  "version" "4.16.6"
  dependencies:
    "caniuse-lite" "^1.0.30001219"
    "colorette" "^1.2.2"
    "electron-to-chromium" "^1.3.723"
    "escalade" "^3.1.1"
    "node-releases" "^1.1.71"

"buffer-from@^1.0.0":
  "integrity" "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8="
  "resolved" "https://registry.npm.taobao.org/buffer-from/download/buffer-from-1.1.1.tgz"
  "version" "1.1.1"

"buffer-indexof@^1.0.0":
  "integrity" "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow="
  "resolved" "https://registry.npm.taobao.org/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-json@^2.0.0":
  "integrity" "sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM="
  "resolved" "https://registry.npm.taobao.org/buffer-json/download/buffer-json-2.0.0.tgz"
  "version" "2.0.0"

"buffer-xor@^1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "https://registry.npm.taobao.org/buffer-xor/download/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg="
  "resolved" "https://registry.npm.taobao.org/buffer/download/buffer-4.9.2.tgz?cache=0&sync_timestamp=1606098078312&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer%2Fdownload%2Fbuffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"builtin-status-codes@^3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "https://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npm.taobao.org/bytes/download/bytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.0":
  "integrity" "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="
  "resolved" "https://registry.npm.taobao.org/bytes/download/bytes-3.1.0.tgz"
  "version" "3.1.0"

"cacache@^12.0.2", "cacache@^12.0.3":
  "integrity" "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw="
  "resolved" "https://registry.nlark.com/cacache/download/cacache-12.0.4.tgz"
  "version" "12.0.4"
  dependencies:
    "bluebird" "^3.5.5"
    "chownr" "^1.1.1"
    "figgy-pudding" "^3.5.1"
    "glob" "^7.1.4"
    "graceful-fs" "^4.1.15"
    "infer-owner" "^1.0.3"
    "lru-cache" "^5.1.1"
    "mississippi" "^3.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.3"
    "ssri" "^6.0.1"
    "unique-filename" "^1.1.1"
    "y18n" "^4.0.0"

"cache-base@^1.0.1":
  "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
  "resolved" "https://registry.npm.taobao.org/cache-base/download/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cache-loader@^4.1.0":
  "integrity" "sha1-mUjK41OuwKH8ser9ojAIFuyFOH4="
  "resolved" "https://registry.npm.taobao.org/cache-loader/download/cache-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer-json" "^2.0.0"
    "find-cache-dir" "^3.0.0"
    "loader-utils" "^1.2.3"
    "mkdirp" "^0.5.1"
    "neo-async" "^2.6.1"
    "schema-utils" "^2.0.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw="
  "resolved" "https://registry.npm.taobao.org/call-bind/download/call-bind-1.0.2.tgz?cache=0&sync_timestamp=1610402811207&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcall-bind%2Fdownload%2Fcall-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"call-me-maybe@^1.0.1":
  "integrity" "sha1-JtII6onje1y95gJQoV8DHBak1ms="
  "resolved" "https://registry.npm.taobao.org/call-me-maybe/download/call-me-maybe-1.0.1.tgz"
  "version" "1.0.1"

"caller-callsite@^2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "https://registry.npm.taobao.org/caller-callsite/download/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "https://registry.npm.taobao.org/caller-path/download/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "https://registry.npm.taobao.org/callsites/download/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="
  "resolved" "https://registry.nlark.com/callsites/download/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@3.0.x":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "https://registry.npm.taobao.org/camel-case/download/camel-case-3.0.0.tgz?cache=0&sync_timestamp=1606867454571&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamel-case%2Fdownload%2Fcamel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase@^5.0.0":
  "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
  "resolved" "https://registry.npm.taobao.org/camelcase/download/camelcase-5.3.1.tgz?cache=0&sync_timestamp=1603921882890&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamelcase%2Fdownload%2Fcamelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^5.3.1":
  "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
  "resolved" "https://registry.npm.taobao.org/camelcase/download/camelcase-5.3.1.tgz?cache=0&sync_timestamp=1603921882890&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamelcase%2Fdownload%2Fcamelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.0.0":
  "integrity" "sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk="
  "resolved" "https://registry.npm.taobao.org/camelcase/download/camelcase-6.2.0.tgz?cache=0&sync_timestamp=1603921882890&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamelcase%2Fdownload%2Fcamelcase-6.2.0.tgz"
  "version" "6.2.0"

"caniuse-api@^3.0.0":
  "integrity" "sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA="
  "resolved" "https://registry.npm.taobao.org/caniuse-api/download/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001109", "caniuse-lite@^1.0.30001219":
  "integrity" "sha1-v9xZQs0zJvpR7gtC++9NqdSSp/o="
  "resolved" "https://registry.nlark.com/caniuse-lite/download/caniuse-lite-1.0.30001228.tgz?cache=0&sync_timestamp=1620658655589&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcaniuse-lite%2Fdownload%2Fcaniuse-lite-1.0.30001228.tgz"
  "version" "1.0.30001228"

"case-sensitive-paths-webpack-plugin@^2.3.0":
  "integrity" "sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ="
  "resolved" "https://registry.npm.taobao.org/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  "version" "2.4.0"

"caseless@~0.12.0":
  "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
  "resolved" "https://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz"
  "version" "0.12.0"

"chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://registry.nlark.com/chalk/download/chalk-1.1.3.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.0", "chalk@^2.0.1", "chalk@^2.3.0", "chalk@^2.4.1", "chalk@^2.4.2":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.nlark.com/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1618995354302&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^4.0.0":
  "integrity" "sha1-yAs/qyi/Y3HmhjMl7uZ+YYt35q0="
  "resolved" "https://registry.nlark.com/chalk/download/chalk-4.1.1.tgz?cache=0&sync_timestamp=1618995354302&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.1":
  "integrity" "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE="
  "resolved" "https://registry.nlark.com/chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chardet@^0.7.0":
  "integrity" "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4="
  "resolved" "https://registry.npm.taobao.org/chardet/download/chardet-0.7.0.tgz?cache=0&sync_timestamp=1601032519509&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchardet%2Fdownload%2Fchardet-0.7.0.tgz"
  "version" "0.7.0"

"check-types@^8.0.3":
  "integrity" "sha1-M1bMoZyIlUTy16le1JzlCKDs9VI="
  "resolved" "https://registry.npm.taobao.org/check-types/download/check-types-8.0.3.tgz"
  "version" "8.0.3"

"chokidar@^2.1.8":
  "integrity" "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc="
  "resolved" "https://registry.npm.taobao.org/chokidar/download/chokidar-2.1.8.tgz?cache=0&sync_timestamp=1610719380575&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.1"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chokidar@^3.4.1":
  "integrity" "sha1-7pznu+vSt59J8wR5nVRo4x4U5oo="
  "resolved" "https://registry.npm.taobao.org/chokidar/download/chokidar-3.5.1.tgz?cache=0&sync_timestamp=1610719380575&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-3.5.1.tgz"
  "version" "3.5.1"
  dependencies:
    "anymatch" "~3.1.1"
    "braces" "~3.0.2"
    "fsevents" "~2.3.1"
    "glob-parent" "~5.1.0"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.5.0"

"chownr@^1.1.1":
  "integrity" "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs="
  "resolved" "https://registry.npm.taobao.org/chownr/download/chownr-1.1.4.tgz"
  "version" "1.1.4"

"chrome-trace-event@^1.0.2":
  "integrity" "sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw="
  "resolved" "https://registry.npm.taobao.org/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz?cache=0&sync_timestamp=1617905826919&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchrome-trace-event%2Fdownload%2Fchrome-trace-event-1.0.3.tgz"
  "version" "1.0.3"

"ci-info@^1.5.0":
  "integrity" "sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc="
  "resolved" "https://registry.npm.taobao.org/ci-info/download/ci-info-1.6.0.tgz"
  "version" "1.6.0"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94="
  "resolved" "https://registry.npm.taobao.org/cipher-base/download/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"class-utils@^0.3.5":
  "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
  "resolved" "https://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-css@4.2.x":
  "integrity" "sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g="
  "resolved" "https://registry.npm.taobao.org/clean-css/download/clean-css-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "source-map" "~0.6.0"

"clean-stack@^2.0.0":
  "integrity" "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs="
  "resolved" "https://registry.nlark.com/clean-stack/download/clean-stack-2.2.0.tgz?cache=0&sync_timestamp=1621915054928&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fclean-stack%2Fdownload%2Fclean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://registry.npm.taobao.org/cli-cursor/download/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-cursor@^3.1.0":
  "integrity" "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc="
  "resolved" "https://registry.npm.taobao.org/cli-cursor/download/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-highlight@^2.1.4":
  "integrity" "sha1-SXNvpFLwqvT65YDjCssmgo0twb8="
  "resolved" "https://registry.npm.taobao.org/cli-highlight/download/cli-highlight-2.1.11.tgz?cache=0&sync_timestamp=1616955426054&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-highlight%2Fdownload%2Fcli-highlight-2.1.11.tgz"
  "version" "2.1.11"
  dependencies:
    "chalk" "^4.0.0"
    "highlight.js" "^10.7.1"
    "mz" "^2.4.0"
    "parse5" "^5.1.1"
    "parse5-htmlparser2-tree-adapter" "^6.0.0"
    "yargs" "^16.0.0"

"cli-spinners@^2.0.0":
  "integrity" "sha1-NsfcmPtqmna9YjjsP3fiQlYn6Tk="
  "resolved" "https://registry.npm.taobao.org/cli-spinners/download/cli-spinners-2.6.0.tgz?cache=0&sync_timestamp=1616091622495&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-spinners%2Fdownload%2Fcli-spinners-2.6.0.tgz"
  "version" "2.6.0"

"cli-truncate@^2.1.0":
  "integrity" "sha1-w54ovwXtzeW+O5iZKiLe7Vork8c="
  "resolved" "https://registry.nlark.com/cli-truncate/download/cli-truncate-2.1.0.tgz?cache=0&sync_timestamp=1628633155345&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcli-truncate%2Fdownload%2Fcli-truncate-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "slice-ansi" "^3.0.0"
    "string-width" "^4.2.0"

"cli-width@^3.0.0":
  "integrity" "sha1-ovSEN6LKqaIkNueUvwceyeYc7fY="
  "resolved" "https://registry.npm.taobao.org/cli-width/download/cli-width-3.0.0.tgz"
  "version" "3.0.0"

"clipboard@^2.0.8":
  "integrity" "sha1-/8bBA90pZ6gwBfP2GXaqRlWkzbo="
  "resolved" "https://registry.npm.taobao.org/clipboard/download/clipboard-2.0.8.tgz"
  "version" "2.0.8"
  dependencies:
    "good-listener" "^1.2.2"
    "select" "^1.1.2"
    "tiny-emitter" "^2.0.0"

"clipboardy@^2.3.0":
  "integrity" "sha1-PCkDZQxo5GqRs4iYW8J3QofbopA="
  "resolved" "https://registry.npm.taobao.org/clipboardy/download/clipboardy-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "arch" "^2.1.1"
    "execa" "^1.0.0"
    "is-wsl" "^2.1.1"

"cliui@^5.0.0":
  "integrity" "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U="
  "resolved" "https://registry.npm.taobao.org/cliui/download/cliui-5.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "string-width" "^3.1.0"
    "strip-ansi" "^5.2.0"
    "wrap-ansi" "^5.1.0"

"cliui@^6.0.0":
  "integrity" "sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE="
  "resolved" "https://registry.npm.taobao.org/cliui/download/cliui-6.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"cliui@^7.0.2":
  "integrity" "sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08="
  "resolved" "https://registry.npm.taobao.org/cliui/download/cliui-7.0.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "https://registry.npm.taobao.org/clone/download/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://registry.npm.taobao.org/clone/download/clone-2.1.2.tgz"
  "version" "2.1.2"

"coa@^2.0.2":
  "integrity" "sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM="
  "resolved" "https://registry.npm.taobao.org/coa/download/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://registry.npm.taobao.org/collection-visit/download/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0", "color-convert@^1.9.1":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "https://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-string@^1.5.4":
  "integrity" "sha1-ZUdKjw50OWJfPSemoZ2J/EUiMBQ="
  "resolved" "https://registry.npm.taobao.org/color-string/download/color-string-1.5.5.tgz"
  "version" "1.5.5"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color@^3.0.0":
  "integrity" "sha1-ymf7TnuX1hHc3jns7tQiBn2RWW4="
  "resolved" "https://registry.npm.taobao.org/color/download/color-3.1.3.tgz?cache=0&sync_timestamp=1602228737770&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolor%2Fdownload%2Fcolor-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "color-convert" "^1.9.1"
    "color-string" "^1.5.4"

"colorette@^1.2.1", "colorette@^1.2.2":
  "integrity" "sha1-y8x51emcrqLb8Q6zom/Ys+as+pQ="
  "resolved" "https://registry.npm.taobao.org/colorette/download/colorette-1.2.2.tgz?cache=0&sync_timestamp=1614259647923&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolorette%2Fdownload%2Fcolorette-1.2.2.tgz"
  "version" "1.2.2"

"combined-stream@^1.0.6", "combined-stream@~1.0.6":
  "integrity" "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8="
  "resolved" "https://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.18.0", "commander@^2.20.0":
  "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
  "resolved" "https://registry.nlark.com/commander/download/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^7.2.0":
  "integrity" "sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc="
  "resolved" "https://registry.nlark.com/commander/download/commander-7.2.0.tgz?cache=0&sync_timestamp=1627358254258&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcommander%2Fdownload%2Fcommander-7.2.0.tgz"
  "version" "7.2.0"

"commander@~2.19.0":
  "integrity" "sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So="
  "resolved" "https://registry.nlark.com/commander/download/commander-2.19.0.tgz"
  "version" "2.19.0"

"commander@2.17.x":
  "integrity" "sha1-vXerfebelCBc6sxy8XFtKfIKd78="
  "resolved" "https://registry.nlark.com/commander/download/commander-2.17.1.tgz"
  "version" "2.17.1"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-emitter@^1.2.1":
  "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
  "resolved" "https://registry.npm.taobao.org/component-emitter/download/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"compressible@~2.0.16":
  "integrity" "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o="
  "resolved" "https://registry.npm.taobao.org/compressible/download/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.4":
  "integrity" "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48="
  "resolved" "https://registry.npm.taobao.org/compression/download/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0":
  "integrity" "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ="
  "resolved" "https://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"confusing-browser-globals@^1.0.10":
  "integrity" "sha1-MNHn89G4grJexJM9HRraw1PSClk="
  "resolved" "https://registry.nlark.com/confusing-browser-globals/download/confusing-browser-globals-1.0.10.tgz"
  "version" "1.0.10"

"connect-history-api-fallback@^1.6.0":
  "integrity" "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w="
  "resolved" "https://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"console-browserify@^1.1.0":
  "integrity" "sha1-ZwY871fOts9Jk6KrOlWECujEkzY="
  "resolved" "https://registry.npm.taobao.org/console-browserify/download/console-browserify-1.2.0.tgz"
  "version" "1.2.0"

"consolidate@^0.15.1":
  "integrity" "sha1-IasEMjXHGgfUXZqtmFk7DbpWurc="
  "resolved" "https://registry.npm.taobao.org/consolidate/download/consolidate-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "bluebird" "^3.1.1"

"constants-browserify@^1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "https://registry.npm.taobao.org/constants-browserify/download/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"content-disposition@0.5.3":
  "integrity" "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70="
  "resolved" "https://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "safe-buffer" "5.1.2"

"content-type@~1.0.4":
  "integrity" "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="
  "resolved" "https://registry.npm.taobao.org/content-type/download/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.7.0":
  "integrity" "sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI="
  "resolved" "https://registry.npm.taobao.org/convert-source-map/download/convert-source-map-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.4.0":
  "integrity" "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="
  "resolved" "https://registry.npm.taobao.org/cookie/download/cookie-0.4.0.tgz"
  "version" "0.4.0"

"copy-anything@^2.0.1":
  "integrity" "sha1-hCQHugJGaw34RIGbvjuuu+XUXYc="
  "resolved" "https://registry.npm.taobao.org/copy-anything/download/copy-anything-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "is-what" "^3.12.0"

"copy-concurrently@^1.0.0":
  "integrity" "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA="
  "resolved" "https://registry.npm.taobao.org/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-text-to-clipboard@^3.0.1":
  "integrity" "sha1-jL+PkOCkfxLkokdDc2Jl0Ve85pw="
  "resolved" "https://registry.npm.taobao.org/copy-text-to-clipboard/download/copy-text-to-clipboard-3.0.1.tgz?cache=0&sync_timestamp=1613624855285&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcopy-text-to-clipboard%2Fdownload%2Fcopy-text-to-clipboard-3.0.1.tgz"
  "version" "3.0.1"

"copy-webpack-plugin@^5.1.1":
  "integrity" "sha1-ioieHcr6bJHGzUvhrRWPHTgjuuI="
  "resolved" "https://registry.nlark.com/copy-webpack-plugin/download/copy-webpack-plugin-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "cacache" "^12.0.3"
    "find-cache-dir" "^2.1.0"
    "glob-parent" "^3.1.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.1"
    "loader-utils" "^1.2.3"
    "minimatch" "^3.0.4"
    "normalize-path" "^3.0.0"
    "p-limit" "^2.2.1"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^4.0.0"
    "webpack-log" "^2.0.0"

"core-js-compat@^3.6.5", "core-js-compat@^3.9.0", "core-js-compat@^3.9.1":
  "integrity" "sha1-LDAsRwhQX6cHKwrbUVbSb3gBoYs="
  "resolved" "https://registry.nlark.com/core-js-compat/download/core-js-compat-3.12.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcore-js-compat%2Fdownload%2Fcore-js-compat-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "browserslist" "^4.16.6"
    "semver" "7.0.0"

"core-js@^2.4.0":
  "integrity" "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw="
  "resolved" "https://registry.nlark.com/core-js/download/core-js-2.6.12.tgz"
  "version" "2.6.12"

"core-js@^3.11.0", "core-js@^3.4.3", "core-js@^3.6.5":
  "integrity" "sha1-a1r0/1VhbAikTThvH1EJF/8gQRI="
  "resolved" "https://registry.nlark.com/core-js/download/core-js-3.12.1.tgz?cache=0&sync_timestamp=1620508118283&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcore-js%2Fdownload%2Fcore-js-3.12.1.tgz"
  "version" "3.12.1"

"core-util-is@~1.0.0", "core-util-is@1.0.2":
  "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
  "resolved" "https://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@^5.0.0":
  "integrity" "sha1-BA9yaAnFked6F8CjYmykW08Wixo="
  "resolved" "https://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-5.2.1.tgz?cache=0&sync_timestamp=1596312863119&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"cosmiconfig@^7.0.0":
  "integrity" "sha1-75tE13OVnK5j3ezRIt4jhTtg+NM="
  "resolved" "https://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-7.0.0.tgz?cache=0&sync_timestamp=1596310657948&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.2.1"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.10.0"

"create-ecdh@^4.0.0":
  "integrity" "sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4="
  "resolved" "https://registry.npm.taobao.org/create-ecdh/download/create-ecdh-4.0.4.tgz?cache=0&sync_timestamp=1596557456448&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcreate-ecdh%2Fdownload%2Fcreate-ecdh-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.5.3"

"create-hash@^1.1.0", "create-hash@^1.1.2", "create-hash@^1.2.0":
  "integrity" "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY="
  "resolved" "https://registry.npm.taobao.org/create-hash/download/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.4", "create-hmac@^1.1.7":
  "integrity" "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8="
  "resolved" "https://registry.npm.taobao.org/create-hmac/download/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"cross-spawn@^5.0.1":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0":
  "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.0":
  "integrity" "sha1-9zqFudXUHQRVUcF34ogtSshXKKY="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"cross-spawn@^7.0.2":
  "integrity" "sha1-9zqFudXUHQRVUcF34ogtSshXKKY="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"cross-spawn@^7.0.3":
  "integrity" "sha1-9zqFudXUHQRVUcF34ogtSshXKKY="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypto-browserify@^3.11.0":
  "integrity" "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw="
  "resolved" "https://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"css-color-names@^0.0.4", "css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "https://registry.npm.taobao.org/css-color-names/download/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-declaration-sorter@^4.0.1":
  "integrity" "sha1-wZiUD2OnbX42wecQGLABchBUyyI="
  "resolved" "https://registry.nlark.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz?cache=0&sync_timestamp=1620754845858&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-declaration-sorter%2Fdownload%2Fcss-declaration-sorter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.1"
    "timsort" "^0.3.0"

"css-line-break@2.0.1":
  "integrity" "sha1-PcdMLtXrZCEUgCgZMkdXkCQ+czg="
  "resolved" "https://registry.nlark.com/css-line-break/download/css-line-break-2.0.1.tgz?cache=0&sync_timestamp=1628084316300&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-line-break%2Fdownload%2Fcss-line-break-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "base64-arraybuffer" "^0.2.0"

"css-loader@^3.5.3":
  "integrity" "sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU="
  "resolved" "https://registry.nlark.com/css-loader/download/css-loader-3.6.0.tgz?cache=0&sync_timestamp=1621514861415&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-loader%2Fdownload%2Fcss-loader-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "camelcase" "^5.3.1"
    "cssesc" "^3.0.0"
    "icss-utils" "^4.1.1"
    "loader-utils" "^1.2.3"
    "normalize-path" "^3.0.0"
    "postcss" "^7.0.32"
    "postcss-modules-extract-imports" "^2.0.0"
    "postcss-modules-local-by-default" "^3.0.2"
    "postcss-modules-scope" "^2.2.0"
    "postcss-modules-values" "^3.0.0"
    "postcss-value-parser" "^4.1.0"
    "schema-utils" "^2.7.0"
    "semver" "^6.3.0"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc="
  "resolved" "https://registry.npm.taobao.org/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^2.0.0", "css-select@^2.0.2":
  "integrity" "sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8="
  "resolved" "https://registry.npm.taobao.org/css-select/download/css-select-2.1.0.tgz?cache=0&sync_timestamp=1618566178339&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-select%2Fdownload%2Fcss-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-tree@^1.1.2":
  "integrity" "sha1-60hw+2/XcHMn7JXC/yqwm16NuR0="
  "resolved" "https://registry.npm.taobao.org/css-tree/download/css-tree-1.1.3.tgz?cache=0&sync_timestamp=1617191603409&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-tree%2Fdownload%2Fcss-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha1-mL69YsTB2flg7DQM+fdSLjBwmiI="
  "resolved" "https://registry.npm.taobao.org/css-tree/download/css-tree-1.0.0-alpha.37.tgz?cache=0&sync_timestamp=1617191603409&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-tree%2Fdownload%2Fcss-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-what@^3.2.1":
  "integrity" "sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ="
  "resolved" "https://registry.npm.taobao.org/css-what/download/css-what-3.4.2.tgz"
  "version" "3.4.2"

"cssesc@^3.0.0":
  "integrity" "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4="
  "resolved" "https://registry.npm.taobao.org/cssesc/download/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@^4.0.0", "cssnano-preset-default@^4.0.8":
  "integrity" "sha1-kgYisfwelaNOiDggPxOXpQTy0/8="
  "resolved" "https://registry.nlark.com/cssnano-preset-default/download/cssnano-preset-default-4.0.8.tgz?cache=0&sync_timestamp=1621620031436&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcssnano-preset-default%2Fdownload%2Fcssnano-preset-default-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "css-declaration-sorter" "^4.0.1"
    "cssnano-util-raw-cache" "^4.0.1"
    "postcss" "^7.0.0"
    "postcss-calc" "^7.0.1"
    "postcss-colormin" "^4.0.3"
    "postcss-convert-values" "^4.0.1"
    "postcss-discard-comments" "^4.0.2"
    "postcss-discard-duplicates" "^4.0.2"
    "postcss-discard-empty" "^4.0.1"
    "postcss-discard-overridden" "^4.0.1"
    "postcss-merge-longhand" "^4.0.11"
    "postcss-merge-rules" "^4.0.3"
    "postcss-minify-font-values" "^4.0.2"
    "postcss-minify-gradients" "^4.0.2"
    "postcss-minify-params" "^4.0.2"
    "postcss-minify-selectors" "^4.0.2"
    "postcss-normalize-charset" "^4.0.1"
    "postcss-normalize-display-values" "^4.0.2"
    "postcss-normalize-positions" "^4.0.2"
    "postcss-normalize-repeat-style" "^4.0.2"
    "postcss-normalize-string" "^4.0.2"
    "postcss-normalize-timing-functions" "^4.0.2"
    "postcss-normalize-unicode" "^4.0.1"
    "postcss-normalize-url" "^4.0.1"
    "postcss-normalize-whitespace" "^4.0.2"
    "postcss-ordered-values" "^4.1.2"
    "postcss-reduce-initial" "^4.0.3"
    "postcss-reduce-transforms" "^4.0.2"
    "postcss-svgo" "^4.0.3"
    "postcss-unique-selectors" "^4.0.1"

"cssnano-util-get-arguments@^4.0.0":
  "integrity" "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-get-match@^4.0.0":
  "integrity" "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-raw-cache@^4.0.1":
  "integrity" "sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"cssnano-util-same-parent@^4.0.0":
  "integrity" "sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz"
  "version" "4.0.1"

"cssnano@^4.0.0", "cssnano@^4.1.10":
  "integrity" "sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk="
  "resolved" "https://registry.nlark.com/cssnano/download/cssnano-4.1.11.tgz?cache=0&sync_timestamp=1621620031632&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcssnano%2Fdownload%2Fcssnano-4.1.11.tgz"
  "version" "4.1.11"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "cssnano-preset-default" "^4.0.8"
    "is-resolvable" "^1.0.0"
    "postcss" "^7.0.0"

"csso@^4.0.2":
  "integrity" "sha1-6jpWE0bo3J9UbW/r7dUBh884lSk="
  "resolved" "https://registry.npm.taobao.org/csso/download/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"cyclist@^1.0.1":
  "integrity" "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="
  "resolved" "https://registry.npm.taobao.org/cyclist/download/cyclist-1.0.1.tgz"
  "version" "1.0.1"

"dashdash@^1.12.0":
  "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
  "resolved" "https://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz?cache=0&sync_timestamp=1601073714105&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdashdash%2Fdownload%2Fdashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"dayjs@^1.10.6":
  "integrity" "sha512-AztC/IOW4L1Q41A86phW5Thhcrco3xuAA+YX/BLpLWWjRcTj5TOt/QImBLmCKlrF7u7k47arTnOyL6GnbG8Hvw=="
  "resolved" "https://registry.npmjs.org/dayjs/-/dayjs-1.10.6.tgz"
  "version" "1.10.6"

"de-indent@^1.0.2":
  "integrity" "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0="
  "resolved" "https://registry.npm.taobao.org/de-indent/download/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^2.2.0":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1607566548985&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1607566548985&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.6.8":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.nlark.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.nlark.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.1":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1607566548985&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.6":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1607566548985&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.7":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "https://registry.nlark.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1", "debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.1":
  "integrity" "sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-4.3.1.tgz?cache=0&sync_timestamp=1607566548985&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "ms" "2.1.2"

"debug@^4.3.2":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1607566548985&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1610348638646&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.npm.taobao.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@^1.0.1":
  "integrity" "sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o="
  "resolved" "https://registry.npm.taobao.org/deep-equal/download/deep-equal-1.1.1.tgz?cache=0&sync_timestamp=1606860166184&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@^0.1.3":
  "integrity" "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="
  "resolved" "https://registry.npm.taobao.org/deep-is/download/deep-is-0.1.3.tgz"
  "version" "0.1.3"

"deepmerge@^1.2.0", "deepmerge@^1.5.2":
  "integrity" "sha1-EEmdhohEza1P7ghC34x/bwyVp1M="
  "resolved" "https://registry.npm.taobao.org/deepmerge/download/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"deepmerge@^4.2.2":
  "integrity" "sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg=="
  "resolved" "https://registry.npmmirror.com/deepmerge/-/deepmerge-4.2.2.tgz"
  "version" "4.2.2"

"default-gateway@^4.2.0":
  "integrity" "sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs="
  "resolved" "https://registry.npm.taobao.org/default-gateway/download/default-gateway-4.2.0.tgz?cache=0&sync_timestamp=1610365791284&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdefault-gateway%2Fdownload%2Fdefault-gateway-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "execa" "^1.0.0"
    "ip-regex" "^2.1.0"

"default-gateway@^5.0.5":
  "integrity" "sha1-T9a9XShV05s0zFpZUFSG6ar8mxA="
  "resolved" "https://registry.npm.taobao.org/default-gateway/download/default-gateway-5.0.5.tgz?cache=0&sync_timestamp=1610365791284&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdefault-gateway%2Fdownload%2Fdefault-gateway-5.0.5.tgz"
  "version" "5.0.5"
  dependencies:
    "execa" "^3.3.0"

"defaults@^1.0.3":
  "integrity" "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730="
  "resolved" "https://registry.npm.taobao.org/defaults/download/defaults-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "clone" "^1.0.2"

"define-properties@^1.1.2", "define-properties@^1.1.3":
  "integrity" "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE="
  "resolved" "https://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
  "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"del@^4.1.1":
  "integrity" "sha1-no8RciLqRKMf86FWwEm5kFKp8LQ="
  "resolved" "https://registry.npm.taobao.org/del/download/del-4.1.1.tgz?cache=0&sync_timestamp=1601076817879&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdel%2Fdownload%2Fdel-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "@types/glob" "^7.1.1"
    "globby" "^6.1.0"
    "is-path-cwd" "^2.0.0"
    "is-path-in-cwd" "^2.0.0"
    "p-map" "^2.0.0"
    "pify" "^4.0.1"
    "rimraf" "^2.6.3"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegate@^3.1.2":
  "integrity" "sha1-tmtxwxWFIuirV0T3INjKDCr1kWY="
  "resolved" "https://registry.npm.taobao.org/delegate/download/delegate-3.2.0.tgz"
  "version" "3.2.0"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.npm.taobao.org/depd/download/depd-1.1.2.tgz"
  "version" "1.1.2"

"des.js@^1.0.0":
  "integrity" "sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM="
  "resolved" "https://registry.npm.taobao.org/des.js/download/des.js-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-node@^2.0.4":
  "integrity" "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE="
  "resolved" "https://registry.nlark.com/detect-node/download/detect-node-2.1.0.tgz?cache=0&sync_timestamp=1621146954463&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdetect-node%2Fdownload%2Fdetect-node-2.1.0.tgz"
  "version" "2.1.0"

"diffie-hellman@^5.0.0":
  "integrity" "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU="
  "resolved" "https://registry.npm.taobao.org/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dir-glob@^2.0.0", "dir-glob@^2.2.2":
  "integrity" "sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ="
  "resolved" "https://registry.npm.taobao.org/dir-glob/download/dir-glob-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "path-type" "^3.0.0"

"dns-equal@^1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://registry.npm.taobao.org/dns-equal/download/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo="
  "resolved" "https://registry.nlark.com/dns-packet/download/dns-packet-1.3.1.tgz?cache=0&sync_timestamp=1621447217966&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdns-packet%2Fdownload%2Fdns-packet-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://registry.npm.taobao.org/dns-txt/download/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"doctrine@^2.1.0":
  "integrity" "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850="
  "resolved" "https://registry.npm.taobao.org/doctrine/download/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE="
  "resolved" "https://registry.npm.taobao.org/doctrine/download/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-converter@^0.2":
  "integrity" "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g="
  "resolved" "https://registry.npm.taobao.org/dom-converter/download/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-serializer@0":
  "integrity" "sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E="
  "resolved" "https://registry.nlark.com/dom-serializer/download/dom-serializer-0.2.2.tgz?cache=0&sync_timestamp=1621256819522&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdom-serializer%2Fdownload%2Fdom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"dom-to-image@^2.6.0":
  "integrity" "sha1-ilA2CAiMh7HCL5A0rgMuGJiVWGc="
  "resolved" "https://registry.npm.taobao.org/dom-to-image/download/dom-to-image-2.6.0.tgz"
  "version" "2.6.0"

"dom7@^2.1.5":
  "integrity" "sha1-p5QRAXgAsx2EAAcM2uu/ySwfY3c="
  "resolved" "https://registry.nlark.com/dom7/download/dom7-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "ssr-window" "^2.0.0"

"domain-browser@^1.1.1":
  "integrity" "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto="
  "resolved" "https://registry.npm.taobao.org/domain-browser/download/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^1.3.1", "domelementtype@1":
  "integrity" "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8="
  "resolved" "https://registry.npm.taobao.org/domelementtype/download/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domelementtype@^2.0.1":
  "integrity" "sha1-mgtsJ4LtahxzI9QiZxg9+b2LHVc="
  "resolved" "https://registry.npm.taobao.org/domelementtype/download/domelementtype-2.2.0.tgz"
  "version" "2.2.0"

"domhandler@^2.3.0":
  "integrity" "sha1-iAUJfpM9ZehVRvcm1g9euItE+AM="
  "resolved" "https://registry.npm.taobao.org/domhandler/download/domhandler-2.4.2.tgz?cache=0&sync_timestamp=1618563983578&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomhandler%2Fdownload%2Fdomhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"domutils@^1.5.1", "domutils@^1.7.0":
  "integrity" "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="
  "resolved" "https://registry.npm.taobao.org/domutils/download/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"dot-prop@^5.2.0":
  "integrity" "sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog="
  "resolved" "https://registry.npm.taobao.org/dot-prop/download/dot-prop-5.3.0.tgz?cache=0&sync_timestamp=1605778235569&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdot-prop%2Fdownload%2Fdot-prop-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "is-obj" "^2.0.0"

"dotenv-expand@^5.1.0":
  "integrity" "sha1-P7rwIL/XlIhAcuomsel5HUWmKfA="
  "resolved" "https://registry.npm.taobao.org/dotenv-expand/download/dotenv-expand-5.1.0.tgz"
  "version" "5.1.0"

"dotenv@^8.2.0":
  "integrity" "sha1-Bhr2ZNGff02PxuT/m1hM4jety4s="
  "resolved" "https://registry.nlark.com/dotenv/download/dotenv-8.6.0.tgz"
  "version" "8.6.0"

"duplexer@^0.1.1":
  "integrity" "sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY="
  "resolved" "https://registry.npm.taobao.org/duplexer/download/duplexer-0.1.2.tgz?cache=0&sync_timestamp=1597220926027&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fduplexer%2Fdownload%2Fduplexer-0.1.2.tgz"
  "version" "0.1.2"

"duplexify@^3.4.2", "duplexify@^3.6.0":
  "integrity" "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk="
  "resolved" "https://registry.npm.taobao.org/duplexify/download/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"easy-stack@^1.0.1":
  "integrity" "sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY="
  "resolved" "https://registry.npm.taobao.org/easy-stack/download/easy-stack-1.0.1.tgz"
  "version" "1.0.1"

"ecc-jsbn@~0.1.1":
  "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
  "resolved" "https://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^2.6.1":
  "integrity" "sha1-SGYSh1c9zFPjZsehrlLDoSDuybo="
  "resolved" "https://registry.npm.taobao.org/ejs/download/ejs-2.7.4.tgz?cache=0&sync_timestamp=1612644037163&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fejs%2Fdownload%2Fejs-2.7.4.tgz"
  "version" "2.7.4"

"electron-to-chromium@^1.3.723":
  "integrity" "sha1-+hqGYPJ5BmIpHLITbw5EakRM39w="
  "resolved" "https://registry.nlark.com/electron-to-chromium/download/electron-to-chromium-1.3.735.tgz?cache=0&sync_timestamp=1621548213415&other_urls=https%3A%2F%2Fregistry.nlark.com%2Felectron-to-chromium%2Fdownload%2Felectron-to-chromium-1.3.735.tgz"
  "version" "1.3.735"

"element-ui@^2.15.6":
  "integrity" "sha512-rcYXEKd/j2G0AgficAOk1Zd1AsnHRkhmrK4yLHmNOiimU2JfsywgfKUjMoFuT6pQx0luhovj8lFjpE4Fnt58Iw=="
  "resolved" "https://registry.npmjs.org/element-ui/-/element-ui-2.15.6.tgz"
  "version" "2.15.6"
  dependencies:
    "async-validator" "~1.8.1"
    "babel-helper-vue-jsx-merge-props" "^2.0.0"
    "deepmerge" "^1.2.0"
    "normalize-wheel" "^1.0.1"
    "resize-observer-polyfill" "^1.5.0"
    "throttle-debounce" "^1.0.1"

"elliptic@^6.5.3":
  "integrity" "sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s="
  "resolved" "https://registry.npm.taobao.org/elliptic/download/elliptic-6.5.4.tgz?cache=0&sync_timestamp=1612290896983&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Felliptic%2Fdownload%2Felliptic-6.5.4.tgz"
  "version" "6.5.4"
  dependencies:
    "bn.js" "^4.11.9"
    "brorand" "^1.1.0"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.1"
    "inherits" "^2.0.4"
    "minimalistic-assert" "^1.0.1"
    "minimalistic-crypto-utils" "^1.0.1"

"emoji-regex@^7.0.1":
  "integrity" "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="
  "resolved" "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-7.0.3.tgz?cache=0&sync_timestamp=1614682725186&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Femoji-regex%2Fdownload%2Femoji-regex-7.0.3.tgz"
  "version" "7.0.3"

"emoji-regex@^8.0.0":
  "integrity" "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="
  "resolved" "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1614682725186&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^2.0.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "https://registry.npm.taobao.org/emojis-list/download/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"emojis-list@^3.0.0":
  "integrity" "sha1-VXBmIEatKeLpFucariYKvf9Pang="
  "resolved" "https://registry.npm.taobao.org/emojis-list/download/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0":
  "integrity" "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="
  "resolved" "https://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^0.9.1":
  "integrity" "sha1-TW5omzcl+GCQknzMhs2fFjW4ni4="
  "resolved" "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.2.0"
    "tapable" "^0.1.8"

"enhanced-resolve@^4.5.0":
  "integrity" "sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew="
  "resolved" "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.5.0"
    "tapable" "^1.0.0"

"enquirer@^2.3.5", "enquirer@^2.3.6":
  "integrity" "sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00="
  "resolved" "https://registry.npm.taobao.org/enquirer/download/enquirer-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "ansi-colors" "^4.1.1"

"entities@^1.1.1":
  "integrity" "sha1-vfpzUplmTfr9NFKe1PhSKidf6lY="
  "resolved" "https://registry.npm.taobao.org/entities/download/entities-1.1.2.tgz"
  "version" "1.1.2"

"entities@^2.0.0":
  "integrity" "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU="
  "resolved" "https://registry.npm.taobao.org/entities/download/entities-2.2.0.tgz"
  "version" "2.2.0"

"errno@^0.1.1", "errno@^0.1.3", "errno@~0.1.7":
  "integrity" "sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8="
  "resolved" "https://registry.npm.taobao.org/errno/download/errno-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.3.1":
  "integrity" "sha1-tKxAZIEH/c3PriQvQovqihTU8b8="
  "resolved" "https://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.2":
  "integrity" "sha1-WpmnB716TFinl5AtSNgoA+3mqtg="
  "resolved" "https://registry.npm.taobao.org/error-stack-parser/download/error-stack-parser-2.0.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ferror-stack-parser%2Fdownload%2Ferror-stack-parser-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "stackframe" "^1.1.1"

"es-abstract@^1.17.2", "es-abstract@^1.18.0-next.1", "es-abstract@^1.18.0-next.2":
  "integrity" "sha1-q4CzWe7Lft5MKYAAOQvFrD7HtaQ="
  "resolved" "https://registry.npm.taobao.org/es-abstract/download/es-abstract-1.18.0.tgz?cache=0&sync_timestamp=1614814465007&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fes-abstract%2Fdownload%2Fes-abstract-1.18.0.tgz"
  "version" "1.18.0"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.2"
    "is-callable" "^1.2.3"
    "is-negative-zero" "^2.0.1"
    "is-regex" "^1.1.2"
    "is-string" "^1.0.5"
    "object-inspect" "^1.9.0"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.2"
    "string.prototype.trimend" "^1.0.4"
    "string.prototype.trimstart" "^1.0.4"
    "unbox-primitive" "^1.0.0"

"es-abstract@^1.18.2":
  "integrity" "sha1-mxDefUwgajWB/VshJCM+BNtJrhk="
  "resolved" "https://registry.nlark.com/es-abstract/download/es-abstract-1.18.5.tgz"
  "version" "1.18.5"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.2"
    "internal-slot" "^1.0.3"
    "is-callable" "^1.2.3"
    "is-negative-zero" "^2.0.1"
    "is-regex" "^1.1.3"
    "is-string" "^1.0.6"
    "object-inspect" "^1.11.0"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.2"
    "string.prototype.trimend" "^1.0.4"
    "string.prototype.trimstart" "^1.0.4"
    "unbox-primitive" "^1.0.1"

"es-to-primitive@^1.2.1":
  "integrity" "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo="
  "resolved" "https://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"escalade@^3.1.1":
  "integrity" "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA="
  "resolved" "https://registry.npm.taobao.org/escalade/download/escalade-3.1.1.tgz?cache=0&sync_timestamp=1602567259580&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fescalade%2Fdownload%2Fescalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^4.0.0":
  "integrity" "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ="
  "resolved" "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz?cache=0&sync_timestamp=1618677243201&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fescape-string-regexp%2Fdownload%2Fescape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"eslint-config-airbnb-base@^14.0.0":
  "integrity" "sha1-ii6zhFXcWjElUBk7MZza7vBCzR4="
  "resolved" "https://registry.npm.taobao.org/eslint-config-airbnb-base/download/eslint-config-airbnb-base-14.2.1.tgz"
  "version" "14.2.1"
  dependencies:
    "confusing-browser-globals" "^1.0.10"
    "object.assign" "^4.1.2"
    "object.entries" "^1.1.2"

"eslint-import-resolver-node@^0.3.4", "eslint-import-resolver-node@^0.3.5":
  "integrity" "sha1-QEi5WDldqJZoJSAB29nsprg7rL0="
  "resolved" "https://registry.nlark.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.6.tgz?cache=0&sync_timestamp=1629046428620&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-import-resolver-node%2Fdownload%2Feslint-import-resolver-node-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "debug" "^3.2.7"
    "resolve" "^1.20.0"

"eslint-import-resolver-webpack@^0.13.0":
  "integrity" "sha1-bS+5KAkdry2kbvoeVoBVVVst6QI="
  "resolved" "https://registry.nlark.com/eslint-import-resolver-webpack/download/eslint-import-resolver-webpack-0.13.1.tgz?cache=0&sync_timestamp=1620972330256&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-import-resolver-webpack%2Fdownload%2Feslint-import-resolver-webpack-0.13.1.tgz"
  "version" "0.13.1"
  dependencies:
    "array-find" "^1.0.0"
    "debug" "^3.2.7"
    "enhanced-resolve" "^0.9.1"
    "find-root" "^1.1.0"
    "has" "^1.0.3"
    "interpret" "^1.4.0"
    "is-core-module" "^2.4.0"
    "is-regex" "^1.1.3"
    "lodash" "^4.17.21"
    "resolve" "^1.20.0"
    "semver" "^5.7.1"

"eslint-loader@^2.2.1":
  "integrity" "sha1-KLnBLaVAV68IReKmEScBova/gzc="
  "resolved" "https://registry.npm.taobao.org/eslint-loader/download/eslint-loader-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "loader-fs-cache" "^1.0.0"
    "loader-utils" "^1.0.2"
    "object-assign" "^4.0.1"
    "object-hash" "^1.1.4"
    "rimraf" "^2.6.1"

"eslint-module-utils@^2.6.2":
  "integrity" "sha1-lOVUDdFf4VIuj/o+yNs7f6fnpTQ="
  "resolved" "https://registry.nlark.com/eslint-module-utils/download/eslint-module-utils-2.6.2.tgz?cache=0&sync_timestamp=1628456039535&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-module-utils%2Fdownload%2Feslint-module-utils-2.6.2.tgz"
  "version" "2.6.2"
  dependencies:
    "debug" "^3.2.7"
    "pkg-dir" "^2.0.0"

"eslint-plugin-import@^2.21.2":
  "integrity" "sha1-aX/9Jj4k2l6E4DsoL1+2IlF3cXc="
  "resolved" "https://registry.nlark.com/eslint-plugin-import/download/eslint-plugin-import-2.24.0.tgz?cache=0&sync_timestamp=1628458245337&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-plugin-import%2Fdownload%2Feslint-plugin-import-2.24.0.tgz"
  "version" "2.24.0"
  dependencies:
    "array-includes" "^3.1.3"
    "array.prototype.flat" "^1.2.4"
    "debug" "^2.6.9"
    "doctrine" "^2.1.0"
    "eslint-import-resolver-node" "^0.3.5"
    "eslint-module-utils" "^2.6.2"
    "find-up" "^2.0.0"
    "has" "^1.0.3"
    "is-core-module" "^2.4.0"
    "minimatch" "^3.0.4"
    "object.values" "^1.1.3"
    "pkg-up" "^2.0.0"
    "read-pkg-up" "^3.0.0"
    "resolve" "^1.20.0"
    "tsconfig-paths" "^3.9.0"

"eslint-plugin-vue@^8.5.0":
  "integrity" "sha512-i1uHCTAKOoEj12RDvdtONWrGzjFm/djkzqfhmQ0d6M/W8KM81mhswd/z+iTZ0jCpdUedW3YRgcVfQ37/J4zoYQ=="
  "resolved" "https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-8.5.0.tgz"
  "version" "8.5.0"
  dependencies:
    "eslint-utils" "^3.0.0"
    "natural-compare" "^1.4.0"
    "semver" "^7.3.5"
    "vue-eslint-parser" "^8.0.1"

"eslint-scope@^4.0.3":
  "integrity" "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg="
  "resolved" "https://registry.npm.taobao.org/eslint-scope/download/eslint-scope-4.0.3.tgz?cache=0&sync_timestamp=1599933589759&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-scope%2Fdownload%2Feslint-scope-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-scope@^5.1.1":
  "integrity" "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw="
  "resolved" "https://registry.nlark.com/eslint-scope/download/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-scope@^7.0.0":
  "integrity" "sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw=="
  "resolved" "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-scope@~3.7.1":
  "integrity" "sha512-W+B0SvF4gamyCTmUc+uITPY0989iXVfKvhwtmJocTaYoc/3khEHmEmvfY/Gn9HA9VV75jrQECsHizkNw1b68FA=="
  "resolved" "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-utils@^2.1.0":
  "integrity" "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc="
  "resolved" "https://registry.nlark.com/eslint-utils/download/eslint-utils-2.1.0.tgz?cache=0&sync_timestamp=1620975524854&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-utils%2Fdownload%2Feslint-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-utils@^3.0.0":
  "integrity" "sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA=="
  "resolved" "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "eslint-visitor-keys" "^2.0.0"

"eslint-visitor-keys@^1.0.0", "eslint-visitor-keys@^1.1.0", "eslint-visitor-keys@^1.3.0":
  "integrity" "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="
  "resolved" "https://registry.nlark.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz?cache=0&sync_timestamp=1620088752356&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^2.0.0":
  "integrity" "sha1-9lMoJZMFknOSyTjtROsKXJsr0wM="
  "resolved" "https://registry.nlark.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz?cache=0&sync_timestamp=1624559054225&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint-visitor-keys@^3.1.0", "eslint-visitor-keys@^3.3.0":
  "integrity" "sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA=="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz"
  "version" "3.3.0"

"eslint@^7.32.0":
  "integrity" "sha1-xtMooUvj+wjI0dIeEsAv23oqgS0="
  "resolved" "https://registry.nlark.com/eslint/download/eslint-7.32.0.tgz"
  "version" "7.32.0"
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    "ajv" "^6.10.0"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "enquirer" "^2.3.5"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^2.1.0"
    "eslint-visitor-keys" "^2.0.0"
    "espree" "^7.3.1"
    "esquery" "^1.4.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.1.2"
    "globals" "^13.6.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.0.4"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.1"
    "progress" "^2.0.0"
    "regexpp" "^3.1.0"
    "semver" "^7.2.1"
    "strip-ansi" "^6.0.0"
    "strip-json-comments" "^3.1.0"
    "table" "^6.0.9"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^7.3.0", "espree@^7.3.1":
  "integrity" "sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y="
  "resolved" "https://registry.nlark.com/espree/download/espree-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "acorn" "^7.4.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^1.3.0"

"espree@^9.0.0":
  "integrity" "sha512-bvdyLmJMfwkV3NCRl5ZhJf22zBFo1y8bYh3VYb+bfzqNB4Je68P2sSuXyuFquzWLebHpNd2/d5uv7yoP9ISnGQ=="
  "resolved" "https://registry.npmmirror.com/espree/-/espree-9.3.1.tgz"
  "version" "9.3.1"
  dependencies:
    "acorn" "^8.7.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^3.3.0"

"esprima@^4.0.0":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0":
  "integrity" "sha1-IUj/w4uC6McFff7UhCWz5h8PJKU="
  "resolved" "https://registry.npm.taobao.org/esquery/download/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.1.0", "esrecurse@^4.3.0":
  "integrity" "sha1-eteWTWeauyi+5yzsY3WLHF0smSE="
  "resolved" "https://registry.npm.taobao.org/esrecurse/download/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="
  "resolved" "https://registry.npm.taobao.org/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1596641353460&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0":
  "integrity" "sha1-MH30JUfmzHMk088DwVXVzbjFOIA="
  "resolved" "https://registry.npm.taobao.org/estraverse/download/estraverse-5.2.0.tgz?cache=0&sync_timestamp=1596641353460&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-5.2.0.tgz"
  "version" "5.2.0"

"estraverse@^5.2.0":
  "integrity" "sha1-MH30JUfmzHMk088DwVXVzbjFOIA="
  "resolved" "https://registry.npm.taobao.org/estraverse/download/estraverse-5.2.0.tgz?cache=0&sync_timestamp=1596641353460&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-5.2.0.tgz"
  "version" "5.2.0"

"esutils@^2.0.2":
  "integrity" "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="
  "resolved" "https://registry.npm.taobao.org/esutils/download/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-pubsub@4.3.0":
  "integrity" "sha1-9o2Ba8KfHsAsU53FjI3UDOcss24="
  "resolved" "https://registry.npm.taobao.org/event-pubsub/download/event-pubsub-4.3.0.tgz?cache=0&sync_timestamp=1606361593712&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fevent-pubsub%2Fdownload%2Fevent-pubsub-4.3.0.tgz"
  "version" "4.3.0"

"eventemitter3@^4.0.0":
  "integrity" "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="
  "resolved" "https://registry.npm.taobao.org/eventemitter3/download/eventemitter3-4.0.7.tgz?cache=0&sync_timestamp=1598517790184&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feventemitter3%2Fdownload%2Feventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.0.0":
  "integrity" "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA="
  "resolved" "https://registry.npm.taobao.org/events/download/events-3.3.0.tgz"
  "version" "3.3.0"

"eventsource@^1.0.7":
  "integrity" "sha1-AOjKfJIQnpSw3fMtrGd9hBAoz68="
  "resolved" "https://registry.npm.taobao.org/eventsource/download/eventsource-1.1.0.tgz?cache=0&sync_timestamp=1616041700200&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feventsource%2Fdownload%2Feventsource-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "original" "^1.0.0"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI="
  "resolved" "https://registry.npm.taobao.org/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"execa@^0.8.0":
  "integrity" "sha1-2NdrvBtVIX7RkP1t1J08d07PyNo="
  "resolved" "https://registry.npm.taobao.org/execa/download/execa-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^1.0.0":
  "integrity" "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg="
  "resolved" "https://registry.npm.taobao.org/execa/download/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^3.3.0":
  "integrity" "sha1-wI7UVQ72XYWPrCaf/IVyRG8364k="
  "resolved" "https://registry.npm.taobao.org/execa/download/execa-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "p-finally" "^2.0.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"execa@^5.0.0":
  "integrity" "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0="
  "resolved" "https://registry.nlark.com/execa/download/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"express@^4.16.3", "express@^4.17.1":
  "integrity" "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ="
  "resolved" "https://registry.npm.taobao.org/express/download/express-4.17.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexpress%2Fdownload%2Fexpress-4.17.1.tgz"
  "version" "4.17.1"
  dependencies:
    "accepts" "~1.3.7"
    "array-flatten" "1.1.1"
    "body-parser" "1.19.0"
    "content-disposition" "0.5.3"
    "content-type" "~1.0.4"
    "cookie" "0.4.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "~1.1.2"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.5"
    "qs" "6.7.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.1.2"
    "send" "0.17.1"
    "serve-static" "1.14.1"
    "setprototypeof" "1.1.1"
    "statuses" "~1.5.0"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@~3.0.2":
  "integrity" "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="
  "resolved" "https://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@^3.0.3":
  "integrity" "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU="
  "resolved" "https://registry.npm.taobao.org/external-editor/download/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "^0.7.0"
    "iconv-lite" "^0.4.24"
    "tmp" "^0.0.33"

"extglob@^2.0.4":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "https://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
  "resolved" "https://registry.npm.taobao.org/extsprintf/download/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="
  "resolved" "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-glob@^2.2.6":
  "integrity" "sha1-aVOFfDr6R1//ku5gFdUtpwpM050="
  "resolved" "https://registry.npm.taobao.org/fast-glob/download/fast-glob-2.2.7.tgz?cache=0&sync_timestamp=1610876574130&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-glob%2Fdownload%2Ffast-glob-2.2.7.tgz"
  "version" "2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    "glob-parent" "^3.1.0"
    "is-glob" "^4.0.0"
    "merge2" "^1.2.3"
    "micromatch" "^3.1.10"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="
  "resolved" "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"faye-websocket@^0.11.3":
  "integrity" "sha1-XA6aiWjokSwoZjn96XeosgnyUI4="
  "resolved" "https://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.11.3.tgz"
  "version" "0.11.3"
  dependencies:
    "websocket-driver" ">=0.5.1"

"figgy-pudding@^3.5.1":
  "integrity" "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4="
  "resolved" "https://registry.npm.taobao.org/figgy-pudding/download/figgy-pudding-3.5.2.tgz"
  "version" "3.5.2"

"figures@^3.0.0":
  "integrity" "sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8="
  "resolved" "https://registry.npm.taobao.org/figures/download/figures-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^6.0.1":
  "integrity" "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc="
  "resolved" "https://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-6.0.1.tgz?cache=0&sync_timestamp=1613794357372&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffile-entry-cache%2Fdownload%2Ffile-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"file-loader@^4.2.0":
  "integrity" "sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8="
  "resolved" "https://registry.npm.taobao.org/file-loader/download/file-loader-4.3.0.tgz?cache=0&sync_timestamp=1603816876316&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffile-loader%2Fdownload%2Ffile-loader-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "loader-utils" "^1.2.3"
    "schema-utils" "^2.5.0"

"file-uri-to-path@1.0.0":
  "integrity" "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90="
  "resolved" "https://registry.npm.taobao.org/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
  "version" "1.0.0"

"filesize@^3.6.1":
  "integrity" "sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc="
  "resolved" "https://registry.nlark.com/filesize/download/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://registry.npm.taobao.org/fill-range/download/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha1-GRmmp8df44ssfHflGYU12prN2kA="
  "resolved" "https://registry.npm.taobao.org/fill-range/download/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@~1.1.2":
  "integrity" "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0="
  "resolved" "https://registry.npm.taobao.org/finalhandler/download/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^0.1.1":
  "integrity" "sha1-yN765XyKUqinhPnjHFfHQumToLk="
  "resolved" "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "commondir" "^1.0.1"
    "mkdirp" "^0.5.1"
    "pkg-dir" "^1.0.0"

"find-cache-dir@^2.1.0":
  "integrity" "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc="
  "resolved" "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^2.0.0"
    "pkg-dir" "^3.0.0"

"find-cache-dir@^3.0.0", "find-cache-dir@^3.3.1":
  "integrity" "sha1-ibM/rUpGcNqpT4Vff74x1thP6IA="
  "resolved" "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-root@^1.1.0":
  "integrity" "sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ="
  "resolved" "https://registry.npm.taobao.org/find-root/download/find-root-1.1.0.tgz"
  "version" "1.1.0"

"find-up@^1.0.0":
  "integrity" "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-1.1.2.tgz?cache=0&sync_timestamp=1597169862146&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"find-up@^2.0.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz?cache=0&sync_timestamp=1597169795121&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz?cache=0&sync_timestamp=1597169795121&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^3.0.0":
  "integrity" "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-3.0.0.tgz?cache=0&sync_timestamp=1597169862146&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.0.0":
  "integrity" "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-4.1.0.tgz?cache=0&sync_timestamp=1597169862146&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^3.0.4":
  "integrity" "sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE="
  "resolved" "https://registry.npm.taobao.org/flat-cache/download/flat-cache-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "flatted" "^3.1.0"
    "rimraf" "^3.0.2"

"flatted@^3.1.0":
  "integrity" "sha1-ZL/tXLaP48p4s+shStl7Y77c5WE="
  "resolved" "https://registry.nlark.com/flatted/download/flatted-3.2.2.tgz"
  "version" "3.2.2"

"flush-write-stream@^1.0.0":
  "integrity" "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug="
  "resolved" "https://registry.npm.taobao.org/flush-write-stream/download/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"follow-redirects@^1.0.0", "follow-redirects@^1.10.0":
  "integrity" "sha1-2RFN7Qoc/dM04WTmZirQK/2R/0M="
  "resolved" "https://registry.nlark.com/follow-redirects/download/follow-redirects-1.14.1.tgz?cache=0&sync_timestamp=1620555234886&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffollow-redirects%2Fdownload%2Ffollow-redirects-1.14.1.tgz"
  "version" "1.14.1"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz"
  "version" "1.0.2"

"forever-agent@~0.6.1":
  "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
  "resolved" "https://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@~2.3.2":
  "integrity" "sha1-3M5SwF9kTymManq5Nr1yTO/786Y="
  "resolved" "https://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz?cache=0&sync_timestamp=1613411617006&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fform-data%2Fdownload%2Fform-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"forwarded@~0.1.2":
  "integrity" "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="
  "resolved" "https://registry.npm.taobao.org/forwarded/download/forwarded-0.1.2.tgz"
  "version" "0.1.2"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://registry.npm.taobao.org/fragment-cache/download/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz"
  "version" "0.5.2"

"from2@^2.1.0":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "https://registry.npm.taobao.org/from2/download/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-extra@^7.0.1":
  "integrity" "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk="
  "resolved" "https://registry.nlark.com/fs-extra/download/fs-extra-7.0.1.tgz?cache=0&sync_timestamp=1620079845472&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffs-extra%2Fdownload%2Ffs-extra-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "https://registry.npm.taobao.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^1.2.7":
  "integrity" "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg="
  "resolved" "https://registry.npm.taobao.org/fsevents/download/fsevents-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "bindings" "^1.5.0"
    "nan" "^2.12.1"

"fsevents@~2.3.1":
  "integrity" "sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro="
  "resolved" "https://registry.npm.taobao.org/fsevents/download/fsevents-2.3.2.tgz"
  "version" "2.3.2"

"function-bind@^1.1.1":
  "integrity" "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="
  "resolved" "https://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://registry.npm.taobao.org/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"gensync@^1.0.0-beta.2":
  "integrity" "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA="
  "resolved" "https://registry.npm.taobao.org/gensync/download/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="
  "resolved" "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.0", "get-intrinsic@^1.1.1":
  "integrity" "sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y="
  "resolved" "https://registry.npm.taobao.org/get-intrinsic/download/get-intrinsic-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"

"get-own-enumerable-property-symbols@^3.0.0":
  "integrity" "sha1-tf3nfyLL4185C04ImSLFC85u9mQ="
  "resolved" "https://registry.npm.taobao.org/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz"
  "version" "3.0.2"

"get-stream@^3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz?cache=0&sync_timestamp=1618462718162&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stream%2Fdownload%2Fget-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
  "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-4.1.0.tgz?cache=0&sync_timestamp=1618462718162&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stream%2Fdownload%2Fget-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^5.0.0":
  "integrity" "sha1-SWaheV7lrOZecGxLe+txJX1uItM="
  "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-5.2.0.tgz?cache=0&sync_timestamp=1618462718162&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stream%2Fdownload%2Fget-stream-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^6.0.0":
  "integrity" "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c="
  "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-6.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stream%2Fdownload%2Fget-stream-6.0.1.tgz"
  "version" "6.0.1"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://registry.npm.taobao.org/get-value/download/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getpass@^0.1.1":
  "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
  "resolved" "https://registry.npm.taobao.org/getpass/download/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-parent@^3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "https://registry.nlark.com/glob-parent/download/glob-parent-3.1.0.tgz?cache=0&sync_timestamp=1620073303944&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob-parent%2Fdownload%2Fglob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob-parent@^5.1.2", "glob-parent@~5.1.0":
  "integrity" "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ="
  "resolved" "https://registry.nlark.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1620073303944&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-to-regexp@^0.3.0":
  "integrity" "sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs="
  "resolved" "https://registry.npm.taobao.org/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"
  "version" "0.3.0"

"glob@^7.0.3", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4":
  "integrity" "sha1-Oxk+kjPwHULQs/eClLvutBj5SpA="
  "resolved" "https://registry.nlark.com/glob/download/glob-7.1.7.tgz?cache=0&sync_timestamp=1620337498129&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob%2Fdownload%2Fglob-7.1.7.tgz"
  "version" "7.1.7"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^11.1.0":
  "integrity" "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="
  "resolved" "https://registry.npm.taobao.org/globals/download/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.6.0":
  "integrity" "sha1-QO9njaEX/nvS4o8fqySVG9AlW+c="
  "resolved" "https://registry.nlark.com/globals/download/globals-13.11.0.tgz?cache=0&sync_timestamp=1628810148451&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobals%2Fdownload%2Fglobals-13.11.0.tgz"
  "version" "13.11.0"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^13.9.0":
  "integrity" "sha1-QO9njaEX/nvS4o8fqySVG9AlW+c="
  "resolved" "https://registry.nlark.com/globals/download/globals-13.11.0.tgz?cache=0&sync_timestamp=1628810148451&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobals%2Fdownload%2Fglobals-13.11.0.tgz"
  "version" "13.11.0"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^9.18.0":
  "integrity" "sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo="
  "resolved" "https://registry.nlark.com/globals/download/globals-9.18.0.tgz?cache=0&sync_timestamp=1628810148451&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobals%2Fdownload%2Fglobals-9.18.0.tgz"
  "version" "9.18.0"

"globby@^6.1.0":
  "integrity" "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw="
  "resolved" "https://registry.npm.taobao.org/globby/download/globby-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "array-union" "^1.0.1"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"globby@^7.1.1":
  "integrity" "sha1-+yzP+UAfhgCUXfral0QMypcrhoA="
  "resolved" "https://registry.npm.taobao.org/globby/download/globby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"globby@^9.2.0":
  "integrity" "sha1-/QKacGxwPSm90XD0tts6P3p8tj0="
  "resolved" "https://registry.npm.taobao.org/globby/download/globby-9.2.0.tgz"
  "version" "9.2.0"
  dependencies:
    "@types/glob" "^7.1.1"
    "array-union" "^1.0.2"
    "dir-glob" "^2.2.2"
    "fast-glob" "^2.2.6"
    "glob" "^7.1.3"
    "ignore" "^4.0.3"
    "pify" "^4.0.1"
    "slash" "^2.0.0"

"good-listener@^1.2.2":
  "integrity" "sha1-1TswzfkxPf+33JoNR3CWqm0UXFA="
  "resolved" "https://registry.npm.taobao.org/good-listener/download/good-listener-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "delegate" "^3.1.2"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.15", "graceful-fs@^4.1.2", "graceful-fs@^4.1.6":
  "integrity" "sha1-/wQLKwhTsjw9MQJ1I3BvGIXXa+4="
  "resolved" "https://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.2.6.tgz"
  "version" "4.2.6"

"gzip-size@^5.0.0":
  "integrity" "sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ="
  "resolved" "https://registry.npm.taobao.org/gzip-size/download/gzip-size-5.1.1.tgz?cache=0&sync_timestamp=1605523270757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgzip-size%2Fdownload%2Fgzip-size-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "duplexer" "^0.1.1"
    "pify" "^4.0.1"

"handle-thing@^2.0.0":
  "integrity" "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04="
  "resolved" "https://registry.npm.taobao.org/handle-thing/download/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"har-schema@^2.0.0":
  "integrity" "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="
  "resolved" "https://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.3":
  "integrity" "sha1-HwgDufjLIMD6E4It8ezds2veHv0="
  "resolved" "https://registry.npm.taobao.org/har-validator/download/har-validator-5.1.5.tgz?cache=0&sync_timestamp=1596082605533&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhar-validator%2Fdownload%2Fhar-validator-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "ajv" "^6.12.3"
    "har-schema" "^2.0.0"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://registry.nlark.com/has-ansi/download/has-ansi-2.0.0.tgz?cache=0&sync_timestamp=1631556755105&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-ansi%2Fdownload%2Fhas-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-bigints@^1.0.1":
  "integrity" "sha1-ZP5qywIGc+O3jbA1pa9pqp0HsRM="
  "resolved" "https://registry.npm.taobao.org/has-bigints/download/has-bigints-1.0.1.tgz?cache=0&sync_timestamp=1615461293395&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhas-bigints%2Fdownload%2Fhas-bigints-1.0.1.tgz"
  "version" "1.0.1"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhas-flag%2Fdownload%2Fhas-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.1", "has-symbols@^1.0.2":
  "integrity" "sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM="
  "resolved" "https://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.2.tgz?cache=0&sync_timestamp=1614443617831&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhas-symbols%2Fdownload%2Fhas-symbols-1.0.2.tgz"
  "version" "1.0.2"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://registry.npm.taobao.org/has-value/download/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://registry.npm.taobao.org/has-value/download/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.0", "has@^1.0.3":
  "integrity" "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="
  "resolved" "https://registry.npm.taobao.org/has/download/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM="
  "resolved" "https://registry.npm.taobao.org/hash-base/download/hash-base-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "inherits" "^2.0.4"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"hash-sum@^1.0.2":
  "integrity" "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ="
  "resolved" "https://registry.npm.taobao.org/hash-sum/download/hash-sum-1.0.2.tgz?cache=0&sync_timestamp=1586263945599&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhash-sum%2Fdownload%2Fhash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash-sum@^2.0.0":
  "integrity" "sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo="
  "resolved" "https://registry.npm.taobao.org/hash-sum/download/hash-sum-2.0.0.tgz?cache=0&sync_timestamp=1586263945599&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhash-sum%2Fdownload%2Fhash-sum-2.0.0.tgz"
  "version" "2.0.0"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I="
  "resolved" "https://registry.npm.taobao.org/hash.js/download/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"he@^1.1.0", "he@1.2.x":
  "integrity" "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="
  "resolved" "https://registry.npm.taobao.org/he/download/he-1.2.0.tgz"
  "version" "1.2.0"

"hex-color-regex@^1.1.0":
  "integrity" "sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4="
  "resolved" "https://registry.npm.taobao.org/hex-color-regex/download/hex-color-regex-1.1.0.tgz"
  "version" "1.1.0"

"highlight.js@^10.7.1":
  "integrity" "sha1-iTGbhh7cZsSIVO0ebaIeqJ+Ec2A="
  "resolved" "https://registry.nlark.com/highlight.js/download/highlight.js-10.7.2.tgz"
  "version" "10.7.2"

"hmac-drbg@^1.0.1":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "https://registry.npm.taobao.org/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"hoopy@^0.1.4":
  "integrity" "sha1-YJIH1mEQADOpqUAq096mdzgcGx0="
  "resolved" "https://registry.npm.taobao.org/hoopy/download/hoopy-0.1.4.tgz"
  "version" "0.1.4"

"hosted-git-info@^2.1.4":
  "integrity" "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k="
  "resolved" "https://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.8.9.tgz?cache=0&sync_timestamp=1617826790271&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhosted-git-info%2Fdownload%2Fhosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hpack.js@^2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://registry.npm.taobao.org/hpack.js/download/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"hsl-regex@^1.0.0":
  "integrity" "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4="
  "resolved" "https://registry.npm.taobao.org/hsl-regex/download/hsl-regex-1.0.0.tgz"
  "version" "1.0.0"

"hsla-regex@^1.0.0":
  "integrity" "sha1-wc56MWjIxmFAM6S194d/OyJfnDg="
  "resolved" "https://registry.npm.taobao.org/hsla-regex/download/hsla-regex-1.0.0.tgz"
  "version" "1.0.0"

"html-entities@^1.3.1":
  "integrity" "sha1-z70bAdKvr5rcobEK59/6uYxx0tw="
  "resolved" "https://registry.npm.taobao.org/html-entities/download/html-entities-1.4.0.tgz?cache=0&sync_timestamp=1617031494718&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtml-entities%2Fdownload%2Fhtml-entities-1.4.0.tgz"
  "version" "1.4.0"

"html-image-compress@^1.1.0":
  "integrity" "sha512-ggLnOHPZBCrIC4je8YXIY1SmKt6wn/3XgOVGzsUs0Y/L1Qlk5QMxoPGuw86i3oqrfaRdIWi8fmaru/wG9OAzhQ=="
  "resolved" "https://registry.npmmirror.com/html-image-compress/-/html-image-compress-1.1.0.tgz"
  "version" "1.1.0"

"html-minifier@^3.2.3":
  "integrity" "sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw="
  "resolved" "https://registry.npm.taobao.org/html-minifier/download/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-tags@^2.0.0":
  "integrity" "sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos="
  "resolved" "https://registry.npm.taobao.org/html-tags/download/html-tags-2.0.0.tgz"
  "version" "2.0.0"

"html-tags@^3.1.0":
  "integrity" "sha1-e15vfmZen7QfMAB+2eDUHpf7IUA="
  "resolved" "https://registry.npm.taobao.org/html-tags/download/html-tags-3.1.0.tgz"
  "version" "3.1.0"

"html-webpack-plugin@^3.2.0":
  "integrity" "sha1-sBq71yOsqqeze2r0SS69oD2d03s="
  "resolved" "https://registry.npm.taobao.org/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "tapable" "^1.0.0"
    "toposort" "^1.0.0"
    "util.promisify" "1.0.0"

"html2canvas@^1.3.2":
  "integrity" "sha1-lRzIOIo86Tn9rAITEAfuKBJK/Cc="
  "resolved" "https://registry.nlark.com/html2canvas/download/html2canvas-1.3.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhtml2canvas%2Fdownload%2Fhtml2canvas-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "css-line-break" "2.0.1"
    "text-segmentation" "^1.0.2"

"htmlparser2@^3.10.1":
  "integrity" "sha1-vWedw/WYl7ajS7EHSchVu1OpOS8="
  "resolved" "https://registry.npm.taobao.org/htmlparser2/download/htmlparser2-3.10.1.tgz?cache=0&sync_timestamp=1617915295732&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "domelementtype" "^1.3.1"
    "domhandler" "^2.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^3.1.1"

"http-deceiver@^1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://registry.npm.taobao.org/http-deceiver/download/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://registry.npm.taobao.org/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1593407647372&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@~1.7.2", "http-errors@1.7.2":
  "integrity" "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8="
  "resolved" "https://registry.npm.taobao.org/http-errors/download/http-errors-1.7.2.tgz?cache=0&sync_timestamp=1593407647372&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.2.tgz"
  "version" "1.7.2"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.1"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.0"

"http-parser-js@>=0.5.1":
  "integrity" "sha1-AdJwnHnUFpi7AdTezF6dpOSgM9k="
  "resolved" "https://registry.npm.taobao.org/http-parser-js/download/http-parser-js-0.5.3.tgz"
  "version" "0.5.3"

"http-proxy-middleware@^1.0.0":
  "integrity" "sha1-Q3ANbZ7st0Gb8IahKND3IF2etmU="
  "resolved" "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "@types/http-proxy" "^1.17.5"
    "http-proxy" "^1.18.1"
    "is-glob" "^4.0.1"
    "is-plain-obj" "^3.0.0"
    "micromatch" "^4.0.2"

"http-proxy-middleware@0.19.1":
  "integrity" "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo="
  "resolved" "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "http-proxy" "^1.17.0"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.11"
    "micromatch" "^3.1.10"

"http-proxy@^1.17.0", "http-proxy@^1.18.1":
  "integrity" "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk="
  "resolved" "https://registry.npm.taobao.org/http-proxy/download/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.2.0":
  "integrity" "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE="
  "resolved" "https://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1600868470262&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-browserify@^1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "https://registry.npm.taobao.org/https-browserify/download/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"human-signals@^1.1.1":
  "integrity" "sha1-xbHNFPUK6uCatsWf5jujOV/k36M="
  "resolved" "https://registry.npm.taobao.org/human-signals/download/human-signals-1.1.1.tgz"
  "version" "1.1.1"

"human-signals@^2.1.0":
  "integrity" "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA="
  "resolved" "https://registry.nlark.com/human-signals/download/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"husky@^7.0.1":
  "integrity" "sha1-V59BgLXaRSAmPocTzIMpQrSOHxw="
  "resolved" "https://registry.nlark.com/husky/download/husky-7.0.1.tgz?cache=0&sync_timestamp=1625567269908&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhusky%2Fdownload%2Fhusky-7.0.1.tgz"
  "version" "7.0.1"

"iconv-lite@^0.4.24", "iconv-lite@0.4.24":
  "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
  "resolved" "https://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&sync_timestamp=1594184278451&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-utils@^4.0.0", "icss-utils@^4.1.1":
  "integrity" "sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc="
  "resolved" "https://registry.npm.taobao.org/icss-utils/download/icss-utils-4.1.1.tgz?cache=0&sync_timestamp=1605801297051&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficss-utils%2Fdownload%2Ficss-utils-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "postcss" "^7.0.14"

"ieee754@^1.1.4":
  "integrity" "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I="
  "resolved" "https://registry.npm.taobao.org/ieee754/download/ieee754-1.2.1.tgz?cache=0&sync_timestamp=1603838235461&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fieee754%2Fdownload%2Fieee754-1.2.1.tgz"
  "version" "1.2.1"

"iferr@^0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "https://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore@^3.3.5":
  "integrity" "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM="
  "resolved" "https://registry.npm.taobao.org/ignore/download/ignore-3.3.10.tgz"
  "version" "3.3.10"

"ignore@^4.0.3", "ignore@^4.0.6":
  "integrity" "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="
  "resolved" "https://registry.npm.taobao.org/ignore/download/ignore-4.0.6.tgz"
  "version" "4.0.6"

"image-size@~0.5.0":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w="
  "resolved" "https://registry.nlark.com/image-size/download/image-size-0.5.5.tgz"
  "version" "0.5.5"

"import-cwd@^2.0.0":
  "integrity" "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk="
  "resolved" "https://registry.npm.taobao.org/import-cwd/download/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "^2.1.0"

"import-fresh@^2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "https://registry.npm.taobao.org/import-fresh/download/import-fresh-2.0.0.tgz?cache=0&sync_timestamp=1608469520474&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-fresh@^3.0.0":
  "integrity" "sha1-NxYsJfy566oublPVtNiM4X2eDCs="
  "resolved" "https://registry.npm.taobao.org/import-fresh/download/import-fresh-3.3.0.tgz?cache=0&sync_timestamp=1608469485280&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-fresh@^3.2.1":
  "integrity" "sha1-NxYsJfy566oublPVtNiM4X2eDCs="
  "resolved" "https://registry.npm.taobao.org/import-fresh/download/import-fresh-3.3.0.tgz?cache=0&sync_timestamp=1608469485280&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-from@^2.1.0":
  "integrity" "sha1-M1238qev/VOqpHHUuAId7ja387E="
  "resolved" "https://registry.npm.taobao.org/import-from/download/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "^3.0.0"

"import-local@^2.0.0":
  "integrity" "sha1-VQcL44pZk88Y72236WH1vuXFoJ0="
  "resolved" "https://registry.npm.taobao.org/import-local/download/import-local-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pkg-dir" "^3.0.0"
    "resolve-cwd" "^2.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE="
  "resolved" "https://registry.nlark.com/indent-string/download/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"indexes-of@^1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "https://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"infer-owner@^1.0.3":
  "integrity" "sha1-xM78qo5RBRwqQLos6KPScpWvlGc="
  "resolved" "https://registry.npm.taobao.org/infer-owner/download/infer-owner-1.0.4.tgz"
  "version" "1.0.4"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2":
  "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
  "resolved" "https://registry.npm.taobao.org/inherits/download/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "https://registry.npm.taobao.org/inherits/download/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz"
  "version" "2.0.3"

"inquirer@^7.1.0":
  "integrity" "sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM="
  "resolved" "https://registry.nlark.com/inquirer/download/inquirer-7.3.3.tgz"
  "version" "7.3.3"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-width" "^3.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.19"
    "mute-stream" "0.0.8"
    "run-async" "^2.4.0"
    "rxjs" "^6.6.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "through" "^2.3.6"

"internal-ip@^4.3.0":
  "integrity" "sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc="
  "resolved" "https://registry.npm.taobao.org/internal-ip/download/internal-ip-4.3.0.tgz?cache=0&sync_timestamp=1605885503627&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finternal-ip%2Fdownload%2Finternal-ip-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "default-gateway" "^4.2.0"
    "ipaddr.js" "^1.9.0"

"internal-slot@^1.0.3":
  "integrity" "sha1-c0fjB97uovqsKsYgXUvH00ln9Zw="
  "resolved" "https://registry.nlark.com/internal-slot/download/internal-slot-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "get-intrinsic" "^1.1.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"interpret@^1.4.0":
  "integrity" "sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4="
  "resolved" "https://registry.npm.taobao.org/interpret/download/interpret-1.4.0.tgz"
  "version" "1.4.0"

"invariant@^2.2.2":
  "integrity" "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY="
  "resolved" "https://registry.npm.taobao.org/invariant/download/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"ip-regex@^2.1.0":
  "integrity" "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk="
  "resolved" "https://registry.npm.taobao.org/ip-regex/download/ip-regex-2.1.0.tgz?cache=0&sync_timestamp=1611327032630&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fip-regex%2Fdownload%2Fip-regex-2.1.0.tgz"
  "version" "2.1.0"

"ip@^1.1.0", "ip@^1.1.5":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.npm.taobao.org/ip/download/ip-1.1.5.tgz"
  "version" "1.1.5"

"ipaddr.js@^1.9.0", "ipaddr.js@1.9.1":
  "integrity" "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="
  "resolved" "https://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute-url@^2.0.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-absolute-url@^3.0.3":
  "integrity" "sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg="
  "resolved" "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-3.0.3.tgz"
  "version" "3.0.3"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
  "resolved" "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.0.4":
  "integrity" "sha1-YjUwMd++4HzrNGVqa95Z7+yujdk="
  "resolved" "https://registry.npm.taobao.org/is-arguments/download/is-arguments-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bind" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM="
  "resolved" "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-bigint@^1.0.1":
  "integrity" "sha1-/7OBRCUDI1rSReqJ5Fs9v/BA7lo="
  "resolved" "https://registry.nlark.com/is-bigint/download/is-bigint-1.0.2.tgz?cache=0&sync_timestamp=1620161578872&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-bigint%2Fdownload%2Fis-bigint-1.0.2.tgz"
  "version" "1.0.2"

"is-binary-path@^1.0.0":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "https://registry.npm.taobao.org/is-binary-path/download/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-binary-path@~2.1.0":
  "integrity" "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk="
  "resolved" "https://registry.npm.taobao.org/is-binary-path/download/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-boolean-object@^1.1.0":
  "integrity" "sha1-PAh48DXLghIo01DS4eNnGXFqPeg="
  "resolved" "https://registry.nlark.com/is-boolean-object/download/is-boolean-object-1.1.1.tgz?cache=0&sync_timestamp=1620428521406&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-boolean-object%2Fdownload%2Fis-boolean-object-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"

"is-buffer@^1.1.5":
  "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
  "resolved" "https://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.4", "is-callable@^1.2.3":
  "integrity" "sha1-ix4FALc6HXbHBIdjbzaOUZ3o244="
  "resolved" "https://registry.npm.taobao.org/is-callable/download/is-callable-1.2.3.tgz?cache=0&sync_timestamp=1612132958731&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-callable%2Fdownload%2Fis-callable-1.2.3.tgz"
  "version" "1.2.3"

"is-ci@^1.0.10":
  "integrity" "sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw="
  "resolved" "https://registry.npm.taobao.org/is-ci/download/is-ci-1.2.1.tgz?cache=0&sync_timestamp=1613632130398&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-ci%2Fdownload%2Fis-ci-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ci-info" "^1.5.0"

"is-color-stop@^1.0.0":
  "integrity" "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U="
  "resolved" "https://registry.npm.taobao.org/is-color-stop/download/is-color-stop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-color-names" "^0.0.4"
    "hex-color-regex" "^1.1.0"
    "hsl-regex" "^1.0.0"
    "hsla-regex" "^1.0.0"
    "rgb-regex" "^1.0.1"
    "rgba-regex" "^1.0.0"

"is-core-module@^2.2.0", "is-core-module@^2.4.0":
  "integrity" "sha1-jp/I4VAnsBFBgCbpjw5vTYYwXME="
  "resolved" "https://registry.nlark.com/is-core-module/download/is-core-module-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "has" "^1.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
  "resolved" "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha1-VQz8wDr62gXuo90wmBx7CVUfc+U="
  "resolved" "https://registry.nlark.com/is-date-object/download/is-date-object-1.0.4.tgz?cache=0&sync_timestamp=1620451552940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-date-object%2Fdownload%2Fis-date-object-1.0.4.tgz"
  "version" "1.0.4"

"is-descriptor@^0.1.0":
  "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
  "resolved" "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-docker@^2.0.0":
  "integrity" "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao="
  "resolved" "https://registry.npm.taobao.org/is-docker/download/is-docker-2.2.1.tgz?cache=0&sync_timestamp=1617958843085&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-docker%2Fdownload%2Fis-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
  "resolved" "https://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="
  "resolved" "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "https://registry.npm.taobao.org/is-glob/download/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw="
  "resolved" "https://registry.npm.taobao.org/is-glob/download/is-glob-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-extglob" "^2.1.1"

"is-negative-zero@^2.0.1":
  "integrity" "sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ="
  "resolved" "https://registry.npm.taobao.org/is-negative-zero/download/is-negative-zero-2.0.1.tgz?cache=0&sync_timestamp=1607123324574&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-negative-zero%2Fdownload%2Fis-negative-zero-2.0.1.tgz"
  "version" "2.0.1"

"is-number-object@^1.0.4":
  "integrity" "sha1-bt+u7XlQz/Ga/tzp+/yp7m3Sies="
  "resolved" "https://registry.nlark.com/is-number-object/download/is-number-object-1.0.5.tgz?cache=0&sync_timestamp=1620421575334&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-number-object%2Fdownload%2Fis-number-object-1.0.5.tgz"
  "version" "1.0.5"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss="
  "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^1.0.1":
  "integrity" "sha1-PkcprB9f3gJc19g6iW2rn09n2w8="
  "resolved" "https://registry.npm.taobao.org/is-obj/download/is-obj-1.0.1.tgz?cache=0&sync_timestamp=1618600378936&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-obj%2Fdownload%2Fis-obj-1.0.1.tgz"
  "version" "1.0.1"

"is-obj@^2.0.0":
  "integrity" "sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI="
  "resolved" "https://registry.nlark.com/is-obj/download/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-path-cwd@^2.0.0":
  "integrity" "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s="
  "resolved" "https://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-2.2.0.tgz"
  "version" "2.2.0"

"is-path-in-cwd@^2.0.0":
  "integrity" "sha1-v+Lcomxp85cmWkAJljYCk1oFOss="
  "resolved" "https://registry.nlark.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz?cache=0&sync_timestamp=1620047154697&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-path-in-cwd%2Fdownload%2Fis-path-in-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "is-path-inside" "^2.1.0"

"is-path-inside@^2.1.0":
  "integrity" "sha1-fJgQWH1lmkDSe8201WFuqwWUlLI="
  "resolved" "https://registry.nlark.com/is-path-inside/download/is-path-inside-2.1.0.tgz?cache=0&sync_timestamp=1620046845369&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-path-inside%2Fdownload%2Fis-path-inside-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "path-is-inside" "^1.0.2"

"is-plain-obj@^1.0.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "https://registry.nlark.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-obj@^3.0.0":
  "integrity" "sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc="
  "resolved" "https://registry.nlark.com/is-plain-obj/download/is-plain-obj-3.0.0.tgz"
  "version" "3.0.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
  "resolved" "https://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz?cache=0&sync_timestamp=1599667313656&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-plain-object%2Fdownload%2Fis-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-regex@^1.0.4", "is-regex@^1.1.2", "is-regex@^1.1.3":
  "integrity" "sha1-0Cn5r/ZEi5Prvj8z2scVEf3L758="
  "resolved" "https://registry.nlark.com/is-regex/download/is-regex-1.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-regex%2Fdownload%2Fis-regex-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "has-symbols" "^1.0.2"

"is-regexp@^1.0.0":
  "integrity" "sha1-/S2INUXEa6xaYz57mgnof6LLUGk="
  "resolved" "https://registry.nlark.com/is-regexp/download/is-regexp-1.0.0.tgz"
  "version" "1.0.0"

"is-resolvable@^1.0.0":
  "integrity" "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg="
  "resolved" "https://registry.npm.taobao.org/is-resolvable/download/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha1-venDJoDW+uBBKdasnZIc54FfeOM="
  "resolved" "https://registry.npm.taobao.org/is-stream/download/is-stream-2.0.0.tgz"
  "version" "2.0.0"

"is-string@^1.0.5", "is-string@^1.0.6":
  "integrity" "sha1-P+XVmS+w2TQE8yWE1LAXmnG1Sl8="
  "resolved" "https://registry.nlark.com/is-string/download/is-string-1.0.6.tgz?cache=0&sync_timestamp=1620448765078&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-string%2Fdownload%2Fis-string-1.0.6.tgz"
  "version" "1.0.6"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha1-ptrJO2NbBjymhyI23oiRClevE5w="
  "resolved" "https://registry.nlark.com/is-symbol/download/is-symbol-1.0.4.tgz?cache=0&sync_timestamp=1620502244179&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-symbol%2Fdownload%2Fis-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typedarray@~1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-unicode-supported@^0.1.0":
  "integrity" "sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc="
  "resolved" "https://registry.nlark.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz?cache=0&sync_timestamp=1625294108348&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-unicode-supported%2Fdownload%2Fis-unicode-supported-0.1.0.tgz"
  "version" "0.1.0"

"is-what@^3.12.0":
  "integrity" "sha1-4SIvRt3ahd6tD9HJ3xMXYOd3VcE="
  "resolved" "https://registry.npm.taobao.org/is-what/download/is-what-3.14.1.tgz?cache=0&sync_timestamp=1615169709354&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-what%2Fdownload%2Fis-what-3.14.1.tgz"
  "version" "3.14.1"

"is-windows@^1.0.2":
  "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
  "resolved" "https://registry.npm.taobao.org/is-windows/download/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://registry.npm.taobao.org/is-wsl/download/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"is-wsl@^2.1.1":
  "integrity" "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE="
  "resolved" "https://registry.npm.taobao.org/is-wsl/download/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isstream@~0.1.2":
  "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
  "resolved" "https://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz"
  "version" "0.1.2"

"javascript-stringify@^2.0.1":
  "integrity" "sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk="
  "resolved" "https://registry.npm.taobao.org/javascript-stringify/download/javascript-stringify-2.1.0.tgz"
  "version" "2.1.0"

"js-base64@^3.7.1":
  "integrity" "sha512-XyYXEUTP3ykPPnGPoesMr4yBygopit99iXW52yT1EWrkzwzvtAor/pbf+EBuDkwqSty7K10LeTjCkUn8c166aQ=="
  "resolved" "https://registry.npmjs.org/js-base64/-/js-base64-3.7.1.tgz"
  "version" "3.7.1"

"js-cookie@^2.2.1":
  "integrity" "sha1-aeEG3F1YBolFYpAqpbrsN0Tpsrg="
  "resolved" "https://registry.npm.taobao.org/js-cookie/download/js-cookie-2.2.1.tgz"
  "version" "2.2.1"

"js-message@1.0.7":
  "integrity" "sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc="
  "resolved" "https://registry.npm.taobao.org/js-message/download/js-message-1.0.7.tgz"
  "version" "1.0.7"

"js-pinyin@^0.1.9":
  "integrity" "sha1-zfGTNWRd2yDUYw3xBYqsJdJWrf0="
  "resolved" "https://registry.npmjs.org/js-pinyin/-/js-pinyin-0.1.9.tgz"
  "version" "0.1.9"

"js-queue@2.0.2":
  "integrity" "sha1-C+WQM4+QOzbHPTPDGIOoIUEs1II="
  "resolved" "https://registry.npm.taobao.org/js-queue/download/js-queue-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "easy-stack" "^1.0.1"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="
  "resolved" "https://registry.nlark.com/js-tokens/download/js-tokens-4.0.0.tgz?cache=0&sync_timestamp=1619345098261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjs-tokens%2Fdownload%2Fjs-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-tokens@^3.0.2":
  "integrity" "sha1-mGbfOVECEw449/mWvOtlRDIJwls="
  "resolved" "https://registry.nlark.com/js-tokens/download/js-tokens-3.0.2.tgz"
  "version" "3.0.2"

"js-yaml@^3.13.1":
  "integrity" "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc="
  "resolved" "https://registry.nlark.com/js-yaml/download/js-yaml-3.14.1.tgz?cache=0&sync_timestamp=1618847250422&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjs-yaml%2Fdownload%2Fjs-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbn@~0.1.0":
  "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
  "resolved" "https://registry.npm.taobao.org/jsbn/download/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsesc@^2.5.1":
  "integrity" "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="
  "resolved" "https://registry.npm.taobao.org/jsesc/download/jsesc-2.5.2.tgz?cache=0&sync_timestamp=1603891175833&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsesc%2Fdownload%2Fjsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.npm.taobao.org/jsesc/download/jsesc-0.5.0.tgz?cache=0&sync_timestamp=1603891175833&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsesc%2Fdownload%2Fjsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.1", "json-parse-better-errors@^1.0.2":
  "integrity" "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="
  "resolved" "https://registry.npm.taobao.org/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0="
  "resolved" "https://registry.npm.taobao.org/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
  "resolved" "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz?cache=0&sync_timestamp=1607998042332&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson-schema-traverse%2Fdownload%2Fjson-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="
  "resolved" "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz?cache=0&sync_timestamp=1608000211395&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson-schema-traverse%2Fdownload%2Fjson-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-schema@0.2.3":
  "integrity" "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="
  "resolved" "https://registry.npm.taobao.org/json-schema/download/json-schema-0.2.3.tgz?cache=0&sync_timestamp=1609553758550&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson-schema%2Fdownload%2Fjson-schema-0.2.3.tgz"
  "version" "0.2.3"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://registry.npm.taobao.org/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stringify-safe@~5.0.1":
  "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
  "resolved" "https://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json3@^3.3.3":
  "integrity" "sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E="
  "resolved" "https://registry.npm.taobao.org/json3/download/json3-3.3.3.tgz"
  "version" "3.3.3"

"json5@^0.5.0":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.1":
  "integrity" "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2", "json5@^2.2.0":
  "integrity" "sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "minimist" "^1.2.5"

"jsonfile@^4.0.0":
  "integrity" "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss="
  "resolved" "https://registry.npm.taobao.org/jsonfile/download/jsonfile-4.0.0.tgz?cache=0&sync_timestamp=1604161933968&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsonfile%2Fdownload%2Fjsonfile-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "graceful-fs" "^4.1.6"

"jsprim@^1.2.2":
  "integrity" "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI="
  "resolved" "https://registry.npm.taobao.org/jsprim/download/jsprim-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.2.3"
    "verror" "1.10.0"

"killable@^1.0.1":
  "integrity" "sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI="
  "resolved" "https://registry.npm.taobao.org/killable/download/killable-1.0.1.tgz"
  "version" "1.0.1"

"kind-of@^3.0.2":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"launch-editor-middleware@^2.2.1":
  "integrity" "sha1-4UsH5scVSwpLhqD9NFeE5FgEwVc="
  "resolved" "https://registry.npm.taobao.org/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "launch-editor" "^2.2.1"

"launch-editor@^2.2.1":
  "integrity" "sha1-hxtaPuOdZoD8wm03kwtu7aidsMo="
  "resolved" "https://registry.npm.taobao.org/launch-editor/download/launch-editor-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "chalk" "^2.3.0"
    "shell-quote" "^1.6.1"

"less-loader@^5.0.0":
  "integrity" "sha1-SY3eOmxsT4h0WO6e0/CGoSrRtGY="
  "resolved" "https://registry.nlark.com/less-loader/download/less-loader-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "clone" "^2.1.1"
    "loader-utils" "^1.1.0"
    "pify" "^4.0.1"

"less@^3.13.1":
  "integrity" "sha1-DryR0qDpwMZzW4PUlrCrBYMHeQk="
  "resolved" "https://registry.npm.taobao.org/less/download/less-3.13.1.tgz?cache=0&sync_timestamp=1612066490256&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fless%2Fdownload%2Fless-3.13.1.tgz"
  "version" "3.13.1"
  dependencies:
    "copy-anything" "^2.0.1"
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "tslib" "^1.10.0"
  optionalDependencies:
    "image-size" "~0.5.0"
    "make-dir" "^2.1.0"
    "mime" "^1.4.1"
    "native-request" "^1.0.5"
    "source-map" "~0.6.0"

"levn@^0.4.1":
  "integrity" "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4="
  "resolved" "https://registry.npm.taobao.org/levn/download/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"lines-and-columns@^1.1.6":
  "integrity" "sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA="
  "resolved" "https://registry.npm.taobao.org/lines-and-columns/download/lines-and-columns-1.1.6.tgz"
  "version" "1.1.6"

"lint-staged@^11.1.2":
  "integrity" "sha1-TdeHgq5D7m6/KWnK2a9npGszzZA="
  "resolved" "https://registry.nlark.com/lint-staged/download/lint-staged-11.1.2.tgz?cache=0&sync_timestamp=1628226857399&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flint-staged%2Fdownload%2Flint-staged-11.1.2.tgz"
  "version" "11.1.2"
  dependencies:
    "chalk" "^4.1.1"
    "cli-truncate" "^2.1.0"
    "commander" "^7.2.0"
    "cosmiconfig" "^7.0.0"
    "debug" "^4.3.1"
    "enquirer" "^2.3.6"
    "execa" "^5.0.0"
    "listr2" "^3.8.2"
    "log-symbols" "^4.1.0"
    "micromatch" "^4.0.4"
    "normalize-path" "^3.0.0"
    "please-upgrade-node" "^3.2.0"
    "string-argv" "0.3.1"
    "stringify-object" "^3.3.0"

"listr2@^3.8.2":
  "integrity" "sha1-l3GwJAeHWqeOc9bg/2VBu+wKruk="
  "resolved" "https://registry.nlark.com/listr2/download/listr2-3.11.0.tgz?cache=0&sync_timestamp=1626978379870&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flistr2%2Fdownload%2Flistr2-3.11.0.tgz"
  "version" "3.11.0"
  dependencies:
    "cli-truncate" "^2.1.0"
    "colorette" "^1.2.2"
    "log-update" "^4.0.0"
    "p-map" "^4.0.0"
    "rxjs" "^6.6.7"
    "through" "^2.3.8"
    "wrap-ansi" "^7.0.0"

"load-json-file@^4.0.0":
  "integrity" "sha1-L19Fq5HjMhYjT9U62rZo607AmTs="
  "resolved" "https://registry.npm.taobao.org/load-json-file/download/load-json-file-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^4.0.0"
    "pify" "^3.0.0"
    "strip-bom" "^3.0.0"

"loader-fs-cache@^1.0.0":
  "integrity" "sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k="
  "resolved" "https://registry.npm.taobao.org/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "find-cache-dir" "^0.1.1"
    "mkdirp" "^0.5.1"

"loader-runner@^2.3.1", "loader-runner@^2.4.0":
  "integrity" "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c="
  "resolved" "https://registry.npm.taobao.org/loader-runner/download/loader-runner-2.4.0.tgz?cache=0&sync_timestamp=1610027880902&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Floader-runner%2Fdownload%2Floader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@^0.2.16":
  "integrity" "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="
  "resolved" "https://registry.npm.taobao.org/loader-utils/download/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.0", "loader-utils@^1.0.2", "loader-utils@^1.1.0", "loader-utils@^1.2.3", "loader-utils@^1.4.0":
  "integrity" "sha1-xXm140yzSxp07cbB+za/o3HVphM="
  "resolved" "https://registry.npm.taobao.org/loader-utils/download/loader-utils-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^2.0.0":
  "integrity" "sha512-TM57VeHptv569d/GKh6TAYdzKblwDNiumOdkFnejjD0XwTH87K90w3O7AiJRqdQoXygvi1VQTJTLGhJl7WqA7A=="
  "resolved" "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"locate-path@^2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "^2.0.0"
    "path-exists" "^3.0.0"

"locate-path@^3.0.0":
  "integrity" "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash.clonedeep@^4.5.0":
  "integrity" "sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8="
  "resolved" "https://registry.npm.taobao.org/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz"
  "version" "4.5.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "https://registry.npm.taobao.org/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.defaultsdeep@^4.6.1":
  "integrity" "sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY="
  "resolved" "https://registry.npm.taobao.org/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz"
  "version" "4.6.1"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha1-hImxyw0p/4gZXM7KRI/21swpXDY="
  "resolved" "https://registry.npm.taobao.org/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.mapvalues@^4.6.0":
  "integrity" "sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw="
  "resolved" "https://registry.npm.taobao.org/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz"
  "version" "4.6.0"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://registry.npm.taobao.org/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.merge@^4.6.2":
  "integrity" "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo="
  "resolved" "https://registry.npm.taobao.org/lodash.merge/download/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.transform@^4.6.0":
  "integrity" "sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A="
  "resolved" "https://registry.npm.taobao.org/lodash.transform/download/lodash.transform-4.6.0.tgz"
  "version" "4.6.0"

"lodash.truncate@^4.4.2":
  "integrity" "sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM="
  "resolved" "https://registry.npm.taobao.org/lodash.truncate/download/lodash.truncate-4.4.2.tgz"
  "version" "4.4.2"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://registry.npm.taobao.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.11", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.19", "lodash@^4.17.20", "lodash@^4.17.21", "lodash@^4.17.3", "lodash@^4.17.4", "lodash@^4.2.0":
  "integrity" "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="
  "resolved" "https://registry.npm.taobao.org/lodash/download/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^2.2.0":
  "integrity" "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo="
  "resolved" "https://registry.nlark.com/log-symbols/download/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"log-symbols@^4.1.0":
  "integrity" "sha1-P727lbRoOsn8eFER55LlWNSr1QM="
  "resolved" "https://registry.npm.taobao.org/log-symbols/download/log-symbols-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "chalk" "^4.1.0"
    "is-unicode-supported" "^0.1.0"

"log-update@^4.0.0":
  "integrity" "sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE="
  "resolved" "https://registry.npm.taobao.org/log-update/download/log-update-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-escapes" "^4.3.0"
    "cli-cursor" "^3.1.0"
    "slice-ansi" "^4.0.0"
    "wrap-ansi" "^6.2.0"

"loglevel@^1.6.8":
  "integrity" "sha1-AF/eL15uRwaPk1/yhXPhJe9y8Zc="
  "resolved" "https://registry.npm.taobao.org/loglevel/download/loglevel-1.7.1.tgz?cache=0&sync_timestamp=1606314002487&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Floglevel%2Fdownload%2Floglevel-1.7.1.tgz"
  "version" "1.7.1"

"loose-envify@^1.0.0":
  "integrity" "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8="
  "resolved" "https://registry.npm.taobao.org/loose-envify/download/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lottie-web@^5.1.9", "lottie-web@^5.9.2":
  "integrity" "sha512-YnoJIKCdKIzno8G/kONOpADW6H/ORZV9puy3vWOhWmHtbDcpISFGVvvdKKa2jwAcsVqXK4xSi0po730kAPIfBw=="
  "resolved" "https://registry.npmmirror.com/lottie-web/-/lottie-web-5.9.2.tgz"
  "version" "5.9.2"

"lower-case@^1.1.1":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "https://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz?cache=0&sync_timestamp=1606867328741&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flower-case%2Fdownload%2Flower-case-1.1.4.tgz"
  "version" "1.1.4"

"lru-cache@^4.0.1":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz?cache=0&sync_timestamp=1594427602316&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flru-cache%2Fdownload%2Flru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^4.1.2":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz?cache=0&sync_timestamp=1594427602316&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flru-cache%2Fdownload%2Flru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-5.1.1.tgz?cache=0&sync_timestamp=1594427602316&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flru-cache%2Fdownload%2Flru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lru-cache@^6.0.0":
  "integrity" "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-6.0.0.tgz?cache=0&sync_timestamp=1594427573763&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flru-cache%2Fdownload%2Flru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"make-dir@^2.0.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "https://registry.npm.taobao.org/make-dir/download/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^2.1.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "https://registry.npm.taobao.org/make-dir/download/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.2", "make-dir@^3.1.0":
  "integrity" "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8="
  "resolved" "https://registry.npm.taobao.org/make-dir/download/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://registry.npm.taobao.org/map-cache/download/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"md5.js@^1.3.4":
  "integrity" "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8="
  "resolved" "https://registry.npm.taobao.org/md5.js/download/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"mdn-data@2.0.14":
  "integrity" "sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA="
  "resolved" "https://registry.nlark.com/mdn-data/download/mdn-data-2.0.14.tgz?cache=0&sync_timestamp=1619049042656&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.14.tgz"
  "version" "2.0.14"

"mdn-data@2.0.4":
  "integrity" "sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs="
  "resolved" "https://registry.nlark.com/mdn-data/download/mdn-data-2.0.4.tgz?cache=0&sync_timestamp=1619049042656&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.4.tgz"
  "version" "2.0.4"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memory-fs@^0.2.0":
  "integrity" "sha1-8rslNovBIeORwlIN6Slpyu4KApA="
  "resolved" "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.2.0.tgz"
  "version" "0.2.0"

"memory-fs@^0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"memory-fs@^0.5.0":
  "integrity" "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw="
  "resolved" "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-source-map@^1.1.0":
  "integrity" "sha1-L93n5gIJOfcJBqaPLXrmheTIxkY="
  "resolved" "https://registry.npm.taobao.org/merge-source-map/download/merge-source-map-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "source-map" "^0.6.1"

"merge-stream@^2.0.0":
  "integrity" "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A="
  "resolved" "https://registry.npm.taobao.org/merge-stream/download/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.2.3":
  "integrity" "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4="
  "resolved" "https://registry.npm.taobao.org/merge2/download/merge2-1.4.1.tgz?cache=0&sync_timestamp=1591169980723&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmerge2%2Fdownload%2Fmerge2-1.4.1.tgz"
  "version" "1.4.1"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^3.1.10", "micromatch@^3.1.4":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "https://registry.nlark.com/micromatch/download/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^4.0.2":
  "integrity" "sha1-iW1Rnf6dsl/OlM63pQCRm/iB6/k="
  "resolved" "https://registry.nlark.com/micromatch/download/micromatch-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "braces" "^3.0.1"
    "picomatch" "^2.2.3"

"micromatch@^4.0.4":
  "integrity" "sha1-iW1Rnf6dsl/OlM63pQCRm/iB6/k="
  "resolved" "https://registry.npm.taobao.org/micromatch/download/micromatch-4.0.4.tgz?cache=0&sync_timestamp=1618054842871&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "braces" "^3.0.1"
    "picomatch" "^2.2.3"

"miller-rabin@^4.0.0":
  "integrity" "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0="
  "resolved" "https://registry.npm.taobao.org/miller-rabin/download/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.47.0":
  "integrity" "sha1-jLMT5Zll08Bc+/iYkVomevRqM1w="
  "resolved" "https://registry.npm.taobao.org/mime-db/download/mime-db-1.47.0.tgz?cache=0&sync_timestamp=1617306118828&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.47.0.tgz"
  "version" "1.47.0"

"mime-types@^2.1.12", "mime-types@~2.1.17", "mime-types@~2.1.19", "mime-types@~2.1.24":
  "integrity" "sha1-bnvotMR5gl+F7WMmaV23P5MF1i0="
  "resolved" "https://registry.npm.taobao.org/mime-types/download/mime-types-2.1.30.tgz?cache=0&sync_timestamp=1617340124913&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.30.tgz"
  "version" "2.1.30"
  dependencies:
    "mime-db" "1.47.0"

"mime@^1.4.1":
  "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
  "resolved" "https://registry.npm.taobao.org/mime/download/mime-1.6.0.tgz"
  "version" "1.6.0"

"mime@^2.4.4":
  "integrity" "sha1-bj3GzCuVEGQ4MOXxnVy3U9pe6r4="
  "resolved" "https://registry.npm.taobao.org/mime/download/mime-2.5.2.tgz"
  "version" "2.5.2"

"mime@1.6.0":
  "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
  "resolved" "https://registry.npm.taobao.org/mime/download/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="
  "resolved" "https://registry.nlark.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.1.0":
  "integrity" "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="
  "resolved" "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-2.1.0.tgz?cache=0&sync_timestamp=1617823583529&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmimic-fn%2Fdownload%2Fmimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mini-css-extract-plugin@^0.9.0":
  "integrity" "sha1-R/LPB6oWWrNXM7H8l9TEbAVkM54="
  "resolved" "https://registry.nlark.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.9.0.tgz"
  "version" "0.9.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "normalize-url" "1.9.1"
    "schema-utils" "^1.0.0"
    "webpack-sources" "^1.1.0"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="
  "resolved" "https://registry.npm.taobao.org/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "https://registry.npm.taobao.org/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4":
  "integrity" "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM="
  "resolved" "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.2.0", "minimist@^1.2.5":
  "integrity" "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="
  "resolved" "https://registry.npm.taobao.org/minimist/download/minimist-1.2.5.tgz"
  "version" "1.2.5"

"minipass@^3.1.1":
  "integrity" "sha1-fUL/HzljVILhX5zbUxhN7r1YFf0="
  "resolved" "https://registry.npm.taobao.org/minipass/download/minipass-3.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fminipass%2Fdownload%2Fminipass-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "yallist" "^4.0.0"

"mississippi@^3.0.0":
  "integrity" "sha1-6goykfl+C16HdrNj1fChLZTGcCI="
  "resolved" "https://registry.npm.taobao.org/mississippi/download/mississippi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^3.0.0"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mixin-deep@^1.2.0":
  "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
  "resolved" "https://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.1", "mkdirp@^0.5.3", "mkdirp@^0.5.5", "mkdirp@~0.5.1":
  "integrity" "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8="
  "resolved" "https://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1591257007439&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"move-concurrently@^1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "https://registry.npm.taobao.org/move-concurrently/download/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"ms@^2.1.1", "ms@2.1.2":
  "integrity" "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.1.2.tgz?cache=0&sync_timestamp=1607433912031&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz?cache=0&sync_timestamp=1607433912031&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.1":
  "integrity" "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz?cache=0&sync_timestamp=1607433912031&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.1.tgz"
  "version" "2.1.1"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://registry.npm.taobao.org/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha1-oOx72QVcQoL3kMPIL04o2zsxsik="
  "resolved" "https://registry.npm.taobao.org/multicast-dns/download/multicast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"mutation-observer@^1.0.3":
  "integrity" "sha1-QukiKxAbyoLlup1aes9KFMDyY9A="
  "resolved" "https://registry.npm.taobao.org/mutation-observer/download/mutation-observer-1.0.3.tgz"
  "version" "1.0.3"

"mute-stream@0.0.8":
  "integrity" "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0="
  "resolved" "https://registry.npm.taobao.org/mute-stream/download/mute-stream-0.0.8.tgz"
  "version" "0.0.8"

"mz@^2.4.0":
  "integrity" "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI="
  "resolved" "https://registry.npm.taobao.org/mz/download/mz-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "any-promise" "^1.0.0"
    "object-assign" "^4.0.1"
    "thenify-all" "^1.0.0"

"nan@^2.12.1":
  "integrity" "sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk="
  "resolved" "https://registry.npm.taobao.org/nan/download/nan-2.14.2.tgz?cache=0&sync_timestamp=1602591646310&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnan%2Fdownload%2Fnan-2.14.2.tgz"
  "version" "2.14.2"

"nanomatch@^1.2.9":
  "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
  "resolved" "https://registry.npm.taobao.org/nanomatch/download/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"native-request@^1.0.5":
  "integrity" "sha1-j2a/YG4PfqJ8DlmV6y9dA+M65vs="
  "resolved" "https://registry.npm.taobao.org/native-request/download/native-request-1.0.8.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnative-request%2Fdownload%2Fnative-request-1.0.8.tgz"
  "version" "1.0.8"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.2":
  "integrity" "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="
  "resolved" "https://registry.npm.taobao.org/negotiator/download/negotiator-0.6.2.tgz"
  "version" "0.6.2"

"neo-async@^2.5.0", "neo-async@^2.6.0", "neo-async@^2.6.1":
  "integrity" "sha1-tKr7k+OustgXTKU88WOrfXMIMF8="
  "resolved" "https://registry.npm.taobao.org/neo-async/download/neo-async-2.6.2.tgz?cache=0&sync_timestamp=1594317853334&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fneo-async%2Fdownload%2Fneo-async-2.6.2.tgz"
  "version" "2.6.2"

"nice-try@^1.0.4":
  "integrity" "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="
  "resolved" "https://registry.npm.taobao.org/nice-try/download/nice-try-1.0.5.tgz?cache=0&sync_timestamp=1614510093028&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnice-try%2Fdownload%2Fnice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@^2.2.0":
  "integrity" "sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw="
  "resolved" "https://registry.npm.taobao.org/no-case/download/no-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-forge@^0.10.0":
  "integrity" "sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M="
  "resolved" "https://registry.npm.taobao.org/node-forge/download/node-forge-0.10.0.tgz?cache=0&sync_timestamp=1599010730303&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-forge%2Fdownload%2Fnode-forge-0.10.0.tgz"
  "version" "0.10.0"

"node-ipc@^9.1.1":
  "integrity" "sha1-Ks+WJoGv2sJgKHbZj+ZDTVTZvTw="
  "resolved" "https://registry.npm.taobao.org/node-ipc/download/node-ipc-9.1.4.tgz?cache=0&sync_timestamp=1614360132246&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-ipc%2Fdownload%2Fnode-ipc-9.1.4.tgz"
  "version" "9.1.4"
  dependencies:
    "event-pubsub" "4.3.0"
    "js-message" "1.0.7"
    "js-queue" "2.0.2"

"node-libs-browser@^2.2.1":
  "integrity" "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU="
  "resolved" "https://registry.npm.taobao.org/node-libs-browser/download/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.1"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "^1.0.1"

"node-releases@^1.1.71":
  "integrity" "sha1-FIAqtrEDmnmgx9ZithClu9durL4="
  "resolved" "https://registry.nlark.com/node-releases/download/node-releases-1.1.72.tgz?cache=0&sync_timestamp=1620978655178&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnode-releases%2Fdownload%2Fnode-releases-1.1.72.tgz"
  "version" "1.1.72"

"normalize-package-data@^2.3.2", "normalize-package-data@^2.5.0":
  "integrity" "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg="
  "resolved" "https://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-2.5.0.tgz?cache=0&sync_timestamp=1616086930281&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^1.0.0":
  "integrity" "sha1-MtDkcvkf80VwHBWoMRAY07CpA3k="
  "resolved" "https://registry.npm.taobao.org/normalize-path/download/normalize-path-1.0.0.tgz"
  "version" "1.0.0"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="
  "resolved" "https://registry.npm.taobao.org/normalize-path/download/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://registry.npm.taobao.org/normalize-range/download/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^3.0.0":
  "integrity" "sha1-suHE3E98bVd0PfczpPWXjRhlBVk="
  "resolved" "https://registry.nlark.com/normalize-url/download/normalize-url-3.3.0.tgz"
  "version" "3.3.0"

"normalize-url@1.9.1":
  "integrity" "sha1-LMDWazHqIwNkWENuNiDYWVTGbDw="
  "resolved" "https://registry.nlark.com/normalize-url/download/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"normalize-wheel@^1.0.1":
  "integrity" "sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU="
  "resolved" "https://registry.npmjs.org/normalize-wheel/-/normalize-wheel-1.0.1.tgz"
  "version" "1.0.1"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://registry.npm.taobao.org/npm-run-path/download/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.0":
  "integrity" "sha1-t+zR5e1T2o43pV4cImnguX7XSOo="
  "resolved" "https://registry.npm.taobao.org/npm-run-path/download/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"npm-run-path@^4.0.1":
  "integrity" "sha1-t+zR5e1T2o43pV4cImnguX7XSOo="
  "resolved" "https://registry.npm.taobao.org/npm-run-path/download/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nprogress@^0.2.0":
  "integrity" "sha1-y480xTIT2JVyP8urkH6UIq28r7E="
  "resolved" "https://registry.npm.taobao.org/nprogress/download/nprogress-0.2.0.tgz"
  "version" "0.2.0"

"nth-check@^1.0.2":
  "integrity" "sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw="
  "resolved" "https://registry.npm.taobao.org/nth-check/download/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="
  "resolved" "https://registry.npm.taobao.org/num2fraction/download/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"oauth-sign@~0.9.0":
  "integrity" "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="
  "resolved" "https://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://registry.npm.taobao.org/object-copy/download/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-hash@^1.1.4":
  "integrity" "sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8="
  "resolved" "https://registry.npm.taobao.org/object-hash/download/object-hash-1.3.1.tgz?cache=0&sync_timestamp=1608920939501&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-hash%2Fdownload%2Fobject-hash-1.3.1.tgz"
  "version" "1.3.1"

"object-inspect@^1.11.0":
  "integrity" "sha1-nc6xRs7dQUig2eUauI00z1CZIrE="
  "resolved" "https://registry.nlark.com/object-inspect/download/object-inspect-1.11.0.tgz"
  "version" "1.11.0"

"object-inspect@^1.9.0":
  "integrity" "sha1-wqp9LQn1DJk3VwT3oK3yTFeC02k="
  "resolved" "https://registry.nlark.com/object-inspect/download/object-inspect-1.10.3.tgz?cache=0&sync_timestamp=1620446150016&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fobject-inspect%2Fdownload%2Fobject-inspect-1.10.3.tgz"
  "version" "1.10.3"

"object-is@^1.0.1":
  "integrity" "sha1-ud7qpfx/GEag+uzc7sE45XePU6w="
  "resolved" "https://registry.npm.taobao.org/object-is/download/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="
  "resolved" "https://registry.npm.taobao.org/object-keys/download/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://registry.npm.taobao.org/object-visit/download/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.0", "object.assign@^4.1.2":
  "integrity" "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA="
  "resolved" "https://registry.npm.taobao.org/object.assign/download/object.assign-4.1.2.tgz?cache=0&sync_timestamp=1604115131746&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.assign%2Fdownload%2Fobject.assign-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "has-symbols" "^1.0.1"
    "object-keys" "^1.1.1"

"object.entries@^1.1.2":
  "integrity" "sha1-Q8z5pQvF/VtknUWrGlefJOCIyv0="
  "resolved" "https://registry.nlark.com/object.entries/download/object.entries-1.1.4.tgz?cache=0&sync_timestamp=1622071045693&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fobject.entries%2Fdownload%2Fobject.entries-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.2"

"object.getownpropertydescriptors@^2.0.3", "object.getownpropertydescriptors@^2.1.0":
  "integrity" "sha1-G9Y66s8NXS0vMbXjk7A6fGAaI/c="
  "resolved" "https://registry.npm.taobao.org/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.2"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.0", "object.values@^1.1.3":
  "integrity" "sha1-6qix4XWJ8C9pjbCT98Yu4WmXQu4="
  "resolved" "https://registry.npm.taobao.org/object.values/download/object.values-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.2"
    "has" "^1.0.3"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4="
  "resolved" "https://registry.npm.taobao.org/obuf/download/obuf-1.1.2.tgz"
  "version" "1.1.2"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="
  "resolved" "https://registry.npm.taobao.org/on-headers/download/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npm.taobao.org/once/download/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://registry.npm.taobao.org/onetime/download/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.0":
  "integrity" "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4="
  "resolved" "https://registry.npm.taobao.org/onetime/download/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"onetime@^5.1.2":
  "integrity" "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4="
  "resolved" "https://registry.npm.taobao.org/onetime/download/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^6.3.0":
  "integrity" "sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk="
  "resolved" "https://registry.nlark.com/open/download/open-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "is-wsl" "^1.1.0"

"opener@^1.5.1":
  "integrity" "sha1-XTfh81B3udysQwE3InGv3rKhNZg="
  "resolved" "https://registry.npm.taobao.org/opener/download/opener-1.5.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fopener%2Fdownload%2Fopener-1.5.2.tgz"
  "version" "1.5.2"

"opn@^5.5.0":
  "integrity" "sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w="
  "resolved" "https://registry.npm.taobao.org/opn/download/opn-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optionator@^0.9.1":
  "integrity" "sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk="
  "resolved" "https://registry.npm.taobao.org/optionator/download/optionator-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.3"

"ora@^3.4.0":
  "integrity" "sha1-vwdSSRBZo+8+1MhQl1Md6f280xg="
  "resolved" "https://registry.npm.taobao.org/ora/download/ora-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "chalk" "^2.4.2"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^2.0.0"
    "log-symbols" "^2.2.0"
    "strip-ansi" "^5.2.0"
    "wcwidth" "^1.0.1"

"original@^1.0.0":
  "integrity" "sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8="
  "resolved" "https://registry.npm.taobao.org/original/download/original-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "url-parse" "^1.4.3"

"os-browserify@^0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "https://registry.npm.taobao.org/os-browserify/download/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-tmpdir@~1.0.2":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://registry.nlark.com/p-finally/download/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-finally@^2.0.0":
  "integrity" "sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE="
  "resolved" "https://registry.nlark.com/p-finally/download/p-finally-2.0.1.tgz"
  "version" "2.0.1"

"p-limit@^1.1.0":
  "integrity" "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg="
  "resolved" "https://registry.nlark.com/p-limit/download/p-limit-1.3.0.tgz?cache=0&sync_timestamp=1628812721654&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-limit%2Fdownload%2Fp-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-limit@^2.0.0", "p-limit@^2.2.0", "p-limit@^2.2.1":
  "integrity" "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE="
  "resolved" "https://registry.npm.taobao.org/p-limit/download/p-limit-2.3.0.tgz?cache=0&sync_timestamp=1606288370125&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-limit%2Fdownload%2Fp-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "https://registry.npm.taobao.org/p-locate/download/p-locate-2.0.0.tgz?cache=0&sync_timestamp=1597081369770&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-locate%2Fdownload%2Fp-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "^1.1.0"

"p-locate@^3.0.0":
  "integrity" "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ="
  "resolved" "https://registry.npm.taobao.org/p-locate/download/p-locate-3.0.0.tgz?cache=0&sync_timestamp=1597081508945&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-locate%2Fdownload%2Fp-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha1-o0KLtwiLOmApL2aRkni3wpetTwc="
  "resolved" "https://registry.npm.taobao.org/p-locate/download/p-locate-4.1.0.tgz?cache=0&sync_timestamp=1597081508945&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-locate%2Fdownload%2Fp-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^2.0.0":
  "integrity" "sha1-MQko/u+cnsxltosXaTAYpmXOoXU="
  "resolved" "https://registry.npm.taobao.org/p-map/download/p-map-2.1.0.tgz?cache=0&sync_timestamp=1618683296436&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-map%2Fdownload%2Fp-map-2.1.0.tgz"
  "version" "2.1.0"

"p-map@^4.0.0":
  "integrity" "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs="
  "resolved" "https://registry.nlark.com/p-map/download/p-map-4.0.0.tgz?cache=0&sync_timestamp=1627082364579&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-map%2Fdownload%2Fp-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@^3.0.1":
  "integrity" "sha1-MWtMiJPiyNwc+okfQGxLQivr8yg="
  "resolved" "https://registry.npm.taobao.org/p-retry/download/p-retry-3.0.1.tgz?cache=0&sync_timestamp=1617002041200&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-retry%2Fdownload%2Fp-retry-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "retry" "^0.12.0"

"p-try@^1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "https://registry.npm.taobao.org/p-try/download/p-try-1.0.0.tgz"
  "version" "1.0.0"

"p-try@^2.0.0":
  "integrity" "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="
  "resolved" "https://registry.npm.taobao.org/p-try/download/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~1.0.5":
  "integrity" "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8="
  "resolved" "https://registry.npm.taobao.org/pako/download/pako-1.0.11.tgz?cache=0&sync_timestamp=1610208924218&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpako%2Fdownload%2Fpako-1.0.11.tgz"
  "version" "1.0.11"

"parallel-transform@^1.1.0":
  "integrity" "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw="
  "resolved" "https://registry.npm.taobao.org/parallel-transform/download/parallel-transform-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cyclist" "^1.0.1"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "https://registry.npm.taobao.org/param-case/download/param-case-2.1.1.tgz?cache=0&sync_timestamp=1606867454357&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparam-case%2Fdownload%2Fparam-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parent-module@^1.0.0":
  "integrity" "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI="
  "resolved" "https://registry.npm.taobao.org/parent-module/download/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-asn1@^5.0.0", "parse-asn1@^5.1.5":
  "integrity" "sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ="
  "resolved" "https://registry.npm.taobao.org/parse-asn1/download/parse-asn1-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "asn1.js" "^5.2.0"
    "browserify-aes" "^1.0.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "https://registry.npm.taobao.org/parse-json/download/parse-json-4.0.0.tgz?cache=0&sync_timestamp=1610966646988&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse-json%2Fdownload%2Fparse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-json@^5.0.0":
  "integrity" "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80="
  "resolved" "https://registry.npm.taobao.org/parse-json/download/parse-json-5.2.0.tgz?cache=0&sync_timestamp=1610966676829&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse-json%2Fdownload%2Fparse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse5-htmlparser2-tree-adapter@^6.0.0":
  "integrity" "sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY="
  "resolved" "https://registry.npm.taobao.org/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz?cache=0&sync_timestamp=1596089871187&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse5-htmlparser2-tree-adapter%2Fdownload%2Fparse5-htmlparser2-tree-adapter-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "parse5" "^6.0.1"

"parse5@^5.1.1":
  "integrity" "sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg="
  "resolved" "https://registry.npm.taobao.org/parse5/download/parse5-5.1.1.tgz"
  "version" "5.1.1"

"parse5@^6.0.1":
  "integrity" "sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws="
  "resolved" "https://registry.npm.taobao.org/parse5/download/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="
  "resolved" "https://registry.npm.taobao.org/parseurl/download/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha1-5sTd1+06onxoogzE5Q4aTug7vEo="
  "resolved" "https://registry.npm.taobao.org/path-browserify/download/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-dirname@^1.0.0":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "https://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^2.0.0":
  "integrity" "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s="
  "resolved" "https://registry.npm.taobao.org/path-exists/download/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="
  "resolved" "https://registry.npm.taobao.org/path-exists/download/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.2":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "https://registry.npm.taobao.org/path-is-inside/download/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.nlark.com/path-key/download/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "https://registry.npm.taobao.org/path-key/download/path-key-3.1.1.tgz?cache=0&sync_timestamp=1617971613935&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.6":
  "integrity" "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw="
  "resolved" "https://registry.npm.taobao.org/path-parse/download/path-parse-1.0.6.tgz"
  "version" "1.0.6"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz?cache=0&sync_timestamp=1601400433519&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^3.0.0":
  "integrity" "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428="
  "resolved" "https://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz?cache=0&sync_timestamp=1611752074264&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-type%2Fdownload%2Fpath-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"path-type@^4.0.0":
  "integrity" "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs="
  "resolved" "https://registry.npm.taobao.org/path-type/download/path-type-4.0.0.tgz?cache=0&sync_timestamp=1611752015315&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-type%2Fdownload%2Fpath-type-4.0.0.tgz"
  "version" "4.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU="
  "resolved" "https://registry.npm.taobao.org/pbkdf2/download/pbkdf2-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"pdfh5@^1.4.2":
  "integrity" "sha512-1BL8HIx/EEZowRPBgas7/WokbGEv1gxKNRmmHSimG113178mKxIBH4pxWBc0tj6d25Sy+EwnlQwv9cUUmQa42w=="
  "resolved" "https://registry.npmmirror.com/pdfh5/-/pdfh5-1.4.2.tgz"
  "version" "1.4.2"

"pdfjs-dist@2.6.347":
  "integrity" "sha512-QC+h7hG2su9v/nU1wEI3SnpPIrqJODL7GTDFvR74ANKGq1AFJW16PH8VWnhpiTi9YcLSFV9xLeWSgq+ckHLdVQ=="
  "resolved" "https://registry.npmjs.org/pdfjs-dist/-/pdfjs-dist-2.6.347.tgz"
  "version" "2.6.347"

"performance-now@^2.1.0":
  "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
  "resolved" "https://registry.npm.taobao.org/performance-now/download/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.2.3":
  "integrity" "sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI="
  "resolved" "https://registry.nlark.com/picomatch/download/picomatch-2.3.0.tgz"
  "version" "2.3.0"

"pify@^2.0.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz"
  "version" "3.0.0"

"pify@^4.0.1":
  "integrity" "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "https://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "https://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pkg-dir@^1.0.0":
  "integrity" "sha1-ektQio1bstYp1EcFb/TpyTFM89Q="
  "resolved" "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-1.0.0.tgz?cache=0&sync_timestamp=1602859008602&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpkg-dir%2Fdownload%2Fpkg-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "find-up" "^1.0.0"

"pkg-dir@^2.0.0":
  "integrity" "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s="
  "resolved" "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-2.0.0.tgz?cache=0&sync_timestamp=1602859010405&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpkg-dir%2Fdownload%2Fpkg-dir-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"pkg-dir@^3.0.0":
  "integrity" "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM="
  "resolved" "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-3.0.0.tgz?cache=0&sync_timestamp=1602859008602&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpkg-dir%2Fdownload%2Fpkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^3.0.0"

"pkg-dir@^4.1.0":
  "integrity" "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM="
  "resolved" "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-4.2.0.tgz?cache=0&sync_timestamp=1602859008602&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpkg-dir%2Fdownload%2Fpkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pkg-up@^2.0.0":
  "integrity" "sha1-yBmscoBZpGHKscOImivjxJoATX8="
  "resolved" "https://registry.npm.taobao.org/pkg-up/download/pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"please-upgrade-node@^3.2.0":
  "integrity" "sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI="
  "resolved" "https://registry.npm.taobao.org/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "semver-compare" "^1.0.0"

"pnp-webpack-plugin@^1.6.4":
  "integrity" "sha1-yXEaxNxIpoXauvyG+Lbdn434QUk="
  "resolved" "https://registry.npm.taobao.org/pnp-webpack-plugin/download/pnp-webpack-plugin-1.6.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpnp-webpack-plugin%2Fdownload%2Fpnp-webpack-plugin-1.6.4.tgz"
  "version" "1.6.4"
  dependencies:
    "ts-pnp" "^1.1.6"

"portfinder@^1.0.26":
  "integrity" "sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g="
  "resolved" "https://registry.npm.taobao.org/portfinder/download/portfinder-1.0.28.tgz"
  "version" "1.0.28"
  dependencies:
    "async" "^2.6.2"
    "debug" "^3.1.1"
    "mkdirp" "^0.5.5"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://registry.npm.taobao.org/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^7.0.1":
  "integrity" "sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4="
  "resolved" "https://registry.npm.taobao.org/postcss-calc/download/postcss-calc-7.0.5.tgz?cache=0&sync_timestamp=1609689118344&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-calc%2Fdownload%2Fpostcss-calc-7.0.5.tgz"
  "version" "7.0.5"
  dependencies:
    "postcss" "^7.0.27"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.2"

"postcss-colormin@^4.0.3":
  "integrity" "sha1-rgYLzpPteUrHEmTwgTLVUJVr04E="
  "resolved" "https://registry.nlark.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz?cache=0&sync_timestamp=1621620031272&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-colormin%2Fdownload%2Fpostcss-colormin-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "color" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-convert-values@^4.0.1":
  "integrity" "sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8="
  "resolved" "https://registry.nlark.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-discard-comments@^4.0.2":
  "integrity" "sha1-H7q9LCRr/2qq15l7KwkY9NevQDM="
  "resolved" "https://registry.nlark.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz?cache=0&sync_timestamp=1621449811540&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-comments%2Fdownload%2Fpostcss-discard-comments-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-duplicates@^4.0.2":
  "integrity" "sha1-P+EzzTyCKC5VD8myORdqkge3hOs="
  "resolved" "https://registry.nlark.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz?cache=0&sync_timestamp=1621449811996&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-duplicates%2Fdownload%2Fpostcss-discard-duplicates-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-empty@^4.0.1":
  "integrity" "sha1-yMlR6fc+2UKAGUWERKAq2Qu592U="
  "resolved" "https://registry.nlark.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-overridden@^4.0.1":
  "integrity" "sha1-ZSrvipZybwKfXj4AFG7npOdV/1c="
  "resolved" "https://registry.nlark.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-load-config@^2.0.0":
  "integrity" "sha1-xepQTyxK7zPHNZo03jVzdyrXUCo="
  "resolved" "https://registry.npm.taobao.org/postcss-load-config/download/postcss-load-config-2.1.2.tgz?cache=0&sync_timestamp=1612743037145&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-load-config%2Fdownload%2Fpostcss-load-config-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "import-cwd" "^2.0.0"

"postcss-loader@^3.0.0":
  "integrity" "sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0="
  "resolved" "https://registry.nlark.com/postcss-loader/download/postcss-loader-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "postcss" "^7.0.0"
    "postcss-load-config" "^2.0.0"
    "schema-utils" "^1.0.0"

"postcss-merge-longhand@^4.0.11":
  "integrity" "sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ="
  "resolved" "https://registry.nlark.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz"
  "version" "4.0.11"
  dependencies:
    "css-color-names" "0.0.4"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "stylehacks" "^4.0.0"

"postcss-merge-rules@^4.0.3":
  "integrity" "sha1-NivqT/Wh+Y5AdacTxsslrv75plA="
  "resolved" "https://registry.nlark.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz?cache=0&sync_timestamp=1621449818973&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-merge-rules%2Fdownload%2Fpostcss-merge-rules-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "cssnano-util-same-parent" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"
    "vendors" "^1.0.0"

"postcss-minify-font-values@^4.0.2":
  "integrity" "sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY="
  "resolved" "https://registry.nlark.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-gradients@^4.0.2":
  "integrity" "sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE="
  "resolved" "https://registry.nlark.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz?cache=0&sync_timestamp=1621449817860&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-minify-gradients%2Fdownload%2Fpostcss-minify-gradients-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "is-color-stop" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-params@^4.0.2":
  "integrity" "sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ="
  "resolved" "https://registry.nlark.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "browserslist" "^4.0.0"
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^4.0.2":
  "integrity" "sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g="
  "resolved" "https://registry.nlark.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz?cache=0&sync_timestamp=1621449812496&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-minify-selectors%2Fdownload%2Fpostcss-minify-selectors-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"postcss-modules-extract-imports@^2.0.0":
  "integrity" "sha1-gYcZoa4doyX5gyRGsBE27rSTzX4="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz?cache=0&sync_timestamp=1602588174939&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-extract-imports%2Fdownload%2Fpostcss-modules-extract-imports-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^7.0.5"

"postcss-modules-local-by-default@^3.0.2":
  "integrity" "sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.3.tgz?cache=0&sync_timestamp=1602587684904&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-local-by-default%2Fdownload%2Fpostcss-modules-local-by-default-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "icss-utils" "^4.1.1"
    "postcss" "^7.0.32"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.1.0"

"postcss-modules-scope@^2.2.0":
  "integrity" "sha1-OFyuATzHdD9afXYC0Qc6iequYu4="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz?cache=0&sync_timestamp=1602594980328&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-scope%2Fdownload%2Fpostcss-modules-scope-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "postcss" "^7.0.6"
    "postcss-selector-parser" "^6.0.0"

"postcss-modules-values@^3.0.0":
  "integrity" "sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz?cache=0&sync_timestamp=1602586320532&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-values%2Fdownload%2Fpostcss-modules-values-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "icss-utils" "^4.0.0"
    "postcss" "^7.0.6"

"postcss-normalize-charset@^4.0.1":
  "integrity" "sha1-izWt067oOhNrBHHg1ZvlilAoXdQ="
  "resolved" "https://registry.nlark.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz?cache=0&sync_timestamp=1621449813014&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-charset%2Fdownload%2Fpostcss-normalize-charset-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-normalize-display-values@^4.0.2":
  "integrity" "sha1-Db4EpM6QY9RmftK+R2u4MMglk1o="
  "resolved" "https://registry.nlark.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-positions@^4.0.2":
  "integrity" "sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8="
  "resolved" "https://registry.nlark.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-repeat-style@^4.0.2":
  "integrity" "sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw="
  "resolved" "https://registry.nlark.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-string@^4.0.2":
  "integrity" "sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw="
  "resolved" "https://registry.nlark.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-timing-functions@^4.0.2":
  "integrity" "sha1-jgCcoqOUnNr4rSPmtquZy159KNk="
  "resolved" "https://registry.nlark.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-unicode@^4.0.1":
  "integrity" "sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs="
  "resolved" "https://registry.nlark.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-url@^4.0.1":
  "integrity" "sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE="
  "resolved" "https://registry.nlark.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-whitespace@^4.0.2":
  "integrity" "sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI="
  "resolved" "https://registry.nlark.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-ordered-values@^4.1.2":
  "integrity" "sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4="
  "resolved" "https://registry.nlark.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-reduce-initial@^4.0.3":
  "integrity" "sha1-f9QuvqXpyBRgljniwuhK4nC6SN8="
  "resolved" "https://registry.nlark.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz?cache=0&sync_timestamp=1621449818195&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-reduce-initial%2Fdownload%2Fpostcss-reduce-initial-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"

"postcss-reduce-transforms@^4.0.2":
  "integrity" "sha1-F++kBerMbge+NBSlyi0QdGgdTik="
  "resolved" "https://registry.nlark.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-selector-parser@^3.0.0":
  "integrity" "sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA="
  "resolved" "https://registry.nlark.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz?cache=0&sync_timestamp=1620753051451&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "dot-prop" "^5.2.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^6.0.0", "postcss-selector-parser@^6.0.2":
  "integrity" "sha1-LFu6gXSsL2mBq2MaQqsO5UrzMuo="
  "resolved" "https://registry.nlark.com/postcss-selector-parser/download/postcss-selector-parser-6.0.6.tgz?cache=0&sync_timestamp=1620753051451&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-6.0.6.tgz"
  "version" "6.0.6"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-svgo@^4.0.3":
  "integrity" "sha1-NDos26yVBdQWJD1Jb3JPOIlMlB4="
  "resolved" "https://registry.nlark.com/postcss-svgo/download/postcss-svgo-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "svgo" "^1.0.0"

"postcss-unique-selectors@^4.0.1":
  "integrity" "sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w="
  "resolved" "https://registry.nlark.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz?cache=0&sync_timestamp=1621449819576&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-unique-selectors%2Fdownload%2Fpostcss-unique-selectors-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "postcss" "^7.0.0"
    "uniqs" "^2.0.0"

"postcss-value-parser@^3.0.0":
  "integrity" "sha1-n/giVH4okyE88cMO+lGsX9G6goE="
  "resolved" "https://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-value-parser@^4.0.2", "postcss-value-parser@^4.1.0":
  "integrity" "sha1-RD9qIM7WSBor2k+oUypuVdeJoss="
  "resolved" "https://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz"
  "version" "4.1.0"

"postcss@^7.0.0", "postcss@^7.0.1", "postcss@^7.0.14", "postcss@^7.0.27", "postcss@^7.0.32", "postcss@^7.0.5", "postcss@^7.0.6":
  "integrity" "sha1-0r4AuZj38hHYonaXQHny6SuXDiQ="
  "resolved" "https://registry.nlark.com/postcss/download/postcss-7.0.35.tgz?cache=0&sync_timestamp=1621568858981&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss%2Fdownload%2Fpostcss-7.0.35.tgz"
  "version" "7.0.35"
  dependencies:
    "chalk" "^2.4.2"
    "source-map" "^0.6.1"
    "supports-color" "^6.1.0"

"prelude-ls@^1.2.1":
  "integrity" "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y="
  "resolved" "https://registry.npm.taobao.org/prelude-ls/download/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prepend-http@^1.0.0":
  "integrity" "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw="
  "resolved" "https://registry.npm.taobao.org/prepend-http/download/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prettier@^1.18.2":
  "integrity" "sha1-99f1/4qc2HKnvkyhQglZVqYHl8s="
  "resolved" "https://registry.nlark.com/prettier/download/prettier-1.19.1.tgz?cache=0&sync_timestamp=1620594183343&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprettier%2Fdownload%2Fprettier-1.19.1.tgz"
  "version" "1.19.1"

"pretty-error@^2.0.2":
  "integrity" "sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y="
  "resolved" "https://registry.npm.taobao.org/pretty-error/download/pretty-error-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "lodash" "^4.17.20"
    "renderkid" "^2.0.4"

"private@^0.1.6":
  "integrity" "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8="
  "resolved" "https://registry.npm.taobao.org/private/download/private-0.1.8.tgz"
  "version" "0.1.8"

"process-nextick-args@~2.0.0":
  "integrity" "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="
  "resolved" "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "https://registry.npm.taobao.org/process/download/process-0.11.10.tgz"
  "version" "0.11.10"

"progress@^2.0.0":
  "integrity" "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg="
  "resolved" "https://registry.npm.taobao.org/progress/download/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise-inflight@^1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "https://registry.npm.taobao.org/promise-inflight/download/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"proxy-addr@~2.0.5":
  "integrity" "sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8="
  "resolved" "https://registry.npm.taobao.org/proxy-addr/download/proxy-addr-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "forwarded" "~0.1.2"
    "ipaddr.js" "1.9.1"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "https://registry.npm.taobao.org/prr/download/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "https://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.28":
  "integrity" "sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ="
  "resolved" "https://registry.npm.taobao.org/psl/download/psl-1.8.0.tgz"
  "version" "1.8.0"

"public-encrypt@^4.0.0":
  "integrity" "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA="
  "resolved" "https://registry.npm.taobao.org/public-encrypt/download/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0":
  "integrity" "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk="
  "resolved" "https://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^3.0.0":
  "integrity" "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="
  "resolved" "https://registry.npm.taobao.org/pump/download/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4="
  "resolved" "https://registry.npm.taobao.org/pumpify/download/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="
  "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-1.3.2.tgz"
  "version" "1.3.2"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://registry.npm.taobao.org/q/download/q-1.5.1.tgz"
  "version" "1.5.1"

"qrcodejs2@0.0.2":
  "integrity" "sha1-Rlr+Xjnxn6zsuTLBH3oYYQkUauE="
  "resolved" "https://registry.npm.taobao.org/qrcodejs2/download/qrcodejs2-0.0.2.tgz"
  "version" "0.0.2"

"qs@~6.5.2":
  "integrity" "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY="
  "resolved" "https://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz"
  "version" "6.5.2"

"qs@6.7.0":
  "integrity" "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="
  "resolved" "https://registry.npm.taobao.org/qs/download/qs-6.7.0.tgz"
  "version" "6.7.0"

"query-string@^4.1.0":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "https://registry.npm.taobao.org/query-string/download/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "https://registry.npm.taobao.org/querystring-es3/download/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://registry.npm.taobao.org/querystring/download/querystring-0.2.0.tgz"
  "version" "0.2.0"

"querystringify@^2.1.1":
  "integrity" "sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y="
  "resolved" "https://registry.npm.taobao.org/querystringify/download/querystringify-2.2.0.tgz?cache=0&sync_timestamp=1597686721254&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fquerystringify%2Fdownload%2Fquerystringify-2.2.0.tgz"
  "version" "2.2.0"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5", "randombytes@^2.1.0":
  "integrity" "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo="
  "resolved" "https://registry.npm.taobao.org/randombytes/download/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg="
  "resolved" "https://registry.npm.taobao.org/randomfill/download/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="
  "resolved" "https://registry.npm.taobao.org/range-parser/download/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.4.0":
  "integrity" "sha1-oc5vucm8NWylLoklarWQWeE9AzI="
  "resolved" "https://registry.npm.taobao.org/raw-body/download/raw-body-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "bytes" "3.1.0"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"raw-loader@^4.0.2":
  "integrity" "sha512-ZnScIV3ag9A4wPX/ZayxL/jZH+euYb6FcUinPcgiQW0+UBtEv0O6Q3lGd3cqJ+GHH+rksEv3Pj99oxJ3u3VIKA=="
  "resolved" "https://registry.npmjs.org/raw-loader/-/raw-loader-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "loader-utils" "^2.0.0"
    "schema-utils" "^3.0.0"

"read-pkg-up@^3.0.0":
  "integrity" "sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc="
  "resolved" "https://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^2.0.0"
    "read-pkg" "^3.0.0"

"read-pkg@^3.0.0":
  "integrity" "sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k="
  "resolved" "https://registry.nlark.com/read-pkg/download/read-pkg-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "load-json-file" "^4.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^3.0.0"

"read-pkg@^5.1.1":
  "integrity" "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w="
  "resolved" "https://registry.npm.taobao.org/read-pkg/download/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.0", "readable-stream@^2.0.1", "readable-stream@^2.0.2", "readable-stream@^2.1.5", "readable-stream@^2.2.2", "readable-stream@^2.3.3", "readable-stream@^2.3.6", "readable-stream@~2.3.6", "readable-stream@1 || 2":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.1.1":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.6.0":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@^2.2.1":
  "integrity" "sha1-DodiKjMlqjPokihcr4tOhGUppSU="
  "resolved" "https://registry.npm.taobao.org/readdirp/download/readdirp-2.2.1.tgz?cache=0&sync_timestamp=1615717506044&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"readdirp@~3.5.0":
  "integrity" "sha1-m6dMAZsV02UnjS6Ru4xI17TULJ4="
  "resolved" "https://registry.npm.taobao.org/readdirp/download/readdirp-3.5.0.tgz?cache=0&sync_timestamp=1615717506044&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "picomatch" "^2.2.1"

"regenerate-unicode-properties@^8.2.0":
  "integrity" "sha1-5d5xEdZV57pgwFfb6f83yH5lzew="
  "resolved" "https://registry.npm.taobao.org/regenerate-unicode-properties/download/regenerate-unicode-properties-8.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerate-unicode-properties%2Fdownload%2Fregenerate-unicode-properties-8.2.0.tgz"
  "version" "8.2.0"
  dependencies:
    "regenerate" "^1.4.0"

"regenerate@^1.2.1", "regenerate@^1.4.0":
  "integrity" "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo="
  "resolved" "https://registry.npm.taobao.org/regenerate/download/regenerate-1.4.2.tgz?cache=0&sync_timestamp=1604218358172&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerate%2Fdownload%2Fregenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.11.0":
  "integrity" "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk="
  "resolved" "https://registry.nlark.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.13.4":
  "integrity" "sha1-ysLazIoepnX+qrrriugziYrkb1U="
  "resolved" "https://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.13.7.tgz"
  "version" "0.13.7"

"regenerator-transform@^0.10.0":
  "integrity" "sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0="
  "resolved" "https://registry.nlark.com/regenerator-transform/download/regenerator-transform-0.10.1.tgz?cache=0&sync_timestamp=1627057502723&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregenerator-transform%2Fdownload%2Fregenerator-transform-0.10.1.tgz"
  "version" "0.10.1"
  dependencies:
    "babel-runtime" "^6.18.0"
    "babel-types" "^6.19.0"
    "private" "^0.1.6"

"regenerator-transform@^0.14.2":
  "integrity" "sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ="
  "resolved" "https://registry.npm.taobao.org/regenerator-transform/download/regenerator-transform-0.14.5.tgz?cache=0&sync_timestamp=1593557393872&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerator-transform%2Fdownload%2Fregenerator-transform-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
  "resolved" "https://registry.npm.taobao.org/regex-not/download/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.2.0":
  "integrity" "sha1-fvNSro0VnnWMDq3Kb4/LTu8HviY="
  "resolved" "https://registry.npm.taobao.org/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz?cache=0&sync_timestamp=1610725711521&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexp.prototype.flags%2Fdownload%2Fregexp.prototype.flags-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"regexpp@^3.1.0":
  "integrity" "sha1-BCWido2PI7rXDKS5BGH6LxIT4bI="
  "resolved" "https://registry.nlark.com/regexpp/download/regexpp-3.2.0.tgz?cache=0&sync_timestamp=1623668872577&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregexpp%2Fdownload%2Fregexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^2.0.0":
  "integrity" "sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA="
  "resolved" "https://registry.nlark.com/regexpu-core/download/regexpu-core-2.0.0.tgz?cache=0&sync_timestamp=1631619113277&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregexpu-core%2Fdownload%2Fregexpu-core-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "regenerate" "^1.2.1"
    "regjsgen" "^0.2.0"
    "regjsparser" "^0.1.4"

"regexpu-core@^4.7.1":
  "integrity" "sha1-LepamgcjMpj78NuR+pq8TG4PitY="
  "resolved" "https://registry.npm.taobao.org/regexpu-core/download/regexpu-core-4.7.1.tgz?cache=0&sync_timestamp=1600413487232&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexpu-core%2Fdownload%2Fregexpu-core-4.7.1.tgz"
  "version" "4.7.1"
  dependencies:
    "regenerate" "^1.4.0"
    "regenerate-unicode-properties" "^8.2.0"
    "regjsgen" "^0.5.1"
    "regjsparser" "^0.6.4"
    "unicode-match-property-ecmascript" "^1.0.4"
    "unicode-match-property-value-ecmascript" "^1.2.0"

"regjsgen@^0.2.0":
  "integrity" "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc="
  "resolved" "https://registry.npm.taobao.org/regjsgen/download/regjsgen-0.2.0.tgz"
  "version" "0.2.0"

"regjsgen@^0.5.1":
  "integrity" "sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM="
  "resolved" "https://registry.npm.taobao.org/regjsgen/download/regjsgen-0.5.2.tgz"
  "version" "0.5.2"

"regjsparser@^0.1.4":
  "integrity" "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw="
  "resolved" "https://registry.nlark.com/regjsparser/download/regjsparser-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "jsesc" "~0.5.0"

"regjsparser@^0.6.4":
  "integrity" "sha1-tInu98mizkNydicBFCnPgzpxg+Y="
  "resolved" "https://registry.npm.taobao.org/regjsparser/download/regjsparser-0.6.9.tgz"
  "version" "0.6.9"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://registry.npm.taobao.org/relateurl/download/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://registry.npm.taobao.org/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.4":
  "integrity" "sha1-SDsaxZxmAaswp6WWpZZcq8z90KU="
  "resolved" "https://registry.npm.taobao.org/renderkid/download/renderkid-2.0.5.tgz?cache=0&sync_timestamp=1609588654734&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frenderkid%2Fdownload%2Frenderkid-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "css-select" "^2.0.2"
    "dom-converter" "^0.2"
    "htmlparser2" "^3.10.1"
    "lodash" "^4.17.20"
    "strip-ansi" "^3.0.0"

"repeat-element@^1.1.2":
  "integrity" "sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek="
  "resolved" "https://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.4.tgz?cache=0&sync_timestamp=1617837642601&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frepeat-element%2Fdownload%2Frepeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"request@^2.88.2":
  "integrity" "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM="
  "resolved" "https://registry.npm.taobao.org/request/download/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.3"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.5.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk="
  "resolved" "https://registry.npm.taobao.org/require-from-string/download/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"require-main-filename@^2.0.0":
  "integrity" "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs="
  "resolved" "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://registry.npm.taobao.org/requires-port/download/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@^1.5.0":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-cwd@^2.0.0":
  "integrity" "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo="
  "resolved" "https://registry.npm.taobao.org/resolve-cwd/download/resolve-cwd-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "resolve-from" "^3.0.0"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "https://registry.npm.taobao.org/resolve-from/download/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="
  "resolved" "https://registry.npm.taobao.org/resolve-from/download/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.10.0", "resolve@^1.14.2", "resolve@^1.20.0":
  "integrity" "sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU="
  "resolved" "https://registry.npm.taobao.org/resolve/download/resolve-1.20.0.tgz"
  "version" "1.20.0"
  dependencies:
    "is-core-module" "^2.2.0"
    "path-parse" "^1.0.6"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://registry.npm.taobao.org/restore-cursor/download/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"restore-cursor@^3.1.0":
  "integrity" "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34="
  "resolved" "https://registry.npm.taobao.org/restore-cursor/download/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
  "resolved" "https://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz"
  "version" "0.1.15"

"retry@^0.12.0":
  "integrity" "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs="
  "resolved" "https://registry.npm.taobao.org/retry/download/retry-0.12.0.tgz"
  "version" "0.12.0"

"rgb-regex@^1.0.1":
  "integrity" "sha1-wODWiC3w4jviVKR16O3UGRX+rrE="
  "resolved" "https://registry.npm.taobao.org/rgb-regex/download/rgb-regex-1.0.1.tgz"
  "version" "1.0.1"

"rgba-regex@^1.0.0":
  "integrity" "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM="
  "resolved" "https://registry.npm.taobao.org/rgba-regex/download/rgba-regex-1.0.0.tgz"
  "version" "1.0.0"

"rimraf@^2.5.4", "rimraf@^2.6.1", "rimraf@^2.6.3":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz?cache=0&sync_timestamp=1581257110269&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frimraf%2Fdownload%2Frimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.2":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "https://registry.nlark.com/rimraf/download/rimraf-3.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frimraf%2Fdownload%2Frimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw="
  "resolved" "https://registry.npm.taobao.org/ripemd160/download/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"run-async@^2.4.0":
  "integrity" "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU="
  "resolved" "https://registry.npm.taobao.org/run-async/download/run-async-2.4.1.tgz"
  "version" "2.4.1"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "https://registry.npm.taobao.org/run-queue/download/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"rxjs@^6.6.0", "rxjs@^6.6.7":
  "integrity" "sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk="
  "resolved" "https://registry.nlark.com/rxjs/download/rxjs-6.6.7.tgz?cache=0&sync_timestamp=1621619189225&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frxjs%2Fdownload%2Frxjs-6.6.7.tgz"
  "version" "6.6.7"
  dependencies:
    "tslib" "^1.9.0"

"sa-sdk-javascript@^1.17.2":
  "integrity" "sha1-/V+Z8yNMlDYXf2jamXI0xyiC0Lg="
  "resolved" "https://registry.nlark.com/sa-sdk-javascript/download/sa-sdk-javascript-1.17.2.tgz?cache=0&sync_timestamp=1622458870688&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsa-sdk-javascript%2Fdownload%2Fsa-sdk-javascript-1.17.2.tgz"
  "version" "1.17.2"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@>=5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@^5.2.0":
  "integrity" "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="
  "resolved" "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://registry.npm.taobao.org/safe-regex/download/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "https://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sax@~1.2.4":
  "integrity" "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="
  "resolved" "https://registry.npm.taobao.org/sax/download/sax-1.2.4.tgz"
  "version" "1.2.4"

"schema-utils@^0.4.0":
  "integrity" "sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^1.0.0":
  "integrity" "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A="
  "resolved" "https://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-errors" "^1.0.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^2.0.0", "schema-utils@^2.5.0", "schema-utils@^2.6.5", "schema-utils@^2.7.0":
  "integrity" "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc="
  "resolved" "https://registry.npm.taobao.org/schema-utils/download/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.0.0":
  "integrity" "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"select-hose@^2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://registry.npm.taobao.org/select-hose/download/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"select@^1.1.2":
  "integrity" "sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0="
  "resolved" "https://registry.npm.taobao.org/select/download/select-1.1.2.tgz"
  "version" "1.1.2"

"selfsigned@^1.10.8":
  "integrity" "sha1-JJKc2Qb+D0S20B+yOZmnOVN6y+k="
  "resolved" "https://registry.nlark.com/selfsigned/download/selfsigned-1.10.11.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fselfsigned%2Fdownload%2Fselfsigned-1.10.11.tgz"
  "version" "1.10.11"
  dependencies:
    "node-forge" "^0.10.0"

"semver-compare@^1.0.0":
  "integrity" "sha1-De4hahyUGrN+nvsXiPavxf9VN/w="
  "resolved" "https://registry.npm.taobao.org/semver-compare/download/semver-compare-1.0.0.tgz"
  "version" "1.0.0"

"semver@^5.5.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1616463550093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.6.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1616463550093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.7.1":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1616463603361&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0", "semver@^6.1.0", "semver@^6.1.1", "semver@^6.1.2", "semver@^6.3.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1616463550093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^7.2.1":
  "integrity" "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1616463603361&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.5":
  "integrity" "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1616463550093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  "version" "5.7.1"

"semver@7.0.0":
  "integrity" "sha1-XzyjV2HkfgWyBsba/yz4FPAxa44="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-7.0.0.tgz?cache=0&sync_timestamp=1616463550093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-7.0.0.tgz"
  "version" "7.0.0"

"send@0.17.1":
  "integrity" "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg="
  "resolved" "https://registry.npm.taobao.org/send/download/send-0.17.1.tgz"
  "version" "0.17.1"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.7.2"
    "mime" "1.6.0"
    "ms" "2.1.1"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.1"
    "statuses" "~1.5.0"

"serialize-javascript@^4.0.0":
  "integrity" "sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao="
  "resolved" "https://registry.npm.taobao.org/serialize-javascript/download/serialize-javascript-4.0.0.tgz?cache=0&sync_timestamp=1599740699862&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@^1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://registry.npm.taobao.org/serve-index/download/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.14.1":
  "integrity" "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk="
  "resolved" "https://registry.npm.taobao.org/serve-static/download/serve-static-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.17.1"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
  "resolved" "https://registry.nlark.com/set-value/download/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="
  "resolved" "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.1.1":
  "integrity" "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="
  "resolved" "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.1.tgz"
  "version" "1.1.1"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc="
  "resolved" "https://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo="
  "resolved" "https://registry.npm.taobao.org/shebang-command/download/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="
  "resolved" "https://registry.nlark.com/shebang-regex/download/shebang-regex-3.0.0.tgz?cache=0&sync_timestamp=1628896299850&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.6.1":
  "integrity" "sha1-Z6fQLHbJ2iT5nSCAj8re0ODgS+I="
  "resolved" "https://registry.npm.taobao.org/shell-quote/download/shell-quote-1.7.2.tgz"
  "version" "1.7.2"

"shvl@^2.0.3":
  "integrity" "sha512-V7C6S9Hlol6SzOJPnQ7qzOVEWUQImt3BNmmzh40wObhla3XOYMe4gGiYzLrJd5TFa+cI2f9LKIRJTTKZSTbWgw=="
  "resolved" "https://registry.npmmirror.com/shvl/-/shvl-2.0.3.tgz"
  "version" "2.0.3"

"side-channel@^1.0.4":
  "integrity" "sha1-785cj9wQTudRslxY1CkAEfpeos8="
  "resolved" "https://registry.npm.taobao.org/side-channel/download/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2", "signal-exit@^3.0.3":
  "integrity" "sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw="
  "resolved" "https://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.3.tgz"
  "version" "3.0.3"

"simple-swizzle@^0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
  "resolved" "https://registry.npm.taobao.org/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"slash@^1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "https://registry.npm.taobao.org/slash/download/slash-1.0.0.tgz"
  "version" "1.0.0"

"slash@^2.0.0":
  "integrity" "sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q="
  "resolved" "https://registry.npm.taobao.org/slash/download/slash-2.0.0.tgz"
  "version" "2.0.0"

"slice-ansi@^3.0.0":
  "integrity" "sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c="
  "resolved" "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-3.0.0.tgz?cache=0&sync_timestamp=1618554953055&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fslice-ansi%2Fdownload%2Fslice-ansi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"slice-ansi@^4.0.0":
  "integrity" "sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms="
  "resolved" "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-4.0.0.tgz?cache=0&sync_timestamp=1618554953055&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fslice-ansi%2Fdownload%2Fslice-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
  "resolved" "https://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
  "resolved" "https://registry.npm.taobao.org/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
  "resolved" "https://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sockjs-client@^1.5.0":
  "integrity" "sha1-JWkI9tWt+5Tau9vQLGY2LMoPnqY="
  "resolved" "https://registry.npm.taobao.org/sockjs-client/download/sockjs-client-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "debug" "^3.2.6"
    "eventsource" "^1.0.7"
    "faye-websocket" "^0.11.3"
    "inherits" "^2.0.4"
    "json3" "^3.3.3"
    "url-parse" "^1.5.1"

"sockjs@^0.3.21":
  "integrity" "sha1-s0/7mOeWkwtgoM+hGQTWozmn1Bc="
  "resolved" "https://registry.npm.taobao.org/sockjs/download/sockjs-0.3.21.tgz?cache=0&sync_timestamp=1596167327079&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsockjs%2Fdownload%2Fsockjs-0.3.21.tgz"
  "version" "0.3.21"
  dependencies:
    "faye-websocket" "^0.11.3"
    "uuid" "^3.4.0"
    "websocket-driver" "^0.7.4"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "https://registry.npm.taobao.org/sort-keys/download/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-list-map@^2.0.0":
  "integrity" "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ="
  "resolved" "https://registry.npm.taobao.org/source-list-map/download/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-resolve@^0.5.0":
  "integrity" "sha1-GQhmvs51U+H48mei7oLGBrVQmho="
  "resolved" "https://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.3.tgz?cache=0&sync_timestamp=1584831908370&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-resolve%2Fdownload%2Fsource-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@~0.5.12":
  "integrity" "sha1-qYti+G3K9PZzmWSMCFKRq56P7WE="
  "resolved" "https://registry.npm.taobao.org/source-map-support/download/source-map-support-0.5.19.tgz?cache=0&sync_timestamp=1587719289626&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.19.tgz"
  "version" "0.5.19"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha1-CvZmBadFpaL5HPG7+KevvCg97FY="
  "resolved" "https://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.0", "source-map@^0.5.6":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha1-UwL4FpAxc1ImVECS5kmB91F1A4M="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.7.3.tgz"
  "version" "0.7.3"

"source-map@~0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"spdx-correct@^3.0.0":
  "integrity" "sha1-3s6BrJweZxPl99G28X1Gj6U9iak="
  "resolved" "https://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0="
  "resolved" "https://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk="
  "resolved" "https://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha1-illRNd75WSvaaXCUdPHL7qfCRn8="
  "resolved" "https://registry.nlark.com/spdx-license-ids/download/spdx-license-ids-3.0.9.tgz"
  "version" "3.0.9"

"spdy-transport@^3.0.0":
  "integrity" "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE="
  "resolved" "https://registry.npm.taobao.org/spdy-transport/download/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s="
  "resolved" "https://registry.npm.taobao.org/spdy/download/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
  "resolved" "https://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@^1.7.0":
  "integrity" "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc="
  "resolved" "https://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz"
  "version" "1.16.1"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssr-window@^2.0.0":
  "integrity" "sha1-mMMBrvmVIzF/jWlhjwAQeRCW78Q="
  "resolved" "https://registry.nlark.com/ssr-window/download/ssr-window-2.0.0.tgz?cache=0&sync_timestamp=1629879256060&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fssr-window%2Fdownload%2Fssr-window-2.0.0.tgz"
  "version" "2.0.0"

"ssri@^6.0.1":
  "integrity" "sha1-FXk5E08gRk5zAd26PpD/qPdyisU="
  "resolved" "https://registry.nlark.com/ssri/download/ssri-6.0.2.tgz?cache=0&sync_timestamp=1621364918494&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fssri%2Fdownload%2Fssri-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "figgy-pudding" "^3.5.1"

"ssri@^8.0.1":
  "integrity" "sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8="
  "resolved" "https://registry.nlark.com/ssri/download/ssri-8.0.1.tgz?cache=0&sync_timestamp=1621364918494&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fssri%2Fdownload%2Fssri-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "minipass" "^3.1.1"

"stable@^0.1.8":
  "integrity" "sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88="
  "resolved" "https://registry.npm.taobao.org/stable/download/stable-0.1.8.tgz"
  "version" "0.1.8"

"stackframe@^1.1.1":
  "integrity" "sha1-UkKUktY8YuuYmATBFVLj0i53kwM="
  "resolved" "https://registry.npm.taobao.org/stackframe/download/stackframe-1.2.0.tgz?cache=0&sync_timestamp=1590854170093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstackframe%2Fdownload%2Fstackframe-1.2.0.tgz"
  "version" "1.2.0"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1609654060878&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz"
  "version" "1.5.0"

"stream-browserify@^2.0.1":
  "integrity" "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs="
  "resolved" "https://registry.npm.taobao.org/stream-browserify/download/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64="
  "resolved" "https://registry.npm.taobao.org/stream-each/download/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw="
  "resolved" "https://registry.npm.taobao.org/stream-http/download/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha1-1wiCgVWasneEJCebCHfaPDktWj0="
  "resolved" "https://registry.npm.taobao.org/stream-shift/download/stream-shift-1.0.1.tgz"
  "version" "1.0.1"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "https://registry.npm.taobao.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.0.0", "string_decoder@^1.1.1", "string_decoder@~1.1.1":
  "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
  "resolved" "https://registry.npm.taobao.org/string_decoder/download/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-argv@0.3.1":
  "integrity" "sha1-leL77AQnrhkYSTX4FtdKqkxcGdo="
  "resolved" "https://registry.npm.taobao.org/string-argv/download/string-argv-0.3.1.tgz"
  "version" "0.3.1"

"string-width@^2.0.0":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^3.0.0", "string-width@^3.1.0":
  "integrity" "sha1-InZ74htirxCBV0MG9prFG2IgOWE="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "^7.0.1"
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^5.1.0"

"string-width@^4.1.0", "string-width@^4.2.0":
  "integrity" "sha1-2v1PlVmnWFz7pSnGoKT3NIjr1MU="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.0"

"string.prototype.trimend@^1.0.4":
  "integrity" "sha1-51rpDClCxjUEaGwYsoe0oLGkX4A="
  "resolved" "https://registry.npm.taobao.org/string.prototype.trimend/download/string.prototype.trimend-1.0.4.tgz?cache=0&sync_timestamp=1614127461586&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimend%2Fdownload%2Fstring.prototype.trimend-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"string.prototype.trimstart@^1.0.4":
  "integrity" "sha1-s2OZr0qymZtMnGSL16P7K7Jv7u0="
  "resolved" "https://registry.npm.taobao.org/string.prototype.trimstart/download/string.prototype.trimstart-1.0.4.tgz?cache=0&sync_timestamp=1614127299808&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimstart%2Fdownload%2Fstring.prototype.trimstart-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"stringify-object@^3.3.0":
  "integrity" "sha1-cDBlrvyhkwDTzoivT1s5VtdVZik="
  "resolved" "https://registry.npm.taobao.org/stringify-object/download/stringify-object-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "get-own-enumerable-property-symbols" "^3.0.0"
    "is-obj" "^1.0.1"
    "is-regexp" "^1.0.0"

"strip-ansi@^3.0.0":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&sync_timestamp=1618846877845&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&sync_timestamp=1618846877845&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-4.0.0.tgz?cache=0&sync_timestamp=1618846877845&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-ansi@^5.0.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1618846877845&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5.1.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1618846877845&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5.2.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1618846877845&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1618846877845&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^6.0.0":
  "integrity" "sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-6.0.0.tgz?cache=0&sync_timestamp=1618846877845&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "ansi-regex" "^5.0.0"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://registry.nlark.com/strip-bom/download/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://registry.npm.taobao.org/strip-eof/download/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0="
  "resolved" "https://registry.nlark.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz?cache=0&sync_timestamp=1620046554687&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-final-newline%2Fdownload%2Fstrip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^2.0.0":
  "integrity" "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g="
  "resolved" "https://registry.nlark.com/strip-indent/download/strip-indent-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^3.1.0", "strip-json-comments@^3.1.1":
  "integrity" "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY="
  "resolved" "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-3.1.1.tgz?cache=0&sync_timestamp=1594567543744&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-json-comments%2Fdownload%2Fstrip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"stylehacks@^4.0.0":
  "integrity" "sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU="
  "resolved" "https://registry.nlark.com/stylehacks/download/stylehacks-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://registry.nlark.com/supports-color/download/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^5.3.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1618561008172&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^6.1.0":
  "integrity" "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM="
  "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-6.1.0.tgz?cache=0&sync_timestamp=1618561008172&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"svg-tags@^1.0.0":
  "integrity" "sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q="
  "resolved" "https://registry.npm.taobao.org/svg-tags/download/svg-tags-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^1.0.0":
  "integrity" "sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc="
  "resolved" "https://registry.npm.taobao.org/svgo/download/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"swiper@^5.3.6":
  "integrity" "sha1-o1D2VL9oQm27ZReTgkklUS0iPA8="
  "resolved" "https://registry.nlark.com/swiper/download/swiper-5.4.5.tgz?cache=0&sync_timestamp=1631782527360&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fswiper%2Fdownload%2Fswiper-5.4.5.tgz"
  "version" "5.4.5"
  dependencies:
    "dom7" "^2.1.5"
    "ssr-window" "^2.0.0"

"table@^6.0.9":
  "integrity" "sha1-7gVZK3FDgxqMlPPO5qrkwczvM+I="
  "resolved" "https://registry.nlark.com/table/download/table-6.7.1.tgz?cache=0&sync_timestamp=1620957183690&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftable%2Fdownload%2Ftable-6.7.1.tgz"
  "version" "6.7.1"
  dependencies:
    "ajv" "^8.0.1"
    "lodash.clonedeep" "^4.5.0"
    "lodash.truncate" "^4.4.2"
    "slice-ansi" "^4.0.0"
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"

"tapable@^0.1.8":
  "integrity" "sha1-KcNXB8K3DlDQdIK10gLo7URtr9Q="
  "resolved" "https://registry.npm.taobao.org/tapable/download/tapable-0.1.10.tgz?cache=0&sync_timestamp=1607088855476&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftapable%2Fdownload%2Ftapable-0.1.10.tgz"
  "version" "0.1.10"

"tapable@^1.0.0", "tapable@^1.1.3":
  "integrity" "sha1-ofzMBrWNth/XpF2i2kT186Pme6I="
  "resolved" "https://registry.npm.taobao.org/tapable/download/tapable-1.1.3.tgz?cache=0&sync_timestamp=1607088825527&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftapable%2Fdownload%2Ftapable-1.1.3.tgz"
  "version" "1.1.3"

"terser-webpack-plugin@^1.4.3", "terser-webpack-plugin@^1.4.4":
  "integrity" "sha1-oheu+uozDnNP+sthIOwfoxLWBAs="
  "resolved" "https://registry.nlark.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.5.tgz?cache=0&sync_timestamp=1620830706425&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fterser-webpack-plugin%2Fdownload%2Fterser-webpack-plugin-1.4.5.tgz"
  "version" "1.4.5"
  dependencies:
    "cacache" "^12.0.2"
    "find-cache-dir" "^2.1.0"
    "is-wsl" "^1.1.0"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^4.0.0"
    "source-map" "^0.6.1"
    "terser" "^4.1.2"
    "webpack-sources" "^1.4.0"
    "worker-farm" "^1.7.0"

"terser@^4.1.2":
  "integrity" "sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc="
  "resolved" "https://registry.nlark.com/terser/download/terser-4.8.0.tgz"
  "version" "4.8.0"
  dependencies:
    "commander" "^2.20.0"
    "source-map" "~0.6.1"
    "source-map-support" "~0.5.12"

"text-segmentation@^1.0.2":
  "integrity" "sha1-H4KPoUqhAcEU3tG9o1un3MF8mFg="
  "resolved" "https://registry.nlark.com/text-segmentation/download/text-segmentation-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "utrie" "^1.0.1"

"text-table@^0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://registry.npm.taobao.org/text-table/download/text-table-0.2.0.tgz"
  "version" "0.2.0"

"thenify-all@^1.0.0":
  "integrity" "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY="
  "resolved" "https://registry.npm.taobao.org/thenify-all/download/thenify-all-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "thenify" ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  "integrity" "sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8="
  "resolved" "https://registry.npm.taobao.org/thenify/download/thenify-3.3.1.tgz?cache=0&sync_timestamp=1592416260110&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthenify%2Fdownload%2Fthenify-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "any-promise" "^1.0.0"

"thread-loader@^2.1.3":
  "integrity" "sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo="
  "resolved" "https://registry.nlark.com/thread-loader/download/thread-loader-2.1.3.tgz?cache=0&sync_timestamp=1620664193759&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fthread-loader%2Fdownload%2Fthread-loader-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "loader-runner" "^2.3.1"
    "loader-utils" "^1.1.0"
    "neo-async" "^2.6.0"

"throttle-debounce@^1.0.1":
  "integrity" "sha512-XH8UiPCQcWNuk2LYePibW/4qL97+ZQ1AN3FNXwZRBNPPowo/NRU5fAlDCSNBJIYCKbioZfuYtMhG4quqoJhVzg=="
  "resolved" "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-1.1.0.tgz"
  "version" "1.1.0"

"throttle-debounce@^2.0.1":
  "integrity" "sha512-H7oLPV0P7+jgvrk+6mwwwBDmxTaxnu9HMXmloNLXwnNO0ZxZ31Orah2n8lU1eMPvsaowP2CX+USCgyovXfdOFQ=="
  "resolved" "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-2.3.0.tgz"
  "version" "2.3.0"

"through@^2.3.6", "through@^2.3.8":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
  "resolved" "https://registry.npm.taobao.org/through/download/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^2.0.0":
  "integrity" "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0="
  "resolved" "https://registry.npm.taobao.org/through2/download/through2-2.0.5.tgz?cache=0&sync_timestamp=1593478628425&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthrough2%2Fdownload%2Fthrough2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunky@^1.0.2":
  "integrity" "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30="
  "resolved" "https://registry.npm.taobao.org/thunky/download/thunky-1.1.0.tgz"
  "version" "1.1.0"

"timers-browserify@^2.0.4":
  "integrity" "sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4="
  "resolved" "https://registry.npm.taobao.org/timers-browserify/download/timers-browserify-2.0.12.tgz?cache=0&sync_timestamp=1603793718173&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftimers-browserify%2Fdownload%2Ftimers-browserify-2.0.12.tgz"
  "version" "2.0.12"
  dependencies:
    "setimmediate" "^1.0.4"

"timsort@^0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://registry.npm.taobao.org/timsort/download/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tiny-emitter@^2.0.0":
  "integrity" "sha1-HRpW7fxRxD6GPLtTgqcjMONVVCM="
  "resolved" "https://registry.npm.taobao.org/tiny-emitter/download/tiny-emitter-2.1.0.tgz"
  "version" "2.1.0"

"tmp@^0.0.33":
  "integrity" "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk="
  "resolved" "https://registry.npm.taobao.org/tmp/download/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"to-arraybuffer@^1.0.0":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "https://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^1.0.3":
  "integrity" "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="
  "resolved" "https://registry.nlark.com/to-fast-properties/download/to-fast-properties-1.0.3.tgz?cache=0&sync_timestamp=1628418855671&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fto-fast-properties%2Fdownload%2Fto-fast-properties-1.0.3.tgz"
  "version" "1.0.3"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://registry.npm.taobao.org/to-object-path/download/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://registry.npm.taobao.org/to-regex-range/download/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ="
  "resolved" "https://registry.npm.taobao.org/to-regex-range/download/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
  "resolved" "https://registry.npm.taobao.org/to-regex/download/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.0":
  "integrity" "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="
  "resolved" "https://registry.npm.taobao.org/toidentifier/download/toidentifier-1.0.0.tgz"
  "version" "1.0.0"

"toposort@^1.0.0":
  "integrity" "sha1-LmhELZ9k7HILjMieZEOsbKqVACk="
  "resolved" "https://registry.npm.taobao.org/toposort/download/toposort-1.0.7.tgz"
  "version" "1.0.7"

"tough-cookie@~2.5.0":
  "integrity" "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI="
  "resolved" "https://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftough-cookie%2Fdownload%2Ftough-cookie-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"tryer@^1.0.1":
  "integrity" "sha1-8shUBoALmw90yfdGW4HqrSQSUvg="
  "resolved" "https://registry.npm.taobao.org/tryer/download/tryer-1.0.1.tgz"
  "version" "1.0.1"

"ts-pnp@^1.1.6":
  "integrity" "sha1-pQCtCEsHmPHDBxrzkeZZEshrypI="
  "resolved" "https://registry.npm.taobao.org/ts-pnp/download/ts-pnp-1.2.0.tgz"
  "version" "1.2.0"

"tsconfig-paths@^3.9.0":
  "integrity" "sha1-ea5npowVKJ/fXFHLdPOXUi15Xtc="
  "resolved" "https://registry.nlark.com/tsconfig-paths/download/tsconfig-paths-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "json5" "^2.2.0"
    "minimist" "^1.2.0"
    "strip-bom" "^3.0.0"

"tslib@^1.10.0", "tslib@^1.9.0":
  "integrity" "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="
  "resolved" "https://registry.nlark.com/tslib/download/tslib-1.14.1.tgz?cache=0&sync_timestamp=1618847132149&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftslib%2Fdownload%2Ftslib-1.14.1.tgz"
  "version" "1.14.1"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "https://registry.npm.taobao.org/tty-browserify/download/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@^0.6.0":
  "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
  "resolved" "https://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
  "resolved" "https://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE="
  "resolved" "https://registry.npm.taobao.org/type-check/download/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-fest@^0.20.2":
  "integrity" "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ="
  "resolved" "https://registry.nlark.com/type-fest/download/type-fest-0.20.2.tgz?cache=0&sync_timestamp=1628211380097&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^0.21.3":
  "integrity" "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc="
  "resolved" "https://registry.nlark.com/type-fest/download/type-fest-0.21.3.tgz?cache=0&sync_timestamp=1621402383646&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.6.0":
  "integrity" "sha1-jSojcNPfiG61yQraHFv2GIrPg4s="
  "resolved" "https://registry.nlark.com/type-fest/download/type-fest-0.6.0.tgz?cache=0&sync_timestamp=1621402383646&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-is@~1.6.17", "type-is@~1.6.18":
  "integrity" "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE="
  "resolved" "https://registry.npm.taobao.org/type-is/download/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "https://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"uglify-js@3.4.x":
  "integrity" "sha1-mtlWPY6zrN+404WX0q8dgV9qdV8="
  "resolved" "https://registry.nlark.com/uglify-js/download/uglify-js-3.4.10.tgz?cache=0&sync_timestamp=1621369441167&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fuglify-js%2Fdownload%2Fuglify-js-3.4.10.tgz"
  "version" "3.4.10"
  dependencies:
    "commander" "~2.19.0"
    "source-map" "~0.6.1"

"unbox-primitive@^1.0.0", "unbox-primitive@^1.0.1":
  "integrity" "sha1-CF4hViXsMWJXTciFmr7nilmxRHE="
  "resolved" "https://registry.npm.taobao.org/unbox-primitive/download/unbox-primitive-1.0.1.tgz?cache=0&sync_timestamp=1616706427948&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funbox-primitive%2Fdownload%2Funbox-primitive-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has-bigints" "^1.0.1"
    "has-symbols" "^1.0.2"
    "which-boxed-primitive" "^1.0.2"

"unicode-canonical-property-names-ecmascript@^1.0.4":
  "integrity" "sha1-JhmADEyCWADv3YNDr33Zkzy+KBg="
  "resolved" "https://registry.npm.taobao.org/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz"
  "version" "1.0.4"

"unicode-match-property-ecmascript@^1.0.4":
  "integrity" "sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw="
  "resolved" "https://registry.npm.taobao.org/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^1.0.4"
    "unicode-property-aliases-ecmascript" "^1.0.4"

"unicode-match-property-value-ecmascript@^1.2.0":
  "integrity" "sha1-DZH2AO7rMJaqlisdb8iIduZOpTE="
  "resolved" "https://registry.npm.taobao.org/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.2.0.tgz"
  "version" "1.2.0"

"unicode-property-aliases-ecmascript@^1.0.4":
  "integrity" "sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ="
  "resolved" "https://registry.npm.taobao.org/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.1.0.tgz?cache=0&sync_timestamp=1583945805856&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funicode-property-aliases-ecmascript%2Fdownload%2Funicode-property-aliases-ecmascript-1.1.0.tgz"
  "version" "1.1.0"

"union-value@^1.0.0":
  "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
  "resolved" "https://registry.npm.taobao.org/union-value/download/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "https://registry.npm.taobao.org/uniq/download/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "https://registry.npm.taobao.org/uniqs/download/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.1":
  "integrity" "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA="
  "resolved" "https://registry.npm.taobao.org/unique-filename/download/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw="
  "resolved" "https://registry.npm.taobao.org/unique-slug/download/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"

"universalify@^0.1.0":
  "integrity" "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="
  "resolved" "https://registry.npm.taobao.org/universalify/download/universalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@~1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="
  "resolved" "https://registry.npm.taobao.org/unquote/download/unquote-1.1.1.tgz"
  "version" "1.1.1"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://registry.npm.taobao.org/unset-value/download/unset-value-1.0.0.tgz?cache=0&sync_timestamp=1616088640915&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funset-value%2Fdownload%2Funset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"upath@^1.1.1":
  "integrity" "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ="
  "resolved" "https://registry.npm.taobao.org/upath/download/upath-1.2.0.tgz?cache=0&sync_timestamp=1604768693775&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fupath%2Fdownload%2Fupath-1.2.0.tgz"
  "version" "1.2.0"

"upper-case@^1.1.1":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "https://registry.npm.taobao.org/upper-case/download/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34="
  "resolved" "https://registry.npm.taobao.org/uri-js/download/uri-js-4.4.1.tgz?cache=0&sync_timestamp=1610237756396&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furi-js%2Fdownload%2Furi-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://registry.npm.taobao.org/urix/download/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@^2.2.0":
  "integrity" "sha1-4OLvZY8APvuMpBsPP/v3a6uIZYs="
  "resolved" "https://registry.npm.taobao.org/url-loader/download/url-loader-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "loader-utils" "^1.2.3"
    "mime" "^2.4.4"
    "schema-utils" "^2.5.0"

"url-parse@^1.4.3", "url-parse@^1.5.1":
  "integrity" "sha1-1fqYkK+KXh8nSiyYN2UQ9kJfbjs="
  "resolved" "https://registry.npm.taobao.org/url-parse/download/url-parse-1.5.1.tgz?cache=0&sync_timestamp=1613660606481&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furl-parse%2Fdownload%2Furl-parse-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url@^0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://registry.npm.taobao.org/url/download/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
  "resolved" "https://registry.npm.taobao.org/use/download/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@~1.0.0":
  "integrity" "sha1-a693dLgO6w91INi4HQeYKlmruu4="
  "resolved" "https://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.1.tgz?cache=0&sync_timestamp=1610159885628&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.2"
    "has-symbols" "^1.0.1"
    "object.getownpropertydescriptors" "^2.1.0"

"util.promisify@1.0.0":
  "integrity" "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA="
  "resolved" "https://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.0.tgz?cache=0&sync_timestamp=1610159885628&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "object.getownpropertydescriptors" "^2.0.3"

"util@^0.11.0":
  "integrity" "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE="
  "resolved" "https://registry.npm.taobao.org/util/download/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "https://registry.npm.taobao.org/util/download/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://registry.npm.taobao.org/utila/download/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"utrie@^1.0.1":
  "integrity" "sha1-4VUjXry93ImuCSYatudzzmFAGy8="
  "resolved" "https://registry.nlark.com/utrie/download/utrie-1.0.1.tgz?cache=0&sync_timestamp=1628857080382&other_urls=https%3A%2F%2Fregistry.nlark.com%2Futrie%2Fdownload%2Futrie-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "base64-arraybuffer" "^1.0.1"

"uuid@^3.3.2", "uuid@^3.4.0":
  "integrity" "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="
  "resolved" "https://registry.npm.taobao.org/uuid/download/uuid-3.4.0.tgz?cache=0&sync_timestamp=1607460077975&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fuuid%2Fdownload%2Fuuid-3.4.0.tgz"
  "version" "3.4.0"

"v-viewer@^1.6.4":
  "integrity" "sha512-LVkiUHpmsbsZXebeNXnu8krRCi5i2n07FeLFxoIVGhw8lVvTBO0ffpbDC6mLEuacCjrIh09HjIqpciwUtWE8lQ=="
  "resolved" "https://registry.npmjs.org/v-viewer/-/v-viewer-1.6.4.tgz"
  "version" "1.6.4"
  dependencies:
    "throttle-debounce" "^2.0.1"
    "viewerjs" "^1.5.0"

"v8-compile-cache@^2.0.3":
  "integrity" "sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4="
  "resolved" "https://registry.npm.taobao.org/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz?cache=0&sync_timestamp=1614993639567&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fv8-compile-cache%2Fdownload%2Fv8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha1-/JH2uce6FchX9MssXe/uw51PQQo="
  "resolved" "https://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vant@^2.2.0":
  "integrity" "sha1-1OvZHD2Ip8vkyRDtwlAobMOic8I="
  "resolved" "https://registry.nlark.com/vant/download/vant-2.12.20.tgz"
  "version" "2.12.20"
  dependencies:
    "@babel/runtime" "7.x"
    "@vant/icons" "^1.5.3"
    "@vant/popperjs" "^1.0.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "vue-lazyload" "1.2.3"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.npm.taobao.org/vary/download/vary-1.1.2.tgz"
  "version" "1.1.2"

"vconsole@^3.7.0":
  "integrity" "sha1-ezR/xjXrCWRrtQ8M4Mv7oYUq8bE="
  "resolved" "https://registry.nlark.com/vconsole/download/vconsole-3.7.0.tgz?cache=0&sync_timestamp=1622107564480&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fvconsole%2Fdownload%2Fvconsole-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "copy-text-to-clipboard" "^3.0.1"
    "core-js" "^3.11.0"
    "mutation-observer" "^1.0.3"

"vendors@^1.0.0":
  "integrity" "sha1-4rgApT56Kbk1BsPPQRANFsTErY4="
  "resolved" "https://registry.npm.taobao.org/vendors/download/vendors-1.0.4.tgz?cache=0&sync_timestamp=1615203397897&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvendors%2Fdownload%2Fvendors-1.0.4.tgz"
  "version" "1.0.4"

"verror@1.10.0":
  "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
  "resolved" "https://registry.npm.taobao.org/verror/download/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"viewerjs@^1.10.2", "viewerjs@^1.5.0":
  "integrity" "sha1-3hb6EGaOTaYyWWmDajJkoEbj75o="
  "resolved" "https://registry.npmmirror.com/viewerjs/download/viewerjs-1.10.2.tgz?cache=0&sync_timestamp=1635059505685&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fviewerjs%2Fdownload%2Fviewerjs-1.10.2.tgz"
  "version" "1.10.2"

"vm-browserify@^1.0.1":
  "integrity" "sha1-eGQcSIuObKkadfUR56OzKobl3aA="
  "resolved" "https://registry.npm.taobao.org/vm-browserify/download/vm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"vue-eslint-parser@^8.0.1":
  "integrity" "sha512-dzHGG3+sYwSf6zFBa0Gi9ZDshD7+ad14DGOdTLjruRVgZXe2J+DcZ9iUhyR48z5g1PqRa20yt3Njna/veLJL/g=="
  "resolved" "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.3.0.tgz"
  "version" "8.3.0"
  dependencies:
    "debug" "^4.3.2"
    "eslint-scope" "^7.0.0"
    "eslint-visitor-keys" "^3.1.0"
    "espree" "^9.0.0"
    "esquery" "^1.4.0"
    "lodash" "^4.17.21"
    "semver" "^7.3.5"

"vue-hot-reload-api@^2.3.0":
  "integrity" "sha1-UylVzB6yCKPZkLOp+acFdGV+CPI="
  "resolved" "https://registry.npm.taobao.org/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz"
  "version" "2.3.4"

"vue-lazyload@1.2.3":
  "integrity" "sha1-kB+ewVx+bKeHgaK65KNDaGve2yw="
  "resolved" "https://registry.npm.taobao.org/vue-lazyload/download/vue-lazyload-1.2.3.tgz"
  "version" "1.2.3"

"vue-loader-v16@npm:vue-loader@^16.1.0":
  "integrity" "sha512-7vKN45IxsKxe5GcVCbc2qFU5aWzyiLrYJyUuMz4BQLKctCj/fmCa0w6fGiiQ2cLFetNcek1ppGJQDCup0c1hpA=="
  "resolved" "https://registry.npmmirror.com/vue-loader/-/vue-loader-16.8.3.tgz"
  "version" "16.8.3"
  dependencies:
    "chalk" "^4.1.0"
    "hash-sum" "^2.0.0"
    "loader-utils" "^2.0.0"

"vue-loader@^15.9.2":
  "integrity" "sha1-FbBXdcPgw4QHZ5OTws5t9nOwEEQ="
  "resolved" "https://registry.nlark.com/vue-loader/download/vue-loader-15.9.7.tgz?cache=0&sync_timestamp=1620717857145&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fvue-loader%2Fdownload%2Fvue-loader-15.9.7.tgz"
  "version" "15.9.7"
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "vue-hot-reload-api" "^2.3.0"
    "vue-style-loader" "^4.1.0"

"vue-lottie@^0.2.1":
  "integrity" "sha512-zInUX69Ij8MhVR3XArpu4PqqBoufwKxS5UMutWCPm59VUaB5H6GtnaIzf9M+l6aYU+Kr8gF/W9dzWLgRuU6V+Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-lottie/-/vue-lottie-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "lottie-web" "^5.1.9"

"vue-pdf@^4.3.0":
  "integrity" "sha512-zd3lJj6CbtrawgaaDDciTDjkJMUKiLWtbEmBg5CvFn9Noe9oAO/GNy/fc5c59qGuFCJ14ibIV1baw4S07e5bSQ=="
  "resolved" "https://registry.npmjs.org/vue-pdf/-/vue-pdf-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "babel-plugin-syntax-dynamic-import" "^6.18.0"
    "loader-utils" "^1.4.0"
    "pdfjs-dist" "2.6.347"
    "raw-loader" "^4.0.2"
    "vue-resize-sensor" "^2.0.0"
    "worker-loader" "^2.0.0"

"vue-resize-sensor@^2.0.0":
  "integrity" "sha512-W+y2EAI/BxS4Vlcca9scQv8ifeBFck56DRtSwWJ2H4Cw1GLNUYxiZxUHHkuzuI5JPW/cYtL1bPO5xPyEXx4LmQ=="
  "resolved" "https://registry.npmjs.org/vue-resize-sensor/-/vue-resize-sensor-2.0.0.tgz"
  "version" "2.0.0"

"vue-router@^3.0.7":
  "integrity" "sha1-7fPPSQeVLR4Fg+B5I3Igxf9utsk="
  "resolved" "https://registry.nlark.com/vue-router/download/vue-router-3.5.1.tgz"
  "version" "3.5.1"

"vue-style-loader@^4.1.0", "vue-style-loader@^4.1.2":
  "integrity" "sha1-bVWGOlH6dXqyTonZNxRlByqnvDU="
  "resolved" "https://registry.npm.taobao.org/vue-style-loader/download/vue-style-loader-4.1.3.tgz?cache=0&sync_timestamp=1614758661292&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-style-loader%2Fdownload%2Fvue-style-loader-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-compiler@^2.6.10":
  "integrity" "sha1-lH7XGWdEyKUoXr4SM/6WBDf8xX4="
  "resolved" "https://registry.npm.taobao.org/vue-template-compiler/download/vue-template-compiler-2.6.12.tgz?cache=0&sync_timestamp=1597927338574&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-template-compiler%2Fdownload%2Fvue-template-compiler-2.6.12.tgz"
  "version" "2.6.12"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.1.0"

"vue-template-es2015-compiler@^1.9.0":
  "integrity" "sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU="
  "resolved" "https://registry.npm.taobao.org/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  "version" "1.9.1"

"vue@^2.5.16", "vue@^2.6.10":
  "integrity" "sha1-9evU+mvShpQD4pqJau1JBEVskSM="
  "resolved" "https://registry.nlark.com/vue/download/vue-2.6.12.tgz"
  "version" "2.6.12"

"vuex-persistedstate@^4.1.0":
  "integrity" "sha512-3SkEj4NqwM69ikJdFVw6gObeB0NHyspRYMYkR/EbhR0hbvAKyR5gksVhtAfY1UYuWUOCCA0QNGwv9pOwdj+XUQ=="
  "resolved" "https://registry.npmmirror.com/vuex-persistedstate/-/vuex-persistedstate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "deepmerge" "^4.2.2"
    "shvl" "^2.0.3"

"vuex@^3.6.2":
  "integrity" "sha512-ETW44IqCgBpVomy520DT5jf8n0zoCac+sxWnn+hMe/CzaSejb/eVw2YToiXYX+Ex/AuHHia28vWTq4goAexFbw=="
  "resolved" "https://registry.npmjs.org/vuex/-/vuex-3.6.2.tgz"
  "version" "3.6.2"

"watchpack-chokidar2@^2.0.1":
  "integrity" "sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc="
  "resolved" "https://registry.npm.taobao.org/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "chokidar" "^2.1.8"

"watchpack@^1.7.4":
  "integrity" "sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM="
  "resolved" "https://registry.nlark.com/watchpack/download/watchpack-1.7.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwatchpack%2Fdownload%2Fwatchpack-1.7.5.tgz"
  "version" "1.7.5"
  dependencies:
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"
  optionalDependencies:
    "chokidar" "^3.4.1"
    "watchpack-chokidar2" "^2.0.1"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98="
  "resolved" "https://registry.npm.taobao.org/wbuf/download/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"wcwidth@^1.0.1":
  "integrity" "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g="
  "resolved" "https://registry.npm.taobao.org/wcwidth/download/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webpack-bundle-analyzer@^3.8.0":
  "integrity" "sha1-9vlNsQj7V05BWtMT3kGicH0z7zw="
  "resolved" "https://registry.nlark.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.9.0.tgz"
  "version" "3.9.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"
    "bfj" "^6.1.1"
    "chalk" "^2.4.1"
    "commander" "^2.18.0"
    "ejs" "^2.6.1"
    "express" "^4.16.3"
    "filesize" "^3.6.1"
    "gzip-size" "^5.0.0"
    "lodash" "^4.17.19"
    "mkdirp" "^0.5.1"
    "opener" "^1.5.1"
    "ws" "^6.0.0"

"webpack-chain@^6.4.0":
  "integrity" "sha1-TycoTLu2N+PI+970Pu9YjU2GEgY="
  "resolved" "https://registry.npm.taobao.org/webpack-chain/download/webpack-chain-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "deepmerge" "^1.5.2"
    "javascript-stringify" "^2.0.1"

"webpack-dev-middleware@^3.7.2":
  "integrity" "sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU="
  "resolved" "https://registry.nlark.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "memory-fs" "^0.4.1"
    "mime" "^2.4.4"
    "mkdirp" "^0.5.1"
    "range-parser" "^1.2.1"
    "webpack-log" "^2.0.0"

"webpack-dev-server@^3.11.0":
  "integrity" "sha1-aV687Xakkp8NXef9c/r+GF/jNwg="
  "resolved" "https://registry.nlark.com/webpack-dev-server/download/webpack-dev-server-3.11.2.tgz"
  "version" "3.11.2"
  dependencies:
    "ansi-html" "0.0.7"
    "bonjour" "^3.5.0"
    "chokidar" "^2.1.8"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^1.6.0"
    "debug" "^4.1.1"
    "del" "^4.1.1"
    "express" "^4.17.1"
    "html-entities" "^1.3.1"
    "http-proxy-middleware" "0.19.1"
    "import-local" "^2.0.0"
    "internal-ip" "^4.3.0"
    "ip" "^1.1.5"
    "is-absolute-url" "^3.0.3"
    "killable" "^1.0.1"
    "loglevel" "^1.6.8"
    "opn" "^5.5.0"
    "p-retry" "^3.0.1"
    "portfinder" "^1.0.26"
    "schema-utils" "^1.0.0"
    "selfsigned" "^1.10.8"
    "semver" "^6.3.0"
    "serve-index" "^1.9.1"
    "sockjs" "^0.3.21"
    "sockjs-client" "^1.5.0"
    "spdy" "^4.0.2"
    "strip-ansi" "^3.0.1"
    "supports-color" "^6.1.0"
    "url" "^0.11.0"
    "webpack-dev-middleware" "^3.7.2"
    "webpack-log" "^2.0.0"
    "ws" "^6.2.1"
    "yargs" "^13.3.2"

"webpack-log@^2.0.0":
  "integrity" "sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8="
  "resolved" "https://registry.npm.taobao.org/webpack-log/download/webpack-log-2.0.0.tgz?cache=0&sync_timestamp=1615477493300&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-log%2Fdownload%2Fwebpack-log-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-colors" "^3.0.0"
    "uuid" "^3.3.2"

"webpack-merge@^4.2.2":
  "integrity" "sha1-onxS6ng9E5iv0gh/VH17nS9DY00="
  "resolved" "https://registry.npm.taobao.org/webpack-merge/download/webpack-merge-4.2.2.tgz?cache=0&sync_timestamp=1608705507443&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-merge%2Fdownload%2Fwebpack-merge-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "lodash" "^4.17.15"

"webpack-sources@^1.1.0", "webpack-sources@^1.4.0", "webpack-sources@^1.4.1":
  "integrity" "sha1-7t2OwLko+/HL/plOItLYkPMwqTM="
  "resolved" "https://registry.npm.taobao.org/webpack-sources/download/webpack-sources-1.4.3.tgz?cache=0&sync_timestamp=1603965314165&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-sources%2Fdownload%2Fwebpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@^4.0.0":
  "integrity" "sha1-v5tEBOogoHNgXgoBHRiNd8tq1UI="
  "resolved" "https://registry.nlark.com/webpack/download/webpack-4.46.0.tgz?cache=0&sync_timestamp=1621407470605&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwebpack%2Fdownload%2Fwebpack-4.46.0.tgz"
  "version" "4.46.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "acorn" "^6.4.1"
    "ajv" "^6.10.2"
    "ajv-keywords" "^3.4.1"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^4.5.0"
    "eslint-scope" "^4.0.3"
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^2.4.0"
    "loader-utils" "^1.2.3"
    "memory-fs" "^0.4.1"
    "micromatch" "^3.1.10"
    "mkdirp" "^0.5.3"
    "neo-async" "^2.6.1"
    "node-libs-browser" "^2.2.1"
    "schema-utils" "^1.0.0"
    "tapable" "^1.1.3"
    "terser-webpack-plugin" "^1.4.3"
    "watchpack" "^1.7.4"
    "webpack-sources" "^1.4.1"

"websocket-driver@^0.7.4", "websocket-driver@>=0.5.1":
  "integrity" "sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A="
  "resolved" "https://registry.npm.taobao.org/websocket-driver/download/websocket-driver-0.7.4.tgz?cache=0&sync_timestamp=1591288882525&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebsocket-driver%2Fdownload%2Fwebsocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha1-f4RzvIOd/YdgituV1+sHUhFXikI="
  "resolved" "https://registry.npm.taobao.org/websocket-extensions/download/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"weixin-js-sdk@^1.6.0":
  "integrity" "sha1-/1BITYEYzhII8RJIz0ocCDFXdRQ="
  "resolved" "https://registry.npm.taobao.org/weixin-js-sdk/download/weixin-js-sdk-1.6.0.tgz"
  "version" "1.6.0"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY="
  "resolved" "https://registry.npm.taobao.org/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.9":
  "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
  "resolved" "https://registry.npm.taobao.org/which/download/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "https://registry.npm.taobao.org/which/download/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"word-wrap@^1.2.3":
  "integrity" "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="
  "resolved" "https://registry.npm.taobao.org/word-wrap/download/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"worker-farm@^1.7.0":
  "integrity" "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag="
  "resolved" "https://registry.npm.taobao.org/worker-farm/download/worker-farm-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "errno" "~0.1.7"

"worker-loader@^2.0.0":
  "integrity" "sha512-tnvNp4K3KQOpfRnD20m8xltE3eWh89Ye+5oj7wXEEHKac1P4oZ6p9oTj8/8ExqoSBnk9nu5Pr4nKfQ1hn2APJw=="
  "resolved" "https://registry.npmjs.org/worker-loader/-/worker-loader-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "loader-utils" "^1.0.0"
    "schema-utils" "^0.4.0"

"wrap-ansi@^5.1.0":
  "integrity" "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk="
  "resolved" "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-5.1.0.tgz?cache=0&sync_timestamp=1618558850700&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "string-width" "^3.0.0"
    "strip-ansi" "^5.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha1-6Tk7oHEC5skaOyIUePAlfNKFblM="
  "resolved" "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-6.2.0.tgz?cache=0&sync_timestamp=1618558887146&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM="
  "resolved" "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-7.0.0.tgz?cache=0&sync_timestamp=1618558850700&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.nlark.com/wrappy/download/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"ws@^6.0.0", "ws@^6.2.1":
  "integrity" "sha1-RC/fCkftZPWbal2P8TD0dI7VJPs="
  "resolved" "https://registry.nlark.com/ws/download/ws-6.2.1.tgz?cache=0&sync_timestamp=1618847051468&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fws%2Fdownload%2Fws-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "async-limiter" "~1.0.0"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="
  "resolved" "https://registry.npm.taobao.org/xtend/download/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^4.0.0":
  "integrity" "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8="
  "resolved" "https://registry.nlark.com/y18n/download/y18n-4.0.3.tgz"
  "version" "4.0.3"

"y18n@^5.0.5":
  "integrity" "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU="
  "resolved" "https://registry.nlark.com/y18n/download/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="
  "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI="
  "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml@^1.10.0":
  "integrity" "sha1-IwHF/78StGfejaIzOkWeKeeSDks="
  "resolved" "https://registry.nlark.com/yaml/download/yaml-1.10.2.tgz"
  "version" "1.10.2"

"yargs-parser@^13.1.2":
  "integrity" "sha1-Ew8JcC667vJlDVTObj5XBvek+zg="
  "resolved" "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-13.1.2.tgz"
  "version" "13.1.2"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^20.2.2":
  "integrity" "sha1-Yd+FwRPt+1p6TjbriqYO9CPLyQo="
  "resolved" "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-20.2.7.tgz"
  "version" "20.2.7"

"yargs@^13.3.2":
  "integrity" "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0="
  "resolved" "https://registry.nlark.com/yargs/download/yargs-13.3.2.tgz?cache=0&sync_timestamp=1620086581476&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fyargs%2Fdownload%2Fyargs-13.3.2.tgz"
  "version" "13.3.2"
  dependencies:
    "cliui" "^5.0.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^3.0.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^13.1.2"

"yargs@^16.0.0":
  "integrity" "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y="
  "resolved" "https://registry.nlark.com/yargs/download/yargs-16.2.0.tgz?cache=0&sync_timestamp=1620086581476&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fyargs%2Fdownload%2Fyargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yorkie@^2.0.0":
  "integrity" "sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k="
  "resolved" "https://registry.npm.taobao.org/yorkie/download/yorkie-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "execa" "^0.8.0"
    "is-ci" "^1.0.10"
    "normalize-path" "^1.0.0"
    "strip-indent" "^2.0.0"
