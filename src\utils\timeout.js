
import { Toast } from 'vant'
import Vue from 'vue'
import storage from './storage'
import API from '../api/url'
import { getAPITimeOut } from '../api/api'
import EventBus from './bus'
import store from '../store'
import { callNative } from './index'

export const initTimeout = async () => {
  EventBus.$on('audi_api_timeout', timeoutHandler)
  const { data } = await getAPITimeOut()
  storage.set('audiApiTimeOut', data.data.configValue)
}

export const needTimeout = (url, method) => initFuncTimeout(url, method)
      || actFuncTimeout(url, method)

const initFuncTimeout = (url, method) => url.startsWith(API.getAPITimeOutTesting)
// url.startsWith(API.getCustomSeries)
//     || url.startsWith(API.getOrderList)
//     || url.startsWith(API.getDetailsByOrderId)
//     || (url.startsWith(API.getCarConfigDetail) && data && data.page === 'quotation') // 报价单页面
//     || (url.startsWith(API.getCarConfigDetail) && data && data.page === 'orderDetailConfirm') // 确认订单页
//     || (url.startsWith(API.getProductDetail) && data && data.page === 'orderConfirm')
//     || (url.startsWith(API.verifyFadada) && method == 'get')
//     || url.startsWith(ConfigAPI.recommendStyleFix)
//     || url.startsWith(ConfigAPI.byStyleUrl)
//     || url.startsWith(ConfigAPI.getStyleList)
//     || (url.startsWith(API.getNearestDealerList) && data && data.page === 'orderDealerList')
//     || (url.startsWith(API.getAgentCityList) && data && data.page === 'seek-store') // 查找渠道商


const actFuncTimeout = (url, method, data) => url.startsWith(API.getTryCarUrl)
    || url.startsWith(API.updateAmsDeliverType)
    || (url.startsWith(API.getNearestDealerList) && data && data.page === 'deliveryPattern')
    || url.startsWith(API.getCheckChangeConfiguration)
    || url.startsWith(API.setConfirmChangeConfiguration)
    // || url.startsWith(API.getContractInfo) 合同的时间基本都是超过3S 等待确认是不是要放出来
    || url.startsWith(API.addCarShoppingCart)
    || url.startsWith(API.carPlaceOrder)
    || url.startsWith(API.carSubmitToken)
    || (url.startsWith(API.getAgentCityList) && data && data.page === 'seek-store-province-change')


const deliveryDealerPattern = new RegExp(`${API.getNearestDealerList}\\?(.)*&page=deliveryPattern`)

const timeoutHandler = (eventData) => {
  // 1 表示是初始化类接口 2 表示是操作类型接口 3 表示是加载更多的接口

  let urlType = 0
  const url = eventData.url
  const method = eventData.method.toLowerCase()
  const data = eventData.data

  console.log('请求超时参数, ', url, data)
  if (
    initFuncTimeout(url, method)
  ) {
    urlType = 1
  } else if (
    actFuncTimeout(url, method, data)
  ) {
    urlType = 2
  }

  // 页面特殊处理， 代理商列表选择因为loading
  if (url.startsWith(API.getNearestDealerList) && data && data.page === 'orderDealerList') {
    EventBus.$emit('audi_api_order_dealer_list_timeout')
  }

  if (url.startsWith(API.carPlaceOrder) || url.startsWith(API.carSubmitToken)) {
    EventBus.$emit('audi_api_order_detail_pay_timeout')
  }

  if (urlType == 1) {
    store.commit('setInitTimeout', true)
    store.commit('hideLoading')
    Toast.clear()
  } else if (urlType == 2) {
    store.commit('hideLoading')
    callNative('toggleLoading', { show: '0' })
    Toast({
      className: 'toast-dark-mini toast-pos-middle',
      message: '请检查您的网络是否连接'
    })
  }
}


export const networkToast = async (state = '4ring') => {
  let className =  state === '4ring' ? 'audi-loading-style' : 'audi-loading-style audiPurple-loading-style';

  await Toast.loading({
    message: '正在加载···',
    duration: 100000,
    className,
  })

  Vue.nextTick(() => {
    const loading = document.getElementsByClassName('audi-loading-style')[0]
    if (loading) {
      const svg = loading.getElementsByTagName('svg')[0]
      const circles = svg.getElementsByTagName('circle')
      if (circles.length == 1) {
        const circle = circles[0].cloneNode()
        circle.style.animation = 'none'
        circle.style.stroke = '#D9D9D9'
        svg.insertBefore(circle, circles[0])
      }
    }
  })
}
