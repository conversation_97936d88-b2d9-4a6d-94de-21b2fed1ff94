<template>
  <div
    class="button"
    @click="$emit('click')"
    :style="{
      backgroundColor: color,
      color: color === 'white' ? 'black' : '#E5E5E5',
      fontSize: fontSize,
      width: width,
      lineHeight: height,
      borderColor: borderColor,
    }"
  >
    <slot name="icon" />
    {{ text }}
  </div>
</template>

<script>
export default {
  props: {
    // button 文案
    text: {
      type: String,
      required: true
    },

    // 背景颜色
    color: {
      type: String,
      required: false,
      default: 'white'
    },
    // 边框颜色
    borderColor: {
      type: String,
      required: false,
      default: 'black'
    },
    // 字体大小
    fontSize: {
      type: String,
      required: false,
      default: '14px'
    },

    height: {
      type: String,
      required: false,
      default: '40px'
    },

    width: {
      type: String,
      required: false,
      default: '100%'
    }
  }
}
</script>

<style scoped lang="less">
.button {
  border: 1px solid black;
  text-align: center;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
