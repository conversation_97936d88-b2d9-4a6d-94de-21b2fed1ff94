<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-03 09:38:53
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-27 15:13:20
 * @FilePath     : \src\view\order\confirm\confirm.vue
 * @Descripttion :
-->
<script src="./confirm"></script>
<template>
  <div
    :class="
      ['confirm-order-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
    />
    
    <div
      class="main-wrapper"
      id="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <div class="order-forms-box">
        <div
          class="order-tips align-left"
        >
          *温馨提示：请您核对购车人信息和车主信息，车主姓名、联系电话和证件号码将用于签署合同
        </div>
        <order-option
          :goods="goods"
          :option-list="optionList"
          :collapse="false"
        />
        <order-buyer
          :buyer-info="buyerInfo"
        />
        <div
          class="affirm-order-info-box"
          data-flex="main:justify"
        >
          <template v-if="operatingBtn">
            <div class="lan-button-box white-button line-two-cols">
              <van-button
                class="lan-button"
                :disabled="(!orderStatus ? false : banEdit || !ORDER_STATUS_DISTRICT.XIAO_DING.includes(orderStatus)) || !!payLoadingText"
                @click="goToEditOrderData"
              >
                返回修改
              </van-button>
            </div>
            <div class="lan-button-box black-button line-two-cols">
              <van-button
                @click="handlePayOrderAction(1)"
                :loading-text="payLoadingText"
                :loading="!!payLoadingText"
                class="lan-button"
              >
                我已确认
              </van-button>
            </div>
          </template>
        </div>
      </div>
      <van-popup
          v-model="popPayShow"
          class="popup-custom"
          :close-on-click-overlay="false"
        >
          <div class="popup-custom-box">
            <div class="popup-custom-main">
              <div class="text align-center">
                当前订单是否已经完成微信支付
              </div>
            </div>
            <div
              class="popup-custom-btn"
              data-flex="main:justify"
            >
              <div class="lan-button-box black-button ghost-button line-two-cols">
                <van-button
                  class="lan-button"
                  @click="handleToOrderDetail"
                >
                  查看订单
                </van-button>
              </div>
              <div
                class="lan-button-box black-button line-two-cols"
              >
                <van-button
                  @click="handlePayOrderAction(0)"
                  class="lan-button lan-darkly"
                >
                  确认完成
                </van-button>
              </div>
            </div>
          </div>
        </van-popup>
      <network @reload="networkReload()"/>
    </div>
  </div>
</template>

<style lang="less" scoped>
.page-wrapper,.main-wrapper {
  box-sizing: border-box;
}
.tips-in{
  height: auto;
  line-height: 20px;
  padding: 12px 16px;
}
.affirm-order-info-box {
  padding: 40px 16px;
  background-color: #f9f9f9;
}
.confirm-order-wrapper {
  /deep/ .order-price-box {
    border-top-width: 0
  }
  /deep/ .cell-list {
    line-height: 24px;
  }
  /deep/ .order-forms-box {
    margin-bottom: 0
  }
}
.order-tips{
  height: 54px;
  line-height: 20px;
  display: flex;
  align-items: center;
}
.lan-button-box .lan-button{
  height: 48px;
  line-height: 48px;
}
</style>
<style lang="less">
.loading {
  background-color: rgba(0, 0, 0, .02);
  .van-loading {
    display: none;
  }
}
</style>
