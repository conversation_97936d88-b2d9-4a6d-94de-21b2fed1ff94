<!--
 * @Author: ft-weih <EMAIL>
 * @Date: 2022-12-08 10:44:46
 * @LastEditors: ft-weih <EMAIL>
 * @LastEditTime: 2023-02-21 13:34:49
 * @FilePath: \audi-order-h5\src\view\enterprisingElite\sampleIdCard.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="container">
    <navigation
      title=""
      :back-type="backType"
    />
    <div
      class="connter"
      v-if="(type==1)"
    >
      <h5>身份证（正反面）</h5>
      <p>请于光线充足的环境下，纯色背景下，拍摄清晰</p>
      <div class="card-img">
        <img src="../../assets/img/card1.png">
        <img src="../../assets/img/card2.png">
      </div>
    </div>
    <div
      class="connter"
      v-if="(type==3)"
    >
      <h5>社保参保证明</h5>
      <p>请于光线充足的环境下，纯色背景下，拍摄清晰</p>
      <div class="card-img-s">
        <img src="../../assets/img/social-security.png">
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import navigation from '../../components/navigation'

export default {
  components: { navigation },
  data() {
    return {
      backType: 'app',
      type: 1,
      typeName: {
        1: '身份证（正反面）',
        2: '护照',
        3: '准购单',
        4: '学历证书'
      }
    }
  },
  mounted() {
    // 判断跳转来源
    const { backType, type } = this.$route.query
    this.type = type || 1
    if (backType) {
      this.backType = backType
    } else {
      this.backType = 'app'
    }
  },
  methods: {
    submit() {
      this.$router.push({
        name: 'supplier-basic-information'
      })
    }
  }
}
</script>

<style lang="less" scoped>
.connter{
    padding: 0 16px;
    h5{
       margin:24px 0 8px 0;
       font-size: 16px;
       font-family: 'Audi-WideBold';
       color:#333333;
    }
    p{
        margin:0;
        font-size: 14px;
        margin-bottom: 16px;
        font-family: 'Audi-Normal';
        color:#666666;
    }
    .card-img{
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
       img{
        display: block;
        width:268px;
        height: 170px;
        margin-bottom: 24px;

    }}
    .card-img-s{
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
       >img{
        display: block;
        // width:268px;
        // height: 194px;
        margin-bottom: 7px;

    }
    }


}
</style>
