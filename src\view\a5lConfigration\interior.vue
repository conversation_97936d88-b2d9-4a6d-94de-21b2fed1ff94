<template>
  <div class="interior-wrapper">
    <div class="collapse-wrapper">

      <div class="mainimg-wrapper">
        <ShowModelButton type="in" />
        <img :src="currentMainImgUrl | imgFix(640, true)" v-if="currentMainImgUrl" alt="">
        <DescriptionText bottom="15px" />
      </div>

      <div class="wrapper-scroll">
        <!-- 面料颜色列表 -->
        <div class="content-wrapper">
          <div class="desc-wrapper">
            <div class="c-font14 c-bold c-lh22">
              {{ currentII.packet ? currentII.packet.labelValueNameZh : currentII.externalFeatureNameZh }}
            </div>
            <div class="desc c-lh20">
              {{ currentII.packet ? currentII.featurePrice : currentII.featurePrice | finalFormatPriceDesc }}
            </div>
          </div>
          <div class="sib-wrapper">
            <div class="wrapper">
              <div v-for="item in currentVersion.carII" :key="item.featureCode" class="item" :class="{
                selected: item.featureCode === currentII.featureCode,
                disabled: item.disabled
              }" @click="toSelectII(item)">
                <img :src="item.packet && item.packet.materialList ? item.packet.materialList[0].materialUrl : item.materialList?.[0]?.materialUrl | imgFix(140, true)" alt="">
              </div>
            </div>
          </div>

          <!-- 饰板列表 -->
          <div class="desc-wrapper c-margin-top">
            <div class="c-font14 c-bold c-lh22">
              {{ currentEih.externalFeatureNameZh }}
            </div>
            <div class="desc c-lh20">
              {{ currentEih.featurePrice | finalFormatPriceDesc }}
            </div>
          </div>
          <div class="eih-wrapper">
            <div class="wrapper">
              <div v-for="item in currentVersion.carEih" :key="item.featureCode" class="item" :class="{
                selected: item.featureCode === currentEih.featureCode,
                disabled: item.disabled
              }" @click="toSelectEih(item)">
                <!-- <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(250, true)" alt=""> -->
                <img :src="item.materialList?.[0]?.materialUrl | imgFix(140, true)" alt="">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <CommonFooter @click="nextPage" />
  </div>
</template>

<script>
import Vue from "vue";
import { mapGetters, mapState } from "vuex";
import { Toast } from "vant";
import CommonFooter from "./components/commonFooter.vue";
import url from "@/config/url";
import { postOmdModelConfig } from "@/configratorApi";
import { getUrlParamObj, isEmptyObj } from "@/utils";
import { getFilterRelateList } from "@/view/newConfigration/util/helper";
import ShowModelButton from "./components/showModelButton.vue";
import DescriptionText from "./components/descriptionText.vue";

Vue.use(Toast);
// 设置Toast 持续时间 debugger
// Toast.setDefaultOptions({ duration: 1000000 })

const { env } = getUrlParamObj();
const isMinip = env === "minip";

const OSS_URL = url.BaseConfigrationOssHost;

export default {
  name: "ConfigrationInterior",
  components: { CommonFooter, ShowModelButton, DescriptionText },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      isMinip: isMinip,
      pageStartTime: 0,
    };
  },
  computed: {
    ...mapGetters([
      "currentSeriesName",
      "currentCarType"
    ]),
    ...mapState({
      userCoupon: (state) => state.configration.userCoupon,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentExterior: (state) => state.configration.currentExterior,
      currentHub: (state) => state.configration.currentHub,
      currentSib: (state) => state.configration.currentSib,
      currentEih: (state) => state.configration.currentEih,
      currentII: (state) => state.configration.currentII,
      configrationActiveTab: (state) => state.configration.configrationActiveTab,
      currentVersion: (state) => state.configration.currentVersion,
      carIdx: (state) => state.configration.carIdx,
      referConfigrationActiveTab: (state) => state.configration.referConfigrationActiveTab,
    }),

    currentMainImgUrl() {
      const seriesName = this.currentSeriesName;
      if (!seriesName) return null;

      if (isEmptyObj(this.currentModelLineData.cc)) return null;

      if (!isEmptyObj(this.currentII.packet) && Array.isArray(this.currentII.packet.labelChildren) && this.currentII.packet.labelChildren.length > 0 && !isEmptyObj(this.currentEih)) {
        const features = {};
        for (const item of this.currentII.packet.labelChildren) {
          if (item.familyCode === "SIB" || item.familyCode === "II") {
            features[item.familyCode] = item.featureCode;
          }
        }
        let arr = [
          features.SIB,
          features.II,
          this.currentEih.featureCode
        ];
        let imgName = arr.join("-");
        return `${OSS_URL}/ccpro-backend/${seriesName}/tmp/interieur/${this.currentModelLineData.cc.modelLineCode}/${imgName}.jpg`;
      }
      return null;
    },
    isCouponValid() {
      if (!this.userCoupon) {
        return false;
      }
      // 获取两个字段的值
      const { activityCode, mpEquityNo } = this.userCoupon;

      // 检查两个字段是否都存在且有实际值（非空字符串、非null、非undefined）
      return Boolean(
        activityCode !== undefined &&
        activityCode !== null &&
        activityCode !== "" &&
        mpEquityNo !== undefined &&
        mpEquityNo !== null &&
        mpEquityNo !== ""
      );
    }
  },
  watch: {
    currentSib(sib) {
      console.info("currentSib watch");
      // 更新饰板
    },

    currentExterior(outColor) {
      console.info("currentSib watch");

      // this.resetSibStatus()
    },

    configrationActiveTab(value) {
      if (value === "interior") {
        this.pageStartTime = Date.now();
      }
    }

  },
  mounted() {
    console.log("%c A5L interior mounted", "font-size:16px;color:green;");
    console.log("%c A5L currentII: ", "font-size:16px;color:green;", this.currentII);
    console.log("%c A5L currentEih: ", "font-size:16px;color:green;", this.currentEih);
    console.log("%c A5L carEih: ", "font-size:16px;color:green;", this.currentVersion.carEih);
    this.pageStartTime = Date.now();
  },

  methods: {
    async resizeCar(item, key) {
      const paramDto = await this.$store.dispatch("getOmdModelConfigParams", { item, key });
      const carConfig = await postOmdModelConfig(paramDto);
      if (carConfig.data.data.code !== "20000") return this.$toast.fail(carConfig.data.data.message);
      this.$store.commit("updateCurrentVersion", carConfig.data.data.result[0]);
      await this.$store.dispatch("setRadEquipmentGroup");
      await this.$store.dispatch("setIIEquipmentGroup");
    },

    /**
     * 选择面料
     * 新的点击置灰逻辑:
     * UI要变为选中状态,且不再置灰
     * 此时点击下一步或者 tab 无法切换提示.
     */
    async toSelectII(item) {
      if (item.featureCode === this.currentII.featureCode) {
        return;
      }
      this.$store.commit("showLoadingNospinner");
      await this.resizeCar(item, "II");
      this.$store.dispatch("clearOptionData"); // 清理选装的数据
      this.$store.dispatch("resetPageAllOptionState");

      this.$store.commit("updateCurrentII", item);

      this.$store.dispatch("computeA5LTotalPriceAndEquityPrice", { isCouponValid: this.isCouponValid });
      this.$store.commit("hideLoadingNospinner");
    },

    // 选择饰板
    async toSelectEih(item) {
      console.log("选择饰板", item);
      if (item.featureCode === this.currentEih.featureCode) {
        return;
      }
      this.$store.commit("showLoadingNospinner");
      await this.resizeCar(item, "EIH");
      // 饰板关联选装包则提示
      const depends = getFilterRelateList(item.optionRelates, "depend");
      const eihDepends = depends.filter((i) => i.optionRelateCategory === "PACKET");
      if (eihDepends.length > 0) {
        Toast("当前饰板包含组合的选装套装");
        this.$store.commit("updateFooterDesc", {
          desc: "当前饰板包含组合的选装套装，点击下一步查看"
        });
      }

      this.$store.dispatch("clearOptionData"); // 清理选装的数据
      this.$store.dispatch("resetPageAllOptionState");

      this.$store.commit("updateCurrentEih", item);

      this.$store.dispatch("computeA5LTotalPriceAndEquityPrice", { isCouponValid: this.isCouponValid });
      this.$store.commit("hideLoadingNospinner");
    },

    // 下一步
    nextPage() {
      if (this.currentEih.disabled) {
        return Toast("您选择的装备需您先更换内饰颜色");
      }
      if (this.currentSib.disabled) {
        return Toast("您选择的装备需您先更换外饰颜色");
      }
      this.clickInteriorSensors();// 埋点
      this.$store.commit("updateConfigrationActiveTab", "option");
    },

    // 埋点
    clickInteriorSensors() {
      const tabMap = {
        exterior: "外观",
        interior: "内饰",
        option: "选装",
        equity: "权益"
      };
      const { engine, customSeriesName } = this.currentModelLineData;
      const param = {
        source_module: "H5",
        refer_tab_name: tabMap[this.referConfigrationActiveTab],
        car_series: "A5L",
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: "定制交付", // 快速交付|定制交付
        select_fabric: this.currentSib.description,
        select_panel: this.currentEih.optionName,
        button_name: "下一步",
        $event_duration: Math.floor((Date.now() - this.pageStartTime) / 1000)
      };
      console.log("CC_CarConfiguration_Trim_BtnClick:", param);
      this.$sensors.track("CC_CarConfiguration_Trim_BtnClick", param);
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.disabled {
  opacity: .5;
}

.collapse-wrapper {
  padding-bottom: @FooterHeight;
}

//滚动区域
@InteriorHeight: 240px;
.wrapper-scroll {
  overflow-y: auto;
  height: calc(104vh - @HeaderHeight - @TabHeight - @InteriorHeight - @FooterHeight);
}

.mainimg-wrapper {
  position: relative;
  height: 240px;
  margin-top: 8px;

  img {
    height: 100%;
  }

  .interior-3dIcon {
    position: absolute;
    width: 52px;
    height: 52px;
    top: 18px;
    left: 24px;
    z-index: 2;
  }
}

.content-wrapper {
  padding: 0 15px;
  margin-top: 16px;
}

.desc-wrapper {
  text-align: center;
  color: #333333;

  > .desc {
    font-size: 12px;
    margin-top: 4px;
  }
}

.sib-wrapper,
.eih-wrapper {
  overflow: auto;
  padding-bottom: 15px;
  margin-bottom: 33px;

  .wrapper {
    width: max-content;
    margin-top: 12px;

    > .item {
      margin-left: 4px;
      display: inline-block;
      width: 70px;
      height: 70px;
      transform: scale(0.7);

      img {
        height: 100%;
        object-fit: cover;
      }

      &.selected {
        transform: scale(1);
        border: 1px solid #000;
        padding: 1px;
        opacity: 1;
      }
    }
  }
}
</style>
