<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-05 12:21:44
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2025-02-11 16:44:48
 * @FilePath     : \src\view\order\pay-status\succeed\common.vue
 * @Descripttion :
-->
<template>
  <div
    class="main-wrapper"
    :style="{
      'padding-top': `${
        !headerFixed.enable || headerFixed.effect !== 'fixed'
          ? 0
          : headerOutHeight
      }px`
    }"
  >
    <div
      class="succeed-common-wrapper"
      :style="`height:calc(100vh - ${headerOutHeight}px)`"
    >
      <!-- app -->
      <div class="series-img-box" :style="env == 'minip'?'top:16%':''">
        <img class="success_logo" src="@/assets/pay/success_icon.png" alt="">
        <div
          class="series-box"
       
          data-flex="main:center cross:center"
        >
          <img
            :class="['series', !(seriesImgString === 'A7L') || 'a7l']"
            :src="seriesImgURL"
          />
        </div>
        <div class="tips">
          <div><img class="tips_icon" src="@/assets/pay/success_title.png" alt=""></div>
          <div><img class="tips_icons" src="@/assets/pay/success_tips.png" alt=""></div>
        </div>
        <div class="series-img-box-contant">
          感谢您对于上汽奥迪的信任与支持，
        </div>
        
        <div class="series-img-box-contant">
          期待与您一起，圈住美好，圈住爱。
        </div>
      </div>
      <div
        class="affirm-order-info-box"
        data-flex="main:justify"
        
      >
        <div
          :class="['lan-button-box  ']"
        >
        <div class="series-img-box-contant">
          如有任何疑问请随时联系您的专属奥迪管家。
        </div>
        <van-button class="lan-button" @click="handleGoToOrderDetailsPage"
            >去签署合同</van-button
          >
        </div>
      </div>
    </div>
    <loading ref="myLoading"></loading>
  </div>
</template>

<script>
import Vue from 'vue'
import { Button } from 'vant'
import { LIMIT_NUMBERS_A7L, RES_SUCCEED_CODE } from '@/config/conf.data'
import { limitedNumbers } from '@/api/payment-success'
import { getCarConfig, getCustomerHelper,customerHelperSendNotice,getCarOrderInfoH5 } from '@/api/api'
import { getSubmitToken } from '@/api/detail'
import { callNative } from '@/utils'
import api from '@/config/url'
import { A7MR } from '@/view/newConfigration/car/a7mr'

import loading from '@/components/loading-center'

const AUTOMOBILE = {
  49: 'A7L',
  G4: 'Q5E',
  G6: 'Q6'
}

Vue.use(Button)
export default {
  name: 'SucceedCommon',
  props: {
    headerFixed: {
      type: Object,
      default: () => {}
    },
    headerOutHeight: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      seriesImgURL: '',
      seriesImgString: 'A7L',
      limitedNumberA7L: '',
      isA7MrCar: false,
      env: '', // 'minip' 'nativeApp'
      state: '',
    }
  },
  created() {
    const {
      $route: {
        query: { orderId, seriesCode, modelLineCode, ccid, env, mobile }
      }
    } = this
    console.log('this.$route.query', this.$route.query, mobile)
    this.handleProcessData(orderId, seriesCode, modelLineCode, ccid)
    // this.handleProcessData('219856910790766','G4','G4IBC3004','1789828981650829314')

    this.env = env === 'minip' ? 'minip' : 'nativeApp'
  },
  methods: {
    async handleProcessData(orderId, seriesCode, modelLineCode, ccid) {
      if (ccid) {
        const {
          data: { data, code }
        } = await getCarConfig({ ccid })
        if (code && RES_SUCCEED_CODE.includes(code)) {
          const {
            configDetail: {
              carModel: {
                modelCode,
                modelNameCn,
                modelLineCode,
                modelPrice,
                headImageUrl
              }
            }
          } = data || {}

          if (headImageUrl) {
            this.seriesImgURL = api.BaseOssHost + headImageUrl
          }
        }
      }

      if (seriesCode && modelLineCode) {
        this.seriesImgString = AUTOMOBILE[seriesCode]
        if (modelLineCode === LIMIT_NUMBERS_A7L?.modelLineCode) {
          const { data } = await limitedNumbers({ orderId })
          if (data?.code && RES_SUCCEED_CODE.includes(data.code)) {
            this.limitedNumberA7L = data?.data?.number || ''
          }
        }

        this.isA7MrCar = A7MR.map((i) => i.code).includes(modelLineCode)
      }
      this.$nextTick(() => {
        this.$refs.myLoading.show(false)
      })
    },
    async handleGoToOrderDetailsPage() {
      const {
        $route: {
          query: { orderId,env }
        }
      } = this
      const {data} = await getCarOrderInfoH5()
      callNative('audiOpen', {
          path: `${data.data.configValue}order/new-money-detail?orderId=${orderId}&fromPage=successStatus`
       })
      sessionStorage.removeItem('orderId')
    }
  },
  components:{
    loading
  }
}
</script>

<style lang="less">
.page-wrapper {
  &.pay-status-succeed-common {
    background-image: linear-gradient(0deg, #36384c 0%, #404259 70%);
    .header-wrapper {
      .header-box {
        color: #fff;
      }
    }
  }
}
</style>
<style lang="less" scoped>
.succeed-common-wrapper {
  width: 100%;
  height: 100%;
}
// app
.main-wrapper {
  min-height: 100vh;
  box-sizing: border-box; /*494c64*/
  background:#FFFFFF ;
  .affirm-order-info-box {
    z-index: 999;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 169px 16px 44px;
    .series-img-box-contant{
      font-size: 12px;
      color: #B3B3B3;
      line-height: 20px;
      text-align: center;
      margin-bottom: 12px;
    }
    .lan-button {
      background: #000000;
      color: #fff;
      border-color: #000000;
      height: 48px;
      line-height: 48px;
    }
  }
  .series-img-box {
    z-index: 98;
    position: absolute;
    left: 50%;
    top: 24%;
    width: 100%;
    transform: translateX(-50%);
    overflow: hidden;
    text-align: center;
    .series-box {
      position: relative;
      z-index: 100;
      width: 100%;
      height: 211px;
      margin: auto;
      img{
        width: auto;
        height: 100%;
      }
      &.mrWidth {
        
        width: 210px;
      }
    }
    .a7l {
      position: relative;
      top: -15px;
    }
    img {
      // width: 180%;
    }
    .series-img-box-contant {
      padding: 0 25px;
      box-sizing: border-box;
      font-size: 12px;
      line-height: 20px;
      color: #666666;
      text-align: center;
    }
    .mb16 {
      margin-bottom: 16px;
    }
    .series-img-box-step {
      display: flex;
      align-items: center;
      padding: 0 25px;
      box-sizing: border-box;
      height: 18px;
      span {
        display: inline-block;
        font-size: 11px;
      }
      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        vertical-align: middle;
      }
      i {
        font-style: normal;
        flex: 1;
        border-bottom: 1px dashed #d9aa77;
        height: 1px;
        margin: 0 5px;
      }
    }
  }
}

.tips{
  text-align: center;
  margin-bottom: 20px;
  margin-top: 6px;
  .tips_icons{
    width: 227px;
  }
  .tips_icon{
    width: 85px;
    margin-bottom: 4px;
  }
}
.success_logo{
  height: 32px;
  width: auto;
  
  display: block;
  margin: 0 auto;
  margin-bottom: 18px;
}
@media screen and (min-height: 600px) and (max-height: 700px) {
  .main-wrapper {
    .series-img-box {
      top: 8%;
      .logo-title {
        margin: 24% auto 8px;
      }
      img {
        width: 160%;
      }
    }
    .affirm-order-info-box {
      padding-top: 136px;
    }
  }
}

@media screen and (max-height: 599px) {
  .main-wrapper {
    .series-img-box {
      top: 8%;
      .logo-title {
        margin: 15% auto -15px;
        width: 144px;
        height: 44px;
      }
      &::after {
        width: 0;
        height: 0;
      }
      img {
        width: 120%;
      }
    }
    .affirm-order-info-box {
      padding-top: 96px;
    }
  }
}
</style>
