<template id="serviceAppointmentOne">
  <div class="serviceAppointmentOne">
    <!-- <p class="hint">
      上汽奥迪服务预约，以优质服务、原厂备件、专业技术为您提供
      无忧车辆养护服务。即刻开启预约，静候您的莅临。
    </p> -->
    <div
      v-if="isShowBindCard"
      class="top-banner-tips no-both-sides"
      data-flex="main:justify cross:center"
    >
      <div class="left">您还未绑定车辆，绑定后自动填写相关信息</div>
      <div class="right" data-flex="cross:center">
        <span class="btn" @click="handleGotoBindCar">去绑定</span
        ><van-icon class="arrow icon" name="arrow" />
      </div>
    </div>
    <img
      :src="
        'https://audi-oss.saic-audi.mobi/audicc/app/order/afterservice/service1.png'
          | audiwebp
      "
      class="item-img"
    />
    <div class="order-forms-box bottom-one">
      <van-form ref="form" @failed="failed">
        <van-cell-group>
          <div class="cell-title van-hairline--bottom">
            <h2 class="h2">服务商信息</h2>
          </div>
          <van-field
            readonly
            is-link
            label="服务商"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="dealerName"
            ref="dealerName"
            type="text"
            @click="onSelectServiceProvider"
            placeholder="请选择服务商"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择服务商'
              }
            ]"
          />
          <van-field
            readonly
            is-link
            label="预约时间"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="appointmentTime"
            ref="appointmentTime"
            type="text"
            @click="onAppointmentTime"
            placeholder="请选择"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择预约时间'
              }
            ]"
          />
        </van-cell-group>
        <van-cell-group>
          <div class="cell-title van-hairline--bottom">
            <h2 class="h2">个人信息</h2>
          </div>

          <div
            class="box-field"
            @click.stop="animation('ownerName')"
            id="ownerName"
          >
            <van-field
              label="姓名"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="submitParam.ownerName"
              ref="ownerName"
              type="text"
              @blur="handlerBlur('ownerName')"
              @focus="handlerFocus('ownerName')"
              placeholder="请输入您的姓名"
              :rules="[
                {
                  trigger: 'onBlur',
                  required: true,
                  message: '请填写姓名！'
                }
              ]"
            />
          </div>

          <!-- <div class="box-field" @click.stop="animation('ownerTel')" id="ownerTel">
!-->

          <van-field
            label="联系方式"
            id="ownerTel"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="submitParam.ownerTel"
            ref="ownerTel"
            maxlength="11"
            type="number"
            on-key-up="value=value.replace(/[\W]/g,'')"
            @blur="handlerBlur('ownerTel')"
            @focus="handlerFocus('ownerTel')"
            @click="animation('ownerTel')"
            placeholder="请输入您的联系电话"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写联系方式！'
              },
              {
                pattern: /\w{11}/,
                message: '请输入正确的联系方式'
              }
            ]"
          />
          <!-- <div id="phone" v-if="showPopover">
                <div class="item" v-for=" phone of actions" :key="phone.text" @click.stop="onSelect(phone)">
                  {{ phone.text }}
                </div>
              </div> -->
          <div id="ownerTelHis">
            <van-popover
              get-container="#ownerTelHis"
              v-model="showPopover"
              :close-on-click-outside="true"
              trigger="click"
              :actions="actions"
              placement="bottom-start"
              @select="onSelect"
            />
          </div>
          <!-- </div>  -->
        </van-cell-group>
      </van-form>
    </div>

    <van-popup
      v-model:show="selectvehicle"
      position="bottom"
      safe-area-inset-bottom
      :style="{ 'max-height': '80vh', 'min-height': '50vh' }"
    >
      <div class="pop-coupons-box">
        <div
          class="pop-coupons-hd van-hairline--bottom"
          data-flex="main:justify cross:center"
        >
          <p class="btn close">
            <van-icon
              class="icon-close"
              name="cross"
              size="20"
              @click="handleActionPopCoupons"
            />
          </p>
          <h3 class="h3">选择车辆</h3>
          <p class="btn finish"></p>
        </div>
        <div class="pop-coupons-main">
          <template v-if="mineCoupons && mineCoupons.length">
            <div class="coupons">
              <van-radio-group v-model="mineCouponsChecked">
                <van-cell-group inset>
                  <van-cell
                    :class="[
                      'list',
                      list.vin === mineCouponsChecked ? 'radio-checked' : ''
                    ]"
                    @click="chooseVin(list)"
                    v-for="(list, index) in mineCoupons"
                    :key="list.vin"
                  >
                    <template #title>
                      <div class="coupon-box">
                        <h4 class="h4 text-one-hid">{{ list.modelNameCn }}</h4>
                        <p><span>VIN：</span>{{ list.vin }}</p>
                      </div>
                    </template>
                    <template #right-icon>
                      <van-radio :name="list.vin" checked-color="#000" />
                    </template>
                  </van-cell>
                </van-cell-group>
              </van-radio-group>
            </div>
          </template>
        </div>
      </div>
    </van-popup>

    <van-loading
      class="lan-loading-custom lan-custom-center lan-loading-darkly enable-masking-out lan-loading-decephaly"
      size="24px"
      vertical
      v-if="loading"
    >
      正在加载···
    </van-loading>

    <div class="bottom_style">
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onBtnNext"
          :text="'下一步'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Toast,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover
} from 'vant'
import { callNative } from '@/utils'
import {
  getAfterSaleDetail,
  postBindCardInfo,
  getAfterSalesByVin,
  getBindCarInfoList
} from '@/api/api'
import storage from '../../utils/storage'
import AudiButton from '@/components/audi-button'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      selectvehicle: false,
      showPopover: false,
      actions: [], // { text: '15000876160' }

      labelWidth: 84,
      labelAlign: 'left',
      animations: {
        ownerName: false,
        ownerTel: false
      },

      submitParam: {
        ownerName: '', // 姓名
        ownerTel: '' // 联系人电话
        // vin: '',
        // seriesCode: '',
        // seriesName: '',
        // modelLineName: '',
        // modelCode: ''
      },
      appoId: '', // 预约ID 修改订单需要
      isInit: '0', // 是否是初次进来
      dealerCode: '',
      dealerName: '',
      serviceType: 1, // 卡券直接进来的
      inputRemark: '', // 卡券直接进来的

      bindDealerCode: '', // Vin码绑定的代理商
      bindVin: '', // Vin码绑定的代理商
      isShowBindCard: false,
      mineCoupons: [],
      mineCouponsChecked: 0,
      mineCouponsCheckedObject: {},
      loading: false
    }
  },
  computed: {
    ...mapState({
      // dealerCode: (state) => state.dealerModel.code758 || '', // 服务商编号
      // dealerName: (state) => state.dealerModel.dealerName, // 服务商名称

      appointmentTime: (state) => state.selectServiceTime.appointmentTime || '' // 预约时间
    })
  },
  watch: {},

  mounted() {
    const {
      appoId,
      serviceType,
      inputRemark,
      dealerCode,
      serviceCode,
      dealerName
    } = this.$route.query
    const param = {
      dealerCode,
      serviceCode,
      dealerName,
      ...JSON.parse(storage.get('dealerModel'))
    }
    if(param.dealerCode){
      storage.set('dealerModel', JSON.stringify(param))
    }
   
    this.serviceType = serviceType
    this.inputRemark = inputRemark
    if (this.serviceType) {
      console.log('serviceType', this.serviceType)
      let serviceName = ''
      if (serviceType === '1') {
        serviceName = '保养'
      } else if (serviceType === '2') {
        serviceName = '维修'
      } else if (serviceType === '3') {
        serviceName = '检查'
      } else {
        serviceName = '其他'
      }
      this.$store.commit('saveSelectServiceType', {
        serviceType: this.serviceType,
        serviceName: serviceName,
        inputServiceRemark: this.inputRemark
      })
    }
    if (this.$store.state?.isInitView) {
      this.isInit = this.$store.state.isInitView
    }
    this.appoId = appoId
    if (appoId !== undefined && this.dealerCode === '' && this.isInit === '0') {
      this.getAfterSaleDetail(appoId)
    }
    if (this.$store.state?.userName) {
      this.submitParam.ownerName = this.$store.state.userName
      this.submitParam.ownerTel = this.$store.state.userDriveTel
    } else {
      this.submitParam.ownerName = storage.get('ownerName') || ''
      this.submitParam.ownerTel = storage.get('ownerTel') || ''
    }
    const dealerModel = storage.get('dealerModel') || '{}'

    this.dealerCode = JSON.parse(dealerModel).serviceCode || ''
    this.dealerName = JSON.parse(dealerModel).dealerName

    this.changeEdit()

    if (appoId === undefined && this.isInit === '0') {
      this.getBindCardInfo()
    }
    const mobile = storage.get('serviceUserMobile') || ''
    if (mobile) {
      const mobileList = mobile.split(',')
      for (let i = 0; i < mobileList.length; i++) {
        if (mobileList[i] && i < 3) {
          this.actions.push({ text: mobileList[i] })
        }
      }
    }
  },
  methods: {
    chooseVin(list) {
      this.mineCouponsChecked = list.vin
      this.mineCouponsCheckedObject = list
      this.handleFinishPopCoupons()
    },
    handleActionPopCoupons() {
      this.selectvehicle = false
    },
    handleFinishPopCoupons() {
      this.selectvehicle = false
      console.log('mineCouponsCheckedObject', this.mineCouponsCheckedObject)
      this.getAfterSalesByVin(this.mineCouponsCheckedObject.vin)
      storage.set(
        'ownerVinInfo',
        JSON.stringify({
          vin: this.mineCouponsCheckedObject.vin,
          seriesCode: this.mineCouponsCheckedObject.seriesCode,
          modelCode: this.mineCouponsCheckedObject.modelCode,
          modelLineName: this.mineCouponsCheckedObject.modelNameCn,
          seriesName: this.mineCouponsCheckedObject.seriesName
        })
      )
    },
    // 查询是否有绑定车辆
    async getBindCardInfo() {
      this.loading = true
      const { data } = await postBindCardInfo({})
      this.isShowBindCard = !data.data?.length
      if (data.data.length > 0) {
        // 如果有用户信息默认填写上
        if (data.data[0].memberVO) {
          this.submitParam.ownerName = data.data[0].memberVO.name
          this.submitParam.ownerTel = data.data[0].memberVO.mobile
          this.changeEdit()
        }
        let vins = ''
        data.data.forEach((item, index) => {
          vins += item.vin + ','
        })
        const bindCarInfoList = await getBindCarInfoList({
          vins: vins.substring(0, vins.length - 1)
        })
        console.log('bindCarInfoList', bindCarInfoList)
        if (bindCarInfoList && bindCarInfoList.data.data.length > 1) {
          this.selectvehicle = true
          this.mineCoupons = bindCarInfoList.data.data
          console.log('mineCouponsCheckedObject', this.mineCoupons)
        } else if (bindCarInfoList && bindCarInfoList.data.data.length === 1) {
          this.selectvehicle = false
          this.mineCouponsCheckedObject = bindCarInfoList.data.data[0]
          this.getAfterSalesByVin(this.mineCouponsCheckedObject.vin)
          storage.set(
            'ownerVinInfo',
            JSON.stringify({
              vin: this.mineCouponsCheckedObject.vin,
              seriesCode: this.mineCouponsCheckedObject.seriesCode,
              modelCode: this.mineCouponsCheckedObject.modelCode,
              modelLineName: this.mineCouponsCheckedObject.modelNameCn,
              seriesName: this.mineCouponsCheckedObject.seriesName
            })
          )
        }
      } else {
        // 调用APP Dialog
        // callNative('popup', {
        //   type: 'alert',
        //   alertparams: {
        //     title: '',
        //     desc: '您还未绑定车辆，绑定车辆可以查看更多信息',
        //     actions: [
        //       {
        //         type: 'fill',
        //         title: '绑定车辆'
        //       },
        //       {
        //         type: 'stroke',
        //         title: '取消'
        //       }
        //     ]
        //   }
        // }).then((data) => {
        //   if (data.type === 'fill') {
        //     // 点击确定{"bizCode":"000003","callFunc":{"functionName":"bindCar"}}
        //     callNative('business', {
        //       callFunc: {
        //         functionName: 'bindCar'
        //       },
        //       bizCode: '000003'
        //     })
        //   }
        // })
      }
      this.loading = false
    },
    // 获取绑定是Vin的服务商
    async getAfterSalesByVin(vin) {
      this.bindVin = vin
      const { data } = await getAfterSalesByVin({ vin: vin })
      this.bindDealerCode = data.data.dealerCode
      console.log('this.bindDealerCode', this.bindDealerCode)
    },
    async getAfterSaleDetail(appoId) {
      const { data } = await getAfterSaleDetail({ appoId: appoId, type: 1 })

      if (data.code === '200') {
        this.submitParam.ownerName = data.data.ownerName
        this.submitParam.ownerTel = data.data.ownerTel
        storage.set(
          'dealerModel',
          JSON.stringify({
            dealerAddress: data.data.dealerAdrress,
            code758: data.data.dealerCode,
            dealerName: data.data.dealerName,
            dealerPhone: data.data.dealerPhone,
            dealerUrl: data.data.dealerUrl,
            serviceCode: data.data.dealerCode
          })
        )
        this.dealerCode = data.data.dealerCode
        this.dealerName = data.data.dealerName
        // this.$store.commit('saveDealer', {
        //   dealerAddress: data.data.dealerAdrress,
        //   code758: data.data.dealerCode,
        //   dealerName: data.data.dealerName,
        //   dealerPhone: data.data.dealerPhone,
        //   dealerUrl: data.data.dealerUrl
        // })

        this.$store.commit('saveSelectServiceTime', {
          appointmentTime: data.data.appointmentTime,
          timeCode: data.data.timeCode
        })
        this.changeEdit()
      }
    },
    changeEdit() {
      if (this.submitParam.ownerName !== '') {
        this.animations.ownerName = true
      }
      if (this.submitParam.ownerTel !== '') {
        this.animations.ownerTel = true
      }
    },
    // 选择服务商
    onSelectServiceProvider() {
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/select-service-providers-list',
        query: { bindDealerCode: this.bindDealerCode }
      })
    },
    // 选择预约时间
    onAppointmentTime() {
      this.sverSubmitParam()
      if (this.dealerCode === '') {
        //callNative('toast', { type: 'fail', message: '请选择服务商' })
        return
      }
      this.$router.push({
        path: '/aftersales/select-time-appoin',
        query: {
          ownerCode: this.dealerCode,
          type: 'appointmentTime'
        }
      })
    },
    sverSubmitParam() {
      this.$store.state.userName = this.submitParam.ownerName
      this.$store.state.userDriveTel = this.submitParam.ownerTel
      this.$store.state.isInitView = '1'
      storage.set('ownerName', this.submitParam.ownerName)
      storage.set('ownerTel', this.submitParam.ownerTel)
    },
    // 下一步
    onBtnNext() {
      if (this.dealerCode === '') {
        callNative('toast', { type: 'fail', message: '请选择服务商' })
        return
      }
      if (this.appointmentTime === '') {
        callNative('toast', { type: 'fail', message: '请选择预约时间' })
        return
      }
      if (this.submitParam.ownerName === '') {
        callNative('toast', { type: 'fail', message: '请填写姓名' })
        return
      }
      if (this.submitParam.ownerTel === '') {
        callNative('toast', { type: 'fail', message: '请填写联系方式' })
        return
      }
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/service-appointment-two',
        query: {
          ownerName: this.submitParam.ownerName,
          ownerTel: this.submitParam.ownerTel,
          // appointmentTime: new Date(this.appointmentTime).getTime(),
          appoId: this.appoId,
          serviceType: this.serviceType,
          inputRemark: this.inputRemark
        }
      })
    },

    animation(ref) {
      if (ref === 'ownerTel') {
        this.showPopover = true
      } else {
        this.showPopover = false
      }
      this.animations[ref] = true
      this.$refs[ref].focus()
    },
    handlerFocus(prop) {
      setTimeout(() => {
        const pannel = document.getElementById(prop)

        // 让当前的元素滚动到浏览器窗口的可视区域内
        pannel.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
        // 此方法是标准的scrollIntoView()方法的专有变体
        // pannel.scrollIntoViewIfNeeded();
      }, 300)

      if (!this.animations[prop]) {
        this.animations[prop] = true
      }
    },
    handlerBlur(prop) {
      this.animations[prop] = !!this.submitParam[prop]
    },
    failed(err) {
      console.error('failed', err)
    },
    onSelect(action) {
      this.showPopover = false
      this.submitParam.ownerTel = action.text
      this.changeEdit()
    },
    handleGotoBindCar() {
      // callNative('business', {
      //   callFunc: {
      //     functionName: 'bindCar'
      //   },
      //   bizCode: '000003'
      // })

      const { origin, pathname } = window.location
      const url = `${origin}${pathname}#/certification/my-certification?scene=latentGuestPageBindCarFromSA`
      callNative('audiOpen', { path: url })
      this.changeEdit()
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/buttons.less');
@import url('../../assets/style/forms-cell.less');
// @import url("../../assets/style/animation.less");
// @import url("../../assets/style/cell.less");

.serviceAppointmentOne {
  padding-bottom: 50px !important;
  padding: 0 16px 16px 16px;

  .hint {
    font-size: 12px;
    color: #000000;
    line-height: 18px;
  }

  .item-img {
    margin-top: 16px;
    width: 100%;
    height: 200px;
  }

  .item-title-bold {
    font-size: 16px;
    color: #000000;
    font-weight: normal;
    font-family: 'Audi-WideBold';
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
  }

  .item-title-bold2 {
    margin-bottom: 0;
    font-size: 16px;
    color: #000000;
    font-weight: normal;
    font-family: 'Audi-WideBold';
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
  }

  .line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;

    .title {
      font-size: 16px;
      color: #000;
    }

    .btn-change {
      display: flex;
      align-items: center;
      width: 70%;
      justify-content: flex-end;

      .change-name {
        font-size: 16px;
        color: #000;
        margin-right: 10px;
      }

      .btn-icon {
        align-items: center;
        font-size: 12px;
        color: #a3a3a3;
      }
    }
  }

  .placeholderText {
    font-size: 16px;
    color: #cccccc;
    font-weight: normal;
    font-family: 'Audi-WideBold';
  }

  // /deep/.van-field__error-message {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   height: 16px;

  //   &::before {
  //     content: "";
  //     display: inline-block;
  //     width: 14px;
  //     height: 14px;
  //     background: url("../../assets/img/error.png") no-repeat 0 0;
  //     background-size: 14px 14px;
  //     margin-right: 4px;
  //   }
  // }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
}

.btn-delete-wrapper {
  margin: 0 16px;
}

.pop-coupons-box {
  .pop-coupons-hd {
    height: 52px;
    padding: 0 16px;

    .icon-close {
      position: relative;
      top: 2px;
    }

    .h3 {
      font-family: Audi-WideBold;
      font-weight: 400;
      font-size: 16px;
    }

    .btn {
      width: 32px;
    }

    &::after {
      border-color: #d9d9d9;
      //border-width: 0.5px;
    }
  }

  .pop-coupons-main {
    max-height: calc(80vh - 52px);
    padding-bottom: 20px;
    box-sizing: border-box;
    overflow-y: auto;

    .coupons {
      .van-cell {
        padding: 16px;
      }
      .van-cell-group--inset {
        margin: 0;
        padding: 6px 0 40px;
        transition: all 0.3s;
        .van-cell.list {
          position: relative;
          width: auto;
          margin: 16px;
          padding: 16px;
          border: 1px solid #e5e5e5;
          min-height: 64px;
          overflow: visible;

          &.radio-checked {
            box-shadow: 1px 1px 6px rgba(0, 0, 0, 0.02),
              -1px -1px 6px rgba(0, 0, 0, 0.02);
          }

          &.radio-checked,
          &.radio-checked::before,
          &.radio-checked::after {
            border-color: #ccc;
          }

          &.radio-checked::before {
            box-shadow: inset -1px -1px 6px rgba(0, 0, 0, 0.02);
          }

          &.radio-checked::after {
            box-shadow: inset 1px 1px 6px rgba(0, 0, 0, 0.02);
          }

          &::before,
          &::after {
            z-index: 9;
            position: absolute;
            content: '';
            right: 84px;
            width: 6px;
            height: 3px;
            border: 1px solid #e5e5e5;
            background-color: #fff;
            transition: all 0.3s;
          }

          &::before {
            top: -1px;
            border-radius: 0 0 7px 7px;
            border-top: 0;
          }

          &::after {
            left: auto;
            bottom: -1px;
            border-radius: 7px 7px 0 0;
            border-bottom: 0;
            -webkit-transform: scaleY(1);
            transform: scaleY(1);
            box-sizing: content-box;
          }
        }

        &::after {
          display: none;
        }
      }

      .coupon-box {
        .h4,
        p {
          margin: 0;
        }

        .h4 {
          font-size: 16px;
          line-height: 24px;
          color: #333333;
          font-family: AudiTypeGB-WideBold, AudiTypeGB;
          font-weight: normal;
        }

        p {
          margin-top: 4px;
          height: 15px;
          line-height: 15px;
          color: rgba(0, 0, 0, 0.5);
        }
      }

      .van-radio {
        padding-left: 8px;
        .van-icon {
          &::before,
          &::after {
            content: '';
            width: 22px;
            height: 22px;
            border-color: #808080;
          }

          &::after {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #e5e5e5;
            transform: translate(-50%, -50%);
          }
        }

        .van-radio__icon--checked {
          .van-icon {
            background-color: #fff;
            border-color: #808080;

            &::after {
              position: absolute;
              width: 10px;
              height: 10px;
              background-color: #333;
            }
          }
        }
      }
    }
  }
}
</style>

<style lang="less">
// .view-wrapper {
//   height: calc(100vh - 60px) !important;
// }
.order-forms-box {
  &.bottom-one {
    margin-bottom: 100px !important;
  }
  .van-cell-group {
    border-top: 8px solid #f2f2f2;
    margin: 0 -16px;

    // padding-bottom: 24px;
    &:first-child {
      border-top-width: 0;
    }

    &.van-hairline--top-bottom {
      &::after {
        display: none;
      }
    }

    .van-hairline--bottom:after,
    .van-cell::after {
      border-color: #e5e5e5;
    }
    .van-field__label {
      margin-right: 0;
    }

    .cell-title {
      box-sizing: content-box;
      margin: 0 16px;
      padding: 16px 0;

      .h2 {
        margin: 0;
        font-size: 16px;
        line-height: 24px;
        font-family: AudiTypeGB-WideBold, AudiTypeGB;

        &.car-owner {
          margin-top: 26px;
        }

        &.mtb {
          margin-top: 8px;
        }
      }

      .sub {
        margin: 0;
        font-size: 10px;
        line-height: 20px;
        color: rgba(#000, 0.4);
      }
    }

    .van-cell {
      padding: 20px 0 16px;
      min-height: 24px;
      font-size: 16px;

      .van-cell__value {
        overflow: visible;
      }

      &.van-field--error {
        padding-bottom: 36px;

        &::after {
          bottom: 20px;
        }
      }

      .van-cell__title {
        flex: none;
        //margin-right: 20px;
        color: #000;
      }

      .van-field__control {
        height: 24px;
        line-height: 24px;

        &:disabled {
          color: #000;
          -webkit-text-fill-color: #000;
        }
      }

      .van-radio-group {
        .van-radio {
          margin-right: 24px;
        }
      }

      &:last-child::after {
        display: block;
      }

      &.van-field--error {
        .van-field__control {
          color: #333;

          &::placeholder {
            color: #ccc;
          }
        }

        .van-field__error-message {
          z-index: 19;
          position: absolute;
          bottom: -35px;
          margin-left: -85px;
          color: #eb0d3f;
          // font-size: 50px;
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 16px;
          &::before {
            content: '';
            position: relative;
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url('~@/assets/img/error-icon.png') no-repeat 0 0;
            background-size: 14px 14px;
            margin-right: 4px;
            top: 0;
          }
        }

        &::after {
          border-color: #eb0d3f;
        }
      }

      &.lan-cell-switch {
        .van-cell__value {
          overflow: visible;
        }

        .lan-switch {
          position: absolute;
          top: -3px;
          right: 0;

          .van-switch__node {
            width: 0.86em;
            height: 0.86em;
            margin: 0.07em;
          }

          &::before {
            content: '';
            position: absolute;
            right: 11px;
            top: 50%;
            width: 4px;
            height: 4px;
            border: solid 2px #8b8b8b;
            border-radius: 50%;
            transform: translateY(-50%);
          }

          &.van-switch--on {
            &::before {
              right: auto;
              left: 15px;
              width: 1px;
              height: 8px;
              border: none;
              border-radius: 0;
              border-left: solid 2px #fff;
            }
          }
        }
      }
    }

    #ownerTelHis {
      margin-top: -20px;
      .van-popover--light {
        .van-popover__content {
          margin-left: 100px;
          margin-top: -10px;
        }
      }
    }
  }
  .box-field {
    position: relative;
    #phone {
      position: absolute;
      z-index: 99;
      top: 60px;
      left: 110px;
      padding: 10px;
      background-color: #fff;
      border: solid 1px #e2e2e2;
      border-radius: 4px;
      box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.08);
      .item {
        margin-bottom: 5px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
