import qs from 'qs'
import request from '@/router/axios'
import api from '@/config/url'
import store from '@/store/index'
import { getToken } from '../utils/auth'
import { getChannel } from '@/utils/index.js'
import apiUrl from './url'

const { BaseApiUrl } = api

export const recomStyleFix = (params) => request({
  url: `${BaseApiUrl}${apiUrl.recommendStyleFix}`,
  method: 'GET',
  params: params,
  channel: getChannel()
})

export const getUpdateNew = (ccid) => request({
  url: `${BaseApiUrl}${apiUrl.updateNew}?ccid=${ccid}`,
  method: 'PUT',
  data: {},
  channel: getChannel()
})

export const policyGet = () => request({
  url: `${BaseApiUrl}${apiUrl.policyUrl}`,
  method: 'GET'
})

export const recommendStyleList = (params) => request({
  url: `${BaseApiUrl}${apiUrl.recommendStyleListUrl}`,
  method: 'GET',
  params: params,
  channel: getChannel()
})

export const recomCarByStyleList = (params) => request({
  url: `${BaseApiUrl}${apiUrl.recomCarByStyleUrl}`,
  method: 'GET',
  params: params,
  channel: getChannel()
})

// 获取车型版本
export const getStyleList = (params) => request({
  url: `${BaseApiUrl}${apiUrl.styleListUrl}`,
  method: 'GET',
  params: params,
  channel: getChannel()
})

// 获取车型(套装)
export const getByStyle = (params) => request({
  url: `${BaseApiUrl}${apiUrl.byStyleUrl}`,
  method: 'GET',
  params: params,
  channel: getChannel()
})

export const getPacketEquity = (params) => request({
  url: `${BaseApiUrl}${apiUrl.packetEquityUrl}`,
  method: 'GET',
  params: params,
  channel: getChannel()
})

// 获取权益图片
export const getRightsImg = (data) => request({
  url: `${BaseApiUrl}${apiUrl.getRightsImg}`,
  method: 'post',
  data: {
    ...data
  }
})

export const getCarConfigH5BaseUrl = async () => {
  const url = '/api-wap/cop-system/api/v1/config/audi.ccpro.index'
  const res = await request({
    url: `${BaseApiUrl}${url}`,
    method: 'GET'
  })
  // console.log('getCarConfigH5BaseUrl res:', res)
  return res.data.data.configValue
}

// 获取车系
export const getCarList = () => request({
  url: `${BaseApiUrl}${apiUrl.carList}`,
  method: 'GET',
  channel: getChannel()
})
export const getRecommendCar = (customSeriesId) => request({
  url: `${BaseApiUrl}${apiUrl.recommendCar}`,
  method: 'GET',
  params: { customSeriesId },
  channel: getChannel()
})
export const getBestRecommendConfig = (bestRecommendId, entryPoint) => request({
  url: `${BaseApiUrl}${apiUrl.bestRecommendConfig}`,
  method: 'POST',
  params: { bestRecommendId, sourceId: store.state.sourceId, entryPoint },
  channel: getChannel()
})
// 获取配置线
export const getModellineList = (customSeriesId, type) => request({
  url: `${BaseApiUrl}${apiUrl.modelList}`,
  method: 'GET',
  params: { customSeriesId, type },
  channel: getChannel()
})

export const getSwitchModellineList = (customSeriesId) => request({
  url: `${BaseApiUrl}${apiUrl.switchmodelList}`,
  method: 'GET',
  params: { customSeriesId },
  channel: getChannel()
})

// 获取外饰
export const getExterior = (modelLineId, seats) => request({
  url: `${BaseApiUrl}${apiUrl.exterior}`,
  method: 'GET',
  params: { modelLineId, seats },
  channel: getChannel()
})

// 获取内饰
export const getInterior = (modelLineId) => request({
  url: `${BaseApiUrl}${apiUrl.interior}`,
  method: 'GET',
  params: { modelLineId },
  channel: getChannel()
})
// 获取外饰

// 获取可选装备

// 获取配置线轮毂
export const getModelHub = (modelLineId, seats) => request({
  url: `${BaseApiUrl}${apiUrl.rad}`,
  method: 'GET',
  params: { modelLineId, seats },
  channel: getChannel()
})
// 提交配置单获取ccid

// 根据ccid获取车辆配置

// 修改配置器车辆

// 获取座椅
export const getInteriorChair = (modelLineId) => request({
  url: `${BaseApiUrl}${apiUrl.vos}`,
  method: 'GET',
  params: { modelLineId },
  channel: getChannel()
})
// 获取饰条
export const getInteriorEih = (modelLineId, seats) => request({
  url: `${BaseApiUrl}${apiUrl.eih}`,
  method: 'GET',
  params: { modelLineId, seats },
  channel: getChannel()
})
// 获取面料
export const getInteriorSib = (modelLineId) => request({
  url: `${BaseApiUrl}${apiUrl.sib}`,
  method: 'GET',
  params: { modelLineId },
  channel: getChannel()
})
// 获取私人订制选装包
export const getPrivateOrder = (modelLineId) => request({
  url: `${BaseApiUrl}${apiUrl.personalOption}`,
  method: 'GET',
  params: { modelLineId },
  channel: getChannel()
})
// 获取选装包详情
export const getPacketItem = (modelLineId, optionId) => request({
  url: `${BaseApiUrl}${apiUrl.packetItem}`,
  method: 'GET',
  params: { modelLineId, optionId },
  channel: getChannel()
})
// 获取内饰与面料
export const getSibColorInterieur = (modelLineId, seats) => request({
  url: `${BaseApiUrl}${apiUrl.sibColorInterieur}`,
  method: 'GET',
  params: { modelLineId, seats },
  channel: getChannel()
})
// 通过optionIds获取配置项详情
export const getOptionsInfoList = (modelLineId, optionIds) => request({
  url: `${BaseApiUrl}${apiUrl.getOptionsInfoList}`,
  method: 'GET',
  params: { modelLineId, optionIds },
  paramsSerializer: (params) => qs.stringify(params, { indices: false }),
  channel: getChannel()
})
// 计算价格
export const getPriceCompute = (modelLineId, optionIds) => request({
  url: `${BaseApiUrl}${apiUrl.priceCompute}`,
  method: 'POST',
  data: { modelLineId, optionIds },
  channel: getChannel()
})
// 半定制计算价格
export const getMeasurePriceCompute = (modelLineId, optionIds) => request({
  url: `${BaseApiUrl}${apiUrl.measurePriceCompute}`,
  method: 'POST',
  data: { modelLineId, optionIds },
  channel: getChannel()
})

// 计算交付时间
export const getDeliveryTimeCompute = (modelLineId, optionIds) => request({
  url: `${BaseApiUrl}${apiUrl.deliveryTimeCompute}`,
  method: 'POST',
  data: { modelLineId, optionIds },
  channel: 'oneapp'
})
// 根据ccid获取交付时间
export const getDeliveryTimeByCcid = (ccid, dealerNetCode) => request({
  url: `${BaseApiUrl}${apiUrl.getDeliveryTimeByCcid}`,
  method: 'GET',
  params: { ccid, dealerNetCode },
  channel: 'oneapp'
})

// 生成配置单
export const getCcInfo = (modelLineId, optionIds, sibInterieurId, entryPoint,mstMgrpId) => request({
  url: `${BaseApiUrl}${apiUrl.getCc}`,
  method: 'POST',
  data: {
    modelLineId,
    optionIds,
    sibInterieurId,
    sourceId: store.state.sourceId,
    entryPoint,
    mstMgrpId
  },
  channel: getChannel()
})
// 是否可以买A7L先行版
export const canBuyA7LEdtionOne = (ccid) => request({
  url: `${BaseApiUrl}${apiUrl.canBuyA7LEdtionOne}?ccid=${ccid}`,
  method: 'POST',
  data: {},
  channel: 'oneapp'
})
// 是否可以买A7L先行版
export const canBuyA7LEdtionOne2 = (ccid) => request({
  url: `${BaseApiUrl}${apiUrl.canBuyA7LEdtionOne2}?ccid=${ccid}`,
  method: 'POST',
  data: {},
  channel: 'oneapp'
})
// 修改配置单
export const updateCcInfo = (ccid, modelLineId, optionIds, sibInterieurId) => request({
  url: `${BaseApiUrl}${apiUrl.ccConfigration}${ccid}`,
  method: 'PUT',
  data: {
    modelLineId,
    optionIds,
    sibInterieurId,
    sourceId: store.state.sourceId
  },
  paramsSerializer: (params) => qs.stringify(params, { indices: false }),
  channel: getChannel()
})
// 查询配置单
export const getCcConfigration = (ccid) => request({
  url: `${BaseApiUrl}${apiUrl.ccConfigration}${ccid}`,
  method: 'GET',
  params: { sourceId: store.state.sourceId },
  channel: getChannel()
})
// 自动保存
export const autoSaveCarShoppingCart = (
  modelLineId,
  optionIds,
  sibInterieurId,
  entryPoint
) => request({
  url: `${BaseApiUrl}${apiUrl.autoSaveCarShoppingCart}`,
  method: 'POST',
  data: {
    carCustomDto: {
      modelLineId,
      optionIds,
      sibInterieurId,
      entryPoint
    }
  },
  channel: getChannel()
})

// OMD畅销推荐车，限定车
export const bestRecommendCar = (customSeriesId) => request({
  url: `${BaseApiUrl}${apiUrl.bestRecommendCar}`,
  method: 'GET',
  params: { customSeriesId },
  channel: 'oneapp'
})
// OMD经销商库存车
export const bestRecommendCarAgent = (params) => request({
  url: `${BaseApiUrl}${apiUrl.bestRecommendCarAgent}`,
  method: 'GET',
  params: params.dealerCode ? params : { params },
  channel: getChannel()
})

// 查询配置单
export const getModelLineQuery = (modelLineId) => request({
  url: `${BaseApiUrl}${apiUrl.modelLineQuery}`,
  method: 'GET',
  params: { modelLineId },
  channel: getChannel()
})
// 半定制化查询选配
export const measureQuery = (modelLineId, optionIds, seats, customType) => request({
  url: `${BaseApiUrl}${apiUrl.measureQuery}?${customType}`,
  method: 'POST',
  data: { modelLineId, optionIds, seats },
  channel: getChannel()
})
// 半定制车 生成配置单
export const getV2CcInfo = (measureId, entryPoint, packetEquityId) => request({
  url: `${BaseApiUrl}${apiUrl.getV2CcInfo}?measureId=${measureId}&sourceId=${store.state.sourceId}&entryPoint=${entryPoint}&packetEquityId=${packetEquityId}`,
  method: 'POST',
  data: {},
  channel: getChannel()
})

// 半定制修改配置单
export const getCcMeasure = (measureId, ccid, packetEquityId) => request({
  url: `${BaseApiUrl}${apiUrl.putMeasureInfo}?measureId=${measureId}&ccid=${ccid}&packetEquityId=${packetEquityId}`,
  method: 'PUT',
  data: {},
  channel: getChannel()
})

/**
 *
 */

// 获取动力,版本,配置线 新版ui
export const getEnergyStyleList = (customSeriesId, type) => request({
  url: `${BaseApiUrl}${apiUrl.getEnergyStyleList}`,
  method: 'GET',
  params: { customSeriesId, type },
  channel: getChannel()
})

// 获取选装包 新版ui
export const getPersonalOptionPlus = (modelLineId, seats) => request({
  url: `${BaseApiUrl}${apiUrl.getPersonalOptionPlus}`,
  method: 'GET',
  params: { modelLineId, seats },
  channel: getChannel()
})

// 获取交付时间和定金
export const postCcEstimate = ({
  beforeCheck,
  customSeriesId,
  modelLineId,
  optionIds,
  seats,
  measure
}) => request({
  url: `${BaseApiUrl}${apiUrl.postCcEstimate}`,
  method: 'POST',
  data: {
    beforeCheck,
    customSeriesId,
    modelLineId,
    optionIds,
    seats,
    measure
  },
  channel: getChannel()
})

// 绑定ccid到用户
export const postBindUserByCCid = ({ ccid }) => request({
  url: `${BaseApiUrl}${apiUrl.bindUserByCCid}`,
  method: 'POST',
  params: { ccid },
  channel: getChannel()
})

// 获取所有车系金融服务的大图
export const getPowerPageImg = () => request({
  url: `${BaseApiUrl}${apiUrl.getPowerPageImg}`,
  method: 'GET',
  params: {
    pageFrontCode: 'car-buying-advertisement',
    pageAccessType: 2
  },
  channel: getChannel()
})

// 获取omd 销售配车-车型名称列表
export const getOmdModels = (seriesCode, type) => request({
  url: `${BaseApiUrl}${apiUrl.omdModels}`,
  method: 'GET',
  params: { seriesCode, type },
  channel: getChannel()
})

// 获取omd 销售配车-车型价格
export const getOmdModelsPrice = (modelUniqueCode, seriesCode) => request({
  url: `${BaseApiUrl}${apiUrl.omdModelsPrice}`,
  method: 'GET',
  params: { modelUniqueCode, seriesCode },
  channel: getChannel()
})
// omd 选中车型详情
export const postOmdModelConfig = (paramDto) => request({
  url: `${BaseApiUrl}${apiUrl.omdModelConfig}`,
  method: 'POST',
  params: paramDto,
  channel: getChannel()
})

// omd 选中车型详情
export const getOmdEquipmentGroup = (modelUniqueCode , labelCode , labelValue) => request({
  url: `${BaseApiUrl}${apiUrl.omdEquipmentGroup}`,
  method: 'GET',
  params: {modelUniqueCode , labelCode , labelValue},
  channel: getChannel()
})
export const getOmdGetlabel = (featureCode , modelUniqueCode) => request({
  url: `${BaseApiUrl}${apiUrl.omdGetlabel}`,
  method: 'GET',
  params: {featureCode , modelUniqueCode},
  channel: getChannel()
})

// omd ccid
export const postOmdGetCcid = (okapiCustomDto) => request({
  url: `${BaseApiUrl}${apiUrl.omdGetCcid}`,
  method: 'POST',
  data: okapiCustomDto,
  channel: getChannel()
})

// omd ccid
export const putOmdGetCcid = (okapiCustomDto) => request({
  url: `${BaseApiUrl}${apiUrl.omdGetCcid}`,
  method: 'PUT',
  data: okapiCustomDto,
  channel: getChannel()
})

// 券码转发-COP查券
export const getCopCouponInfo = (userId, seriesCode, ccid) => request({
  url: `${BaseApiUrl}${apiUrl.copCouponInfo}`,
  method: 'GET',
  customHeader:{
    'X-Member-Id': userId,
  },
  params: { seriesCode, ccid},
  channel: getChannel()
})

export const getOmdHighLight = (modelUniqueCode) => request({
  url: `${BaseApiUrl}${apiUrl.omdHighLight}`,
  method: 'GET',
  params: {modelUniqueCode},
  channel: getChannel()
})