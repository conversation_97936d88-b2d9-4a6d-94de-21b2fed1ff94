<template>
  <div class="container">
    <div class="circle-wrapper">
      <div class="circle">
        <div>
          {{ number }}
        </div>
      </div>

      <div class="desc">
        将在3s后跳转至签署合同页，请您 完成购车合同签署
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      number: 3,
      intervalId: 0
    }
  },

  mounted() {
    this.startTiming()
  },
  destroyed() {
    clearInterval(this.intervalId)
  },

  methods: {
    startTiming() {
      this.intervalId = setInterval(() => {
        if (this.number === 1) {
          clearInterval(this.intervalId)
          this.toContractInfoPage()
        }
        this.number -= 1
      }, 1000)
    },

    toContractInfoPage() {
      const { orderId } = this.$route.query
      this.$router.push({
        path: '/contract-info',
        query: { orderId }
      })
    }
  }
}
</script>

<style lang="less" scoped>
  .container {
    position: relative;
  }

  .circle-wrapper {
    margin-top: 18vh;

    .circle {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;

      width: 72px;
      height: 72px;
      border-radius: 50%;
      border: 1px solid;
    }
    .desc {
      margin: 20px auto 0 auto;
      font-size: 18px;
      text-align: center;
      width: 273px;
    }
  }
</style>
