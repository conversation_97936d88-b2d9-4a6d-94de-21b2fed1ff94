<template>
  <div
    name="name"
    style="position: relative;"
  >
    <navigation :back-type="backType">
      <div
        @click="onHidden"
        class="_right-text"
      >
        {{ isHiddenEqual ? '显示相同':'隐藏相同' }}
      </div>
    </navigation>

    <contrast
      :car-table.sync="carTable"
      :hidden-equal="isHiddenEqual"
    />
  </div>
</template>

<script>
// models
import contrast from './contrast.vue'
import navigation from '../../components/navigation.vue'
import { getCarConfig } from '@/api/api'

export default {
  components: { contrast, navigation },
  name: 'Name',
  data() {
    return {
      backType: 'app',
      statusBarHeight: 0,
      navigationBarHeight: 50,
      isHiddenEqual: false, // 是否隐藏相同
      carlist: [],
      carTable: {}
    }
  },
  mounted() {
    const { ccid } = this.$route.query
    console.log('ccid==', ccid)
    this.getCarInfo(ccid)
  },

  methods: {
    async getCarInfo(ccid) {
      const ccIdList = ccid.split(',')
      if (ccIdList.length > 1) {
        const res = await Promise.all(
          ccIdList.map((i) => this.getCarContrastConfig(i))
        )
        this.carlist = res
      }
      this.showView()
    },

    async getCarContrastConfig(carCustomId) {
      const { data } = await getCarConfig({ ccid: carCustomId })
      if (data.data) {
        return data.data
      }
      return ''
    },
    async showView() {
      const carTable = {
        totalPrice: [], // 总价
        carModel: [], // 型号
        carSeries: [], // 系列
        outsideColor: [], // 外饰
        // 还差一个装备
        RADlist: [], // 轮毂
        VOSlist: [], // 座椅
        EIHlist: [] // 饰条
      }

      this.carlist.forEach((i, index) => {
        carTable.totalPrice.push(i.configDetail.totalPrice)
        carTable.carModel.push(i.configDetail.carModel)
        carTable.carSeries.push(i.configDetail.carSeries)
        carTable.outsideColor.push(i.configDetail.outsideColor)

        carTable.RADlist.push([{ optionCode: '02' }])
        carTable.VOSlist.push([{ optionCode: '02' }])
        carTable.EIHlist.push([{ optionCode: '02' }])

        if (i.configDetail.carSeries.seriesCode === 'G4') {
          i.configDetail.optionList.map((j) => {
            if (j.optionClassification === 'EIH') {
              j.optionCode = '02'
              j.optionNameCn = '--'
            }
          })
        }

        i.configDetail.optionList.map((j) => {
          switch (j.optionClassification) {
            case 'COLOR_EXTERIEUR':
              // 外饰颜色
              break
            case 'COLOR_INTERIEUR':
              // 内饰颜色
              break
            case 'VOS':
              // 座椅
              if (carTable.VOSlist.length > 0 && carTable.VOSlist[index]) {
                carTable.VOSlist[index].push(j)
              } else {
                carTable.VOSlist.push([j])
              }
              break
            case 'RAD':
              // 轮毂
              if (carTable.RADlist.length > 0 && carTable.RADlist[index]) {
                carTable.RADlist[index].push(j)
              } else {
                carTable.RADlist.push([j])
              }
              break
            case 'EIH':
              // 饰条
              if (carTable.EIHlist.length > 0 && carTable.EIHlist[index]) {
                carTable.EIHlist[index].push(j)
              } else {
                carTable.EIHlist.push([j])
              }
              break
            case 'SIB':
              // 面料
              break
            case 'PACKET':
              // 选装包
              break
            default:
              break
          }
        })
      })
      carTable.VOSlist = this.dataHandle(carTable.VOSlist, carTable.VOSlist[0][0]?.optionCode)
      carTable.RADlist = this.dataHandle(carTable.RADlist, carTable.RADlist[0][0]?.optionCode)
      carTable.EIHlist = this.dataHandle(carTable.EIHlist, carTable.EIHlist[0][0]?.optionCode)
      this.carTable = carTable
      console.log('carTable', carTable)
    },
    dataHandle(list, value) {
      let isHide = true
      const optionCode = value
      list.forEach((i) => {
        i.forEach((j) => { if (j.optionCode !== optionCode && optionCode !== '02') isHide = false })
      })
      list.forEach((i) => {
        if (i.length > 1 && i[0].optionCode === '02') i.shift()
        i.forEach((j) => {
          j.isHide = isHide
          j.show = 1
        })
      })
      return list
    },
    // 点击影藏
    onHidden() {
      this.isHiddenEqual = !this.isHiddenEqual
    }
  }
}
</script>

<style lang='less' scoped>
  h3 div {
    box-sizing: border-box;
  }

  .border-bottom {
    border-bottom: 1px solid #e5e5e5;
  }

  .transparent {
    background-color: transparent;
  }

  .color-white {
    color: #fff !important;
  }

  .nav-box {
    position: fixed;
    top: 0;
    display: flex;
    width: 100%;
    text-align: center;
    z-index: 2000;
    border-bottom: 1px #dddada solid;

    .nav-back-box {
      width: 10%;
    }

    .nav-content {
      flex: 1;
      display: flex;
      align-items: center;
      padding-bottom: 10px;
      height: 35px;
      background-color: #fff;
    }

    .nav-title {
      font-size: 18px;
      font-weight: 500;
      flex: 1;
      max-width: 70% !important;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-left: 14%;
    }

    .nav-back {
      width: 20px;
      height: 20px;
      margin-left: 10px;
    }

    .nav_right {
      display: flex;
      justify-content: flex-end;
      padding-right: 15px;
    }

    ._right-text {
      font-size: 14px;
      color: #999999;
    }
  }
</style>
