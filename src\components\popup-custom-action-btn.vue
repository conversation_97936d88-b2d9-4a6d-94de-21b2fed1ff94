<!--
  eslint-disable vue/no-mutating-props
 * <AUTHOR> <PERSON>
 * @Date         : 2022-08-03 10:34:40
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-12-15 20:45:21
 * @FilePath     : \src\components\popup-custom-action-btn.vue
 * @Descripttion : 自定义 popup 弹窗（交互按钮）
-->
<template>
  <van-popup
    v-model="popupShow"
    class="popup-custom popup-custom-skin"
    :close-on-click-overlay="false"
  >
    <div class="popup-custom-box">
      <div class="popup-custom-main">
        <slot name="popup-custom-main" />
      </div>
      <div
        :class="['popup-custom-btn', btnConf.customClass || '']"
        :data-flex="btnConf.flex"
      >
        <template v-if="btnItems && Object.keys(btnItems).length">
          <div
            class="btn btn-box"
            v-for="(item, index) in btnItems"
            :key="index"
            :style="item.style"
          >
            <van-button
              :class="`lan-custom-btn lan-${item.name || 'list'}-btn ${btnConf.customDisabled ? 'lan-custom-disabled' : ''}`"
              :block="item.block"
              :type="item.type || 'default'"
              :disabled="item.disabled || item.loading || !!item.loadingText"
              :loading="!!item.loadingText || item.loading || false"
              :loading-text="item.loadingText"
              :icon="item.icon"
              :color="item.color || CONF_SKIN.DEFAULT"
              :plain="!!item.plain"
              size="large"
              @click="handleBtnClick(item, index)"
            >
              {{ item.text }}
            </van-button>
          </div>
        </template>
      </div>
    </div>
  </van-popup>
</template>

<script>
import Vue from 'vue'
import { mapGetters } from 'vuex'
import { Popup, Button, Toast } from 'vant'
import { CONF_SKIN } from '@/config/conf.data'

Vue.use(Popup).use(Button).use(Toast)
const styleKeys = ['height', 'width']
export default {
  name: 'PopupCustomActionBtn',
  props: {
    btnConf: {
      type: Object,
      default: () => {}
    },
    btnItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      CONF_SKIN,
      popupShow: false
    }
  },
  watch: {
    btnConf: {
      handler({ show }) {
        this.popupShow = show
      },
      deep: true,
      immediate: true
    },
    btnItems: {
      handler(val) {
        const { btnConf: { width, borderRadius } } = this
        val.forEach((i) => {
          let style = ''
          const keys = Object.keys(i);
          (width && !keys.includes('width')) && ((style += `--lan-popup-button-width:${width};`))
          // eslint-disable-next-line no-return-assign
          styleKeys.forEach((el) => (keys.includes(el) && i[el]) && (style += `--lan-popup-button-${el}:${i[el]};`))
          style && (i.style = style)
        })
      },
      immediate: true,
      deep: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    ...mapGetters(['getDevice']),
    handleBtnClick({
      customDisabled, disabled, click, loadingText, name, native
    }, index) {
      const { nativeApp } = this.getDevice() || ''
      if (!nativeApp && native === 'app') {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '请在APP中使用该功能',
          forbidClick: true,
          duration: 800
        })
      }

      const { btnItems } = this
      const isLoadingText = btnItems.map((i) => i.loadingText).filter(Boolean) || []
      if (isLoadingText.length || [customDisabled, disabled].includes(true)) return
      if (!click) {
        this.popupShow = false
        return
      }
      this.$emit(`emit${click}`, [click, index, name])
    }
  }
}
</script>

<style lang="less">
@button-disabled-opacity: .9;
.popup-custom-box {
  .lan-popup-custom-btn {
    height: var(--lan-popup-button-min-height, auto);
  }
  .lan-custom-btn {
    height: var(--lan-popup-button-height, 55px);
    width: var(--lan-popup-button-width, 100%);
    border-radius: var(--lan-popup-button-radius, 0);
    transition: all .45s;
    &.van-button--default {
      display: block !important
    }
    // &.van-button--plain {
    //   height: calc(100% - 1px);
    //   position: relative;
    //   top: 1px
    // }
    &.van-button--disabled {
      opacity: .85;
      .van-button__content {
        opacity: .8;
        .van-loading {
          margin-right: 5px;
        }
      }
    }
    &.lan-custom-disabled {
      opacity: 1;
      &:active::before {
        opacity: 0;
      }
    }
  }
}

</style>
