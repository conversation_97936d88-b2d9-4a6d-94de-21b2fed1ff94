<template>
  <div>
    <div class="collapse-wrapper">
      <van-collapse v-model="activeNames" accordion>
        <!-- 推荐组合 -->
        <van-collapse-item name="RECOMMENDED" v-if="composeOptionVisible">
          <template #title>
            <div>推荐组合 <van-icon name="arrow" class="van-cell__right-icon" /></div>
          </template>
          <template #right-icon>
            <van-checkbox :value="optionComposesChecked" checked-color="#000"></van-checkbox>
          </template>

          <div v-for="item, idx in pageOptionComposes" :key="idx" @click="toSelectOptionComposes(item)">
            <!-- 夏日礼包 -->
            <div v-if="item.composePersonalOptions.find(i=>summerPackage.includes(i.optionCode))" class="card-wrapper" :class="{
              'selected': currentComposeName === item.composeName,
              'disabled': item.disabled
            }">
              <div class="title-wrapper c-flex-between c-bold c-font16">
                <div v-if="currentSeriesName ==='q5e'">{{ item.composePersonalOptions.find(i=>summerPackage.includes(i.optionCode))?.optionName}}</div>
                <div v-else>推荐组合</div>
                <div> ¥ {{ item.composePrice | formatPrice }}</div>
                <div class="summer-price">-¥ {{  summerPrice | formatPrice }}</div>
              </div>

              <!-- // 内容  -->
              <div v-if="currentSeriesName ==='q5e'" class="content-wrapper c-font12">
                <div v-for="i in item.composePersonalOptions.find(i=>summerPackage.includes(i.optionCode)).packetItems" :key="i.optionCode"> {{ i.optionName }}</div>
              </div>
              <div v-else class="content-wrapper c-font12">
                <div v-for="i in item.composePersonalOptions" :key="i.optionCode"> {{ i.optionName  }}</div>
              </div>

              <div v-if="currentSeriesName ==='q5e'" class="btn-wrapper c-font12 ">
                <div v-if="item.composePersonalOptions.length !== 0" @click.stop="toOptionDetail(item.composePersonalOptions.find(i=>summerPackage.includes(i.optionCode)))">查看详情</div>
              </div>
              <div v-else class="btn-wrapper c-font12 ">
                <div v-if="item.composePersonalOptions.length !== 0" @click.stop="toCommpendListPage(item, idx)">查看详情</div>
              </div>
              <div class="tag-wrapper"> 限时优惠 </div>
            </div>

            <div v-else class="card-wrapper" :class="{
              'selected': currentComposeName === item.composeName,
              'disabled': item.disabled
            }">
              <!-- a7l后排剧院级音响智能屏套装 -->
              <div v-if='A7L_INTELLIGENT_AUDIO.optionCode.length === item.composePersonalOptions.map(i => i.optionCode).length && A7L_INTELLIGENT_AUDIO.optionCode.every(code => item.composePersonalOptions.map(i => i.optionCode).includes(code))' class="title-wrapper c-flex-between c-bold c-font16">
                <div class="relative">{{ A7L_INTELLIGENT_AUDIO.name }}
                  <div class="discount c-font10">限时优惠</div>
                </div>
                <div>¥ {{  item.composePrice | formatPrice }}</div>
                <div class="summer-price"> ¥{{ A7L_INTELLIGENT_AUDIO.price  | formatPrice }}</div>
              </div>

              <div v-else class="title-wrapper c-flex-between c-bold c-font16">
                <div>{{ item.composePersonalOptions.length === 0 ? '无需选装' : '推荐组合' }}</div>
                <div> ¥{{ item.composePrice | formatPrice }}</div>
              </div>
              <div class="c-font12 " v-show="item.dependsTag"> {{ composeDependDesc }}</div>

              <div class="content-wrapper c-font12">
                <div v-for="i in item.composePersonalOptions" :key="i.optionCode"> {{ i.optionName }} {{ a7lFigureKey.includes(i.optionCode) ? '(支持部分手机机型)': '' }}</div>
              </div>

              <div class="btn-wrapper c-font12 ">
                <div v-if="item.composePersonalOptions.length !== 0" @click.stop="toCommpendListPage(item, idx)">查看详情</div>
              </div>
            </div>
          </div>
        </van-collapse-item>

        <!-- 全部选装 -->
        <van-collapse-item name="ALL" v-if="allOptionVisible">
          <template #title>
            <div>全部选装 <van-icon name="arrow" class="van-cell__right-icon" /></div>
          </template>
          <template #right-icon>
            <van-checkbox :value="optionChecked" checked-color="#000"></van-checkbox>
          </template>

          <div v-for="item in pageAllOptionList" :key="item.optionCode"> <div class="card-wrapper"  @click="clickCheckBox(item)">
              <div class="title-wrapper c-flex-between c-bold c-font16">
                <div class="name">{{ item.optionName }}</div>
                <div :class="{
                  'price-text': !item.price
                }"> {{ item.price | finalFormatPriceDesc }}</div>
              </div>

              <div class="btn-wrapper c-font12 margin-top">
                <div class="depend-desc" v-show="item.dependsTag"> {{ pageAllOptionList.filter(i=> i.dependsTag).length > 1 ? "可选的组合套装(多选一)" : "关联的组合套装" }} </div>
                <div class="depend-desc" v-show="a7lFigureKey.includes(item.optionCode)"> 支持部分手机机型 </div>
                <div @click.stop="toOptionDetail(item)">查看详情</div>
                <van-checkbox :value="item.selected" checked-color="#000" :disabled="item.disabled"></van-checkbox>
              </div>
            </div>
          </div>

        </van-collapse-item>
      </van-collapse>
    </div>

    <!-- q5e荣耀 选装与标装的互斥:前排座椅按摩与前排座椅通风冲突 -->
    <div v-if="isQ5eRongYao">
      <ConflictDialog :show="optionDialogVisible" @cancel="optionDialogVisible = false"  @triggerBuriedPoint="clickOptionPopupSensors"/>
    </div>

    <CommonFooter @click="nextPage" />
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import {
  Collapse, CollapseItem, Checkbox, CheckboxGroup, Dialog, Toast, Divider
} from 'vant'
import wx from 'weixin-js-sdk'
import CommonFooter from '../components/commonFooter.vue'
import { getFilterRelateList } from '@/view/newConfigration/util/helper'
import ConflictDialog from './conflictDialog.vue'
import {
  STANDARD_CAR, SUMMER_PACKAGE, A7L_FIGURE_KEY, A7L_INTELLIGENT_AUDIO
} from '../util/carModelSeatData'
import { getUrlParamObj } from '@/utils'

const { env } = getUrlParamObj()
Vue.use(Toast)
Vue.use(Checkbox)
Vue.use(CheckboxGroup)
Vue.use(Collapse)
Vue.use(CollapseItem)
Vue.use(Divider)

let firstToastTag = true
export default {
  name: 'ConfigrationOption',
  components: { CommonFooter, ConflictDialog },
  data() {
    return {
      activeNames: 'RECOMMENDED',
      // optionCodeListDisabledBySib: [],
      optionDialogVisible: false,
      summerPackage: SUMMER_PACKAGE,
      a7lFigureKey: A7L_FIGURE_KEY,
      A7L_INTELLIGENT_AUDIO: A7L_INTELLIGENT_AUDIO,
      pageStartTime: 0
    }
  },
  computed: {
    ...mapGetters(['pageOptionComposes', 'pageAllOptionList', 'currentCarType', 'paramSibDependOptionList', 'paramSibConflictOptionList', 'currentSeriesName', 'paramEihDependsOptionList']),
    ...mapState({
      ccid: (state) => state.ccid,
      skuid: (state) => state.skuid,
      currentSeat: (state) => state.configration.currentSeat,
      currentHub: (state) => state.configration.currentHub,
      currentComposeName: (state) => state.configration.currentComposeName,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentSib: (state) => state.configration.currentSib,
      personalOptionComposes: (state) => state.configration.personalOptionComposes,
      deliveryTimeData: (state) => state.configration.deliveryTimeData,
      selectedOptions: (state) => state.configration.selectedOptions,
      carIdx: (state) => state.configration.carIdx,
      referConfigrationActiveTab: (state) => state.configration.referConfigrationActiveTab,
      currentVersion: (state) => state.configration.currentVersion,
      configrationActiveTab: (state) => state.configration.configrationActiveTab
    }),

    // 推荐组合标题选中状态
    optionComposesChecked() {
      return this.currentComposeName
    },

    // 全部选装标题选中状态
    optionChecked() {
      return this.pageAllOptionList.find((item) => item.selected && !item.disabled)
    },

    /**
     * 显示推荐组合
     */
    composeOptionVisible() {
      if (this.currentModelLineData.measure === 1) {
        // 半定制强制显示
        if (!STANDARD_CAR.includes(this.currentModelLineData.modelLineCode)) {
          return true
        }
      }
      const status2 = this.pageAllOptionList.filter((i) => i.status === 2) // 可选装备列表
      const isStandardCar = STANDARD_CAR.includes(this.currentModelLineData.modelLineCode)
      return this.pageOptionComposes.length > 0 && !isStandardCar
    },
    /**
     * 显示全部选装
     * 1. 包含c
     * 2. 特殊车型列表
     * -----2. pageAllOptionList 主数据里无可用选装  #测试删掉这个条件
     */
    allOptionVisible() {
      if (this.currentModelLineData.measure === 1) {
        // 半定制 逻辑
        // if (STANDARD_CAR.includes(this.currentModelLineData.modelLineCode)) {
        //   return true
        // }

        if (this.pageAllOptionList.length === 0) {
          return false
        }
      }
      const isStandardCar = STANDARD_CAR.includes(this.currentModelLineData.modelLineCode)
      const includeC = this.currentModelLineData.typeFlag?.includes('C')
      // const status2 = this.pageAllOptionList.filter((i) => i.status === 2)
      return includeC || isStandardCar
    },

    isQ5eRongYao() {
      const code = this.currentModelLineData.modelLineCode
      return ['G4IBF3003', 'G4ICF3007', 'G4ICF3012', 'G4IBF3005', 'G4ICF3017', 'G4IBF3007'].includes(code)
    },

    // 当前面料依赖多选一的列表
    optionSingleList() {
      return this.paramSibDependOptionList.map((i) => i.optionRelateCode)
    },

    // 推荐组合描述文案
    composeDependDesc() {
      const composeDepends = this.pageOptionComposes.filter((i) => i.dependsTag)
      if (composeDepends.length > 1) {
        return '可选的组合套装(多选一)'
      }
      return '需组合的套装'
    },

    /**
     * 夏日礼包的原价,这里写死
     * 正常是跟着code走,这里为了简化,随着车型判断.
     * 夏日礼包下线可删除
     */
    summerPrice() {
      if (this.currentSeriesName === 'a7l') {
        return 53700
      }
      if (this.currentSeriesName === 'q5e') {
        return 13000
      }
      return 0
    }
  },

  watch: {
    // 全部选装数据
    pageAllOptionList: {
      deep: true,
      handler(val) {
        this.watchPersonalOptions(val)
      }
    },
    configrationActiveTab(val) {
      if (val === 'option') {
        this.setUITitle()
        this.pageStartTime = new Date().getTime()
      }
    }
  },

  mounted() {
    this.setUITitle()
    this.doFromDetailAction()
    this.pageStartTime = new Date().getTime()
  },

  methods: {
    // 标题栏的隐藏
    setUITitle() {
      const onlyC = this.currentModelLineData.typeFlag === 'C'
      // 设置默认展开的栏目
      if (onlyC || !this.composeOptionVisible || STANDARD_CAR.includes(this.currentModelLineData.modelLineCode)) {
        this.activeNames = 'ALL'
      } else {
        this.activeNames = 'RECOMMENDED'
      }

      /**
       * 标题栏的隐藏
       * 1. 不包含c 隐藏全部选装标题栏
       * 2. 只有c 隐藏推荐组合标题栏
       * 3. 只有一个栏目的时候就隐藏标题栏
       * 4. 特殊车型列表,只展示全部选装(隐藏推荐组合标题栏)
       */
      const composeDom = document.querySelector('.van-collapse-item__title')
      const includeC = this.currentModelLineData.typeFlag?.includes('C')
      const isAllVisible = this.composeOptionVisible && this.allOptionVisible
      if (!includeC || onlyC || STANDARD_CAR.includes(this.currentModelLineData.modelLineCode) || !isAllVisible) {
        if (composeDom) {
          this.$nextTick(() => {
            composeDom.style.display = 'none'
          })
        }
      }
    },

    // 从详情页返回做的操作
    doFromDetailAction() {
      const { optionCode } = this.$route.query
      if (optionCode) {
        const item = this.pageAllOptionList.find((item) => item.optionCode === optionCode)
        this.clickCheckBox(item)
      }
    },

    // 点击选装包
    async clickCheckBox(item) {
      console.log(item.optionCode, item.optionName, item.selected)

      // 标装无任何提示
      if (item.status === 1 || item.manualStatus === 1) {
        return
      }


      // 同级互斥的提示
      if (item.peerDisabled) {
        Toast('请重新选择')
        this.$store.commit('updateFooterDesc', {
          desc: '当前所选选选装包有冲突，请重新选择'
        })
        return
      }

      // 当前选装禁用提示
      if (item.disabled || item.initDisable) {
        Toast('您选择的装备需您先更换内饰')
        this.$store.commit('updateFooterDesc', {
          desc: '当前所选选装包与所选面料冲突，请重新选择'
        })
        return
      }

      // 当前的选装包是否为Q6红色卡钳(PC2)
      // 如果轮毂已经选中了带卡钳的状态下, 这里的pc2 为自动选中状态, 且无法取消
      if (this.currentSeriesName === 'q6' && item.optionCode === 'PC2') {
        if (this.currentHub.optionCode.includes('PC2') && item.selected) {
          Toast('您选择的装备需您先更换轮毂')
          this.$store.commit('updateFooterDesc', {
            desc: '当前所选选装包与所选轮毂冲突，请重新选择'
          })
          return
        }
      }

      // 当前点击的选装包是否为面料依赖
      const isSibDepend = this.paramSibDependOptionList.find((depend) => depend.optionRelateCode === item.optionCode)

      // 当前点击的选装包是否为饰板依赖
      const isEihDepend = this.paramEihDependsOptionList.eihDepends.find((depend) => depend.optionRelateCode === item.optionCode)

      if (!item.selected) {
        // 要选中的逻辑
        if (!isSibDepend) {
          // 选择非面料依赖的选装包时, 需要面料依赖的选装为选中状态
          const hasSibDependSelected = this.pageAllOptionList.find((option) => this.paramSibDependOptionList.find((depend) => depend.optionRelateCode === option.optionCode) && option.selected)
          if (!hasSibDependSelected && this.paramSibDependOptionList.length > 0) {
            return Toast('您选择的装备需您先更换内饰')
          }
        }
      } else {
        // 要取消的逻辑
        if (isSibDepend || isEihDepend) {
          return Toast('您选择的装备需您先更换内饰')
        }

        if (this.optionSingleList.includes(item.optionCode)) {
          item.selected = false
          return
        }
      }

      /**
       * 选装与标装的互斥
       */
      const massage = item.optionCode === '8I6' && item.status === 2 && !item.selected
      const ventilate = item.optionCode === '4D3' && item.status === 2 && !item.selected
      if (massage || ventilate) {
        this.optionDialogVisible = true
        return
      }
      item.selected = !item.selected

      // 同级选装的互斥
      this.setConflictOption(item)
      // 检查全部强制单选逻辑
      this.handleSingleOption(item)


      // 选中选装之后的操作
      if (item.selected) {
        // 全部选装和推荐组合互斥,如果当前选装被选中,需要清空推荐组合
        if (this.currentComposeName) {
          this.firstToastFromOptionComposes()
          this.$store.commit('updateCurrentComposeName', '')
        }
      }
    },

    // 单选逻辑:多选一,无法取消，优先级大于同级互斥
    handleSingleOption(option) {
      const clude = this.optionSingleList.includes(option.optionCode)
      if (!clude) {
        return
      }
      // console.log('面料单选list', this.optionSingleList)
      for (const item of this.pageAllOptionList) {
        const isInclude = this.optionSingleList.includes(item.optionCode)
        if (isInclude) {
          item.selected = false
          item.disabled = false

          // 取消的包也需要清除此包关联的同级互斥
          const conflicts = getFilterRelateList(item.optionRelates, 'conflict')
          for (const conflict of conflicts) {
            conflict.optionRelateCode
            const peerOption = this.pageAllOptionList.find((i) => i.optionCode === conflict.optionRelateCode)
            if (peerOption) {
              peerOption.peerDisabled = false
            }
          }
        }
      }
      option.selected = true
    },

    // 选中推荐组合
    toSelectOptionComposes(item) {
      if (item.disabled) {
        Toast('您选择的装备需要您先更换内饰')
        this.$store.commit('updateFooterDesc', {
          desc: '当前所选选装包与所选面料冲突，请重新选择'
        })
        return
      }

      /**
       * 兼容q6的红色卡钳(PC2)逻辑
       * 如果当前轮毂带红色卡钳,并且所选组合里无红色卡钳, 则弹窗提示
       */
      const isQ6 = this.currentSeriesName === 'q6'
      if (isQ6 && this.currentHub.optionCode.includes('PC2')) {
        const composePc2 = item.composePersonalOptions.find((i) => i.optionCode === 'PC2')
        if (!composePc2) {
          Toast('您选择的装备需要您先更换外饰')
          this.$store.commit('updateFooterDesc', {
            desc: '当前所选选装包与所选外饰冲突，请重新选择'
          })
        }
        return
      }

      this.$store.dispatch('clickOptionCompose', item)
    },

    // 当前选装包,同级互斥
    setConflictOption(option) {
      const conflicts = getFilterRelateList(option.optionRelates, 'conflict')
      // console.log('option 同级互斥:', conflicts.map((i) => i.optionRelateCode), option.selected)

      for (const optionItem of this.pageAllOptionList) {
        if (optionItem.status === 1 || optionItem.manualStatus === 1) {
          continue
        }
        const paramSibConflictOptionList = this.paramSibConflictOptionList.map((i) => i.optionRelateCode)
        for (const conflict of conflicts) {
          const isSibDisabled = paramSibConflictOptionList.includes(conflict.optionRelateCode)
          if (!isSibDisabled) {
            if (!optionItem.initDisable) {
              optionItem.disabled = false
            }
            if (optionItem.optionCode === conflict.optionRelateCode) {
              if (!optionItem.initDisable) {
                optionItem.disabled = option.selected
                // 手动添加同级互斥选项字段:peerDisabled
                optionItem.peerDisabled = option.selected
              }
              break
            }
          }

          // 如果 isSibDisabled 为 true，即当前项在禁用列表中，那么就跳过当前循环迭代
          // if (isSibDisabled) {
          //   continue
          // }

          // // 只有当 optionItem 不是初始禁用时，才进一步判断
          // if (!optionItem.initDisable) {
          //   // 如果 optionItem 的 optionCode 与当前冲突项的 optionRelateCode 相等
          //   if (optionItem.optionCode === conflict.optionRelateCode) {
          //     // 根据当前冲突项的 selected 属性来禁用或启用 optionItem
          //     optionItem.disabled = conflict.selected
          //     break // 找到匹配项后就不需要进一步查找
          //   } else {
          //     // 如果没有匹配，那么确保 optionItem 是启用的
          //     optionItem.disabled = false
          //   }
          // }

          // if (!isSibDisabled) {
          //   if (!optionItem.initDisable) {
          //     optionItem.disabled = false

          //     if (optionItem.optionCode === conflict.optionRelateCode) {
          //       optionItem.disabled = conflict.selected
          //       // 手动添加同级互斥选项字段:peerDisabled
          //       optionItem.peerDisabled = conflict.selected
          //       break
          //     }
          //   }
          // }
        }
      }
    },

    // 更新选装包数据
    async watchPersonalOptions(val) {
      // 更新已选中数据
      const res = val.filter((item) => item.selected && !item.disabled)
      this.$store.commit('updateSelectedOptions', res)

      /**
       * 计算价格/交付时间..
       */
      this.$store.commit('showLoading')
      await this.$store.dispatch('setTimeAndPrice')
      this.$store.commit('hideLoading')
    },

    // 跳转到下一页
    async nextPage() {
      this.clickOptionSensors('下一步')// 埋点

      this.$store.commit('showLoading')
      await this.$store.dispatch('setTimeAndPrice')
      this.$store.commit('hideLoading')

      const isSpecialCar = STANDARD_CAR.includes(this.currentModelLineData.modelLineCode)

      // 半定逻辑
      if (this.currentModelLineData.measure === 1) {
        if (this.currentComposeName === '' && !isSpecialCar) {
          Toast('请至少选择一个装备组合')
          return
        }
        return this.toNextPage()
      }

      // 检查当前carType是否匹配当前车型开放的ABC类
      const isTurnonC = this.currentModelLineData.typeFlag?.includes('C')

      if (!isSpecialCar) {
        if (!isTurnonC && this.currentComposeName === '') {
          Toast('请至少选择一个装备组合')
          return
        }
      }

      if (['B', 'C'].includes(this.currentCarType)) {
        const DESC = {
          B: '您所配置的车型预计交付时间为签订合同后3个月起，定金5千元，定金不可退',
          C: '您所配置的车型预计交付时间为签订合同后6个月起，定金5千元，定金不可退'
        }

        Dialog.confirm({
          title: '交付周期说明',
          message: DESC[this.currentCarType],
          className: 'customstyle',
          confirmButtonText: '我再想想',
          cancelButtonText: '确认选装'
        }).then(() => {
          // 我再想想
          this.clickOptionPopupSensors('我再想想', `${this.currentCarType}类交付时间弹窗`)// 埋点
        }).catch(() => {
          this.clickOptionPopupSensors('确认选装', `${this.currentCarType}类交付时间弹窗`)// 埋点
          // 确认选装
          this.toNextPage()
        })
      } else {
        this.toNextPage()
      }
    },

    // 跳转到报价单页面
    async toNextPage() {
      this.$store.commit('showLoading')
      const { orderStatus, action, orderId } = this.$route.query

      if (action === 'modelDetailModify') {
        // 进配置与权益页面
        this.$router.push({
          path: '/modelDetailMofidy',
          query: {
            orderId,
            idx: this.carIdx,
            ccid: this.ccid,
            orderStatus
          }
        })
        return
      }

      await Promise.all([
        this.$store.dispatch('getSkuId'), // this.skuid
        this.$store.dispatch('getCCid', {
          orderStatus
        })
      ])
      this.$store.commit('hideLoading')

      if (['30', '00'].includes(orderStatus)) {
        this.toMoneyDetailPage()
        return
      }

      if (env === 'minip' && !this.$storage.get('token')) {
        this.openWebViewMinip()
      } else {
        this.toQuotationPage()
      }
    },

    // 跳转到报价单页面
    async toQuotationPage() {
      const {
        orderStatus, orderId, dealerCode, shoppingCartId
      } = this.$route.query

      this.$router.push({
        path: '/quotation',
        query: {
          ccid: this.ccid,
          skuid: this.skuid,
          orderStatus,
          orderId,
          dealerCode,
          shoppingCartId,
          idx: this.carIdx,
          customBack: 'newConfigration'
        }
      })
    },

    // 跳转小程序页面
    async openWebViewMinip() {
      const {
        orderStatus, orderId, dealerCode, shoppingCartId
      } = this.$route.query
      const ccid = this.ccid
      const skuid = this.skuid
      const idx = this.carIdx
      const query = {
        ccid,
        skuid,
        orderStatus,
        orderId,
        dealerCode,
        shoppingCartId,
        idx,
        customBack: 'newConfigration'
      }
      const { origin, pathname } = window.location
      const string = Object.keys(query).reduce((i, n) => i + (query[n] ? (`&${n}=${query[n]}`) : ''), '')
      const strp = string.substring(1, string.length)
      const url = encodeURIComponent(`${origin}${pathname}#/quotation?${strp}`)
      wx.miniProgram.navigateTo({ url: `/pages/web/index?idx=${idx}&url=${url}&skuid=${skuid}&dealerCode=${dealerCode}` })
    },

    // 进入详情页
    toMoneyDetailPage() {
      const { orderId } = this.$route.query
      this.$router.push({
        path: '/order/money-detail',
        query: { orderId }
      })
    },

    // 跳转到推荐组合详情页面
    toCommpendListPage(item) {
      this.$router.push({
        path: '/commpendOptionList',
        query: {
          composeName: item.composeName,
          customBack: 'newConfigration'
        }
      })
      this.clickOptionSensors('查看详情')// 埋点
    },

    // 进入选装详情页面
    toOptionDetail(item) {
      this.$store.commit('updateCurrentOptionDetail', item)
      const query = item.disabled ? { disabled: item.disabled } : {}
      this.$router.push({
        path: '/configrationOptionDetail',
        query: {
          customBack: 'newConfigration',
          ...query
        }
      })

      this.clickOptionSensors('查看详情')// 埋点
    },

    // 首次点击全部选装并成功选中一个后,弹窗加底部文案更新
    firstToastFromOptionComposes() {
      if (firstToastTag) {
        const text = '签订合同后六个月内交付,定金五万,定金不可退'
        Toast(text)
        this.$store.commit('updateFooterDesc', { desc: text })
        firstToastTag = false
      }
    },

    // 埋点
    clickOptionSensors(buttonName) {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const tabMap = {
        exterior: '外观',
        interior: '内饰',
        option: '选装',
        equity: '权益'
      }
      const { engine, customSeriesName } = this.currentModelLineData

      let param = {
        source_module: 'H5',
        refer_tab_name: tabMap[this.referConfigrationActiveTab],
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        select_package: this.selectedOptions.map((i) => i.optionName).join(),
        package_type: this.currentComposeName ? '组合包' : '选装包',
        select_part: '',
        button_name: buttonName
      }

      if (buttonName === '下一步') {
        param = {
          ...param,
          $event_duration: Math.floor((Date.now() - this.pageStartTime) / 1000)
        }
      }

      console.log('CC_CarConfiguration_OptionalPackage_BtnClick:', param)
      this.$sensors.track('CC_CarConfiguration_OptionalPackage_BtnClick', param)
    },

    // 埋点
    clickOptionPopupSensors(buttonName, windowName) {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const { engine, customSeriesName } = this.currentModelLineData

      const param = {
        source_module: 'H5',
        window_name: windowName,
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        select_package: this.selectedOptions.map((i) => i.optionName).join(),
        package_type: this.currentComposeName ? '组合包' : '选装包',
        select_part: '',
        button_name: buttonName
      }

      // console.log(param)
      console.log('CC_CarConfiguration_OptionalPackage_PopupClick', windowName)
      this.$sensors.track('CC_CarConfiguration_OptionalPackage_PopupClick', param)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";

.collapse-wrapper {
  overflow-y: auto;
  // margin-top: 16px;
  height: calc(100vh - @HeaderHeight - @TabHeight - @FooterHeight);
  padding-bottom: @FooterHeight;
}

.margin-top {
  margin-top: 32px;
}

.card-wrapper {
  position: relative;
  border-radius: 4px;
  border: 1px solid #E5E5E5;
  padding: 14px 16px;
  box-sizing: border-box;
  margin-bottom: 16px;

  &.selected {
    border: 1px solid #000;
  }

  &.disabled {
    opacity: 0.5;
  }

  >.title-wrapper {
    color: #333;
    line-height: 22px;
    >.name {
      max-width: 65%;
    }
    >.price-text {
      font-size: 12px;
      color: #999;
      font-weight: normal;
    }

    .relative {
      position: relative;
      .discount {
        position: absolute;
        left: 103%;
        top: 50%;
        transform: translateY(-50%);
        background: #EB0D3F;
        color: #fff;
        padding: 0 5px;
        line-height: 16px;
        width: max-content;
      }
    }
    .summer-price {
      position: absolute;
      right: 16px;
      top: 40px;
      text-decoration:line-through;
      color: #ccc;
      font-size: 14px;
    }
  }

  >.content-wrapper {
    overflow: auto;
    height: 66px;
    margin-top: 6px;
    max-width: 75%;

    >div {
      line-height: 20px;
    }
  }

  >.btn-wrapper {
    position: relative;
    width: 100%;
    color: #000;
    display: flex;
    justify-content: space-between;

    >.depend-desc {
      position: absolute;
      bottom: 100%;
      left: 0;
      color: #8b8b8b;
    }
  }
  >.tag-wrapper {
    position: absolute;
    right: 0;
    bottom: 0;
    background:#EB0D3F;
    color: #fff;
    font-weight: normal;
    font-size: 12px;
    line-height: 20px;
    box-sizing: border-box;
    text-align: right;
    padding:0 12px 0 18px;
    border-radius: 0 0 3px 0;
    &::before{
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      background-color: #fff;
      border-top: 10px solid transparent;
      border-bottom: 10px solid #EB0D3F;
      border-left: 8px solid transparent;
      border-right: 8px solid #EB0D3F;
    }
  }
}

//覆盖vant组件样式
/deep/.collapse-wrapper {
  .van-collapse-item__title {
    font-size: 16px;
  }

  .van-hairline--top-bottom:after {
    border: none;
  }
}
</style>
