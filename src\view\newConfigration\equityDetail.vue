<template>
  <div class="container" @click="handleClick">
    <div v-html="currentEquityDetail">
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import { Icon } from 'vant'

Vue.use(Icon)

export default {
  data() {
    return {
    }
  },
  computed: {
    ...mapState({
      currentEquityDetail: (state) => state.configration.currentEquityDetail
    })
  },
  mounted() {
    // this.initSwiper()
    this.initScroll()
  },
  // 退出页面时销毁swiper
  beforeDestroy() {
  },

  methods: {
    initScroll() {
      const container = document.querySelector('.container')
      if (container) {
        container.scrollTop = 0
      }
    },
    handleClick(event) {
      if (event.target.matches('.van-icon')) {
        this.toBack()
      }
    },
    toBack() {
      console.log('back')
      this.$router.back()
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  height: 100%;
  background-color: #f9fafb;
  padding: 0 18px;
  padding-bottom: 30px;
  box-sizing: border-box;
  overflow: auto;
}

.swiper-slide {
  box-sizing: border-box;
  background: #fff;

  top: 50%;
  transform: translateY(-50%);
  height: 80vh;
  transition: height 0.1s;

  &.swiper-slide-active {
    height: 85vh;
  }
}


.content {
  padding: 12px;
  overflow: auto;
  height: 100%;
  box-sizing: border-box;
}

.title {
  text-align: center;
  font-size: 16px;
  font-family: "Audi-WideBold";
  line-height: 24px;

  &.small {
    font-size: 14px;
    margin-top: 8px;
    line-height: 22px;
  }
}
.detail {
  font-size: 12px;
  color: #000;
  opacity: 0.6;
}
.van-icon {
  position: absolute;
  top: 5px;
  right: 12px;
  font-size: 18px;
  padding: 6px;
}
.swiper-pagination {
  bottom: 30px;
}
/deep/.swiper-pagination-bullet {
  height: 5px;
  width: 12px;
  border-radius: 0;
  background-color: #939393;
  margin: 0 2px;

  &.swiper-pagination-bullet-active {
    background-color: #fff;
  }
}
</style>
