/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-08-10 14:22:32
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-06 10:10:12
 * @FilePath     : \src\config\conf.data.js
 * @Descripttion :
 */

export const HUI_XING = Object.freeze({
  HW: 100,
  WW: 500000
})

export const MANAGER_ON_OFF = {
  LONG_TEST_DRIVE: false
}

export const CONF_LINKS = {
  OPEN_APP: 'https://app.saic-audi.mobi/index.html#/?channel='
}

export const CONF_SKIN = {
  DEFAULT: '#000000',
  ESSENTIAL: '#F50537'
}

export const ENGINE_TYPE = {
  FUEL_OIL: ['G6', '49'],
  BEVS: ['G4'],
  HYBRID: []
}

export const EXCEPTION = {
  CONSOLES_PAGE: [
    {
      title: '扫描失败',
      template: 'charging-pile-qrcode',
      error: 'qrcode'
    }
  ]
}
export const ORDER_STATUS_DISTRICT = {
  EBSCO: ['41', '42'],
  DIES: ['98', '99'],
  REFUND: ['80', '84'],
  REFUNDED: ['81', '99'],
  AGREE_HIGH: ['00', '31', '32'],
  XIAO_DING: ['00', '30'],
  DA_DING: ['31', '32', '90'],
  DA_DING_BEFORE: ['00', '30', '301'],
  DA_DING_AFTER: ['30', '31', '32', '90'],
  LIFE_MIN_CYCLE: [
    {
      status: '30',
      name: '01',
      text: '已支付意向金'
    },
    {
      status: '31',
      name: '02',
      text: '已支付定金'
    },
    {
      status: '41',
      name: '03',
      text: '已签署购车协议'
    },
    {
      status: '42',
      name: '04',
      text: '车辆已验收'
    },
    {
      status: '32',
      name: '05',
      text: '已支付尾款'
    },
    {
      status: '90',
      name: '06',
      text: '车辆已交付'
    }
  ],
  NEW_LIFE_MIN_CYCLE: [
    {
      status: '31',
      name: '01',
      text: '支付定金'
    },
    {
      status: '41',
      name: '02',
      text: '签署购车协议'
    },
    {
      status: '90',
      name: '03',
      text: '车辆交付'
    }
  ],
  LIFE_MAX_CYCLE: [
    {
      status: '00',
      text: '待支付意向金',
      desc: '未支付的意向金订单将在COUNTDOWN_TIME内自动关闭，如有疑问请联系客服或代理商。'
    },
    {
      status: '30',
      text: '已支付意向金，待支付定金',
      desc: [
        '预售已开启，早支付定金，享优先交付。如需信贷，前往下方“金融方案”。', // 油车
        '代理商将按当地政策为您开启定金支付。如需信贷，前往下方“金融方案”。' // 新能源车
      ]
    },
    {
      status: '31',
      text: '已支付定金，待签署合同',
      desc: '您已成功支付定金，请您与奥迪管家确认订单信息后，开启合同签署。完成合同签署后将为您匹配车辆资源。'
    },
    {
      status: '41',
      text: '已签署购车协议',
      desc: [
        '资源匹配中，车辆到达展厅后将邀您验收并支付尾款。', // 全款
        '资源匹配中，车辆到达展厅后将邀您验收并支付尾款。请签署贷款合同。' // 贷款
      ]
    },
    {
      status: '101',
      text: '已进入资源准备流程',
      desc: [
        '资源已匹配，按顺序进入资源准备流程。', // 全款
        '资源已匹配，按顺序进入资源准备流程。若尚未签署贷款合同，请尽快完成。' // 贷款
      ]
    },
    {
      status: '102',
      text: '车辆已到店，待验车',
      desc: '您的车辆已发运到店。待检查完毕，您的专属奥迪管家会与您沟通验车事宜。'
    },
    {
      status: '103',
      text: '车辆已验收，待支付尾款',
      desc: '您已在上汽奥迪APP上确认验车，请及时依选定方式支付尾款。'
    },
    {
      status: '104',
      text: '已上传支付凭证，确认中',
      desc: '您已在上汽奥迪APP上传尾款支付凭证，待厂家确认支付结果。'
    },
    {
      status: '105',
      text: '上传支付凭证审核未通过',
      desc: '您上传的尾款支付凭证未通过审核，请重新上传或联系您的专属奥迪管家。'
    },
    {
      status: '301',
      text: '已支付意向金，待支付定金。', // 在订单状态中默认显示30状态的文本
      desc: '您已成功支付尾款，您的专属奥迪管家会与您沟通交车事宜。'
    },
    {
      status: '30O',
      text: '待支付定金',
      desc: '未支付定金的订单将在1小时内自动关闭，您可享受的购车权益以您支付定金的时间为准，如有疑问请联系客服或代理商'
    },
    // 32，32-106 理论上同一状态
    {
      status: '32',
      text: '待交车',
      desc: '您已成功支付尾款，您的专属奥迪管家会与您沟通交车事宜。'
    },
    {
      status: '106',
      text: '已支付尾款，待交车',
      desc: '您已成功支付尾款，您的专属奥迪管家会与您沟通交车事宜。'
    },
    {
      status: '80',
      text: '退款中',
      desc: '厂家已受理您的退款申请，退款通常将原路退回，请耐心等待。'
    },
    {
      status: '81',
      text: '退款已完成',
      desc: '您的订单已退款成功'
    },
    {
      status: '84',
      text: '大定退款中',
      desc: '厂家已受理您的退款申请，退款通常将原路退回，请耐心等待'
    },
    {
      status: '90',
      text: '已完成',
      desc: '您已完成该订单，祝您用车愉快'
    },
    {
      status: '97IN',
      text: '有更新',
      desc: [
        '您所选择的配置价格有更新，请确认是否更新。',
        '由于您所选择的配置价格有更新，当前订单已失效，您需要退订后重新下单。' // 贷款
      ]
    },
    {
      status: '97UP',
      text: '已失效',
      desc: [
        '失效配置请退订，重新下单',
        '由于您所选择的车型已下架，当前订单已失效，您需要退订后选择其他车型重新下单。', // 车型下架
        '由于您所选择的选装包已下架，当前订单已失效，您需要退订后选择其他配置重新下单。', // 选装下架且已申请贷款
        '由于您所选择的选装包已下架，请您重新选择车辆配置。'// 选装下架且未申请贷款
      ]
    },
    {
      status: '98',
      text: '已关闭',
      desc: '您的订单超时未支付，已自动关闭'
    },
    {
      status: '99',
      text: '退款已完成',
      desc: '您的订单已退款成功。'
    },
    {
      status: '999', // 定金拗断
      text: '订单已关闭',
      desc: '您的订单已关闭成功。'
    }
  ]
}

export const ORDER_AGREEMENT = [
  {
    status: '00',
    sign: '00',
    name: 'user-agreement',
    title: '《意向金协议》'
  },
  {
    status: '31',
    sign: '31',
    name: 'contract-info',
    title: '《汽车购买协议》'
  },
  {
    status: '32',
    sign: '32',
    name: 'delivery-contract',
    title: '《交车确认书》'
  }
]

// 因为A7L限量号不同于Q5/6，故区分
export const LIMIT_NUMBERS_A7L = {
  nickCode: 'a7l',
  seriesCode: '49',
  modelBadgeName: 'A7L 先行 edition one',
  modelShortName: 'A7L 先行 edition one',
  modelNanoName: 'A7L e-tron',
  modelLineCode: '498B2Y005'
}

export const LIMIT_NUMBERS = [
  {
    nickCode: 'q5',
    seriesCode: 'G4',
    integral: 55,
    increase: 100,
    burningTime: {
      text: '',
      seconds: 60 * 30
    },
    modelBadgeName: 'Q5 e-tron艺创典藏版',
    modelShortName: 'Q5 e-tron edition one',
    modelNanoName: 'Q5 e-tron',
    modelLineCode: 'G4ICF3002'
  },
  {
    nickCode: 'q6',
    seriesCode: 'G6',
    integral: 66,
    increase: 100,
    burningTime: {
      text: '30分钟',
      seconds: 60 * 30
    },
    modelBadgeName: 'Q6 50 TFSI quattro Roadjet 凌云版',
    modelShortName: 'Q6 edition one',
    modelNanoName: 'Q6',
    modelLineCode: 'G6ICBY002'
  }
]

export const AUTOMOBILE = [
  {
    nickCode: 'q5',
    seriesCode: 'G4',
    seriesPathName: 'Q5E',
    seriesNanoName: 'Q5 e-tron'
  },
  {
    nickCode: 'q6',
    seriesCode: 'G6',
    seriesPathName: 'Q6',
    seriesNanoName: 'Q6'
  },
  {
    nickCode: 'a7',
    seriesCode: '49',
    seriesPathName: 'A7L',
    seriesNanoName: 'A7L'
  }
]

// 超时
export const TIMEOUT_MS = {
  LOCATION: 5000
}

export const CCPRO_OPTIONS = {
  OPTIONS: {
    RAD: '轮毂',
    VOS: '座椅',
    EIH: '饰条',
    SIB: '面料'
  },

  insideColor: '内饰',
  outsideColor: '外饰',
  INSIDE: ['VOS', 'EIH', 'SIB'],
  OUTSIDE: ['RAD']
}

export const CCPRO_RIGHTS_PACKAGE = [
  {
    optionName: '新生权益',
    optionCode: 'YEG'
  },
  {
    optionName: '轻装权益包',
    optionCode: 'YEA'
  },
  {
    optionName: '重装权益包',
    optionCode: 'CCPRO-YEB'
  },
  {
    optionName: '尊享权益',
    optionCode: 'CCPRO-YEB'
  }
]


export const RES_SUCCEED_CODE = ['00', '200']

export const FILE_MIME_HEX = {
  25504446: 'pdf',
  '504b34': 'zip'
}

export const Q5E_LITE_MODEL_CODE = ['G4IBC3-GWTZWTZ', 'G4ICC3-GWTZWTZ']


//  // 权益类型 1-充电桩 2-奥金 3-挂靠+奥金
//  if (data.data === 1) {
//   this.chargingPileButtonText = '充电桩'
// } else if (data.data === 2) {
//   this.chargingPileButtonText = '奥金'
// } else if (data.data === 3) {
//   this.chargingPileButtonText = '挂靠+奥金'
// } else {
//   this.chargingPileButtonText = '未选择'
// }


export const CHARGING_PILES_RIGHTS = {
  '0001': '订单已提交，等待处理中',
  '0002': '已取消',
  '0003': '待确认电力准备情况',
  '0004': '已评价',
  '0005': '积分已发送',
  2100: '待确认电力准备情况',
  1000: '订单待提交',
  1001: '订单待审核',
  1002: '待分配安装专属顾问',
  1003: '待确认电力准备情况',
  1101: '待确认电力准备情况',
  1102: '不满足充电桩安装条件', // 电力准备失败
  1103: '待确认电力准备情况',
  1201: '待勘测',
  1202: '待勘测',
  1203: '不满足充电桩安装条件', // 勘测失败
  1301: '待安装',
  1302: '待安装',
  1303: '安装失败',
  1401: '服务已完成，待评价',
  1402: '服务已完成，待评价',
  1501: '服务已完成，待评价',
  1800: '服务已完成，待评价',
  2000: '已取消订单',
  '002600001': '待分配VIP专属服务顾问',
  '002600002': '待确认电力准备情况',
  '002600003': '待勘测',
  '002600004': '待安装',
  '002600005': '服务已完成，待评价',
  '002600006': '不满足充电桩安装条件',
  '002600007': '不满足充电桩安装条件',
  '002600008': '服务已完成，待评价',
  '002600009': '服务已完成，待评价',
  '002600010': '已分配专属顾问，待填写安装信息',
  '002600011': '已分配专属顾问，待填写安装信息'

}


export const CHARGING_STATUS_TEXT = {
  1: '充电桩',
  2: '奥金',
  3: '挂靠+奥金',
  400: CHARGING_PILES_RIGHTS[1203],
  5: '充电桩+3600元充电卡',
  6: '7200元充电卡',
  7: `${HUI_XING.WW}奥金+3600元充电卡`
  // 20: '充电桩',
  // 21: '充电桩+3600充电卡'
}

export const TESTDRIVE_STATUS = [
  {
    status: [90210],
    text: '待开始',
    desc: '感谢您预约本次试驾体验，上汽奥迪恭候您的到来'
  },
  {
    status: [90211],
    text: '待支付',
    desc: ''
  },
  {
    status: [90220],
    text: '进行中',
    desc: '试驾体验已开始，如有任何疑问，请咨询您身边的体验专家'
  },
  {
    status: [90230],
    text: '已完成',
    desc: '感谢您体验上汽奥迪汽车，我们期待聆听您的声音'
  },
  {
    status: [90240, 90241],
    text: '已取消',
    desc: '本次试驾体验已取消，上汽奥迪期待与您相见'
  }
]


export const SERIES_POWER_KW = [
  'Audi Q6 40 TFSI quattro Roadjet',
  'Audi Q6 45 TFSI quattro Roadjet',
  'Audi Q6 50 TFSI quattro Roadjet',
  'Audi Q5 e-tron edition one',
  'Audi Q5 e-tron 50 quattro Roadjet',
  'Audi Q5 e-tron 40 Roadjet',
  // '奥迪A7L',
  '奥迪A7L 45TFSI quattro',
  '奥迪A7L 55TFSI quattro'
]

export const WEICHAT_MINIP = {
  PROGRAMID: 'gh_95ecbbd143f0',
  APPID: 'wxb097d03856d768c9',
  APPKEY: 'AUDIMINIAPP'
}
