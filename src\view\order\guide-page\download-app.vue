<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-07-28 15:03:35
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-11-30 10:59:03
 * @FilePath     : \src\view\order\guide-page\download-app.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['guide-page-download-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      background-color="transparent"
      header-left-icon-color="white"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      :listening-emit-back="true"
      @handleLeftBack="handleLeftBackAction"
    />
    <div
        class="main-wrapper"
        :style="{
          'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
        }"
      >
      <div
        class="download-common-wrapper"
      >
        <div
          class="series-img-box"
        >
          <div
            class="series-box"
            data-flex="main:center cross:center"
          >
            <img
              :class="['series', !(seriesImgString === 'A7L') || 'a7l']"
              :src="seriesImgURL"
            >
          </div>
          <div class="logo-title">
            audi
          </div>
          <div class="description">
            <div class="list">感谢您对于上汽奥迪品牌车型的信任与支持，欢迎加入上奥汇大家庭。</div>
            <div class="list">为保障车辆交付及使用的顺利，需要您下载并登录上汽奥迪APP，完成下方购车流程。</div>
            <div class="list line" data-flex="main:left">
              <p class="p" data-flex="dir:top main:center"><span class="text"><font>申请信贷服务</font></span><span class="num"><img src="~@/assets/pay/s1.png"/></span></p>
              <p class="p" data-flex="dir:top main:center"><span class="text"><font>支付尾款</font></span><span class="num"><img src="~@/assets/pay/s2.png"/></span></p>
              <p class="p" data-flex="dir:top main:center"><span class="text"><font>交付车辆</font></span><span class="num"><img src="~@/assets/pay/s3.png"/></span></p>
            </div>
            <div class="list">请点击下方前往上汽奥迪APP，登录后您亦将获得更多专属服务资讯、掌握更多社区动态。</div>
            <div class="list">如有任何疑问请随时联系您的专属奥迪管家。</div>
            <div class="list">期待与您一起，圈住美好，圈住爱。</div>
          </div>
        </div>
        <div
          class="affirm-order-info-box"
          data-flex="main:justify"
        >
          <div class="lan-button-box white-button ghost-button line-two-cols">
            <van-button
              class="lan-button"
              @click="handleGoToOrderDetailsPage"
            >
              查看订单详情
            </van-button>
          </div>
          <div
            class="lan-button-box white-button white-border line-two-cols"
          >
            <van-button
              @click="handleOpenApp"
              class="lan-button"
            >
            前往上汽奥迪APP
            </van-button>
          </div>
        </div>
      </div>
    </div>
    <model
      title="在APP内打开"
      content="点击下方复制链接，前往浏览器打开"
      confirm-text="复制链接"
      @onConfirm="copyClipboard"
      :modalshow.sync="modalshow"
    />
  </div>
</template>

<script>
import HeaderCustom from '@/components/header-custom.vue'
// import { getMyOrders, getCarConfig } from '@/api/api'
import Vue from 'vue'
import {
  Button, Toast
} from 'vant'
import { RES_SUCCEED_CODE } from '@/config/conf.data'
import { getCarConfig } from '@/api/api'
import api from '@/config/url'
import store from '@/store/index'
import { isIos } from '@/utils'
import model from '@/components/model.vue'

const AUTOMOBILE = {
  49: 'A7L',
  G4: 'Q5E',
  G6: 'Q6'
}

Vue.use(Button).use(Toast)


export default {
  components: {
    'header-custom': HeaderCustom, model
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      orderId: '',
      seriesImgURL: '',
      seriesImgString: 'A7L',
      limitedNumberA7L: '',
      modalshow: false
    }
  },
  created() {
    store.commit('setTitle', '')
    const {
      $route: {
        query: {
          orderId, ccid
        }, query
      }
    } = this
    this.orderId = orderId

    console.log('%c [ guide-page-download query ]-61', 'font-size:14px; background:#cf222e; color:#fff;', this.$route.query)

    this.handleProcessData(orderId, ccid)
  },
  methods: {
    handleLeftBackAction() {
      const { orderId, $router } = this
      $router.push({ name: 'money-detail', query: { orderId } })
    },
    async handleProcessData(orderId, ccid) {
      if (ccid) {
        const { data: { data, code } } = await getCarConfig({ ccid })
        if (code && RES_SUCCEED_CODE.includes(code)) {
          const {
            configDetail: {
              carModel: {
                modelCode, modelNameCn, modelLineCode, modelPrice, headImageUrl
              }
            }
          } = data || {}

          if (headImageUrl) {
            this.seriesImgURL = api.BaseOssHost + headImageUrl
          }
        }
      }
    },
    handleOpenApp() {
      this.modalshow = true
      // const { origin, pathname } = window.location
      // const { orderId } = this.$route.query
      // const routeStr = isIos ? 'order' : 'orderDetail'
      // const url = `https://app.saic-audi.mobi/scvw/#/?scaudi://mall/${routeStr}?url=${origin}${pathname}#/order/money-detail?orderId=${orderId}`
      // location.href = url
    },
    handleGoToOrderDetailsPage() {
      const { $route: { query: { orderId,orderType } } } = this
      this.$router.push({
        name: 'model-detail',
        query: {
          orderId,
          orderType
        }
      })
    },
    copyClipboard() {
      // 复制到粘贴板
      const copyResult = this.InsertShearPlate()
      this.modalshow = false
      if (copyResult) {
        Toast({
          message: '已复制到剪贴板',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      } else {
        Toast({
          message: '复制失败',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      }
    },
    InsertShearPlate() {
      // 复制到粘贴板
      const text = 'https://app.saic-audi.mobi/index.html#/?channel=miniapp_VehiclePurchase'
      const copyString = text
      // 创建input标签存放需要复制的文字
      const myInput = document.createElement('input')
      // 把文字放进input中，供复制
      myInput.value = copyString
      document.body.appendChild(myInput)
      // 选中创建的input
      myInput.select()
      // 执行复制方法， 该方法返回bool类型的结果，告诉我们是否复制成功
      const copyResult = document.execCommand('copy')
      // 操作中完成后 从Dom中删除创建的input
      document.body.removeChild(myInput)
      return copyResult
    }
  }

}
</script>

<style lang="less" scoped>

.page-wrapper {
  &.guide-page-download-wrapper {
    padding: 16px 0;
    height: 100%;
    box-sizing: border-box;
    background-color: #404259;
    .header-wrapper {
      .header-box {
        color: #fff;
      }
    }
  }
}
// .download-common-wrapper {
//   width: 100%;
//   height: 100%;
// }
.main-wrapper {
  // min-height: 100vh;
  box-sizing: border-box; /*494c64*/
  background: url(~@/assets/pay/pay-status-succeed.-bg.png) no-repeat 50% 0 / cover;
  .series-img-box {
    position: relative;
    z-index: 1;
    width: calc(100% - 56px);
    padding: 10px 12px;
    margin: 0 auto;

    box-shadow: 3px 5px 10px rgba(0, 0, 0, .1), -3px -5px 10px rgba(0, 0, 0, .1);
    &::before {
      position: absolute;
      z-index: 0;
      content: '';
      background-color: #fcfcfc;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    }
    &::after {
      position: absolute;
      z-index: 0;
      content: '';
      border: solid 1px #ebd6c3;
      top: 10px;
      left: 12px;
      bottom: 10px;
      right: 12px;
    }
    .logo-title {
      position: relative;
      z-index: 101;
      text-indent: -999em;
      width: 180px;
      height: 55px;
      margin: 12px auto 0;
      background: url(~@/assets/pay/pay-status-succeed-title.png) no-repeat 50% 0 / cover;
    }
    .series-box {
      padding-top: 38px;
      width: 220px;
      height: 148px;
      margin: 0 auto;
      overflow: hidden;
      img {
        width: 180%;
        object-fit: center;
      }
    }
    .a7l {
      position: relative;
      top: -15px;
    }


    .description {
      position: relative;
      z-index: 99;
      padding: 12px 12px 42px 12px;
      .list {
        margin-top: 12px;
        font-size: 14px;
        color: #333;
        line-height: 22px;
        &.line {
          width: 100%;
          .p {
            width: 35%;
            text-align: center;
            margin: 0 0 10px;
            position: relative;
            z-index: 6;
            &:first-child {
              width: 45%;
            }
            &:last-child {
              width: 30%;
              &::after {
                display: none;
              }
            }
            &::after {
              content: '';
              position: absolute;
              left: 0;
              bottom: 7px;
              width: 100%;
              z-index: 7;
              border-top: 1px solid;
              height: 1px;
              border-image: linear-gradient(165deg, rgba(204, 152, 105, 1), rgba(225, 181, 128, 1)) 1 1;
            }
            .text {
              position: absolute;
              z-index: 15;
              left: 16px;
              background-color: #fff;
              padding: 0 3px 0 4px;
              font {
                font-size: 11px;
                color: #000000;
                line-height: 18px;
                background: linear-gradient(348deg, #B57950 0%, #E3B782 14%, #C38C5F 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
              }
            }
            .num {
              // margin: auto;
              width: 16px;
              height: 18px;
              position: relative;
              z-index: 11;
              img {
                width: 100%;
                vertical-align: top
              }
            }
          }
        }
      }
    }

  }
  .affirm-order-info-box {
    padding: 16px;
  }
}
</style>
