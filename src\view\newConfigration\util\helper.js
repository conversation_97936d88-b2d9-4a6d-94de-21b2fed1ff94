/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-24 17:18:35
 * @Last Modified by: z<PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-06-08 22:27:57
 */
import { isEmptyObj } from '@/utils'


// 获取optionids
export function getOptionId(state) {
  const modelLineId = state.currentModelLineData.modelLineId
  const outcolorId = state.currentExterior.optionId
  const hubId = state.currentHub.optionId
  const eihId = state.currentEih.optionId
  const sibId1 = state.currentSib.sibOptionId
  const sibId2 = state.currentSib.interieurOptionId
  const packetIds = state.selectedOptions.map((i) => i.optionId)
  const ids = [outcolorId, hubId, eihId, sibId1, sibId2, ...packetIds].filter((i) => i)
  const optionIds = Array.from(new Set(ids))
  return optionIds
}

// log warn
export function logWarn(arr, msg) {
  if (arr.length === 0) {
    console.warn(`${msg} 为空`)
  }
}

// 获取默认值
export const getDefaultSelectd = (state, key, arr) => {
  if (!isEmptyObj(state[key])) {
    return state[key]
  }
  const avaliable = arr.filter((i) => !i.disabled)
  const res = avaliable.find((i) => i.status === 1)
  return res || avaliable[0]
}


// 为交互数据包装selected、disabled属性
export const wrapArray = (arr) => arr.map((item) => ({
  ...item,
  selected: false,
  disabled: false
}))

/**
 *
 * @param {*} relateList 当前配置项的 relateList 数据
 * @param {*} relateType conflict | depend
 * @returns
 */
export const getFilterRelateList = (relateList, relateType) => {
  if (!Array.isArray(relateList)) {
    return []
  }

  return relateList.filter((item) => item.relateType === relateType)
}

/**
 * 获取当前环境高定还是半定
 * 1: 高定
 * 2: 半定制
 */
export const getCarType = () => 1 // 暂时只有高定

/**
 * 毕竟两个数组内容是否一致
 * @param {*} arr1
 * @param {*} arr2
 * @returns
 */
export function arraysEqual(arr1, arr2) {
  // 检查长度
  if (arr1.length !== arr2.length) return false

  // 深拷贝并排序数组
  const sortedArr1 = [...arr1].sort()
  const sortedArr2 = [...arr2].sort()

  // 逐元素比较
  for (let i = 0; i < sortedArr1.length; i++) {
    if (sortedArr1[i] !== sortedArr2[i]) {
      return false
    }
  }

  // 所有元素都相等
  return true
}
