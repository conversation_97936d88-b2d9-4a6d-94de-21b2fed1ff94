.order-forms-box, .lan-cell-box {
  .van-cell-group {
    border-top: 8px solid #f2f2f2;
    // padding-bottom: 26px;
    // padding-bottom: 24px;
    // &:first-child {
    //   border-top-width: 0;
    // }
    &.van-hairline--top-bottom {
      &::after {
        display: none;
      }
    }
    .van-hairline--bottom:after, .van-cell::after {
      border-color: #e5e5e5;
    }
    .cell-title {
      box-sizing: content-box;
      margin: 0 16px;
      padding: 16px 0;
      .h2 {
        margin: 0;
        font-size: 16px;
        line-height: 24px;
        font-family: AudiTypeGB-WideBold, AudiTypeGB;
        &.car-owner {
          margin-top: 26px;
        }
        &.mtb {
          margin-top: 8px;
        }
      }
      .sub {
        margin: 0;
        font-size: 10px;
        line-height: 20px;
        color: rgba(#000, .4);
      }
    }
    .van-cell {
      padding: 20px 16px 16px;
      min-height: 24px;
      font-size: 16px;
      &.van-field--error {
        padding-bottom: 36px;
        &::after {
          bottom: 20px;
        }
      }
      .van-cell__title {
        flex:none;
        margin-right: 20px;
        color: #000;
      }
      .van-field__control {
        height: 24px;
        line-height: 24px;
        &:disabled {
          color: #000;
          -webkit-text-fill-color:#000
        }
      }
      .van-radio-group {
        .van-radio {
          margin-right: 24px;
        }
      }
      &:last-child::after {
        display: block;
      }
      &.van-field--error {
        .van-field__control {
          color: #333;
          &::placeholder {
            color: #ccc;
          }
        }
        .van-field__error-message {
          z-index: 19;
          position: absolute;
          bottom: -38px;
          margin-left: -90px;
          color: #EB0D3F;
          &::before {
            content: "";
            position: relative;
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url("~@/assets/img/error-icon.png") no-repeat 0 0;
            background-size: 14px 14px;
            margin-right: 4px;
            top: 2px;
          }
        }
        &::after { border-color: #EB0D3F;}
      }
      &.lan-cell-switch {
        .van-cell__value {
          overflow: visible;
        }
        .lan-switch {
          position: absolute;
          top: -3px;
          right: 0;
          .van-switch__node {
            width: .86em;
            height: .86em;
            margin: .07em;
          }
          &::before {
            content: '';
            position: absolute;
            right: 11px;
            top: 50%;
            width: 4px;
            height: 4px;
            border: solid 2px #8b8b8b;
            border-radius: 50%;
            transform: translateY(-50%);
          }
          &.van-switch--on {
            &::before {
              right: auto;
              left: 15px;
              width: 1px;
              height: 8px;
              border: none;
              border-radius: 0;
              border-left: solid 2px #fff;
            }
          }
        }
      }

    }
  }
  .financial-plans-box {
    padding: 12px 16px 0;

    .van-cell {
      padding: 12px;
      font-size: 12px;
      color: #666;
      background-color: #f5f5f5;
      .van-cell__value, .van-cell__right-icon {
        color: #000;
      }
    }
  }
  .agreement-box {
    padding: 36px 16px 22px;
    font-size: 12px;
    .van-checkbox__label {
      color: #999;
    }
    .font {
      color: #000;
    }
  }
}
.cell-box {
  padding: 10px 16px;
  .cell-list {
    padding: 6px 0;
    font-size: 14px;
    line-height: 20px;
    .title {
      color: rgba(#000, .4);
    }
    .value {
      max-width: calc(100% - 100px);
    }
  }
}
.lan-list-box {
  .cell-box {
    .cell-list {
      padding: 0;
      font-size: 12px;
      .title {
        color: rgba(#000, .8);
        .arrow  {
          width: 11px;
          height: 11px;
          margin-left: 2px;
        }
      }
    }
  }
}
.order-forms-box {
  .lan-cell-group {
    &.van-cell-group:not(.lan-list-box) {
      padding-bottom: 8px;
    }
  }
}