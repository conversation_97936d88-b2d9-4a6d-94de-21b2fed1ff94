<template>
    <div id="detail">
      <iframe :src="url"></iframe>
    </div>
  </template>

  <script>

  import { getNewConsentByType } from '@/api/api'

  export default {
    name: 'userPolicy',
    data() {
      return {
        url : ""
      }
    },
    async mounted() {

    },
    created() {
        this.$store.state.title  = this.$route.query?.title
        if (this.$route.query?.title === '隐私条款') {
          console.info('🚀 ~ file:user-policy method:created line:24 -----', '隐私政策')
          // 隐私政策
          // const {Did} = await callNative('getUserInfo', {})
          getNewConsentByType({}, {
            'Content-Type': 'application/json',
            'X-Channel': 1000,
            'Did': parseInt(10000*Math.random())
          }).then(res => {
            console.info('🚀 ~ file:rich-text method: line:50 -----', res)
            this.url = res.data?.data?.content;
          })
        } else {
          this.url = this.$route.query?.url
        }
    },
  }
  </script>
  <style lang="less" scoped>

    #detail {
        width: 100;

        iframe {
            width: 100%;
            border: none;
            height: 100vh;
        }
    }
  </style>
