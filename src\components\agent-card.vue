<template>
  <div name="agent">
    <div class="_card">
      <div class="_logo">
        <img :src="$loadWebpImage(agentCardData.imageUrl.includes('http') ? agentCardData.imageUrl : BaseOssHost + agentCardData.imageUrl)">
      </div>
      <div class="_content">
        <div class="_top">
          <div class="_title text-hide">
            {{ agentCardData.dealerName }}
          </div>
          <div class="_address">
            <div class="text-hide">
              地址：{{ agentCardData.dealerAdrress }}
            </div>
            <div class="paddding-left">
              距您{{ distance }}
            </div>
          </div>
        </div>
        <!-- <div class="_toDetail">
          查看详情
          <van-icon
            name="arrow"
            color=""
            size=""
          />
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import api from '../config/url'
import { getdiscount2 } from '../utils'

export default {
  props: {
    // eslint-disable-next-line vue/require-default-prop
    agentCardData: Object,
    // eslint-disable-next-line vue/require-default-prop
    location: String
  },
  data() {
    return {
      BaseOssHost: api.BaseOssHost,
      distance: '0km'
    }
  },
  watch: {
    location(val) {
      if (this.agentCardData.distance) {
        this.distance = this.agentCardData.distance
      } else {
        const longitude = this.agentCardData.longitude
        const latitude = this.agentCardData.latitude
        const lo = val.split(',')
        if (longitude && latitude) {
          getdiscount2(longitude, latitude, lo[1], lo[0]).then((dis) => { this.distance = `${(dis / 1000).toFixed(1)}km` })
        } else {
          this.distance = '0km'
        }
      }
    }
  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style lang='less' scoped>
    .div {
        box-sizing: border-box;
    }

    ._card {
        width: 100%;
        background: #FFFFFF;
        box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
        display: flex;

        ._logo {
            width: 110px;
            height: 110px;

            img {
                width: 110px;
                height: 110px;
            }
        }

        ._content {
            width: calc(100% - 110px);
            height: 110px;
            padding: 8px;
            display: flex;
            flex-flow: column;
            box-sizing: border-box;
            justify-content: space-between;
            overflow: hidden;

            ._top {
                ._title {
                    width: 100%;
                    font-size: 16px;
                    font-family: Audi-WideBold;
                    color: #000000;
                    line-height: 21px;
                }

                ._address {
                    width: 100%;
                    margin-top: 6px;
                    font-size: 12px;
                    color: #333333;
                    overflow: hidden;
                    line-height: 16px;

                    .paddding-left {
                        // padding-left: 38px;
                        margin-top: 4px;
                    }
                }
            }

            ._toDetail {
                display: flex;
                width: 100%;
                align-items: center;
                font-size: 12px;
                color: #333333;
                line-height: 20px;
            }

        }
    }

    .text-hide {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
