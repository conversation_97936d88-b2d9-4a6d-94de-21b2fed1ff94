<!-- 试驾预约列表 -->
<template>
  <div :class="['container', { fromPurple: fromType }]">
    <div class="appointment-box" v-if="list.length">
      <div class="list" @click="toDriveDetail(item)" v-for="item of list" :key="item.id">
        <div class="list-head" data-flex="main:right" >
          <span class="status">{{ item.state.text }}</span>
        </div>
        <div class="list-main" data-flex="main:left">
          <div class="media">
            <img :src="`http://audi-oss.saic-audi.mobi/audicc/app/order/appointment/series/${item.path}/default-front-view.png` | audiwebp">
          </div>
          <div class="main" data-flex="dir:top" data-block>
            <div data-block>
              <div class="h3 text-one-hidd">
                {{ item.dealerDto.dealerName }}
              </div>
              <time class="time">试驾时间: {{ item.date }} {{ item.time }}</time>
            </div>
            <div class="type" data-flex="cross:bottom">
              {{ item.testDriverType | testDriverTypeFilter }}
            </div>
          </div>
        </div>
        <div class="list-tools" data-flex="main:right">
          <div class="button" data-flex="main:right">
            <div v-if="item.testDriverType !== 5" class="lan-button-box white-button" data-flex="main:right">
              <van-button v-if="item.isReport" class="lan-button lan-button-small white-button" @click.stop="handleConfirmJump(item, 'report')">
                试驾报告
              </van-button>
            </div>
            <div class="lan-button-box black-button" data-flex="main:right">
              <template v-if="item.testDriverType !== 5">
                <van-button v-if="[90240, 90241].includes(+item.status)" class="lan-button lan-button-small activeLanSmallBtn" @click.stop="handleConfirmJump(item, 'appointment')">
                  再次预约
                </van-button>
                <van-button v-if="+item.status === 90210" class="lan-button lan-button-small activeLanSmallBtn" @click.stop="handleConfirmJump(item, 'upload')">
                  {{ !!item.custIdCard ? '已' : '' }}上传证件
                </van-button>
                <template v-if="item.isReport">
                  <van-button v-if="+item.questionnaireStatus === 0" class="lan-button lan-button-small activeLanSmallBtn" @click.stop="handleConfirmJump(item, 'appraise')">
                    去评价
                  </van-button>
                  <van-button v-if="+item.questionnaireStatus === 2" class="lan-button lan-button-small activeLanSmallBtn" @click.stop="handleConfirmJump(item, 'check')">
                    查看评价
                  </van-button>
                </template>
              </template>
              <van-button v-if="+item.canceled === 1 && item.testDriverType === 5" class="lan-button lan-button-small" @click.stop="cancelAppo(item)">
                取消预约
              </van-button>
            </div>
          </div>
        </div>
        <finishedTemplate finishedText="抵达终点" v-if="list.length"></finishedTemplate>
      </div>
    </div>
    <div class="notdata" v-if="list.length < 1">
      <img class="notdata-img" :src="require(fromType ? '../../assets/img/wenjuanNotDataa.png' : '../../assets/img/noPublish.png')">
      <p class="notdata-text">
        暂无预约
      </p>
    </div>
    <van-dialog class="dialog lan-dialog-custom line-two-cols" v-model="modalshow" :close-on-click-overlay="true" message="确定取消试驾预约？" cancel-button-text="取消" confirm-button-text="确定" show-cancel-button @confirm="submit" />
  </div>
</template>

<script>
import Vue from 'vue'
import { Toast, Button, Dialog } from 'vant'
import wx from 'weixin-js-sdk'
import { mapGetters } from 'vuex'
import { getUserInfo } from '@/api/api'
import { AUTOMOBILE, TESTDRIVE_STATUS } from '@/config/conf.data'
import { callNative } from '@/utils/'
import {
  testDriverList, getListTestDrive, cancelOrUpdate, postVeryLongReservationCancel
} from '../../api/test-driver'
import api from '../../config/url'
import storage from '@/utils/storage'
Vue.use(Toast).use(Button).use(Dialog)
import finishedTemplate from '@/components/finishedTemplate.vue'

const codeType = ['00', '200']
export default {
  components: { finishedTemplate },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      BaseOssHost: api.BaseOssHost,
      dataready: false,
      columns1: [
        { id: '90010', text: '到店' },
        { id: '90020', text: '活动' },
        { id: '90030', text: '试驾' },
        { id: '90040', text: '交车' },
        { id: '90050', text: '验车' }],
      list: [],
      mobile: '',
      status: {},
      modalshow: false,
      cancelAppoData: {},
      TESTDRIVE_STATUS,
      device: {}
    }
  },



  async mounted() {
    this.device = await this.getDevice() || {}

    console.log('%c [ this.device ]-179', 'font-size:14px; background:#cf222e; color:#fff;', this.device)

    const res = await callNative('getAudiUserInfo', { sync: 'login' });
    console.log("res111:",res)
    if (res.token) {
      console.log("进来token了")
      this.$store.commit('setToken', res)
      storage.set('token', res.token)
      
      const { data } = await getUserInfo()
      if (codeType.includes(data.code)) {
        this.mobile = data.data.userInfo.mobile
        localStorage.setItem('userInfo', JSON.stringify(data.data.userInfo))
      }
      this.testDriverList()

    }

    
  },
  methods: {
    ...mapGetters(['getDevice']),
    toDriveDetail(item) {
      if (item.testDriverType === 5) {
        // 超长试驾
        this.$router.push({
          path: this.fromType ? '/testdrive/long-detail?fromType=fromPurple' : '/testdrive/long-detail',
          query: {
            id: item.appoId
          }
        })
      } else {
        this.$router.push({
          path: this.fromType ? '/testdrive/detail?fromType=fromPurple' : '/testdrive/detail',
          query: {
            id: item.appoId
          }
        })
      }
      const params = {
        page_name: '我的预约'
      }
      this.$sensors.track('clickReservationCard', params)
    },
    async testDriverList() {
      // this.$store.commit('showLoading')
      const data = {
        // appoBeginTime: '2021-08-01 10:00',
        // appoEndTime: '2021-09-31 10:00',
        // appoPhone: this.mobile,
        appoType: '90030',
        // seriesCode: '498B',
        page: 1,
        size: 999
      }
      const { data: { data: list } } = await testDriverList(data)
      console.log("list111:",list)
      // this.$store.commit('hideLoading')
      if (list?.data?.length) {
        list.data.forEach((l) => {
          const [date, t1] = l.appoBeginTime.split(' ')
          const [d, t2] = l.appoEndTime.split(' ')
          const [seriesCode] = AUTOMOBILE.filter((i) => l.seriesId === i.seriesCode)
          l.date = date
          l.time = `${t1} - ${t2}`
          l.path = seriesCode.seriesPathName
          l.state = TESTDRIVE_STATUS.find((i) => i.status.includes(l.status))
        })
        this.list = list.data
      }
      // const params = {
      //   mobile: this.mobile || '',
      //   page: 1,
      //   size: 999
      // }
      // await getListTestDrive(params).then((res) => {
      //   if (codeType.includes(res.data.code)) {
      //     this.list = res.data.data.data
      //   }
      // })
    },
    cancelAppo(data) {
      this.modalshow = true
      this.cancelAppoData = data

      const params = {
        page_name: '我的预约试驾页面'
      }
      this.$sensors.track('clickCancelAppointment', params)
    },
    submit() {
      this.$store.commit('showLoading')
      this.modalshow = false
      // 超长试驾取消postVeryLongReservationCancel
      if (this.cancelAppoData.testDriverType === 5) {
        postVeryLongReservationCancel({ appoId: this.cancelAppoData.appoId }).then((res) => {
          if (res.status === 200) {
            this.testDriverList().then((ew) => {
              this.$store.commit('hideLoading')
            })
          }
        })
      } else {
        const data = {
          appoId: this.cancelAppoData.appoId,
          operation: 1,
          appoType: 90030
        }
        cancelOrUpdate(data).then((res) => {
          if (res.status === 200) {
            this.testDriverList().then((ew) => {
              this.$store.commit('hideLoading')
            })
          }
        })
      }

      const params = {
        test_drive_id: this.cancelAppoData.appoId
      }
      this.$sensors.track('clickCancelTestDrive', params)
    },
    handleConfirmJump(item, type = '') {
      const { env } = this.$route.query
      const {
        potMobile, testDriveId, appoId, potName, seriesId, questionnaireShortUrl, dealerDto: { dealerName, dealerCode }
      } = item
      console.log('%c [ handleUploadingPhoto ]-267', 'font-size:14px; background:#cf222e; color:#fff;', item, item.potMobile, item.id, item.appoId, seriesId)
      if (type === 'appointment') {
        const query = {
          form: 'order',
          mobile: potMobile,
          name: potName,
          dealerCode,
          idx: ['49', 'G4', 'G6'].indexOf(seriesId)
        }

        if (env === 'minip') {
          const string = Object.keys(query).reduce((i, n) => i + ((query[n] || ['', 0].includes(query[n])) ? (`&${n}=${query[n]}`) : ''), '')
          const strp = string.substring(1, string.length)
          wx.miniProgram.navigateTo({ url: `/pages/testdrive/create?${strp}` })
          return
        }
        return this.$router.push({
          path: this.fromType ? '/testdrive/create?fromType=fromPurple' : '/testdrive/create',
          query
        })
      }
      if (type === 'upload') {
        return this.$router.push({
          path:  this.fromType ? '/testdrive/upload-license?fromType=fromPurple' : '/testdrive/upload-license',
          query: {
            mobile: potMobile,
            id: testDriveId,
            appoid: appoId
          }
        })
      }
      if (type === 'report') {
        return this.$router.push({
          path: this.fromType ? '/testdrive/report?fromType=fromPurple' : '/testdrive/report',
          query: {
            id: testDriveId,
            seriesCode: seriesId,
            appoid: appoId
          }
        })
      }
      if (['appraise', 'check'].includes(type)) {
        const { nativeApp } = this.getDevice() || { nativeApp: false }
        if (nativeApp) {
          callNative('audiOpen', { path: questionnaireShortUrl })
        } else {
          window.open(questionnaireShortUrl, '_blank')
        }
      }
    }
  },
  filters: {
    testDriverTypeFilter(val) {
      if (val === 1) {
        return '到店试驾'
      } if (val === 4) {
        return '到店试乘'
      } if (val === 5) {
        return '超长试驾'
      }
      return ''
    },
    beginTimeFilter(val) {
      return val.substring(0, 16)
    },
    endTimeFilter(val) {
      return val.substring(11, 16)
    }
  }
}
</script>

<style lang='less' scoped>
div {
  box-sizing: border-box;
}

.container {
  display: flex;
  flex-flow: column;
  width: 100%;
  margin: 0;
  padding: 0;
  border-top: 1px #F2F2F2 solid;
  color: #000000;

  .appointment-list {
    padding: 24px 16px 16px 16px;
    display: flex;
    flex-flow: column;
    border-bottom: 1px #F2F2F2 solid;
    width: 100%;

    ._box {
      width: 100%;
      display: flex;
      align-items: center;

      ._img {
        width: 80px;
        background-color: #f2f2f2;

        img {
          width: 80px;
          height: 80px;
        }
      }

      ._content {
        width: calc(100% - 90px);
        height: 90px;
        display: flex;
        padding-left: 10px;
        flex-flow: column;

        ._head {
          display: flex;
          justify-content: space-between;

          ._title {
            font-size: 16px;
            line-height: 28px;
          }

          ._type {
            font-size: 12px;
            line-height: 16px;
            color: #333333;
          }
        }

        ._test {
          font-size: 12px;
          line-height: 16px;
          margin-top: 4px;
          color: #333333;
        }

        ._time {
          font-size: 12px;
          line-height: 16px;
          margin-top: 12px;
        }
      }
    }

    ._foot {
      margin-top: 16px;
      width: 100%;
      display: flex;
      justify-content: flex-end;

      ._button {
        border: 1px solid #000000;
        width: 68px;
        height: 24px;
        background: #FFFFFF;
        border: 1px solid #000000;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }
  }

  .notdata {
    margin-top: 80px;

    .notdata-img {
      display: block;
      width: 160px;
      height: 160px;
      margin: auto;
    }

    .notdata-text {
      text-align: center;
      font-size: 13px;
      color: #B3B3B3;
      font-family: DFP King Gothic GB-Regular;
    }
  }
}

.appointment-box {
  padding-bottom: 60px;

  .list {
    padding: 16px;
    border-bottom: .5px solid #F2F2F2;

    .list-head {
      height: 20px;
      line-height: 20px;

      .status {
        color: #262626;
        font-size: 12px;
        font-family: 'Audi-WideBold';
      }
    }

    .list-main {
      margin: 8px 0;

      .media,
      .media img {
        height: 80px;
        width: 80px;
      }
 
      .media {
        img {
          transform: scale(1.2);
        }
      }

      .main {
        margin-left: 8px;

        .h3 {
          height: 22px;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: 22px;
          margin-bottom: 4px;
        }

        .time {
          width: 209px;
          height: 20px;
          font-size: 12px;
          color: #B3B3B3;
          line-height: 20px;
        }

        .type {
          height: 20px;
          font-size: 12px;
          color: #808080;
          line-height: 20px;
        }
      }
    }

    .list-tools {
      .lan-button-box {
        .lan-button {
          margin-left: 4px;
        }

        .black-button {
          .lan-button-small {
            color: #E5E5E5;
          }
        }
      }
    }
  }
}


.fromPurple {
  div,span{
    background: transparent !important;
  }
  .appointment-box {
    padding-bottom: 60px;
    .list {
      margin: 16px;
      background: rgba(255, 255, 255, 0.05) !important;
      border-radius: .8rem;
      padding: 10px 16px 12px 8px;
      border-bottom:none;
      .list-head {
        height: 20px;
        line-height: 20px;
        text-align: right;
        background: transparent !important;
        padding-left: 18px;
        .status {
          color: #fff;
          font-size: 12px;
          font-family:none;
          background: transparent !important;
        }
      }

      .list-main {
        margin: 8px 0;
        display: flex;
        align-items: center;
        gap: 23px;
        background: transparent !important;
        .media,
        .media img {
          height: 80px;
          width: 80px;
          border-radius: .8rem;
          background: transparent !important;
        }

        .media {
          img {
            transform: scale(1);
          }
        }

        .main {
          margin-left: 8px;
          background: transparent !important;
          .h3 {
            font-size: 14px;
            font-weight: 400;
            color: #fff;
            margin-bottom: 4px;
            background: transparent !important;
            margin-bottom:2px
          }

          .time {
            width: 209px;
            font-size: 12px;
            color: #888A95 !important;
            background: transparent !important;
          }

          .type {
            font-size: 12px;
            color: #AFB1B9 !important;
            background: transparent !important;
            margin-top: 8px;
            
          }
        }
      }

      .list-tools {
        .lan-button-box {
          .lan-button {
            margin-left: 4px;
          }

          .black-button {
            .lan-button-small {
              color: #E5E5E5 !important;
            }
          }
        }

        .lan-button-box {
          text-align: right;
        }

        .lan-button-small {
          height: 28px;
          color: #FFF !important;
          background: rgba(255, 255, 255, 0.10) !important;
          border: none;
          border-radius: .4rem;
        }

        .activeLanSmallBtn {
          height: 28px;
          color: #FFF !important;
          background: #49377C !important;
          border: none;
          border-radius: .4rem;
        }
      }
    }
  }
}
</style>
