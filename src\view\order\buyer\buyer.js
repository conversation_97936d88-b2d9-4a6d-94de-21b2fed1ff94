/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-03 10:18:22
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-11-03 10:43:26
 * @FilePath     : \src\view\order\buyer\buyer.js
 * @Descripttion :
 */
import Vue from 'vue'
import {

  Cell,
  CellGroup
} from 'vant'


import {
  callNative
} from '@/utils'

Vue.use(Cell)
  .use(CellGroup)
export default {
  name: 'OrderBuyer',
  props: {
    buyerInfo: {
      type: Object,
      default: () => { }
    },
    encryption: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      spread: true,
      eyes1:false,
      eyes2:false,
      eyes3:false,
      eyes4:false,
      }
  },
  created() { 
     this.eyes1 = this.encryption
     this.eyes2 = this.encryption
     this.eyes3 = this.encryption
     this.eyes4 = this.encryption
  },
  methods: {
    handleSpreadEachOther() {
      this.spread = !this.spread
    },
    handleNavigationSoftware(latitude, longitude, dealerAdrress) {
      console.log('%c [ handleNavigationSoftware ]-2450', 'font-size:14px; background:#cf222e; color:#fff;', latitude, longitude, dealerAdrress)
      if ([latitude, longitude, dealerAdrress].every((i) => i)) {
        callNative('navigationMap', {
          lat: latitude,
          long: longitude,
          des: dealerAdrress
        })
      }
    }
  }
}
