<template>
  <div class="unsubscribeSucceed">
    <div>
      <img
        src="../../assets/img/correct.png"
      >
    </div>
    <div class="title">{{context[type].title}}！</div>
    <p class="cont" v-html="`${context[type].tip}`" />
    <div class="tips">订单查看路径：[我的]-[订单]-[服务]</div>
    <div class="btnWarp">
      <div
        class="buttons"
        @click="checkOrder"
      >
        查看我的服务订单
      </div>
    </div>
  </div>
</template>

<script>

import { callNative } from '@/utils'

export default {
  data() {
    return {
      type: 0,
      context: [
        { title: '提交成功', tip: '稍后我们的售后专家将会跟您取得联系；<br/>您的服务卡券请在进站后联系服务商使用' },
        { title: '取消成功', tip: '您已取消取车需求订单' },
        { title: '提交成功', tip: '稍后我们的售后专家将会跟您取得联系' },
        { title: '取消成功', tip: '您已取消送车需求订单' },
        { title: '取消成功', tip: '您已取消取送车需求订单' }
      ]
    }
  },
  mounted() {
    this.type = this.$route.query.type
    if (this.type >= 0) {
      this.$store.state.title = this.context[this.type].title
    }
  },
  methods: {

    checkOrder() {
      callNative('openRoutePath', { path: 'scaudi://mall/orderlist?index=3' })
      callNative('close', { type: 'service' })
    }
  }
}
</script>

<style scoped lang="less">
    @import url("../../assets/style/scroll.less");
    @import url("../../assets/style/buttons.less");

    .unsubscribeSucceed {
        padding: 16px;
        text-align: center;
      img {
          margin-top: 110px;
          width: 72px;
          height: 72px;
        }

        p {
            text-align: center;
            font-size: 16px;
            color: #999999;
            line-height: 22px;
            margin-top: 25px;
        }

        .btnWarp {
            position: fixed;
            z-index: 2;
            height: 50px;
            width: 100%;
            bottom: 0;
            padding-bottom: 30px;
            background: #fff;

            .buttons2 {
                top: 60px;
            }
        }
    }
    .title{
      margin-top: 28px;
    }
    .tips{
      color: #555;
    }
</style>
