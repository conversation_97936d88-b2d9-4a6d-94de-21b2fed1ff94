/**
 * 轮播图中的贴图配置
 *
 *    '2Y2Y': 'white', 皓月白
      'B9B9': 'blue', 星河蓝
      '8C8C': 'red',
      '2T2T': 'black', 矿夜黑
      '2L2L': 'gray', 和风灰
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      'B9A2': 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
 */
export default {
  A7L: {
    outsideList: ['45.png', '0.png', '-45.png', '-90.png', '90.png', 'fs.png'],
    defaltCarImg: '0.png',
    defaultHubImg: {
      F15: '0_xm.png', // 星芒
      F13: '0_xs.png', // 星束
      '42F': '0_xd.png', // 星斗
      '40V': '0_xy.png', // 星云
      '44Q': '0_yz.png' // 雅致
    },
    '498B2Y001': {
      // 志远型
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png'],
          ['grille/0_grille.png'],
          ['grille/-45_grille.png', 'back_word/-45_55word_white.png'],
          ['grille/-90_grille.png', 'back_word/-90_55word_white.png'],
          ['grille/90_grille.png'],
          ['grille/fs_grille.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    },
    // 志远型曜黑套装
    '498B2Y002': {
      outsideList: ['45.png', '0.png', '-45.png', '-90.png', '90.png', 'fs.png'],
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          [],
          [],
          ['back_word/-45_55word_black.png'],
          ['back_word/-90_55word_black.png'],
          [],
          []
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9B9: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9A2: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ]
        }
      }
    },
    // 境远型
    '498B2Y003': {
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      // 志远型
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png'],
          ['grille/0_grille.png'],
          ['grille/-45_grille.png', 'back_word/-45_55word_white.png'],
          ['grille/-90_grille.png', 'back_word/-90_55word_white.png'],
          ['grille/90_grille.png'],
          ['grille/fs_grille.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    },
    // 境远型曜黑套装
    '498B2Y004': {
      outsideList: ['45.png', '0.png', '-45.png', '-90.png', '90.png', 'fs.png'],
      // 志远型
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          [],
          [],
          ['back_word/-45_55word_black.png'],
          ['back_word/-90_55word_black.png'],
          [],
          []
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9B9: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9A2: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ]
        }
      }
    },
    // 先行版
    '498B2Y005': {
      outsideList: ['45.png', '0.png', '-45.png', '-90.png', '90.png', 'fs.png'],
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          [],
          [],
          ['back_word/-45_55word_black.png'],
          ['back_word/-90_55word_black.png'],
          [],
          []
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9B9: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9A2: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ]
        }
      }
    },
    // 先见版
    '498B2Y006': {
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png'],
          ['grille/0_grille.png'],
          ['grille/-45_grille.png', 'back_word/-45_55word_white.png'],
          ['grille/-90_grille.png', 'back_word/-90_55word_white.png'],
          ['grille/90_grille.png'],
          ['grille/fs_grille.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    },
    // 见远型
    '498BZY001': {
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      // 志远型
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png'],
          ['grille/0_grille.png'],
          ['grille/-45_grille.png', 'back_word/-45_45word_white.png'],
          ['grille/-90_grille.png', 'back_word/-90_45word_white.png'],
          ['grille/90_grille.png'],
          ['grille/fs_grille.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    },
    // 见远型曜黑套装
    '498BZY002': {
      outsideList: ['45.png', '0.png', '-45.png', '-90.png', '90.png', 'fs.png'],
      // 志远型
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          [],
          [],
          ['back_word/-45_45word_black.png'],
          ['back_word/-90_45word_black.png'],
          [],
          []
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9B9: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9A2: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ]
        }
      }
    },
    // 黑武士
    '498BZY003': {
      outsideList: ['45.png', '0.png', '-45.png', '-90.png', '90.png', 'fs.png'],
      // 志远型
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          [],
          [],
          ['back_word/-45_45word_black.png'],
          ['back_word/-90_45word_black.png'],
          [],
          []
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9B9: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9A2: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ]
        }
      }
    },
    // 白法师
    '498BZY004': {
      outsideList: ['45.png', '0.png', '-45.png', '-90.png', '90.png', 'fs.png'],
      // 志远型
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          [],
          [],
          ['back_word/-45_45word_black.png'],
          ['back_word/-90_45word_black.png'],
          [],
          []
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9B9: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9A2: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ]
        }
      }
    },
    // 圣骑士版 - 流晶
    '498BZY005': {
      outsideList: ['45.png', '0.png', '-45.png', '-90.png', '90.png', 'fs.png'],
      // 志远型
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          [],
          [],
          ['back_word/-45_45word_black.png'],
          ['back_word/-90_45word_black.png'],
          [],
          []
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9B9: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          B9A2: [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ]
        }
      }
    },
    // 风骑士版-曜黑
    '498BZY006': {
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      // 志远型
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png'],
          ['grille/0_grille.png'],
          ['grille/-45_grille.png', 'back_word/-45_45word_white.png'],
          ['grille/-90_grille.png', 'back_word/-90_45word_white.png'],
          ['grille/90_grille.png'],
          ['grille/fs_grille.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    },
    // 筑梦 流晶
    '498BZG001': {
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png', 'grass/45_grass_no_privacy.png'],
          ['grille/0_grille.png', 'grass/0_grass_no_privacy.png'],
          ['grille/-45_grille.png', 'grass/-45_grass_no_privacy.png', 'back_word/-45_45word_white_no_quattro.png'],
          ['grille/-90_grille.png', 'grass/-90_grass_no_privacy.png', 'back_word/-90_45word_white_no_quattro.png'],
          ['grille/90_grille.png', 'grille/90_grille_no_quattro.png'],
          ['grille/fs_grille.png', 'grass/fs_grass_no_privacy.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    },
    // 筑梦 青春版
    '498BZG002': {
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png', 'grass/45_grass_no_privacy.png'],
          ['grille/0_grille.png', 'grass/0_grass_no_privacy.png'],
          ['grille/-45_grille.png', 'grass/-45_grass_no_privacy.png', 'back_word/-45_45word_white_no_quattro.png'],
          ['grille/-90_grille.png', 'grass/-90_grass_no_privacy.png', 'back_word/-90_45word_white_no_quattro.png'],
          ['grille/90_grille.png', 'grille/90_grille_no_quattro.png'],
          ['grille/fs_grille.png', 'grass/fs_grass_no_privacy.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    },
    // 筑梦 未来版
    '498BZG003': {
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png', 'grass/45_grass_no_privacy.png'],
          ['grille/0_grille.png', 'grass/0_grass_no_privacy.png'],
          ['grille/-45_grille.png', 'grass/-45_grass_no_privacy.png', 'back_word/-45_45word_white_no_quattro.png'],
          ['grille/-90_grille.png', 'grass/-90_grass_no_privacy.png', 'back_word/-90_45word_white_no_quattro.png'],
          ['grille/90_grille.png', 'grille/90_grille_no_quattro.png'],
          ['grille/fs_grille.png', 'grass/fs_grass_no_privacy.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    },
    // 筑梦 新生 498BZG004
    '498BZG004': {
      outsideList: ['45_color.png', '0_color.png', '-45_color.png', '-90_color.png', '90_color.png', 'fs_color.png'],
      '2Y2Y': 'white',
      B9B9: 'blue',
      '8C8C': 'red',
      '2T2T': 'black',
      '2L2L': 'gray',
      '0D0D': 'silver',
      '3Z3Z': 'green',
      '2YA2': 'white',
      B9A2: 'blue',
      '8CA2': 'red',
      '2TA2': 'black',
      '2LA2': 'gray',
      '0DA2': 'silver',
      '3ZA2': 'green',
      extraImg: {
        // 配置线自带配置,如格栅等
        default: [
          ['grille/45_grille.png', 'grass/45_grass_no_privacy.png'],
          ['grille/0_grille.png', 'grass/0_grass_no_privacy.png'],
          ['grille/-45_grille.png', 'grass/-45_grass_no_privacy.png', 'back_word/-45_45word_white_no_quattro.png'],
          ['grille/-90_grille.png', 'grass/-90_grass_no_privacy.png', 'back_word/-90_45word_white_no_quattro.png'],
          ['grille/90_grille.png', 'grille/90_grille_no_quattro.png'],
          ['grille/fs_grille.png', 'grass/fs_grass_no_privacy.png']
        ],
        // 轮毂贴图
        hub: {
          F15: ['45_xm.png', '0_xm.png', '-45_xm.png', '', '', 'fs_xm.png'], // 星芒
          F13: ['45_xs.png', '0_xs.png', '-45_xs.png', '', '', 'fs_xs.png'], // 星束
          '42F': ['45_xd.png', '0_xd.png', '-45_xd.png', '', '', 'fs_xd.png'], // 星斗
          '40V': ['45_xy.png', '0_xy.png', '-45_xy.png', '', '', 'fs_xy.png'], // 星云
          '44Q': ['45_yz.png', '0_yz.png', '-45_yz.png', '', '', 'fs_yz.png'] // 星云
        },
        // 跟随外饰变化的贴图
        exterior: {
          '2Y2Y': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9B9: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8C8C': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2T2T': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2L2L': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0D0D': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3Z3Z': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ],
          '2YA2': [
            [],
            ['top/0_top_white.png'],
            ['top/-45_top_white.png'],
            ['top/-90_top_white.png'],
            [],
            ['top/fs_top_white.png']
          ],
          B9A2: [
            [],
            ['top/0_top_blue.png'],
            ['top/-45_top_blue.png'],
            ['top/-90_top_blue.png'],
            [],
            ['top/fs_top_blue.png']
          ],
          '8CA2': [
            [],
            ['top/0_top_red.png'],
            ['top/-45_top_red.png'],
            ['top/-90_top_red.png'],
            [],
            ['top/fs_top_red.png']
          ],
          '2TA2': [
            [],
            ['top/0_top_black.png'],
            ['top/-45_top_black.png'],
            ['top/-90_top_black.png'],
            [],
            ['top/fs_top_black.png']
          ],
          '2LA2': [
            [],
            ['top/0_top_gray.png'],
            ['top/-45_top_gray.png'],
            ['top/-90_top_gray.png'],
            [],
            ['top/fs_top_gray.png']
          ],
          '0DA2': [
            [],
            ['top/0_top_silver.png'],
            ['top/-45_top_silver.png'],
            ['top/-90_top_silver.png'],
            [],
            ['top/fs_top_silver.png']
          ],
          '3ZA2': [
            [],
            ['top/0_top_green.png'],
            ['top/-45_top_green.png'],
            ['top/-90_top_green.png'],
            [],
            ['top/fs_top_green.png']
          ]
        }
      }
    }
  },
  Q5E: {
    outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
    defaltCarImg: 'Side.png',
    defaultHubImg: {
      F15: '0_xm.png', // 星芒
      F13: '0_xs.png', // 星束
      '42F': '0_xd.png' //
    },
    // 星耀型 机甲套装 白法师
    G4ICC3005: {
      hubDirections: [],
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_40word_black.png', 'back_word/-45_40word_black.png', '', 'front_word/fs_front_black.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA1': '2YA1',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA1': '2LA1',
      '7M7M': '7M7M',
      '7MA1': '7MA1',
      Y2Y2: 'Y2Y2',
      Y2A1: 'Y2A1',
      S1S1: 'S1S1',
      S1A1: 'S1A1',
      '0G0G': '0G0G',
      '0GA1': '0GA1',
      Y6A2: 'Y6A2'
    },
    // 星耀型 机甲套装 黑武士
    G4ICC3003: {
      hubDirections: [],
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_40word_black.png', 'back_word/-45_40word_black.png', '', 'front_word/fs_front_black.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 星耀型 机甲套装 影武士
    G4ICC3004: {
      hubDirection: [],
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_40word_black.png', 'back_word/-45_40word_black.png', '', 'front_word/fs_front_black.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA1': '2YA1',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA1': '2LA1',
      '7M7M': '7M7M',
      '7MA1': '7MA1',
      Y2Y2: 'Y2Y2',
      Y2A1: 'Y2A1',
      S1S1: 'S1S1',
      S1A1: 'S1A1',
      '0G0G': '0G0G',
      '0GA1': '0GA1',
      Y6A2: 'Y6A2'
    },
    //  荣耀型 机甲套装 白法师
    G4ICF3006: {
      hubDirections: [],
      grassDirection: ['', '', '', '', '', ''],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_50word_black.png', 'back_word/-45_50word_black.png', '', 'front_word/fs_front_black.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA1': '2YA1',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA1': '2LA1',
      '7M7M': '7M7M',
      '7MA1': '7MA1',
      Y2Y2: 'Y2Y2',
      Y2A1: 'Y2A1',
      S1S1: 'S1S1',
      S1A1: 'S1A1',
      '0G0G': '0G0G',
      '0GA1': '0GA1',
      Y6A2: 'Y6A2'
    },
    // 荣耀型 机甲套装 黑武士
    G4ICF3004: {
      hubDirections: [],
      grassDirection: ['', '', '', '', '', ''],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_50word_black.png', 'back_word/-45_50word_black.png', '', 'front_word/fs_front_black.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 荣耀型 机甲套装 影武士
    G4ICF3005: {
      hubDirections: [],
      grassDirection: ['', '', '', '', '', ''],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_50word_black.png', 'back_word/-45_50word_black.png', '', 'front_word/fs_front_black.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA1': '2YA1',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA1': '2LA1',
      '7M7M': '7M7M',
      '7MA1': '7MA1',
      Y2Y2: 'Y2Y2',
      Y2A1: 'Y2A1',
      S1S1: 'S1S1',
      S1A1: 'S1A1',
      '0G0G': '0G0G',
      '0GA1': '0GA1',
      Y6A2: 'Y6A2'
    },
    // 星耀型 锦衣套装
    G4IBC3001: {
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['', '', 'back_word/-90_40word_white.png', 'back_word/-45_40word_white.png', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 星耀型 锦衣套装 2023
    G4IBC3004: {
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['', '', 'back_word/-90_40word_white.png', 'back_word/-45_40word_white.png', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 星耀型 锦衣套装 逐日版
    G4IBC3002: {
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 荣耀型 锦衣套装
    G4IBF3001: {
      grassDirection: ['', '', '', '', '', ''],
      engineDirection: ['', '', 'back_word/-90_50word_white.png', 'back_word/-45_50word_white.png', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 荣耀型 锦衣套装 "2023"
    G4IBF3003: {
      grassDirection: ['', '', '', '', '', ''],
      engineDirection: ['', '', 'back_word/-90_50word_white.png', 'back_word/-45_50word_white.png', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 荣耀型 锦衣套装 逐日版
    G4IBF3002: {
      grassDirection: ['', '', '', '', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 星耀型 机甲套装
    G4ICC3001: {
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['', '', 'back_word/-90_40word_white.png', 'back_word/-45_40word_white.png', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 星耀型 机甲套装 2023
    G4ICC3007: {
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_50word_black.png', 'back_word/-45_50word_black.png', '', 'front_word/fs_front_black.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA1': '2YA1',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA1': '2LA1',
      '7M7M': '7M7M',
      '7MA1': '7MA1',
      Y2Y2: 'Y2Y2',
      Y2A1: 'Y2A1',
      S1S1: 'S1S1',
      S1A1: 'S1A1',
      '0G0G': '0G0G',
      '0GA1': '0GA1',
      Y6A2: 'Y6A2'
    },
    // 闪耀型 机甲套装 2023 better lite
    G4ICC3006: {
      grassDirection: [],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_40word_black.png', 'back_word/-45_40word_black.png', '', 'front_word/fs_front_black.png'],
      hubDirections: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA1': '2YA1',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA1': '2LA1',
      '7M7M': '7M7M',
      '7MA1': '7MA1',
      Y2Y2: 'Y2Y2',
      Y2A1: 'Y2A1',
      S1S1: 'S1S1',
      S1A1: 'S1A1',
      '0G0G': '0G0G',
      '0GA1': '0GA1',
      Y6A1: 'Y6A1'
    },
    // 闪耀型 锦衣套装 better lite 2023
    G4IBC3003: {
      grassDirection: [],
      engineDirection: [],
      hubDirections: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 星耀型 机甲套装 逐日版
    G4ICC3002: {
      grassDirection: ['grass/45_grass_no_privacy.png', '', 'grass/-90_grass_no_privacy.png', 'grass/-45_grass_no_privacy.png', 'grass/0_grass_no_privacy.png', 'grass/fs_grass_no_privacy.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 荣耀型 机甲套装
    G4ICF3001: {
      grassDirection: ['', '', '', '', '', ''],
      engineDirection: ['', '', 'back_word/-90_50word_white.png', 'back_word/-45_50word_white.png', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 荣耀型 机甲套装 2023
    G4ICF3007: {
      grassDirection: ['', '', '', '', '', ''],
      engineDirection: ['front_word/45_front_black.png', 'front_word/0_front_black.png', 'back_word/-90_50word_black.png', 'back_word/-45_50word_black.png', '', 'front_word/fs_front_black.png'],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA1': '2YA1',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA1': '2LA1',
      '7M7M': '7M7M',
      '7MA1': '7MA1',
      Y2Y2: 'Y2Y2',
      Y2A1: 'Y2A1',
      S1S1: 'S1S1',
      S1A1: 'S1A1',
      '0G0G': '0G0G',
      '0GA1': '0GA1',
      Y6A2: 'Y6A2'
    },
    // 艺创典藏版
    G4ICF3002: {
      grassDirection: ['', '', '', '', '', ''],
      engineDirection: ['', '', 'back_word/-90_50word_white.png', 'back_word/-45_50word_white.png', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    },
    // 荣耀型 机甲套装 逐日版
    G4ICF3003: {
      grassDirection: ['', '', '', '', '', ''],
      outsideList: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      '2Y2Y': '2Y2Y',
      '2YA2': '2YA2',
      '2T2T': '2T2T',
      '2L2L': '2L2L',
      '2LA2': '2LA2',
      '7M7M': '7M7M',
      '7MA2': '7MA2',
      Y2Y2: 'Y2Y2',
      Y2A2: 'Y2A2',
      S1S1: 'S1S1',
      S1A2: 'S1A2',
      '0G0G': '0G0G',
      '0GA2': '0GA2',
      Y6A2: 'Y6A2'
    }
  },
  Q6: {
    defaultHubImg: {

    },
    semiCustomizer: [],
    G6ICBY002: { // Audi Q6 50 TFSI quattro Roadjet 凌云版 Edition One 飞骑套装
      platform: 'sphere',
      currentModelCode: 'G6ICBY002',
      currentExCode: 'C9A2',
      currentHub: '53E',
      currentVos: 'Q4Q',
      currentSibColor: 'N5D+UD',
      currentEih: '5MD',
      currentInCode: 'N5D+UD',
      currentOptions: ['SAP', 'HSP', '5MD'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      grassDirection: ['grass/45_grass_privacy.png', '', '', 'grass/-45_grass_privacy.png', 'grass/0_grass_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['', '', 'back_word/-90_50word_black.png', 'back_word/-45_50word_black.png', '', '']
    },
    G6ICBY001: { // Audi Q6 50 TFSI quattro Roadjet 行云型 飞骑套装
      platform: 'sphere',
      currentModelCode: 'G6ICBY001',
      currentExCode: '2LA2',
      currentHub: '48P',
      currentVos: 'Q4Q',
      currentSibColor: 'N5D+TO',
      currentEih: '5TD',
      currentInCode: 'N5D+TO',
      currentOptions: ['SAP', 'HSP', '5TD'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      grassDirection: ['grass/45_grass_privacy.png', '', '', 'grass/-45_grass_privacy.png', 'grass/0_grass_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['', '', 'back_word/-90_50word_black.png', 'back_word/-45_50word_black.png', '', '']
    },
    G6IBBY001: { // Audi Q6 50 TFSI quattro Roadjet 行云型 羽林套装
      platform: 'sphere',
      currentModelCode: 'G6IBBY001',
      currentExCode: '3D3D',
      currentHub: '48P',
      currentVos: 'Q2J',
      currentSibColor: 'N5D+TT',
      currentEih: '5TV',
      currentInCode: 'N5D+TT',
      currentOptions: ['SAP', 'HSP', 'N6NQ', '2ZM'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      grassDirection: ['grass/45_grass_privacy.png', '', '', 'grass/-45_grass_privacy.png', 'grass/0_grass_privacy.png', 'grass/fs_grass_no_privacy.png'],
      engineDirection: ['', '', 'back_word/-90_50word_white.png', 'back_word/-45_50word_white.png', '', '']
    },
    G6ICAY001: { // Audi Q6 45 TFSI quattro Roadjet 齐云型 飞骑套装
      platform: 'sphere',
      currentModelCode: 'G6ICAY001',
      currentExCode: '2LA2',
      currentHub: '48P',
      currentVos: 'Q4Q',
      currentSibColor: 'N7K+TO',
      currentEih: '5TD',
      currentInCode: 'N7K+TO',
      currentOptions: ['SAP', 'HSP', '5TD'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      grassDirection: ['', '', '', '', '', ''],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      engineDirection: ['', '', 'back_word/-90_45word_black.png', 'back_word/-45_45word_black.png', '', '']
    },
    G6ICAY002: { // Audi Q6 45 TFSI quattro Roadjet 齐云型 飞骑套装 影法师版 (7座)
      platform: 'sphere',
      currentModelCode: 'G6ICAY001',
      currentExCode: '2LA2',
      currentHub: '48P',
      currentVos: 'Q4Q',
      currentSibColor: 'N7K+TO',
      currentEih: '5TD',
      currentInCode: 'N7K+TO',
      currentOptions: ['SAP', 'HSP', '5TD'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      grassDirection: ['grass/45_grass_privacy.png', '', '', 'grass/-45_grass_privacy.png', 'grass/0_grass_privacy.png', 'grass/fs_grass_no_privacy.png'],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      engineDirection: ['', '', 'back_word/-90_45word_black.png', 'back_word/-45_45word_black.png', '', '']
    },
    G6ICAY003: { // Audi Q6 45 TFSI quattro Roadjet 齐云型 飞骑套装  影法师版 (6座)
      platform: 'sphere',
      currentModelCode: 'G6ICAY001',
      currentExCode: '2LA2',
      currentHub: '48P',
      currentVos: 'Q4Q',
      currentSibColor: 'N7K+TO',
      currentEih: '5TD',
      currentInCode: 'N7K+TO',
      currentOptions: ['SAP', 'HSP', '5TD'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      grassDirection: ['grass/45_grass_privacy.png', '', '', 'grass/-45_grass_privacy.png', 'grass/0_grass_privacy.png', 'grass/fs_grass_no_privacy.png'],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      engineDirection: ['', '', 'back_word/-90_45word_black.png', 'back_word/-45_45word_black.png', '', '']
    },
    G6ICAY004: { // Audi Q6 45 TFSI quattro Roadjet 齐云型 飞骑套装  黑武士版 (7座)
      platform: 'sphere',
      currentModelCode: 'G6ICAY001',
      currentExCode: '2LA2',
      currentHub: '48P',
      currentVos: 'Q4Q',
      currentSibColor: 'N7K+TO',
      currentEih: '5TD',
      currentInCode: 'N7K+TO',
      currentOptions: ['SAP', 'HSP', '5TD'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      grassDirection: ['grass/45_grass_privacy.png', '', '', 'grass/-45_grass_privacy.png', 'grass/0_grass_privacy.png', 'grass/fs_grass_no_privacy.png'],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      engineDirection: ['', '', 'back_word/-90_45word_black.png', 'back_word/-45_45word_black.png', '', '']
    },
    G6ICAY005: { // Audi Q6 45 TFSI quattro Roadjet 齐云型 飞骑套装  黑武士版 (6座)
      platform: 'sphere',
      currentModelCode: 'G6ICAY001',
      currentExCode: '2LA2',
      currentHub: '48P',
      currentVos: 'Q4Q',
      currentSibColor: 'N7K+TO',
      currentEih: '5TD',
      currentInCode: 'N7K+TO',
      currentOptions: ['SAP', 'HSP', '5TD'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      grassDirection: ['grass/45_grass_privacy.png', '', '', 'grass/-45_grass_privacy.png', 'grass/0_grass_privacy.png', 'grass/fs_grass_no_privacy.png'],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      engineDirection: ['', '', 'back_word/-90_45word_black.png', 'back_word/-45_45word_black.png', '', '']
    },
    G6IBAY001: { // Audi Q6 45 TFSI quattro Roadjet 齐云型 羽林套装
      platform: 'sphere',
      currentModelCode: 'G6IBAY001',
      currentExCode: '3D3D',
      currentHub: '48P',
      currentVos: 'Q2J',
      currentSibColor: 'N5U+TI',
      currentEih: '5TV',
      currentInCode: 'N5U+TI',
      currentOptions: ['SAP', 'HSP', '5TV'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      grassDirection: ['', '', '', '', '', ''],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      engineDirection: ['', '', 'back_word/-90_45word_white.png', 'back_word/-45_45word_white.png', '', '']
    },
    G6ICCY001: { // Audi Q6 40 TFSI quattro Roadjet 观云型 飞骑套装
      platform: 'sphere',
      currentModelCode: 'G6ICCY001',
      currentExCode: '2LA2',
      currentHub: 'C5S',
      currentVos: 'Q4Q',
      currentSibColor: 'N7U+TO',
      currentEih: '5TE',
      currentInCode: 'N7U+TO',
      currentOptions: ['SAP', 'HSP', '5TE'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      grassDirection: ['', '', '', '', '', ''],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      engineDirection: ['', '', 'back_word/-90_40word_black.png', 'back_word/-45_40word_black.png', '', '']
    },
    G6IBCY001: { // Audi Q6 40 TFSI quattro Roadjet 观云型 羽林套装
      platform: 'sphere',
      currentModelCode: 'G6IBCY001',
      currentExCode: '3D3D',
      currentHub: 'C5S',
      currentVos: 'Q2J',
      currentSibColor: 'N5W+MP',
      currentEih: '5MY',
      currentInCode: 'N5W+MP',
      currentOptions: ['SAP', 'HSP', '5MY'],
      interiorType: '',
      currentView: 'out',
      showInnerButton: '0',
      showInnerHotspot: 0,
      recommendAngle: 'Front45.png',
      imagesDirection: ['Front45.png', 'Front.png', 'Rear.png', 'Rear45.png', 'Side.png', 'Top.png'],
      grassDirection: ['', '', '', '', '', ''],
      hubDirection: ['45_hub.png', '', '', '-45_hub.png', '0_hub.png', 'fs_hub.png'],
      engineDirection: ['', '', 'back_word/-90_40word_white.png', 'back_word/-45_40word_white.png', '', '']
    }
  }

}
