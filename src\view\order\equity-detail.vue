<template>
  <div
    class="container"
    :class="[father==='configration'?'container1':'']"
  >
    <template v-if="dataList">
      <div class="layout-wrapper">
        <div
          class="layout-item-wrapper"
          v-for="(item, idx) in dataList"
          :key="'dataList' + idx"
          v-show="item.type!=='smallTitle'"
        >
          <div class="name-wrapper">
            <span class="name">
              {{ item.desc }}
            </span>
            <div
              @click="showDetail(item)"
              v-show="item.detail"
            >
              <van-icon
                name="info-o"
                style="vertical-align: middle;"
              />
            </div>
          </div>

          <div class="item-wrapper">
            <div
              class="item"
              v-for="(j, k) of item.children"
              :key="'children' + k"
            >
              <div
                class="name"
                v-html="j.desc"
              />
              <div
                @click="showDetail(j)"
                v-show="j.detail"
              >
                <van-icon name="info-o" />
              </div>
            </div>
          </div>
        </div>

        <!-- // 底部的type 字段 -->

        <div
          class="loan-item"
          style="margin-top:20px;"
          v-for="(item, idx) in dataList"
          :key="'dataList2' + idx"
          v-show="item.type==='smallTitle'"
        >
          {{ item.desc }}
        </div>
      </div>
    </template>
    <!-- <img
      class="quanyiimage"
      v-if="dataImage"
      :src="dataImage"
    > -->
    <!-- <div
      class="toquanyidetail"
      @click="toquanyidetail"
      v-if="carSeries.seriesCode === 'G4' || selectCarInfo.seriesCode === 'G4'"
    >
      查看详情 >
    </div> -->
    <!-- // 弹窗详情 -->
    <van-popup
      closeable
      v-model="detailVisible"
      style="width:90%;max-height:80%"
    >
      <div class="detail-wrapper">
        <div
          class="title"
          v-html="currentTitle"
        />
        <div
          class="detail"
          v-html="currentDetail"
        />
      </div>
    </van-popup>
    <div style="width: 100%;height: 30px;" />
  </div>
</template>


<script>

import Vue from 'vue'
import { Popup } from 'vant'
import { mapState } from 'vuex'
import { getUserRightsByCarModelId } from '@/api/api'

Vue.use(Popup)

export default {
  props: {
    father: {
      type: String,
      required: false,
      default: ''
    },
    carModelId: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      dataList: [],
      detailVisible: false,
      currentDetail: '',
      currentTitle: ''
    }
  },
  watch: {
    carModelId(val) {
      this.getEquity()
    }
  },

  mounted() {
    this.getEquity()
  },

  methods: {
    async getEquity() {
      const modelLineCode = this.$store.state?.carDetail?.configDetail?.carModel?.modelLineCode
      let resultId = this.carModelId || modelLineCode
      resultId = resultId || this.$route.query?.modelLineCode
      const caseCode = this.$route.query?.caseCode
      let p = { 
        carModelId: resultId,
        caseCode: "YEA,YEG".includes(caseCode) ? 'A' : '',
        type: 1,
        modelYear: this.selectCarInfo?.modelYear || this.$store.state?.carDetail?.configDetail?.carModel?.modelYear || this.$storage.getPlus('selectCarInfo')?.modelYear
      }
      let { orderId } = this.$route.query
      if(orderId) {
        p.orderId = orderId 
      }
      if (!p.caseCode) {
        delete p.caseCode
      }
      const { data } = await getUserRightsByCarModelId(p)
      this.dataList = JSON.parse(data.data.rights)
    },
    // 显示详情
    showDetail(item) {
      this.currentTitle = item.detailTitle
      this.currentDetail = item.detail
      this.detailVisible = true
    },
    close() {
      this.detailVisible = false
    },
    sectionToChinese(section) {
      const chnNumChar = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      const chnUnitChar = ['', '十', '百', '千', '万', '亿', '万亿', '亿亿']
      let strIns = ''; let
        chnStr = ''
      let unitPos = 0
      let zero = true
      while (section > 0) {
        const v = section % 10
        if (v === 0) {
          if (!zero) {
            zero = true
            chnStr = chnNumChar[v] + chnStr
          }
        } else {
          zero = false
          strIns = chnNumChar[v]
          strIns += chnUnitChar[unitPos]
          chnStr = strIns + chnStr
        }
        unitPos++
        section = Math.floor(section / 10)
      }
      return chnStr
    }
  }
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  padding: 16px;
  font-size: 14px;
  &.container1{
    height: calc(100vh - 190px);
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
  }
}
.layout-wrapper {
  .layout-item-wrapper {
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
    margin-bottom: 20px;
    padding: 10px;
    box-sizing: border-box;
    >.name-wrapper {
      display: flex;
      justify-content: space-between;
      align-items:center;
    }

    .item-wrapper {
      margin-top: 8px;

      .item {
        display: flex;
        justify-content: space-between;
        line-height: 26px;
        > .name {
          max-width: 76%;
        }
      }
    }
  }
}

.detail-wrapper {
  padding: 20px;
  padding-bottom: 50px;
  > .title {
    font-weight: bold;
    font-size: 16px;
    padding-right: 50px;
  }
  > .detail {
    margin-top: 15px;
    font-size: 14px;
  }
}
</style>
