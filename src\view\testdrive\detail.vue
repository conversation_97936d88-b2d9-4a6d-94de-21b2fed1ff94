<!-- 预约试驾详情 -->
<template>
  <div
    name="detail"
    :class="['detail-container',{ fromPurple: fromType }]"
  >
    <div
      class="detail-box"
    >
      <div
        class="scroll-into-view"
        id="scroll-into-view"
      />
      <div class="status-box">
        <div class="h3">
          {{ data.state && data.state.text || '' }}
        </div>
        <div class="desc">
          {{ data.state && data.state.desc }}
        </div>
      </div>
      <div class="dealer-box">
        <div class="h2">
          {{ agentCardData.dealerName }}
        </div>
        <div class="address">
          地址: {{ agentCardData.dealerAdrress }}
        </div>
        <div
          class="tools"
          data-flex="main:justify"
        >
          <div
            class="phone list"
            data-flex="main:center cross:center"
            @click="handleCallPhone('')"
          >
            <div
              class="img"
              data-flex="main:center cross:center"
            >
              <img
                src="@/assets/img/phone-1.png"
              >
            </div>
            <span>电话</span>
          </div>
          <div
            class="localtion list"
            data-flex="main:center cross:center"
            @click="handleNavigationSoftware"
          >
            <div
              class="img"
              data-flex="main:center cross:center"
            >
              <img
                src="@/assets/img/localtion-1.png"
              >
            </div><span>导航</span>
          </div>
        </div>
      </div>
      <div
        class="adviser"
        data-flex="cross:center main:justify"
      >
        <div
          class="info"
          data-flex="main:left cross:center"
          data-block
        >
          <div class="icon">
            <img
              src="@/assets/img/adviser.jpg"
            >
          </div>
          <div
            class="name text-one-hidd"
            data-flex="main:left cross:center"
          >
            <font class="text-one-hidd">
              {{ data.consName }}
            </font><span>您的专属管家</span>
          </div>
        </div>
        <div
          class="phone"
          @click="handleCallPhone('phone', data.consPhone)"
        >
          <img
            src="@/assets/img/phone.png"
          >
        </div>
      </div>
      <div class="list-box">
        <div class="list">
          <div
            class="title"
            data-flex="main:justify cross:center"
          >
            <div class="h4">
              预约时间
            </div>
            <div class="right">
              {{ data.date }} {{ data.time }}
            </div>
          </div>
        </div>
      </div>
      <div
        class="upload-box model-box"
        data-flex="main:justify cross:center"
        @click="toupload(data.potMobile)"
      >
        <div class="title">
          上传证件
        </div>
        <div
          class="status-text"
          data-flex="cross:center"
        >
          <span>{{ confirm ? '已' : '未' }}上传</span><van-icon
            class="arrow"
            name="arrow"
            size="16px"
          />
        </div>
      </div>
      <div class="list-box">
        <div class="list">
          <div
            class="title"
            data-flex="main:justify cross:center"
          >
            <div class="h4">
              试驾信息
            </div>
          </div>
          <div
            class="item"
            data-flex="main:justify cross:center"
          >
            <div class="name">
              试驾车系
            </div>
            <div class="info">
              {{ data.seriesDto && data.seriesDto.seriesName }}
            </div>
          </div>
          <div
            class="item"
            data-flex="main:justify cross:center"
          >
            <div class="name">
              个人姓名
            </div>
            <div class="info">
              {{ data.approveName }}
            </div>
          </div>
          <div
            class="item unbo"
            data-flex="main:justify cross:center"
          >
            <div class="name">
              联系电话
            </div>
            <div class="info">
              {{ data.potMobile | phoneNum}}
            </div>
          </div>
        </div>
      </div>
      <div
        class="testdrive-box model-box"
        data-flex="main:justify cross:center"
        @click="toway"
      >
        <div class="title">
          试驾路线
        </div>
        <div
          class="status-text"
          data-flex="cross:center"
        >
          <van-icon
            class="arrow"
            name="arrow"
            size="16px"
          />
        </div>
      </div>
      <div class="list-box">
        <div class="list">
          <div
            class="title"
            data-flex="main:justify cross:center"
          >
            <div class="h4">
              注意事项
            </div>
          </div>
          <div
            class="item unbo"
            v-for="(list, index) of DESC_LIST"
            :key="index"
          >
            <div class="desc">
              {{ list }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="footer-fixed footer-button-box"
      data-flex="main:justify"
    >
      <div 
        :class="['lan-button-box white-button', pillars > 1 ? 'line-two-cols' : '']"
        v-if="data.isReport"
      >
        <van-button
          class="lan-button"
          @click="toBaogao"
        >
          试驾报告
        </van-button>
      </div>
      <div
        :class="['lan-button-box black-button', pillars > 1 ? 'line-two-cols' : '']"
        v-if="+data.canceled === 1"
      >
        <van-button
          class="lan-button"
          @click="modalshow = true"
        >
          取消预约
        </van-button>
      </div>

      <div
        :class="['lan-button-box black-button', pillars > 1 ? 'line-two-cols' : '']"
        v-if="data.isReport && [0, 2].includes(+data.questionnaireStatus)"
      >
        <van-button
          class="lan-button"
          @click="toPingjia"
        >
          {{ data.questionnaireStatus === 0 ? "去" : '查看' }}评价
        </van-button>
      </div>

      <div
        :class="['lan-button-box black-button', pillars > 1 ? 'line-two-cols' : '']"
        v-if="[90240, 90241].includes(+data.status)"
      >
        <van-button
          class="lan-button"
          @click="tocreate"
        >
          再次预约
        </van-button>
      </div>
    </div>
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols"
      v-model="modalshow"
      cancel-button-text="取消"
      confirm-button-text="确认"
      show-cancel-button
      message="确定取消试驾预约？"
      @confirm="submit(true)"
      @cancel="modalshow = false"
    />
    <!-- <model
      :modalshow.sync="modalshow"
      @update:modalshow="submit"
      title="确定取消试驾预约？"
    /> -->
    <model
      title="在APP内打开"
      content="点击下方复制链接，前往浏览器打开"
      confirm-text="复制链接"
      @onConfirm="copyClipboard"
      :modalshow.sync="modalOpenShow"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Icon, Toast, Button, Dialog
} from 'vant'
import { mapGetters } from 'vuex'
// import agentCard from '@/components/agent-card.vue'
import model from '@/components/model.vue'
import {
  InsertUrlShearPlate,
  callNative
} from '@/utils'
import { CONF_LINKS, TESTDRIVE_STATUS } from '@/config/conf.data'
import wx from 'weixin-js-sdk'
import { cancelOrUpdate, testDriverDetail } from '../../api/test-driver'

Vue.use(Icon).use(Toast).use(Button).use(Dialog)

const DESC_LIST = [
  '1.试驾人员须有持有中华人民共和国合法驾驶执照，当天请携带好本人驾驶执照，以供奥迪管家确认您的驾驶资格。若您未满18周岁，仅可进行试乘体验。',
  '2.试驾人员须签署相关试驾协议书，试驾时请遵守《中华人民共和国道路交通安全法》和《中华人民共和国道路交通管理条例》等相关法律法规，遵从奥迪管家的指引，否则上汽奥迪有权取消您的试驾体验。',
  '3. 试驾人员须确保身体条件正常，具备参加试驾所需的体力与精力，为了您和家人的安全，孕妇、高血压、心脏病患者和饮酒者以及身高不足1.4米的儿童不能参加试驾体验。若您正在服用可能造成嗜睡、晕眩等影响安全驾驶的药物，请提前告知。',
  '4.试驾当天建议穿着宽松舒适的服装，鞋子以运动鞋或休闲鞋等轻便平底鞋为佳，请勿穿着拖鞋，女士请勿穿高跟鞋及厚底鞋。近视人员请佩戴眼镜进行试驾体验。',
  '5.若试驾当天遇大雨、下雪、道路结冰、大雾等影响安全性的特殊天气，为了您和家人的安全，我们建议您改日进行体验，奥迪管家会与您协商改期。'
]

export default {
  components: { model },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      CONF_LINKS,
      id: '',
      data: {},
      status: {},
      modalshow: false,
      modalOpenShow: false,
      agentCardData: {},
      confirm: false,
      location: '31.230525,121.473667', // 当前的经纬度
      DESC_LIST,
      pillars: 0,
      headerHeight: 0
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.getdata()
    // this.$bridge.callHandler('getLocationCity', {}, (err, data) => {
    //   if (err) {
    //     // 发生错误
    //     console.log(err)
    //     return
    //   }
    //   if (data.location) {
    //     this.location = data.location
    //   }
    // })
  },
  methods: {
    ...mapGetters(['getDevice']),
    toPingjia() {
      const { data } = this
      const { nativeApp } = this.getDevice() || { nativeApp: false }
      if (nativeApp) {
        callNative('audiOpen', { path: data.questionnaireShortUrl })
      } else {
        window.open(data.questionnaireShortUrl, '_blank')
      }
      const params = {
        page_name: '预约试驾订单',
        test_drive_id: this.data.id,
        test_drive_car: '奥迪A7L',
        name: this.data.approveName,
        contact_information: this.data.potMobile,
        phone_number: this.data.potMobile,
        test_drive_type: '试乘试驾',
        agent: this.data.dealerDto.dealerName,
        test_drive_date: this.data.appoBeginTime.substring(0, 10),
        test_drive_time: this.data.appoBeginTime.substring(11, 16) + this.data.appoEndTime.substring(11, 16)
      }
      this.$sensors.track('clickToComment', params)
    },
    toBaogao() {
      // const { env } = this.$route.query || ''
      // if (env === 'minip') {
      //   this.modalOpenShow = true
      //   return
      // }
      this.$router.push({
        path: this.fromType ? '/testdrive/report?fromType=fromPurple' : '/testdrive/report',
        query: {
          id: this.data.id,
          seriesCode: this.data.seriesDto.seriesCode,
          appoid: this.id
        }
      })
      this.$store.commit('setHeaderVisible', false)
      const params = {
        page_name: '预约试驾订单',
        test_drive_id: this.data.id,
        test_drive_car: '奥迪A7L',
        name: this.data.approveName,
        contact_information: this.data.potMobile,
        phone_number: this.data.potMobile,
        test_drive_type: '试乘试驾',
        agent: this.data.dealerDto.dealerName,
        test_drive_date: this.data.appoBeginTime.substring(0, 10),
        test_drive_time: this.data.appoBeginTime.substring(11, 16) + this.data.appoEndTime.substring(11, 16)
      }
      this.$sensors.track('testDriveReport', params)
    },
    copyClipboard() {
      if (InsertUrlShearPlate(`${CONF_LINKS.OPEN_APP}miniapp_testdrive`)) {
        this.modalOpenShow = false
        Toast({
          type: 'fail',
          message: '已复制到剪贴板',
          icon: require('../../assets/img/success.png')
        })
      } else {
        Toast({
          type: 'fail',
          message: '复制失败',
          icon: require('../../assets/img/error.png')
        })
      }
    },
    toupload(mobile) {
      if (this.data.status !== 90210) {
        return
      }
      // const { env } = this.$route.query || ''
      // if (env === 'minip') {
      //   this.modalOpenShow = true
      //   return
      // }
      this.$router.push({
        path: this.fromType ? '/testdrive/upload-license?fromType=fromPurple' : '/testdrive/upload-license',
        query: {
          mobile: mobile,
          id: this.data.id,
          appoid: this.id
        }
      })

      const params = {
        page_name: '试驾详情页面'
      }
      this.$sensors.track('clickPerfectInformation', params)
    },
    tocreate() {
      const {
        data: {
          potMobile, potName, seriesId, dealerDto: { dealerName, dealerCode }
        }
      } = this

      const query = {
        form: 'order',
        mobile: potMobile,
        name: potName,
        dealerCode,
        idx: ['49', 'G4', 'G6'].indexOf(seriesId)
      }

      if (this.$route.query.env === 'minip') {
        const string = Object.keys(query).reduce((i, n) => i + ((query[n] || ['', 0].includes(query[n])) ? (`&${n}=${query[n]}`) : ''), '')
        const strp = string.substring(1, string.length)
        wx.miniProgram.navigateTo({ url: `/pages/testdrive/create?${strp}` })
        return
      }
      this.$router.push({
        path: this.fromType ? '/testdrive/create?fromType=fromPurple' : '/testdrive/create',
        query
      })

      const params = {
        page_name: '预约试驾订单页面',
        key_module: '详情页'
      }
      this.$sensors.track('bookTestDrive', params)
    },
    toway() {
      this.$router.push({
        path: this.fromType ? '/testdrive/way?fromType=fromPurple' : '/testdrive/way',
        query: {
          amsTestDriveRoute: this.data.amsTestDriveRoute
        }
      })
    },
    submit(isSubmit) {
      const params = {
        page_name: '预约试驾页面'
      }
      this.$sensors.track('clickCancelAppointment', params)
      if (isSubmit) {
        this.modalshow = false
        const data = {
          appoId: this.data.appointmentId,
          operation: 1,
          appoType: 90030
        }
        cancelOrUpdate(data).then((res) => {
          if (res.status === 200) {
            this.$router.push({
              path: this.fromType ? '/testdrive/appointment-list?fromType=fromPurple' : '/testdrive/appointment-list'
            })
          }
        })

        const params = {
          test_drive_id: this.data.appointmentId
        }
        this.$sensors.track('clickCancelTestDrive', params)
      }
    },
    async getdata() {
      const vw = document.getElementById('scroll-into-view')
      if (vw) {
        vw.scrollIntoView({ block: 'start' })
      }
      this.$store.commit('showLoading')
      await testDriverDetail({ id: this.id }).then((res) => {
        console.log('res', res)
        if (res.status === 200 && res.data.code === '200') {
          const {
            data, data: {
              appoResult, isReport, canceled, questionnaireStatus, status
            }
          } = res.data
          if (data && Object.keys(data)?.length) {
            const [date, t1] = data.appoBeginTime.split(' ')
            const [d, t2] = data.appoEndTime.split(' ')
            data.date = date
            data.time = `${t1} - ${t2}`
            data.state = TESTDRIVE_STATUS.find((i) => i.status.includes(data.status))
          }
          this.pillars = [isReport, +canceled === 1, isReport && [0, 2].includes(+questionnaireStatus), [90240, 90241].includes(+status)].filter((i) => i).length
          this.data = data
          this.agentCardData = this.data.dealerDto
          this.confirm = !!this.data.custIdCard
          console.log('%c [ status, appoResult ]-501', 'font-size:14px; background:#cf222e; color:#fff;', data.status, data.appoResult, this.confirm)
        }
        this.$store.commit('hideLoading')
      })
    },
    handleCallPhone(cons = '', phone) {
      const { agentCardData: { dealerPhone } } = this
      const phoneNumber = `tel:${phone || dealerPhone}`
      if (cons) {
        if (!phone) {
          return Toast({
            message: ' 专属管家电话为空',
            className: 'toast-dark-mini toast-pos-middle',
            forbidClick: true,
            duration: 800
          })
        }
      } else {
        if (!dealerPhone) {
          return Toast({
            message: '经销商电话为空',
            className: 'toast-dark-mini toast-pos-middle',
            forbidClick: true,
            duration: 800
          })
        }
      }
      console.log('%c [ 呼起电话 ]-530', 'font-size:14px; background:#cf222e; color:#fff;', phoneNumber)
      window.location.href = phoneNumber
    },
    handleNavigationSoftware() {
      const { agentCardData: { latitude, longitude, dealerAdrress } } = this

      console.log('%c [ handleNavigationSoftware ]-544', 'font-size:14px; background:#cf222e; color:#fff;', latitude, longitude, dealerAdrress)
      if ([latitude, longitude, dealerAdrress].every((i) => i)) {
        callNative('navigationMap', {
          lat: `${latitude}`,
          long: `${longitude}`,
          des: dealerAdrress
        })
      } else {
        Toast({
          message: '经销商位置信息不全',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      }
    }
  },
  filters: {
    beginTimeFilter(val) {
      if (val) {
        return val.substring(0, 16)
      }
      return ''
    },
    endTimeFilter(val) {
      if (val) {
        return val.substring(11, 16)
      }
      return ''
    }
  }
}
</script>

<style lang='less' scoped>
.detail-box {
  padding: 16px 16px 120px 16px;
  .scroll-into-view {
    position: relative;
    top: -100px;
    width: 100%;
    height: 1px;
    z-index: -1;
    opacity: 0;
  }
  .status-box {
    border-bottom: 1px solid #F2F2F2;
    padding-bottom: 16px;
    color: #000000;
    .h3 {
      font-size: 14px;
      font-family: Audi-WideBold;
      line-height: 24px;
    }
    .desc {
      height: 18px;
      font-size: 10px;
      line-height: 18px;
      margin-top: 4px;
    }
  }
  .dealer-box {
    color: #000000;
    border: 1px solid #F2F2F2;
    margin-top: 12px;
    padding: 12px 12px 3px 12px;
    .h2 {
      font-size: 14px;
      line-height: 22px;
    }
    .address {
      margin-top: 8px;
      height: 36px;
      font-size: 10px;
      line-height: 18px;
      color: rgba(0, 0, 0, .6);
    }
    .tools {
      color: #000000;
      border-top: 1px solid #F2F2F2;
      .img {
        width: 24px;
        height: 24px;
        margin-right: 4px;
        img {
          height: 24px;
          width: 24px;
        }
      }
      .list {
        font-size: 10px;
        width: 50%;
        padding: 9px;
        &:first-child {
          border-right: 1px solid #F2F2F2;
        }
      }
    }
  }
  .adviser {
    margin-top: 11px;
    padding: 8px 14px 8px 8px;
    background-color: #F8F8F8;
    .icon, .icon img {
      width: 48px;
      height: 48px;
      border-radius: 50%;
    }
    .icon {
      margin-right: 16px;
    }
    .info {
      .name {
        width: calc(100vw - 170px);
        font-size: 14px;
        font-family: Audi-WideBold;
        font-weight: normal;
        color: #000000;
        line-height: 24px;
        span {
          font-size: 12px;
          font-weight: 400;
          color: #666666;
          line-height: 20px;
          margin-left: 4px;
        }
      }
    }
    .phone, .phone img {
      width: 36px;
      height: 36px;
    }
  }
  .list-box {
    .list {
      padding: 16px 0;
      .title {
        padding: 12px 0;
        .h4 {
          width: 64px;
            font-size: 16px;
            font-family: Audi-WideBold;
            font-weight: normal;
        }
        .right {
          font-size: 14px;
        }
      }
      .item {
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 12px;
        &:last-child {
          margin: 0 0 -4px 0;
          padding-bottom: 12px;
          border-bottom: 1px solid #F2F2F2;
        }
        &.unbo:last-child {
          border: none;
        }
        .name, .desc {
          color: rgba(0, 0, 0, .4);
        }
        .info {
          color: #333333;
        }
        .desc {
          line-height: 22px
        }
      }
    }
  }
  .model-box {
    border-top: 1px solid #f2f2f2;
    border-bottom: 1px solid #f2f2f2;
    height: 24px;
    padding: 16px 0;
    box-sizing: content-box;
    .title {
      font-size: 16px;
    }
    .status-text {
      font-size: 14px;
      color: #666666;
      .arrow {
        margin: -1px 0 0 8px;
        position: relative;
      }
    }
  }
}
.footer-fixed {
  position: fixed;
  z-index: 999;
  bottom: 50px;
  left: 16px;
  right: 16px;
  &.footer-button-box {
    height: 56px;
  }
}


.fromPurple{
  .footer-button-box{
    background: transparent !important;
  }
  .lan-button-box{
    background: #202020 !important;
    color: #FFFF !important;
    width: 48%;
    border-radius: .8rem;
  }
}
</style>
