/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-19 21:07:44
 * @Last Modified by: z<PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-06-07 22:30:40
 * 报价单actions
 */
import {
  getCarModelData, getNearDealers, getDealerByCode, getUserInfo, judgeReservationClient, autoSaveCarShoppingCart, getCarConfig
} from '@/api/api'
import { postBindUserByCCid } from '@/configratorApi/index.js'
import {
  callNative, handleTimeout, getLocation, getdiscount2, getUrlParamObj
} from '@/utils'


async function getLocationCity() {
  const res = await callNative('getLocationCity', {})
  if (!res.location) {
    console.warn('未从 native 获取到 location 数据')
  }
  return res
}

export default {
  async getNativeAppUserInfo({ commit, state }, p = {}) {
    const { isLogin, isAuth, isGuest } = p
    const params = {}
    params.sync = 'login'
    if (isAuth && !isGuest) {
      // 是否鉴权
      params.sync = 'authenticate'
    }
    // console.log('%c [ getAudiUserInfo ]-147', 'font-size:14px; background:#07c160; color:#fff;', isLogin, isAuth, isGuest, params)
    let res = ''
    const { env } = getUrlParamObj()
    console.log("env != 'test'", env != 'test')
    if (env != 'test') res = await callNative('getAudiUserInfo', params)
    console.log('游客模式 数据走查：getAudiUserInfo 的返回值 ', res)
    res = res || {
      token: '', userId: 0, refreshToken: '', isLogin: 0
    }
    commit('setLogin', res)
  },

  // 通过ccid获取车辆配置
  async getCarDetailByCcid({ commit, state, dispatch }, param) {
    const params = { ccid: param.ccid }
    if (param.page) {
      params.page = param.page
    }
    const { data } = await getCarConfig(params)
    console.log('通过ccid获取车辆配置:', data)
    // 保存车系详情
    commit('updateCarSeries', data.data.configDetail.carSeries)
    // 更新车辆详情
    commit('updateCarDetail', data.data)

    // 保存外观颜色信息?
    commit('saveOutsideColorInfo', data.data)

    // 构造config数据
    commit('standardConfigData', data.data)

    // 订单详情页的时候不更新创世卡
    if (param.page !== 'money-detail') {
      // 更新车辆创世卡信息
      !param.youkeVis && dispatch('getReservationClient', {
        seriesCode: data.data.configDetail.carSeries.seriesCode
      })
    }
    return data
  },


  // 获取以及更新当前的经销商数据
  async getDealerInfo({ commit, state, dispatch }) {
    if (state.dealerInfo?.dealerCode) {
      // 已经存在 dealercode, 执行更新
      console.warn('已经存在 dealercode')
    } else {
      const res = await getLocationCity()

      const location = res.location.split(',')
      const cityCode = await getLocation([location[1], location[0]])
      const param = {
        latitude: location[0] || '',
        longitude: location[1] || '',
        cityCode
      }
      dispatch('updateDealerInfo', param)
    }
  },

  // 通过经纬度/城市code 来获取经销商信息
  async updateDealerInfo({ commit, state }, param) {
    const { data } = await getNearDealers(param)
    const dealerInfo = data.data

    if (param.longitude && param.latitude && dealerInfo.longitude && dealerInfo.latitude) {
      console.log('距离计算 ', param.longitude, param.latitude, dealerInfo.longitude, dealerInfo.latitude)
      dealerInfo.distance = await getdiscount2(param.longitude, param.latitude, dealerInfo.longitude, dealerInfo.latitude)
    } else {
      dealerInfo.distance = 0
    }
    commit('updateCityOnlyHeader', param.cityCode && dealerInfo.dealerCode === '76600019')
    commit('updateDealerInfo', dealerInfo)
  },

  // 根据经销商的code来获取经销商的详情
  async getDealerOfCode({ commit, state }, param) {
    const { data } = await getDealerByCode(param)
    if (data?.data && Object.keys(data.data)?.length) {
      commit('updateDealerInfo', data.data)
    }
  },

  // 根据经销商的code来获取经销商的详情
  async getDealerByCode({ commit, state }, param) {
    const { data } = await getDealerByCode(param)
    const dealerInfo = data.data
    const res = await getLocationCity()

    const location = res.location?.split(',') || [0, 0]
    // 这里使用0/1 是因为上面经过地址的逆解析
    const locationObj = {
      latitude: location[0] || '',
      longitude: location[1] || ''
    }

    if (locationObj.longitude && locationObj.latitude && dealerInfo.longitude && dealerInfo.latitude) {
      console.log('距离计算 ', locationObj.longitude, locationObj.latitude, dealerInfo.longitude, dealerInfo.latitude)
      dealerInfo.distance = await getdiscount2(locationObj.longitude, locationObj.latitude, dealerInfo.longitude, dealerInfo.latitude)
    } else {
      dealerInfo.distance = 0
    }
    commit('updateDealerInfo', dealerInfo)
  },

  // 更新创世卡状态
  async getReservationClient({ commit, state, dispatch }, param) {
    const { data } = await getUserInfo()
    const mobile = data.data?.userInfo?.mobile
    if (!mobile) {
      return console.error('获取mobile失败')
    }
    const res = await judgeReservationClient({
      custMobile: mobile,
      seriesCode: param.seriesCode
    })

    // 创世卡用户更新经销商
    if (res.data.data) {
      dispatch('getDealerByCode', {
        dealerCode: res.data.data.orgCode
      })
    }

    commit('updateReserveCard', !!res.data.data)
    commit('updateReserveCardInfo', res.data.data)
  },


  // 自动保存
  async autoSaveShoppingCart({ commit }, param) {
    const { data } = await autoSaveCarShoppingCart(param)
    if (data.code === '00') {
      // 好像不需要结果?
      console.log('auto save shopping', data)
    }
  },

  async bindCcid({ commit }, param) {
    const { data } = await postBindUserByCCid(param)
    if (data.code !== '00') {
      console.error('绑定ccid 到用户失败')
    }
  }
}
