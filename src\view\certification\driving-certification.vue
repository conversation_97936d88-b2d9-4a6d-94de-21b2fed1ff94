<template>
  <div class="driving-certification">
    <div v-if="isPhotograph">
      <div class="line_content" />
      <div class="upload_tip">
        如果您已通过发票完成车辆认证, 无需再上传行驶证
      </div>
      <p class="item-p">拍照上传行驶证</p>
      <img
        class="item-img-updata"
        src="../../assets/img/icon-identity2.png"
        @click="onUploadOcrIdCard(1)"
      />
      <div class="item-title-bold-updata">上传行驶证正面</div>
    </div>

    <!-- 上传成功页面  -->
    <div v-if="isFileOK">
      <!-- <div style="justify-content: center; display: flex">
        <img
          style="width: 24px; height: 24px"
          src="../../assets/img/contract-success2.png"
        >
        <div style="display: flex; align-items: center; padding-left: 8px">
          上传成功！
        </div>
      </div> -->
      <img class="item-img" :src="base64" />
      <div class="line_content-result">
        <div class="s_line" />
        <div class="text_center">请确认识别结果（可修改）</div>
        <div class="e_line" />
      </div>
      <div class="line-border-connent-title">身份信息</div>
      <div v-if="uploadType === 1">
        <div class="line-border-bottom-result">
          <div class="name">所有人</div>
          <div class="str" v-if="!isChange">
            {{ ocrVehiclelicenseModel.owner }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleLicenseMainOwner }}
          </div>
        </div>
        <div class="line-border-bottom-result">
          <div class="name">车牌号码</div>
          <div class="str" v-if="!isChange">
            {{ ocrVehiclelicenseModel.plateNum }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleLicenseMainPlateNum }}
          </div>
        </div>
        <div class="line-border-bottom-result">
          <div class="name">VIN</div>
          <div class="str" v-if="!isChange">
            {{ ocrVehiclelicenseModel.vin }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleLicenseMainVin }}
          </div>
        </div>
        <p class="is-text-wang" v-if="isWrong">
          <img src="../../assets/wall/icon11.png" />

          {{ isWrong ? 'VIN码位数有误，请修改' : '您的VIN有误，请修改' }}
        </p>

        <div
          class="line-border-bottom-result"
          v-if="ocrVehiclelicenseModel.registerDate"
        >
          <div class="name">注册日期</div>
          <div class="str" v-if="!isChange">
            {{ ocrVehiclelicenseModel.registerDate }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleLicenseMainRegisterDate }}
          </div>
        </div>
        <p class="is-text-wang" v-if="isWrongTime1">
          <img src="../../assets/wall/icon11.png" />
          注册日期格式应为yyyy-mm-dd，请修改
        </p>
        <div v-if="ocrVehiclelicenseModel.date" class="line-border-bottom-result">
          <div class="name">发证日期</div>
          <div class="str">
            {{ ocrVehiclelicenseModel.date }}
          </div>
        </div>
        <p class="is-text-wang" v-if="isWrongTime2">
          <img src="../../assets/wall/icon11.png" />
          发证日期格式应为yyyy-mm-dd，请修改
        </p>
      </div>

      <div class="bottom_style">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="onReturnUpdate"
            :text="'修改信息'"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="onConfirm"
            text="确认提交"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </div>
    <!-- 修改页面-->
    <!-- <div v-if="isChange">
      <div class="item-title-bold">
        请选择1种认证方式
      </div>
      <img
        v-if="uploadType === 1"
        class="item-img"
        src="../../assets/img/icon-identity2.png"
        @click="onUploadOcrIdCard(1)"
      >
      <div
        class="item-title-bold"
        style="text-align: center; justify-content: center; font-size: 14px"
      >
        上传行驶证
      </div>
      <div class="line_content">
        <div class="s_line" />
        <div class="text_center">
          或
        </div>
        <div class="e_line" />
      </div>

      <div class="line">
        <div class="title">
          手动修改信息
        </div>
        <div
          class="btn-change"
          @click="onConfirm"
        >
          <van-icon
            class="btn-icon"
            name="arrow"
            size="16px"
          />
        </div>
      </div>
    </div> -->

    <input
      class="hide_file"
      ref="leftFile"
      id="upload"
      type="file"
      @change="getFile($event)"
      capture="camera"
      accept="image/camera"
    />
    <!--拍照提示-->
    <van-action-sheet
      v-model="show"
      :safe-area-inset-bottom="true"
      :round="false"
    >
      <div style="padding-right: 16px; padding-left: 16px">
        <div
          class="item-title-bold"
          style="
            text-align: center;
            justify-content: center;
            font-size: 16px;
            margin-top: 18px;
          "
        >
          证件上传示例
        </div>
        <div
          style="
            text-align: center;
            justify-content: center;
            font-size: 14px;
            margin-top: 16px;
            color: #666;
            padding-right: 26px;
            padding-left: 26px;
          "
        >
          请于光线充足的环境下，纯色背景下，四角对齐，横向拍照
        </div>
        <img class="item-img" src="../../assets/img/icon-identity3.png" />
      </div>
      <van-grid :border="false" :column-num="4" :gutter="16">
        <van-grid-item>
          <img src="../../assets/img/icon-identity5.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">缺失</div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity6.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">模糊</div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity7.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">过滤</div>
        </van-grid-item>

        <van-grid-item text="">
          <img src="../../assets/img/icon-identity8.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">
            背景模糊
          </div>
        </van-grid-item>
      </van-grid>
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onUploadingImg"
          :text="'知道了'"
          color="black"
          font-size="15px"
          height="48px"
        />
      </div>
    </van-action-sheet>

    <!--确认提交提示-->
    <van-popup
      v-model="showConfirmsubmitAction"
      :style="{ width: '100%' }"
      position="bottom"
      :close-on-click-overlay="false"
    >
      <div class="item-title-bold-updata-title">修改信息</div>
      <div v-if="uploadType === 1">
        <div
          class="item-title-bold-updata-connent"
          style="margin-left: 16px; margin-right: 16px"
        >
          <div class="name">所有人</div>
          <van-field
            class="str"
            type="text"
            v-model="updateVehicleLicenseMainOwner"
            rows="1"
          />
        </div>
        <div
          class="item-title-bold-updata-connent"
          style="margin-left: 16px; margin-right: 16px"
        >
          <div class="name">车牌号码</div>
          <van-field
            class="str"
            type="text"
            @focus="focusHandle"
            v-model="updateVehicleLicenseMainPlateNum"
            rows="1"
          />
        </div>
        <div
          class="item-title-bold-updata-connent"
          :style="{
            'margin-left': '16px',
            'margin-right': '16px',
            'margin-bottom': ocrVehiclelicenseModel.registerDate
              ? '16px'
              : '110px'
          }"
        >
          <div class="name">VIN</div>
          <van-field
            class="str"
            type="text"
            v-model="updateVehicleLicenseMainVin"
            @input="formValidatorName"
          />
        </div>
        <p class="is-text-wangs" v-if="isWrong1">
          <img src="../../assets/wall/icon11.png" />

          {{ isWrong1 ? 'VIN码位数有误，请修改' : '您的VIN有误，请修改' }}
        </p>
        <div
          v-if="ocrVehiclelicenseModel.registerDate"
          class="item-title-bold-updata-connent"
          :style="{marginBottom: !isWrongTime1 ? '110px' : '0', borderBottom: !isWrongTime1 ? '1px solid #f2f2f2' : 'none', marginLeft: '16px', marginRight: '16px'}"
        >
          <div class="name">注册日期</div>
          <van-field
            class="str"
            type="text"
            v-model="updateVehicleLicenseMainRegisterDate"
            @input="formValidatorDates"
            rows="1"
          />
        </div>
        <p class="is-text-wangs" v-if="isWrongTime1" style='margin-bottom: 110px;'>
          <img src="../../assets/wall/icon11.png" />
          注册日期格式应为yyyy-mm-dd，请修改
        </p>
      </div>

      <div class="bottom_style">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="afterConfirmation"
            text="取消修改"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="beforeConfirmation"
            text="确认修改"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </van-popup>

    <van-popup
      v-if="modalshow"
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal">
        <div class="modal-title">
          {{
            showBindingTypeAndcarOwn === 1 ? '请选择车辆类别' : '请选择车辆关系'
          }}
        </div>

        <div>
          <div
            class="service-line"
            style="margin-top: 8px"
            v-if="showBindingTypeAndcarOwn === 1"
          >
            <div class="ch-change" @click.stop="onCarOwnType(1)">
              <img
                class="ch-icon"
                :src="carOwnType === 1 ? activeIcon : inactiveIcon"
              />
            </div>
            <div class="title-bold">私家车</div>
          </div>

          <div
            class="service-line"
            style="margin-top: 8px"
            v-if="showBindingTypeAndcarOwn === 1"
          >
            <div class="ch-change" @click.stop="onCarOwnType(0)">
              <img
                class="ch-icon"
                :src="carOwnType === 0 ? activeIcon : inactiveIcon"
              />
            </div>
            <div class="title-bold">公务车</div>
          </div>

          <div
            class="service-line"
            style="margin-top: 8px"
            v-if="showBindingTypeAndcarOwn === 0"
          >
            <div class="ch-change" @click.stop="onBindingType(1)">
              <img
                class="ch-icon"
                :src="bindingType === 1 ? activeIcon : inactiveIcon"
              />
            </div>
            <div class="title-bold">我是车主</div>
          </div>
          <div
            class="service-line"
            style="margin-top: 8px"
            v-if="showBindingTypeAndcarOwn === 0"
          >
            <div class="ch-change" @click.stop="onBindingType(2)">
              <img
                class="ch-icon"
                :src="bindingType === 2 ? activeIcon : inactiveIcon"
              />
            </div>
            <div class="title-bold">我是用车人</div>
          </div>
        </div>
        <div
          class="modal-confirm center"
          @click.stop="onConfirmBindingTypeAndcarOwn"
        >
          {{ '确定' }}
        </div>
      </div>
    </van-popup>

    <!--确认提交提示-->
    <van-popup
      v-model="beforeConfirmationsubmitAction"
      :style="{ width: '90%' }"
    >
      <div
        style="
          background: #f2f2f2;
          height: 126px;
          padding-left: 26px;
          padding-right: 26px;
          margin-bottom: 12px;
        "
      >
        <div class="item-title-bold-point-out">重要提示</div>
        <div class="item-title-bold-point-out-text">
          确认提交后无法进行修改，如信息有误，将影响你的认证，无法获得相关权益
        </div>
      </div>
      <div v-if="uploadType === 1">
        <div class="line-border-bottom-affirm">
          <div class="name">所有人</div>
          <div class="str" v-if="!isChange">
            {{ ocrVehiclelicenseModel.owner }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleLicenseMainOwner }}
          </div>
        </div>
        <div class="line-border-bottom-affirm">
          <div class="name">车牌号码</div>
          <div class="str" v-if="!isChange">
            {{ ocrVehiclelicenseModel.plateNum }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleLicenseMainPlateNum }}
          </div>
        </div>
        <div class="line-border-bottom-affirm">
          <div class="name">VIN</div>
          <div class="str" v-if="!isChange">
            {{ ocrVehiclelicenseModel.vin }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleLicenseMainVin }}
          </div>
        </div>
        <div
          v-if="ocrVehiclelicenseModel.registerDate"
          class="line-border-bottom-affirm"
        >
          <div class="name">注册日期</div>
          <div class="str" v-if="!isChange">
            {{ ocrVehiclelicenseModel.registerDate }}
          </div>
          <div class="str" v-if="isChange">
            {{ updateVehicleLicenseMainRegisterDate }}
          </div>
        </div>
        <div class="line-border-bottom-affirm line-border-bottom-affirm-bottom">
          <div class="name">发证日期</div>
          <div class="str">
            {{ ocrVehiclelicenseModel.date }}
          </div>
        </div>
      </div>

      <div class="bottom_styles">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="cancel"
            text="取消"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="onConfirmsubmit"
            text="确认"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </van-popup>

    <van-dialog
      class="dialog lan-dialog-custom line-two-cols lan-dialog-content-f12"
      v-model="dialogShow"
      cancel-button-text="取消"
      confirm-button-text="确定"
      :title="`上汽奥迪申请获取相机权限`"
      show-cancel-button
      :message="`上汽大众需要申请相机权限，以便通过扫一扫、拍摄照片或视频为您提供上传头像、车辆图片、绑车/会员认证、专属桩绑定、发动态、提问题、写文章、评论、扫码充电、形象定制、AI助手相关服务。拒绝或取消授权不影响使用其他服务。`"
      @confirm="dialogConfirm"
      @cancel="dialogCancel"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Image as VanImage,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover,
  ActionSheet,
  Grid,
  GridItem
} from 'vant'
import HtmlImageCompress from 'html-image-compress'
import storage from '../../utils/storage'
import { callNative, compressCertificationImg } from '@/utils'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { noticeScBindCar } from '@/api/certification'

import {
  postCarRecog,
  bindDrivingLicense,
  postOcrVehiclelicense,
  getManCarMemberInfo,
  getFindCustomerCarList,
  getOrderManCarVerify
} from '@/api/api'
import AudiButton from '@/components/audi-button'
import dayjs from 'dayjs'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)
  .use(ActionSheet)
  .use(Grid)
  .use(GridItem)
  .use(VanImage)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      show: false, // 上传拍照提示
      showConfirmsubmitAction: false,
      uploadType: 1, // 上传识别类型 1=行驶证，2= 发票
      isPhotograph: true, // 拍照页面
      isFileOK: false, // 上传成功页面
      isChange: false, // 修改页面

      ocrVehiclelicenseModel: {}, // 行驶证识别结果

      updateVehicleLicenseMainOwner: '', //	修改后-所有人【必填】		false
      updateVehicleLicenseMainPlateNum: '', //	修改后-车牌号码【必填】		false
      updateVehicleLicenseMainRegisterDate: '', //	修改后-注册日期【必填】		false
      updateVehicleLicenseMainVin: '', //	修改后-车辆识别代号【必填】		false

      ocrInvoiceVehiclelicenseModel: {}, // 发票识别结果
      updateVehicleInvoiceBuyer: '', //	修改后-购买方名称【必填】		false
      updateVehicleInvoiceBuyerId: '', //	修改后-购买方名称的身份证号或组织机构代码【必填】		false
      updateVehicleInvoiceCarVin: '', //	修改后-车辆识别代号/车架号【必填】		false
      updateVehicleInvoiceIssueDate: '', //	修改后-开票日期【必填】

      carMemberInfoModel: {}, // 会员信息

      base64: '',
      bindingType: 1, // 绑定类别，1:车主 2:用车人【必填】
      carOwnType: 1, // 车辆拥有类别，0：非私家车，1：私家车【必填】
      showBindingTypeAndcarOwn: 1, // 0显示绑定类别，1显示车辆拥有类别
      modalshow: false,
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      vin: '',
      customerCarList: [],
      modalshowInvoice: false, // 车辆类型弹框
      carType: 0, // 车辆类型，0：新增车辆，1：二手车
      onUploadOcrIdCardType: 1,

      beforeConfirmationsubmitAction: false,
      isWrong: false,
      isWrong1: false,

      isWrongTime1: false,
      isWrongTime2: false,
      dialogShow: false,
      carMemberRelId:null,
    }
  },
  created() {},
  mounted() {
    this.getFindCustomerCarList()
    // console.log('query.data', this.$route.query.data)
    let query = {}
    if (this.$route.query.data) {
      query = JSON.parse(this.$route.query.data)
      const plateNum = query.plateNum
      this.updateVehicleLicenseMainPlateNum = plateNum
      // console.log('plateNum', plateNum)
      if (plateNum) {
        this.showConfirmsubmitAction = true
        // this.beforeConfirmationsubmitAction=true

        this.initObj(query)
      }
      this.isChange = query.isChange
      this.base64 = query.image
      this.isPhotograph = false
      this.isFileOK = true
      // this.isChange = false
    }
    this.carType = this.$route.query.carType || 0

    if(sessionStorage.getItem('currentCar') && JSON.parse(sessionStorage.getItem('currentCar'))){
       this.carMemberRelId = JSON.parse(sessionStorage.getItem('currentCar')).carMemberRelId
    }
  },
  destroyed() {},
  methods: {
    ...mapGetters(['getDevice']),
    focusHandle() {
      const {
        owner,
        plateNum,
        vin,
        vehicleType,
        userCharacter,
        address,
        date,
        engineNo,
        model,
        registerDate
      } = this.ocrVehiclelicenseModel
      const param = {
        owner,
        plateNum,
        vin,
        vehicleType,
        userCharacter,
        address,
        date,
        engineNo,
        model,
        registerDate,
        // bindingType:this.carOwnType == 0 ? 2 : this.bindingType,
        bindingType:
          this.carMemberInfoModel.name === this.updateVehicleLicenseMainOwner
            ? 1
            : 2,
        carOwnType: this.carOwnType,
        carType: this.carType,
        image: this.base64,
        updateVehicleLicenseMainOwner: this.updateVehicleLicenseMainOwner,
        updateVehicleLicenseMainPlateNum: this.updateVehicleLicenseMainPlateNum,
        updateVehicleLicenseMainVin: this.updateVehicleLicenseMainVin,
        updateVehicleLicenseMainRegisterDate:
          this.updateVehicleLicenseMainRegisterDate,
        isChange: this.isChange
      }
      this.$router.push({
        path: '/certification/input-car-no',
        query: {
          type: 'addCarNo',
          carType: this.carType,
          data: JSON.stringify(param)
        }
      })
    },
    async getFindCustomerCarList() {
      // 查询车辆是否绑定
      const { data } = await getFindCustomerCarList({
        bindingStatus: 4,
        bindingType: 0
      })
      if (data.data.length > 0) {
        this.customerCarList = data.data
      }
    },

    formValidatorName(name) {
      if (name.length !== 17) {
        this.isWrong1 = true
        return
      } else {
        this.isWrong1 = false
      }
    },

    formValidatorDates(dataTime) {
      if (!this.isValidDateFormat(dataTime)) {
        this.isWrongTime1 = true
      } else {
        this.isWrongTime1 = false
      }
    },

    async getFile(e) {
      const file = e.target.files[0]

      const reader = new FileReader()
      if (file) {
        // 通过文件流将文件转换成Base64字符串
        reader.readAsDataURL(file)
        // 转换成功后
        const this1 = this
        reader.onloadend = function () {
          // 输出结果
          this1.formData(file, reader.result)
        }
      }
    },
    async formData(file, base64) {
      // const htmlImageCompress = new HtmlImageCompress(file, {
      //   quality: 0.4
      // }).then((result) => {
      //   console.log(result)
      //   const formData = new FormData()
      //   formData.append('file', result.file)
      //   formData.append('fileType', 1)
      //   formData.append('categoryType', 1)
      //   formData.append('fileCategoryId', 1)
      //   formData.append('carType', this.carType)
      //   this.postOcrVehiclelicense(formData)
      // })
      // console.log(file,'filefilefile')

      if (file) {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('fileType', 1)
        formData.append('categoryType', 1)
        formData.append('fileCategoryId', 1)
        formData.append('carType', this.carType)
        this.postOcrVehiclelicense(formData)
        //通过文件流将文件转换成Base64字符串

        let params = {
          content: base64,
          file: file
        }
        const fileTemp = await compressCertificationImg(params)
        var readers = new FileReader()
        readers.readAsDataURL(fileTemp)
        //转换成功后
        const this2 = this
        readers.onloadend = function () {
          //输出结果
          this2.base64 = readers.result
        }
      }
    },
    // 识别行驶证
    async postOcrVehiclelicense(formData) {
      this.show = false
      this.$store.commit('showLoading')
      // const params = {
      //   carType: this.carType,
      //   formData
      // }
      const { data } = await postOcrVehiclelicense(formData)
      this.$store.commit('hideLoading')

      if (data.code === '00') {
        this.isPhotograph = false
        this.isFileOK = true
        this.isChange = false
        this.initObj(data.data)
      } else {
        // callNative('toast', { type: 'fail', message: data.message })
        this.$router.push({
          path: '/certification/driving-certification-error',
          query: {
            carMemberRelId: this.carMemberRelId
          }
        })
      }
    },
    initObj(e = {}) {
      console.log('initObj', e)
      this.ocrVehiclelicenseModel = e
      this.updateVehicleLicenseMainOwner =
        e.updateVehicleLicenseMainOwner || e.owner
      this.updateVehicleLicenseMainPlateNum =
        e.updateVehicleLicenseMainPlateNum || e.plateNum
      this.updateVehicleLicenseMainRegisterDate =
        e.updateVehicleLicenseMainRegisterDate || e.registerDate
      this.updateVehicleLicenseMainVin = e.updateVehicleLicenseMainVin || e.vin
    },
    // 修改
    onReturnUpdate() {
      // 修改状态点击是重新上传
      // if (this.isChange) {
      //   this.isPhotograph = true
      //   this.isFileOK = false
      //   this.isChange = false
      // } else {
      //   this.isPhotograph = false
      //   this.isFileOK = false
      //   this.isChange = true
      // }

      // this.isChange = true
      this.showConfirmsubmitAction = true
      this.beforeConfirmationsubmitAction = false
    },

    cancel() {
      this.beforeConfirmationsubmitAction = false
      this.showConfirmsubmitAction = false
      // this.isChange=false
      this.isFileOK = true
    },

    onUploadOcrIdCard() {
      if (this.customerCarList.length > 0) {
        this.uploadType = this.onUploadOcrIdCardType
        this.show = true
      } else {
        this.uploadType = this.onUploadOcrIdCardType
        this.show = true
      }
    },

    // 上传图
    async onUploadingImg() {
      const data = await callNative('albumCameraEvent', { type: 1 })
      console.log(data, '是否开启相册权限')
      if (data.status) {
        this.$refs.leftFile.click()
      } else {
        this.dialogShow = true
      }
    },
    dialogCancel() {},
    dialogConfirm() {
      callNative('openpage', {})
    },
    isValidDateFormat(dateStr) {
      // 正则表达式严格匹配 YYYY-MM-DD 格式
      const regex = /^\d{4}-\d{2}-\d{2}$/;
      if (!regex.test(dateStr)) return false;

      // 使用 dayjs 验证日期是否有效
      return dayjs(dateStr, 'YYYY-MM-DD', true).isValid();
    },
    // 提交
    onConfirm() {
      // this.showConfirmsubmitAction = true
      if (this.updateVehicleLicenseMainVin.length !== 17) {
        //  callNative('toast', { type: 'fail', message: '身份证/机构代码位数位数有误，请修改' })
        this.isWrong = true
        return
      } else {
        this.isWrong = false
      }

      if (!this.isValidDateFormat(this.updateVehicleLicenseMainRegisterDate)) {
        callNative('toast', { type: 'fail', message: '注册日期格式应为yyyy-mm-dd，请修改' })
        this.isWrongTime1 = true
        return
      }
      if (!this.isValidDateFormat(this.ocrVehiclelicenseModel.date)) {
        callNative('toast', { type: 'fail', message: '发证日期格式应为yyyy-mm-dd，请修改' })
        this.isWrongTime2 = true
        return
      }
      this.beforeConfirmationsubmitAction = true
    },
    // 选择车辆类别
    onCarOwnType(type) {
      this.carOwnType = type
    },
    // 选择用车人
    onBindingType(type) {
      this.bindingType = type
    },

    defSubmitFn() {
      // 提交
      this.postVehicleLicenseRecognition()
    },

    onConfirmBindingTypeAndcarOwn() {
      // if (this.showBindingTypeAndcarOwn === 1 && this.carOwnType !== 0) {
      //   this.showBindingTypeAndcarOwn = 0
      // } else {
      // 提交
      this.modalshow = false
      this.showBindingTypeAndcarOwn = 1

      this.postVehicleLicenseRecognition()
      // }
    },
    beforeConfirmation() {
      if (this.updateVehicleLicenseMainVin.length !== 17) {
        callNative('toast', { type: 'fail', message: 'VIN码位数有误，请修改' })
        return
      }
      if (!this.isValidDateFormat(this.updateVehicleLicenseMainRegisterDate)) {
        callNative('toast', { type: 'fail', message: '注册日期格式应为yyyy-mm-dd，请修改' })
        this.isWrongTime1 = true
        return
      }
      this.showConfirmsubmitAction = false
      this.beforeConfirmationsubmitAction = false

      // this.beforeConfirmationsubmitAction = true
      this.isChange = true
    },

    afterConfirmation() {
      this.updateVehicleLicenseMainOwner = this.ocrVehiclelicenseModel.owner
      this.updateVehicleLicenseMainPlateNum =
        this.ocrVehiclelicenseModel.plateNum
      this.updateVehicleLicenseMainRegisterDate =
        this.ocrVehiclelicenseModel.registerDate
      this.updateVehicleLicenseMainVin = this.ocrVehiclelicenseModel.vin
      this.beforeConfirmationsubmitAction = false
      this.showConfirmsubmitAction = false
      this.isChange = false

      this.isWrongTime1 = false
      this.isWrongTime2 = false
    },
    // 确认提交
    async onConfirmsubmit() {
      const { data } = await getManCarMemberInfo({})
      // 会员信息
      this.carMemberInfoModel = data.data
      this.getOrderManCarVerify().then((res) => {
        if (res && res.code === '00' && res.data.boolType) {
          // 行驶证
          if (
            this.carMemberInfoModel.name ===
              this.updateVehicleLicenseMainOwner &&
            this.carMemberInfoModel.plateNum ==
              this.updateVehicleLicenseMainPlateNum
          ) {
            this.defSubmitFn()
          } else {
            if (this.updateVehicleLicenseMainOwner === '') {
              callNative('toast', { type: 'fail', message: '请填写姓名' })
              return
            }
            if (this.updateVehicleLicenseMainPlateNum === '') {
              callNative('toast', { type: 'fail', message: '请填写车牌号' })
              return
            }
            if (this.updateVehicleLicenseMainVin.length !== 17) {
              callNative('toast', {
                type: 'fail',
                message: 'VIN码位数有误，请修改'
              })
              return
            }
            // // 提交
            if (res.data.bindingType && res.data.buyType) {
              let params = {
                ...res.data
              }
              this.postVehicleLicenseRecognition(params)
            } else {
              this.showConfirmsubmitAction = false
              this.beforeConfirmationsubmitAction = false

              this.modalshow = true
            }
          }
          // if (this.updateVehicleLicenseMainOwner === '') {
          //   callNative('toast', { type: 'fail', message: '请填写姓名' })
          //   return
          // }
          // if (this.updateVehicleLicenseMainPlateNum === '') {
          //   callNative('toast', { type: 'fail', message: '请填写车牌号' })
          //   return
          // }
          // this.modalshow = true
        } else {
          // this.isChange = false
          this.$router.push({
            path: '/certification/driving-certification-error',
            query: {}
          })
        }
      })
    },

    async getOrderManCarVerify() {
      const {
        owner,
        plateNum,
        vin,
        vehicleType,
        userCharacter,
        address,
        date,
        engineNo,
        model,
        registerDate
      } = this.ocrVehiclelicenseModel
      // owner = this.updateVehicleLicenseMainOwner || owner
      // plateNum = this.updateVehicleLicenseMainPlateNum || plateNum
      // registerDate = this.updateVehicleLicenseMainRegisterDate || registerDate
      console.log(
        '测试姓名',
        this.carMemberInfoModel,
        this.updateVehicleLicenseMainOwner
      )
      const param = {
        owner,
        plateNum,
        vin,
        vehicleType,
        userCharacter,
        address,
        date,
        engineNo,
        model,
        registerDate,
        // bindingType: this.carOwnType == 0 ? 2 : this.bindingType,
        bindingType:
          this.carMemberInfoModel.name === this.updateVehicleLicenseMainOwner
            ? 1
            : 2,
        carOwnType: this.carOwnType,
        carType: this.carType,
        image: this.base64,
        updateVehicleLicenseMainOwner: this.isChange
          ? this.updateVehicleLicenseMainOwner
          : this.ocrVehiclelicenseModel.owner,
        updateVehicleLicenseMainPlateNum: this.isChange
          ? this.updateVehicleLicenseMainPlateNum
          : this.ocrVehiclelicenseModel.plateNum,
        updateVehicleLicenseMainVin: this.isChange
          ? this.updateVehicleLicenseMainVin
          : this.ocrVehiclelicenseModel.vin,
        updateVehicleLicenseMainRegisterDate: this.isChange
          ? this.updateVehicleLicenseMainRegisterDate
          : this.ocrVehiclelicenseModel.registerDate
      }
      this.$store.commit('showLoading')
      const { data } = await getOrderManCarVerify(param)
      this.showConfirmsubmitAction = false
      this.beforeConfirmationsubmitAction = false

      this.$store.commit('hideLoading')
      return data
    },
    // 上传行驶证认证
    async postVehicleLicenseRecognition(val) {
      const {
        owner,
        plateNum,
        vin,
        vehicleType,
        userCharacter,
        address,
        date,
        engineNo,
        model,
        registerDate
      } = this.ocrVehiclelicenseModel
      // owner = this.updateVehicleLicenseMainOwner || owner
      // plateNum = this.updateVehicleLicenseMainPlateNum || plateNum
      // registerDate = this.updateVehicleLicenseMainRegisterDate || registerDate
      const carMemberRelId = this.carMemberRelId;
      console.log(carMemberRelId,"carMemberRelId123")
      const param = {
        owner,
        plateNum,
        vin,
        vehicleType,
        userCharacter,
        address,
        date,
        engineNo,
        model,
        registerDate,
        // bindingType: val?val:this.carOwnType == 0 ? 2 : this.bindingType,
        bindingType: val
          ? val.bindingType
          : this.carMemberInfoModel.name === this.updateVehicleLicenseMainOwner
          ? 1
          : 2,
        carOwnType: val ? val.buyType : this.carOwnType,
        carType: this.carType,
        image: this.base64,
        updateVehicleLicenseMainOwner: this.isChange
          ? this.updateVehicleLicenseMainOwner
          : this.ocrVehiclelicenseModel.owner,
        updateVehicleLicenseMainPlateNum: this.isChange
          ? this.updateVehicleLicenseMainPlateNum
          : this.ocrVehiclelicenseModel.plateNum,
        updateVehicleLicenseMainVin: this.isChange
          ? this.updateVehicleLicenseMainVin
          : this.ocrVehiclelicenseModel.vin,
        updateVehicleLicenseMainRegisterDate: this.isChange
          ? this.updateVehicleLicenseMainRegisterDate
          : this.ocrVehiclelicenseModel.registerDate,

        // 补传身份类型
        authType: !carMemberRelId ? 1 : 0,
        ...(carMemberRelId ? { carMemberRelId } : {})
      }
      this.$store.commit('showLoading')
      const { data } = await bindDrivingLicense(param)
      this.showConfirmsubmitAction = false
      this.beforeConfirmationsubmitAction = false

      this.$store.commit('hideLoading')
      console.log(data, '上传成功数据')
      if (data.code === '00' || data.code === '44' || data.code === '200') {
        storage.setPlus('certificationType', 0)
        const params = {
          reviewStatus: data.code == '44' ? '0' : '1',
          updateVehicleLicenseMainOwner:
            data.data.updateVehicleLicenseMainOwner,
          updateVehicleLicenseMainPlateNum:
            data.data.updateVehicleLicenseMainPlateNum,
          updateVehicleLicenseMainVin: data.data.updateVehicleLicenseMainVin,
          updateVehicleLicenseMainRegisterDate:
            data.data.updateVehicleLicenseMainRegisterDate,
          updateVehicleLicenseMainOwner:
            data.data.updateVehicleLicenseMainOwner,
          updateVehicleLicenseMainOwner: data.data.updateVehicleLicenseMainOwner
        }
        storage.set('certificationList', JSON.stringify(params))

        try {
          const scInfo = await callNative('getUserInfo', {})
          console.log('scInfoUserInfo', scInfo)
          noticeScBindCar(vin, scInfo.userId)
        } catch (e) {
          console.log(e)
        }
        // this.isChange = false
        this.$router.push({
          path: '/certification/driving-certification-in',
          query: {
            carMemberRelId: null
          }
        })
      } else {
        // if (this.isChange) {
        //   this.isPhotograph = false
        //   this.isFileOK = false
        //   this.isChange = true
        // } else {
        //   this.isPhotograph = true
        //   this.isFileOK = false
        //   this.isChange = false
        // }
        // this.isPhotograph = true
        this.isFileOK = true
        this.isWrong = false
        this.isWrong1 = false
        this.beforeConfirmationsubmitAction = false
        if (data.code === '78') {
          callNative('toast', {
            type: 'fail',
            message: '证件类型有误，请重新上传正确的证件类型'
          })
        } else if (data.code === '53') {
          callNative('toast', {
            type: 'fail',
            message: '证件有误，请重新上传正确的证件'
          })
        } else if (data.code === '61') {
          callNative('toast', {
            type: 'fail',
            message: '请确定信息正确性，并填写完整'
          })
        } else if (data.code === '51') {
          callNative('toast', {
            type: 'fail',
            message: '车辆识别代码(VN)位数有误，请修改或重新识别证件'
          })
        } else if (data.code === '05') {
          callNative('toast', {
            type: 'fail',
            message: '证件日期有误，请修改或重新识别证件'
          })
        } else if (data.code === '35') {
          callNative('toast', {
            type: 'fail',
            message: '该证件非二手车行驶证，请更换证件重新认证'
          })
        } else if (data.code === '49') {
          callNative('toast', {
            type: 'fail',
            message: '系统正在处理，请勿重复提交'
          })
        } else if (data.code === '10') {
          callNative('toast', {
            type: 'fail',
            message: '车辆识别代码(VN)有误，请修改或重新识别证件'
          })
        } else if (data.code === '81') {
          callNative('toast', {
            type: 'fail',
            message: '证件号码有误，请修改或重新识别证件'
          })
        } else if (data.code === '82') {
          callNative('toast', {
            type: 'fail',
            message: '证件号码有误，请修改或重新识别证件'
          })
        } else if (data.code === '85') {
          callNative('toast', {
            type: 'fail',
            message:
              '该车辆尚未交车，请联系购车人前往APP整车订单完成交车确认后再进行车辆认证'
          })
        } else if (data.code === '86') {
          callNative('toast', {
            type: 'fail',
            message:
              '该车辆尚未交车，请前往APP整车订单完成交车确认后再进行车辆认证'
          })
        } else if (data.code === '48') {
          callNative('toast', {
            type: 'fail',
            message: '车辆识别代码(VIN)有误，请修改或重新识别证件'
          })
        } else if (data.code === '63') {
          callNative('toast', {
            type: 'fail',
            message: '车辆类别有误，请确认车辆类别是私家车或公务车'
          })
        } else if (data.code === '65') {
          callNative('toast', {
            type: 'fail',
            message:
              '车主姓名有误，请保证与当前已认证的车主姓名一致，若有误请修改或重新认证'
          })
        } else if (data.code === '64') {
          callNative('toast', {
            type: 'fail',
            message:
              '认证该车辆的账户数量已满，暂时无法认证。可联系其他车辆认证账户解除绑定'
          })
        } else if (data.code === '42') {
          callNative('toast', {
            type: 'fail',
            message: '证件号或姓名有误，请修改或重新识别证件'
          })
        } else if (data.code === '52') {
          callNative('toast', {
            type: 'fail',
            message: '车辆识别码VIN有误，请与当前已认证的车辆识别码保持一致'
          })
        } else if (data.code === '20') {
          callNative('toast', {
            type: 'fail',
            message: '您已绑定过该车，无需重复绑定'
          })
        } else if (data.code === '23') {
          callNative('toast', {
            type: 'fail',
            message: '该车辆为私家车，无法认证为公务车，请重新提交'
          })
        } else if (data.code === '83') {
          callNative('toast', {
            type: 'fail',
            message: '证件号码有误，请修改或重新识别证件'
          })
        } else if (data.code === '84') {
          callNative('toast', {
            type: 'fail',
            message: '证件日期有误，请修改或重新识别证件'
          })
        } else if (data.code === '57') {
          callNative('toast', {
            type: 'fail',
            message: '车牌号有误，请修改或重新识别证件'
          })
        } else if (data.code === '72') {
          callNative('toast', {
            type: 'fail',
            message: '请确定信息正确性，并填写完整'
          })
        } else {
          callNative('toast', { type: 'fail', message: data.message })
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
.driving-certification {
  // height: 100%;
  padding: 0 16px;
  padding-bottom: 100px;
}
.item-p {
  color: #333333;
  font-size: 16px;
  font-family: 'Audi-WideBold';
  line-height: 24px;
  margin: 0;
  margin-bottom: 14px;
  margin-top: 16px;
}

.upload_tip {
  background: rgb(238, 240, 243);
  padding: 6px 16px;
  color: #333333;
  font-size: 12px;
  font-family: 'Audi-WideBold';
  margin: 0 -16px;
  line-height: 20px;
}

.btn {
  color: #000;
  // font-family: "Audi-WideBold";
  font-size: 16px;
}
.item-img-updata {
  // padding-right: 16px;
  // padding-left: 16px;
  width: 100%;
  height: 100%;
  // margin: 0 15px;
}
.item-title-bold-updata {
  width: 100%;
  font-size: 14px;
  color: #333333;
  font-family: 'Audi-WideBold';
  margin-bottom: 16px;
  text-align: center;
}
.item-img {
  // padding-right: 16px;
  // padding-left: 16px;
  // width: 92%;
  height: 200px;
  // margin: 0 15px;
  margin-top: 16px;
}

.item-title-bold {
  width: 100%;
  font-size: 16px;
  color: #000;
  font-family: 'Audi-WideBold';
  margin-bottom: 16px;
}

.line_content-result {
  margin-top: 16px;
  margin-bottom: 24px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .s_line {
    width: 100%;
    height: 1px;
    margin-right: 16px;
    background: #e5e5e5;
  }

  .text_center {
    text-align: center;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #999;
    font-size: 14px;
  }

  .e_line {
    width: 100%;
    height: 1px;
    margin-left: 16px;
    background: #e5e5e5;
  }
}
.line-border-connent-title {
  font-size: 16px;
  color: #000000;
  font-family: 'Audi-WideBold';
  line-height: 24px;
  margin-bottom: 16px;
}
.line_content {
  // margin-top: 10px;
  // margin-bottom: 16px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .s_line {
    width: 100%;
    height: 1px;
    margin-right: 16px;
    background: #e5e5e5;
  }

  .text_center {
    text-align: center;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #999;
    font-size: 14px;
  }

  .e_line {
    width: 100%;
    height: 1px;
    margin-left: 16px;
    background: #e5e5e5;
  }
}

.interval_line {
  margin-top: 30px;
  margin-left: -16px;
  margin-right: -16px;
  height: 8px;
  background: #f2f2f2;
}

.line {
  padding-top: 16px;
  padding-bottom: 16px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 16px;
    color: #000;
  }

  .item_btn {
    color: #666;
    font-size: 14px;
  }

  .btn-change {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn-icon {
      align-items: center;
      font-size: 12px;
      color: #000;
    }
  }
}

.btn-delete-wrapper {
  margin: 16px;
}

.hide_file {
  display: none;
}

.line-border-bottom {
  // width: 100%;
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f2f2f2;

  .name {
    width: 90px;
    font-size: 16px;
    color: #000;
    font-family: 'Audi-Normal';
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #000;
  }
}

.line-border-bottom-result {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
  padding: 16px 0;
  margin-bottom: 16px;
  .name {
    width: 90px;
    font-size: 16px;
    color: #000000;
    font-family: 'Audi-Normal';
    line-height: 24px;
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    padding: 0;
  }
}
.item-title-bold-updata-title {
  font-size: 16px;
  padding-left: 16px;
  margin-top: 24px;
  color: #000000;
  margin-bottom: 16px;
  width: 90%;
  font-family: 'Audi-WideBold';
  line-height: 24px;
}
.item-title-bold-updata-connent {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f2f2f2;
  margin-bottom: 16px;
  .name {
    width: 90px;
    font-size: 16px;
    color: #000000;
    font-family: 'Audi-Normal';
    line-height: 24px;
    margin-right: 10px;
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    padding: 0;
  }
}
.item-title-bold-point-out {
  text-align: center;
  justify-content: center;
  font-size: 18px;
  padding-top: 24px;
  line-height: 26px;
  margin-bottom: 8px;
  font-family: 'Audi-WideBold';
}
.item-title-bold-point-out-text {
  font-size: 14px;
  line-height: 22px;
  color: #333333;
  font-family: 'Audi-Normal';
  text-align: center;
}
.line-border-bottom-affirm {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
  padding: 16px 0;
  margin: 0 16px;
  margin-bottom: 11px;
  .name {
    width: 90px;
    font-size: 16px;
    color: #000000;
    font-family: 'Audi-Normal';
    line-height: 24px;
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    padding: 0;
  }
}
.line-border-bottom-affirm-bottom {
  margin-bottom: 25px;
}
.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0px;
  padding-bottom: 38px;
  left: 0px;
  position: fixed;

  .div-btn {
    width: 100%;
    margin: 0 2px;
  }
}

.bottom_styles {
  width: 100%;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 25px;

  .div-btn {
    width: 100%;
    margin: 0 2px;
  }
}

._modal {
  width: 343px;
  background: #ffffff;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;

  .modal-title {
    font-size: 18px;
    font-weight: 500;
    color: #1a1a1a;
    margin-bottom: 16px;
    font-family: 'Audi-WideBold';
  }

  .service-line {
    width: 295px;
    height: 48px;
    display: flex;
    align-items: center;

    .ch-change {
      padding-right: 16px;

      .ch-icon {
        width: 20px;
        height: 20px;
      }
    }

    .title-bold {
      font-size: 16px;
      color: #000;
      font-weight: normal;
      font-family: 'Audi-Normal';
    }
  }

  .modal-confirm {
    margin-top: 24px;
    width: 85%;
    height: 50px;
    background: #1a1a1a;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.is-text-wang {
  padding: 0;
  line-height: 24px;
  margin: 0;
  border-top: 1px solid #eb0d3f;
  color: #eb0d3f;
  font-size: 12px;
  margin-top: -16px;

  > img {
    width: 24px;
    height: 24px;
    // margin-right: 4px;
  }
}

.is-text-wangs {
  padding: 0;
  line-height: 24px;
  border-top: 1px solid #eb0d3f;
  color: #eb0d3f;
  font-size: 12px;
  margin-top: -16px;
  margin-left: 16px;
  margin-right: 16px;

  > img {
    width: 24px;
    height: 24px;
    // margin-right: 4px;
  }
}
// ::v-deep .van-action-sheet {
//   height: 66%;
// }

::v-deep .van-cell {
  line-height: 0;
}
</style>
