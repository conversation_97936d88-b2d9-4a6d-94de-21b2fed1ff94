import request from '@/router/axios'
import api from '../config/url'

const baseUrl = api.BaseApiUrl

function getUserToken() {
  const token = localStorage.getItem('token')
  let isToken = {}
  if (token) {
    isToken = {
      'x-access-token': token
    }
  } else {
    isToken = {}
  }
  return isToken
}

// 佩戴身份徽章
export const wearIdentity = (carModel, wearState) => request({
  url: `${baseUrl}/api-wap/audi-bbs/api/member/wearIdentity`,
  method: 'POST',
  headers: {
    'x-access-token': localStorage.getItem('token'),
    'X-User-Id': '6100000001010483'
  },
  data: {
    carModel,
    wearState
  }
})

// 徽章佩戴
export const wearBadge = (carType, orderId, wearState) => request({
  url: `${baseUrl}/api-wap/audi-bbs/api/member/wearbadge`,
  method: 'POST',
  headers: {
    'x-access-token': localStorage.getItem('token'),
    // 'x-access-token': 'dhUj6ET8TJAACwNu_t3eg6fyvtzd4a-z',
    'X-User-Id': '6100000001010483'

  },
  data: {
    carType,
    orderId,
    wearState
  }
})

// 获取徽章信息
export const getUserAboutInfo = (data) => request({
  url: `${baseUrl}/api-wap/audi-bbs/api/member/getUserAboutInfo/getUserInfoById?userId=${data}`,
  method: 'GET',
  // headers: {
  //   'x-access-token': localStorage.getItem('token')
  //   // 'x-access-token': 'dhUj6ET8TJAACwNu_t3eg6fyvtzd4a-z'

  // }
  headers: getUserToken()
})

// 获取用户所有的徽章信息
export const getAllBadges = (data) => request({
  url: `${baseUrl}/api-wap/audi-bbs/api/member/getAllBadges?type=${data}`,
  method: 'GET',
  headers: getUserToken()
})

// 获取用户信息
export const getUserInfo = () => request({
  url: `${baseUrl}/api-wap/audi-bbs/api/member/getUserInfo`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
    // 'x-access-token': 'dhUj6ET8TJAACwNu_t3eg6fyvtzd4a-z'
  }
})
