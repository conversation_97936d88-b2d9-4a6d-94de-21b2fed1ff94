<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-09-29 16:20:27
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-09-30 15:39:38
 * @FilePath     : \src\view\limited-number\credentials\showcase\q5.vue
 * @Descripttion :
-->
<template>
  <div
    class="limit-number-box q5"
  >
    <div class="_text1">
      上汽奥迪 {{ modelShortName }} 专属认证标徽
    </div>
    <div class="_img1">
      <img src="@/assets/img/icon-q5e1.png">
    </div>
    <div class="_title">
      尊敬的 {{ modelShortName }} <p>{{ numbers }}</p> 号车主
    </div>
    <div class="_text2">
      <p>欢迎您加入上汽奥迪大家庭</p>
      <p>让我们以科技重构艺术 以艺术再造生活</p>
      <p class="_text-bold">
        艺创未来
      </p>
    </div>
    <div style="display: flex;justify-content: center;">
      <div class="img_box">
        <div class="_number">
          <img
            v-for="(item,index) in numbers"
            :key="index"
            :src="require(`@/assets/limitNumber/number${item}.png`)"
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    numbers: {
      type: String,
      default: ''
    },
    modelShortName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="less" scoped>
  .limit-number-box {
    color: #fff;
    text-align: center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    height: 100%;
    overflow: hidden;
    &.q5 {
      background-image: url("../../../../assets/img/q5e-back.png");
    }
    ._text1 {
      font-size: 8px;
      margin-top: 13vh;
      font-family: "Audi-ExtendedBold";
      color: #333;
      letter-spacing: 0.5px;
      width: 100%;
      text-align: center;
    }

    ._img1 {
      width: 100%;
      text-align: center;
      margin-top: 20px;

      img {
        width: 30vw;
      }
    }

    ._text2 {
      font-size: 14px;
      font-family: "Audi-Normal";
      font-weight: 300;
      color: #616161;
      letter-spacing: 1px;
      width: 100%;
      text-align: center;

      ._text-bold{
        color: #464646;
        font-family: "Audi-WideBold";
      }
    }

    ._title {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      font-family: "Audi-ExtendedBold";
      letter-spacing: 0.5px;
      font-size: 14px;
      margin: 9vh 0;

      p {
        display: inline-block;
        margin: 0 5px;
        padding: 0 5px;
        border-bottom: 2px solid #333;
        background: linear-gradient(to bottom, #63390c, #ca8b38);
        -webkit-background-clip: text;
        color: transparent;
      }
    }

    .img_box {
      background-image: url("../../../../assets/img/icon-q5e2.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 210px;
      height: 81px;
      position: relative;
      margin-top: 35px;

      ._number {
        position: absolute;
        top: 48px;
        right: 72px;
        display: flex;
        align-items: center;

        img {
          width: 10px;
          height: 13px;
          object-fit: contain;
        }
      }
    }
  }
</style>
