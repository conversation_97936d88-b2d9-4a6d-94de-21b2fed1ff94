<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-01-30 11:04:33
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-11-30 11:42:52
 * @FilePath     : \src\view\contract\enterprise\finished.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['contract-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      listening-emit-back
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`, height: `calc(100vh - ${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px)`}"
    >
      <div
        class="contract-box"
        data-flex="dir:top"
      >
        <dl
          class="dl"
          data-flex="dir:top"
          data-block
        >
          <dd class="dd">
            <ul>
              <li
                class="li"
                style="margin: 0;"
              >
                <p class="p">
                  您已选择线上签署合同，请耐心等待上汽奥迪工作人员于第三方合同签署平台（法大大SAAS平台）发起签署流程，线上认证通过即可签署
                </p>
              </li>
            </ul>
          </dd>
        </dl>
        <div
          class="affirm-order-info-box"
          data-flex="cross:bottom main:justify"
        >
          <div
            class="lan-button-box black-button"
          >
            <van-button
              @click="$router.push({ name: 'model-detail', query: { orderId,orderType } })"
              class="lan-button"
            >
              查看订单详情
            </van-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import {
  Button
} from 'vant'
import HeaderCustom from '@/components/header-custom.vue'

Vue.use(Button)
export default {
  components: {
    'header-custom': HeaderCustom
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      orderId: '',
      orderType: ''
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      const { orderId,orderType } = this.$route.query
      if (orderId) {
        this.orderId = orderId
      }
      if(orderType) {
        this.orderType = orderType
      }
    },
    handleLeftBack() {
      const { orderId,orderType } = this
      this.$router.push({ name: 'model-detail', query: { orderId,orderType } })
    }
  }
}
</script>
<style lang="less" scoped>
.contract-wrapper {
  .contract-box {
    height: 100%;
    font-size: 14px;
    color: #333;
    line-height: 22px;
    .dl {
      margin: 0;
      padding: 16px;
      .dd {
        margin: 0;
        .li {
          margin: 24px 0 0 0;
          .h4, .p {
            margin: 0 0 4px 0;
          }
          .h4 {
            font-family: 'Audi-WideBold';
            font-weight: normal;
            .spu {
              color: #999;
              font-size: 12px;
            }
          }
          // &:last-child {
          //   margin-top: 24px;
          // }
        }
      }
    }
  }
  .affirm-order-info-box {
    padding: 16px 16px 50px 16px;
  }
}
</style>
