<template>
  <div class="unsubscribeSucceed">
    <div class="car-wrapper" v-if="carInfo">
      <div class="content-wrappers">
        <div class="model-name">
          {{ carInfo.modelNameCn ? carInfo.modelNameCn : '' }}
        </div>
        <div class="plate-number">
          {{ carInfo.plateNumber ? carInfo.plateNumber : ' ' }}
        </div>

        <div class="car-status">
          {{ showGidType(carInfo) }}
        </div>
      </div>

      <div class="navgation-wrapper">
        <img
          v-if="carInfo.headImageUrl"
          :src="
            $loadWebpImage(
              (carInfo.headImageUrl || '').includes('http')
                ? carInfo.headImageUrl
                : baseOssHost + carInfo.headImageUrl
            )
          "
        />
        <img v-else src="@/assets/img/vehicle.png" />
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">账号类型</div>
      <div class="str">
        {{ carInfo && carInfo.bindingType === '2' ? '用车人' : '车主' }}
      </div>
    </div>

    <div class="line-border-bottom">
      <div class="name">VIN码</div>
      <div class="str vin-str">
        <span>{{
          showVin ? vin : vin.substring(0, 3) + '**********' + vin.substring(13)
        }}</span>
        <div>
          <van-icon
            :name="showVin ? 'closed-eye' : 'eye-o'"
            size="24"
            @click="showVin = !showVin"
          />
          <span @click="copyVin">复制</span>
        </div>
      </div>
    </div>

    <div class="line-border-bottom">
      <div class="name">车牌号码</div>
      <div class="str" v-if="plateNumber">
        {{ plateNumber }}
      </div>
      <div v-else class="str" style="color: #999" @click="onAddCarNo">
        <span>请输入您的车牌号</span>
        <van-icon class="btn-icon" name="arrow" size="16px" />
      </div>
    </div>

    <div class="line-border-bottom">
      <div class="name">购车发票</div>
      <div class="str" v-if="hasUploadInvoice">已上传</div>
      <div v-else class="str" style="color: #999" @click="uploadInvoice">
        <span>未上传</span>
        <van-icon class="btn-icon" name="arrow" size="16px" />
      </div>
    </div>

    <div class="line-border-bottom">
      <div class="name">行驶证</div>
      <div class="str" v-if="hasUploadLicence">已上传</div>
      <div v-else class="str" style="color: #999" @click="uploadLicence">
        <span>未上传</span>
        <van-icon class="btn-icon" name="arrow" size="16px" />
      </div>
    </div>
    <!--  -->
    <div class="btn" v-if="reviewStatus === '1'">
      <AudiButton
        @click="onCarUnbinding"
        text="解除绑定"
        color="white"
        font-size="15px"
        height="50px"
      />
    </div>

    <van-popup
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal">
        <div class="modal-title">身份验证</div>

        <div style="font-size: 14px; color: #000; margin-top: 8px">
          {{ phone.substring(0, 3) + '****' + phone.substring(7) }}
        </div>

        <!-- <div class="modal-line">
          <div style="width: 80px; font-size: 16px; color: #000">图形码</div>

          <van-field v-model="verifyCode" placeholder="请输入图形验证码" />
          <img
            style="height: 24px; width: 120px"
            :src="base64"
            @click="getCaptchaImage()"
          />
        </div> -->
        <div class="modal-line">
          <div style="width: 80px; font-size: 16px; color: #000">验证码</div>

          <van-field
            v-model="captcha"
            placeholder="请输入验证码"
            type="digit"
          />
          <AudiButton
            v-show="show"
            @click="onSendSMS"
            :text="'获取验证码'"
            color="white"
            font-size="12px"
            height="24px"
            width="120px"
          />
          <AudiButton
            v-show="!show"
            :text="count + 's'"
            color="white"
            font-size="12px"
            height="24px"
            width="120px"
          />
        </div>
        <div style="display: flex; justify-content: space-between; width: 90%">
          <div style="margin-left: 16px; width: 44%">
            <AudiButton
              @click="modalshow = false"
              :text="'不解绑了'"
              color="white"
              font-size="15px"
              height="50px"
            />
          </div>
          <div style="margin-right: 16px; width: 44%">
            <AudiButton
              @click="onConfirmsubmit"
              :text="'确定'"
              color="black"
              font-size="15px"
              height="50px"
            />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import { Field, Icon, Popup, Image as VanImage, Dialog, Toast } from 'vant'
import { callNative, InsertUrlShearPlate } from '@/utils'
import { postSendSMS, postCarUnbinding } from '@/api/api'
import { getUserCarDetail } from '@/api/certification'
import AudiButton from '@/components/audi-button'
import api from '@/config/url'

Vue.use(Field).use(Icon).use(Dialog).use(Popup).use(Toast)
export default {
  components: {
    AudiButton
  },
  data() {
    return {
      baseOssHost: api.BaseOssHost,
      modalshow: false,
      reviewStatus: '',
      plateNumber: '',
      vin: '',
      ocrOrderDate: '',
      phone: '',
      captcha: '', // 手机验证码
      verifyCode: '', // 图形验证码
      base64: '',
      findCustomerCarList: [],
      show: true,
      count: '',
      timer: null,
      unbindConfirm: false, // 1 已经确认过解绑协议
      carInfo: null,
      showVin: false,
      hasUploadLicence: false,
      hasUploadInvoice: false
    }
  },
  created() {
    const { phone } = this.$route.query
    this.phone = phone

    this.getFindCustomerCarList()

    if (sessionStorage.getItem('unbindConfirm') === '1') {
      this.unbindConfirm = true
      this.onCarUnbinding()
      sessionStorage.removeItem('unbindConfirm')
    }
  },
  mounted() {},
  methods: {
    async getFindCustomerCarList() {
      if (sessionStorage.getItem('currentCar')) {
        const carInfo = JSON.parse(sessionStorage.getItem('currentCar'))
        this.carInfo = carInfo

        this.reviewStatus = carInfo.reviewStatus
        this.plateNumber = carInfo.plateNumber
        this.vin = carInfo.vin
        this.ocrOrderDate = carInfo.ocrOrderDate
        this.$store.commit('showLoading')
        const { data } = await getUserCarDetail(this.carInfo.carMemberRelId)
        this.$store.commit('hideLoading')
        this.hasUploadLicence =
          data.data.certificateLabel.find((ele) => ele.certificateType === '1')
            .certificateStatus === '1'

        this.hasUploadInvoice =
          data.data.certificateLabel.find((ele) => ele.certificateType === '2')
            .certificateStatus === '1'
      }
    },
    showGidType(carInfo) {
      if (carInfo.reviewStatus === '0') {
        return '待审核'
      }
      if (carInfo.reviewStatus === '1') {
        return '已认证'
      }
      if (carInfo.reviewStatus === '2') {
        return '审核被拒'
      }
      if (carInfo.reviewStatus === '3') {
        return '已解绑'
      }
      if (carInfo.reviewStatus === '8') {
        return '已废弃'
      }
    },

    // 添加车牌号
    onAddCarNo() {
      if (this.carInfo.reviewStatus === '1') {
        this.$router.push({
          path: '/aftersales/input-car-no',
          query: { type: 'addCarNo', vin: this.vin }
        })
      } else {
        callNative('toast', {
          type: 'fail',
          message: '车辆认证审核中，请审核通过后再添加车牌号'
        })
      }
    },
    async onCarUnbinding() {
      if (this.unbindConfirm) {
        this.show = true
        clearInterval(this.timer)
        this.timer = null

        this.modalshow = true
        this.getCaptchaImage()
      } else {
        // 跳转到确认页面
        this.$router.push({
          path: '/certification/car-unbind-confirm'
        })
      }
    },
    // 获取图形验证码
    async getCaptchaImage() {
      this.base64 =
        `${api.BaseApiUrl}/api-wap/cop-cms/api/v1/captcha/image?random=202202` +
        `&${new Date().getTime()}`
      console.log(this.base64)
    },
    // 发送短信
    async onSendSMS() {
      // if (!this.verifyCode) {
      //   callNative("toast", { type: "fail", message: "请输入图形验证码" });
      //   return;
      // }

      const { data } = await postSendSMS({ phone: this.phone })
      if (data.code === '00') {
        // 短信发送成功后倒计时
        this.setCountdown()
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    setCountdown() {
      const TIME_COUNT = 60
      if (!this.timer) {
        this.count = TIME_COUNT
        this.show = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.show = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    // 解绑
    async onConfirmsubmit() {
      if (!this.captcha) {
        callNative('toast', { type: 'fail', message: '请输入验证码' })
        return
      }
      this.show = true
      clearInterval(this.timer)
      this.timer = null

      const param = {
        captcha: this.captcha,
        vin: this.vin
      }
      this.$store.commit('showLoading')
      const { data } = await postCarUnbinding(param)
      this.$store.commit('hideLoading')
      if (data.code === '200') {
        setTimeout(() => {
          callNative('vehicleStatusChange', {
            bind: false,
            vin: this.vin,
            scene: this.$store.state.pageFromScene,
            source: this.$store.state.pageSource
          })
          callNative('toast', { type: 'success', message: '解绑成功' })
        }, 2000)
        this.$router.back(-1)
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    copyVin() {
      const res = InsertUrlShearPlate(this.carInfo.vin)
      if (res) {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '复制成功',
          forbidClick: true,
          duration: 800
        })
      } else {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '复制失败',
          forbidClick: true,
          duration: 800
        })
      }
    },
    uploadInvoice() {
      if (this.carInfo.reviewStatus === '1') {
        this.$router.push({
          path:
            '/certification/car-certification?carMemberRelId=' +
            this.carInfo.carMemberRelId
        })
      } else {
        callNative('toast', {
          type: 'fail',
          message: '车辆认证审核中，请审核通过后上传该证件'
        })
      }
    },
    uploadLicence() {
      if (this.carInfo.reviewStatus === '1') {
        this.$router.push({
          path:
            '/certification/driving-certification?carMemberRelId=' +
            this.carInfo.carMemberRelId
        })
      } else {
        callNative('toast', {
          type: 'fail',
          message: '车辆认证审核中，请审核通过后上传该证件'
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/buttons.less');

.unsubscribeSucceed {
  padding: 16px;
  .car-wrapper {
    position: relative;
    padding: 13px 16px;
    aspect-ratio: 343/130;
    background-image: url(../../assets/img/car_mask.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-sizing: border-box;
    .content-wrappers {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .model-name {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 18px;
      color: #1a1a1a;
      line-height: 26px;
      font-family: 'Audi-ExtendedBold';
      /deep/ i {
        font-size: 14px;
        font-weight: bold;
      }
    }
    .plate-number {
      margin-bottom: auto;
      font-size: 14px;
      color: #808080;
      line-height: 22px;
    }

    .car-status {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 53px;
      height: 24px;
      font-size: 11px;
      color: #1a1a1a;
      background: #ffffff;
    }

    .navgation-wrapper {
      img {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 200px;
        aspect-ratio: 200/110;
        object-fit: cover;
      }
    }
  }

  // img {
  //   margin-top: 10px;
  //   width: 48px;
  //   height: 48px;
  // }

  p {
    text-align: center;
    font-size: 16px;
    color: #000000;
    margin-top: 8px;
  }

  .content {
    text-align: center;
    font-size: 14px;
    color: #666;
  }

  .interval_line {
    margin-left: -16px;
    margin-right: -16px;
    height: 8px;
    background: #f2f2f2;
  }

  .line-border-bottom {
    display: flex;
    align-items: center;
    padding: 24px 0 16px 0;
    width: 100%;
    border-bottom: 1px solid #f2f2f2;

    .name {
      width: 90px;
      font-size: 16px;
      color: #4c4c4c;
    }

    .str {
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      font-size: 16px;
      color: #1a1a1a;
    }

    .vin-str {
      > div {
        display: inline-flex;
        align-items: center;
        span {
          margin-left: 12px;
          font-size: 13px;
          color: #000000;
        }
      }
    }
  }
}

.btn {
  text-align: center;
  box-sizing: border-box;
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0px;
  padding: 0 16px;
  padding-bottom: 26px;
  left: 0px;
}

._modal {
  width: 343px;
  background: #ffffff;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;

  .modal-title {
    font-size: 18px;
    font-weight: 500;
    color: #1a1a1a;

    font-family: 'Audi-WideBold';
  }

  .modal-line {
    width: 80%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
    white-space: nowrap;
  }
}
</style>
