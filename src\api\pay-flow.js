import request from '../router/axios'
import api from '../config/url'
import {
  getToken
} from '../utils/auth'

const baseUrl = api.BaseApiUrl

export const getPayFlowImg = () => request({
  url: `${baseUrl}/api-wap/audi-page/api/floors?pageAccessType=2&pageFrontCode=digital-currency-electronic&pageId=1407523556081061889`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})
