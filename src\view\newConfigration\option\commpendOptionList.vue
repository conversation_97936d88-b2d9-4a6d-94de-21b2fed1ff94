<template>
  <div class="root-wrapper">

    <div class='list-wrapper'>
      <div v-for="item in currentData.composePersonalOptions" :key="item.optionCode">
        <div class="card-wrapper">
          <div class="title-wrapper c-flex-between c-bold c-font16">
            <div>{{ item.optionName }}</div>
            <div class="price"> ¥{{ item.price | formatPrice }}
              <div v-if="summerPackage.includes(item.optionCode)" class="summer-price">¥{{  summerPrice | formatPrice }}</div>
              <div v-if='isA7LDiscount && item.optionCode === A7L_INTELLIGENT_AUDIO.optionCode[1]' class="summer-price a7l-price">
                ¥{{  A7L_INTELLIGENT_AUDIO.price9wp | formatPrice }}
              </div>
            </div>
          </div>

          <div class="btn-wrapper c-font12 margin-top">
            <div class="c-font12 text" v-show="a7lFigureKey.includes(item.optionCode)">支持部分手机机型</div>
            <div @click="toOptionDetail(item)">查看详情</div>
          </div>
          <div v-if="summerPackage.includes(item.optionCode)" class="tag-wrapper"> 限时优惠 </div>
        </div>
      </div>
    </div>

    <div class="footer c-footer-shadow c-flex-between c-font14">
      <div class="left">
        <div class="price-wrapper c-bold c-font16">
          ￥{{ currentData.composePrice | formatPrice }}
        </div>
      </div>

      <div class="right">
        <div class="next-btn" :class="{
          'bg-white': isSelected
        }" @click="clickOption()">
          {{ isSelected ? '移除选装' : '添加选装' }}
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import Vue from 'vue'
import { Toast } from 'vant'
import { A7L_FIGURE_KEY, SUMMER_PACKAGE, A7L_INTELLIGENT_AUDIO } from '../util/carModelSeatData'

Vue.use(Toast)
export default {
  computed: {
    ...mapGetters(['pageOptionComposes', 'currentSeriesName']),
    ...mapState({
      composeName: (state) => state.configration.currentComposeName
    }),

    isSelected() {
      const { composeName } = this.$route.query
      return this.composeName === composeName
    },

    // 参考option.vue  summerPrice 实际此页面只有a7会用到
    summerPrice() {
      if (this.currentSeriesName === 'a7l') {
        return 53700
      }
      if (this.currentSeriesName === 'q5e') {
        return 13000
      }
      return 0
    },

    // 是否为 a7l 后排剧院级音响智能屏套装 组合包
    isA7LDiscount() {
      const discount = A7L_INTELLIGENT_AUDIO.optionCode.every((code) => this.currentData.composePersonalOptions.map((i) => i.optionCode).includes(code))
      const length = A7L_INTELLIGENT_AUDIO.optionCode.length === this.currentData.composePersonalOptions.map((i) => i.optionCode).length
      if (discount && length) {
        return true
      }
      return false
    }
  },
  data() {
    return {
      currentData: {},
      summerPackage: SUMMER_PACKAGE,
      a7lFigureKey: A7L_FIGURE_KEY,
      A7L_INTELLIGENT_AUDIO
    }
  },
  mounted() {
    this.getCurrentData()
  },
  methods: {
    getCurrentData() {
      const { composeName } = this.$route.query
      this.currentData = this.pageOptionComposes.find((i) => i.composeName === composeName)
    },

    //  切换选装状态
    async clickOption() {
      if (this.currentData.disabled) {
        Toast('您选择的装备需要您先更换内饰')
        this.$store.commit('updateFooterDesc', {
          desc: '当前所选选装包与所选面料冲突，请重新选择'
        })
        this.$router.back()
        return
      }
      this.$store.dispatch('clickOptionCompose', this.currentData)
      this.$router.back()
    },

    // 进入选装详情页面
    toOptionDetail(item) {
      this.$store.commit('updateCurrentOptionDetail', item)
      this.$router.push({
        path: '/configrationOptionDetail',
        query: {
          tag: 'composePage',
          customBack: 'newConfigration'
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "../../../assets/style/common.less";

.list-wrapper {
  padding: 15px 16px;
}

.card-wrapper {
  position: relative;
  border-radius: 4px;
  border: 1px solid #E5E5E5;
  // height: 144px;
  padding: 14px 16px;
  box-sizing: border-box;
  margin-bottom: 16px;

  &.selected {
    border: 1px solid #000;
  }


  >.title-wrapper {
    color: #000;

    .price {
      position: relative;
      .summer-price {
        //禁止换行
        white-space: nowrap;
        position: absolute;
        top: 103%;
        left: 0;
        text-decoration:line-through;
        color: #999;
        font-size: 15px;

        //特殊的样式,需要覆盖
        &.a7l-price {
          top: 120%;
          right: 0;
          left: auto;
          font-size: 14px;
          color: #ccc;
        }
      }
    }
  }

  >.content-wrapper {
    overflow: hidden;
    height: 66px;
    margin-top: 6px;

    >div {
      line-height: 20px;
    }
  }

  >.btn-wrapper {
    position: relative;
    width: 100%;
    color: #000;
    display: flex;
    justify-content: space-between;

    &.margin-top {
      margin-top: 32px;
    }

    >.text {
      position: absolute;
      bottom: 103%;
      left: 0;
      color: #999999;
    }
  }

  >.tag-wrapper {
    position: absolute;
    right: 0;
    bottom: 0;
    background:#EB0D3F;
    color: #fff;
    font-weight: normal;
    font-size: 12px;
    line-height: 20px;
    box-sizing: border-box;
    text-align: right;
    padding:0 12px 0 18px;
    border-radius: 0 0 3px 0;
    &::before{
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      background-color: #fff;
      border-top: 10px solid transparent;
      border-bottom: 10px solid #EB0D3F;
      border-left: 8px solid transparent;
      border-right: 8px solid #EB0D3F;
    }
  }
}

.footer {
  background: #fff;
  position: fixed;
  bottom: 0px;
  left: 0;
  width: 100%;
  padding: 5px 13px 20px 13px;
  box-sizing: border-box;

  // padding: 10px;
  .left {
    .price-wrapper {
      margin-top: 10px;
    }

    .deposit-wrapper {
      color: #999999;
      margin-top: 4px;
    }
  }

  .right {
    border: 1px solid;

    >.next-btn {
      .c-font16;
      width: 140px;
      line-height: 56px;
      background-color: #000;
      color: #fff;
      text-align: center;

      &.bg-white {
        background-color: #fff;
        color: #000;
      }
    }
  }

  .delivery-text-wrapper {
    position: absolute;
    left: 0;
    bottom: 100%;
    width: 100%;
    line-height: 32px;
    background-color: #F2F2F2;
    padding-left: 16px;
    box-sizing: border-box;

  }

}
</style>
