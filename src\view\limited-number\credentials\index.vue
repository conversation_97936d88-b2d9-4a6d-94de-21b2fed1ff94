<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-09-29 15:05:10
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-02-21 10:29:25
 * @FilePath     : \src\view\limited-number\credentials\index.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['limited-number-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      listening-emit-back
      @handleLeftBack="handleLeftBack"
    >
      <template
        #right
      >
        <p
          class="btn"
          @click="$router.push({ name: 'limited-number-rule-note', query: { seriesCode } })"
        >
          规则
        </p>
      </template>
    </header-custom>
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <limited-number-q5
        v-if="nickCode === 'q5'"
        :numbers="numbers"
        :model-short-name="modelShortName"
      />
      <limited-number-q6
        v-if="nickCode === 'q6'"
        :numbers="numbers"
        :model-short-name="modelShortName"
      />
      <div
        v-if="nickCode === 'q5' && orderStatus === '90'"
        class="digital-collection-entrance"
        @click="handleThoughtJump"
      />
      <digital-collection
        v-if="nickCode === 'q5' && orderStatus === '90'"
        :warm-prompt.sync="digitalCollectionShow"
      />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Dialog, Toast, Popover } from 'vant'
import { mapState } from 'vuex'
import HeaderCustom from '@/components/header-custom.vue'
import LimitedNumberQ5 from './showcase/q5.vue'
import LimitedNumberQ6 from './showcase/q6.vue'
import {
  XIAN_XING_VERSION_Q5
} from '@/config/constant'
import {
  LIMIT_NUMBERS
} from '@/config/conf.data'
import { getHasLimitedNumber } from '@/api/api'
import api from '@/config/url'
import DigitalCollection from './digitalCollection.vue'

Vue.use(Dialog).use(Popover).use(Toast)
const codeType = ['00', '200']
export default {
  components: {
    'header-custom': HeaderCustom,
    'limited-number-q5': LimitedNumberQ5,
    'limited-number-q6': LimitedNumberQ6,
    'digital-collection': DigitalCollection
  },
  data() {
    return {
      imgUrl: '@/assets/img/bgImg.png',
      BaseOssHost: api.BaseOssHost,
      cipCampaign: '',
      colorNameCn: '',
      numbers: '',
      modelBadgeName: '',
      modelShortName: '',
      nickCode: '',
      isChange: false,
      indexList: [],
      data: {
        number: '000',
        carType: '',
        customerTel: null,
        id: '',
        numberDisplay: '000',
        orderNumber: null,
        reserved: false,
        reservedTel: null,
        stage: 1,
        status: 1
      },
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      XIAN_XING_VERSION_Q5,
      seriesCode: '',
      digitalCollectionShow: false,
      orderStatus: ''
    }
  },
  methods: {
    handleLeftBack() {
      const { orderId } = this.$route.query
      this.$router.push({ name: 'money-detail', query: { orderId } })
    },
    async limitedNumbers() {
      const {
        orderId, modelLineCode, seriesCode, limitNumber, orderStatus
      } = this.$route.query
      const { modelBadgeName, modelShortName, nickCode } = LIMIT_NUMBERS.filter((i) => i.seriesCode === seriesCode)[0] || {
        modelBadgeName: '',
        modelShortName: ''
      }
      this.modelBadgeName = modelBadgeName
      this.modelShortName = modelShortName
      this.modelLineCode = modelLineCode
      this.nickCode = nickCode
      this.numbers = limitNumber
      this.seriesCode = seriesCode
      this.orderStatus = orderStatus
      const { data } = await getHasLimitedNumber({ orderId }) // 查询已有限量号
      if (codeType.includes(data.code) && data.data) {
        this.numbers = data.data
      }
    },
    handleThoughtJump() {
      this.digitalCollectionShow = true
    }
  },
  computed: {
    ...mapState({
      carSeries: (state) => state.carSeries
    })
  },
  created() {
    this.limitedNumbers()
  },
  destroyed() {}
}
</script>

<style lang="less" scoped>
  .limit-number-box {
    color: #fff;
    text-align: center;
    background-image: url("../../../assets/img/q5e-back.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    height: 100%;
    overflow: hidden;

    ._text1 {
      font-size: 8px;
      margin-top: 13vh;
      font-family: "Audi-ExtendedBold";
      color: #333;
      letter-spacing: 0.5px;
      width: 100%;
      text-align: center;
    }

    ._img1 {
      width: 100%;
      text-align: center;
      margin-top: 20px;

      img {
        width: 30vw;
      }
    }

    ._text2 {
      font-size: 14px;
      font-family: "Audi-Normal";
      font-weight: 300;
      color: #616161;
      letter-spacing: 1px;
      width: 100%;
      text-align: center;

      ._text-bold{
        color: #464646;
        font-family: "Audi-WideBold";
      }
    }

    ._title {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      font-family: "Audi-ExtendedBold";
      letter-spacing: 0.5px;
      font-size: 14px;
      margin: 9vh 0;

      p {
        display: inline-block;
        margin: 0 5px;
        padding: 0 5px;
        border-bottom: 2px solid #333;
        background: linear-gradient(to bottom, #63390c, #ca8b38);
        -webkit-background-clip: text;
        color: transparent;
      }
    }

    .img_box {
      background-image: url("../../../assets/img/icon-q5e2.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 210px;
      height: 81px;
      position: relative;
      margin-top: 35px;

      ._number {
        position: absolute;
        top: 48px;
        right: 72px;
        display: flex;
        align-items: center;

        img {
          width: 10px;
          height: 13px;
          object-fit: contain;
        }
      }
    }
  }
.page-wrapper,.main-wrapper {
  height: 100vh;
  box-sizing: border-box;
}

.digital-collection-entrance {
  position: fixed;
  right: 5px;
  bottom: 130px;
  width: 85px;
  height: 86px;
  background: url('~@/assets/limitNumber/digital-collection-entrance.png') no-repeat 50% / contain;
}
</style>
