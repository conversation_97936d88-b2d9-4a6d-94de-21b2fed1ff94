<template>
  <div class="swiper-container" >
    <div class="swiper-wrapper">
      <div class="swiper-slide" v-for="item in imgList" :key="item" >
        <div>
          <img :src="item | imgFix(640)" alt="" @load="endLoad">
        </div>
      </div>
    </div>
    <!-- <div class="navigation">
      <div class="swiper-button-prev"></div>
      <div class="swiper-button-next"></div>
    </div> -->
    <!-- <div class="swiper-pagination" /> -->
  </div>
</template>
<script>
import Swiper from 'swiper'

// import Vue from 'vue';
// import { Swipe, SwipeItem } from 'vant';
// Vue.use(Swipe);
// Vue.use(SwipeItem);

export default {
  components: {},
  props: {
    imgList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      mySwiper: null
    }
  },
  watch: {
    imgList() {
      this.$nextTick(() => {
        if (this.mySwiper) {
          this.mySwiper.destroy()
        }
        this.initSwiper()
      })
    }

  },
  mounted() {
    // this.$store.commit('showLoading')
    this.$nextTick(() => {
      this.initSwiper()
    })
  },

  methods: {
    initSwiper() {
      this.mySwiper = new Swiper('.swiper-container', {
        loop: true,
        cache: false,
        speed: 1000,
        autoplay: true,
        direction: 'horizontal',
        pagination: {
          el: '.swiper-pagination',
          type: 'fraction'
        },
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      })
    },
    endLoad() {
      // this.$store.commit('hideLoading')
    }
  },
  beforeDestroy() {
    this.mySwiper.destroy()
    this.mySwiper = null
  }
}
</script>
<style lang="less" scoped>
.swiper-container {
  width: 100%;
  //   height: 220px;
  --swiper-pagination-color: black;
  --swiper-navigation-size: 14px;
  // overflow: visible;

  .swiper-wrapper {
    width: 100%;

    .slide img {
      width: 100%;
      height: 100%;
    }

    .swiper-slide {
      >div {
        position: relative;
      }

      img {
        height: 220px;
        object-fit: contain;
      }

      .ex-img,
      .ex-img {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 2;
      }

      video {
        height: 220px;
        object-fit: contain;
      }
    }
  }

  .navigation {
    margin: 0 auto;
    position: relative;
    width: 40px;
    height: 18px;
  }

  .swiper-pagination {
    position: absolute;
    top: 236px;
  }
}
</style>
