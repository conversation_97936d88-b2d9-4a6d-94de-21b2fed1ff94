/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-10-19 16:43:53
 * @LastEditors: ft-weih <EMAIL>
 * @LastEditTime: 2023-05-24 16:32:10
 * @FilePath     : \src\view\dealer\list\list.js
 * @Descripttion :
 */

import Vue from 'vue'
import {
  Search, Field, CellGroup, Form, Button, Cell, Picker, Popup, Loading, Toast, List, PullRefresh
} from 'vant'

import { mapGetters } from 'vuex'
import HeaderCustom from '@/components/header-custom.vue'
import {
  callNative, getUrlParamObj, getLocationCoordinate, delay, getLocationCityName
} from '@/utils'
import {
  getCity
} from '@/api/detail'
import { getNearestDealerList, getAudiMinipUrl, getAPITimeOutTesting } from '@/api/api'
import { TIMEOUT_MS } from '@/config/conf.data'
import cityList from '@/utils/city'
import getLocationInfo from '@/utils/location'
import network from '@/components/network.vue'
import { networkToast } from '@/utils/timeout'
import { minipLocation } from '@/utils/weixin-js-sdk'

const positionStaticList = ['76621029', '76649019', '76633019', '76600989']

Vue.use(Search).use(Form).use(Button).use(Field)
  .use(CellGroup)
  .use(Cell)
  .use(Picker)
  .use(Popup)
  .use(Loading)
  .use(Toast)
  .use(List)
  .use(PullRefresh)
export default {
  components: {
    'header-custom': HeaderCustom, network
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      city: '',
      location: '0,0',
      labelWidth: 72,
      labelAlign: 'left',
      citesList: [],
      citesName: '',
      citesCode: '',
      citesLocation: '',
      locationLoading: '',
      showCitesPicker: false,
      citesDefaultIndex: 0,
      citesLoading: true,
      citesPickerIndexes: [],
      dealerName: '',
      dealerNameKW: '',
      dealerList: [],
      device: {},
      LOCATION_TIMEOUT_MS: 3000,
      refreshing: false,
      finished: false,
      loading: false,
      params: {
        size: 10,
        pageSize: 1
      }
    }
  },
  created() {
    networkToast()
    this.handleProcessData()

    this.$EventBus.$on('audi_api_order_dealer_list_timeout', () => {
      Toast.clear()
    })
  },
  methods: {
    ...mapGetters(['getDevice']),
    async handleProcessData() {
      getAPITimeOutTesting()
      let isLoadingDealerList = false
      this.citesList = cityList
      const device = this.getDevice() || {}
      const { env } = this.$route.query
      this.locationLoading = '定位中 ...'
      this.device = device
      let city = ''
      let location = ''
      // let { city, location } = this.$route.query || ''
      // if (!city && device?.nativeApp) {
      const LOCATION_TIMEOUT_MS = this.LOCATION_TIMEOUT_MS || TIMEOUT_MS.LOCATION
      // const locationCity = await getLocationInfo(this.LOCATION_TIMEOUT_MS)
      delay(() => {
        if (!city || !location) {
          console.log(`%c [ 获取用户定位超时 => ${LOCATION_TIMEOUT_MS} MS ]-94`, 'font-size:14px; background:#fc011a; color:#fff;')
        }
        if (!isLoadingDealerList) {
          this.getDealerList()
          isLoadingDealerList = true
          this.citesLoading && (this.citesLoading = false)
        }
      }, LOCATION_TIMEOUT_MS)
      console.log('%c [ getLocationCity ]-92', 'font-size:14px; background:#cf222e; color:#fff;', LOCATION_TIMEOUT_MS)
      let locationCity = {}
      if (device?.nativeApp) {
        locationCity = await callNative('getLocationCity', {}) || ''
      }
      if (env === 'minip') {
        // 小程序定位 {indoor_building_id: '', latitude: '34.193916', accuracy: '30.0', indoor_building_floor: '1000', indoor_building_type: '-1', speed: '0.0', longitude: '108.88516', errMsg: 'getLocation:ok'}
        const minipLocationCity = await minipLocation()
        if (minipLocationCity?.latitude && minipLocationCity?.longitude) {
          locationCity.location = `${minipLocationCity?.latitude},${minipLocationCity?.longitude}`
        }
      }
      console.log('%c [ locationCity ]-92', 'font-size:14px; background:#000; color:#fff;', locationCity)
      if (locationCity.location) {
        const arrayLocation = locationCity.location.split(',')
        city = await getLocationCityName([arrayLocation[1] * 1, arrayLocation[0] * 1])
      } else {
        !city && locationCity.city && (city = locationCity.city)
      }
      !location && locationCity.location && (location = locationCity.location)
      // if (!locationCity || !city || !location) {
      //   const { LOCATION_TIMEOUT_MS } = this
      //   delay(() => {
      //     if (!city || !location) {
      //       console.log(`%c [ 获取用户定位超时 => ${LOCATION_TIMEOUT_MS} MS ]-442`, 'font-size:14px; background:#cf222e; color:#fff;')
      //     }
      //     if (this.locationLoading) {
      //       this.locationLoading = ''
      //     }
      //   }, LOCATION_TIMEOUT_MS)
      // }
      // }

      city && (this.city = city)
      location && (this.location = location)
      if (!isLoadingDealerList) {
        isLoadingDealerList = true
        if (!city) {
          this.getDealerList()
        } else {
          const citesCode = await this.handleSearchProvinceOfCity(city, this.citesList) || ''
          if (citesCode) {
            this.citesCode = citesCode
            this.getDealerList()
          } else {
            this.getDealerList()
          }
        }
      }

      this.citesLoading = false
      console.log('%c [ city, location ]-84', 'font-size:14px; background:#cf222e; color:#fff;', city, location)
    },
    async onConfirmCitesPicker(cites) {
      const { citesCode, citesName } = this.handleFilterCitesCode(cites)

      if (citesCode && citesName) {
        this.citesCode = citesCode
        this.citesName = citesName
        // const { geocodes: [LOCATIONC] } = await getLocationCoordinate({ city: citesCode, address: citesName })
        // const { location: { lat, lng } } = LOCATIONC || ''
        // if (lat && lng) {
        //   this.citesLocation = `${lat}/${lng}`
        //   this.location = `${lat},${lng}`
        // }
        // const { dealerNameKW } = this
        this.getDealerList()
      }
      if (this.showCitesPicker) {
        this.showCitesPicker = false
      }
    },
    async handleSearchProvinceOfCity(city, cites = []) {
      let citesCode = ''
      let citesInfo = {}
      const provinceCecity = ['', city]
      const treesToArray = (trees, childrenName = 'children') => trees.reduce((list, item, index) => {
        const { ...argument } = item
        const childrenData = item[childrenName]
        if (childrenData) {
          const [cityData] = childrenData.filter((i, x) => {
            if (i.text === city) {
              this.citesList[index].defaultIndex = x
              return i
            }
          }) || []

          if (cityData && Object.keys(cityData)?.length) {
            this.citesDefaultIndex = index
            provinceCecity[0] = item.text || ''
            if (cityData.code) {
              citesCode = cityData.code
              citesInfo = cityData
            }
          }
        }

        delete argument[childrenName]
        return [...list, argument, ...(childrenData?.length ? treesToArray(childrenData) : [])]
      }, [])

      treesToArray(cites)
      this.citesList[0].defaultIndex = 5
      this.citesCode = citesCode
      // if (citesCode) {
      //   const { geocodes: [LOCATIONC] } = await getLocationCoordinate({ city: citesCode, address: citesInfo.text })
      //   const { location: { lat, lng } } = LOCATIONC
      //   if (lat && lng) {
      //     this.locationLoading = `${lat}/${lng}`
      //     this.location = `${lat},${lng}`
      //   }
      // }

      // eslint-disable-next-line no-nested-ternary
      this.citesName = !citesCode ? '' : (!provinceCecity[0] ? provinceCecity[1] : provinceCecity.join('/'))
      return citesCode
    },
    handleFilterCitesCode(cites, citesList) {
      const [province] = [...citesList || this.citesList].filter((p) => p.text === cites[0]) || []
      let citesCode = ''
      let citesName = ''
      if (province?.children?.length) {
        const [city] = province.children.filter((c) => c.text === cites[1])
        citesCode = city?.code
        citesName = cites.join('/')
        this.citesCode = citesCode
      }
      return { citesCode, citesName }
    },
    async getDealerList(params) {
      params?.onLoad !== 1 && (networkToast())
      const { dealerNameKW, citesCode, location } = this
      const [latitude, longitude] = location?.split(',') || ''

      //! v5.0 购车城市与门店 【是否保留原有定位】，扩展根据当前选择城市为主
      const param = {
        ...(dealerNameKW ? { dealerName: dealerNameKW } : {}),
        ...(citesCode ? { cityCode: citesCode } : {}),
        ...(+latitude ? { latitude } : {}),
        ...(+longitude ? { longitude } : {}),
        ...params,
        ...this.params,
        ifRegionCodeByCity: 1,
        defaultHeadquarters: 1,
        page: 'orderDealerList'
      }

      const { data } = await getNearestDealerList(param)
      Toast.clear()
      this.locationLoading = ''

      // const AudiHeadquarters = []
      if (data.code === '00') {
        // const { cityCode } = param || ''
        const dealerList = data?.data?.map((i, x) => {
          const { distance } = i
          i.distance = distance || 0
          // eslint-disable-next-line no-nested-ternary
          i.distanceStr = distance
            ? distance < 1000 ? `${Math.round(distance)}m` : `${Math.round((distance / 1000) * 10) / 10}km`
            : ''
          // 过滤总部信息
          // if (i.dealerCode === '76600019') {
          //   AudiHeadquarters = [x, i]
          // }
          return i
        }) || []

        this.dealerList = [...dealerList]

        // 下拉刷新 和上拉加载区分
        // this.dealerList = params?.onLoad === 1 ? [...this.dealerList, ...dealerList] : dealerList

        // this.loading && (this.loading = false) // false 执行下次的 onLoad 方法
        this.finished = true
        // this.refreshing && (this.refreshing = false)
        // // 当前无定位显示指定代理商
        // !cityCode && (this.dealerList = this.dealerList.filter((i) => positionStaticList.includes(i.dealerCode)))
        // // 距离由近到远排序
        // this.dealerList.splice(AudiHeadquarters[0], 1).sort((a, b) => a.distance - b.distance)
        // 总部信息排到末尾
        // this.dealerList.push(AudiHeadquarters[1])
      }
    },
    async handlePickDealer(item) {
      const {
        dealerName, cityName, latitude, dealerCode, longitude, provinceName
      } = item
      if (!dealerName || !cityName || !dealerCode) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '当前代理商信息不完善',
          forbidClick: true,
          duration: 500
        })
      }

      // this.$route.params.backCords = {
      //   dealerName, cityName: this.citesName, dealerCode, backFrom: 'dealer-list'
      // }
      item.mineLocationCitesName = this.citesName
      item.mineLocationCitesCode = this.citesCode
      console.log('%c [ this.dealerCode ]-267', 'font-size:14px; background:#cf222e; color:#fff;', this.dealerCode)
      const {
        isluckybag, pageType, scene, idx, from
      } = this.$route.query
      this.$store.commit('updateDealerInfo', item)
      // 福袋车确认单页面更改代理商重置一下交车方式
      if (isluckybag) {
        this.$store.commit('updateDeliveryPattern', {})
      }

      const [citesName] = this.citesName.split('/').reverse() || ['']

      if (pageType && pageType === 'invite') {
        const res = await getAudiMinipUrl()
        window.location = `${res.data.data.configValue}acceptInviteFriends?scene=${scene}&dealerCode=${dealerCode}&dealerName=${dealerName}&idx=${idx}&from=${from}`
      } else {
        this.$route.params.backCords = { citesName }
        this.$router.back()
      }
    },
    onSearch(dealerName) {
      this.getDealerList({ dealerName })
      console.log('%c [ onSearch ]-93', 'font-size:14px; background:#cf222e; color:#fff;', dealerName)
    },
    onClearSearch() {
      this.dealerNameKW = ''
      this.getDealerList()
    },
    onCancel() {
      console.log('%c [ onCancel ]-94', 'font-size:14px; background:#cf222e; color:#fff;')
    },
    onLoad() {
      console.log('%c [ onLoad ]-269', 'font-size:14px; background:#cf222e; color:#fff;')
      this.getDealerList({ onLoad: 1 })
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.refreshing = false
      Toast.clear()
      this.getDealerList()
    },

    networkReload() {
      this.handleProcessData()
    }
  }
}
