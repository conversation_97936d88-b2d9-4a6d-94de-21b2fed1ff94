<template>
  <div class="container">
    <div
      class="item-wrapper"
      v-for="(item, idx) in seriesList"
      :key="idx"
      @click="chooseDealer(item)"
    >
      <div class="content-wrapper flex1">
        <div class="c-font16">
          {{ item.customSeriesName }}
        </div>
        <div style="margin-top: 14px" />
        <div class="c-font16">
          {{ " " }}
        </div>

        <div style="margin-top: 15px" />
      </div>

      <div class="navgation-wrapper">
        <img :src="(ossUrl + item.imageUrl) | audiwebp " alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import { getCustomSeries2 } from "@/api/api";
import baseUrl from "@/config/url";
import storage from "../../utils/storage";

export default {
  data() {
    return {
      ossUrl: baseUrl.BaseOssHost,
      seriesList: [],
      seriesCode: "",
    };
  },

  async mounted() {
    this.getModelLine();
  },

  methods: {
    async getModelLine() {
      const { data } = await getCustomSeries2({});
      this.seriesList = data.data
      this.seriesList = this.seriesList.reverse()
    },

    chooseDealer(item) {
      if (this.seriesCode || this.seriesCode !== item.seriesCode) {
        var ownerVinInfo = storage.get('ownerVinInfo') || "{}"
        storage.set('ownerVinInfo', JSON.stringify({
            vin: JSON.parse(ownerVinInfo).vin,
            seriesCode: item.seriesCode,
            modelCode: JSON.parse(ownerVinInfo).modelCode || '',
            modelLineName: JSON.parse(ownerVinInfo).modelLineName || '',
            seriesName: item.customSeriesName
        }))
      }
      this.$router.back(-1);
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.container {
  padding: 0 16px;
}

.flex1 {
  flex: 1;
}

.item-wrapper {
  .c-flex-between;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
  padding: 16px;
  margin-top: 16px;
}

.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  // margin-left: 15px;
  padding: 5px 0;
}
.navgation-wrapper {
  text-align: center;
  img {
    width: 142px;
    height: 80px;
  }
}
</style>
