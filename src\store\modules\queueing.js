/*
 * <AUTHOR> <PERSON>
 * @Date         : 2023-01-12 11:43:14
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-01-13 16:47:33
 * @FilePath     : \src\store\modules\queueing.js
 * @Descripttion :
 */
/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-12-28 16:42:09
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-12-28 22:38:00
 * @FilePath     : \src\store\modules\queueing.js
 * @Descripttion :
 */

export default {
  state: {
    handleOnOffMap: {
      isRefreshing: false,
      deadline: []
    },
    downloadLivingData: {
      total: 0,
      loaded: 0
    },
    request: [],
    methods: []
  },
  mutations: {
    SET_QUEUE: (state, [key, val]) => {
      state[key] = val
    },
    SET_ONOFF_MAP: (state, [key, val]) => {
      state.handleOnOffMap[key] = val
    },
    SET_DOWNLOAD_LIVING: (state, { total, loaded }) => {
      state.downloadLivingData = { ...state.downloadLivingData, ...{ ...(loaded ? { loaded } : {}), ...(total ? { total } : {}) } }
    }
  },
  actions: {
    SetOnOffMap({ commit, state, dispatch }, data) {
      commit('SET_ONOFF_MAP', data)
    },
    SetQueue({ commit, state, dispatch }, data) {
      commit('SET_QUEUE', data)
    },
    GetOnOffMap({ state: { handleOnOffMap } }, key = '') {
      return handleOnOffMap[key] ?? handleOnOffMap
    },
    GetOnQueue({ state }, key = null) {
      return (key && state[key]) || []
    },
    GetDownloadLivingData({ state: { downloadLivingData } }, key = '') {
      return downloadLivingData[key] ?? downloadLivingData
    },
    SetDownloadLivingData({ state: { downloadLivingData }, commit }, data) {
      commit('SET_DOWNLOAD_LIVING', data)
    }
  }
}
