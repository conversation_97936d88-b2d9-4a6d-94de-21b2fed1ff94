<!-- 选择试驾类型 -->
<template>
  <div class="content">
    <div class="item item2">
      <div
        class="line"
        @click="onTestDrive"
      >
        <div style="font-size: 18px; color: #fff">
          常规试驾
        </div>
        <van-icon
          class="btn-icon"
          name="arrow"
          color="#fff"
          size="16px"
        />
      </div>
    </div>

    <div class="item">
      <div
        class="line"
        @click="onLongCreate()"
      >
        <div style="font-size: 18px; color: #fff">
          超长试驾
        </div>
        <van-icon
          class="btn-icon"
          name="arrow"
          color="#fff"
          size="16px"
        />
      </div>
    </div>

    <model
      :modalshow.sync="modalshow"
      @update:modalshow="submit"
      :title="modelTitle"
      cancel-text="返回"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { Form, Button } from 'vant'
import { callNative } from '@/utils'
import { getVeryLongReservationValidAuth } from '@/api/api'
import AudiButton from '@/components/audi-button'
import model from '@/components/model.vue'

Vue.use(Form).use(Button)

export default {
  components: {
    AudiButton, model
  },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      modalshow: false,
      modelTitle: '该活动仅限小订及以上用户'
    }
  },
  mounted() {

  },
  methods: {
    onTestDrive() {
      this.$router.push({
        path: this.fromType ? '/testdrive/create?fromType=fromPurple' : '/testdrive/create',
        query: {
          idx: 1
        }
      })
    },

    async onLongCreate() {
      // 13、检查用户有没有权限预约超长试驾
      this.$store.commit('showLoading')
      const { data } = await getVeryLongReservationValidAuth({})
      this.$store.commit('hideLoading')
      if (data.code === '200') {
        this.$router.push({
          path: '/testdrive/long-create',
          query: {}
        })
      } else {
        this.modelTitle = data.message
        this.modalshow = true
      }
    },
    submit(isSubmit) {
      this.modalshow = false
      if (!isSubmit) {
        // 返回
        callNative('close', {})
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "../../assets/style/common.less";

.content {
  height: 100%;
  padding: 16px;

  .item {
    height: 193px;
    background-image: url(../../assets/img/Q5e-tron-02.jpg);
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .line {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 161px;
      margin-left: 16px;
      margin-right: 16px;
      white-space: nowrap;
      position: relative;
    }
  }

  .item2 {
     background-image: url(../../assets/img/Q5e-tron-01.jpg);
     margin-bottom: 20px;
  }
}
</style>
