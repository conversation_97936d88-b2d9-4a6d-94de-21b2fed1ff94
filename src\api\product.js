import request from '../router/axios'
import api from '../config/url'
import { getToken } from '../utils/auth'

const baseUrl = api.BaseApiUrl
// 获取submit-token

export const getSubmitToken = () => request({
  url: `${baseUrl}/api-wap/cop-auth/api/v1/submit-tokens`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 提交订单
export const postSubmitOrder = async (data) => {
  const res = await getSubmitToken()
  if (['200', '00'].includes(res.data.code)) {
    return request({
      url: `${baseUrl}/api-wap/audi-eshop/api/v1/orders`,
      method: 'POST',
      headers: {
        'x-access-token': localStorage.getItem('token'),
        'x-submit-token': res.data.data
      },
      data: {
        ...data
      }
    })
  }
}
// 提交支付
export const postPayments = async (data) => {
  const res = await getSubmitToken()
  if (['200', '00'].includes(res.data.code)) {
    return request({
      url: `${baseUrl}/api-wap/cop-order/api/v1/payments`,
      method: 'POST',
      headers: {
        'x-access-token': localStorage.getItem('token'),
        'x-submit-token': res.data.data
      },
      data: {
        ...data
      }
    })
  }
}
