<template>
  <div clas="wrapper">

    <div v-for="item, idx in powerData" :key="item.energySystem" @click="toSelectPower(item, idx)" class="item" :class="{
      selected: currentPower === item.energySystem
    }">
      <!-- 动力名称 -->
      <div class="left c-font14 ">
        <div class="name c-bold">{{ item.energySystem }}</div>
        <div class="price">
          <PreferentialPrice v-if="item.preferential" :preferential="item.preferential" :price="item.price"/>
          <span class="orginal-price" v-else>¥{{ item.price | formatPrice }}起</span>
        </div>
        <div class="version-wrapper">
          <div v-for="version in item.styleVos" :key="version.styleId" class="version-text c-font12">
               {{ addText(version.modelLineList[0])}} {{ version.styleName }}
          </div>
        </div>
      </div>

      <div class="img-wrapper">
        <img :src="ossUrl + item.imageUrl | imgFix(450)" alt="">
      </div>
    </div>

    <EquityImg/>
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState, mapMutations, mapGetters } from 'vuex'
import { Icon } from 'vant'
import { getEnergyStyleList } from '@/configratorApi'
import url from '@/config/url'
import EquityImg from './components/equityImg.vue'
import PreferentialPrice from './components/preferentialPrice.vue'

const ossUrl = url.BaseConfigrationOssHost
Vue.use(Icon)
export default {
  components: {
    EquityImg,
    PreferentialPrice
  },
  data() {
    return {
      ossUrl: ossUrl,
      currentPower: '',
      powerData: []
    }
  },
  computed: {
    ...mapGetters({
      seriesName: 'currentSeriesName',
      currentSeriesId: 'currentSeriesId'
    }),
    ...mapState({
      carIdx: (state) => state.configration.carIdx
    })
  },

  async mounted() {
    this.getPowerData()
  },

  methods: {
    addText(modelLine) {
      // 非mr车型的24款,去除 文案里的24款文案: version === '1' 为mr车型
      if (modelLine.modelYear === '2024') {
        if (modelLine.customSeriesCode === '49') {
          return ''
        }
        return '24款'
      }
      if (modelLine.modelYear === '2025') {
        if (modelLine.customSeriesCode === '49') {
          return ''
        }
        if (modelLine.modelLineCode === 'G6IBCY008') {
          return '25款 quattro'
        }
        return '25款'
      }
      return ''
    },

    // 获取动力数据
    async getPowerData() {
      this.$store.commit('showLoading')
      await this.$store.dispatch('getCarSeriesData')
      const { measureType } = this.$route.query
      /**
       * type
       * 1: 高定
       * 2: 半定
       * 3: 高定+半定
       */
      // 按照需求 a7l 先上混合高定和半定, measureType 对应配置单页的[重新配置]
      let type = null
      if (['a7l', 'q6'].includes(this.seriesName)) {
        if (measureType) {
          type = measureType
        } else {
          type = 3
        }
      } else {
        type = 1
      }
      const res = await getEnergyStyleList(this.currentSeriesId, type)

      // 过滤 q6半定观云型 75c26b8c-aca8-46ad-a258-c5308da5ceb0
      for (const item of res.data.data) {
        item.styleVos = item.styleVos.filter((i) => i.styleId !== '75c26b8c-aca8-46ad-a258-c5308da5ceb0')
      }
      this.powerData = res.data.data
      console.log('%c this.powerData', 'font-size:16px;color:green;', this.powerData)
      this.$store.commit('hideLoading')
    },
    // 获取type
    // getType() {
    //   /**
    //    * 新版
    //    * q5e 根据点击入口 semi-definite 判断type 1 || 2
    //    * a7 和 q6 必定是高定. type = 1
    //    */
    //   if (this.seriesName === 'q5e') {
    //     return this.$storage.getPlus('semi-definite') === '个性定制' ? 2 : 1
    //   }
    //   return 1
    // },

    // 选中动力
    toSelectPower(item, idx) {
      this.currentPower = item.energySystem

      this.$store.commit('updateCurrentVersionData', item.styleVos)
      this.$store.commit('updateCarModelTab', 'version')

      // 埋点
      this.clickPowerSensors(item, idx)
    },

    // 埋点
    clickPowerSensors(item, idx) {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const param = {
        source_module: 'H5',
        car_series: carMap[this.carIdx],
        delivery_type: '定制交付', // 快速交付|定制交付
        select_price: `${item.price}起`,
        select_index: idx + 1,
        power_type: `${carMap[this.carIdx]} ${item.energySystem}`
      }

      // console.log(param, item)
      this.$sensors.track('CC_SelectCar_Power_Click', param)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.item {
  position: relative;
  background: #fff;
  margin-top: 16px;
  padding: 14px 0;
  border: 1px solid transparent;
  overflow: hidden;

  &.selected {
    border: 1px solid #000;
  }

  >.left {
    min-height: 100px;

    >.name {
      padding-left: 24px;
      line-height: 20px;
    }
    >.price {
      margin-top: 2px;
      padding-left: 24px;
      line-height: 20px;
      color:#4C4C4C;

      .orginal-price {
        font-size: 12px;
        transform: scale(0.8);
        transform-origin: 0 center;
        display: block;
      }
    }

    >.version-wrapper {
      margin-top: 8px;

      >.version-text {
        position: relative;
        color: #999999;
        font-size: 12px;
        line-height: 18px;
        margin-left: 30px;
        &::before{
          position: absolute;
          content: '· ';
          right: 101%;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }

  >.img-wrapper {
    position: absolute;
    bottom: -15px;
    right: 0%;
    width: 60%;
  }
}


</style>
