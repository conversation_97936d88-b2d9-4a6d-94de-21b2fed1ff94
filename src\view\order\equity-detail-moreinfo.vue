<template>
  <div
    class="container"
  >
    <!-- <div
      class="content"
      ref="contentcc"
      id="contentcc"
      @click="clickShowImg"
    >
      <img
        class="image"
        v-for="item in dataImage"
        :key="item"
        :src="item"
        alt="详情图"
      >
    </div> -->
    <div
      ref="image"
      @click="clickShowImg"
    >
      <img
        id="image"
        :src="dataImage"
        alt="详情图"
      >
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import 'viewerjs/dist/viewer.css'
import Viewer from 'viewerjs'
import { getUserRightsByCarModelId } from '@/api/api'
import api from '../../config/url'
// import Viewer from '@/utils/lib/viewer.esm'
// import '@/utils/lib/viewer.min.css'

export default {
  data() {
    return {
      BaseOssHost: api.BaseOssHost,
      dataImage: ''
    }
  },
  computed: mapState({
    env: 'env',
    selectCarInfo: 'selectCarInfo'
  }),
  async mounted() {
    const { title } = this.$route.query
    if (title) {
      this.$store.commit('setTitle', title)
    }
    await this.getEquity()


    // 初始化图片查看
    this.clickShowImg()
  },

  methods: {
    async getEquity() {
      const resultId = this.selectCarInfo.modelLineCode
      // if (process.env.NODE_ENV === 'development') {
      //   resultId = 'G4ICF3002'
      // }
      let { orderId } = this.$route.query
      let p = {}
      if(orderId) {
        p = { orderId, carModelId: resultId, type: 4 }
      } else {
        p = { carModelId: resultId, type: 4 }
      }
      const modelYear = this.$route.query?.modelYear || ''
      const { data } = await getUserRightsByCarModelId({...p, modelYear})

      const rights = decodeURIComponent(data.data.rights)
      this.dataImage = []
      if (rights.startsWith('http')) {
        this.dataImage = rights
      } else {
        this.dataImage = this.BaseOssHost + rights
      }
    },
    clickShowImg() {
      const img = this.$refs.image
      if (!img) return console.error(' img 无法找到')
      const viewer = new Viewer(img, {
        className: 'equity-detail',
        backdrop: true,
        button: true,
        inline: false,
        // container: div2,
        rotatable: false,
        toolbar: false,
        title: false,
        navbar: false,
        slideOnTouch: false,
        scalable: false,
        movable: true,
        zoomable: true,
        zoomOnWheel: true,
        toggleOnDblclick: false,
        maxWidth: '90%',
        viewed(e) {
          // const imgCanvas = document.querySelector('.equity-detail .viewer-canvas img')
          // if (imgCanvas) {
          //   imgCanvas.style.margin = 'auto'
          //   // imgCanvas.style.marginTop = '0'
          //   // imgCanvas.style.width = `${document.body.clientWidth}px`
          //   // imgCanvas.style.height = 'auto'
          // }
          viewer.zoomTo(0.5)
        },
        move() {
          // console.log('move')
        }
      })
    },
    clickShowImg1() {
      const viewer = new Viewer(document.getElementById('image'), {
        navbar: false,
        title: false,
        toolbar: false,
        viewed() {
          viewer.zoomTo(0.1)
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100vw;height: 100vh;
  box-sizing: border-box;
  padding: 16px;
  font-size: 14px;
  overflow-y: auto;
}
.image{
  display: block;
  width: 100%;
}
</style>
