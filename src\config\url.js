let BaseApiUrl = ''
let BaseOssHost = ''
let BaseOssHostCC = ''
let BaseConfigrationOssHost = ''
let serveUrl
let consentByTypeUrl = ''

// console.log(process.env.VUE_APP_ENV)
console.log('xxxxxxxx', process.env.VUE_APP_ENV)
switch (process.env.VUE_APP_ENV) {
  case 'development': // 开发环境
    BaseApiUrl = 'https://uataudi-api.saic-audi.mobi'
    BaseOssHost = 'https://sx-audi.oss-cn-shanghai.aliyuncs.com/'
    BaseOssHostCC =
      'https://sx-audi.oss-cn-shanghai.aliyuncs.com/cc-mobi/20211231/'
    BaseConfigrationOssHost = 'https://sx-audi.oss-cn-shanghai.aliyuncs.com'
    serveUrl = 'https://appdc-uat.mos.csvw.com/sa?project=saoneapp'
    break

  case 'dev': // dev环境
    BaseApiUrl = 'https://dev-audi-api.svwsx.cn'
    BaseOssHost = 'https://sx-audi.oss-cn-shanghai.aliyuncs.com/'
    BaseOssHostCC =
      'https://sx-audi.oss-cn-shanghai.aliyuncs.com/cc-mobi/20211231/'
    BaseConfigrationOssHost = 'https://sx-audi.oss-cn-shanghai.aliyuncs.com'
    serveUrl = 'https://appdc-dev.mos.csvw.com/sa?project=saoneapp'
    break

  case 'qa': // qa环境
    BaseApiUrl = 'https://devaudi-api.saic-audi.mobi'
    BaseOssHost = 'https://sx-audi.oss-cn-shanghai.aliyuncs.com/'
    BaseOssHostCC =
      'https://sx-audi.oss-cn-shanghai.aliyuncs.com/cc-mobi/20211231/'
    BaseConfigrationOssHost = 'https://sx-audi.oss-cn-shanghai.aliyuncs.com'
    serveUrl = 'https://appdc-dev.mos.csvw.com/sa?project=saoneapp'
    break

  case 'pre': // 预生产环境
    BaseApiUrl = 'https://uataudi-api.saic-audi.mobi'
    BaseOssHost = 'https://sx-audi.oss-cn-shanghai.aliyuncs.com/'
    BaseOssHostCC =
      'https://sx-audi.oss-cn-shanghai.aliyuncs.com/cc-mobi/20211231/'
    BaseConfigrationOssHost = 'https://sx-audi.oss-cn-shanghai.aliyuncs.com'
    serveUrl = 'https://appdc-uat.mos.csvw.com/sa?project=saoneapp'
    consentByTypeUrl = 'https://audi-api-uat.mos.csvw.com' // 敏感个人信息处理规则地址
    break

  case 'prod': // 生产环境
    BaseApiUrl = 'https://audi-api.saic-audi.mobi'
    BaseOssHost = 'https://audi-oss.saic-audi.mobi/'
    BaseOssHostCC = 'https://audi-oss.saic-audi.mobi/cc-mobi/20211231/'
    BaseConfigrationOssHost = 'https://audi-oss.saic-audi.mobi/'
    serveUrl = 'https://appdc.mos.csvw.com/sa?project=saoneapp'
    consentByTypeUrl = 'https://audi-api.mos.csvw.com' // 敏感个人信息处理规则地址
    break

  default:
    // 默认使用生产的接口
    BaseApiUrl = 'https://audi-api.saic-audi.mobi'
    BaseOssHost = 'https://audi-oss.saic-audi.mobi/'
    BaseOssHostCC = 'https://audi-oss.saic-audi.mobi/cc-mobi/20211231/'
    BaseConfigrationOssHost = 'https://audi-oss.saic-audi.mobi'
    serveUrl = 'https://appdc.mos.csvw.com/sa?project=saoneapp'
    consentByTypeUrl = 'https://audi-api.mos.csvw.com' // 敏感个人信息处理规则地址
    break
}
export default {
  BaseApiUrl,
  BaseOssHost,
  BaseConfigrationOssHost,
  serveUrl,
  BaseOssHostCC,
  consentByTypeUrl
}
