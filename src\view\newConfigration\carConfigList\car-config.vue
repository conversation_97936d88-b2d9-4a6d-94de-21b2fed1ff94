<!-- 弹出框 -->
<template>
  <div>
    <div class="config-wrapper">
      <div v-if="0" class="c-flex-between padding">
        <div>车辆配置</div>
        <div class="c-flex-center small-font" @click="showCarConfigDetail = !showCarConfigDetail">
          查看全部
          <van-icon :name="showCarConfigDetail ? 'arrow-up' : 'arrow-down'" />
        </div>
      </div>

      <div class="c-flex-between ">
        <div class="c-flex-center">
          <div class="sc-left" style="height: 56px;width: 56px;">
            <img style="width: 100%;
              vertical-align: middle;
              object-fit: cover;
              height: 100%;" :src="page2dCarList[4] | imgFix(100)" alt="" @click="imagePreview(page2dCarList[4])">
          </div>
          <div class="sc-height">
            <div class="f14" style="width: 160px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
              {{ currentModelLineData.modelLineName }}
            </div>
            <div class="small-font" style="padding-top: 8px;" v-if="$route.query.idx == 2">
              {{ currentModelLineData.price | formatPrice }}
            </div>
            <div v-else class="small-font">
              ¥{{ currentModelLineData.price | formatPrice }}
            </div>
          </div>
        </div>

        <div class=" small-font sc-nowrap" style="padding-top: 15px;" @click="toConfigTable">
          查看参数
          <van-icon name="arrow" />
        </div>
      </div>


      <!-- 配置组件 -->
      <div>
        <div class="config-item-wrapper" v-show="true">

          <!-- //外观色 -->
          <div class="config-item c-flex-center">
            <div class="sc-left">
              <img :src="excolorImg | imgFix(100)" alt="" @click="imagePreview(excolorImg)">
            </div>
            <div class="sc-height">
              <div class="f14"> {{ currentExterior.optionName }} </div>
              <div class="small-font">
                {{ currentExterior.price | finalFormatPriceDesc }}
                <!-- <span
                  v-if="A7L_INTELLIGENT_AUDIO.optionCode.every(code => standardConfigData.map(i => i.optionCode).includes(code)) && item.optionCode === A7L_INTELLIGENT_AUDIO.optionCode[1]"
                  class="down-price">
                  ¥{{ A7L_INTELLIGENT_AUDIO.price9wp | formatPrice }}
                </span> -->
              </div>
            </div>
          </div>

          <!-- 内饰面料 -->
          <div class="config-item c-flex-center">
            <div class="sc-left">
              <img :src="sibImg | imgFix(100)" alt="" @click="imagePreview(sibImg)">
            </div>
            <div class="sc-height">
              <div class="f14"> {{ currentSib.sibName }} </div>
              <div class="small-font">
                {{ currentSib.price | finalFormatPriceDesc }}
                <!-- <span
                  v-if="A7L_INTELLIGENT_AUDIO.optionCode.every(code => standardConfigData.map(i => i.optionCode).includes(code)) && item.optionCode === A7L_INTELLIGENT_AUDIO.optionCode[1]"
                  class="down-price">
                  ¥{{ A7L_INTELLIGENT_AUDIO.price9wp | formatPrice }}
                </span> -->
              </div>
            </div>
          </div>

          <!-- 饰板 -->
          <div class="config-item c-flex-center">
            <div class="sc-left">
              <img :src="eihImg | imgFix(100)" alt="" @click="imagePreview(eihImg)">
            </div>
            <div class="sc-height">
              <div class="f14"> {{ currentEih.optionName }} </div>
              <div class="small-font">
                {{ currentEih.price | finalFormatPriceDesc }}
              </div>
            </div>
          </div>

          <!-- 轮毂 -->
          <div class="config-item c-flex-center">
            <div class="sc-left">
              <img :src="hubImg | imgFix(100)" alt="" @click="imagePreview(hubImg)">
            </div>
            <div class="sc-height">
              <div class="f14"> {{ currentHub.optionName }} </div>
              <div class="small-font">
                {{ currentHub.price | finalFormatPriceDesc }}
              </div>
            </div>
          </div>
        </div>


        <!-- <div class="titleBox" v-show="standardConfigData.find(f => f.optionClassification == 'PACKET')">
          选装
        </div>
        <div class="config-item-wrapper" v-show="standardConfigData.find(f => f.optionClassification == 'PACKET')">
          <div class="config-item c-flex-center " v-for="item in standardConfigData"
            v-show="item.optionClassification == 'PACKET'" :key="item.id">
            <div class="sc-left">
              <img :src="$loadWebpImage(item.img)" alt="" @click="imagePreview(item.img)">
            </div>
            <div class="sc-height">
              <div class="f14">
                {{ item.name }} {{ a7lFigureKey.includes(item.optionCode) ? '(支持部分手机机型)' : '' }}
              </div>
              <div class="small-font">
                {{ isNumber(item.price) ? '¥' : '' }}{{ item.price | formatPrice }}
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { ImagePreview, Popup } from 'vant'
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import confiUrl from '@/config/url'
import { XIAN_XING_VERSION, XIAN_JIAN_VERSION, XIAN_XING_VERSION_Q5 } from '@/config/constant'
import { A7L_FIGURE_KEY, A7L_INTELLIGENT_AUDIO } from '@/view/newConfigration/util/carModelSeatData'

const OSS_URL = confiUrl.BaseConfigrationOssHost

Vue.use(Popup)
export default {

  data() {
    return {
      showCarConfigDetail: true,
      a7lFigureKey: A7L_FIGURE_KEY,
      A7L_INTELLIGENT_AUDIO
    }
  },
  computed: {
    ...mapGetters([
      'currentSeriesName',
      'page2dCarList',
      'pageOutColorArray',
      'pageHubArray',
      'currentCarType'
    ]),
    ...mapState({
      // carHeadImageUrl: (state) => (state.carDetail.configDetail?.carModel?.headImageUrl ? baseOssHost + state.carDetail.configDetail?.carModel?.headImageUrl : ''),
      // carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn, // 车辆名称
      // carImgUrl: (state) => (state.carDetail.configDetail?.carModel?.imageUrl ? baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl : ''), // 主图
      totalPrice: (state) => state.carDetail.configDetail?.totalPrice,
      // carSeries: (state) => state.carSeries,

      modellineId: (state) => state.configration.currentModelLineData.modellineId,
      modelLineCode: (state) => state.configration.currentModelLineData.modelLineCode,

      carHeadImage: (state) => state.configration.page2dCarList[0], // 车辆组合45度图
      // carModelName: (state) => state.configration.currentModelLineData.modelLineName, // 车辆名称
      currentModelLineData: (state) => state.configration.currentModelLineData, // 车辆价格
      currentExterior: (state) => state.configration.currentExterior, // 当前外观颜色数据
      currentSib: (state) => state.configration.currentSib, // 当前内饰颜色数据
      currentEih: (state) => state.configration.currentEih, // 当前饰板数据
      currentHub: (state) => state.configration.currentHub // 当前轮毂数据
    }),

    excolorImg() {
      return OSS_URL + this.currentExterior.imageUrl
    },
    sibImg() {
      return OSS_URL + this.currentSib.imageUrl
    },
    eihImg() {
      return OSS_URL + this.currentEih.imageUrl
    },
    hubImg() {
      return OSS_URL + this.currentHub.imageUrl
    },
    serverName() {
      if (this.carSeries?.seriesCode === 'G4') {
        if (this.modelLineCode === XIAN_XING_VERSION_Q5) {
          return '艺领权益服务包'
        }
        return '艺创权益服务包'
      }
      if (this.modelLineCode === XIAN_XING_VERSION || this.modelLineCode === XIAN_JIAN_VERSION) {
        return '领尊权益服务包'
      }
      return '尊享权益服务包'
    }
  },
  watch: {},
  mounted() {
    console.log(this.carImgUrl)
  },

  methods: {
    imagePreview(url) {
      ImagePreview([url])
    },
    isNumber(value) {
      return typeof value === 'number' && !isNaN(value)
    },
    toConfigTable() {
      // console.log(this.$store.state.carDetail)
      // this.$emit('dataTrack', '查看参数')
      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: this.currentModelLineData.modelLineId,
          carModelName: this.currentModelLineData.modelLineName,
          seriesName: this.currentModelLineData.customSeriesCode,
          from: 0,
          customBack: 'newConfigration'
        }
      })
    },
    toEquity() {
      const configDetail = this.$store.state.carDetail.configDetail
      let caseCode = ''
      configDetail?.optionList && configDetail?.optionList.forEach((e) => {
        if (e.optionCode == 'YEG' || e.optionCode == 'YEA') {
          caseCode = e.optionCode
        }
      })
      this.$router.push({
        path: '/theEquity',
        query: {
          modelLineCode: this.modelLineCode,
          caseCode: caseCode || '',
          orderId: this.$route.query?.orderId || '',
          seriesName: this.carSeries.seriesCode,
          modelVersion: this.carSeries.modelVersion
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";

@leftWith: 18vw;
@rightMargin: 15px;
@topMargin: 20px;
@fontColor: #999;

//向上的边距
.sc-m-top {
  margin-top: @topMargin;
}

.sc-left {
  // width: @leftWith;
  margin-right: @rightMargin;
  height: 56px;
  width: 56px;
}

//box阴影
.sc-shadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
}

// 下划线
.sc-u-line {
  border-bottom: 1px solid #e5e5e5;
}


.f14 {
  font-size: 14px;
}

.small-font {
  .c-font12;
  color: #999;
}

.container {
  .sc-m-top;
  position: relative;
  padding: 0 18px;
}

.hint-wrapper {
  background-color: #f2f2f2;
  font-size: 14px;
  padding: 10px 18px;
}

.sc-nowrap {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
}

.sc-height {
  height: auto !important;
  .c-flex-between;
  flex-direction: column;
  flex: 1;
  height: @leftWith;
  padding: 14px 0;
  box-sizing: border-box;
}

// 我的配置
.config-wrapper {
  // .sc-shadow;
  padding: 0 0 0 16px;
  margin-top: 15px;

  >.padding {
    .sc-u-line;
    padding: 10px 0;
  }

  .config-item {
    position: relative;
    padding: 12px 0;
    height: @leftWith;
  }
}

// 客户权益
.client-wrapper {
  .sc-shadow;

  margin-top: 20px;
  padding: 5px 20px;
  border-bottom: 1px solid #e5e5e5;

  >.title {
    padding: 5px 0;
    margin-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
  }
}

@leftWith: 14.5vw;

.f14 {
  font-size: 14px;
  line-height: 14px;
  max-width: 265px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.config-item-wrapper {
  position: relative;
}

.titleBox {
  margin-left: -16px;
  height: 26px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 26px;
}


//向上的边距
.sc-m-top {
  margin-top: @topMargin;
}

.sc-left {
  width: 14.5vw;
  margin-right: @rightMargin;

  img {
    height: 56px;
    width: 56px;
  }
}

.small-font {
  .c-font12;
  color: #999;
  padding-top: 8px;

  >.down-price {
    .c-font10;
    color: #ccc;
    margin-left: 6px;
    text-decoration: line-through;
  }
}

.container {
  .sc-m-top;
  position: relative;
  padding: 0 18px;
}

.hint-wrapper {
  background-color: #f2f2f2;
  font-size: 14px;
  padding: 10px 18px;
}

.sc-nowrap {
  white-space: nowrap;
}


.sc-height {
  .c-flex-between;
  flex-direction: column;
  flex: 1;
  height: @leftWith;
  padding: 8px 0;
  box-sizing: border-box;
}


// 客户权益
.client-wrapper {
  .sc-shadow;

  margin-top: 20px;
  padding: 5px 20px;
  border-bottom: 1px solid #e5e5e5;

  >.title {
    padding: 5px 0;
    margin-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
  }
}
</style>
