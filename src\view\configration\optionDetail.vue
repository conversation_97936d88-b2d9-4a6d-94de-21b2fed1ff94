<template>
  <div class="option-detail">
    <div
      class="option-detail-goback"
      :style="{ top: 8 + statusBarHeight + navigationBarHeight / 2 + 'px' }"
      @click="goBack"
    >
      <img
        src="../../assets/img/icon17.png"
        alt=""
      >
    </div>
    <div class="option-detail-main">
      <div class="option-detail-topimg">
        <img
          :src="$loadWebpImage(BaseConfigrationOssHost + clickOption.imageUrlDetail + '?x-oss-process=image/resize,w_512')"
          alt=""
        >
      </div>
      <div class="option-detail-info">
        <div class="option-detail-title">
          {{ clickOption.optionName }}
        </div>
        <div
          v-if="clickOption.optionType === 'packet'"
          class="option-detail-subtitle"
        />
        <div
          v-if="clickOption.optionType === 'packet'"
          class="option-detail-desc-sp"
        >
          <span
            v-for="(item, index) in clickOption.optionDetailList"
            :key="index"
          >
            {{ item.optionDesc }}
          </span>
        </div>
        <div
          v-if="clickOption.optionType === 'packet'"
          class="option-detail-desc"
        >
          <span>该系统包含：
            <span
              v-for="(item, index) in optionNameList"
              :key="item"
            >
              {{ item }}{{ optionNameList.length - 1 === index ? '。' : '、' }}
            </span>
          </span>

          <div
            class="option-card"
            v-for="item in clickOption.packetItems" v-show="!item.status == 0"
            :key="item.optionId" v-if="!(carVis && '3L4+6XT,QQ9,4A3'.includes(item.optionCode))"
          >
            <img @click="imagePreview(BaseConfigrationOssHost + item.imageUrlList + '?x-oss-process=image/resize,w_256')"
              :src="$loadWebpImage(BaseConfigrationOssHost + item.imageUrlList + '?x-oss-process=image/resize,w_256')"
              alt=""
            >
            <div class="option-card-info">
              <div class="option-card-optionName">
                {{ item.optionName }}
              </div>
              <div class="option-card-price" v-if="item.price  && item.price != '价格已包含'">
                {{ item.price | finalFormatPrice }}
              </div>
              <span
                class="option-detail-tips"
                v-if="('498BZY001,498BZY002,498B2Y001,498B2Y002').includes(selectCarInfo.modelLineCode)&&item.optionCode === 'Q4Q'"
              >
                受产能限制，计划于01月17日起暂停供应
              </span>
              <span
                class="option-detail-tips"
                v-if="('498BZY001,498BZY002,498B2Y001,498B2Y002').includes(selectCarInfo.modelLineCode)&&item.optionCode === 'Q4Q'"
              >
                受产能限制，计划于01月17日起暂停供应
              </span>
              <span
                class="option-detail-tips"
                v-if="
                  item.optionName &&
                    item.optionName.indexOf('数字钥匙') !== -1
                "
              >
                *点击<P @click="toRoute()">
                  查看兼容机型
                </P>
                查看手机是否兼容
              </span>
            </div>
          </div>
        </div>
        <div
          v-else
          class="option-detail-desc"
        >
          <span v-if="!'YEG,YEA,CCPRO-YEB'.includes(clickOption.optionCode)">
            <p
              v-for="(item) in clickOption.optionDetailList"
              :key="item.optionDesc"
            >
              {{ item.optionDesc }}
            </p>
          </span>
          <div v-else-if="'YEA'.includes(clickOption.optionCode)">
            <div >一、尊享用车礼</div>
            <div>1. 3年免费24小时安全顾问事故处理协助</div>
            <div>二、畅享互联礼</div>
            <div>1. 3年免费Audi Connect & Asterix移动互联服务</div>
            <div>2. 3年免费基础服务流量</div>
            <div>3. 3年内每月赠送 6GB通用流量</div>
            <div>三、早鸟包（2022年完成开票）</div>
            <div>1. 首年免费保险权益包含交强险和商业险（三者险 300 万保额）</div>
            <div>四、悦享优贷礼</div>
            <div>1.最低20%首付起，最长60期低息方案，更可享最高24期0息金融方案</div>
            <div>2.首付低至1元起，最长60期融资租赁方案；更可享残值担保方案，期末可买可还可展期</div>
          </div>
          <div v-else-if="'CCPRO-YEB'.includes(clickOption.optionCode)">
            <div >一、质保礼</div>
            <div>1. 2年或者5万公里三包，以先到者为准</div>
            <div>2. 3年或10万公里整车质保，以先到者为准</div>
            <div>二、尊享用车礼</div>
            <div>1. 3年六次免费基础保养（含取送车）</div>
            <div>2. 3年免费24小时安全顾问事故处理协助</div>
            <div>3.首年免费原厂漆面维修（一个漆面）</div>
            <div>4.轮胎安心使用保障（1年或2万公里）</div>
            <div>5.免费出险代步车服务</div>
            <div>三、驭享出行礼</div>
            <div>1.同城不限公里数免费代驾服务 <br>
                2.免费精致洗车服务 <br>
                3.免费机场专属代泊车及停车服务 <br>
                4.免费机场贵宾厅服务 <br>
                5.免费机场贴心接送  <br>
                6.免费异地无忧用车
                </div>

            <div>四、畅享互联礼</div>
            <div>1. 3年免费Audi Connect & Asterix移动互联服务 <br>
                2. 3年免费基础服务流量 <br>
                3. 3年内每月赠送 6GB通用流量</div>
            <div>五、早鸟包（2022年完成开票）</div>
            <div>1.首年免费保险权益包含交强险和商业险（三者险 300 万保额）</div>
            <div>六、悦享优贷礼</div>
            <div>1.最低20%首付起，最长60期低息方案，更可享最高24期0息金融方案</div>
            <div>2.首付低至1元起，最长60期融资租赁方案；更可享残值担保方案，期末可买可还可展期</div>
          </div>
        </div>
      </div>
    </div>
    <div class="option-detail-bottom">
      <div class="text">
        <p>
          <span v-if="!'YEG,YEA,CCPRO-YEB'.includes(clickOption.optionCode)"> {{ clickOption.price | finalFormatPrice }}</span>
        </p>
      </div>
      <div
        class="button"
        @click="handleBtn"
      >
        确认
      </div>
    </div>
    <option-popup v-if="showOptionPopup" />
    <van-popup v-model:show="show">
      <div class="ex-popup">
        <main>
          <span>{{ popupDesc }}</span>
        </main>
        <footer
          @click="iknow"
        >
          <div
            class="popup-iknow"
          >
            我知道了
          </div>
        </footer>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import url from '@/config/url'
import OptionPopup from '@/components/option-popup.vue'
import { callNative } from '@/utils'
import { ImagePreview, Popup } from 'vant'
import Vue from 'vue'
import {
  checkV2
} from '@/utils'
Vue.use(Popup)


export default {
  components: { OptionPopup },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      modelLineId: '',
      optionId: '',
      optionNameList: [],
      statusBarHeight: 0,
      navigationBarHeight: 50,
      show: false,
      popupDesc: '',
      extraProcessObj: {
        checkOption: {},
        extraProcessList: []
      },
      carVis: ''
    }
  },
  async created() {
    console.log('%c clickOption', 'font-size:25px;color:green;', this.clickOption)
    let selectCarInfo = this.$storage.getPlus("selectCarInfo")    
    this.carVis ="G6ICAY001,G6IBAY001".includes(selectCarInfo.modelLineCode)
    this.$store.commit('setHeaderVisible', false)
  },
  mounted() {
  },
  computed: {
    ...mapState([
      'idx',
      'selectCarInfo',
      'clickOption',
      'showOptionPopup',
      'currentOptionsList',
      'currentModelHub',
      'currentSibColorInterieur',
      'modelScrollTop'
    ]),
    ...mapGetters(['packetItem'])
  },
  watch: {
    packetItem: {
      handler(next) {
        if (next && next.length > 0) {
          
          let arr = next.map((res) => res.optionName)
          console.log("let str = arr", arr);
          let str = arr.findIndex(e => e == "Nappa纳帕打孔真皮座椅面料")
          let str1 = arr.findIndex(e => e == "Dinamica超纤真皮印花座椅面料")
          let  temp = []

          if (str != -1 && str1 != -1) {
            arr.forEach((e, i)=> {
              if (i == str) {
                temp[i] = "Nappa纳帕打孔真皮座椅面料或Dinamica超纤真皮印花座椅面料"
              } else {
               if (i < str) {
                 temp.push(e)
               }
              }
          })
          }
          
          console.log("optionNameList", temp);
          this.optionNameList = temp 
        }
      },
      immediate: true
    }
  },
  methods: {
    imagePreview(url) {
      ImagePreview([url])
    },
    // selectedHub: function (hub) {
    //   this.$store.commit('setCurrentModelHub', hub)
    // }
    async unCheckOptionFun(option) {
      const findOption = this.currentOptionsList.findIndex(
        (item) => item.optionId === option.optionId
      )
      if (findOption !== -1) {
        this.$store.commit('setDelOption', findOption)
      }
    },
    async checkOptionFun(option) {
      const findOption = this.currentOptionsList.find(
        (item) => item.optionId === option.optionId
      )
      if (option.optionType === 'packet-equity' || option.optionType === 'packet' || option.optionType === 'personal') {
        // 查询是否有依赖项，当前选中的选装包中是否有互斥项，有则弹窗
        let flag = option.optionRelates
            && option.optionRelates.find((val) => val.relateType === 'depend')
        // 当前选中的选装包中是否有互斥项，
        this.currentOptionsList.forEach((item) => {
          const temp = option.optionRelates
              && option.optionRelates.find(
                (val) => val.optionRelateId === item.optionId
              )
          if (temp) {
            flag = temp
          }
        })
        // 若存在依赖，或当前选装存在互斥，则弹窗
        if (option.optionRelates && option.optionRelates.length > 0 && flag) {
          this.$store.commit('setShowOptionPopup', true)
        } else {
          if ('G4IBF3001,G4ICF3001'.includes(this.selectCarInfo.modelLineCode) && "8I6,4D3".includes(this.clickOption.optionCode) && !checkV2()) {
            this.$store.commit('setCurrentOptionsList', [
              ...this.currentOptionsList,
            ])
          } else {
            this.$store.commit('setCurrentOptionsList', [
              ...this.currentOptionsList,
              option
            ])
          }
        }
      } else {
        if (!findOption) {
          this.$store.commit('setCurrentOptionsList', [
            ...this.currentOptionsList,
            option
          ])
        }
      }
    },
    handleCheck(option) {
      if (option.category === 'PACKET' && option.packetItems && option.packetItems.length > 0) {
        const temp = []
        for (const item of option.packetItems) {
          const a = this.currentOptionsList.find((res) => res.optionCode === item.optionCode)
          a && temp.push(a)
        }
        if (temp.length > 0) {
          const tempOptionNames = temp.map((res) => res.optionName).join(',')
          this.extraProcessObj.checkOption = option
          this.extraProcessObj.extraProcessList = temp
          this.popupDesc = `此套装中包含${tempOptionNames}；将自动为您取消${tempOptionNames}`
          this.show = true
        }
      } else {
        // 选中选装件时，若选装件已被选中的某个选装包包含
        let temp
        let packet
        for (const item of this.currentOptionsList) {
          temp = item.packetItems && item.packetItems.find((res) => res.optionCode === option.optionCode)
          if (temp) {
            packet = item
            break
          }
        }
        if (temp) {
          this.extraProcessObj.checkOption = option
          this.extraProcessObj.extraProcessList = [packet]
          this.popupDesc = `您已选择的${packet.optionName}中包含此装备，将为您取消${packet.optionName}的选择`
          this.show = true
        }
      }
    },
    iknow() {
      this.show = false
      if (this.popupDesc === '选装超级运动座椅套装 需选装黑色车内顶棚') {
        this.checkOptionFun(this.clickOption)
      } else if (this.popupDesc === '若取消黑色车内顶棚选装，之前选择的超级运动座椅套装会同时取消') {
        console.log('%c currentOptionsList', 'font-size:25px;color:green;', this.currentOptionsList, this.clickOption)

        this.unCheckOptionFun(this.clickOption)
        const superChair = this.allPrivateOrderList.find((res) => res.optionCode === 'PS6')
        if (superChair) {
          this.unCheckOptionFun(superChair)
        }
      } else {
        console.log('%c item', 'font-size:25px;color:green;', this.extraProcessObj)
        this.checkOptionFun(this.extraProcessObj.checkOption)
        for (const item of this.extraProcessObj.extraProcessList) {
          this.unCheckOptionFun(item)
        }
      }
      this.goBack()
    },
    async handleBtn() {
      if (this.clickOption.conflict == "此装备与当前所选内饰冲突，请选中其他内饰后尝试") {
        this.goBack()
        return
      }

      this.$sensors.track('confirmEquipmentDetails', {
        page_name: `装备详情-${this.clickOption.optionName}`,
        amount_of_money: this.clickOption.price,
        front_seat_color: this.currentSibColorInterieur.interieurName
      })

      if (this.currentOptionsList.find((item) => this.clickOption.optionId === item.optionId) || this.clickOption.status === 1) {
        this.goBack()
        return
      }

      if (this.clickOption.category === 'PACKET' && this.clickOption.packetItems && this.clickOption.packetItems.length > 0) {
        const temp = []
        for (const item of this.clickOption.packetItems) {
          const a = this.currentOptionsList.find((res) => res.optionCode === item.optionCode)
          a && temp.push(a)
        }
        if (temp.length > 0) {
          const tempOptionNames = temp.map((res) => res.optionName).join(',')
          this.extraProcessObj.checkOption = this.clickOption
          this.extraProcessObj.extraProcessList = temp
          this.popupDesc = `此套装中包含${tempOptionNames}；将自动为您取消${tempOptionNames}`
          this.show = true
          return
        }
      } else {
        // 选中选装件时，若选装件已被选中的某个选装包包含
        let temp
        let packet
        for (const item of this.currentOptionsList) {
          temp = item.packetItems && item.packetItems.find((res) => res.optionCode === this.clickOption.optionCode)
          if (temp) {
            packet = item
            break
          }
        }
        if (temp) {
          this.extraProcessObj.checkOption = this.clickOption
          this.extraProcessObj.extraProcessList = [packet]
          this.popupDesc = `您已选择的${packet.optionName}中包含此装备，将为您取消${packet.optionName}的选择`
          this.show = true
          return
        }
      }


// optionCode: "4D3"
// optionDetailList: （…）
// optionId: "589c4507-9125-4a2c-8e39-24a49f5260b7"
// optionName: "前排座椅通风"

// optionCode: "8I6"
// optionDetailList: Array(0)
// optionId: "8348848f-f72a-4cd2-90ce-e3658c93da5a"
// optionName: "前排座椅按摩"

      if ('G4IBF3001,G4ICF3001'.includes(this.selectCarInfo.modelLineCode) && "8I6,4D3".includes(this.clickOption.optionCode) && !checkV2()) {
        const findOption = this.currentOptionsList.find(
          (item) => item.optionId === this.clickOption.optionId
        )
        if (this.clickOption.optionType === 'packet' || this.clickOption.optionType === 'personal') {
          // await this.$store.dispatch('getPacketItemInfo')
          if (
            this.clickOption.optionRelates
            && this.clickOption.optionRelates.length > 0
          ) {
            this.$store.commit('setShowOptionPopup', true)
          } else {
            this.$store.commit('setCurrentOptionsList', [
              ...this.currentOptionsList,
              this.clickOption
            ])
          }
        } else {
          if (!findOption) {
            this.$store.commit('setCurrentOptionsList', [
              ...this.currentOptionsList,
              this.clickOption
            ])
          }
        }
      }
      if (this.clickOption.optionCode == 'WA3' && this.clickOption.conflict && this.clickOption.conflict == '此装备与当前所选内饰冲突，请选中其他内饰后尝试') {
        
      } else {
        this.checkOptionFun(this.clickOption)
      }
      console.log(this.clickOption);
      
      this.goBack()
    },
    goBack() {
      this.$store.commit('setScrollTop', this.modelScrollTop + 1)
      this.$store.commit('setBackFromOptionDetail', true)
      if ( this.$route.query.idx ==2) {
        let obj = {
          idx: this.$route.query.idx,
          definedCar:  this.$storage.getPlus('semi-definite') == '个性定制' ? 0 : 1, //1 高定
        }
        let modelLineCode = this.$route.query?.modelLineCode
        if ("G6ICAY005,G6ICAY004,G6ICAY003,G6ICAY002".includes(modelLineCode)) {
          obj = {
            ...this.$route.query
          }
        }
        this.$router.push({
          path: '/configration',
          query: obj
        })
      } else {
        this.$router.go(-1)
      }
    },
    toRoute() {
      this.$sensors.track('viewCompatibleModels', {
        page_name: `装备详情-${this.clickOption.optionName}`
      })
      this.$router.push({
        path: '/configration/modelCompatible'
      })
    },
    async getHeight() {
      // if (env === 'test') return
      const data = await callNative('navigationBarHeight', {})
      const that = this
      console.log('navigationBarHeight', data)
      that.statusBarHeight = data.statusBarHeight
      // 导航栏高度
      that.navigationBarHeight = data.navigationBarHeight
    }
  }
}
</script>

<style lang="less" scoped>
.option-detail {
  .option-detail-goback {
    position: absolute;
    left: 8px;
    // top: calc(8px + env(safe-area-inset-top));
    width: 24px;
    height: 24px;
    > img {
      width: 24px;
      height: 24px;
    }
  }
  .option-detail-main {
    .option-detail-topimg {
      width: 100%;
      height: 260px;
      img {
        height: 260px;
        object-fit: cover;
      }
    }
    .option-detail-info {
      padding: 16px;
      box-sizing: border-box;
      padding-bottom: 120px;
      position: relative;
      .option-detail-title {
        font-size: 18px;
        font-weight: 400;
        color: #000000;
        line-height: 22px;
        margin-bottom: 16px;
      }
      .option-detail-subtitle {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 20px;
        margin-bottom: 24px;
      }
      .option-detail-desc-sp {
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        line-height: 20px;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #E5E5E5;
        p {
          margin: 5px 0;
        }
      }
      .option-detail-desc {
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        line-height: 20px;
        p {
          margin: 5px 0;
        }
      }

      .option-detail-tips {
        color: #999999;
        font-size: 10px;
        display: flex;
        align-items: center;
        position: absolute;
        // margin-top: 10px;

        p {
          color: #1a1a1a;
          font-weight: bold;
          margin: 0 3px;
        }
      }
    }
    .option-card {
      height: 77px;
      width: 100%;
      display: flex;
      align-items:center;
      border-bottom: 1px solid #E5E5E5;
      >img{
        width: 50px;
        height: 50px;
        object-fit:cover;
        margin-right:18px;
      }
      .option-card-optionName {
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        line-height: 14px;
        margin-bottom: 8px;
      }
      .option-card-price {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        line-height: 14px;
      }
    }
  }
  .option-detail-bottom {
    position: fixed;
    bottom: 0;
    height: 106px;
    width: 100vw;
    z-index: 1;
    background: #ffffff;
    // border: 1px solid red;
    box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);
    .text {
      color: #999;
      line-height: 12px;
      font-size: 12px;
      padding-left: 16px;
      padding-top: 10px;
      span {
        color: black;
        font-family: 'Audi-WideBold';
        font-size: 16px;
      }
    }
    .button {
      position: absolute;
      top: 12px;
      right: 16px;
      width: 136px;
      height: 44px;
      background: #000;
      color: white;
      font-size: 16px;
      text-align: center;
      line-height: 44px;
    }
  }
  .ex-popup {
    width: calc(100vw - 32px);
    height: 200px;
    background-color: #fff;
    padding: 32px;
    box-sizing: border-box;
    position: relative;
    main {
      font-size: 16px;
      color: #333333;
      line-height: 25px;
      text-align: center;
    }
    footer {
      width: calc(100% - 64px);
      height: 56px;
      background: #000000;
      border: 1px solid #000000;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      bottom: 32px;
    }
  }
}
</style>
