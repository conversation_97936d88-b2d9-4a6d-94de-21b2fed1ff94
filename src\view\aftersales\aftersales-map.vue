<template>
  <div
    name="name"
    class="container"
  >
    <div
      id="container"
      style="width:100vw; height:300px"
    />
    <!-- //搜索 -->
    <div class="info">
      <div class="input-item">
        <div class="input-item-prepend">
          <span
            class="input-item-text"
            style="width:8rem;"
          >请输入关键字</span>
        </div>
        <input
          id="tipinput"
          type="text"
        >
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'Name',
  data() {
    return {

    }
  },
  computed: {
   
  },
  async mounted() {
   
    const map = new AMap.Map(document.getElementById('container'), {
      zoom: 16, // 显示范围
      center: [121.20215, 31.41444], // 初始显示的坐标
      resizeEnable: true
    })

    // 输入查找地点提示的api
    const auto = new AMap.Autocomplete({
      input: 'tipinput'
    })

    // 地图标记
    const icon = new AMap.Icon({ // 自定义图标
      size: new AMap.Size(44, 44),
      image: require('../../assets/img/map-index.png'),
      imageSize: new AMap.Size(44, 44),
      anchor: 'center'
    })
    const marker1 = new AMap.Marker({ // 插点
      icon: icon,
      position: [121.20215, 31.41444],
      offset: new AMap.Pixel(0, 0) // 相对于基点的偏移位置
    })
    const marker2 = new AMap.Marker({ // 插点
      icon: icon,
      position: [121.20200, 31.41444],
      offset: new AMap.Pixel(0, 0) // 相对于基点的偏移位置
    })
    const markerList = [marker1, marker2] // 多个点实例组成的数组
    map.add(markerList)

    // 获取定位
    const options = {
      showButton: true, // 是否显示定位按钮
      buttonPosition: 'RB', // 定位按钮的位置
      buttonOffset: new AMap.Pixel(10, 20), // 定位按钮距离对应角落的距离
      showMarker: true, // 是否显示定位点
      // 'markerOptions': { //自定义定位点样式，同Marker的Options  个人的点
      //   'offset': new AMap.Pixel(-18, -36),
      //   'content': '<img src="https://a.amap.com/jsapi_demos/static/resource/img/user.png" style="width:36px;height:36px"/>'
      // },
      showCircle: true, // 是否显示定位精度圈
      circleOptions: { // 定位精度圈的样式
        strokeColor: '#0093FF',
        noSelect: true,
        strokeOpacity: 0.5,
        strokeWeight: 1,
        fillColor: '#ff0000',
        fillOpacity: 0.25
      }
    }
    AMap.plugin(['AMap.Geolocation'], () => {
      const geolocation = new AMap.Geolocation(options)
      map.addControl(geolocation)
      geolocation.getCurrentPosition()
    })

    // 导航路线  --汽车
    const drivingOption = {
      policy: AMap.DrivingPolicy.LEAST_TIME, // 其它policy参数请参考 https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingPolicy
      map: map
    }
    const driving = new AMap.Driving(drivingOption)
    // 根据起终点经纬度规划驾车导航路线
    driving.search(new AMap.LngLat(121.20215, 31.41444), new AMap.LngLat(121.20215, 31.01444), (status, result) => {
      if (status === 'complete') {
        console.log('绘制驾车路线完成')
      } else {
        console.log(`获取驾车数据失败：${result}`)
      }
    })
  },
  methods: {

  }
}
</script>

<style lang='scss' scoped>

</style>
