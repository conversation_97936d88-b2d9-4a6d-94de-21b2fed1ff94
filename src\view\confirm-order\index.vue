<template>
  <div class="confirm-order">
    <template v-if="orderInfo">
      <div class="confirm-totas">
        订单修改确认，请对如下信息进行确认！
      </div>
      <div class="order-box">
        <div class="order-content">
          <div class="title">
            原车主信息
          </div>
          <div class="info-item">
            <div class="item-title">
              购车人：
            </div>
            <div class="item-cont">
              {{ orderInfo.modificationValue.carBuyerName }}
            </div>
          </div>
          <!-- <div class="info-item">
					<div class="item-title">
						购车人手机号：
					</div>
					<div class="item-cont">
						{{orderInfo.modificationValue.carBuyerMobile}}
					</div>
				</div> -->
          <div class="info-item">
            <div class="item-title">
              购车人证件类型：
            </div>
            <div class="item-cont">
              {{ orderInfo.modificationValue.carBuyerType|cardType }}
            </div>
          </div>
          <div class="info-item">
            <div class="item-title">
              购车人身份证号：
            </div>
            <div class="item-cont">
              {{ orderInfo.modificationValue.carBuyerNumber }}
            </div>
          </div>
          <template v-if="orderInfo.purchaseMessageType==1">
            <div class="info-item">
              <div class="item-title">
                车主：
              </div>
              <div class="item-cont">
                {{ orderInfo.modificationValue.carOwnerName }}
              </div>
            </div>
            <div class="info-item">
              <div class="item-title">
                车主手机号：
              </div>
              <div class="item-cont">
                {{ orderInfo.modificationValue.carOwnerMobile }}
              </div>
            </div>
            <div class="info-item">
              <div class="item-title">
                车主证件类型：
              </div>
              <div class="item-cont">
                {{ orderInfo.modificationValue.carOwnerCertificateType|cardType }}
              </div>
            </div>
            <div class="info-item">
              <div class="item-title">
                车主身份证号：
              </div>
              <div class="item-cont">
                {{ orderInfo.modificationValue.carOwnerCertificateNumber }}
              </div>
            </div>
          </template>
          <template v-else>
            <div class="info-item">
              <div class="item-title">
                企业名称：
              </div>
              <div class="item-cont">
                {{ orderInfo.modificationValue.enterpriseName }}
              </div>
            </div>
            <div class="info-item">
              <div class="item-title">
                组织机构代码：
              </div>
              <div class="item-cont">
                {{ orderInfo.modificationValue.enterpriseCode }}
              </div>
            </div>
          </template>
        </div>
        <div class="order-hr">
          更改后
        </div>
        <div class="order-content">
          <div class="title">
            更改后车主信息
          </div>
          <div class="info-item">
            <div class="item-title">
              购车人：
            </div>
            <div class="item-cont">
              {{ orderInfo.carBuyerName }}
            </div>
          </div>
          <!-- <div class="info-item">
					<div class="item-title">
						购车人手机号：
					</div>
					<div class="item-cont">
						{{orderInfo.carBuyerMobile}}
					</div>
				</div> -->
          <div class="info-item">
            <div class="item-title">
              购车人证件类型：
            </div>
            <div class="item-cont">
              {{ orderInfo.carBuyerType|cardType }}
            </div>
          </div>
          <div class="info-item">
            <div class="item-title">
              购车人身份证号：
            </div>
            <div class="item-cont">
              {{ orderInfo.carBuyerNumber }}
            </div>
          </div>
          <template v-if="orderInfo.purchaseMessageType==1">
            <div class="info-item">
              <div class="item-title">
                车主：
              </div>
              <div class="item-cont">
                {{ orderInfo.carOwnerName }}
              </div>
            </div>
            <div class="info-item">
              <div class="item-title">
                车主手机号：
              </div>
              <div class="item-cont">
                {{ orderInfo.carOwnerMobile }}
              </div>
            </div>
            <div class="info-item">
              <div class="item-title">
                车主证件类型：
              </div>
              <div class="item-cont">
                {{ orderInfo.carOwnerCertificateType|cardType }}
              </div>
            </div>
            <div class="info-item">
              <div class="item-title">
                车主身份证号：
              </div>
              <div class="item-cont">
                {{ orderInfo.carOwnerCertificateNumber }}
              </div>
            </div>
          </template>
          <template v-else>
            <div class="info-item">
              <div class="item-title">
                企业名称：
              </div>
              <div class="item-cont">
                {{ orderInfo.enterpriseName }}
              </div>
            </div>
            <div class="info-item">
              <div class="item-title">
                组织机构代码：
              </div>
              <div class="item-cont">
                {{ orderInfo.enterpriseCode }}
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="btnWarp">
        <!-- <div class="checked_msg">
				<el-checkbox :disabled='disabled!=0' v-model="checked">我已阅读并同意修改以上内容</el-checkbox>
		</div> -->
        <div
          class="buttons"
          @click="submit"
          v-if="disabled==0"
        >
          确认修改
        </div>
        <div
          class="buttons status"
          v-if="disabled==1||disabled==2"
        >
          {{ disabled==1?'修改成功':'确认信息已超时' }}
        </div>
        <!-- <div class="bt" /> -->
      </div>
    </template>
    <div
      class="no-data"
      v-else
    >
      订单不存在
    </div>
    <!-- <div class="showtoast-ceng"> -->
    <div
      class="success-updeta"
      v-if="isShowToast"
    >
      {{ disabled==1?"订单信息已修改":disabled==2?"确认信息已超时":message }}
    </div>
    <!-- <div class="success-updeta" v-if="&&isShowToast"></div> -->
    <!-- </div> -->
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Toast
} from 'vant'
import {
  queryNgaOrderInfoConfirm,
  confirmUpdate
} from '@/api/api'

Vue.use(Toast)
export default {
  data() {
    return {
      orderId: null,
      orderInfo: null,
      disabled: 0,
      isShowToast: false,
      message: '',
				 checked: false
    }
  },
  mounted() {
    const {
      id
    } = this.$route.query
    this.orderId = id
    this.getData()
  },
  filters: {
    cardType(value) {
      let type
      if (value === '1') {
        type = '居民身份证'
      } else if (value === '101') {
        type = '外国人护照'
      } else if (value === '102') {
        type = '港澳台居民往内地通行证（回乡证）'
      } else if (value === '103') {
        type = '台湾居民来往大陆通行证（台胞证）'
      } else if (value === '104') {
        type = '港澳居民居住证'
      } else if (value === '105') {
        type = '台湾居民居住证'
      }
      return type
    }
  },
  methods: {
    async getData() {
      this.$store.commit('showLoading')
      queryNgaOrderInfoConfirm({
        id: this.orderId
      }).then((res) => {
        this.$store.commit('hideLoading')
        console.log('queryNgaOrderInfoConfirm', res)
        if (res.data.code === '00') {
          this.orderInfo = res.data.data
          this.disabled = res.data.data.updateStatus
          if (res.data.data.updateStatus != 0) {
            this.isShowToast = true
            this.checked = true
            setTimeout(() => {
              this.isShowToast = false
            }, 2000)
          }
        }
      })
    },
    submit() {
      // if(!this.checked){
      // 	this.isShowToast=true
      // 			this.message="请勾选我已阅读并同意修改以上内容"
      // 			setTimeout(() => {
      // 				this.isShowToast=false
      // 			}, 2000);
      // 	return
      // }
      this.$store.commit('showLoading')
      confirmUpdate({
        id: this.orderId
      }).then((res) => {
        console.log(res)
        this.$store.commit('hideLoading')
        if (res.data.code === '00') {
          this.isShowToast = true
          setTimeout(() => {
            this.isShowToast = false
          }, 2000)
          this.disabled = 1
          this.getData()
        }
        if (res.data.code === '01') {
          // Toast(res.data.message);
          // this.disabled = 2
          this.isShowToast = true
          this.message = res.data.message
          setTimeout(() => {
            this.isShowToast = false
          }, 2000)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
	.confirm-order {
		font-size: 16px;
		height: 100px;
		// font-family: "AudiTypeGB-Normal", "AudiTypeGB";
		 font-family: 'Audi-Normal';
		.no-data{
			font-size: 16px;
			color:#666666;
			text-align: center;
			margin-top: 150px;
		}
		.success-updeta{
			position: fixed;
			top:50%;
			left:50%;
			transform: translate(-50%,-50%);
			color:#ffffff;
			padding:10px;
			line-height: 20px;
			z-index: 9999;
			text-align: center;
			background: #000;
			font-size: 14px;
		}
		.confirm-totas {
			padding: 0 17px;
			height: 44px;
			background: #F2F2F2;
			line-height: 44px;
			font-size: 14px;
			font-weight: 400;
			color: #000000;
		}

		.order-box {
			padding: 16px;
			font-size: 14px;
			padding-bottom: 130px;
			font-weight: 400;
			color: #333333;

			.order-hr {
				font-size: 14px;
				font-weight: 400;
				color: #333333;
				line-height: 17px;
				text-align: center;
				margin: 25px auto;
				position: relative;

				&::before {
					content: "";
					display: table;
					width: 40%;
					height: 1px;
					background: #D8D8D8;
					position: absolute;
					top: 50%;
					left: 0px;
				}

				&::after {
					content: "";
					display: table;
					width: 40%;
					height: 1px;
					background: #D8D8D8;
					position: absolute;
					top: 50%;
					right: 0px;
				}
			}

			.title {
				font-size: 16px;
				font-weight: normal;
				color: #000000;
				line-height: 24px;
				margin-bottom: 10px;
			}

			.info-item {
				display: flex;
				align-items: center;
				height: 35px;
				// line-height: 35px;

				.item-title {
					color: #999999;
					width: 50%;
				}

				.item-cont {
					width: 65%;
					color: #333333;
					text-align: right;
				}
			}
		}

		.btnWarp {
			position: fixed;
			height: 56px;
			width: 100%;
			bottom: 0px;
			background: #fff;
			z-index: 99;
			padding-bottom: 50px;
			.checked_msg{
				padding-left: 20px;
				font-size: 14px;
				margin:10px 0;

			.el-checkbox{
color:#000000;
				}
				/deep/.el-checkbox__input.is-checked+.el-checkbox__label{
					color:#000000;
				}
				/deep/.el-checkbox__input.is-checked .el-checkbox__inner{
					background-color: #000000;
    border-color: #000000;
				}
			}
			.buttons {
				height: 56px;
				z-index: 20;
				margin: auto;
				background: #000;
				font-family: "Audi-Normal";
				color: #fff;
				line-height: 56px;
				text-align: center;
				border: 0;
				font-size: 18px;
				width: calc(100% - 32px);
				margin: 0 auto;
				&.status{
					background: #ccc;
				}
			}

			.bt {
				content: "";
				display: block;
				background: #000;
				opacity: 0.2;
				width: 134px;
				height: 5px;
				position: absolute;
				bottom: 9px;
				left: 50%;
				transform: translateX(-50%);
				margin: 0 auto;
				border-radius: 100px;
			}
		}
	}
</style>
