import request from '@/router/axios'
import api from '@/config/url'

const baseUrl = api.BaseApiUrl

export const getUserCarDetail = (carMemberRelId) =>
  request({
    url: `${baseUrl}/api-wap/audi-after-service/api/manCar/customer/${carMemberRelId}/detail`,
    method: 'get'
  })

export const noticeScBindCar = (vin, scd) =>
  request({
    url: `${baseUrl}/api-wap/audi-after-service/api/manCar/noticeScBindCar?vin=${vin}&scd=${scd}`,
    method: 'get'
  })
