<template>
  <div class="full-page">
    <div class="header-wrapper c-flex-center-between" :style="{
      paddingTop: statusHeight + 'px'
    }">
      <div class="left c-flex">
        <div class="btn back" @click="toBack">
          <img :src="require('../../assets/img/icon03.png')" alt="">
        </div>
      </div>

      <div class="center"> {{ $store.state.title }} </div>

      <div class="right c-font14" @click="dialogVisible = true"> 兼容性声明 </div>
    </div>
    <iframe class="iframe-page" :src="PAGE_URL" frameborder="0"></iframe>

    <van-popup v-model="dialogVisible" closeable>
      <div class="desc-wrapper">
        <div class="title c-bold c-font14">关于支持机型</div>
        <div class="content c-font12">
          兼容机型列表中列出的移动设备，已通过我们的兼容性测试。<br>
          同一机型下的所有子型号应用有同样的功能表现。但是，不能排除兼容性差异引起的功能限制。<br>
          移动设备中的更高固件版本应具有相同的功能范围。但是，不能排除由更高固件状态引起的功能限制。<br>
          如果因在使用过程中遇到兼容性问题，请拨打热线400-820-1118及时向我们反馈。
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import Vue, { ref, onMounted, onUnmounted } from 'vue'
import { Popup } from 'vant'
import { useStore } from '@/view/newConfigration/util/vueApi'
import { callNative } from '@/utils/index'

Vue.use(Popup)
const store = useStore()
const dialogVisible = ref(false)
const statusHeight = ref(0)

const PAGE_URL = 'https://audi-mweb-uat.mos.csvw.com/pmdm/cfg/modelsCompatibleACK'

const toBack = () => {
  store.commit('setHeaderVisible', true)
  history.back()
}

const setStatusBarHeight = async () => {
  const data = await callNative('getDeviceInfo', {}) || {}
  console.log('data', data)
  const { statusBarHeight } = data
  statusHeight.value = statusBarHeight || 20
}

onMounted(() => {
  setStatusBarHeight()
  store.commit('setHeaderVisible', false)
})

onUnmounted(() => {
  store.commit('setHeaderVisible', true)
})
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";


.header-wrapper {
  padding: 0 15px;
  height: 44px;
  >div {
    flex: 1;

    &.center {
      text-align: center;
    }

    &.right {
      text-align: right;
    }
  }

  .btn {
    width: 20px;
    // padding: 0 5px;

    img {
      vertical-align: middle;
    }

    &.close {
      margin-left: 6px;
    }
  }
  .center {
    font-weight: bold;
  }
}
.full-page {
  width: 100%;
  height: 100%;
  position: relative;
}
.iframe-page {
  width: 100%;
  height: 100%;
  border: none;
}

.desc-wrapper {
  width: 92vw;
  padding: 20px;
  // padding-top: 40px;
  box-sizing: border-box;
  >.title {
    text-align: center;
  }
  >.content{
    margin-top: 6px;
    line-height: 20px;
    color: #000;
    opacity: 0.5;
    min-height: 80vh;
  }
}
</style>
