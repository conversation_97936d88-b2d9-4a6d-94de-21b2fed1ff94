<template>
  <div class="_container">
    <navigation
      title=""
      :back-type="backType"
    />
    <div style="flex:1">
      <van-field
        v-model="formData.userFullName"
        label="姓名"
        placeholder="请输入您的姓名"
        @input="onInput0"
        :error-message="message0"
        :readonly="readonly"
      />
      <van-field
        v-model="formData.idCard"
        label="身份证号"
        @input="onInput1"

        :error-message="message1"
        placeholder="请输入您的身份证号"
        :readonly="readonly"
      />
      <van-field
        v-model="formData.employerName"
        label="工作单位"
        @input="onInput2"

        :error-message="message2"
        placeholder="请输入您的工作单位"
        :readonly="readonly"
      />
      <van-field
        v-model="formData.phoneNumber"
        label="手机号"
        maxlength="11"
        type="number"
        @input="onInput3"
        :readonly="readonly"

        :error-message="message3"

        placeholder="请输入您的手机号"
      />

      <div class="immediate-list">
        <p>是否直系亲属购车</p>
        <!-- <van-switch
        v-model="formData.immediateFamily"
        active-color="#0da20d"
        inactive-color="#E5E5E5"
        size="24px"
        :disabled="readonly"
      /> -->
        <img
          v-if="!formData.immediateFamily"
          style="width: 58px;
        height: 32px;"
          src="../../assets/img/Switch1.png"
          @click="isShowSwitch"
        >
        <img
          v-else
          style="width: 58px;
        height: 32px;"
          src="../../assets/img/Switch2.png"
          @click="isShowSwitch"
        >
      </div>
      <div v-if="formData.immediateFamily">
        <van-field
          v-model="formData.immediateFamilyName"
          label="姓名"
          :error-message="message4"
          @input="onInput4"
          :readonly="readonly"

          placeholder="请输入您的直系亲属姓名"
        />
        <van-field
          v-model="formData.immediateFamilyIdCard"
          label="身份证号"
          placeholder="请输入您的直系亲属身份证号"
          :error-message="message5"
          @input="onInput5"
          :readonly="readonly"
        />
        <van-field
          v-model="formData.immediateFamilyPhoneNumber"
          label="手机号"
          maxlength="11"
          type="number"
          :error-message="message6"
          @input="onInput6"
          :readonly="readonly"

          placeholder="请输入您的直系亲属手机号"
        />
      </div>
      <div class="btn-delete-height" />
    </div>
    <div class="bottom_style">
      <p>
        上汽集团及其下属企业员工购车咨询电话：
      </p>
      <p>
        021-695-56182
      </p>
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onSubmit"
          :text="'下一步'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
    <van-overlay :show="isShowMessageId">
      <div class="wrapper">
        <div class="block">
          您已重新提交过资料
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Button,
  ActionSheet,
  Cell,
  Popup,
  Area,
  Toast,
  Checkbox,
  CheckboxGroup,
  Switch,
  Overlay
} from 'vant'
import AudiButton from '@/components/audi-button'
import navigation from '../../components/navigation'
import {
  getqueryRelativeInformation,
  getqueryRelativeInformationById
} from '@/api/api'

Vue.use(Form)
  .use(Field)
  .use(Button)
  .use(ActionSheet)
  .use(Cell)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Area)
  .use(Switch)
  .use(Overlay)
  .use(Toast)
export default {
  components: {
    AudiButton,
    navigation
  },
  data() {
    return {
      formData: {
        userFullName: '',
        idCard: '',
        employerName: '',
        phoneNumber: '',
        immediateFamily: false,
        immediateFamilyName: '',
        immediateFamilyPhoneNumber: '',
        immediateFamilyIdCard: ''
      },
      backType: 'app',
      message0: '',
      message1: '',
      message2: '',
      message3: '',
      message4: '',
      message5: '',
      message6: '',
      readonly: false,
      isShowMessageId: false,
    }
  },

  watch: {
    'formData.immediateFamily': {
      handler(val) {
        console.log(val)
        if (!val) {
          this.formData.immediateFamilyName = ''
          this.formData.immediateFamilyPhoneNumber = ''
          this.formData.immediateFamilyIdCard = ''
        }
      }
    }
  },

  mounted() {
    const ismessageId = this.$route.query.messageId
    const params = {
      messageId: ismessageId
    }
    if (ismessageId) {
      getqueryRelativeInformationById(params).then((res) => {
        if (res.data.data && res.data.data.informationStatus === 2) {
          this.isShowMessageId = false
          this.getqueryRelativeInformation()
        } else {
          this.isShowMessageId = true
        }
      })
    } else {
      this.getqueryRelativeInformation()
    }
    
  },
  methods: {
    isShowSwitch() {
      // if (!this.readonly) {
        this.formData.immediateFamily = !this.formData.immediateFamily
      // }
    },
    onSubmit() {
      if (!this.formData.immediateFamily) {
        if (this.testName(this.formData.userFullName)
         && this.testIdcard(this.formData.idCard)
          && this.testOrder(this.formData.employerName)
          && this.testMobile(this.formData.phoneNumber)) {
          localStorage.setItem('enterpriseFrom', JSON.stringify(this.formData))
          this.$router.push({
            name: 'data-upload'
          })
        }
      } else {
        if (this.testName(this.formData.userFullName)
         && this.testIdcard(this.formData.idCard)
          && this.testOrder(this.formData.employerName)
           && this.testMobile(this.formData.phoneNumber)
           && this.testNameRelatives(this.formData.immediateFamilyName)
           && this.testIdcardRelatives(this.formData.immediateFamilyIdCard)
           && this.testMobileRelatives(this.formData.immediateFamilyPhoneNumber)) {
          localStorage.setItem('enterpriseFrom', JSON.stringify(this.formData))
          this.$router.push({
            name: 'data-upload'
          })
        }
      }
    },

    getqueryRelativeInformation() {
      getqueryRelativeInformation().then((res) => {
        const dataFrom = JSON.parse(localStorage.getItem('enterpriseFrom'))
          if (dataFrom) {
            this.formData = {
              ...dataFrom
            }
            if (dataFrom.immediateFamily == 1) {
              this.formData.immediateFamily = true
            } else {
              this.formData.immediateFamily = false
            }
          }else{
            if (res.data.data) {
              // this.readonly = true
              this.formData = {
                ...res.data.data
              }
              if (res.data.data.immediateFamily == 1) {
                this.formData.immediateFamily = true
              } else {
                this.formData.immediateFamily = false
              }
            }
          }
  
      })
    },

    onInput0(val) {
      this.testName(val)
    },
    onInput1(val) {
      this.testIdcard(val)
    },
    onInput2(val) {
      this.testOrder(val)
    },
    onInput3(val) {
      this.testMobile(val)
    },
    onInput4(val) {
      this.testNameRelatives(val)
    },
    onInput5(val) {
      this.testIdcardRelatives(val)
    },
    onInput6(val) {
      this.testMobileRelatives(val)
    },
    testName(val) {
      if (val.length > 0) {
        this.message0 = ''
        return true
      }
      this.message0 = '请输入姓名'
      return false
    },

    testOrder(val) {
      if (val.length > 0) {
        this.message2 = ''
        return true
      }
      this.message2 = '请输入工作单位'
      return false
    },
    testMobile(val) {
      const re = /^1\d{10}$/
      if (re.test(val)) {
        this.message3 = ''
        return true
      }
      this.message3 = '手机号格式错误'
      return false
    },
    testNameRelatives(val) {
      if (val.length > 0) {
        this.message4 = ''
        return true
      }
      this.message4 = '请输入直系亲属姓名'
      return false
    },
    testIdcard(val) {
      const regs = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/
      if (val.length <= 0 || !regs.test(val)) {
        this.message1 = '身份证号错误'
        return false
      }
      this.message1 = ''
      return true
    },
    testIdcardRelatives(val) {
      const regs = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/
      if (val.length <= 0 || !regs.test(val)) {
        this.message5 = '身份证号错误'
        return false
      }
      this.message5 = ''
      return true
    },
    testMobileRelatives(val) {
      const re = /^1\d{10}$/
      if (re.test(val)) {
        this.message6 = ''
        return true
      }
      this.message6 = '手机号格式错误'
      return false
    }

  }
}
</script>

<style lang='less' scoped>
::v-deep .van-cell {
  padding: 16px;
}

::v-deep .van-cell::after {
  border-bottom: 1px #e5e5e5 solid;
}

::v-deep .van-button {
  border-radius: 0;
}

::v-deep .van-field__label {
  font-size: 16px;
  color: #000;
  width: 70px;
}
.immediate-list{
    display:flex;
    align-items: center;
    justify-content:space-between;
    padding:0 16px
}

::v-deep .van-field {
  .van-field__body {
    textarea {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }

    input {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }
  }
}

._container{
height: 100vh;
    display: flex;
    flex-direction: column;
}
.btn-delete-height {
  height: 80px;
}
.btn-delete-wrapper {
  margin: 16px;
}
.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  p{
      margin:0;
      text-align: center;
      font-size: 12px;
  font-family: 'Audi-Normal';
  }
}
.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .block {
    width: 90%;
    height: 120px;
    line-height:120px;
    text-align: center;
    background-color: #fff;
  }
</style>
