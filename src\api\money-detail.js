import request from '../router/axios'
import api from '../config/url'
import {
  getToken
} from '../utils/auth'

const baseUrl = api.BaseApiUrl


// 查询是否用过数字人名币接口
export const getWhetherOrderDigitalCertificate = (params) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/v1/whetherOrderDigitalCertificate`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  params: {
    ...params
  }
})

// 查询订单的数字人民币支付状态
export const getOrderStatusCNY = (params) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/v1/getOrderDigitalCertificate`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  params: {
    ...params
  }
})
