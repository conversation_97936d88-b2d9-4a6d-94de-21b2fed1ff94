/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-13 16:01:47
 * @Last Modified by: vhj<PERSON>yi
 * @Last Modified time: 2024-05-We 10:23:57
 * 常量配置
 */


/**
 * q5q6 座椅数据
 * q5e逻辑座椅
 * tagCode的值与后端人员约定.
 * tagCode: Q5E_7SEAT  == 7座 (标配7座)
 * tagCode: Q5E_6SEAT  == 6座 (标配6座)
 *
 * tagCode查询当前车型的选装包数据,
 */
export const SEAT_DATA_Q5_Q6 = {
  q5e: {
    // 7座 则显示标配文案
    Q5E_7SEAT: {
      // name: '七座舒适选装'
    },
    Q5E_6SEAT: {
      // name: '六座舒享套装'
    },
    defaultOptionCode: 'WE8', // 默认6座选装包
    seat6HideCodeList: ['8I6+PV3+4A4', '4D3+PV3+4A4', '4A4', 'PV3'], // 6座要隐藏的选装包
    seat7HideCodeList: ['WE8', '8I6+WE8', '4D3+WE8'] // 7座要隐藏的选装包
  },

  q6: {
    Q6_7SEAT: {
      // name: '七座舒适选装'
    },
    Q6_6SEAT: {
      // name: '六座舒享套装'
    },
    defaultOptionCode: 'PS1', // 默认6座选装包
    seat6HideCodeList: ['PS3', 'PS8'],
    seat7HideCodeList: ['PS1']
  }
}


/**
 * Q6 特殊车型modelLineCode列表. (是由座椅选择车型 + 以及座椅显示价格套装隐藏价格)
 */
export const Q6_SAMURAI = [
  // 23款
  'G6ICAY005', // 45黑武士6座
  'G6ICAY004', // 45黑武士7座
  'G6ICAY003', // 45影武士6座
  'G6ICAY002', // 45影武士7座
  'G6ICBY004',
  'G6ICBY003',
  // 24款
  'G6IBCY003', // 观云 羽林 6座
  'G6IBCY002', // 观云 羽林 7座
  'G6ICCY003', // 观云 飞骑 6座
  'G6ICCY002', // 观云 飞骑 7座
  'G6ICAY007', // 齐云 飞骑 7座
  'G6ICAY008', // 齐云 飞骑 6座
  'G6IBAY002', // 齐云 羽林 7座
  'G6IBAY003', // 齐云 羽林 6座
  'G6ICAY011', // 45黑武士 6座
  'G6ICAY009', // 45黑武士 7座
  'G6ICAY012', // 45影武士 6座
  'G6ICAY010', // 45影武士 7座
  'G6ICAY013', // 45 special line 7座
  'G6ICAY014', // 45 special line 6座

  'G6ICAY020', // "24款 Q6 45 TFSI quattro 黑武士 6座
  'G6ICAY018', // "24款 Q6 45 TFSI quattro 黑武士 7座

  'G6ICAY021', // 24款 Q6 45 TFSI quattro 影武士 6座
  'G6ICAY019', // 24款 Q6 45 TFSI quattro 影武士 7座
  'G6ICAY015', // 24款 Q6 45 TFSI quattro RS套件竞速版

  'G6ICBY009', //  24款 Q6 50 TFSI quattro 黑武士 6座
  'G6ICBY010', // 24款 Q6 50 TFSI quattro 影武士 6座
  'G6IBBY002', // 24款 Q6 50 TFSI quattro 行云型 羽林套装 6座
  'G6ICBY008', // 24款 Q6 50 TFSI quattro 行云型 飞骑套装 6座

  'G6ICAY017', // new 齐云 飞骑 6座
  'G6ICAY016', // new 齐云 飞骑 7座

  // new 24款
  'G6ICAY028', // 24款 Q6 45 TFSI quattro 影武士 7座
  'G6ICBY011', // 24款 Q6 50 TFSI quattro 行云型 飞骑套装 6座
  'G6IBCY005', // 24款 Q6 40 TFSI quattro 观云型 羽林套装 6座
  'G6ICAY030', // 24款 Q6 45 TFSI quattro 影武士 6座
  'G6ICAY024', // 24款 Q6 45 TFSI quattro RS套件竞速版
  'G6IBAY004', // 24款 Q6 45 TFSI quattro 齐云型 羽林套装 7座
  'G6ICCY004', // 24款 Q6 40 TFSI quattro 观云型 飞骑套装 7座
  'G6ICBY013', // 24款 Q6 50 TFSI quattro 影武士 6座
  'G6ICAY029', // 24款 Q6 45 TFSI quattro 黑武士 6座
  'G6IBCY004', // 24款 Q6 40 TFSI quattro 观云型 羽林套装 7座
  'G6ICAY025', // 24款 Q6 45 TFSI quattro 齐云型 飞骑套装 7座
  'G6ICCY005', // 24款 Q6 40 TFSI quattro 观云型 飞骑套装 6座
  'G6ICBY012', // 24款 Q6 50 TFSI quattro 黑武士 6座
  'G6ICAY027', // 24款 Q6 45 TFSI quattro 黑武士 7座
  'G6ICAY026', // 24款 Q6 45 TFSI quattro 齐云型 飞骑套装 6座
  'G6IBBY003', // 24款 Q6 50 TFSI quattro 行云型 羽林套装 6座
  'G6IBAY005', // 24款 Q6 45 TFSI quattro 齐云型 羽林套装 6座
  'G6ICAY022', // 24款 Q6 45TFSI quattro 逐云版 7座
  'G6ICAY023', // 24款 Q6 45TFSI quattro 逐云版 6座

  'G6IBCY007', // 25款 Q6 40 TFSI quattro 观云型 羽林套装 6座
  'G6IBCY006', // 25款 Q6 40 TFSI quattro 观云型 羽林套装 7座
  'G6ICCY006', // 25款 Q6 40 TFSI quattro 观云型 飞骑套装 7座
  'G6ICCY007', // 25款 Q6 40 TFSI quattro 观云型 飞骑套装 6座
  'G6ICAY032', // 25款 Q6 45 TFSI quattro 齐云型 飞骑套装 6座
  'G6ICAY031', // 25款 Q6 45 TFSI quattro 齐云型 飞骑套装 7座
  'G6IBAY007', // 25款 Q6 45 TFSI quattro 齐云型 羽林套装 6座
  'G6IBAY006', // 25款 Q6 45 TFSI quattro 齐云型 羽林套装 7座
  'G6ICBY014', // 25款 Q6 50 TFSI quattro 行云型 飞骑套装 6座
  'G6IBBY004', // 25款 Q6 50 TFSI quattro 行云型 羽林套装 6座
  'G6ICAY034', // 25款 Q6 45 TFSI quattro 黑武士 6座
  'G6ICAY033', // 25款 Q6 45 TFSI quattro 黑武士 7座
  'G6ICBY015', // 25款 Q6 50 TFSI quattro 黑武士 6座
  'G6IBCY008' // 25款 Q6 40 TFSI quattro 观云型 羽林套装特殊款
]


/**
 * 特殊车型,只显示标装,不显示推荐组合
 * 到选装页面也不需要强制要求选中一个组合
 */
export const STANDARD_CAR = [
  '498B2Y006', '498B2Y007', '498B2Y008', '498BZG002', '498BZG003',
  'G4ICC3003', 'G4ICC3004', 'G4ICC3005', 'G4ICF3004', 'G4ICF3005', 'G4ICF3006',
  'G6ICBY002', 'G6ICAY002', 'G6ICAY003', 'G6ICAY004', 'G6ICAY005', 'G6ICBY003', 'G6ICBY004',
  '498B2Y012', '498B2Y011',
  'G6ICAY011',
  'G6ICAY009',
  'G6ICAY012',
  'G6ICAY010',
  'G6ICBY006',
  'G6ICBY007',
  'G6ICAY006',
  'G6ICAY013', // 45 special line 7座
  'G6ICAY014', // 45 special line 6座
  'G6ICAY020', // "24款 Q6 45 TFSI quattro 黑武士 6座
  'G6ICAY018', // "24款 Q6 45 TFSI quattro 黑武士 7座
  'G6ICAY019', // 24款 Q6 45 TFSI quattro 影武士 7座

  'G6ICAY021', // 24款 Q6 45 TFSI quattro 影武士 6座
  'G6ICBY009', //  24款 Q6 50 TFSI quattro 黑武士 6座
  'G6ICBY010', // 24款 Q6 50 TFSI quattro 影武士 6座
  '498B2Y014', // MR55黑武士
  '498B2Y015' // MR55RS
]


/**
 * 夏日礼包. 只有a7 和 q5e车型有
 */
export const SUMMER_PACKAGE = ['4D3+PV3+4A4+YED', 'PS8+WAB+GZ2']

/**
 * a7l 后排剧院级音响智能屏套装
 */
export const A7L_INTELLIGENT_AUDIO = {
  name: '后排剧院级音响智能屏套装',
  // 9WP+YED ,这个顺序在其他组件有依赖,如果要更新,注意对应的索引值
  optionCode: ['9VS', '9WP+YED'],
  price: 28000,
  price9wp: 20000
}


/**
 * a7l 耀黑套装 跟6NQ(黑色车内顶棚关联)
 */
export const BLACK_A7L = ['498B2Y002', '498B2Y004', '498B2Y005', '498BZY002', '498BZY003', '498BZY012']


/**
 * a7l
 * 奥迪高级智能数字套装 WAO
 * 显示特殊文案: 支持部分手机机型
 *
 * 数字套装的子装备: 数字钥匙 2F1
 * 显示关联的兼容机型页面
 */
export const A7L_FIGURE_KEY = ['WAO', '2F1']
