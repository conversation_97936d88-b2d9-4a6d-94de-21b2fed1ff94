<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-10-11 14:42:29
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-10-11 15:37:28
 * @FilePath     : \src\view\exception\consoles-page\errors\charging-pile-qrcode.vue
 * @Descripttion : 
-->
<template>
  <div class="error-box" data-flex="main:center cross:center">
    <div class="error-main charging-pile-qrcode">
      <div class="error-qrcode">
        <i class="error-icon"></i>
        <p>该二维码已失效，请扫描最新二维码</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChargingPileQrcode'
}
</script>

<style lang="less" scoped>
.error-box {
  min-height: 100%;

  .error-qrcode {
    margin-top: -120px;

    .error-icon {
      position: relative;
      display: block;
      margin: 0 auto;
      border: solid 1px #000;
      width: 71px;
      height: 71px;
      border-radius: 50%;
      overflow: hidden;

      &::after,
      &::before {
        content: '';
        position: absolute;
      }

      &::before {
        border-left: solid 1px #000;
        width: 1px;
        height: 28px;
        top: 50%;
        left: 50%;
        margin-top: -14px;
      }

      &::after {
        width: 2px;
        height: 4px;
        left: 50%;
        top: calc(50% - 10px);
        margin-left: -1px;
        background-color: #fff;
      }
    }

    p {
      margin: 25px 0 0 0;
      font-size: 18px;
    }
  }
}
</style>