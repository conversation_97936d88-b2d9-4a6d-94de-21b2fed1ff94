// import { resolve } from 'core-js/fn/promise'
import store from "../store/index.js"
import storage from "./storage.js"
//向原生请求token
export function getUserInfoFn(params) {
  // if (process.env.NODE_ENV === 'pre') {
  //   const res = {
  //     token: 'tyn-cXBmS4UACwNu_t3WApN4ts2sQDi7',
  //     userId: 3100000001054210 //3100000001054210   //3100000001055755 //3100000001056316
  //   }
  //   storage.set("userId", res.userId)
  //   return Promise.resolve(res)
  // }

  return new Promise((resolve, reject) => {
    // return resolve({
    //   token: 'SWrekt_7RBAAF82dUA4tf6s5SdCgz01v',
    // })
    if (window.webViewJavascriptBridge) {
      console.log('向原生请求token----------')
      window.webViewJavascriptBridge.callHandler(
        'getAudiUserInfo', { name: 'sun', ...params },
        (error, data) => {
          if (error) {
            console.log('获取getAudiUserInfo失败')
            return reject(error)
          }
          console.log('获取到的token信息-------------', data.token)
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })
}
//获取状态栏高度
export function getNavigationBarHeight() {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      console.log('----navigationBarHeight')
      window.webViewJavascriptBridge.callHandler(
        'navigationBarHeight', { name: '' },
        (error, data) => {
          if (error) {
            console.log('获取navigationBarHeight')
            return reject(error)
          }
          console.log('获取到的navigationBarHeight---------', data)
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })

}
//返回goBack prepage
export function getPrepage() {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      window.webViewJavascriptBridge.callHandler(
        'prepage', { name: '' },
        (error, data) => {
          if (error) {
            return reject(error)
          }
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })
}

//调用APP分享
export function getAppShareWechat(params) {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      window.webViewJavascriptBridge.callHandler(
        'audiShare',
        params,
        (error, data) => {
          if (error) {
            return reject(error)
          }
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })
  
}
//打开APP页面
export function getOpenAppRoutePath(path, params) {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      window.webViewJavascriptBridge.callHandler(
        'openRoutePath', {
          path: path,
          params: params
        },
        (error, data) => {
          if (error) {
            return reject(error)
          }
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })
}
//打开APP web页面
export function getAudiOpen(path, params) {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      window.webViewJavascriptBridge.callHandler(
        'audiOpen', {
          path: path,
          params: params
        },
        (error, data) => {
          if (error) {
            return reject(error)
          }
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })
}
/**
 * 打开APP 图片预览页面
 * index 下标
 * urls 图片集合list
 * */
export function showImage(imgIndex, urls) {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      window.webViewJavascriptBridge.callHandler(
        'showImages', {
          index: imgIndex,
          urls: urls
        },
        (error, data) => {
          if (error) {
            return reject(error)
          }
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })
}

//获取手机端底部的高度//navigationHeight
export function getCurrentNavigationHeight() {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      window.webViewJavascriptBridge.callHandler(
        'getCurrentNavigationHeight', { name: '' },
        (error, data) => {
          if (error) {
            return reject(error)
          }
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })
}

/***
   * toast
   *这里需要通知Native端需要展示的Toast类型
  1.type为fail展示错误的提示
  2.type为success展示正确的提示
  3.message为提示的文本
   */
export function appToast(type, message) {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      window.webViewJavascriptBridge.callHandler(
        'toast', {
          type: type,
          message: message
        },
        (error, data) => {
          if (error) {
            return reject(error)
          }
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })

}
/**
 * 调用APP的Loading事件
 * @param {0等于取消，1=显示} show 
 * @returns 
 */
export function toggleLoading(show) {
  return new Promise((resolve, reject) => {
    if (window.webViewJavascriptBridge) {
      window.webViewJavascriptBridge.callHandler(
        'toggleLoading', { show: show },
        (error, data) => {
          if (error) {
            return reject(error)
          }
          return resolve(data)
        }
      )
    } else {
      return reject('找不到window.webViewJavascriptBridge')
    }
  })

}

export function closeSurvy(params) {
  window.webViewJavascriptBridge.callHandler(
    'closeSurvey', { url: params.url },
    (error, data2) => {
      if (error) {
        console.log('closeSurvey')
        return
      }
    }
  )
}

// 调用原生app的方法
export const callNative = (name, param) => {
  // 模拟非app环境的逻辑
  // const { env } = getUrlParamObj()
  // if (env === 'test') {
  //   console.log('来了')
  //   // 在这里输入测试环境的数据
  //   const testData = {
  //     token: 'zfXWZFM7SZwACwNu_t3XDZ7ynpbjJbv8',
  //     statusBarHeight: 20,
  //     navigationBarHeight: 44,
  //     location: '31.228896,121.453734'
  //   }
  //   console.warn('当前是开发环境, 模拟app数据:', testData)
  //   return Promise.resolve(testData)
  // }

  return new Promise((res) => {
    //
    try {
      window.webViewJavascriptBridge.callHandler(name, param, (err, data) => {
        if (err) {
          console.error(`call native 返回结果错误, name: ${name},err: ${err}`)
        } else {
          res(data)
        }
      })
    } catch (err) {
      console.error(`native内部错误: nativeName:${name}`, err)
      res({})
    }
  })
}

export const getUrlParamObj = () => {
  const url = window.location.href
  // eslint-disable-next-line no-new-object
  const theRequest = new Object()
  // eslint-disable-next-line no-useless-escape
  const strs = url.split(/[?&#\/]/)
  strs.forEach((e) => {
    if (e.indexOf('=') > -1) {
      const splitArr = e.split('=')
      theRequest[splitArr[0]] = splitArr[1]
    }
  })
  return theRequest
}

export const checkLoginStatus = async () => {
  const { env, token } = getUrlParamObj();
  if (env === "minip") {
    const isLogin = +!!token;
    if(token){
      store.commit("setIsLogin", 1)
      storage.set("token", token);
    } else {
      store.commit("setIsLogin", 0)
      storage.remove("token", token)
    }
  } else {
    const userInfo = await getUserInfo({'sync': 'login'})
    store.commit("setIsLogin", +userInfo.isLogin)
    if(+userInfo.isLogin){
      storage.set("token", userInfo.token);
    } else {
      storage.remove("token", userInfo.token);
      storage.remove("userId", userInfo.userId);
    }
  }
}