<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-11-20 20:06:27
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-12-06 16:14:47
 * @FilePath     : \src\components\cascader.vue
 * @Descripttion :
-->
<template>
  <div class="lan-cascader">
    <div class="title" data-flex="main:justify cross:center">
      <div class="h1">{{ title }}</div>
      <van-icon name="cross" class="icon-cross" @click="() => { $emit('update:close', false) }" />
    </div>
    <van-tabs v-model="active" @change="handleTabOnChang" animated swipeable>
      <van-tab v-for="(tab, index) in tabs" :title="tab || `选项${index}`" :key="index">
        <div class="content">
          <div :class="`item skin-${item.skin || item.value} ${selected[index] === item.value ? ' item-checked' : ''}`" data-flex="main:justify cross:center" @click="handleSelectColumn(index, item)" v-for="item in column[index]" :key="item.value">
            <template v-if="item.skin === 'dealer'">
              <div class="dealer">
                <div class="name">{{ item.text || item.dealerName || '上汽奥迪代理商' }}</div>
                <div class="desc">{{ item.distanceStr ? item.distanceStr + ' | ' : '' }}{{ item.dealerAdrress }}</div>
              </div>
              <van-icon name="success" class="icon-success" />
            </template>
            <template v-else>
              <div class="name">{{ item.text }}</div>
              <van-icon name="success" class="icon-success" />
            </template>
          </div>
          <div class="tips" v-if="asyncTabIndex > 0 && (active + 1 === maxTab) && (loading || isEmpty)">
            <van-loading class="lan-loading" size="40" v-if="loading" />
            <van-empty class="lan-empty" description="没有更多信息" v-if="isEmpty" />
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import Vue from 'vue'
import {
  Tab, Tabs,
  Toast, Empty
} from 'vant'
import { addNameToTree, delay } from '@/utils'

Vue.use(Tab).use(Tabs).use(Toast).use(Empty)

export default {
  name: 'lan-cascader',
  props: {
    title: {
      type: String,
      default: '这里是标题'
    },
    options: {
      type: Array,
      default: () => []
    },
    fieldNames: {
      type: Object,
      default: () => {}
    },
    asyncTabIndex: {
      type: Number,
      default: -1
    },
    asyncColumn: {
      type: Array,
      default: () => []
    },
    maxTab: {
      type: Number,
      default: 3
    },
    close: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    options: {
      handler(list, oldVal) {
        this.handleList(list)
      },
      immediate: true,
      deep: true
    },
    asyncColumn: {
      handler([index, value, list]) {
        this.handleAsyncColumn(index, value, list)
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      active: 0,
      tabs: ['请选择'],
      column: [],
      list: [],
      selected: [],
      loading: false,
      isEmpty: false
    }
  },
  mounted() {},
  created() {
    console.log('%c [ cascader created ]-106', 'font-size:14px; background:#cf222e; color:#fff;', this.options)
  },
  methods: {
    handleList(list) {
      console.log('%c [ handleList ]-105', 'font-size:14px; background:#cf222e; color:#fff;')
      this.list = addNameToTree(list, this.fieldNames)
      this.column = [this.list]
    },
    handleSelectColumn(index, item) {
      const { maxTab, active } = this

      const {
        value, text, children
      } = item || { value: '', text: '', children: [] }

      if ((maxTab === index + 1) && item.skin === 'dealer' && (!value || !text)) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '当前代理商信息不完善',
          forbidClick: true,
          duration: 500
        })
      }

      const tabsList = [...this.tabs]
      if (tabsList.length && this.selected[index] === value && !(maxTab === index + 1)) {
        this.active++
        return
      }

      const tabs = active < tabsList.length ? tabsList.splice(0, active + 1) : tabsList

      console.log('%c [ tabs ]-143', 'font-size:14px; background:#cf222e; color:#fff;', tabs)
      // const tab = active < tabs.length ? tabs.splice(0, active + 1) : tabs
      tabs[index] = text
      const childrenLength = children?.length || 0
      const { asyncTabIndex } = this
      const isAsync = !childrenLength && index === asyncTabIndex
      const selected = [...this.selected]
      selected[index] = value || ''
      this.selected = selected
      if (maxTab === index + 1) {
        this.handleFinished(item)
        return
      }
      if (childrenLength || isAsync) {
        if (isAsync) {
          this.loading = true
          if (this.isEmpty) this.isEmpty = false
          delay(() => {
            if (this.loading) {
              this.loading = false
            }
          }, 10000)
          this.$emit('asyncNext', { value, index })
          this.handleAutoCutTab(tabs, maxTab)
          return
        }

        this.column[index + 1] = children
      }
      this.handleAutoCutTab(tabs, maxTab)
    },
    handleAutoCutTab(tabs, maxTab) {
      if (tabs.length >= maxTab) return
      tabs.push('请选择')
      this.tabs = tabs
      delay(() => {
        this.active++
      }, 400)
    },
    handleAsyncColumn(index, value, list) {
      this.column[index + 1] = list
      delay(() => {
        if (this.loading) this.loading = false
        if (!list.length) this.isEmpty = true
      }, 600)
    },
    handleFinished(item) {
      const { selected } = this
      this.$emit('finish', { selected, item })
      delay(() => {
        this.$emit('update:close', false)
      }, 600)
    },
    handleTabOnChang(index) {
      // const { loading, isEmpty } = this
    }
  }
}
</script>
<style lang="less" scoped>
.lan-cascader {
  padding: 20px 24px;
  height: 75vh;
  &, .van-tabs /deep/ .van-tabs__wrap .van-tabs__nav , .van-tabs /deep/ .van-tabs__content .content .tips , .van-tabs /deep/ .van-tabs__content .content .icon-success{
    background-color: #F7F7F7;
  }
  .title {
    height: 40px;
    .h1 {
      font-size: 16px;
      line-height: 40px;
      font-weight: 600;
      color: #000
    }
    .icon-cross {
      display: block;
      width: 24px;
      height: 24px;
      // font-size: 18px;
      background: url('~@/assets/icon-close.png') no-repeat 50% / contain;
      &::before {
        display: none;
      }
    }
  }
  .van-tabs {
    padding-top: 10px;
    /deep/ .van-tabs__wrap {
      height: 30px;
      .van-tabs__nav {
        padding: 0;
        justify-content: start;
        .van-tab {
          flex: none;
          font-size: 14px;
          color: #808080;
          // min-width: 84px;
          width: 33.333333%;
          padding: 0 16px;
          &.van-tab--active {
            color: #1A1A1A ;
          }
        }
        .van-tabs__line {
          bottom: 0;
          height: 1px;
          width: 84px;
          border-radius: 0;
        }
      }
    }
    /deep/ .van-tabs__content {
      .content {
        margin-top: 10px;
        height: calc(75vh - 125px);
        overflow: auto;
        position: relative;
        &::-webkit-scrollbar{
          display: none;
        }
        .item {
          margin-top: 8px;
          height: 46px;
          border-bottom: .5px solid #D9D9D9;
          &.skin-dealer {
            height: auto;
          }
          .dealer {
            width: calc(100% - 26px);
            padding: 12px 0;
            .name {
              line-height: 22px;
            }
            .desc {
              margin-top: 4px;
              font-size: 12px;
              color: #999;
              line-height: 20px;
            }
          }
          .name {
            color: #1A1A1A;
            font-size: 14px;
          }
          .icon-success {
            display: block;
            box-sizing: border-box;
            width: 16px;
            height: 16px;
            text-align: center;
            color: #fff;
            border: solid 1px #fff;
            border-radius: 50%;
            overflow: hidden;
            &::before {
              display: none;
            }
            // &::after {
            //   content: '\e728';
            //   position: absolute;
            //   transform: scale(.6);
            //   left: .1px;
            //   top: -.4px;
            //   color: #fff;
            // }
          }
          &.item-checked {
            .icon-success {
              border-color: #000;
              background: #000 url('~@/assets/icon-checked.png') no-repeat 50% / contain;
              &::after {
                color: #000;
              }
            }
          }
        }

        .tips {
          position: absolute;
          z-index: 22;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          // background-color: #fff;
          .lan-loading {
            font-size: 40px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
  }

}
</style>
