<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-08-09 11:55:29
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-11-08 15:35:38
 * @FilePath     : \src\view\order\logistics-status.vue
 * @Descripttion :
-->
<template>
  <div class="">
    <div
      class="order-tips align-left"
      v-if="estimateDeliveryTime"
    >
      预计交付周期：{{ estimateDeliveryTime }}
    </div>
    <div class="_container">
      <img
        class="_heardImg"
        :src="carImgUrl"
      >
      <order-steps :statuslist="statuslist" />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import orderSteps from '@/components/order-steps.vue'
import { getTraceInfo, updateOrderTraceInfoStatus } from '../../api/api.js'

import api from '@/config/url'

const baseOssHost = api.BaseOssHost
export default {
  components: { orderSteps },
  data() {
    return {
      statuslist: [],
      estimateDeliveryTime: null
    }
  },
  computed: {
    ...mapState({
      carImgUrl: (state) => baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl // 主图
    })
  },
  mounted() {
    this.getTraceInfo()
  },
  methods: {
    async getTraceInfo() {
      const { orderId } = this.$route.query
      const res = await getTraceInfo({ orderId })
      this.statuslist = res.data.data.reverse()
      this.statuslist.forEach((i, index) => {
        if (i.estimateDeliveryTime && index === 0) {
          this.estimateDeliveryTime = i.estimateDeliveryTime
        }
      })

      updateOrderTraceInfoStatus({ orderId })
    }
  }
}
</script>

<style lang='less' scoped>
  ._container {
    width: 100%;
    padding: 16px;
    box-sizing: border-box;

    ._heardImg {
      width: 100%;
      margin-bottom: 18px;
    }
  }
  .heard_date{
    line-height: 44px;
    width: 100%;
    box-sizing: border-box;
    background-color: #F2F2F2;
    padding: 0 16px;
    font-size: 14px;
    color: #000000;
    overflow: hidden;
  }
</style>
