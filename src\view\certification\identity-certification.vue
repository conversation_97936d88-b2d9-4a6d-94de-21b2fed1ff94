<template>
  <div class="idcertification">
    <div v-if="isPhotograph">
      <div class="item-title-bold">拍照上传证件号或驾驶证</div>
      <img
        class="item-img"
        src="../../assets/img/icon-identity1.png"
        @click="onUploadOcrIdCard(1)"
      />
      <div
        class="item-title-bold"
        style="text-align: center; justify-content: center; font-size: 14px"
      >
        上传证件号
      </div>
      <div class="line_content">
        <div class="s_line" />
        <div class="text_center">上传一种证件即可</div>
        <div class="e_line" />
      </div>
      <img
        class="item-img"
        src="../../assets/img/icon-identity2.png"
        @click="onUploadOcrIdCard(2)"
      />
      <div
        class="item-title-bold"
        style="text-align: center; justify-content: center; font-size: 14px"
      >
        上传驾驶证
      </div>
      <div class="interval_line" />

      <div class="line">
        <div class="title" style="font-weight: bold">其他方式</div>
        <!-- <div class="item_btn">(该方式仅支持港澳台和外籍人士上传证件)</div> -->
        <div class="btn-change" />
      </div>

      <div class="line">
        <div class="title">上传护照或其他证件</div>

        <div class="btn-change" @click="onElseCertification">
          <van-icon class="btn-icon" name="arrow" size="16px" />
        </div>
      </div>
    </div>

    <!-- 上传成功页面-->
    <div v-if="isFileOK">
      <!-- <div style="justify-content: center; display: flex">
        <img
          style="width: 24px; height: 24px"
          src="../../assets/img/contract-success2.png"
        >
        <div style="display: flex; align-items: center; padding-left: 8px">
          上传成功！
        </div>
      </div> -->
      <img class="item-img-result" :src="base64" />
      <div class="line_content">
        <div class="s_line" />
        <div class="text_center">请确认识别结果（可修改）</div>
        <div class="e_line" />
      </div>
      <div class="user-name">身份信息</div>
      <div class="line-border-bottom">
        <div class="name" style="font-weight: bold">姓名</div>
        <div class="str" v-if="!isChange">
          {{ name }}
        </div>
        <div class="str" v-if="isChange">
          {{ updateName }}
        </div>
      </div>
      <!-- <p
        class="is-text-wang"
        v-if="isWrong2"
      >
        <img src="../../assets/wall/icon11.png">
        请填写姓名
      </p> -->
      <div class="line-border-bottom">
        <div class="name" style="font-weight: bold">证件号</div>
        <div class="str" v-if="!isChange">
          {{ idNumber }}
        </div>
        <div class="str" v-if="isChange">
          {{ updateGid }}
        </div>
      </div>
      <!-- <p
        class="is-text-wang"
        v-if="isWrong"
      >
        <img src="../../assets/wall/icon11.png">
        您的证件号有误，请修改
      </p> -->
      <div class="bottom_style">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="onReturnUpdate"
            :text="'修改信息'"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="onConfirm"
            :text="'确认提交'"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </div>
    <!-- 修改页面-->
    <!-- <div v-if="isChange">
      <div class="item-title-bold">
        请选择1种认证方式
      </div>
      <img
        v-if="uploadType === 1"
        class="item-img"
        src="../../assets/img/icon-identity1.png"
        @click="onUploadOcrIdCard(1)"
      >
      <img
        v-if="uploadType === 2"
        class="item-img"
        src="../../assets/img/icon-identity2.png"
        @click="onUploadOcrIdCard(2)"
      >
      <div
        class="item-title-bold"
        style="text-align: center; justify-content: center; font-size: 14px"
      >
        {{ uploadType === 1 ? "上传证件号" : "上传驾驶证" }}
      </div>
      <div class="line_content">
        <div class="s_line" />
        <div class="text_center">
          或
        </div>
        <div class="e_line" />
      </div>

      <div class="line">
        <div class="title">
          手动修改信息
        </div>
        <div
          class="btn-change"
          @click="onConfirm"
        >
          <van-icon
            class="btn-icon"
            name="arrow"
            size="16px"
          />
        </div>
      </div>
    </div> -->

    <input
      class="hide_file"
      ref="leftFile"
      id="upload"
      type="file"
      @change="getFile($event)"
      capture="camera"
      accept="image/camera"
    />
    <!--拍照提示-->
    <van-action-sheet
      v-model="show"
      :safe-area-inset-bottom="true"
      :round="false"
    >
      <div style="padding-right: 16px; padding-left: 16px">
        <div
          class="item-title-bold"
          style="
            text-align: center;
            justify-content: center;
            font-size: 16px;
            margin-top: 18px;
          "
        >
          证件上传示例
        </div>
        <div
          style="
            text-align: center;
            justify-content: center;
            font-size: 14px;
            margin-top: 16px;
            color: #666;
            padding-right: 26px;
            padding-left: 26px;
          "
        >
          请于光线充足的环境下，纯色背景下，四角对齐，横向拍照
        </div>
        <img class="item-img" src="../../assets/img/icon-identity3.png" />
      </div>
      <van-grid :border="false" :column-num="4" :gutter="16">
        <van-grid-item>
          <img src="../../assets/img/icon-identity5.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">缺失</div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity6.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">模糊</div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity7.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">过滤</div>
        </van-grid-item>

        <van-grid-item text="">
          <img src="../../assets/img/icon-identity8.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">
            背景模糊
          </div>
        </van-grid-item>
      </van-grid>
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onUploadingImg"
          :text="'知道了'"
          color="black"
          font-size="15px"
          height="48px"
        />
      </div>
    </van-action-sheet>

    <!--确认提交提示-->
    <van-popup
      v-model="showConfirmsubmitAction"
      :style="{ width: '100%' }"
      position="bottom"
      :close-on-click-overlay="false"
    >
      <div class="item-title-bold-resule">修改信息</div>
      <div
        class="line-border-bottom"
        style="padding-left: 16px; padding-right: 16px"
      >
        <div class="name" style="font-weight: bold">姓名</div>
        <van-field
          class="str"
          type="text"
          v-model="updateName"
          rows="1"
          @input="formValidatorupdateName"
        />
      </div>
      <p class="is-text-wang" v-if="isWrong3">
        <img src="../../assets/wall/icon11.png" />
        请填写姓名
      </p>
      <div
        class="line-border-bottom"
        style="padding-left: 16px; padding-right: 16px"
      >
        <div class="name" style="font-weight: bold">证件号</div>
        <van-field
          class="str"
          type="text"
          v-model="updateGid"
          @input="formValidatorNames"
        />
      </div>
      <p class="is-text-wang" v-if="isWrong1">
        <img src="../../assets/wall/icon11.png" />
        证件号位数有误，请修改
      </p>
      <div style="height: 216px" />
      <div class="bottom_style">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="afterConfirmation"
            text="取消修改"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="beforeConfirmation"
            text="确认修改"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </van-popup>

    <van-popup
      v-model="beforeConfirmationsubmitAction"
      :style="{ width: '90%' }"
    >
      <div
        style="
          background: #f2f2f2;
          height: 126px;
          padding-left: 26px;
          padding-right: 26px;
        "
      >
        <div
          class="item-title-bold"
          style="
            text-align: center;
            justify-content: center;
            font-size: 18px;
            padding-top: 24px;
          "
        >
          重要提示
        </div>
        <div
          style="
            text-align: center;
            justify-content: center;
            font-size: 14px;
            line-height: 24px;
          "
        >
          确认提交后无法进行修改，如信息有误，将影响你的认证，无法获得相关权益
        </div>
      </div>

      <div
        class="line-border-bottom"
        style="padding-left: 16px; padding-right: 16px; margin-top: 12px"
      >
        <div class="name" style="font-weight: bold">姓名</div>
        <div class="str">
          {{ updateName }}
        </div>
        <!-- <van-field
          v-if="isChange"
          class="str"
          type="text"
          v-model="updateName"
          rows="1"
        /> -->
      </div>
      <div
        class="line-border-bottom"
        style="
          margin-bottom: 100px;
          padding-left: 16px;
          padding-right: 16px;
          margin-top: 12px;
        "
      >
        <div class="name" style="font-weight: bold">证件号</div>
        <div class="str">
          {{ updateGid }}
        </div>
        <!-- <van-field
          v-if="isChange"
          class="str"
          type="text"
          v-model="updateGid"
          rows="1"
        /> -->
      </div>

      <div class="bottom_style">
        <div class="div-btn" style="margin-left: 16px">
          <AudiButton
            @click="cancel"
            text="取消"
            color="white"
            font-size="15px"
            height="48px"
          />
        </div>
        <div class="div-btn" style="margin-right: 16px">
          <AudiButton
            @click="onConfirmsubmit"
            text="确认"
            color="black"
            font-size="15px"
            height="48px"
          />
        </div>
      </div>
    </van-popup>
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols lan-dialog-content-f12"
      v-model="dialogShow"
      cancel-button-text="取消"
      confirm-button-text="确定"
      :title="`上汽奥迪申请获取相机权限`"
      show-cancel-button
      :message="`上汽大众需要申请相机权限，以便通过扫一扫、拍摄照片或视频为您提供上传头像、车辆图片、绑车/会员认证、专属桩绑定、发动态、提问题、写文章、评论、扫码充电、形象定制、AI助手相关服务。拒绝或取消授权不影响使用其他服务。`"
      @confirm="dialogConfirm"
      @cancel="dialogCancel"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Image as VanImage,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover,
  ActionSheet,
  Grid,
  GridItem
} from 'vant'
import HtmlImageCompress from 'html-image-compress'
import { callNative, compressFileImg } from '@/utils'

import {
  postOrcIdentifyIdCard,
  postOcrDrivingLicence,
  postIdAndDrivingLicenseRecognition,
  getManCarMemberInfo
} from '@/api/api'
import AudiButton from '@/components/audi-button'
import validateIdCard from '@/utils/idcard'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)
  .use(ActionSheet)
  .use(Grid)
  .use(GridItem)
  .use(VanImage)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      show: false, // 上传拍照提示
      showConfirmsubmitAction: false,
      uploadType: 1, // 上传识别类型 1=证件号，2=驾驶证
      isPhotograph: true, // 拍照页面
      isFileOK: false, // 上传成功页面
      isChange: false, // 修改页面

      name: '',
      address: '',
      idNumber: '',
      base64: '',
      boolRepeatCertificate: 0,
      updateGid: '',
      updateName: '',
      beforeConfirmationsubmitAction: false,
      isWrong: false,
      isWrong1: false,
      isWrong3: false,
      isWrong2: false,
      dialogShow: false
    }
  },

  mounted() {
    this.getManCarMemberInfo()
  },
  methods: {
    async getManCarMemberInfo() {
      const { data } = await getManCarMemberInfo({})

      // 判断 实名认证状态,1-认证用户，2-未认证用户，3-认证审核中，4-认证审核被拒'
      this.boolRepeatCertificate = `${data.data.certificationStatus}`
    },

    async getFile(e) {
      const file = e.target.files[0]

      const reader = new FileReader()
      if (file) {
        // 通过文件流将文件转换成Base64字符串
        reader.readAsDataURL(file)
        // 转换成功后
        const this1 = this
        reader.onloadend = function () {
          // 输出结果
          this1.formData(file, reader.result)
        }
      }
    },
    async formData(file, base64) {
      const htmlImageCompress = new HtmlImageCompress(file, {
        quality: 0.5
      }).then((result) => {
        console.log(result)
        this.base64 = result.base64
        const formData = new FormData()
        formData.append('file', result.file)
        formData.append('fileType', 1)
        formData.append('categoryType', 1)
        formData.append('fileCategoryId', 1)

        if (this.uploadType === 1) {
          this.postOrcIdentifyIdCard(formData)
        } else {
          this.postOcrDrivingLicence(formData)
        }
      })

      // const fileTemp = await compressFileImg({
      //   content: base64,
      //   file: file
      // })
      // var reader = new FileReader();
      // if (file) {
      //   //通过文件流将文件转换成Base64字符串
      //   reader.readAsDataURL(fileTemp);
      //   //转换成功后
      //   const this2 = this;
      //   reader.onloadend = function() {
      //     //输出结果
      //     this2.base64 = reader.result

      //   };
      // }
      // const formData = new FormData();
      // formData.append("file", fileTemp);
      // formData.append("fileType", 1);
      // formData.append("categoryType", 1);
      // formData.append("fileCategoryId", 1);

      // if (this.uploadType === 1) {
      //   this.postOrcIdentifyIdCard(formData);
      // } else {
      //   this.postOcrDrivingLicence(formData);
      // }
    },

    // 识别证件号
    async postOrcIdentifyIdCard(formData) {
      this.$store.commit('showLoading')
      const { data } = await postOrcIdentifyIdCard(formData)
      this.$store.commit('hideLoading')

      this.show = false
      if (data.code === '00') {
        this.isPhotograph = false
        this.isFileOK = true
        this.isChange = false
        this.name = data.data.name
        this.address = data.data.address
        this.idNumber = data.data.idNumber
        this.updateGid = data.data.idNumber
        this.updateName = data.data.name
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    // 识别驾驶证
    async postOcrDrivingLicence(formData) {
      this.$store.commit('showLoading')
      const { data } = await postOcrDrivingLicence(formData)
      this.$store.commit('hideLoading')
      this.show = false
      if (data.code === '00') {
        this.isPhotograph = false
        this.isFileOK = true
        this.isChange = false
        this.name = data.data.name
        this.address = data.data.address
        this.idNumber = data.data.number
        this.updateGid = data.data.number
        this.updateName = data.data.name
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    // 修改
    onReturnUpdate() {
      // 修改状态点击是重新上传
      // if (this.isChange) {
      //   this.isPhotograph = true
      //   this.isFileOK = false
      //   this.isChange = false
      // } else {
      //   this.isPhotograph = false
      //   this.isFileOK = false
      //   this.isChange = true
      // }
      // this.showConfirmsubmitAction = false
      // this.isChange = true
      this.showConfirmsubmitAction = true
    },

    cancel() {
      this.beforeConfirmationsubmitAction = false
      this.showConfirmsubmitAction = false
      // this.isChange=false
    },
    // 其他身份认证
    onElseCertification() {
      this.$router.push({
        path: '/certification/else-certification',
        query: {
          ownerCode: this.dealerCode,
          type: 'appointmentTime'
        }
      })
    },
    onUploadOcrIdCard(type) {
      this.uploadType = type
      this.show = true
    },

    // 上传图
    async onUploadingImg() {
      const data = await callNative('albumCameraEvent', { type: 1 })
      console.log(data, '是否开启相册权限')
      if (data.status) {
        this.$refs.leftFile.click()
      } else {
        this.dialogShow = true
      }
    },
    dialogCancel() {},
    dialogConfirm() {
      callNative('openpage', {})
    },
    // 提交
    onConfirm() {
      // this.showConfirmsubmitAction = true
      if (
        this.name === ''
        && this.updateName === ''
        && this.idNumber === ''
        && this.updateGid === ''
      ) {
        this.isWrong2 = true
        callNative('toast', { type: 'fail', message: '请填写姓名和证件号' })
        return
      }
      if (this.name === '' && this.updateName === '') {
        this.isWrong2 = true
        callNative('toast', { type: 'fail', message: '请填写姓名' })
        return
      }

      if (this.idNumber === '' && this.updateGid === '') {
        this.isWrong = true
        callNative('toast', { type: 'fail', message: '请填写证件号' })
        return
      }
      if (!validateIdCard(this.updateGid)[0]) {
        this.isWrong = true
        return
      }
      this.isWrong = false
      this.isWrong2 = false
      this.beforeConfirmationsubmitAction = true
    },
    formValidatorNames(name) {
      if (!validateIdCard(name)[0]) {
        this.isWrong1 = true
      } else {
        this.isWrong1 = false
      }
    },

    formValidatorupdateName(name) {
      if (name === '') {
        this.isWrong3 = true
      } else {
        this.isWrong3 = false
      }
    },
    // 确认提交
    async onConfirmsubmit() {
      if (this.isChange) {
        if (!validateIdCard(this.updateGid)[0]) {
          callNative('toast', {
            type: 'fail',
            message: '身份证/机构代码位数有误，请修改'
          })
          return
        }
      } else {
        if (!validateIdCard(this.idNumber)[0]) {
          callNative('toast', {
            type: 'fail',
            message: '身份证/机构代码位数有误，请修改'
          })
          return
        }
      }

      const param = {
        authType: 0,
        name: this.name,
        idNumber: this.idNumber,
        image: this.base64,
        certificateType: this.uploadType,
        boolRepeatCertificate: this.boolRepeatCertificate == '4',
        submitType: this.boolRepeatCertificate == '4' ? 2 : 1,
        updateGid: this.isChange ? this.updateGid : this.idNumber,
        updateName: this.isChange ? this.updateName : this.name
      }
      this.$store.commit('showLoading')
      try {
        const { data } = await postIdAndDrivingLicenseRecognition(param)
        this.$store.commit('hideLoading')
        if (data.code === '200') {
          this.getMemberInfo()
        } else {
          callNative('toast', { type: 'fail', message: data.message })
        }
      } catch (e) {
        callNative('toast', {
          type: 'fail',
          message: '网络错误，身份认证失败'
        })
        console.log(e)
      }
    },

    async getMemberInfo() {
      const { data } = await getManCarMemberInfo({})

      // 判断 实名认证状态,1-认证用户，2-未认证用户，3-认证审核中，4-认证审核被拒'
      if (data.data.certificationStatus == 1) {
        this.$router.push({
          path: '/certification/identity-certification-status',
          query: { type: 0 }
        })
      } else if (data.data.certificationStatus == 3) {
        this.$router.push({
          path: '/certification/identity-certification-in',
          query: {}
        })
      }
    },
    beforeConfirmation() {
      if (this.updateName === '' && this.updateGid === '') {
        callNative('toast', { type: 'fail', message: '请填写姓名和证件号' })
        this.isWrong3 = true
        this.isWrong1 = true
        return
      }
      if (this.updateName === '' || this.updateGid === '') {
        if (this.updateName === '') {
          callNative('toast', { type: 'fail', message: '请填写姓名' })
          this.isWrong3 = true
        } else {
          this.isWrong3 = false
        }
        if (this.updateGid === '') {
          this.isWrong1 = true
          callNative('toast', { type: 'fail', message: '请填写证件号' })
        }
        return
      }

      if (this.updateGid.length !== 18) {
        this.isWrong1 = true
        callNative('toast', {
          type: 'fail',
          message: '身份证/机构代码位数位数有误，请修改'
        })
        return
      }
      if (!validateIdCard(this.updateGid)[0]) {
        this.isWrong1 = true
        callNative('toast', {
          type: 'fail',
          message: '您的身份证号/机构代码有误，请修改'
        })
        return
      }
      this.showConfirmsubmitAction = false
      // this.beforeConfirmationsubmitAction = true
      this.isChange = true
      this.isWrong1 = false
      this.isWrong3 = false
    },

    afterConfirmation() {
      this.updateGid = this.idNumber
      this.updateName = this.name
      this.beforeConfirmationsubmitAction = false
      this.showConfirmsubmitAction = false
      this.isChange = false
      if (this.updateName === '') {
        this.isWrong3 = true
      } else {
        this.isWrong3 = false
      }

      if (this.updateGid === '') {
        this.isWrong1 = true
      } else {
        this.isWrong3 = false
      }
    }
  }
}
</script>

<style scoped lang="less">
.idcertification {
  // height: 100%;
  padding: 16px;
  padding-bottom: 80px;
  padding-top: 0;
}

.item-img {
  padding-right: 16px;
  padding-left: 16px;
  width: 92%;
  height: 200px;
}

.item-img-result {
  height: 200px;
  margin-top: 16px;
}

.item-title-bold {
  width: 100%;
  font-size: 16px;
  color: #000;
  font-family: 'Audi-WideBold';
  margin-bottom: 16px;
}

.line_content {
  margin-top: 16px;
  margin-bottom: 24px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .s_line {
    width: 100%;
    height: 1px;
    margin-right: 16px;
    background: #e5e5e5;
  }

  .text_center {
    text-align: center;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #999;
    font-size: 14px;
  }

  .e_line {
    width: 100%;
    height: 1px;
    margin-left: 16px;
    background: #e5e5e5;
  }
}

.interval_line {
  margin-top: 30px;
  margin-left: -16px;
  margin-right: -16px;
  height: 8px;
  background: #f2f2f2;
}

.line {
  padding-top: 16px;
  padding-bottom: 16px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 16px;
    color: #000;
  }

  .item_btn {
    color: #666;
    font-size: 14px;
  }

  .btn-change {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn-icon {
      align-items: center;
      font-size: 12px;
      color: #000;
    }
  }
}

.btn-delete-wrapper {
  margin: 16px;
}

.hide_file {
  display: none;
}

.user-name {
  font-size: 16px;
  color: #000;
  font-family: 'Audi-WideBold';
  line-height: 24px;
  margin-bottom: 16px;
}

.line-border-bottom {
  // width: 100%;
  display: flex;
  align-items: center;
  // margin-top: 16px;
  // padding-bottom: 16px;
  // border-bottom: 1px solid #f2f2f2;
  margin-top: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f2f2f2;

  .name {
    width: 64px;
    font-size: 16px;
    color: #000;
    margin-right: 16px;
    font-family: 'Audi-Normal';
    line-height: 24px;
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    padding: 0;
  }
}
.is-text-wang {
  padding: 0;
  line-height: 24px;
  margin: 0;
  border-top: 1px solid #eb0d3f;
  color: #eb0d3f;
  font-size: 12px;
  padding-left: 16px;
  > img {
    width: 24px;
    height: 24px;
    // margin-right: 4px;
  }
}
.item-title-bold-resule {
  font-size: 16px;
  padding-left: 16px;
  margin-top: 24px;
  color: #000000;
  width: 90%;
  font-family: 'Audi-WideBold';
  line-height: 24px;
}
.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0px;
  padding-bottom: 38px;
  left: 0px;
  position: fixed;

  .div-btn {
    width: 100%;
    margin: 0 2px;
  }
}
</style>
