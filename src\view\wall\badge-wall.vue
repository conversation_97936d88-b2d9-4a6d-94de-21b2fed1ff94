<template>
  <div class="badge-wall">
    <div class="header">
      <div class="title">
        <div
          class="btn-back"
          @click="toBack"
        />
        <div class="name">
          <span>{{ $store.state.title }}</span>
        </div>
      </div>
      <div class="identity-info">
        <div class="avatar">
          <van-image
            round
            class="imag"
            :src="userBadgeInfo.avatarUrl"
          />
          <van-image
            class="v-imag"
            v-if="userBadgeInfo.identityName !== '普通用户'"
            :src="require('@/assets/wall/icon03.png')"
          />
        </div>

        <div class="text">
          <div class="top">
            <span>{{ userBadgeInfo.displayName }}</span>
            <van-image
              v-if=" wearBadge !== null && wearBadge !== undefined ? true : false"
              class="badge-imag"
              :class="[{'achievementType':wearBadge.category === 1},{'ownerTYpe':wearBadge.category === 0}]"
              :src="wearBadge.badgePicture"
            />
          </div>
          <div
            class="bottom"
            v-if="identityWearBadge != null && identityWearBadge.type === 1"
          >
            <span>{{ identityWearBadge.desc }}</span>
          </div>
        </div>
      </div>
      <!-- 弹出层 -->
      <van-popup
        v-if="isPopupShow"
        class="vanPopup"
      >
        <div class="popup">
          <van-image
            class="popupImag"
            :src="require(`@/assets/wall/${popupImagSrc}`)"
          />
          <span class="popupText">佩戴成功!</span>
        </div>
      </van-popup>
    </div>
    <div class="badge-info">
      <div v-if="ownerBadges.length !== 0 ? true : false">
        <span>车主徽章</span>
      </div>
      <div class="ownerBadge">
        <div
          v-for="item in ownerBadges"
          :key="item.orderId"
          class="car-owner-badge"
        >
          <div class="car-owner-imag">
            <van-image
              class="big-logo"
              :src="item.badgePicture"
            />
            <van-image
              v-if="item.wear === 1 ? true : false"
              class="panel small-logo"
              :src="require('@/assets/wall/icon04.png')"
            />
          </div>
          <div class="badge-title">
            {{ getOwnerBadgeName(item.carSeriesId) }}车主徽章
          </div>
        </div>
      </div>

      <div v-if="userAchievementBadges.length !== 0">成就徽章</div>
      <div
        class="achievement-badge"
      >
        <div
          v-for="badge in userAchievementBadges"
          :key="badge.id"
          @click="routerDetail(badge.id,badge.levelName)"
        >
          <van-image
            class="achievement-image"
            :src="badge.iconUrl"
          />
          <van-image
            v-if="badge.wear === 1"
            class="badgeHook"
            :src="require('@/assets/wall/icon04.png')"
          />
          <span>{{ badge.levelName }}</span>
        </div>
      </div>
      <Header
        ref="header"
        v-show="false"
      />
    </div>
  </div>
</template>

<script>
import { Image, Icon, Popup } from 'vant'
import Vue from 'vue'
import { mapMutations } from 'vuex'
import Header from '@/components/header.vue'
import { getUserAboutInfo, getAllBadges } from '@/api/wall'

Vue.use(Image, Icon, Popup)

export default {
  data() {
    return {
      isPopupShow: false,
      popupImagSrc: 'icon12.png',
      wearBadge: '', // 佩戴徽章
      ownerBadges: '', // 车主徽章情况
      achievementBadges: '', // all成就徽章
      userBadgeInfo: '', // 用户徽章信息
      identityWearBadge: '', // 身份徽章佩戴信息
      userAchievementBadges: '', // 用户成就徽章
      userId: this.$route.params.userId
    }
  },
  components: {
    Header
  },
  mounted() {
    this.setHeaderVisible(false)
    this.getUserIdentityInfo()
    this.setShowPopup()
  },
  methods: {
    ...mapMutations(['setHeaderVisible']),
    toBack() {
      this.$refs.header.toBack()
    },
    routerDetail(badgeId, levelName) {
      this.$router.push({ path: `/wall/badge-detail/${this.userBadgeInfo.userId}/${badgeId}/${levelName}` })
    },
    async getUserIdentityInfo() {
      try {
        console.log()
        // 获取用户点亮徽章 '3100000001011527' '6100000001010483'
        const userResult = await getUserAboutInfo(this.userId)
        this.userBadgeInfo = userResult.data.data
        // 获取佩戴徽
        this.wearBadge = this.userBadgeInfo.badge[0]
        // 获取佩戴身份徽章信息
        this.identityWearBadge = this.userBadgeInfo.identityBadge

        const userAllBadge = this.userBadgeInfo.allBadge
        if (userAllBadge.length !== 0) {
          // 获取车主徽章
          this.ownerBadges = userAllBadge.filter((item) => item.category === 0)
          // 成就徽章
          this.achievementBadges = userAllBadge.filter((item) => item.category === 1)
        }
        // 获取所有的徽章信息
        const allResult = await getAllBadges(2)
        const allBadges = allResult.data.data
        // 获取所有不亮最低等级的成就徽章
        const allNotLightBadges = allBadges.filter((item) => item.badgeType === 2 && item.lightStatus === 0 && item.level === 1)
        // 获取当前用户所有亮的徽章
        const allLightBadges = this.getAllLightBadge(allBadges, this.achievementBadges)
        // 筛选出每种等级最高的亮徽章
        const highestBadges = this.getHighestLevelBadge(allLightBadges)
        // 高亮匹配，亮的替换不亮的
        for (let i = 0; i < allNotLightBadges.length; i++) {
          highestBadges.forEach((item) => {
            if (allNotLightBadges[i].type === item.type) {
              allNotLightBadges[i] = item
            }
          })
        }
        this.userAchievementBadges = allNotLightBadges
      } catch (error) {
      }
    },
    getOwnerBadgeName(carSeriesId) {
      if (carSeriesId === '49') {
        return 'A7L'
      } if (carSeriesId === 'G4') {
        return 'Q5'
      }
    },
    getHighestLevelBadge(arr) {
      const dataArr = []
      const newData = []
      arr.forEach((item) => {
        dataArr[item.type] = dataArr[item.type] || []
        dataArr[item.type].push(item)
      })

      dataArr.forEach((item) => {
        item.sort((a, b) => b.level - a.level)
      })
      dataArr.forEach((item) => {
        newData.push(item[0])
      })
      return newData
    },
    getAllLightBadge(allBadges, achievementBadges) {
      const allLightBadge = []
      achievementBadges.forEach((badge) => {
        allBadges.forEach((all) => {
          if ((all.id).toString() === badge.orderId) {
            allLightBadge.push(all)
          }
        })
      })
      return allLightBadge
    },
    setShowPopup() {
      const code = this.$route.params.code
      if (code !== null && code !== undefined && code !== ' ') {
        this.isPopupShow = true
        if (code !== '00') {
          this.popupImagSrc = 'icon11.png'
          this.popupText = '网络出错,请稍后重试'
        } else {
          this.popupImagSrc = 'icon12.png'
          this.popupText = '佩戴成功!'
        }
        setTimeout(() => {
          this.isPopupShow = false
        }, 1000)
      }
    }
  }
}

</script>
<style scoped lang="less">
   .panel{
          width: 32px;
          height: 32px;
    }
   .header{
     position: relative;
     height: 200px;
     padding-top: 44px;
     box-sizing: border-box;
     background: url('~@/assets/wall/badgebg.png');
     background-size: cover;
     .title{
        height: 44px;
        display: flex;
        padding: 12px 12px 11px 12px;
        box-sizing:border-box;
        .btn-back{
          width: 20px;
          height: 21px;
          background: url('~@/assets/wall/icon01.png') center/contain no-repeat;
        }
        .name{
          display: flex;
          width: 100%;
          justify-content: center;
          font-size: 16px;
        }
     }
     .identity-info{
       height: 112px;
       padding: 12px 0px 0px 12px;
       display: flex;
       .avatar{
         width: 65px;
         height: 65px;
         margin-right: 9px;
         position: relative;
         .imag{
           fit:"cover";
         }
         .v-imag{
           width: 16px;
           height: 16px;
           position: absolute;
           right: 0px;
           bottom: 0px;
         }
       }
       .text{
         .top{
           display: flex;
           margin-top: 14px;
           font-size: 16px;
            .badge-imag{
              // width: 40px;
              // height: 15px;
              fit: 'cover';
              margin-left: 5px;
            }
         }
         .bottom{
           font-size: 12px;
           color: #333333 ;
         }
       }
     }

    .achievementType{
      width: 22px;
      height: 22px;
    }

    .ownerTYpe{
      width: 40px;
      height: 15px;
    }

    // 弹出层
    .vanPopup{
      width: calc(100% - 75px);
      height: 56px;
      position: absolute;
      top: 55px;
      left: 38px;
      padding: 16px;
      box-sizing: border-box;
      background-color: #FFFFFF ;
      font: 12px;
      .popup{
        display: flex;
        align-items: center;
        height: 24px;
        .popupImag{
          width: 24px;
          height: 24px;
          margin-right: 12px;
        }
      }
    }
   }
   .badge-info{
     padding: 24px 0px 0px 26px;
     font-size: 14px;
     font-weight: 700;
    //车主徽章
    .ownerBadge{
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 48px;
      .car-owner-badge{
            margin: 16px 15px 16px 16px;
            align-items: center;
            font-weight: 700;
            font-size: 12px;
            // background-color: blue;
           .car-owner-imag{
               width: 80px;
               height: 80px;
               position: relative;
               margin: 0 auto;
              //  background-color: pink;
                .big-logo{
                   transform: translateY(25px)
                }
                .small-logo{
                  position: absolute;
                  bottom: 9px;
                  right: -6px;
                }
             }
             .badge-title{
                margin-left: 16px;
             }
          }
         }


    //成就徽章
     .achievement-badge{
       display: flex;
       flex: 1;
       flex-wrap: wrap;
       margin-left: 26px;
       margin-top: 23px;
       font-size: 12px;
       div{
            display: flex;
            flex-direction: column;
            width: 65px;
            height: 65px;
            margin-right: 36px;
            margin-bottom: 44px;
            position: relative;
          .achievement-image{
             margin-bottom: 10px;
             fit:'cover'
           }
           .badgeHook{
             width: 32px;
             height: 32px;
             fit:'cover';
             position: absolute;
             top: 35px;
             left: 40px;
           }
           span{
             text-align: center;
           }
       }
     }
    //车主身份
     .car-owner-identity{
       font-size: 12px;
       margin: 21px 0px 48px;
       .car-owner-identity-imag{
         width: 129px;
         height: 14px;
         position: relative;
         .identity-small-logo{
           position: absolute;
           top: 5px;
           left: 113px;
         }
       }
       .identity-title{
         margin:16px 0px 0px 23px;
       }
     }

   }
</style>
