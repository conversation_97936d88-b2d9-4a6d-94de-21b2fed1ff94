<template>
    <div>
        
    </div>
</template>
<script>
export default {
    data() {
        return {
            
        }
    },
    created() {
        if(this.$route.query?.orderType == '07') {
            this.$router.replace({
                path:'/order/money-detail',
                query:{...this.$route.query}
            })
        }else {
            this.$router.replace({
                path:'/order/new-money-detail',
                query:{...this.$route.query}
            })
        }
    }
}
</script>