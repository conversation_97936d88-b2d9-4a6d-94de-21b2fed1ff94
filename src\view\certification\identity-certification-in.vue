<template>
  <div class="unsubscribeSucceed">
    <div style="text-align: center">
      <img src="../../assets/img/correct.png" />
    </div>

    <p v-html="`提交成功，待审核`" />

    <div class="content">工作人员会尽快审核，请耐心等待</div>
    <div class="interval_line" />

    <div class="line-border-bottom">
      <div class="name">证件</div>
      <div class="str">
        {{ showGidType() }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">姓名</div>
      <div class="str">
        {{ name }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">证件号</div>
      <div class="str">
        {{ gid }}
      </div>
    </div>

    <div class="btnWarp">
      <div class="buttons" @click="onCertification">完成</div>
    </div>
  </div>
</template>

<script>
import { callNative } from '@/utils'
import { getManCarMemberInfo } from '@/api/api'

export default {
  data() {
    return {
      type: 0,

      btnName: '',
      gid: '',
      name: '',
      gidType: '',
      pageFromScene: ''
    }
  },
  created() {
    this.pageFromScene = this.$store.state.pageFromScene
  },
  mounted() {
    this.getManCarMemberInfo()
  },
  methods: {
    // 查询是否有绑定车辆
    async getManCarMemberInfo() {
      const { data } = await getManCarMemberInfo({})
      if (data.data.certificationStatus == 3) {
        this.gid = data.data.gid
        this.name = data.data.name
        this.gidType = data.data.gidType
      }
    },
    showGidType() {
      // 1-身份证，2-驾驶证，101-外国人护照，102-港澳居民居住证，103-台湾居民来往大陆通行证（台胞证
      if (this.gidType == '1') {
        return '身份证'
      }
      if (this.gidType == '2') {
        return '驾驶证'
      }
      if (this.gidType == '101') {
        return '外国人护照'
      }
      if (this.gidType == '102') {
        return '港澳台居民往内地通行证（回乡证）'
      }
      if (this.gidType == '103') {
        return '台湾居民来往大陆通行证（台胞证）'
      }
      if (this.gidType == '104') {
        return '港澳居民居住证'
      }
      if (this.gidType == '105') {
        return '台湾居民居住证'
      }
      if (this.gidType == '106') {
        return '中国人民解放军军官证（军官证）'
      }
      if (this.gidType == '107') {
        return '中国人民武装警察部队警官证（武警证）'
      }
      if (this.gidType == '108') {
        return '外国人永久居留居住证（外国人永居证）'
      }
    },

    onCertification() {
      callNative('close', {})
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/buttons.less');

.unsubscribeSucceed {
  padding: 0 16px;
  img {
    margin-top: 52px;
    width: 66px;
    height: 66px;
  }

  p {
    margin: 0;
    text-align: center;
    font-size: 16px;
    color: #000000;
    margin-top: 8px;
    line-height: 24px;
    font-family: 'Audi-Normal';
    margin-bottom: 4px;
  }
  .content {
    text-align: center;
    font-size: 12px;
    color: #999999;
    line-height: 20px;
    font-family: 'Audi-Normal';
  }

  .interval_line {
    margin-top: 24px;
    margin-left: -16px;
    margin-right: -16px;
    height: 8px;
    background: #f2f2f2;
    margin-bottom: 8px;
  }
  .line-border-bottom {
    width: 100%;
    display: flex;
    //   align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 15px;

    .name {
      width: 80px;
      font-size: 16px;
      color: #000;
    }
    .str {
      font-family: 'Audi-Normal';
      font-size: 16px;
      color: #333;
    }
  }
  .btnWarp {
    width: 100%;
    background-color: #ffffff;
    // display: flex;
    // flex-direction: column;
    // justify-content: space-between;
    position: fixed;
    bottom: 0;
    padding-bottom: 38px;
    left: 16px;
    .checkbox_button {
      margin-left: 16px;
      margin-bottom: 5px;
      width: 18px;
      height: 18px;
      color: #000;
    }
  }
}
</style>
