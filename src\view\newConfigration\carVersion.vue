<template>
  <div class="modelline-wrapper">

    <div v-for="item, idx in currentVersionData" :key="item.modelLineId" class="item" :class="{
      selected: item.styleId === currentStyleId
    }" @click="toSelectModelLine(item, idx)">
      <div class="left c-font14">
        <div class="title c-bold">
          <div class="title-name"> {{ addText(item.modelLineList[0]) }} {{ item.styleName }} </div>
          <div class="tag-tightstock" v-if="item.stockout === 1">库存紧张</div>

          <!-- 爆款 -->
          <!-- 临时隐藏爆款标签 -->
          <!-- <div class="tag-wrapper" v-if="item.recommendStatus === 1">爆款</div> -->
        </div>

        <div class="price-wrapper">
          <div class="price">
            <PreferentialPrice v-if="item.preferential" :preferential="item.preferential" :price="item.price"/>
            <span class="orginal-price" v-else>¥{{ item.price | formatPrice }}起</span>
          </div>
          <!-- 限时优惠的标签 -->
          <!-- <div class="red-tag-wrapper" v-if="item.preferential">
            <div class="tag-wrapper">
              <img v-if="'6dba6e6f-362b-4e7c-94e2-603765de30ff' === item.styleId" src="@/assets/icon-youhui-zhigao.png"
                alt="">
              <img v-else src="@/assets/icon-youhui.png" alt="">
            </div>
            <div class="text"> {{ item.preferential | finalFormatPriceDesc }}</div>
          </div> -->
        </div>

        <div class="sellblip-wrapper">
          <div class="sellblip" v-for="sellpoint, idx in item.sellBlips" :key="idx">
            {{ sellpoint }}
          </div>
        </div>
      </div>


      <!-- car image -->
      <div class="img-wrapper" :class="{
        a7mr2: item.modelLineList[0].version === '2' && currentSeriesName === 'a7l',
        q6Scale: currentSeriesName === 'q6',
        q6rsScale: item.modelLineList[0].modelLineCode === 'G6ICAY024'
      }">
        <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(450)">
      </div>

      <!-- 权益 -->
      <div class="icon-equity-wrapper" @click.stop="showEquityImage(item)">
        尊享购车权益 <van-icon name="arrow" />
      </div>
    </div>

    <div class="equity-img-wrapper" v-if="equityImgVisible">
      <div class="header">
        <div class="btn-back" @click="equityImgVisible = false">
          <img :src="require('../../assets/img/icon03.png')" alt="">
        </div>
      </div>
      <div class="img-wrapper">
        <img :src="equityUrl" alt="">
      </div>
    </div>

    <EquityImg/>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import url from '@/config/url'
import { getRightsImg } from '@/configratorApi'
import { A7MR } from './car/a7mr'
import EquityImg from './components/equityImg.vue'
import PreferentialPrice from './components/preferentialPrice.vue'

export default {
  components: {
    EquityImg,
    PreferentialPrice
  },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      modelVersionList: [],
      equityImgVisible: false,
      equityUrl: ''
    }
  },

  computed: {
    ...mapGetters(['currentSeriesName']),
    ...mapState({
      currentVersionData: (state) => state.configration.currentVersionData,
      currentStyleId: (state) => state.configration.currentVersion?.styleId,
      carIdx: (state) => state.configration.carIdx,
      orderTime: (state) => state.configration.orderTime
    })
  },

  methods: {
    addText(modelLine) {
      // 非mr车型的24款,去除 文案里的24款文案: version === '1' 为mr车型
      if (modelLine.modelYear === '2024') {
        if (modelLine.customSeriesCode === '49') {
          return ''
        }
        return '24款'
      }
      if (modelLine.modelYear === '2025') {
        if (modelLine.customSeriesCode === '49') {
          return ''
        }
        if (modelLine.modelLineCode === 'G6IBCY008') {
          return '25款 quattro'
        }
        return '25款'
      }
      return ''
    },

    // 选中
    toSelectModelLine(item, idx) {
      if (item.styleId !== this.currentStyleId) {
        this.$store.commit('clearSelectedConfig', ['carModel'])
      }

      this.$store.commit('updateCurrentVersion', item)

      // 跳转到下一个tab
      this.$store.commit('updateCarModelTab', 'carModel')

      // 埋点
      this.clickVersionSensors(item, idx)
    },

    // 埋点
    clickVersionSensors(item, idx) {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const param = {
        source_module: 'H5',
        car_series: carMap[this.carIdx],
        car_version: item.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        select_price: `${item.price}起`,
        select_index: idx + 1,
        is_hot: item.recommendStatus === 1
      }

      // console.log(param, item)
      this.$sensors.track('CC_SelectCar_Version_Click', param)
    },

    async showEquityImage(item) {
      console.log(item)

      let serachTime = Date.now()
      if (this.orderTime) {
        const decodedTimeStr = this.orderTime.replace(/\+/g, ' ')
        const date = new Date(decodedTimeStr) // 转为日期对象
        serachTime = Math.floor(date.getTime() / 1000) // 转为时间戳（秒）
      }
      const param = {
        carModelId: item.modelLineList[0].modelLineCode,
        modelYear: item.modelYear,
        modelVersion: item.version,
        searchTime: serachTime
      }
      this.$store.commit('showLoading')
      const data = await getRightsImg(param)

      this.$store.commit('hideLoading')
      const img = data.data.data[0]
      // console.log(img)
      this.equityUrl = img
      this.equityImgVisible = true
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.modelline-wrapper {

  .item {
    position: relative;
    margin-top: 18px;
    background-color: #fff;
    font-size: 12px;
    padding: 14px 0;
    // border: 1px solid transparent;
    overflow: hidden;

    &.selected {
      border: 1px solid #000;
    }

    .left {
      min-height: 100px;

      .title {
        position: relative;
        line-height: 20px;
        padding-left: 24px;
        >.title-name {
          display: inline-block;
        }
        >.tag-tightstock {
          background: #855B3F;
          color: #fff;
          font-size: 10px;
          line-height: 18px;
          display: inline-block;
          padding: 0 5px;
          margin-left: 10px;
          transform: scale(0.8);
          transform-origin: 0 center;
        }

        >.tag-wrapper {
          position: absolute;
          right: 0;
          top: 50%;
          width: 40px;
          transform: translateY(-50%);
          background: #EB0D3F;
          color: #fff;
          font-weight: normal;
          font-size: 10px;
          line-height: 14px;
          box-sizing: border-box;
          text-align: right;
          padding-right: 7px;

          &::before {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            top: 0;
            background-color: #fff;
            border-top: 7px solid transparent;
            border-bottom: 7px solid #EB0D3F;
            border-left: 4px solid transparent;
            border-right: 4px solid #EB0D3F;
          }
        }
      }

      .price-wrapper {
        display: flex;
        align-items: center;
        margin-top: 2px;

        .price {
          color: #4C4C4C;
          line-height: 20px;
          padding-left: 24px;
          .orginal-price {
            font-size: 12px;
            transform: scale(0.8);
            transform-origin: 0 center;
            display: block;
          }
          .price-before-preferential {
            font-size: 12px;
            margin-right: 10px;
            text-decoration: line-through;
            color: #999
          }
          .price-after-preferential {
            color: #F50538;
          }
        }

        .red-tag-wrapper {
          display: flex;
          align-items: center;
          border: 1px solid #EB0D3F;
          height: 14px;
          margin-left: 5px;

          >.tag-wrapper {
            height: 100%;

            img {
              width: auto;
              height: 100%;
              vertical-align: top;
            }
          }

          >.text {
            color: #EB0D3F;
            font-size: 10px;
            line-height: 14px;
            padding: 0 4px;
          }
        }
      }

      // 亮点
      .sellblip-wrapper {
        margin-top: 14px;
        color: #999;
        font-size: 12px;
        margin-left: 30px;

        >.sellblip {
          position: relative;
          line-height: 18px;

          &::before {
            position: absolute;
            content: '· ';
            right: 101%;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }

    .img-wrapper {
      position: absolute;
      bottom: -9%;
      right: -7%;
      width: 62%;

      &.a7mr2 {
        width: 58%;
        right: -5%;
      }

      &.q6Scale {
        width: 60%;
      }

      &.q6rsScale {
        width: 59%;
      }
    }

    .icon-equity-wrapper {
      position: absolute;
      top: 0;
      right: 0;
      color: #855B3F;
      background: linear-gradient(90deg, #F9F1E6 0%, #FBF3E0 100%);
      padding: 6px 7px 6px 10px;
      display: flex;
      align-items: center;

      .van-icon {
        // font-size: 12px;
        // color: #855B3F;
        margin-left: 4px;
      }

      &::after {
        position: absolute;
        right: 100%;
        top: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid #F9F1E6;
        border-top: 13px solid #F9F1E6;
        border-bottom: 14px solid transparent;
        display: block;
        content: '';
      }
    }
  }
}

.equity-img-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 999;
  background-color: #000;

  >.header {
    padding-top: 35px;
    height: 44px;
    background-color: #000;
    display: flex;
    align-items: center;
    .btn-back {
      width: 30px;
      margin-left: 12px;
      img {
        filter:invert(1);
        width: 100%;
      }
    }
  }
  >.img-wrapper {
    text-align: center;
    height: calc(100% - 79px);
    display: flex;
    align-items: center;
    overflow-y: auto;
    img {
      width: 100%;
    }
  }
}
</style>
