<template>
  <div class="container">
    <div class="title">
      试驾评价
    </div>
    <div class="rate-con">
      <div class="label">
        人员服务态度
      </div>
      <van-rate
        v-model="value1"
        allow-half
        color="#000"
        void-icon="star"
        void-color="#eee"
      />
    </div>
    <div class="rate-con">
      <div class="label">
        人员专业度
      </div>
      <van-rate
        v-model="value2"
        allow-half
        color="#000"
        void-icon="star"
        void-color="#eee"
      />
    </div>
    <div class="rate-con">
      <div class="label">
        试乘试驾车况
      </div>
      <van-rate
        v-model="value3"
        allow-half
        color="#000"
        void-icon="star"
        void-color="#eee"
      />
    </div>
    <div class="rate-con">
      <div class="label">
        试乘试驾路线
      </div>
      <van-rate
        v-model="value4"
        allow-half
        color="#000"
        void-icon="star"
        void-color="#eee"
      />
    </div>
    <div class="rate-con">
      <div class="label">
        试乘试驾满意度
      </div>
      <van-rate
        v-model="value5"
        allow-half
        color="#000"
        void-icon="star"
        void-color="#eee"
      />
    </div>
    <div class="title">
      车辆评价
    </div>
    <div class="che-rate">
      <div
        v-for="(item,index) in che"
        :key="item.text"
        @click="item.selected=!item.selected"
        :class="[item.selected?'selected':'',index>0?'ml':'']"
      >
        {{ item.text }}
      </div>
    </div>
    <div class="title">
      详细评价
    </div>
    <van-field
      v-model="detail"
      rows="2"
      autosize
      type="textarea"
      placeholder="备注"
      class="detail-field"
    />
    <div class="queren">
      确认
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import { Rate, Field } from 'vant'

Vue.use(Rate).use(Field)
export default {
  name: 'Evaluation',
  data() {
    return {
      value1: 3,
      value2: 3.5,
      value3: 3,
      value4: 3.5,
      value5: 3,
      che: [
        { text: '豪华', selected: false },
        { text: '操控好', selected: false },
        { text: '配置高', selected: false }
      ],
      detail: ''
    }
  },
  mounted() {

  },
  methods: {

  }
}
</script>
<style lang="less" scoped>
.container{
  .title{
    font-weight: bold;
    margin: 20px 16px;
  }
  .rate-con{
    display: flex;
    align-items: center;
    height: 50px;
    margin: 0 16px;
    border-bottom: solid 1px #e5e5e5;
    .label{
      width: 120px;
      margin-right: 16px;
    }
  }
  .che-rate{
    display: flex;
    margin: 0 16px;
    div{
      background-color: #f2f2f2;
      font-size: 14px;
      padding: 6px 20px;
      &.ml{
        margin-left: 12px;
      }
      &.selected{
        background-color: #000;
        color: #fff;
      }
    }
  }
  .detail-field{
    border: solid 1px #000;
    // padding: 0;
    margin: 0 16px;
    box-sizing: border-box;
    width: calc(100% - 32px);
  }
  .queren{
    margin: 40px 16px;
    background-color: #000;
    height: 56px;
    text-align: center;
    line-height: 56px;
    color: #fff;
  }
}
</style>
