<template>
  <div
    class="tipcontent"
    @click.self="$emit('close')"
  >
    <div class="wenzi">
      <div
        class="close"
        @click="$emit('close')"
      >
        <img src="../../../assets/img/icon05.png">
      </div>
      <div class="title">
        限量号玩法
      </div>
      <div
        class="content"
        v-html="limitnumberdesc"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  computed: mapState({
    limitnumberdesc: (state) => state.configration.limitNumberData[0]?.desc
  })
}

</script>

<style lang="less" scoped>
.tipcontent {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;

  .wenzi {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 16px 16px 100px 16px;
    background-color: #fff;
    box-sizing: border-box;
    color: #000;

    >.close {
      position: absolute;
      top: 14px;
      right: 20px;
      width: 26px;

      img {
        width: 100%;
      }
    }

    >.title {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }

    >.content {
      margin: 20px 0 6px 0;
      text-align: left;
      line-height: 25px;
      white-space: break-spaces;
    }
  }
}
</style>
