<template>
  <div class="way">
    <div class="title">
      {{ amsTestDriveRoute.routeTitle }}
    </div>
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <img :src="amsTestDriveRoute.routeImage">
        </div>
      </div>
      <!-- <div class="swiper-pagination" /> -->
    </div>
    <!-- <div
      class="bottom"
      @click="tolist"
    >
      返回预约试驾
    </div> -->
  </div>
</template>
<script>
export default {
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple', 
      swiper: {},
      amsTestDriveRoute: {}
    }
  },
  mounted() {
    this.amsTestDriveRoute = this.$route.query.amsTestDriveRoute
    this.initSwiper()
  },
  methods: {
    tolist() {
      this.$router.push({
        path: this.fromType ? '/testdrive/appointment-list?fromType=fromPurple' : '/testdrive/appointment-list'
      })
    },
    initSwiper() {
      // eslint-disable-next-line no-undef
      this.swiper = new Swiper('.swiper-container', {
        autoHeight: true, // enable auto height
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.way{
  ::v-deep .swiper-pagination-bullet {
    width: 20px;
    height: 8px;
    border-radius: 0;
    margin: 0 4px;
    background-color: rgba(255, 255, 255, 0.7);
  }

  ::v-deep .swiper-pagination-bullet-active {
    background-color: rgba(0, 0, 0, 0.9);
  }
  .title{
    margin: 16px;
  }
  .swiper-container {
    width: calc(100% - 32px);
    margin: 0 auto;
    height: auto;
    .swiper-wrapper{
      height: auto !important;
      .swiper-slide{
        width: 100%;
      }
    }
  }
  .bottom{
    position: fixed;
    bottom: 30px;left: 16px;
    height: 56px;width: calc(100vw - 32px);
    text-align: center;
    line-height: 56px;
    background-color: #000;
    color: #fff;
    z-index: 10;
  }
}
</style>
