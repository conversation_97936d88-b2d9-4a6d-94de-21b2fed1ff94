<template>
  <div class="unsubscribeSucceed">
    <div class="circle" />
    <p>退款提交成功！您的款项将在24小时 内退回原支付账户</p>

    <div style="position: absolute;width: 100%;bottom: 60px;">
      <div
        class="btn-pay-wrapper"
        v-if="allowRefund"
      >
        <AudiButton
          @click="goCC"
          text="重新配置"
          color="black"
          font-size="16px"
          height="56px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Icon } from 'vant'
import AudiButton from '@/components/audi-button'

Vue.use(Icon)
export default {
  components: {
    AudiButton
  },
  data() {
    return {
      allowRefund: false
    }
  },
  created() {
    if (this.$route.query.valid == 0 || this.$route.query.updateFlag == 1) {
      this.allowRefund = true
    }
  },
  methods: {
    goCC() {
      const seriesCode = this.$route.query?.seriesCode
      console.log('车系：seriesCode 49 G4 G6', seriesCode)
      // 49 G4 G6
      let obj = {}
      if (seriesCode == 49) {
        obj = {
          path: '/configration',
          query: {
            idx: 0
          }
        }
      }
      if (seriesCode == 'G4') {
        obj = {
          path: '/configration',
          query: {
            idx: 1,
            tabIndex: 0,
            definedCar: 0
          }
        }
      }

      if (seriesCode == 'G6') {
        obj = {
          path: '/configration',
          query: {
            idx: 2,
            tabIndex: 0,
            definedCar: 1
          }
        }
      }
      this.$router.push(obj)
    }
  }
}
</script>

<style scoped lang="less">
.btn-pay-wrapper {
  margin: 0 16px;
}
.unsubscribeSucceed {
  .circle {
    position: relative;
    width: 72px;
    height: 72px;
    // border: 1px solid #000000;
    border-radius: 50%;
    margin: 166px auto auto auto;
    background: url('~@/assets/img/contract-success.png') no-repeat 50% / contain;
    // /deep/.van-icon-success {
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   right: 0;
    //   bottom: 0;
    //   margin: auto;
    //   &::before {
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     right: 0;
    //     width: 20px;
    //     height: 18px;
    //     bottom: 0;
    //     margin: auto;
    //   }
    // }
  }
  p {
    padding: 0 57px 0 59px;
    text-align: center;
    font-size: 16px;
    line-height: 28px;
    font-weight: 400;
  }
}
</style>
