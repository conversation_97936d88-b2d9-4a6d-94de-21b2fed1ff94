<template>
  <div
    :class="
      ['contract-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      listening-emit-back
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`, height: `calc(100vh - ${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px)`}"
    >
      <div
        class="container"
        v-if="orderInfo"
      >
        <div class="service-line">
          <div class="title-bold">
            {{ setStatusDesc(orderInfo.orderStatus) }}
          </div>
          <div
            class="btn-change"
            @click="onInfoStep"
          >
            <img
              class="btn-icon"
              src="../../assets/img/icon-arrow.png"
            >
          </div>
        </div>

        <div
          class="c-font12"
          style="color: #333; font-size: 14px; margin-top: 8px"
        >
          {{ orderInfo.orderStatusUpdateTime }}
        </div>

        <div
          class="item-store"
          style="margin-top: 16px"
          v-if="orderInfo.consultantName"
        >
          <div>
            <img
              class="img-wrapper"
              :src="require('@/assets/img/driving-url.jpg')"
              alt=""
            >
          </div>
          <div class="navgation-wrapper flex1">
            <div
              class="c-font14 c-bold"
              style="color: #000"
            >
              {{ "VIP专属服务顾问：" + orderInfo.consultantName }}
            </div>
          </div>
          <div class="navgation-wrapper">
            <div>
              <img
                class="nav-icon"
                :src="require('@/assets/img/call-phone.png')"
                alt=""
                @click="callPhone(orderInfo.consultantPhone)"
              >
            </div>
          </div>
        </div>

        <!-- <div
      class="item-store"
      v-if="orderInfo.constructstaffName"
    >
      <div>
        <img
          class="img-wrapper"
          :src="require('@/assets/img/driving-url.jpg')"
          alt=""
        />
      </div>
      <div class="navgation-wrapper flex1">
        <div class="c-font14 c-bold" style="color: #000">
          {{ "安装人员：" + orderInfo.constructstaffName }}
        </div>
      </div>
      <div class="navgation-wrapper">
        <div>
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""
            @click="callPhone(orderInfo.constructstaffPhone)"
          />
        </div>
      </div>
    </div> -->

        <div v-if="orderInfo.surveyInfoList && orderInfo.surveyInfoList.length > 0">
          <div
            class="linediv"
            style="margin-top: 0px;"
          />
          <div
            class="title-bold"
            style="margin-top: 16px"
          >
            勘测信息
          </div>

          <div
            v-for="(item, idx) in orderInfo.surveyInfoList"
            :key="idx"
          >
            <div
              class="item-wrapper"
              style="margin-top: 12px"
            >
              <div
                class="content-wrapper flex1"
                style="color: #333; font-size: 12px"
              >
                {{ item.title }}
              </div>
              <div
                class="navgation-wrapper"
                style="color: #000; font-size: 12px"
              >
                {{ item.dsc }}
              </div>
            </div>
          </div>
          <div style="color: #999; font-size: 12px; padding-top: 10px">
            {{ "前2次勘测免费，从第3次起需支付勘测费(如有疑问请联系您的专属顾问)" }}
          </div>
        </div>

        <div v-if="orderInfo.powerFail">
          <div class="linediv" />
          <div
            class="title-bold"
            style="margin-top: 16px"
          >
            失败原因
          </div>

          <div style="color: #333; font-size: 12px; padding-top: 10px">
            {{ orderInfo.powerFail }}
          </div>
        </div>


        <div class="linediv" />
        <div
          class="title-bold"
          style="margin-top: 8px"
        >
          订单信息
        </div>

        <div
          class="item-wrapper"
          style="margin-top: 12px"
        >
          <div
            class="content-wrapper flex1"
            style="color: #333; font-size: 12px"
          >
            订单类型
          </div>
          <div
            class="navgation-wrapper"
            style="color: #000; font-size: 12px"
          >
            {{ "充电桩安装" }}
          </div>
        </div>
        <div
          class="item-wrapper"
          style="font-size: 14px; padding-top: 0px"
        >
          <div
            class="content-wrapper flex1"
            style="color: #333; font-size: 12px"
          >
            需求订单号
          </div>
          <div
            class="navgation-wrapper"
            style="color: #000; font-size: 12px"
          >
            {{ orderInfo.csmsOrderId }}
          </div>
        </div>
        <div
          class="item-wrapper"
          style="font-size: 14px; padding-top: 0px"
        >
          <div
            class="content-wrapper flex1"
            style="color: #333; font-size: 12px"
          >
            下单时间
          </div>
          <div
            class="navgation-wrapper"
            style="color: #000; font-size: 12px"
          >
            {{ orderInfo.orderTime }}
          </div>
        </div>
        <div class="linediv" />
        <div v-if="orderInfo.province">
          <div
            class="title-bold"
            style="margin-top: 8px"
          >
            个人信息
          </div>
          <!-- <template v-if="!order.isWallBoxOrder">
          <div
            class="item-wrapper"
            style="font-size: 14px; padding-top: 14px"
          >
            <div
              class="content-wrapper flex1"
              style="color: #333; font-size: 12px"
            >
              购车人姓名
            </div>
            <div
              class="navgation-wrapper"
              style="color: #000; font-size: 12px"
            >
              {{ orderInfo.buyCarName }}
            </div>
          </div>
          <div
            class="item-wrapper"
            style="font-size: 14px; padding-top: 0px"
          >
            <div
              class="content-wrapper flex1"
              style="color: #333; font-size: 12px"
            >
              购车人联系方式
            </div>
            <div
              class="navgation-wrapper"
              style="color: #000; font-size: 12px"
            >
              {{ orderInfo.buyCarMobile | phoneNum}}
            </div>
          </div>

          <div
            class="item-wrapper"
            style="font-size: 14px; padding-top: 0px"
          >
            <div
              class="content-wrapper flex1"
              style="color: #333; font-size: 12px"
            >
              车主姓名
            </div>
            <div
              class="navgation-wrapper"
              style="color: #000; font-size: 12px"
            >
              {{ orderInfo.clientName }}
            </div>
          </div>
          <div
            class="item-wrapper"
            style="font-size: 14px; padding-top: 0px"
          >
            <div
              class="content-wrapper flex1"
              style="color: #333; font-size: 12px"
            >
              车主联系方式
            </div>
            <div
              class="navgation-wrapper"
              style="color: #000; font-size: 12px"
            >
              {{ orderInfo.clientPhone | phoneNum }}
            </div>
          </div>
        </template> -->
          <div
            class="item-wrapper"
            style="font-size: 14px; padding-top: 0px"
          >
            <div
              class="content-wrapper flex1"
              style="color: #333; font-size: 12px"
            >
              安装人姓名
            </div>
            <div
              class="navgation-wrapper"
              style="color: #000; font-size: 12px"
            >
              {{ orderInfo.contact }}
            </div>
          </div>
          <div
            class="item-wrapper"
            style="font-size: 14px; padding-top: 0px"
          >
            <div
              class="content-wrapper flex1"
              style="color: #333; font-size: 12px"
            >
              安装人联系方式
            </div>
            <div
              class="navgation-wrapper"
              style="color: #000; font-size: 12px"
            >
              {{ orderInfo.mobile | phoneNum}}
            </div>
          </div>
          <!-- <div
        class="item-wrapper"
        style="font-size: 14px; padding-top: 0px"
      >
        <div
          class="content-wrapper flex1"
          style="color: #333; font-size: 12px"
        >
          电源类型
        </div>
        <div
          class="navgation-wrapper"
          style="color: #000; font-size: 12px"
        >
          {{ orderInfo.chargePileModel }}
        </div>
      </div> -->
          <div class="item-wrapper">
            <div
              class="content-wrapper flex1"
              style="color: #333; font-size: 12px"
            >
              安装地址
            </div>
            <div
              class="navgation-wrapper"
              style="color: #000; font-size: 12px"
            >
              {{ orderInfo.province + orderInfo.city + orderInfo.county }}
            </div>
          </div>
          <div
            class="item-wrapper"
            style="font-size: 14px; padding-top: 0px"
          >
            <div
              class="content-wrapper flex1"
              style="color: #333; font-size: 12px"
            >
              详细地址
            </div>
            <div
              class="navgation-wrapper"
              style="color: #000; font-size: 12px"
            >
              {{ orderInfo.address }}
            </div>
          </div>

          <div class="linediv" />
        </div>
        <div class="btn-delete-height" />
        <div
          class="bottom_style"
          v-if="![3].includes(orderInfo.status) && btnText"
        >
          <p
            class="warning-tips"
            data-flex="main:left"
            v-if="skukw === 'lite' && BTN_TEXTS[2].code.includes(orderStatus) && !CANCEL_CODE.includes(orderStatus)"
          >
            <img :src="require('@/assets/img/warning-circle.png')"><span>充电桩暂不满足安装条件，可选择其他权益</span>
          </p>
          <div class="btn-delete-wrapper">
            <AudiButton
              @click="onOrderBtn"
              :text="btnText"
              color="black"
              font-size="16px"
              height="56px"
            />
          </div>
        </div>

        <van-action-sheet
          v-model="show"
          :round="false"
          :safe-area-inset-bottom="true"
          class="lan-action-sheet-huixing"
          title="请您选择奥金"
        >
          <van-radio-group
            v-model="radioType"
            class="radio-group-huixing"
          >
            <template v-if="skukw === 'lite'">
              <van-radio
                name="6"
                v-if="['1102', '1203'].includes(orderStatus) && autoOrderStatus !== '90'"
              >
                7200元公共充电卡
                <template #icon="props">
                  <img
                    class="img-icon"
                    :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
                  >
                </template>
              </van-radio>
              <van-radio
                name="7"
                v-else
              >
                {{HUI_XING.WW}}奥金+3600元公共充电卡
                <template #icon="props">
                  <img
                    class="img-icon"
                    :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
                  >
                </template>
              </van-radio>
            </template>
            <template v-else>
              <van-radio
                name="2"
              >
                {{ HUI_XING.WW }}奥金 (不安装充电桩)
                <template #icon="props">
                  <img
                    class="img-icon"
                    :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
                  >
                </template>
              </van-radio>
              <!-- <van-radio
                name="3"
              >
                {{ HUI_XING.WW }}奥金+公共桩挂靠 (不安装充电桩)
                <template #icon="props">
                  <img
                    class="img-icon"
                    :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
                  >
                </template>
              </van-radio> -->
            </template>
          </van-radio-group>
          <div
            class="btn-delete-wrapper"
            style="margin-top: 16px"
          >
            <AudiButton
              @click="onSelectIntegral"
              :text="'确认'"
              color="black"
              font-size="16px"
              height="56px"
            />
          </div>
        </van-action-sheet>
        <van-popup
          v-model="interceptExit"
          class="popup-custom"
          :close-on-click-overlay="false"
        >
          <div class="popup-custom-box">
            <div class="popup-custom-main">
              <div class="h3 align-center">
                确定退出吗？
              </div>
              <div class="text align-center">
                <div class="list">
                  {{ CANCEL_CODE.includes(orderStatus) ? '由于充电桩订单被取消，需更换权益' : '由于充电暂不满足安装条件，需更换权益' }}
                </div>
              </div>
            </div>
            <div
              class="popup-custom-btn"
              data-flex="main:justify"
            >
              <div
                class="lan-button-box white-button line-two-cols"
              >
                <van-button
                  @click="handleCancelJump"
                  class="lan-button"
                >
                  仍要退出
                </van-button>
              </div>
              <div class="lan-button-box black-button line-two-cols">
                <van-button
                  @click="handleConfirmJump"
                  class="lan-button"
                >
                  选择权益
                </van-button>
              </div>
            </div>
          </div>
        </van-popup>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Tab,
  Tabs,
  ActionSheet,
  RadioGroup,
  Radio,
  Field,
  Checkbox,
  Popup,
  Button,
  CheckboxGroup
} from 'vant'
import { mapGetters } from 'vuex'
import { getChargingPileInfo, postChargingPileCancel, postCreateChargingPile } from '@/api/api'
import baseUrl from '@/config/url'
import { callNative } from '@/utils'
import AudiButton from '@/components/audi-button'
import Q5E_TRON_DATA from '@/config/Q5e-tron-map.data'
import { CHARGING_PILES_RIGHTS, Q5E_LITE_MODEL_CODE, HUI_XING } from '@/config/conf.data'
import HeaderCustom from '@/components/header-custom.vue'
import storage from '../../utils/storage'

Vue.use(Tab).use(Tabs).use(ActionSheet)
Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Field).use(CheckboxGroup).use(Checkbox).use(Popup)
  .use(Button)

const BTN_TEXTS = [
  {
    text: '取消订单',
    code: ['002600001', '0001', '1000', '1001', '1002']
  },
  {
    text: '填写安装信息',
    code: ['002600002', '002600010', '002600011', '1003', '1101']
  },
  {
    text: '选择权益',
    code: ['002600006', '002600007', '1102', '1203']
  },
  {
    text: '去评价',
    code: ['002600005', '002600008', '002600009', '1401', '1402', '1501', '1800']
  }
]

export default {
  components: {
    AudiButton,
    'header-custom': HeaderCustom
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      show: false,
      ossUrl: baseUrl.BaseOssHost,
      orderInfo: {},
      btnText: '',
      orderId: '',
      orderStatus: '',
      businessType: '',
      carOrderId: '',
      radioType: '2',
      activeRadioIcon: require('../../assets/img/radio_checked.png'),
      inactiveRadioIcon: require('../../assets/img/radio_normal.png'),
      boolSubmit: false, // 已提交安装信息,false=已提交，不显示添加安装信息按钮；true:未提交，显示安装信息按钮")
      submitParam: [
      ],
      Q5E_TRON_DATA,
      CHARGING_PILES_RIGHTS,
      skukw: '',
      interceptExit: false,
      CANCEL_CODE: ['0002', 2000],
      NO_STANDARD_CODE: ['1102', '1203'],
      autoOrderStatus: '',
      BTN_TEXTS,
      HUI_XING
    }
  },

  async mounted() {
    const isOrderIntegral = storage.get('isOrderIntegral') || ''
    if (isOrderIntegral) {
      storage.set('isOrderIntegral', '')
    }

    const { appoId, orderId } = this.$route.query
    this.orderId = appoId || orderId
    this.getChargingPileInfo()
  },

  methods: {
    ...mapGetters(['getDevice']),
    async getChargingPileInfo() {
      let params = {}
      if (this.$route.query.copOrderId) {
        params = this.$route.query.copOrderId.includes('Optional') ? {
          copOrderId: this.$route.query.copOrderId.split('(')[1].slice(0, -1)
        } : { copOrderId: this.$route.query.copOrderId }
      } else {
        params = { orderId: this.orderId }
      }
      const { data } = await getChargingPileInfo(params)
      if (data.code === '00') {
        this.orderInfo = data.data
        this.orderStatus = data.data.orderStatus
        this.businessType = data.data.businessType
        this.carOrderId = data.data.orderId
        this.boolSubmit = data.data.boolSubmit
        this.autoOrderStatus = data.data.autoOrderStatus
        if (Q5E_LITE_MODEL_CODE.includes(data.data.modelCode)) {
          this.skukw = 'lite'
          this.radioType = '7'
        }
        this.setBtnText(this.orderStatus, this.businessType)
      }
    },

    setBtnText(orderStatus, businessType) {
      this.btnText = ''
      const { orderInfo, CANCEL_CODE, BTN_TEXTS } = this
      if (CANCEL_CODE.includes(orderStatus) && orderInfo.havaOrder === 2) {
        // this.btnText = '选择权益'
        BTN_TEXTS[2].code = [...BTN_TEXTS[2].code, ...CANCEL_CODE]
      }
      const [TEXTS] = BTN_TEXTS.filter((i) => i.code.includes(orderStatus))
      if (TEXTS?.text && TEXTS?.code?.length) {
        const { text, code } = TEXTS
        if (code === BTN_TEXTS[1]?.code) {
          this.boolSubmit && (this.btnText = text)
        } else {
          this.btnText = text
        }
        if (['取消订单', '填写安装信息', '选择奥金', '选择权益'].includes(this.btnText) && businessType !== '1') {
          this.btnText = ''
        }
        console.log('%c [ btnText ]-531', 'font-size:14px; background:#cf222e; color:#fff;', text)
        return
      }

      if (orderStatus === '0003' || orderStatus === '0002' || orderStatus === '0004' || orderStatus === '002600003' || orderStatus === '002600004') {
        this.btnText = ''
      }
      if ((orderStatus === '002600001' || orderStatus === '0001') && businessType === '1') {
        this.btnText = '取消订单'
      }
      if (orderStatus === '002600002' && this.boolSubmit && businessType === '1') {
        this.btnText = '填写安装信息'
      }
      if (orderStatus === '002600005' || orderStatus === '002600008' || orderStatus === '002600009') {
        this.btnText = '去评价'
      }
      if ((orderStatus === '002600006' || orderStatus === '002600007') && businessType === '1') {
        this.btnText = '选择奥金'
      }
      if ((orderStatus === '002600010' || orderStatus === '002600011') && this.boolSubmit && businessType === '1') {
        this.btnText = '填写安装信息'
      }
    },

    // 订单状态
    setStatusDesc(orderStatus) {
      // 新版状态 v20230214
      if (CHARGING_PILES_RIGHTS[`${orderStatus}`]) {
        console.log('订单状态', CHARGING_PILES_RIGHTS[`${orderStatus}`], orderStatus)
        return CHARGING_PILES_RIGHTS[`${orderStatus}`]
      }
    },

    callPhone(phone) {
      window.location.href = `tel:${phone}`
    },

    onOrderBtn() {
      const {
        skukw, orderStatus, autoOrderStatus, CANCEL_CODE, handleGoJumpSelectEquities
      } = this
      if (skukw === 'lite' && CANCEL_CODE.includes(orderStatus) && autoOrderStatus !== '90') {
        handleGoJumpSelectEquities()
        return
      }
      if (BTN_TEXTS[1].code.includes(orderStatus)) {
        // 填写安装信息
        this.$router.push({
          path: '/charging/install-create',
          query: {
            orderId: this.orderId,
            chargePileModel: this.orderInfo.chargePileModel
          }
        })
      }
      // 评价逻辑
      if (BTN_TEXTS[3].code.includes(orderStatus)) {
        window.location.href = this.orderInfo.shortUrl
        // callNative("openRoutePath", { path: "scaudi://mall/order?url="+this.orderInfo.shortUrl });
      }
      // 不满足安装信息
      if (BTN_TEXTS[2].code.includes(orderStatus)) {
        // 选择奥金
        this.show = true
      }
      // 取消订单
      if (BTN_TEXTS[0].code.includes(orderStatus)) {
        // 调用APP Dialog
        callNative('popup', {
          type: 'alert',
          alertparams: {
            title: '',
            desc: '是否确定取消订单？',
            actions: [
              {
                type: 'fill',
                title: '确定'
              },
              {
                type: 'stroke',
                title: '取消'
              }
            ]
          }
        }).then((data) => {
          if (data.type === 'fill') {
            // 点击确定
            this.cancelOrder()
          }
        })
      }
    },
    async cancelOrder() {
      const { data } = await postChargingPileCancel({ appointmentOrderId: this.orderId })
      this.getChargingPileInfo()
    },
    // 选择奥金
    async onSelectIntegral() {
      if (this.skukw === 'lite') {
        this.postSubmit()
        return
      }
      // 调用APP Dialog
      callNative('popup', {
        type: 'alert',
        alertparams: {
          title: '',
          desc: '选择兑换奥金后，不可选择安装充电桩，请您确认是否选择兑换奥金',
          actions: [
            {
              type: 'fill',
              title: '确认兑换奥金'
            },
            {
              type: 'stroke',
              title: '我再想想'
            }
          ]
        }
      }).then((data) => {
        if (data.type === 'fill') {
          // 点击确定
          this.postSubmit()
        }
      })
    },
    async postSubmit() {
      this.show = false
      const param = {
        orderId: this.carOrderId,
        equityType: this.radioType
      }
      this.$store.commit('showLoading')
      const { data } = await postCreateChargingPile(param)
      this.$store.commit('hideLoading')
      if (data.code === '00') {
        const { orderId } = this.$route.query
        this.$router.push({
          path: '/charging/charging-pile-success',
          query: { type: this.radioType, ...(orderId ? { from: 'install-info', name: 'money-detail', param: `'orderId=${orderId}'` } : {}) }
        })
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    onInfoStep() {
      this.$router.push({
        path: '/charging/install-info-step',
        query: { csmsOrderId: this.orderInfo.csmsOrderId }
      })
    },
    handleCancelJump() {
      this.interceptExit = true
      const { nativeApp } = this.getDevice() || { nativeApp: false }
      if (nativeApp) {
        callNative('prepage', { times: 1 })
      } else {
        this.$router.go(-1)
      }
    },
    handleGoJumpSelectEquities() {
      const {
        skukw, carOrderId, $router, autoOrderStatus
      } = this
      $router.push({
        path: '/charging/select-equities',
        query: { orderId: carOrderId, skukw, orderStatus: autoOrderStatus }
      })
    },
    handleConfirmJump() {
      const {
        skukw, autoOrderStatus, orderStatus, CANCEL_CODE
      } = this
      this.interceptExit = false
      if (skukw === 'lite' && CANCEL_CODE.includes(orderStatus) && autoOrderStatus !== '90') {
        this.handleGoJumpSelectEquities()
        return
      }
      this.show = true
    },
    handleLeftBack() {
      const {
        orderStatus, NO_STANDARD_CODE, CANCEL_CODE, orderInfo
      } = this
      // if ([...NO_STANDARD_CODE, ...CANCEL_CODE].includes(orderStatus) && orderInfo.status !== 3) {
      if (((NO_STANDARD_CODE.includes(orderStatus) || (CANCEL_CODE.includes(orderStatus) && orderInfo.havaOrder === 2)) && orderInfo.status !== 3) && orderInfo.businessType === '1') {
        this.interceptExit = true
      } else {
        const { nativeApp } = this.getDevice() || { nativeApp: false }
        if (nativeApp) {
          callNative('prepage', { times: 1 })
        } else {
          this.$router.go(-1)
        }
      }
    }

  }
}
</script>

<style lang="less" scoped>
@import url("../../assets/style/scroll.less");

@import "../../assets/style/common.less";
@import url("../../assets/style/buttons.less");

.container {
  overflow: auto;
  // height: 100%;
  padding-left: 16px !important;
  padding-right: 16px;
}

.linediv {
  margin-top: 16px;
  margin-bottom: 16px;
  width: 100%;
  height: 1px;
  background-color: #e5e5e5;
}

.flex1 {
  flex: 1;
}

.title-bold {
  font-size: 16px;
  color: #000;
  font-weight: normal;
  font-family: "Audi-WideBold";
}

.title-under-line {
  font-size: 16px;
  color: #000;
  font-weight: normal;
  text-decoration: underline;
  font-family: "Audi-Normal";
}

.item-store {
  .c-flex-between;
  border-top: 1px solid #e5e5e5;
  // border-top: 1px solid #e5e5e5;
  padding: 16px 0;
}

.item-wrapper {
  .c-flex-between;
}

.phone-icon {
  width: 12px;
  vertical-align: baseline;
}

.img-wrapper {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  display: flex;
}

.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  padding: 5px 0;
}

.navgation-wrapper {
  .c-font14;
  .c-flex-center;
  color: #b2b2b2;
  text-align: center;

  .nav-icon {
    width: 24px;
    height: 24px;
    margin: 0 auto 5px auto;
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  padding-top: 16px;
  padding-bottom: 50px;
  left: 0px;
}

.btn-delete-wrapper {
  margin: 0 16px;
}

.btn-delete-height {
  height: 90px;
}

.service-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;

  .title-bold {
    font-size: 16px;
    color: #000;
    font-weight: normal;
    font-family: "Audi-WideBold";
  }

  .btn-change {
    .btn-icon {
      width: 20px;
      height: 20px;
    }
  }
}

.img-icon {
  height: 20px;
  width: 20px;
  padding-bottom: 8px;
}
.lan-action-sheet-huixing {
  /deep/.van-action-sheet__header {
    height: 52px;
    line-height: 52px;
    font-weight: 600;
    color: #000;
    border-bottom: solid .5px #D9D9D9;
    .van-action-sheet__close {
      width: 24px;
      height: 24px;
      &::before {
        content: '';
        width: 24px;
        height: 24px;
        background: url(~@/assets/img/cross-leptonema.png) no-repeat 50%;
        background-size: 86%;
      }
    }
  }
  /deep/ .van-action-sheet__content {
    padding-bottom: 50px;
    .radio-group-huixing {
      padding-bottom: 24px;
      .van-radio {
        height: 24px;
        line-height: 24px;
        padding: 16px;
        .van-radio__label {
          color: #333;
          margin-left: 22px;
        }
      }
    }
  }
}
.warning-tips {
  padding: 0 16px 0;
  line-height: 20px;
  img {
    width: 20px;
    height: 20px;
  }
  span {
    font-size: 12px;
    margin-left: 4px;
    opacity: .6;
  }
}
</style>
