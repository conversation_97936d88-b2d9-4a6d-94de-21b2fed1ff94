<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-04-29 15:03:53
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-10-26 10:43:16
 * @FilePath     : \src\view\limited-number\select-number\series\index.vue
 * @Descripttion : 限量号(Q5)首页
-->
<script src="../index"></script>
<template>
  <div
    :style="{background: `#000 url(${require('@/assets/limitNumber/bg.jpg')}) 50% bottom no-repeat`}"
    :class="
      ['limited-number-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    >
      <template #right>
        <p
          class="btn"
          @click="$router.push({ name: 'limited-number-rule-note', query: { seriesCode } })"
        >
          规则
        </p>
      </template>
    </header-custom>
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`,
        'background': `url(${require('@/assets/limitNumber/audi-logobg.jpg')}) 50% 30px no-repeat`
      }"
    >
      <template v-if="step && step < 5">
        <div
          class="sign-box"
          v-if="step > 1 && step < 4"
        >
          <div
            class="sign"
          >
            本轮选号还有<van-count-down
              class="lan-count-down"
              :time="time"
              format="HH : mm : ss"
              auto-start
              ref="countDown"
              @finish="handleCountDownFinished"
            />结束，<br>本轮选号结束前，若不完成选号或不进行换号，系统将在以下号码中自动为您匹配一个限量号。
          </div>
        </div>
        <div
          :class="`limited-number-box code-box code-box-s${step}`"
          data-flex="dir:top"
        >
          <div
            v-if="step === 1"
            class="code-input-box"
            data-flex="dir:top cross:top"
            data-block
          >
            <div
              class="code"
              data-flex="main:center cross:center"
            >
              <!-- type 为 number会发有异常 -->
              <input
                readonly
                id="limited-number"
                type="tel"
                pattern="[0-9]*"
                :maxlength="numberLength"
                v-model="limitedNumber"
                :autofocus="false"
                @focus="focused = true"
                @blur="focused = false"
              >
              <label
                for="limited-number"
                class="line"
                v-for="(item,index) in numberLength"
                v-text="numbers[index]"
                :key="index"
                :class="{'animated': focused && cursorIndex === index}"
              />
            </div>
          </div>
          <div
            class="number-pools"
            v-if="step > 1"
          >
            <div
              :class="['number-box', `number-s${step}`]"
            >
              <div
                data-flex="main:center cross:center"
                v-if="!numberPool.length"
              >
                <p
                  class="numbers"
                  style="width:100%"
                />
              </div>
              <div
                :data-flex="`${numberPool.length < 3 ? 'main:center' : 'main:left'} box:wrap`"
                v-else
              >
                <p
                  :class="['numbers', pitchNumber === num || !pitchNumber && index === 0 ? 'pitch-on' : '']"
                  v-for="(num,index) of numberPool"
                  :key="index"
                  @click="handlePitchOnNumber(num)"
                >
                  {{ num }}
                </p>
              </div>
            </div>
          </div>
          <div class="code-tips">
            <template v-if="step === 1">
              <p
                v-if="limitedNumberIllegality"
                class="error"
              >
                需输入001-999范围内的限量号
              </p>
              <p v-else>
                请输入您的意向号码：<br>若您的意向号码未被占用，系统将为您自动匹配； 若您的意向号码已被占用，须进入随机选号环节
              </p>
            </template>
            <template v-else>
              <p v-if="step === 2 && numberPool.length === 3">
                您可从三个限量号中任选一个，也可进行奥金抽号
              </p>
              <p v-if="numberPool && numberPool.length < (step === 2 ? 3 : 20)">
                剩余限量号不足，仅有以上限量号供您选择
              </p>
            </template>
          </div>
          <div
            class="button-box"
          >
            <template v-if="step === 1">
              <audi-button
                :text="dispositionBtnText"
                color="black"
                height="56px"
                :class="['black-btn', dispositionBtnEnabled ? 'btn-enabled' : 'btn-un-enabled']"
                @click="handleDispositionBtn"
              />
            </template>
            <template v-else>
              <audi-button
                :text="dispositionBtnText"
                color="black"
                height="56px"
                style="margin-bottom: 5px"
                :class="['black-btn', numberPool.length && dispositionBtnEnabled ? 'btn-enabled' : 'btn-un-enabled']"
                @click="handleDispositionBtn"
              />
              <audi-button
                :text="spendingBtnStatus ? '正在选号中...' : `消耗${refreshIntegral}奥金更新备选号`"
                height="56px"
                :class="['white-btn', spendingBtnStatus || !numberPool.length || !numberNotEnough ? 'btn-un-enabled' : 'btn-enabled']"
                @click="handleIntegralBuyBtn"
              />
            </template>
          </div>
        </div>
        <van-number-keyboard
          v-if="step === 1"
          class="lan-number-keyboard"
          :show="focused"
          v-model="limitedNumber"
          :maxlength="numberLength"
        />
      </template>
      <van-popup
        v-model="popShow"
        class="popup-custom"
        :close-on-click-overlay="false"
      >
        <div class="popup-custom-box">
          <div class="popup-custom-main">
            <div class="text align-center">
              {{ popMainText }}
              <p v-if="popMainTips">
                {{ popMainTips }}
              </p>
            </div>
          </div>
          <div
            class="popup-custom-btn"
            :data-flex="popBtnReign.length > 1 ? 'main:justify' : 'dir:top'"
          >
            <audi-button
              v-for="(b,i) of popBtnReign"
              height="56px"
              :key="i"
              :text="b.text"
              :color="b.color"
              :width="popBtnReign.length === 2 ? 'calc(50% - 3px)' : '100%'"
              :class="[`${b.color}-btn`, b.enabled ? 'btn-enabled' : 'btn-un-enabled']"
              @click="handleClickPopupBtn(i)"
            />
          </div>
        </div>
      </van-popup>
      <van-popup
        v-model="popCircle"
        class="popup-custom popup-circle"
        :close-on-click-overlay="false"
        data-flex="main:center"
      >
        <div
          class="popup-custom-box"
          data-flex="cross:center dir:top"
        >
          <van-circle
            v-model="loadingRate"
            :speed="20"
            color="#000"
            layer-color="#f6f6f6"
            size="120px"
            :text="`绑定中...`"
          />
          <p>请您耐心等待</p>
        </div>
      </van-popup>
    </div>
  </div>
</template>
<style lang="less">
.popup-custom {
  width: calc(100vw - 32px);
  &.popup-circle {
    width: 68vw;
    padding: 40px 0 20px;
    p {
      margin-top: 24px;
      font-size: 14px;
    }
  }
  padding: 20px 24px;
  box-sizing: border-box;
  .popup-custom-main {
    padding-top: 15px;
    .text {
      font-size: 16px;
      line-height: 180%;
      &.align-center {
        text-align: center;
      }
      p {
        font-size: 12px;
        color: #666;
        line-height: 24px;
        margin: 6px 0 -12px 0;
      }
    }
  }
  .popup-custom-btn {
    margin-top: 30px;
  }
}
.sign-box {
  padding: 18px 18px 5px 18px;
  color: #fff;
  text-align: center;
  .sign {
    line-height: 160%;
  }
  .lan-count-down {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    padding: 0 4px;
    margin: 0 3px;
    border-radius: 1px;
    vertical-align: text-bottom;
    background-color: #fff;
  }
}
.number-pools {
  .number-box {
    text-align: center;
    width: 100%;
    height: 320px;
    &.number-s2, &.number-s3 {
      .numbers {
        &::before {
          top: 8px;
          left: -13px;
          bottom: 8px;
          border-left: 1px solid #e2e2e2;
        }
      }
    }
    &.number-s2 {
      height: 66px;
      .numbers {
        width: calc(33.333333% - 24px);
        &:nth-child(3n+1) {
          &::before {
            border-left-color: #fff;
          }
        }
      }
    }
    &.number-s3 {
      .numbers {
        width: calc(24.999999% - 20px);
        margin: 12px 10px;
        font-size: 24px;
        // max-height: 32px;
        &:nth-child(4n+1) {
        &::before {
          border-left-color: #fff;
        }
      }
      }
    }
    .numbers {
      position: relative;
      margin: 12px;
      font-weight: bold;
      font-size: 30px;
      max-height: 38px;
      color: #000;
      font-family: 'Audi-WideBold';
      box-sizing: border-box;
      &::before, &::after {
        content: '';
        position: absolute;
        transition: all .35s;
      }
      &::after {
        border-bottom: 3px solid transparent;
      }
      &.pitch-on {
        &::after {
          right: 8px;
          left: 8px;
          bottom: 0;
          border-bottom-color: #000;
        }
      }
    }
  }
}

@media screen and (max-height: 700px) {
  .main-wrapper {
    .sign-box {
      font-size: 14px;
      padding-bottom: 0;
    }
    .code-box {
      &.code-box-s3 {
        padding: 10px 14px 16px;
      }
    }
  }


  // .number-pools {
  //   .number-box {
  //     &.number-s3 {
  //       // height: 220px;
  //       .numbers {
  //         margin: 6px 10px;
  //       }
  //     }
  //   }
  // }
}

.lan-number-keyboard {
  background-color: #323233;
  .van-key {
    border-radius: 5px;
    background-color: #646465;
    color: #fff;
    box-shadow: 1px 1px 1px #000;

  }
  .van-key__wrapper {
    &:nth-last-child(1) {
      .van-key {
        background-color: transparent;
        box-shadow: none
      }
    }
    &:nth-last-child(3) {
      .van-key {
        position: absolute;
        left: -999px;
        display: none;
      }
    }
  }

}
</style>
<style lang="less" scoped>
@import url(../../index.less);
.page-wrapper {
  background-size: 100% auto !important;
  .main-wrapper {
    background-size: 90% !important;
  }
}
.button-box, .popup-custom-btn {
  .button {
    transition: all .35s;
  }
  .black-btn {
    &.btn-un-enabled {
      background-color: #e2e2e2 !important;
      border-color: #e2e2e2 !important;
      color: #aaa !important;
    }
  }
  .white-btn {
    &.btn-un-enabled {
      background-color: #fefefe!important;
      border-color: #f2f2f2 !important;
      color: #c2c2c2 !important;
    }
  }
}
@media screen and (max-height: 700px) {
  .code-box.code-box-s1 {
    margin-top: 18% !important;
  }
  .main-wrapper {
    background-position-y: 60px !important;
  }
}

.header-wrapper {
  .header-right {
    .btn {
      font-size: 14px;
    }
  }
}
.code-box {
  background-color: #fff;
  margin: 16px;
  padding: 20px 24px 16px;
  &.code-box-s1{
    margin-top: 25%;
  }
  .code-tips {
    margin: 12px 0;
    font-size: 14px;
    color: #666;
    // height: 60px;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      height: 1px;
      background-image: linear-gradient(to right, #E5E5E5 35%, #ffffff 0%);
      background-position: bottom;
      background-size: 10px 1px;
      background-repeat: repeat-x;
      bottom: -10px;
      overflow: hidden;
    }
    p {
      margin: 0 0 8px 0;
      font-size: 12px;
      line-height: 160%;
      text-align: center;
      &.error {
        color: #F50537;
      }
    }
  }
  .code-input-box {
    text-align: center;
    color: #2e2f33;
    font-size: 16px;
    .code {
      position: relative;
      margin: 0 auto;
      input {
        position: absolute;
        top: -100%;
        left: -9999px;
        opacity: 0;
      }
      .line {
        position: relative;
        margin: 0 7px;
        width: 48px;
        height: 64px;
        line-height: 64px;
        text-align: center;
        font-size: 36px;
        font-weight: bold;
        color: #2e2f33;
        font-family: 'Audi-WideBold';
        background-color: #F2F2F2 ;
        cursor: text;
        &::after {
          display: block;
          position: absolute;
          content: "";
          left: 0;
          width: 100%;
          bottom: 0;
          height: 1px;
          transform: scaleY(0.5);
          transform-origin: 0 100%;
        }
        &.animated::before {
          display: block;
          position: absolute;
          left: 50%;
          top: 35%;
          width: 1px;
          height: 40%;
          content: "";
          background-color: #2e2f33;
          animation-name: coruscate;
          animation-duration: 1s;
          animation-iteration-count: infinite;
          animation-fill-mode: both;
        }
      }
    }
  }
  .button-box {
    padding-top: 12px;
    position: relative;
    &::before, &::after{
      content: '';
      position: absolute;
      top: -10px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: #000;
      overflow: hidden;
    }
    &::before {
      left: -32px;
    }
    &::after{
      right: -32px;
    }
  }
}


@keyframes coruscate {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
</style>
