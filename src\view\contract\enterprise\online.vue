<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-01-30 11:04:33
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-18 17:38:28
 * @FilePath     : \src\view\contract\enterprise\online.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['contract-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`, height: `calc(100vh - ${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px)`}"
    >
      <div
        class="contract-box"
        data-flex="dir:top"
      >
        <dl
          class="dl"
          data-flex="dir:top"
          data-block
        >
          <dt
            class="dt"
            v-if="+isSelectOnline !== 1"
          >
          接下来您可以在以下签署方式中进行选择:
          </dt>
          <dd class="dd">
            <ul>
              <!-- <li
                class="li"
                style="margin: 0;"
                v-if="+isSelectOnline === 1"
              >
                <p class="p">
                  您已选择线上签署合同，请耐心等待上汽奥迪工作人员于第三方合同签署平台（法大大SAAS平台）发起签署流程，线上认证通过即可签署；
                </p>
              </li> -->
              <li
                class="li"
            
              >
                <h4 class="h4">
                  线上签署
                </h4>
                <p class="p">
                  点击“线上签署”，后续将由上汽奥迪工作人员于第三方合同签署平台(法大大SAAS平台)发起签署流程，线上认证通过即可签署。
                </p>
              </li>
              <!-- <li class="li">
                <h4 class="h4">
                  线下签署<span class="spu">（线下签署涉及邮寄等步骤，流程可能较长）</span>
                </h4>
                <p
                  class="p"
                  v-if="+isSelectOnline === 1"
                >
                  如遇特殊情况，无法完成线上签署流程，您依然可以点击“线下签署”，下载购车合同文件，联系代理商奥迪管家进行线下签署购车合同。
                </p>
                <p
                  class="p"
                  v-else
                >
                  点击“线下签署”，下载购车合同文件，联系代理商奥迪管家进行线下签署购车合同。
                </p>
              </li> -->
            </ul>
          </dd>
        </dl>
        <div
          class="affirm-order-info-box"
      
        >
          <!-- <div class="lan-button-box black-button ghost-button line-two-cols">
            <van-button
              class="lan-button"
              @click="goToContractOffline"
            >
              线下签署
            </van-button>
          </div> -->
          <div
            class="lan-button-box black-button "
          >
            <van-button
              @click="goToContractPage"
              :disabled="+isSelectOnline === 1"
              class="lan-button lan-darkly"
            >
              线上签署
            </van-button>
          </div>
        </div>
        <van-popup
          v-model="popChangeContract"
          class="popup-custom"
          :close-on-click-overlay="false"
        >
          <div class="popup-custom-box">
            <div class="popup-custom-main">
              <div class="text align-center">
                提交成功后，系统会将您此次已完成签署的购车合同发送至您的邮箱中
              </div>
            </div>
            <div
              class="popup-custom-btn"
              data-flex="main:justify"
            >
              <div class="lan-button-box black-button ghost-button line-two-cols">
                <van-button
                  class="lan-button"
                  @click="popChangeContract = false"
                >
                  取消
                </van-button>
              </div>
              <div
                class="lan-button-box black-button line-two-cols"
              >
                <van-button
                  @click="goToContractPage"
                  class="lan-button lan-darkly"
                >
                  确认
                </van-button>
              </div>
            </div>
          </div>
        </van-popup>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import {
  Button, Loading, Toast, Popup
} from 'vant'
import HeaderCustom from '@/components/header-custom.vue'
import { sendEnterpriseWeChatMessage, setEnterpriseChannelSign } from '@/api/api'
import wx from 'weixin-js-sdk'

Vue.use(Button).use(Loading).use(Toast).use(Popup)
export default {
  components: {
    'header-custom': HeaderCustom
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      orderId: '',
      seriesCode: '',
      isTurnOffLine: -1,
      mobile: '',
      loading: false,
      isSelectOnline: 0,
      popChangeContract: false
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      const { isSelectOnline } = this.$route.query
      if (isSelectOnline) {
        this.isSelectOnline = +isSelectOnline
      }
    },
    async goToContractOffline() {
      const {
        orderId, isClickDownloadContract, env,orderType
      } = this.$route.query
      const { data: sign } = await setEnterpriseChannelSign({ orderId }).finally((s) => {
        // signingToast.clear()
      })
      if (sign?.code !== '00') {
        Toast({
          message: '企业合同转线下标记失败',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      }
      console.log('%c [ sign ]-213', 'font-size:14px; background:#cf222e; color:#fff;', sign)
      // if (env === 'minip') {
      //   // return this.handleGoToPageDownloadApp()
      //   const { origin, pathname } = location
      //   const url = `/pages/order/contract/index?orderId=${orderId}&isTurnOffLine=1&isClickDownloadContract=${isClickDownloadContract}&prod=${process.env.VUE_APP_ENV === 'pre' ? 0 : 1}&url=${encodeURIComponent(`${origin}${pathname}#/contract/guide`)}`
      //   return wx.miniProgram.navigateTo({ url })
      // }
      this.$router.push({
        name: 'contract-info',
        query: { orderId, turnOffLine: 1, isClickDownloadContract,orderType}
      })
    },
    async goToContractPage() {
      const {
        orderId, purchaseContractId, mobile, isSelectOnline, isClickDownloadContract, lastSelectOnline,orderType
      } = this.$route.query

      return this.$router.push({
        name: 'contract-enterprise-knows',
        query: {
          orderId, purchaseContractId, mobile, isSelectOnline, isClickDownloadContract, lastSelectOnline,orderType
        }
      })
    },
    async handleOnlineContract() {
      const { orderId } = this
      this.loading = true
      const { data: { code, data } } = await sendEnterpriseWeChatMessage({ orderId }).finally((s) => {
        this.loading = false
      })

      console.log('%c [ code, data ]-231', 'font-size:14px; background:#cf222e; color:#fff;', code, data)
      if (code === '00') {
        this.isTurnOffLine = 2
      }
      Toast({
        message: code === '00' ? '发起签署流程成功' : '发起签署流程失败',
        className: 'toast-dark-mini toast-pos-middle',
        forbidClick: true,
        duration: 800
      })
    },
    handleLeftBack() {
    }
  }
}
</script>
<style lang="less" scoped>
.contract-wrapper {
  .contract-box {
    height: 100%;
    font-size: 14px;
    color: #333;
    line-height: 22px;
    .dl {
      margin: 0;
      padding: 16px;
      .dd {
        margin: 0;
        .li {
          margin: 24px 0 0 0;
          .h4, .p {
            margin: 0 0 4px 0;
          }
          .h4 {
            font-family: 'Audi-WideBold';
            font-weight: normal;
            .spu {
              color: #999;
              font-size: 12px;
            }
          }
          // &:last-child {
          //   margin-top: 24px;
          // }
        }
      }
    }
  }
  .affirm-order-info-box {
    padding: 16px 16px 50px 16px;
  }
}
</style>
