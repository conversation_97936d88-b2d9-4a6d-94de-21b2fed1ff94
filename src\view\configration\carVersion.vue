<template>
  <div id="carVersion">
    <div style="padding: 0 16px">
      <div style="padding-top: 16px">
        <div style="height: 234px; width: 100%;">
          <img
            style="transform: scale(1.6);"
            v-if="currentVerInfo.customSeriesCode != 49 && currentVerInfo.imageUrl"
            :src="BaseConfigrationOssHost + currentVerInfo.imageUrl | imgFix(740)"
            alt="车辆配置线图"
          >
          <img
            style="transform: scale(1.4);"
            v-if="currentVerInfo.customSeriesCode == 49 && currentVerInfo.imageUrl"
            :src="BaseConfigrationOssHost + currentVerInfo.imageUrl | imgFix(740)"
            alt="车辆配置线图"
          >
        </div>
        <div class="namebox">
          {{ formatName(currentVerInfo.modelLineName)[0] }} <br>
          {{ formatName(currentVerInfo.modelLineName)[1] }}
        </div>
        <div
          style="display: flex;justify-content: center;padding: 6px 0"
          @click.stop="showDetail()"
        >
          <div class="detail">
            查看详情
          </div>
          <van-icon
            color="#999"
            name="arrow"
          />
        </div>
      </div>
      <!-- v-if="$route.query.from != 'hotRecom'" -->
      <div
        style="padding-top:12px"
        v-if="!visibleChair"
      >
        <div
          v-if="versionData.length > 0 && $route.query.idx == 0"
          style="font-family: 'Audi-WideBold';"
        >
          请选择套装
        </div>
        <div
          v-if="versionData.length > 0 && $route.query.idx == 1"
          style="font-family: 'Audi-WideBold';"
        >
          请选择型号
        </div>
        <div style="display: flex;justify-content: space-between;padding-top: 8px">
          <div
            class="itemBox"
            @click="versionChange(item)"
            v-for="(item, index) in versionData"
            :style="{
              border: `1px solid ${currentVerInfo.modelLineCode == item.modelLineCode ? '#000' : '#E5E5E5'}`,
              marginRight: index == 0 ? '3px' : 0,
              marginLeft: index == 1 ? '3px' : 0
            }"
          >
            <div style="height: 103px;position: relative;overflow: hidden;">
              <img
                :src="BaseConfigrationOssHost + item.suitImageUrl | imgFix(640)"
                alt="车辆配置线图"
              >
            </div>
            <div class="title">
              {{ item.suit }}
            </div>
          </div>
        </div>
      </div>

      <div
        style="padding-top:12px"
        v-if="visibleChair"
      >
        <div style="font-family: 'Audi-WideBold';">
          请选择座舱
        </div>
        <div style="display: flex;justify-content: space-between;padding-top: 8px">
          <div
            class="itemBox"
            @click="versionChange(item)"
            v-for="(item, index) in versionData"
            :style="{
              border: `1px solid ${currentVerInfo.modelLineCode == item.modelLineCode ? '#000' : '#E5E5E5'}`,
              marginRight: index == 0 ? '3px' : 0,
              marginLeft: index == 1 ? '3px' : 0
            }"
          >
            <div style="height: 103px;position: relative;overflow: hidden;">
              <img
                id="imgB"
                :src="BaseConfigrationOssHost + item.tags[0].imageUrl"
              >
            </div>
            <div
              class="title"
              style="display: flex;justify-content: space-between;"
            >
              <div>{{ item.tags[0].tagName }}</div>
              <van-icon
                @click.stop="goInfo(item)"
                style="line-height: 53px;margin-right: 10px;"
                name="info-o"
              />
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="packageData.length > 0"
        style="padding-top: 38px"
      >
        <div style="margin-bottom: 8px;font-family: 'Audi-WideBold';">
          请选择权益
        </div>
        <div
          class="itemB"
          @click="packageChange(item)"
          v-for="(item, index) in packageData"
          :key="index"
          :style="{ border: `1px solid ${item.optionCode == currentPackageInfo.optionCode ? '#000' : '#E5E5E5'}`, marginTop: index ? '12px' : 0 }"
        >
          <div>{{ item.optionName }}</div>
          <div
            style="display: flex;"
            @click.stop="handleLook(item)"
          >
            <div
              class="detail"
              color="#999"
            >
              查看详情
            </div>
            <van-icon
              color="#999"
              name="arrow"
            />
          </div>
        </div>
      </div>
      <div style="height: 128px" />
    </div>
    <footer>
      <div class="text">
        <p style="line-height: 21px;">
          <span> ￥{{ price | formatPrice }}</span>
        </p>
      </div>
      <div
        class="button"
        @click="handleNext"
      >
        下一步
      </div>
    </footer>
    <network @reload="networkReload()" />
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import {
  getRecommendCar, getPacketEquity, getModellineList, getStyleList, getByStyle, recomCarByStyleList, getPrivateOrder, recomStyleFix
} from '@/configratorApi/index'
import url from '@/config/url'
import storage from '@/utils/storage'
import { checkV2 } from '@/utils/index'
import network from '@/components/network.vue'
import { getAPITimeOutTesting } from '@/api/api'
import { appToast } from '@/utils/bridgeApi'
import { Toast }  from "vant"

export default {
   components: {
    network
  },
  data() {
    return {
      visibleChair: false, // visibleChair == true 表示q6
      chairInfo: [],
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      versionData: [],
      packageData: [],
      // todo remove
      modelLineCode: '',
      modelLineId: '',
      carData: [],
      currChair: {},
      currentVerInfo: {},
      currentPackageInfo: {},
      price: '',
      priceBox: '',
      tempLocalInfo: {
        modelLineCode: '',
        optionCode: ''
      },
      defCode: 'YEA'
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 获取上一级路由的路径
      vm.$store.commit('setFromPath', from.path)
      if (['/car-config-table', '/theOptionDetails'].includes(from.path)) {
        vm.tempLocalInfo.modelLineCode = vm?.$storage?.getPlus('currentModelLineCode')
        vm.tempLocalInfo.optionCode = vm?.$storage?.getPlus('currentOptionCode')
      }
    })
  },
  computed: {
    ...mapState({
      hotRecommendCarList: (state) => state.hotRecommend.hotRecommendCarList
    })
  },
  activated() {
  },
  created() {
    this.$store.commit('setTitle', '版本选择')
    // this.modelLineCode = this.$route.query.modelLineCode
    // this.modelLineId = this.$route.query.modelLineId
    this.getByStyle()
    // this.recomStyleFix()
    
  },
  mounted() {
  },

  methods: {

    async goInfo(option) {
      const tags = option.tags[0]
      let chair = ''
      if (tags.tagCode == 'Q6_6SEAT') chair = 'PS1'
      if (tags.tagCode == 'Q6_7SEAT') chair = 'PS8'
      const modelLineId = option.modelLineId // modelLineId
      // optionCode "PS8" PS1
      const resBox = await getPrivateOrder(modelLineId)
      const arr = resBox?.data?.data || []
      this.chairInfo = arr.find((e) => chair == e.optionCode) || {}
      this.$store.commit('setClickOption', this.chairInfo)
      resBox && this.$router.push({
        path: '/optiondetailPlus',
        query: {
          form: 'carVersion'
        }
      })
    },
    formatName(val = '') {
      let index = val.length
      for (let i = 0, l = val.length; i < l; i++) {
        if (val.charCodeAt(i) > 255) {
          index = i
          break
        }
      }
      const str1 = val.substring(0, index)
      const str2 = val.substring(index)
      return [str1, str2]
    },
    handleNext() {
      if (!this.currentVerInfo?.modelLineId) return
      const {
        orderStatus, orderId, ccid, shoppingCartId, idx, from, styleId
      } = this.$route.query
      const obj = {
        orderStatus, orderId, ccid, shoppingCartId
      }

      // q5 + q6 + from 'hotRecom' 更新semi-definite
      if ((idx == 2 || idx == 1) && from == 'hotRecom') this.$storage.setPlus('semi-definite', this.currentVerInfo?.measure ? '个性定制' : '私人高定') // 1 '私人订制' : '0个性定制'
      this.$router.push({
        path: '/configration',
        query: {
          ...obj,
          idx: ['49', 'G4', 'G6'].findIndex((e) => e == this.currentVerInfo.customSeriesCode),
          from: from === 'hotRecom' ? 'hotRecom' : 'carVersion',
          tabIndex: 1,
          modelLineId: this.currentVerInfo?.modelLineId,
          styleId: styleId,
          optionId: this.currentPackageInfo?.optionId || '',
          modelLineCode: this.currentVerInfo.modelLineCode,
          caseCode: this.currentPackageInfo?.optionCode
        }
      })
    },
    async getByStyle() {
      const {
        styleId, from, customSeriesId, modelLineId, idx
      } = this.$route.query
      // type 1 高  2半  3混
      getAPITimeOutTesting();
      const type = checkV2() ? 2 : 1
      const param = { styleId, type }
      let res = { data: {} }
      if (from !== 'hotRecom') {
        res = await getByStyle(param)
      } else {
        this.visibleChair = customSeriesId === 'd9f653d1-7870-478d-82bb-b16b27f4675c' // q6

        // if (['1', '2'].includes(idx)) { // q6 q5e
        //   res = await recomStyleFix({ customSeriesId })

        //   if (!res?.data?.data) return
        //   let arr = []
        //   res.data.data.forEach((element) => {
        //     if (element.styleVo && element.styleVo.styleId == modelLineId) {
        //       arr = element.styleVo.recommendCarSphereVos
        //     }
        //   })
        //   res.data.data = arr
        // } else {
        //   res = await getRecommendCar(customSeriesId)
        // }

        if (this.hotRecommendCarList.length === 0) {
          await this.$store.dispatch('getHotRecommendCarList')
        }
        let arr = []
        this.hotRecommendCarList.forEach((element) => {
          if (element.styleVo && element.styleVo.styleId === modelLineId) {
            arr = element.styleVo.recommendCarSphereVos
          }
        })
        res.data.data = arr
      }

      const data = res?.data?.data
      if (!data) return

      const hotCar = data.find((e) => e.modelLineId === modelLineId)
      // console.log('hotcar', hotCar)

      this.versionData = hotCar ? [hotCar] : data.sort(this.compareArr('price', true))
      this.versionData[0] && this.$storage.setPlus('currentModelLineCode', this.versionData[0].modelLineCode)
      this.currentVerInfo = this.tempLocalInfo?.modelLineCode ? ({ ...this.versionData.find((e) => e.modelLineCode == this.tempLocalInfo?.modelLineCode) || {} }) : this.versionData[0]
      this.price = this.currentVerInfo.price
      this.packetEquity()
    },
    /** 两个参数： 参数1 是排序用的字段， 参数2 是：是否升序排序 true 为升序，false为降序 */
    compareArr(attr, rev) {
      if (rev == undefined) {
        rev = 1
      } else {
        rev = (rev) ? 1 : -1
      }
      return (a, b) => {
        a = a[attr]
        b = b[attr]
        if (a < b) {
          return rev * -1
        }
        if (a > b) {
          return rev * 1
        }
        return 0
      }
    },

    async packetEquity() {
      const res = await getPacketEquity({ modelLineId: this.currentVerInfo.modelLineId })
      this.packageData = res.data.data.filter((f) => f.status !== 0)
      this.defCode = (this.packageData.find((e) => e.price < 0) || {})?.optionCode

      if (!this.defCode) {
        this.defCode = (this.packageData.find((e) => e.price > 0) || {})?.optionCode
      }

      if (!this.packageData.length) return
      if (this.tempLocalInfo?.optionCode) {
        const obj = this.packageData.find((e) => e.optionCode == this.tempLocalInfo?.optionCode)
        this.currentPackageInfo = { ...obj }
        this.priceBox = Math.abs(obj.price)
      } else {
        const obj = this.packageData.find((e) => e.optionCode == this.defCode)
        this.currentPackageInfo = obj
        this.priceBox = Math.abs(obj.price)
      }
    },
    showDetail() {
      const line = this.currentVerInfo
      // 这些车要显示特别版的图片+pdf
      const modelLineCodes = [
        '498B2Y007', // 黑武士55TFSI
        "498BZY007", // 黑武士45TFSI
        "498B2Y008", // 影武士45TFSI
        "498BZY008"// 影武士45TFSI
      ]
      const isSpecial = modelLineCodes.includes(line.modelLineCode) 

      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: line.modelLineId,
          carModelName: line.modelLineName,
          seriesCode: line.customSeriesCode,
          ...isSpecial ? { special: isSpecial } : {}, 
        }
      })
    },
    currChairChange(e) {
      this.currChair = e.chairObj
    },
    versionChange(e) {
      console.log(e)
      this.$storage.setPlus('currentModelLineCode', e.modelLineCode)
      const car = e
      this.currentVerInfo = car
      this.price = car.price
      if (this.currentPackageInfo.optionCode != this.defCode) {
        this.price = this.currentVerInfo.price + this.priceBox
      }
    },
    packageChange(e) {
      this.$storage.setPlus('currentOptionCode', e.optionCode)
      this.currentPackageInfo = e
      if (e.optionCode != this.defCode) {
        this.price = this.currentVerInfo.price + this.priceBox
      } else {
        this.price = this.currentVerInfo.price
      }
    },
    handleLook(e) {
      // "YEA"
      // optionId
      // "f7329319-befc-454c-a1c3-1eaed09f4bf0"
      // "轻装权益包"
      this.$router.push({
        path: '/theEquity',
        query: {
          form: 'carVersion',
          modelLineCode: this.currentVerInfo.modelLineCode,
          caseCode: e.optionCode,
          optionName: e.optionName,
          seriesName: this.currentVerInfo.customSeriesCode
        }
      })
      // return
      // this.$router.push({
      //     path: '/theOptionDetails',
      //     query: {
      //         optionCode: e.optionCode,
      //         modelLineId: this.currentVerInfo.modelLineId
      //     }
      // })
    },

    async networkReload() {
      await this.getByStyle();
      Toast.clear();
    }
  }
}
</script>
<style lang="less" scoped>
#carVersion {
    overflow: hidden;
}

#imgB {
    width: 100%;
    height: 100%;
    position: initial;
    transform: inherit;
}

footer {
    position: fixed;
    bottom: 0;
    height: 106px;
    width: 100vw;
    z-index: 2;
    background: #ffffff;
    box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);

    .text {
        color: #999;
        line-height: 12px;
        font-size: 12px;
        padding-left: 16px;
        padding-top: 10px;

        span {
            color: black;
            font-family: 'Audi-WideBold';
            font-size: 16px;
        }
    }

    .button {
        position: absolute;
        top: 12px;
        right: 16px;
        width: 136px;
        height: 44px;
        background: #000;
        color: white;
        font-size: 16px;
        text-align: center;
        line-height: 44px;
    }
}

.van-icon-arrow {
    font-size: 12px;
    line-height: 19px;
}

.namebox {
    padding-top: 16px;
    font-size: 14px;
    font-weight: 700;
    color: #333333;
    line-height: 22px;
    text-align: center;
    font-family: 'Audi-WideBold';
}

.detail {
    padding-right: 3px;
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    color: #999;
    line-height: 20px;
}

.title {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 53px;
    padding-left: 16px;
    background: #fff
}

.itemB {
    padding: 25px 24px;
    display: flex;
    justify-content: space-between;
}

.itemBox {
    height: 157px;
    width: 168px;
    background: #f5f5f5;

    img {
        width: 168px;
        height: 103px;
        position: absolute;
        top: -16px;
        left: 80px;
        transform: scale(2.2);
    }
}

.border {
    border: 1px solid #000;
}

.borderE5 {
    border: 1px solid #E5E5E5;
}
</style>
