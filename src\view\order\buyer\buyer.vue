<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-03 10:18:17
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-27 15:11:46
 * @FilePath     : \src\view\order\buyer\buyer.vue
 * @Descripttion :
-->
<script src="./buyer"></script>
<template>
  <div class="buyer-info">
    <van-cell-group class="lan-cell-group lan-top">
      <div class="cell-title van-hairline--bottom">
        <h2 class="h2">
          购车信息
        </h2>
      </div>
      <div class="cell-box">
        <div
          class="cell-list"
          data-flex="main:justify"
        >
          <span class="title">购买类型</span>
          <span class="value">{{ buyerInfo.buyTypeName }}</span>
        </div>
        <!-- <div
          class="cell-list"
          data-flex="main:justify"
        >
          <span class="title">付款方式</span>
          <span class="value">{{ buyerInfo.paymentTypeName }}</span>
        </div> -->
        <div
          class="cell-list"
          data-flex="main:justify"
        >
          <span class="title">省份城市</span>
          <span class="value">{{ buyerInfo.carLicenseCityName }}</span>
        </div>
        <div
          class="cell-list"
          data-flex="main:justify"
        >
          <span class="title">代理商</span>
          <span
            class="value"
            data-flex="main:right"
          ><span
            data-flex="main:left"
            data-block
          >{{ buyerInfo.dealerName }}</span><font
            class="navigation-btn"
            v-if="buyerInfo.lbsLatitude && buyerInfo.lbsLongitude && buyerInfo.address"
            @click="handleNavigationSoftware(buyerInfo.lbsLatitude, buyerInfo.lbsLongitude, buyerInfo.address)"
          >导航</font></span>
        </div>
      </div>
    </van-cell-group>
    <van-cell-group class="lan-cell-group">
      <div class="cell-title van-hairline--bottom">
        <h2 class="h2">
          购车人信息
        </h2>
      </div>
      <div class="cell-box">
        <div
          class="cell-list"
          data-flex="main:justify"
        >
          <span class="title">下单姓名</span>
          <span class="value">{{ buyerInfo.fullName }}</span>
        </div>
        <div
          class="cell-list"
          data-flex="main:justify"
        >
          <span class="title" >联系电话</span>

          <div>
              <span class="value" v-if="eyes1">{{ buyerInfo.mobile | phoneNum}}</span>
              <span class="value" v-else>{{ buyerInfo.mobile}}</span>

              <van-icon
                :name="eyes1 ? 'eye-o' : 'closed-eye'"
                size="20"
                @click="eyes1 = !eyes1"
                style="transform: translateY(5px);margin-left: 10px;"
              />
          </div>
         
        </div>
        <div
          class="cell-list"
          data-flex="main:justify"
        >
          <span class="title">证件类型</span>
          <span class="value">{{ buyerInfo.IDTypeName }}</span>
        </div>
        <div
          class="cell-list"
          data-flex="main:justify"
        >
          <span class="title">证件号码</span>

          <div>
              <span class="value" v-if="eyes2">{{ buyerInfo.IDNumber | idCardNum}}</span>
              <span class="value" v-else>{{ buyerInfo.IDNumber}}</span>

              <van-icon
                :name="eyes2 ? 'eye-o' : 'closed-eye'"
                size="20"
                @click="eyes2 = !eyes2"
                style="transform: translateY(5px);margin-left: 10px;"
              />
          </div>
          
        </div>
      </div>
    </van-cell-group>
    <van-cell-group class="lan-cell-group">
      <div class="cell-title van-hairline--bottom">
        <h2 class="h2">
          车主信息
        </h2>
      </div>
      <div class="cell-box">
        <template v-if="buyerInfo.buyType === '01'">
          <div
            class="cell-list"
            data-flex="main:justify"
          >
            <span class="title">车主姓名</span>
            <span class="value">{{ buyerInfo.carOwnerName }}</span>
          </div>
          <div
            class="cell-list"
            data-flex="main:justify"
          >
            <span class="title">联系电话</span>
            <div >
              <span class="value" v-if="eyes3">{{ buyerInfo.carOwnerMobile | phoneNum}}</span>
              <span class="value" v-else>{{ buyerInfo.carOwnerMobile}}</span>


              <van-icon
                :name="eyes3 ? 'eye-o' : 'closed-eye'"
                size="20"
                @click="eyes3 = !eyes3"
                style="transform: translateY(5px);margin-left: 10px;"
              />

            </div>
          </div>
          <div
            class="cell-list"
            data-flex="main:justify"
          >
            <span class="title">证件类型</span>
            <span class="value">{{ buyerInfo.carOwnerCertificateTypeName }}</span>
          </div>
          <div
            class="cell-list"
            data-flex="main:justify"
          >
            <span class="title">证件号码</span>

            <div>

              <span class="value" v-if="eyes4">{{ buyerInfo.carOwnerCertificateNumber | idCardNum}}</span>
              <span class="value" v-else>{{ buyerInfo.carOwnerCertificateNumber}}</span>

              <van-icon
                :name="eyes4 ? 'eye-o' : 'closed-eye'"
                size="20"
                @click="eyes4 = !eyes4"
                style="transform: translateY(5px);margin-left: 10px;"
              />

            </div>
           
          </div>
        </template>
        <template v-else>
          <div
            class="cell-list"
            data-flex="main:justify"
          >
            <span class="title">企业名称</span>
            <span class="value">{{ buyerInfo.enterpriseName }}</span>
          </div>
          <div
            class="cell-list"
            data-flex="main:justify"
          >
            <span class="title">企业代码</span>
            <span class="value">{{ buyerInfo.enterpriseCode }}</span>
          </div>
        </template>
      </div>
    </van-cell-group>
  </div>
</template>

<style lang="less">
@import url("~@/assets/style/forms-cell.less");
</style>
<style lang="less" scoped>
.navigation-btn {
  margin-left: 9px;
  padding-left: 9px;
  position: relative;
  &::before {
    content: '|';
    position: absolute;
    left: 0;
    top: 0;
    font-size: 10px;
    color:  #D8D8D8
  }
}
.lan-top{
  border-top: 0;
}
.lan-cell-group{
  padding-bottom: 0 !important;

}
</style>
