<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-11-10 11:21:16
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-11-28 17:12:43
 * @FilePath     : \src\components\car-series-pop.vue
 * @Descripttion : 车系选择
-->
<template>
  <div class="car-series">
    <div class="list" data-flex="main:justify cross:center" @click="() => { $emit('confirmSelectionSeries', list) }" v-for="list of series" :key="list.id">
      <div class="name">{{ list.name }}</div>
      <div class="img">
        <img :src="list.imageUrl">
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'car-series-pop',
  data() {
    return {
      series: [
        {
          idx: 3,
          id: '',
          name: 'A5L',
          seriesCode: 'F0',
          seriesName: 'A5L',
          imageUrl: 'https://audi-oss.saic-audi.mobi/audicc/app/favoritecar/v6/selectmodel_A5L.png'
        },
        {
          idx: 0,
          id: 'e1bcd996-e961-4179-8d23-8272626acedf',
          name: 'A7L',
          seriesCode: '49',
          seriesName: 'A7L',
          imageUrl: 'https://audi-oss.saic-audi.mobi/audicc/app/favoritecar/v6/selectmodel_A7L.png'
        },
        {
          idx: 2,
          id: '0f978dc2-c42e-46b3-863b-2f2fc3f0522c',
          name: 'Q6',
          seriesCode: 'G6',
          seriesName: 'Q6',
          imageUrl: 'https://audi-oss.saic-audi.mobi/audicc/app/favoritecar/v6/selectmodel_Q6.png'
        },
        {
          idx: 1,
          id: '06d75710-cc14-4d77-a4d0-e354bf3911de',
          name: 'Q5 e-tron',
          seriesCode: 'G4',
          seriesName: 'Q5',
          imageUrl: 'https://audi-oss.saic-audi.mobi/audicc/app/favoritecar/v6/selectmodel_Q5.png'
        }
      ]
    }
  },
  mounted() {},
  created() {},
  methods: {}
}
</script>
<style lang="less" scoped>
.car-series {
  .list {
    margin-top: 9px;
    padding: 2px 16px;
    &:first-child {
      margin-top: 15px;
    }
    background-color: #fff;
    transition: all .2s linear;
    &:active {
      background-color: #f6f6f6;
    }
    .name {
      font-size: 16px;
      font-family: Audi-Normal;
    }
    .img {
      width: 134px;
      height: 90px;
      object-fit: contain;
      overflow: hidden;
      img {
        width: 100%;
        height: auto;
      }
    }
  }
}
</style>
