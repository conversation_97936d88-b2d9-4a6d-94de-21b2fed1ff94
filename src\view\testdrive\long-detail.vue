<!-- 预约试驾详情 -->
<template>
  <div name="detail" class="detail-container">
    <div class="_head">
      <div class="_title">
        {{ status[data.status] }}
      </div>
      <div class="_content">
        {{ statusContent[data.status] }}
      </div>
    </div>

    <div class="_box">
      <div class="_list">
        <div class="_li">
          <div>试驾车系</div>
          <div style="color: #666">{{ data.seriesDto.customSeriesName }}</div>
        </div>
        <div class="_li">
          <div>试驾日期</div>
          <div style="color: #666">
            {{ data.appoBeginTime.substring(5, 7).replace(/\-/g, ".")+"月"+data.appoBeginTime.substring(8, 10).replace(/\-/g, ".")+"日" }}-{{
              data.appoEndTime.substring(5, 7).replace(/\-/g, ".")+"月"+data.appoEndTime.substring(8, 10).replace(/\-/g, ".")+"日"
            }}
          </div>
        </div>
        <div class="_li">
          <div>到店时间</div>
          <div style="color: #666">{{ data.appoBeginTime.substring(11, 16).replace(/\-/g, ".") }}</div>
        </div>
        <div class="_li">
          <div>试驾城市</div>
          <div style="color: #666">{{  data.dealerDto.provinceName + data.dealerDto.cityName }}</div>
        </div>
      </div>
      <div class="interval_line"></div>

      <div class="_list">
        <div class="_li" style="margin-top: 13px">
          <div>姓名</div>
          <div>{{ data.approveName }}</div>
        </div>
        <div class="_li">
          <div>联系方式</div>
          <div>{{ data.potMobile }}</div>
        </div>
      </div>
      <div class="interval_line"></div>
      <div class="_list">
        <div class="_li" style="margin-top: 13px">
          <div>代理商</div>
          <div>{{ data.dealerDto.dealerName }}</div>
        </div>
      </div>

      <div class="_agent">
        <agentCard :agent-card-data="agentCardData" :location="location" />
      </div>
      <div
        class="_cancel-test"
        @click="modalshow = true"
        v-if="data.canceled == 1"
      >
        取消预约
      </div>


       <div class="btn-delete-height"></div>
       <div class="bottom_style" v-if="data.payView">
          <div class="btn-delete-wrapper">
            <AudiButton
              @click="toPay"
              :text="'去支付'"
              color="black"
              font-size="16px"
              height="50px"
            />
          </div>
        </div>
        <div class="bottom_style" v-if="data.status === 90220">
          <div class="btn-delete-wrapper">
            <AudiButton
              @click="tocallNative"
              :text="'查看我的订单'"
              color="black"
              font-size="16px"
              height="50px"
            />
          </div>
        </div>

        <div class="bottom_style" v-if="data.isReport">
          <div class="btn-delete-wrapper">
            <AudiButton
              @click="goReport"
              :text="'查看试驾报告'"
              color="black"
              font-size="16px"
              height="50px"
            />
          </div>
        </div>
        
    </div>
    <model
      :modalshow.sync="modalshow"
      @update:modalshow="submit"
      title="确定取消试驾预约？"
    />
  </div>
</template>

<script>
import Vue from "vue";
import { Icon } from "vant";
import agentCard from "@/components/agent-card.vue";
import model from "@/components/model.vue";
import { postVeryLongReservationCancel, getVeryLongReservationTestDriveDetailByAppoid } from "../../api/test-driver";
import { callNative } from "@/utils";
import AudiButton from "@/components/audi-button";
import storage from "../../utils/storage";

Vue.use(Icon);
export default {
  components: { agentCard, model,AudiButton},
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      id: "",
      data: {},
      status: {
        90210: "待开始",
        90220: "订单进行中",
        90230: "订单已完成",
        90240: "订单已取消",
        90241: "订单已取消",
        90211: "订单未支付",
      },
      statusContent: {
        90210: "",
        90220: "超长试驾进行中，如有任何问题，您可以与您的体验专家直接沟通，我们将竭诚为您提供服务。",
        90230: "",
        90240: "您的试驾预约已成功取消",
        90241: "您的试驾预约已取消",
        90211: "预约成功，请您在60分钟内完成后续操作，如果超时，本预约将会自动取消，敬请谅解",
      },
      modalshow: false,
      agentCardData: {},
      confirm: false,
      location: "31.230525,121.473667", // 当前的经纬度
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getdata();
    this.$bridge.callHandler("getLocationCity", {}, (err, data) => {
      if (err) {
        // 发生错误
        console.log(err);
        return;
      }
      if (data.location) {
        this.location = data.location;
      }
    });
  },
  methods: {
    async getdata() {
      this.$store.commit("showLoading");
      await getVeryLongReservationTestDriveDetailByAppoid({ appoId: this.id }).then((res) => {
        console.log("res", res);
        if (res.status === 200 && res.data.code === "200") {
          this.data = res.data.data;
          this.agentCardData = this.data.dealerDto;
          this.confirm = !!this.data.custIdCard;
        }
        this.$store.commit("hideLoading");
      });
    },
    //去支付
    toPay(){
      const param = {
          payUrl: this.data.payUrl,
          appoId:this.id
        };
       storage.set("saveLongTestdrivePayUrl", JSON.stringify(param));

      this.$router.push({
            path: this.fromType ? '/testdrive/long-create-success?fromType=fromPurple' : '/testdrive/long-create-success',
            query: {
              type: 0
            }
      })
    },
    tocallNative() {
      callNative("openRoutePath", { path: "scaudi://mall/orderlist?index=3" });
	    callNative("close", { type: "service" });
    },
      //查看试驾报告
    goReport() {
      // window.location.href = this.data.reportUrl
      this.$router.push({
        path: this.fromType ? '/testdrive/long-report?fromType=fromPurple' : '/testdrive/long-report',
        query: {
          id: this.data.testDriveId,
          seriesCode: this.data.seriesDto.seriesCode,
          appoid: this.id
        }
      })
    },
    submit(isSubmit) {
      const params = {
        page_name: "预约试驾页面",
      };
      this.$sensors.track("clickCancelAppointment", params);
      if (isSubmit) {
        this.modalshow = false;
        const data = {
          appoId: this.data.appointmentId,
          
        };
        postVeryLongReservationCancel(data).then((res) => {
          if (res.status === 200) {
           this.getdata();
          }
        });

        const params = {
          test_drive_id: this.data.appointmentId,
        };
        this.$sensors.track("clickCancelTestDrive", params);
      }
    },
    filters: {
      beginTimeFilter(val) {
        if (val) {
          return val.substring(0, 16);
        }
        return "";
      },
      endTimeFilter(val) {
        if (val) {
          return val.substring(11, 16);
        }
        return "";
      },
    },
  },
};
</script>

<style lang='less' scoped>
@import url("../../assets/style/buttons.less");

div {
  box-sizing: border-box;
}

.detail-container {
  display: flex;
  flex-flow: column;
  width: 100%;
  margin: 0;
  padding: 0;
  border-top: 1px #f2f2f2 solid;
  color: #000000;
  padding-bottom: 50px;
  overflow: hidden;

  ._head {
    width: 100%;
    display: flex;
    flex-flow: column;
    box-sizing: border-box;
    border-bottom: 1px #f2f2f2 solid;
    background: #f2f2f2;
    padding: 14px 16px;

    ._title {
      font-size: 16px;
      font-family: Audi-WideBold;
      color: #000000;
      line-height: 24px;
    }

    ._content {
      font-size: 14px;
      font-weight: 400;
      color: #000000;
      line-height: 20px;
      margin-top: 7px;
    }
  }

  ._box {
    padding: 0 16px 56px 16px;

    ._agent {
      padding-top: 24px;
      padding-bottom: 8px;
      width: 100%;
    }
    ._way {
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 16px 0;
      border-bottom: 1px #f2f2f2 solid;
      padding: 0 0 20px 0;
    }
    ._list {
      width: 100%;
      display: flex;
      flex-flow: column;

      ._title {
        width: 100%;
        font-size: 16px;
        font-family: Audi-WideBold;
        color: #000000;
        line-height: 24px;
      }

      ._time {
        font-size: 14px;
        color: #000000;
        line-height: 18px;
        margin-top: 7px;
      }

      ._li {
        height: 56px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        div {
          font-size: 16px;
          color: #333333;
          line-height: 18px;
        }
      }
    }

    .interval_line {
      margin-left: -16px;
      margin-right: -16px;
      height: 8px;
      background: #f2f2f2;
    }

    ._cancel-test {
      width: 80px;
      // padding: 16px 0;
      height: 30px;
      margin: 16px 0;
      font-size: 12px;
      color: #333;
      text-align: center;
      border: 1px solid #333;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      float: right;
    }

    .btn-delete-height {
      height: 90px;
    }
    .btn-delete-wrapper {
      margin: 16px;
    }
    .bottom_style {
      width: 100%;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: absolute;
      bottom: 0px;
      // padding: 16px;
      left: 0px;
    } 
  }
}
</style>
