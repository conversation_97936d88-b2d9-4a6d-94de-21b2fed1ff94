<template>
  <div class="bottom" v-show="!initTimeout">
    <!-- <template v-if="idx==='0'"> -->
    <div class="text">
      <p
        class="totlePrice"
        v-if="currentIndex == 0"
      >
        总价<span> ￥{{ selectCarInfo.price | formatPrice }}</span>
      </p>
      <p
        v-else
        class="totlePrice"
      >
        总价<span> ￥{{ currentPrice | formatPrice }}</span>
      </p>
      <!-- <p
        class="dueTime"
        v-if="selectCarInfo.deliveryTime && selectCarInfo.seriesName == 'A7L'"
        v-html="`预计交付时间：${selectCarInfo.deliveryTime || ''}`"
      > -->
      <!-- 预计交付时间：{{ selectCarInfo.deliveryTime }} -->
      <!-- </p> -->
    </div>
    <div
      class="button"
      @click="handleBtn(currentIndex)"
    >
      下一步
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Toast } from 'vant'
import { mapState, mapMutations, mapGetters } from 'vuex'
import wx from 'weixin-js-sdk'
import {
  getCcInfo, getV2CcInfo, updateCcInfo, autoSaveCarShoppingCart, getCcMeasure
} from '@/configratorApi/index'
import {
  getOtds
} from '@/api/api'
import {
  checkV2,
  callNative, getUrlParamObj, paramsStrict, checkType, delay, getLocationProvince
} from '@/utils'
import storage from '@/utils/storage'

Vue.use(Toast)

export default {
  inject: ['reload', 'checkLoginFn'],
  props: {
    idx: {
      type: String,
      default: '0'
    },
    currentIndex: {
      type: String,
      default: '0'
    },
    showLNRights: {
      type: Boolean
    }
  },
  data() {
    return {
      timeout: null,
      count: 0,
      show: false
    }
  },
  computed: {
    ...mapState([
      'env',
      'currentTabIndex',
      'selectCarInfo',
      'currentExColor',
      'currentModelHub',
      'currentInteriorChair',
      'currentSibColorInterieur',
      'currentInteriorEih',
      'currentOptionsList',
      'currentPrice',
      'measureConfigCodeList',
      'currentMeasureConfigCode',
      'showV2Popup',
      'currentV2popupItem',
      'privateOrderList',
      'initTimeout'
    ]),
    V2PopupMeasureConfigCodeList() {
      const arr = this.measureConfigCodeList.filter((e) => e.prCodes.length > 0)
      return arr
    },
    ...mapGetters(['getterCcid']),
    getPrice() {
      const {
        currentTabIndex,
        selectCarInfo,
        currentExColor,
        currentModelHub,
        currentInteriorChair,
        currentInteriorEih,
        currentSibColorInterieur,
        currentOptionsList
      } = this

      const obj = {
        currentTabIndex,
        selectCarInfo,
        currentExColor,
        currentModelHub,
        currentInteriorChair,
        currentInteriorEih,
        currentSibColorInterieur,
        currentOptionsList
      }
      return obj
    }
  },
  watch: {
    getPrice: {
      handler(next, old) {
        // return
        // 避免deliveryTime改变时一直循环下去，可能有更优的方式？
        // if (this.idx == 2) return
        let pams = Object.keys(next)
        pams = pams.filter((e) => e !== 'selectCarInfo')
        let selectCarInfopams = Object.keys(next.selectCarInfo)
        selectCarInfopams = selectCarInfopams.filter((e) => e !== 'deliveryTime' && e !== 'price')
        let flag = true
        for (let j = 0, m = pams.length; j < m; j++) {
          if (next[pams[j]] !== old[pams[j]]) {
            flag = false
            break
          }
        }
        if (flag) {
          for (let i = 0, l = selectCarInfopams.length; i < l; i++) {
            if (next.selectCarInfo[selectCarInfopams[i]] !== old.selectCarInfo[selectCarInfopams[i]]) {
              flag = false
              break
            }
          }
        }
        if (flag && this.count > 0) {
          this.count = 0
          return
        }
        this.count++
        const fun = () => {
          const {
            selectCarInfo, currentTabIndex,
            currentExColor,
            currentModelHub,
            currentInteriorChair,
            currentInteriorEih,
            currentSibColorInterieur,
            currentOptionsList
          } = next
          if ((this.$route.query.idx == 2 && this.currentIndex == 0) || (this.idx == 1 && !checkV2() && this.currentIndex == 0)) {
            return
          }
          let arr = []; let optionIds = []
          let idArr = []
          if (selectCarInfo.modelLineId && currentExColor.optionId && currentModelHub.optionId && currentInteriorEih.optionId && currentSibColorInterieur.sibInterieurId) {
            arr = []
            const measureBool = checkV2()
            const boo = (measureBool && currentTabIndex > 2) || !measureBool
            if ((measureBool && currentTabIndex > 0) || !measureBool) arr.push(currentExColor)
            if ((measureBool && currentTabIndex > 1) || !measureBool) arr.push(currentModelHub)
            if (boo) arr.push(currentInteriorEih)
            arr = arr.map((item) => item.optionId)
            if (boo && currentInteriorChair && currentInteriorChair.optionId) arr.push(currentInteriorChair.optionId)
            if (boo) arr.push(currentSibColorInterieur.sibOptionId)
            if (boo) arr.push(currentSibColorInterieur.interieurOptionId)
            if (currentOptionsList.length > 0) {
              currentOptionsList.forEach((e) => {
                if (e.optionId) {
                  optionIds.push(e.optionId)
                }
              })
              // optionIds = currentOptionsList.map((item) => item.optionId)
            }
            optionIds = optionIds.filter((e) => e)
            idArr = [...arr, ...optionIds]
            const { optionId } = this.$route.query
            if (optionId) idArr.push(optionId)
            this.$store.dispatch('getMeasurePriceComputeAction', idArr)
            this.$store.dispatch('getDeliveryTimeComputeAction', idArr)
          }
        }
        if (this.timeout) clearTimeout(this.timeout)
        this.timeout = setTimeout(() => {
          fun()
        }, 250)
      },
      deep: true
    }
  },
  methods: {
    handleBtnCopy(e) {
    },
    newVersion() {
      const line = this.selectCarInfo
      if (!line.styleId) {
        return this.$router.go(-1)
      }
      const {
        orderStatus, orderId, ccid, shoppingCartId, from, idx
      } = this.$route.query
      const obj = {
        orderStatus, orderId, ccid, shoppingCartId, idx
      }
      this.$router.push(
        {
          path: '/carVersion',
          query: {
            ...obj,
            styleId: line.styleId
          }
        }
      )
    },
    /** 配置器下一步按钮事件 */
    handleBtn(index) {
      const { idx, modelLineCode } = this.$route.query
      // q6 齐云 黑武士/影武士(7座/6座)
      const visiCar = idx === '2' && 'G6ICAY005,G6ICAY004,G6ICAY003,G6ICAY002'.includes(modelLineCode)
      this.show = visiCar
      if (visiCar && index == 6) {
        this.$parent.checkMeasureConfig()
      }

      /**
       * currentindex === 0 车型页
       * 有两种case 进入 选择车型页
       * 1. q6 或 a7l
       * 2. q5e 高定
       */
      if (this.currentIndex === '0') {
        if (this.idx !== '1') {
          return this.newVersion()
        }
        if (this.idx === '1' && this.$storage.getPlus('semi-definite') == '私人高定') {
          return this.newVersion()
        }
      }

      //
      if (this.V2PopupMeasureConfigCodeList.length > 0 && checkV2() && this.currentIndex == 4) {
        if (this.currentV2popupItem > -1) {
          const tmp = this.measureConfigCodeList.filter((e) => e.prCodes.length > 0)
          const tmplist = tmp[this.currentV2popupItem]?.prCodes.map((e) => this.privateOrderList.find((e1) => e1.optionCode === e))
          this.$store.commit('setCurrentOptionsList', tmplist)
          this.$store.commit('setCurrentMeasureConfigCode', tmp[this.currentV2popupItem])
          // this.$store.commit('setShowV2Popup', false)
          // this.$store.commit('setTitle',  `选择${this.$storage.getPlus('semi-definite')}`)
          // document.title = '选择私人定制'
        } else {
          const condition = this.measureConfigCodeList.find((e) => e.prCodes.length == 0)
          if (!condition) {
            Toast({
              type: 'fail',
              message: '请选择一组装备',
              icon: require('../../assets/img/error.png')
            })
            return
          }
          this.$store.commit('setCurrentMeasureConfigCode', condition)
        }
      }
      const {
        selectCarInfo,
        currentExColor,
        currentModelHub,
        currentInteriorChair,
        currentInteriorEih,
        currentSibColorInterieur,
        currentOptionsList,
        currentPrice,
        currentMeasureConfigCode
      } = this

      let arr = this.currentOptionsList && this.currentOptionsList.map((res) => res.optionName)

      arr = arr.join('/')

      const doms = document.getElementsByClassName('el-tabs__item')
      if (this.currentIndex == 0) {
        this.$emit('setTab', 1 + parseInt(index))
        let interiorType = ''
        if (this.currentInteriorChair && this.currentInteriorChair.optionName) {
          interiorType += (`${this.currentInteriorChair.optionName}/`)
        }
        this.$sensors.track('clickToSelectTheColor', {
          page_name: '选择车型',
          model_name: selectCarInfo.modelLineName,
          price: currentPrice,
          cars_appearance: currentExColor.optionName,
          hub_model: currentModelHub.optionName,
          interior_type: `${interiorType}${currentSibColorInterieur?.sibName}/${currentSibColorInterieur.interieurName}/${currentInteriorEih.optionName}`,
          personal_tailor: arr
        })
      } else {
        let interiorType = ''
        if (this.currentInteriorChair && this.currentInteriorChair.optionName) {
          interiorType += (`${this.currentInteriorChair.optionName}/`)
        }
        this.$sensors.track('bookNow', {
          page_name: '车型选择',
          model_name: selectCarInfo.modelLineName,
          price: currentPrice,
          cars_appearance: currentExColor.optionName,
          hub_model: currentModelHub.optionName,
          interior_type: `${interiorType}${currentSibColorInterieur?.sibName}/${currentSibColorInterieur.interieurName}/${currentInteriorEih.optionName}`,
          personal_tailor: arr || ''
        })
        let arr = []
        const optionIds = []

        if (selectCarInfo.modelLineId && currentExColor.optionId && currentModelHub.optionId && currentInteriorEih?.optionId && currentSibColorInterieur?.sibInterieurId) {
          arr = [
            currentExColor,
            currentModelHub,
            currentInteriorEih
          ]
          arr = arr.map((item) => item.optionId)
          if (currentInteriorChair && currentInteriorChair.optionId) {
            arr.push(currentInteriorChair.optionId)
          }
          arr.push(currentSibColorInterieur.sibOptionId)
          arr.push(currentSibColorInterieur.interieurOptionId)
          currentOptionsList.length > 0 && currentOptionsList.forEach((e) => {
            if (e.optionId) optionIds.push(e.optionId)
          })

          const { optionId } = this.$route.query
          if (optionId && !optionIds.find((e) => optionId == e)) optionIds.push(optionId)

          // showLNRights 是否展示限量号tab
          if ((this.showLNRights && doms.length === (parseInt(index) + 1)) || (!this.showLNRights && doms.length === (parseInt(index)))) {
            let { ccid } = this.$route.query
            const { orderStatus, orderId } = this.$route.query
            ccid = ccid || this.getterCcid
            let request
            // currentMeasureConfigCode getV2CcInfo
            if (checkV2()) {
              const entryPoint = this.$storage.getPlus('entryPoint')
              if (orderStatus && ccid) {
                // 半定制修改配置单
                const packetEquityId = optionId || ''
                request = getCcMeasure(currentMeasureConfigCode.measureId, ccid, packetEquityId)
              } else {
                // 半定生成配置单
                const packetEquityId = optionId || ''
                request = getV2CcInfo(currentMeasureConfigCode.measureId, entryPoint, packetEquityId)
              }
            } else {
              if (orderStatus && ccid) {
                request = updateCcInfo(ccid, selectCarInfo.modelLineId, [...arr, ...optionIds], currentSibColorInterieur.sibInterieurId)
              } else {
                if (this.$route.query.idx != 2) {
                  const entryPoint = this.$storage.getPlus('entryPoint')
                  request = getCcInfo(selectCarInfo.modelLineId, [...arr, ...optionIds], currentSibColorInterieur.sibInterieurId, entryPoint)
                } else {
                  const entryPoint = this.env == 'minip' ? 'MINIP_PERSONAL' : 'ONEAPP_PERSONAL'
                  request = getCcInfo(selectCarInfo.modelLineId, [...arr, ...optionIds], currentSibColorInterieur.sibInterieurId, entryPoint)
                }
              }
            }
            let seriesId = ''
            if (this.idx === '0') {
              seriesId = 'ADA7'
            } else if (this.idx === '1') {
              seriesId = 'G4'
            } else if (this.idx == '2') {
              seriesId = 'G6'
            }

            storage.setPlus('ccproData', {
              modelLineId: selectCarInfo.modelLineId,
              sibInterieurId: currentSibColorInterieur.sibInterieurId,
              optionIds: [...arr, ...optionIds]
            })
            // let entryPoint = ''
            // if (this.$route.query.idx != 2) {
            //   entryPoint = this.$storage.getPlus('entryPoint')
            // } else {
            //   entryPoint = this.env == 'minip' ? 'MINIP_PERSONAL' : 'ONEAPP_PERSONAL'
            // }

            // autoSaveCarShoppingCart 自动加到购物车第一个
            Promise.all([
              getOtds({ seriesId: seriesId }),
              request
            ]).then((res) => {
              const skuId = res[0].data.code === '00' && res[0].data.data.prodSkuId
              const ccInfo = res[1].data.code === '00' && res[1].data.data
              this.$store.commit('setCcInfo', ccInfo)
              if (skuId && ccInfo && ccInfo.ccId) {
                this.$sensors.track('selectPersonalV2', {
                  model_name: this.selectCarInfo.modelLineName,
                  modelLineId: this.selectCarInfo.modelLineId,
                  selected: this.currentV2popupItem > -1,
                  ccid: ccInfo.ccId,
                  env: this.env
                })

                if (orderStatus === '30' || orderStatus === '00') {
                  this.$router.push({
                    path: '/order/money-detail',
                    query: { orderId }
                  })
                } else {
                  const { dealerCode, shoppingCartId, idx } = this.$route.query
                  const { env } = getUrlParamObj()
                  const pubVis = !this.checkLoginFn('车型') && env === 'minip'
                  pubVis && this.openWebViewMinip({
                    ccInfo, dealerCode, idx, skuId
                  })
                  !pubVis && this.$router.push({
                    path: '/quotation',
                    query: {
                      ccid: ccInfo.ccId, skuid: skuId, orderStatus, dealerCode, shoppingCartId, idx
                    }
                  })
                }
              }
            })
          } else {
            doms[parseInt(index) + 1]?.scrollIntoView()
            if (!this.showLNRights && index === '4') {
              this.$emit('setTab', 2 + parseInt(index))
            } else {
              this.$emit('setTab', 1 + parseInt(index))
            }
          }
        } else {
          if (this.idx == 2 || (this.idx == 1 && this.$storage.getPlus('semi-definite') == '私人高定')) {
            doms[parseInt(index) + 1]?.scrollIntoView()
            if (!this.showLNRights && index === '4') {
              this.$emit('setTab', 2 + parseInt(index))
            } else {
              this.$emit('setTab', 1 + parseInt(index))
            }
          }
        }
        // this.$store.commit('setOptionsSelected', this.$store.state.currentTabIndex)
      }
    },
    openWebViewMinip(e) {
      const { env } = getUrlParamObj()
      const {
        ccInfo, dealerCode, idx, skuId
      } = e
      const { origin, pathname } = window.location
      const url = encodeURIComponent(`${origin}${pathname}#/quotation?ccid=${ccInfo.ccId}`)
      env === 'minip' && wx.miniProgram.navigateTo({ url: `/pages/web/index?idx=${idx}&url=${url}&skuid=${skuId}&dealerCode=${dealerCode}` })
    }
  }
}
</script>

<style lang="less" scoped>
.bottom {
  position: fixed;
  bottom: 0;
  height: 106px;
  width: 100vw;
  z-index: 2;
  background: #ffffff;
  border: 1px slid red;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);

  .text {
    color: #999;
    line-height: 12px;
    font-size: 12px;
    padding-left: 16px;
    padding-top: 10px;

    span {
      color: black;
      font-family: 'Audi-WideBold';
      font-size: 16px;
    }

    .totlePrice {
      line-height: 21px;
    }

    .dueTime {
      padding: 8px 16px 0 0;
      box-sizing: border-box;
      white-space: pre-wrap;
    }
  }

  .button {
    position: absolute;
    top: 12px;
    right: 16px;
    width: 136px;
    height: 44px;
    background: #000;
    color: white;
    font-size: 16px;
    text-align: center;
    line-height: 44px;
  }

  .yuding {
    position: absolute;
    top: 28px;
    left: 16px;
    right: 16px;
    height: 50px;
    background: #000;
    color: white;
    font-size: 16px;
    text-align: center;
    line-height: 50px;
  }
}
</style>
