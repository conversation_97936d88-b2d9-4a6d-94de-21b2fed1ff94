<template>
  <div class="footer-wrapper">
    <div class="footer c-footer-shadow c-flex-between c-font14">
      <!-- <div class="delivery-text-wrapper c-font14" v-show="!popupVisible && carIdx !== 3">
        {{ footerDesc.desc }}
      </div> -->

      <div class="left">
        <div class="price-wrapper c-bold c-font16" @click="showPriceDetail">
          ￥{{ totalPrice | formatPrice }}
          <van-icon name="arrow-up" class="" />
        </div>

        <div class="deposit-wrapper  c-font14 c-lh24">
          <!--          定金: ¥ <span :class="{ 'line-through':isCouponValid }">{{ currentEarnestMoney | formatPrice }}</span> <span v-if="isCouponValid" class="blacks">￥0</span>-->
          定金: ¥ <span :class="{ 'line-through':isCouponValid }">{{ currentEarnestMoney | formatPrice }}</span> <span v-if="isCouponValid" class="blacks">￥0</span>
          <!-- 定金: ¥ {{ '5000' | formatPrice }} -->
        </div>
      </div>

      <div class="right">
        <div class="next-btn" @click="commonNextPage">
          下一步
        </div>
      </div>
    </div>

    <!-- 价格明细弹窗-->
    <van-popup v-model="popupVisible" position="bottom" closeable @close="closePopup">
      <div class="pop-wrapper c-font12">

        <div class="c-bold c-font16 price-title">价格明细</div>
        <div>
          <PriceList />
        </div>

        <div class="line price" v-if="equityPrice !== 0">
          <div class="c-lh20">预售权益</div>
          <div class="">{{ equityPrice | finalFormatPriceDescPrefixMinus }}</div>
        </div>
        <div class="line price">
          <div class="c-lh20">总价</div>
          <div class="c-bold">￥{{ totalPrice | formatPrice }}</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from "vue";
import { DropdownItem, DropdownMenu, Popup } from "vant";
import { mapGetters, mapState } from "vuex";
import PriceList from "./priceList.vue";

Vue.use(DropdownMenu);
Vue.use(DropdownItem);
Vue.use(Popup);

export default {
  components: { PriceList },
  data() {
    return {
      popupVisible: false
    };
  },
  computed: {
    ...mapGetters(["currentEarnestMoney", "currentCarType"]),
    ...mapState({
      // footerDesc: (state) => state.configration.footerDesc,
      userCoupon: (state) => state.configration.userCoupon,
      totalPrice: (state) => state.configration.totalPrice,
      equityPrice: (state) => state.configration.equityPrice,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentVersion: (state) => state.configration.currentVersion,
      carIdx: (state) => state.configration.carIdx
    }),
    isCouponValid() {
      if (!this.userCoupon) {
        return false;
      }
      // 获取两个字段的值
      const { activityCode, mpEquityNo } = this.userCoupon;

      // 检查两个字段是否都存在且有实际值（非空字符串、非null、非undefined）
      return Boolean(
        activityCode !== undefined &&
        activityCode !== null &&
        activityCode !== "" &&
        mpEquityNo !== undefined &&
        mpEquityNo !== null &&
        mpEquityNo !== ""
      );
    }
  },
  props: {},
  created() {
    // this.active  = this.props.active
  },
  mounted() {
    console.log("%c A5L totalPrice:", "font-size:16px;color:green;", this.totalPrice);
  },
  methods: {
    // 显示隐藏价格弹窗
    showPriceDetail() {
      this.popupVisible = !this.popupVisible;

      // 埋点
      const operation = this.popupVisible ? "展开价格明细" : "收起价格明细";
      this.clickPopupSensors(operation);
    },

    // 下一步的一些公共逻辑
    commonNextPage() {
      this.popupVisible = false;
      this.$emit("click");
    },
    // 收起的埋点
    closePopup() {
      this.clickPopupSensors("收起价格明细");
    },

    // 埋点
    clickPopupSensors(operationType) {
      const {
        engine, customSeriesName
      } = this.currentModelLineData;
      const param = {
        source_module: "H5",
        car_series: "A5L",
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: "定制交付", // 快速交付|定制交付
        operation_type: operationType
      };

      // console.log(param)
      this.$sensors.track("CC_CarConfiguration_Operate", param);
    }
  }
};
</script>

<style scoped lang="less">
@import "../../../assets/style/common.less";

@HEIGHT: 100px;
.footer {
  background: #fff;
  position: fixed;
  z-index: 20;
  bottom: 0;
  left: 0;
  width: 100%;
  height: @HEIGHT;
  padding: 5px 13px 0 13px;
  box-sizing: border-box;

  .left {
    height: min-content;

    .price-wrapper {
      margin-top: 10px;
    }

    .deposit-wrapper {
      color: #999999;
      margin-top: 4px;
    }
  }

  .right {
    border: 1px solid;
    height: min-content;

    > .next-btn {
      &:extend(.c-font16);
      width: 140px;
      line-height: 56px;
      background-color: #000;
      color: #fff;
      text-align: center;
    }
  }

  .delivery-text-wrapper {
    position: absolute;
    left: 0;
    bottom: 100%;
    width: 100%;
    line-height: 32px;
    background-color: #F2F2F2;
    padding-left: 16px;
    box-sizing: border-box;
  }
}


.pop-wrapper {
  padding: 16px 16px;

  > .price-title {
    line-height: 26px;
  }
}


.line {
  .c-flex-between;
}

/deep/ .van-icon-arrow-up {
  &::before {
    vertical-align: middle;
  }
}

/deep/ .van-overlay {
  z-index: 10 !important;
}

.van-popup {
  z-index: 15 !important;
  bottom: @HEIGHT;
}

/deep/ .van-popup__close-icon--top-right {
  color: #000;
}

.line-through {
  text-decoration: line-through;
  color: gray;
}

.blacks {
  color: #000;
}
</style>
