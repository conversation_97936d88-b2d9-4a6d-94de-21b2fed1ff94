<template>
  <div class="container">
    <van-radio-group v-model="radio">
      <van-cell-group>
        <van-cell style="height: 40px;align-items: center;" title="无需服务" clickable @click="radio = '1'">
          <template #right-icon>
            <img
              class="img-icon"
              :src="radio === '1' ? activeIcon : inactiveIcon"
            />
          </template>
        </van-cell>
        <van-cell style="height: 40px;align-items: center;" title="日期时间填错" clickable @click="radio = '2'">
          <template #right-icon>
            <img
              class="img-icon"
              :src="radio === '2' ? activeIcon : inactiveIcon"
            />
          </template>
        </van-cell>
         <van-cell style="height: 40px;align-items: center;" title="地址电话填错" clickable @click="radio = '3'">
          <template #right-icon>
            <img
              class="img-icon"
              :src="radio === '3' ? activeIcon : inactiveIcon"
            />
          </template>
        </van-cell>
         <van-cell style="height: 40px;align-items: center;" title="没用卡券" clickable @click="radio = '4'">
          <template #right-icon>
            <img
              class="img-icon"
              :src="radio === '4' ? activeIcon : inactiveIcon"
            />
          </template>
        </van-cell>
        <van-cell style="height: 40px;align-items: center;" title="其他" clickable @click="radio = '5'">
          <template #right-icon>
            <img
              class="img-icon"
              :src="radio === '5' ? activeIcon : inactiveIcon"
            />
          </template>
        </van-cell>
      </van-cell-group>
    </van-radio-group>
    <div class="chat-input">
      <van-field
        class="textarea"
        type="textarea"
        rows="4"
        placeholder="取消原因(选填)"
        v-model="inputRemark"
        maxlength="70"
      />
    </div>

    <div class="bottom_style">
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onSubmit"
          :text="'确认取消'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { postCancelDeliverCarOrder } from "@/api/api";
import { callNative } from "@/utils";

import { RadioGroup, Radio, Field, Cell, CellGroup } from "vant";
import AudiButton from "@/components/audi-button";

Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(Field).use(Cell).use(CellGroup);
export default {
  components: {
    AudiButton,
  },
  data() {
    return {
      orderId: "",
      type:1,
      radio: "1",
      inputRemark: "",
      activeIcon: require("../../assets/img/radio_checked.png"),
      inactiveIcon: require("../../assets/img/radio_normal.png"),
      isShowDialog:false
    };
  },

  async mounted() {
    const { orderId ,type,isShowDialog} = this.$route.query;
    this.orderId = orderId;
    this.type = type
    this.isShowDialog = isShowDialog
  },

  methods: {
    // 确认
   async onSubmit() {
     if(this.isShowDialog){
        // 调用APP Dialog
        callNative("popup", {
          type: "alert",
          alertparams: {
            title: "",
            desc: "是否确定取消订单？",
            actions: [
              {
                type: "fill",
                title: "确定",
              },
              {
                type: "stroke",
                title: "取消",
              },
            ],
          },
        }).then((data) => {
          if (data.type === "fill") {
            // 点击确定
            this.cancelOrder()
          }
        });
     }else{
       this.cancelOrder()
     }
    },
   async cancelOrder(){
      
      let serviceName = "";
      switch (this.radio) {
        case "1":
          serviceName = "无需服务";
          break;
        case "2":
          serviceName = "日期时间填错";
          break;
        case "3":
          serviceName = "地址电话填错";
          break;
        case "4":
          serviceName = "没用卡券";
          break;
        case "5":
          serviceName = "其他";
          break;
        default:
          break;
      }
      if(this.inputRemark){
        serviceName += "("+this.inputRemark+")"
      }
      console.log("serviceName", serviceName);
      this.$store.commit("showLoading");
      const { data } = await postCancelDeliverCarOrder({
        orderId: this.orderId,
        cancelReason: serviceName ,
      });
      this.$store.commit("hideLoading");
      if (data.code === "200") {
	      this.$router.push({
          path: "/aftersales/service-success",
          query: { type: this.type },
        });
      } else {
        callNative("toast", { type: "fail", message: data.message });
      }
    }
  },
};
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.container {
  padding-top: 16px;
}
.img-icon {
  display: flex;
  
  
  height: 20px;
  width: 20px;
}

.chat-input {
  //   width: 100%;
  display: flex;
  padding: 16px;
  .textarea {
    width: 100%;
    height: 140px;
    background: #ffffff;
    border: 1px solid #e5e5e5;
    padding: 8px;
    font-size: 14px;
    color: #000000;
    border-radius: 0;
    -webkit-appearance: none;
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
}
.btn-delete-wrapper {
  margin: 0 16px;
}
</style>
