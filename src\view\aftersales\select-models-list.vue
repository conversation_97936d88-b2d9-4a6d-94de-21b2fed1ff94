<template>
  <div class="container">
    <div
      class="item-wrapper"
      v-for="(item, idx) in dealerList"
      :key="idx"
      @click="chooseDealer(item)"
    >
      <div class="content-wrapper flex1">
        <div class="c-font16">
          {{ item.modelLineName }}
        </div>
        <div style="margin-top: 14px" />
        <div class="c-font16">
          {{ " " }}
        </div>

        <div style="margin-top: 15px" />
      </div>

      <div class="navgation-wrapper">
        <img :src="(ossUrl + item.imageUrl) | audiwebp " alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import { getCustomSeries2 } from "@/api/api";
import {
   getSwitchModellineList,
} from '@/configratorApi'
import baseUrl from "@/config/url";
import storage from "../../utils/storage";

export default {
  data() {
    return {
      ossUrl: baseUrl.BaseOssHost,
      dealerList: [],
      seriesCode:'',
    };
  },

  async mounted() {
    var ownerVinInfo = storage.get('ownerVinInfo') || "{}"
	  this.seriesCode = JSON.parse(ownerVinInfo).seriesCode	
    this.getModelLine();
  },

  methods: {
    async getModelLine() {
      let customSeriesId = "64fe9f8e-050d-4843-bffc-c47675a9956a";
      const { data } = await getCustomSeries2({});
      const item = data.data.find((i) => i.seriesCode === this.seriesCode)

      customSeriesId = item.customSeriesId;
      // const seriesCode = item.seriesCode; // 车系code
      // const seriesName = item.customSeriesName; // 车系名称

      await getSwitchModellineList(customSeriesId).then((res) => {
        if (res.status === 200 && res.data.code === "00") {
          if (data.data.length > 0) {
            res.data.data.forEach((element) => {
              element.seriesCode = item.seriesCode; // 车型code在提交订单是需要，
              element.seriesName = item.customSeriesName;
            });
          }
          this.dealerList = res.data.data;
        }
      });
    },

    chooseDealer(item) {      
      if (this.seriesCode || this.seriesCode !== item.seriesCode) {
        var ownerVinInfo = storage.get('ownerVinInfo') || "{}"
        storage.set('ownerVinInfo', JSON.stringify({
            vin: JSON.parse(ownerVinInfo).vin,
            seriesCode: JSON.parse(ownerVinInfo).seriesCode,
            modelCode: item.modelCode || '',
            modelLineName: item.modelLineName || '',
            seriesName: JSON.parse(ownerVinInfo).seriesName
        }))
      }
      this.$router.back(-1);
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.container {
  padding: 0 16px;
}

.flex1 {
  flex: 1;
}

.item-wrapper {
  .c-flex-between;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
  padding: 16px;
  margin-top: 16px;
}

.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  // margin-left: 15px;
  padding: 5px 0;
}
.navgation-wrapper {
  text-align: center;
  img {
    width: 142px;
    height: 80px;
  }
}
</style>
