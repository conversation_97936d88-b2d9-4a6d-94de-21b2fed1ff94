<template>
  <div class="configration">
    <div class="header">
      <el-tabs v-if="visible" v-model="currentIndex" @tab-click="clickTab" :before-leave="handleBeforeLeave">
        <el-tab-pane label="车型" name="0">
          <model-line :current-index="currentIndex" :ccid="ccid" />
        </el-tab-pane>
        <el-tab-pane :disabled="disabledTab"  label="颜色" name="1">
          <div>
            <exterior v-show="showTabPane" ref="exteriorRef" :current-index="currentIndex" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="轮毂" name="2">
          <Hub  v-if="showTabPane"  :current-index="currentIndex" />
        </el-tab-pane>
        <el-tab-pane label="内饰" name="3">
          <Interior  v-if="showTabPane"  :current-index="currentIndex" />
        </el-tab-pane>
        <el-tab-pane
          :label="$storage.getPlus('semi-definite') == '个性定制' && $route.query.definedCar != 1 ? '个性定制' : '私人定制'"
          name="4">
          <private-order v-if="$storage.getPlus('semi-definite') == '私人高定'" :current-index="currentIndex" />
          <!-- <private-order-copy v-if="checkMeasure && idx == 2" :current-index="currentIndex" /> -->
            
          <!--  [{prCodes: ['6NQ', '6NQ', '6NQ', '6NQ']}]  -->
          <!-- V2PopupMeasureConfigCodeList  showV2Popup-->
          <div class="v2popupcss" v-if="noHasV2List">
            <div class="v2popup_title">结合专业推荐及供货效率，您可以选择以下装备组合：</div>
            <div class="v2popup_item" v-for="(item,index) in V2PopupMeasureConfigCodeList" :key="item.measureId">
              <div style="position: relative" @click="setCurrentV2popupItem(index)">
                <img v-if="item.prCodes[0]" :src="BaseConfigrationOssHost + getPersonal(item.prCodes[0]).imageUrl | imgFix(640, true)"
                  alt="">
                <div class="v2popup_head" style="position: absolute;bottom: 0;width: 100%;">
                  <div class="head_bg" style="margin-left: 10px;" :class="[currentV2popupItem===index?'current':'']">
                  </div>
                  <div class="head_price" style="color: #FFFFFF;padding-right: 10px;">
                    {{getPersonalAllPrice(item.prCodes) | finalFormatPrice}}</div>
                </div>
              </div>
              <div class="v2popup_main" style="padding: 8px">
                <div class="main_item" @click="handelDetail(item.prCodes)" v-for="(item1,index1) in item.prCodes"
                  :key="'waha'+index1" v-show="index1 < indexPackage">
                  <div class="main_item_left">· {{getPersonal(item1).optionName}}</div>
                  <div class="main_item_right">{{getPersonal(item1).price | finalFormatPrice}}</div>
                </div>
                <div style="text-align: center;padding: 5px;" v-if="item.prCodes.length > 3">
                  <van-icon v-if="!flag" @click="handleMore(item.prCodes, 0)" name="arrow-down" />
                  <van-icon v-if="flag" @click="handleMore(item.prCodes, 1)" name="arrow-up" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane name="5" label="限量号" v-if="showLNRights">
          <span slot="label">
            <span>{{ LNRightTitle }}</span>
            <div class="tip" @click.stop="showTip" />
          </span>
          <limit-number />
        </el-tab-pane>
        <el-tab-pane :label="firstEquityTitle" name="6" v-if="showFirstEquity">
          <equity-detail v-if="currentIndex > 4" :car-model-id="selectCarInfo.modelLineCode" father="configration" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <option-popup v-if="showOptionPopup" />
    <option-popup-plus @handleEventFn="handleEventFn" v-if="showOptionPopupPlus.bool" />

    <!-- 限量号玩法 -->
    <LimitNumberDesc
      v-show="limitNumberDescDialogVisible"
      @close="limitNumberDescDialogVisible = false"
    />

    <div class="nopersonalpopup"
      v-show="hasV2ListDialog">
      <div class="main">
        <div class="content">基于您当前选择的车型颜色等信息，该车辆为标准配置，可直接下单。如需选择其他装备，可尝试选择其他颜色及内饰信息。</div>
        <div class="button1" @click="toBaojia">生成报价单</div>
        <div class="button2" @click="toXiugai">修改配置</div>
      </div>
    </div>
    <Bottom :idx="idx" ref="bottomdom" @setTab="setTab" :show-l-n-rights="showLNRights" :current-index="currentIndex" />
    <network @reload="networkReload()" />
  </div>
</template>

<script>
import { mapState, mapMutations, mapGetters } from 'vuex'
import { Base64 } from 'js-base64'
import url from '@/config/url'
import ModelLine from './modelline.vue'
import Exterior from './exterior'
import Interior from './interior'
import Hub from './hub'
import PrivateOrder from './private-order.vue'
// import PrivateOrderCopy from './private-order-copy.vue'
import LimitNumber from './limitNumber/limit-number.vue'
import Bottom from './bottom'
import OptionPopup from '@/components/option-popup.vue'
import OptionPopupPlus from '@/components/option-popup-plus.vue'
import EquityDetail from '@/view/order/equity-detail'
import { getAPITimeOutTesting, getUserRightsByCarModelId } from '@/api/api'
import { getQueryParam, callNative, checkV2 } from '@/utils/index'
import { getPacketEquity, getByStyle, getStyleList, getCarList, getRecommendCar } from '@/configratorApi/index'
import { defPackageData6, defPackageData7 } from "./data.js";
import LimitNumberDesc from './limitNumber/limitNumberDesc.vue'
import network from '@/components/network.vue';
import { Toast } from "vant"

export default {
  name: 'Configration',
  components: {
    ModelLine,
    Bottom,
    Exterior,
    Hub,
    Interior,
    PrivateOrder,
    OptionPopup,
    OptionPopupPlus,
    EquityDetail,
    LimitNumber,
    LimitNumberDesc,
    network
  },
  data() {
    return {
      // checkMeasure: '',
      fromPath: '',
      visible: true,
      // visiCar: false,
      indexPackage: 3,
      flag: false,
      // isCC0_5: false,
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      customSeriesId: '', // 当前车辆的customSeriesId
      currentIndex: getQueryParam('tabIndex') || '0',
      idx: '0', // 0:A7L  1:Q5
      ccid: '',
      limitNumberDescDialogVisible: false,
      // limitnumberdesc: '',
      // limitnumberimage: '',
      showLNRights: false,
      LNRightTitle: '限量号',
      showFirstEquity: false,
      firstEquityTitle: '先行权益',
      from: '',
      oldActiveName: '',
      disabledTab: false,
      showTabPane: true,
      reactiveCheckV2: false, // 设置为响应式查询半定
    }
  },
  watch: {
    currentIndex: {
      handler(val) {
        this.limitNumberDescDialogVisible = false
        // this.$store.commit('setScrollTop', 0)
        let title = ''
        switch (val) {
          case '0':
            const { orderStatus, orderId, ccid, shoppingCartId, from } = this.$route.query
            const obj = { orderStatus, orderId, ccid, shoppingCartId }
            if (['hot', 'hotRecom'].includes(from)) {
              this.$router.push({
              path: '/hot-recommended',
                query: {
                  ...obj,
                  idx: this.idx
                }
              })
              return
            }
            title = '选择车型'
            break
          case '1':
            title = '选择颜色'
            break
          case '2':
            title = '选择轮毂'
            break
          case '3':
            title = '选择内饰'
            break
          case '4':
            // optionName "DCC自适应动态悬架"
            const PDE = this.privateOrderList.find(e => e.optionCode === "PDE");
            if(PDE && this.$store.state.selectCarInfo.modelLineName.includes("观云型") && !checkV2()) {
              if(PDE?.cancel) return
              this.$store.commit('setCurrentOptionsList', [...this.$store.state.currentOptionsList, PDE])
            }

            // if (!(this.privateOrderList && this.privateOrderList.length > 0) && checkV2()) {
            //   this.showNopersonalpopup = true
            // }
            title = checkV2() ? '选择个性定制' : '选择私人定制'
            break
          case '5':
            title = this.LNRightTitle
            break
          case '6':
            title = this.firstEquityTitle
            break
          default:
            title = '选择车型'
            break
        }
        this.$store.commit('setTitle', title)
      },
      immediate: true
    },

    'selectCarInfo.modelLineId': {
      handler(next) {
        // if (this.idx == 2) { return }
        this.getLimitnumberAndQuanyiData()
        this.getFirstEquityData()
        this.$store.commit('setCurrentOptionsList', [])
      }
    }
  },

  computed: {
    ...mapGetters({
      currentCustomSeriesId: 'currentCustomSeriesId'
    }),
    ...mapState([
      'env',
      'backFromOptionDetail',
      'showOptionPopup',
      'showOptionPopupPlus',
      'modelLineList',
      'ccConfigration',
      'selectCarInfo',
      'currentExColor',
      'currentModelHub',
      'currentInteriorChair',
      'currentInteriorEih',
      'currentSibColorInterieur',
      'currentOptionsList',
      'currentPrice',
      'measureConfigCodeList',
      'showV2Popup',
      'privateOrderList',
      'allPrivateOrderList',
      'currentV2popupItem',
      'nowTabIndex'
    ]),
    V2PopupMeasureConfigCodeList() {
      const arr = this.measureConfigCodeList.filter((e) => e.prCodes.length > 0)
      return arr
    },
    hasV2ListDialog() {
      return this.currentIndex === '4' && this.reactiveCheckV2 && this.V2PopupMeasureConfigCodeList.length === 0
    },
    noHasV2List() {
      return this.V2PopupMeasureConfigCodeList.length > 0 && this.reactiveCheckV2
    },
  },
  inject: ['reload'],
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.path == '/optiondetail') {
        vm.fromPath = from.path
        vm.$store.commit("setFromPath", from.path)
        vm.currentIndex = 4
      }
    })
  },
  async activated() {
    this.activateData()
  },

  async created() {
    const { idx, definedCar, customSeriesId } = this.$route.query
    // if ((idx == 1 && definedCar == 0) || idx == 1) { //q5e
    //   // this.$storage.setPlus('semi-definite', '个性定制')
    //   let minip = this.$route.query.env == 'minip'
    //   // this.$storage.setPlus('entryPoint', minip ? 'MINIP_MEASURE' : 'ONEAPP_MEASURE')
    // }
    if (idx === '2' && definedCar === '1') { // q6
      this.$storage.setPlus('semi-definite', '私人高定')
    }
    this.customSeriesId = customSeriesId
    const ccid = getQueryParam('ccid')
    if (ccid) {
      this.ccid = ccid
      await this.$store.dispatch('getCcConfigrationAction', ccid)
    }

    const fnVis = () => {
      let b = false
      if (idx !== '2' || (this.idx == 1 && checkV2())) {
        b = true
      } else {
        b = false
        if (this.currentIndex != 0) {
          b = true
        }else {
          b = false
          if (this.currentIndex != 0) {
            b = true
        }
      }
      return b
    }
      return b
    }

    this.showTabPane = fnVis()
  },
  mounted() {
    this.$store.commit('updateReserveCard', false)
    this.$store.commit('updateReserveCardInfo', {})
    this.$store.commit('updateBestRecommandDealerCode', '')
    // this.isCC0_5 = this.$storage.getPlus('semi-definite') == '个性定制' &&  this.privateOrderList?.length > 0
  },

  methods: {
    ...mapMutations(['setSelectCarIfo', 'setLineList']),
    async getByStyleFn(customSeriesId) {
      let { styleId, modelLineId, from } = this.$route.query
      
      this.styleId = styleId
      // type 1 高  2半  3混
      let type = checkV2() ? 2 : 1
      let p = { styleId, type }
      let res = {}
      if (from.includes('carVersion')) res = await getByStyle(p)
      if (from.includes('hotRecom') && customSeriesId) res = await getRecommendCar(customSeriesId)
      let versionData = res.data.data
    
      if (!versionData.length) return
      let line = versionData.find(e => e.modelLineId == modelLineId)
      let seriesName = {
        '49': 'A7L',
        'G4': 'Q5E',
        'G6': 'Q6',
      }[line.customSeriesCode]
      line && this.$store.commit('setSelectCarIfo', {
        // ...line,
        modelYear: line.modelYear || '',
        modelLineCode: line.modelLineCode, 
        modelLineId: line.modelLineId,
        modelLineName: line.modelLineName,
        seriesName: seriesName || '',
        seriesCode: line.customSeriesCode,
        deliveryTime: line.deliveryTime || '',
        price: line.price
      })
      try {
        // 查询库存,暂未用
        // await this.$store.dispatch('measureQueryBefore')
        if (checkV2()) {
          await this.$store.dispatch('measureQuery', { step: '1' })
        } else {
          this.$store.dispatch('getExteriorList')
          this.$store.dispatch('getModelHubList')
          this.$store.dispatch('getAllInterior')
          this.$store.dispatch('getPrivateOrderList', { idx: this.idx })
          if(this.idx == 1 && !checkV2()) this.getFirstEquityData()
        }
        this.$store.commit('hideLoading')
        // this.$store.commit("setOptionsSelected", 1)
      } catch (error) {
        console.log("数据更新失败.....");
      }
    },

    /**
     * getStyleList  车型的数据,结构参考:https://jsonhero.io/j/XdAEtGK63gwu
     * a7l+ q6 + q5e高定
     */
    async getStyleListFn() {
      await this.$store.dispatch('getCurrentCarInfo')
      const customSeriesId = this.currentCustomSeriesId
      const { from } = this.$route.query
      // carVersion：车型列表 到 版本选择过来的数据
      // hotRecom：  车辆推荐 到 版本选择页有权益包 过来的数据
      const isFrom = ['carVersion', 'hotRecom'].includes(from)

      // 这里给接口修改为 then 的方案防止阻塞 getByStyleFn
      // type 1 高  2半  3混
      getStyleList({ customSeriesId, type: checkV2() ? 2 : 1 }).then((res) => {
        const data = res.data.data
        if (data) {
          // data[0] && this.$store.commit('setCurrentPrice', data[0].price)
          this.$store.commit('setLineList', data.sort(this.compareArr('price', true)))
          this.$store.commit('hideLoading')

          if (!isFrom) {
            // 初始化数据 给推荐页过来的车型默认第一个
            data && data[0] && this.$store.commit('setSelectCarIfo', data[0])
          }
        }
      })

      if (isFrom) {
        this.setTab(1)
        // 初始化数据 给推荐页过来的车型默认第一个
        // from.includes('hotRecom') && data && data[0] && this.$store.commit('setSelectCarIfo', data[0])
        this.disabledTab = false
        this.showTabPane = true
        this.currentIndex = '1'
        this.getByStyleFn(customSeriesId)
      } else {
        // hot： 车辆推荐页无有权益包 过来的数据
        if (from === 'hot' && this.currentIndex == 1) {
          let selectHotCar = this.$storage.getPlus('selectHotCar')
          this.$store.commit('setSelectCarIfo', selectHotCar)
          this.$store.commit('hideLoading')
        }
      }
    },
    /** 两个参数： 参数1 是排序用的字段， 参数2 是：是否升序排序 true 为升序，false为降序*/
    compareArr(attr, rev) {
      // console.log(attr, rev)
      if (rev == undefined) {
          rev = 1;
      } else {
          rev = (rev) ? 1 : -1;
      }
      return (a, b) => {
          a = a[attr];
          b = b[attr];
          if (a < b) {
              return rev * -1;
          }
          if (a > b) {
              return rev * 1;
          }
          return 0;
      }
    },
    // async  getCustomSeriesList () {
    //   this.$store.commit('showLoading')
    //   await getCarList().then(async (res) => {
    //     if (res.data.code === '00') {
    //       let obj = res.data.data.find(e => e.seriesName == ['A7L', "Q5E",  "Q6"][this.idx])
    //       this.$store.commit('hideLoading')
    //       this.getStyleListFn(obj.customSeriesId)
    //     }
    //   }).catch(error=>{
    //     console.log(error)
    //   })
    // },
    async getIdx() {
      const res = await callNative('getParmas', {})
      if (res && res.params && Base64.decode(res.params).startsWith('{')) {
        const params = JSON.parse(Base64.decode(res.params))
        this.idx = `${params.data}`
      }
      const { idx } = this.$route.query
      if (idx) {
        this.idx = idx
      }
      this.$store.commit('setIdx', this.idx)
    },
    setCurrentV2popupItem(index) {
      let optionIndex = this.V2PopupMeasureConfigCodeList[index].prCodes.findIndex(e => "4D3,8I6".includes(e)) + 1
      // modelLineCode: "G4ICF3001" 荣耀型 机甲
      // modelLineCode: "G4IBF3001"  荣耀型 锦衣
      if ("G4ICF3001,G4IBF3001".includes(this.selectCarInfo.modelLineCode) && optionIndex) {
        let code = this.V2PopupMeasureConfigCodeList[index].prCodes.find(e => "4D3,8I6".includes(e))
        let ind = -1
        if (this.currentV2popupItem != -1) {
          ind = this.V2PopupMeasureConfigCodeList[this.currentV2popupItem].prCodes.findIndex(e => "4D3,8I6".includes(e))
        }
        if (ind != -1) return
        this.$store.commit('setShowOptionPopupPlus', { bool: true, code: code, index: index })
        return
      }

      this.$store.commit('setCurrentV2popupItem', index)
      let tmp = this.V2PopupMeasureConfigCodeList
      let tmplist = tmp[index]?.prCodes.map(e => {
        return this.privateOrderList.find((item) => {
          return item.optionCode === e
        })
      })

      let {
        selectCarInfo,
        currentExColor,
        currentModelHub,
        currentInteriorChair,
        currentInteriorEih,
        currentSibColorInterieur,
        currentOptionsList
      } = this

      let arr = [];
      let optionIds = []

      arr = [
        currentExColor,
        currentModelHub,
        currentInteriorEih
      ]
      arr = arr.map((item) => item.optionId)
      if (currentInteriorChair && currentInteriorChair.optionId) {
        arr.push(currentInteriorChair.optionId)
      }
      arr.push(currentSibColorInterieur.sibOptionId)
      arr.push(currentSibColorInterieur.interieurOptionId)
      let arrOp = [...currentOptionsList, ...tmplist]
      if (arrOp.length > 0) {
        optionIds = Array.from(arrOp, (item) => item.optionId)
      }
      let { optionId } = this.$route.query
      if(optionId) optionIds.push(optionId)

      optionIds = optionIds.filter((e) => e)
      if (checkV2()) {
        //  debugger
        this.$store.dispatch('getMeasurePriceComputeAction', [...arr, ...optionIds])
      }
    },
    handleEventFn(index) {
      this.$store.commit('setShowOptionPopupPlus', { bool: false, code: '' })

      this.$store.commit('setCurrentV2popupItem', index)
      let tmp = this.V2PopupMeasureConfigCodeList
      let tmplist = tmp[index]?.prCodes.map(e => {
        return this.privateOrderList.find((item) => {
          return item.optionCode === e
        })
      })

      let {
        selectCarInfo,
        currentExColor,
        currentModelHub,
        currentInteriorChair,
        currentInteriorEih,
        currentSibColorInterieur,
        currentOptionsList
      } = this

      let arr = [];
      let optionIds = []

      arr = [
        currentExColor,
        currentModelHub,
        currentInteriorEih
      ]
      arr = arr.map((item) => item.optionId)
      if (currentInteriorChair && currentInteriorChair.optionId) {
        arr.push(currentInteriorChair.optionId)
      }
      arr.push(currentSibColorInterieur.sibOptionId)
      arr.push(currentSibColorInterieur.interieurOptionId)
      let arrOp = [...currentOptionsList, ...tmplist]
      if (arrOp.length > 0) {
        optionIds = Array.from(arrOp, (item) => item.optionId)
      }

      optionIds = optionIds.filter((e) => e)
      if (checkV2()) {
        //  debugger
        this.$store.dispatch('getMeasurePriceComputeAction', [...arr, ...optionIds])
      }
    },
    handleMore(e, i) {
      this.flag = !this.flag
      this.indexPackage = this.flag ? e.length + 1 : 3
    },
    handelDetail(e) {
      this.$router.push({
        path: '/configration/EquipmentBox',
        query: {
          code: e.join(','),
          idx: this.idx,
        }
      })
    },
    getPersonal(item) {
      let arr = this.privateOrderList
      return arr.find((e) => {
        return e.optionCode === item
      })
    },
    getPersonalAllPrice(item) {
      let total = 0
      item.forEach(element => {
        let tmp = this.getPersonal(element)
        if (tmp && tmp.price > 0) {
          total += tmp.price
        }
      })
      return total
    },
    toBaojia() {
      this.checkMeasureConfig()
      this.$refs.bottomdom.handleBtn(6)
    },
    toXiugai() {
      this.setTab(0)
    },
    clickTab(tab) {
      if (this.idx != 1  && this.$route.query.from == 'hot' && this.currentIndex == 0) {
        let line = this.modelLineList[0]
        line && this.$store.commit('setCurrentPrice', line.price)
        return
      }
      if((this.idx != 1 && this.oldActiveName == 0) ||
       (this.idx == 1 && !checkV2() &&  this.oldActiveName == 0))  {
        this.visible = false
        this.currentIndex = '0'
        this.disabledTab == true
        this.$nextTick(()=>{
          this.visible = true
          setTimeout(() => {
            this.$store.commit('hideLoading')
          }, 0);
        })
        return
      }
      // this.$store.commit("setOptionsSelected", tab.index)
      if (this.currentIndex == 4) {
        let b = checkV2() 
        this.$store.commit('setShowV2Popup', b) //装备组合
      } else {
        //  this.$store.commit('setShowV2Popup', false) //装备组合
      }

      this.$store.commit('setCurrentTabIndex', tab.index)
      let arr = this.currentOptionsList
        && this.currentOptionsList.map((res) => res.optionName)
      arr = arr.join('/')
      const params = {
        tab_name: tab.label,
        colour_tab: this.currentExColor.optionName
      }
      this.$sensors.track('selectModelTab', params)

      let sensorsName
      if (tab.label === '私人定制' || tab.label === '个性定制') {
        sensorsName = 'clickPersonalTailor'
      }
      if (tab.label === '限量号') {
        sensorsName = 'clickCarsLimit'
      }
      if ('权益,先见权益,先行权益'.includes(tab.label)) {
        sensorsName = 'clickCarsRights'
      }
      let interiorType = ''
      if (this.currentInteriorChair && this.currentInteriorChair.optionName) {
        interiorType += (`${this.currentInteriorChair.optionName}/`)
      }
      sensorsName
        && this.$sensors.track(sensorsName, {
          model_name: this.selectCarInfo.modelLineName,
          price: this.currentPrice,
          cars_appearance: this.currentExColor.optionName,
          hub_model: this.currentModelHub.optionName,
          interior_type: `${interiorType}${this.currentSibColorInterieur?.sibName}/${this.currentSibColorInterieur.interieurName}/${this.currentInteriorEih.optionName}`,
          personal_tailor: arr
        })
    },
    handleBeforeLeave(activeName, oldActiveName) {
      if(this.idx != 1 || (!checkV2() && this.idx == 1)) {
        this.oldActiveName = oldActiveName
        this.disabledTab == true
        return
      }
     
      if (Number(activeName) === 4 && this.currentIndex == 4) {
        let b = this.$storage.getPlus('semi-definite') == '个性定制' && this.privateOrderList?.length > 0
        this.$store.commit('setShowV2Popup', b) //装备组合
      }
      if (checkV2()) {
        if (Number(oldActiveName) === 4) {
          // if (activeName - oldActiveName > 0) {
          if (activeName - oldActiveName > 0 && !this.checkMeasureConfig()) {
            // this.$store.commit('setShowV2Popup', true) //装备组合
            this.setTab(4)
            return false
          }
        } else {
          if (this.nowTabIndex) {
            this.$store.commit('updateNowTabIndex', '')
          } else if (activeName - oldActiveName > 1) { return false }
        }
      }
    },
    // 判断是否有符合选配的半定制化车
    checkMeasureConfig() {
      if (this.measureConfigCodeList && this.measureConfigCodeList.length > 0) {
        for (let i = 0, l = this.measureConfigCodeList.length; i < l; i++) {
          if (this.currentOptionsList && this.currentOptionsList.length === this.measureConfigCodeList[i].prCodes.length) {
            if (this.currentOptionsList.length === 0) {
              this.$store.commit('setCurrentMeasureConfigCode', this.measureConfigCodeList.find((e) => e.prCodes.length === 0))
              return true
            }
            let flag = true
            for (let j = 0, m = this.currentOptionsList.length; j < m; j++) {
              if (!this.measureConfigCodeList[i].prCodes.some((e) => e === this.currentOptionsList[j].optionCode)) {
                flag = false
                break
              }
            }
            if (flag) {
              const optionCodes = this.currentOptionsList.map((e) => e.optionCode)
              optionCodes.sort()
              const tmp = this.measureConfigCodeList.find((f) => {
                const arr = f.prCodes
                arr.sort()
                return optionCodes.toString() === arr.toString()
              })
              this.$store.commit('setCurrentMeasureConfigCode', tmp)
              return true
            }
          }
        }
      }
      return false
    },
    setTab(index) {
      if (this.ccid) {
        this.currentIndex = `${index}`
      } else {
        // if (index - Number(this.currentIndex) <= 1) {
        this.currentIndex = `${index}`
        // }
      }
      this.$store.commit('setCurrentTabIndex', index)
    },

    // 获取限量号数据
    async getLimitnumberAndQuanyiData() {
      if (this.selectCarInfo && this.selectCarInfo.modelLineCode) {
        const params = {
          carModelId: this.selectCarInfo.modelLineCode,
          type: 2
        }

        const { data } = await getUserRightsByCarModelId(params)
        this.showLNRights = data.data !== null
        if (this.showLNRights) {
          const lNDetail = JSON.parse(data.data.rights)
          this.LNRightTitle = data.data.title
          this.$store.commit('updateLimitNumberData', lNDetail)
        }
      }
    },
    async getFirstEquityData() {
      if (this.selectCarInfo && this.selectCarInfo.modelLineCode) {
        const params = {
          carModelId: this.selectCarInfo.modelLineCode,
          modelYear: this.selectCarInfo.modelYear,
          type: 1
        }
        const res = await getUserRightsByCarModelId(params)
        let { data } = res
        this.showFirstEquity = data.data !== null
        if (this.showFirstEquity) {
          this.firstEquityTitle = data.data.title || '权益'
        }
      }
    },
    showTip() {
      if (this.currentIndex !== '5') {
        this.currentIndex = '5'
      } else {
        this.limitNumberDescDialogVisible = !this.limitNumberDescDialogVisible
      }
    },

    async activateData(){
      getAPITimeOutTesting();
      this.reactiveCheckV2 = checkV2()
      const {
        modelLineCode, visible: condition, tabIndex, from, modelLineId: urlModelLineId
      } = this.$route.query

      // this.checkMeasure = checkV2()
      const visiCar = 'G6ICAY005,G6ICAY004,G6ICAY003,G6ICAY002'.includes(modelLineCode)
      if (visiCar) {
        const chair7 = 'G6ICAY002,G6ICAY004'.includes(modelLineCode) // 7座
        const chair6 = 'G6ICAY003,G6ICAY005'.includes(modelLineCode) // 6座
        let defPackageData = []
        if (chair6) defPackageData = defPackageData6
        if (chair7) defPackageData = defPackageData7
        this.$store.commit('setPrivateOrderList', defPackageData)
      }

      if (tabIndex === '4') {
        this.$store.commit('setTitle', `选择${this.$storage.getPlus('semi-definite') == '私人高定' ? '私人定制' : '个性定制'}`)
      }

      // let condition = this.$route.query.visible
      if (condition) {
        const ccData = this.$storage.getPlus('cc_data')
        this.$store.commit('setCcData', ccData)
        return
      }
      // this.$store.commit('setSemiDefinite', '')
      // this.$store.commit('setShowV2Popup', false)
      this.currentIndex = tabIndex

      if (this.nowTabIndex) {
        this.currentIndex = this.nowTabIndex
      }
      await this.getIdx()
      if (this.backFromOptionDetail) {
        this.setTab(4)
        this.$store.commit('setBackFromOptionDetail', false)
      } else {
        /**
         * q5e半定
         */
        if (this.idx == 1 && checkV2()) {
          this.$store.dispatch('initCarModelList', {
            idx: this.idx, urlModelLineId, style: from === 'quotation' ? 'switch' : '', $storage: this.$storage
          })
        } else {
          // this.getCustomSeriesList()
          /**
           * a7l+ q6 + q5e高定
           */
          this.getStyleListFn()
        }
      }
      switch (this.currentIndex) {
        case '4':
          this.$store.commit('setTitle', checkV2() ? '选择个性定制' : '选择私人定制')
          break
        case '1':
          this.$store.commit('setTitle', '选择颜色')
          break
        default:
          this.$store.commit('setTitle', '选择车型')
          break
      }
    },

    async networkReload(){
      await this.activateData();
      Toast.clear();
    }
  }
}
</script>

<style lang="less" scoped>
.configration {
  position: relative;
  width: 100vw;

  .v2popupcss {
    padding-top: 10px;
    width: 100vw;
    overflow-y: auto;
    background-color: #fff;
    padding: 0 16px 110px 16px;
    box-sizing: border-box;
    font-size: 14px;

    .v2popup_title {
      // height: 34px;
      // line-height: 34px;
      padding: 8px 0;
      font-weight: bold;
    }

    .v2popup_item {
      box-shadow: 0px 0px 8px 0px rgba(51, 51, 51, 0.1200);
      margin: 8px 0;
      margin-bottom: 16px;

      .v2popup_head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 0;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.0500) 0%, rgba(0, 0, 0, 0.2000) 100%);

        .head_bg {
          width: 24px;
          height: 24px;
          background-image: url("../../assets/img/icon-radio.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          &.current {
            background-image: url("../../assets/img/icon-radio-cur.png");
          }
        }

        .head_price {
          font-weight: bold;
        }
      }

      .v2popup_main {
        .main_item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #333;
          padding: 4px 0;

          .main_item_left {
            flex: 1;
          }

          .main_item_right {
            font-size: 12px;
          }
        }
      }
    }
  }

  .nopersonalpopup {
    position: fixed;
    z-index: 9999;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 16px;
    box-sizing: border-box;

    .main {
      background-color: #fff;
      position: absolute;
      width: calc(100% - 64px);
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 16px;

      .content {
        font-weight: bold;
        line-height: 24px;
      }

      .button1 {
        background-color: #000;
        color: #fff;
        height: 56px;
        margin-top: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .button2 {
        border: solid 1px #000;
        height: 56px;
        margin-top: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  /deep/ .el-tabs__header {
    margin: 0;
    max-width: 100vw;
    height: 34px;
    font-size: 16px;
    font-weight: 400;

    .is-scrollable {
      padding: 0;
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      display: none;
    }

    .el-tabs__nav {
      width: 100vw;
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;
      /* IE 10+ */
      overflow-x: auto;

      &::-webkit-scrollbar {
        display: none;
        /* Chrome Safari */
      }
    }

    .el-tabs__item {
      width: 20vw;
      height: 34px;
      line-height: 34px;
      padding: 0;
      text-align: center;
      color: #999;

      & .tip {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url('../../assets/img/wen1.png');
        background-size: 100% 100%;
        position: relative;
        left: 2px;
      }

      &.is-active {
        color: #000000;

        &.is-focus {
          box-shadow: none;
        }

        & .tip {
          background-image: url('../../assets/img/wen2.png');
        }
      }
    }

    .el-tabs__active-bar {
      background-color: #000;
      width: 20vw;
    }

    .el-tabs__nav-wrap::after {
      background-color: #fff;
    }
  }
}

.el-tab-pane {
  max-height: calc(100vh - 220px);
  padding-bottom: 110px;
  overflow-y: auto;
}
</style>
