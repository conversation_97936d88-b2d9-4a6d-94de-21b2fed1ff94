<template>
  <div class="_container">
    <iframe class="prism-player" :src="pdfUrl"></iframe>

    <!-- <div class="border-bottom">
      <div class="_title">
        <p class="font-WideBold">三包凭证编号：</p>
        <p>{{detail.vin}}</p>
      </div>
    </div>
    <div class="border-bottom">
      <div class="_title">
        <p class="font-WideBold">产品信息</p>
      </div>
      <div class="_item">
        <p>产品品牌：</p>
        <p>{{brandCode[detail.brandCode]}}</p>
      </div>
      <div class="_item">
        <p>型号：</p>
        <p>{{detail.factoryPlateModel}}</p>
      </div>
      <div class="_item">
        <p>车辆类型：</p>
        <p>{{detail.vehicleType}}</p>
      </div>
      <div class="_item margin-bottom">
        <p>生产日期：</p>
        <p>{{ dayjs(detail.producerDate).format('YYYY-MM-DD') }}</p>
      </div>
    </div>
    <div class="border-bottom">
      <div class="_title">
        <p class="font-WideBold">生产者信息</p>
      </div>
      <div class="_item">
        <p>名称：</p>
        <p>{{detail.producerName}}</p>
      </div>
      <div class="_item">
        <p>地址：</p>
        <p>{{detail.producerAddress}}</p>
      </div>
      <div class="_item">
        <p>邮政编码：</p>
        <p>{{detail.producerPosNo}}</p>
      </div>
      <div class="_item margin-bottom">
        <p>客服电话：</p>
        <p>{{detail.producerTel}}</p>
      </div>
    </div>
    <div class="border-bottom">
      <div class="_title">
        <p class="font-WideBold">销售者信息</p>
      </div>
      <div class="_item">
        <p>名称：</p>
        <p>{{detail.sellerName}}</p>
      </div>
      <div class="_item">
        <p>地址：</p>
        <p>{{detail.sellerAddress}}</p>
      </div>
      <div class="_item">
        <p>邮政编码：</p>
        <p>{{detail.salesPosCode}}</p>
      </div>
      <div class="_item margin-bottom">
        <p>客服电话：</p>
        <p>{{detail.sellerPhone}}</p>
      </div>
    </div>
    <div class="border-bottom">
      <div class="_title">
        <p class="font-WideBold">修理者信息</p>
      </div>
      <div class="_item">
        <p>网点信息的查询方式：</p>
        <p>{{detail.networkinformation}}</p>
      </div>
    </div>
    <div class="border-bottom">
      <div class="_title">
        <p class="font-WideBold">交付信息</p>
      </div>
      <div class="_item">
        <p>开具购车发票的日期：</p>
        <p>{{ dayjs(detail.invoiceDate).format('YYYY-MM-DD') }}</p>
      </div>
      <div class="_item">
        <p>交付车辆的日期：</p>
        <p>{{detail.deliveryDate}}</p>
      </div>
    </div>
    <div class="border-bottom">
      <div class="_title">
        <p class="font-WideBold">汽车产品包修期信息（以先到者为准）</p>
      </div>
      <div class="_item">
        <p>汽车产品包修期限开始日期/里程：</p>
        <p>{{detail.deliveryDate}}</p>
      </div>
      <div class="_item">
        <p>汽车产品包修期限终止日期/里程：</p>
        <p>{{detail.warrantyCalendar}}</p>
      </div>
    </div>
    <div class="">
      <div class="_title">
        <p class="font-WideBold">汽车产品三包有效期信息（以先到者为准）</p>
      </div>
      <div class="_item">
        <p>汽车产品三包期限开始日期/里程：</p>
        <p>{{detail.deliveryDate}}</p>
      </div>
      <div class="_item">
        <p>汽车产品三包期限终止日期/里程：</p>
        <p>{{detail.guaranteeCalendar}}</p>
      </div>
    </div> -->
    <!-- <div class="">
      <div class="_title">
        <p class="font-WideBold">销售者签章：</p>
      </div>
      
    </div> -->

    <div class="btn-wrapper">
      <AudiButton text="一键下载" color="black" height="56px" font-size="16px" @click="downloadConfigpdf" />
    </div>
  </div>
</template>

<script>
  import { callNative } from '@/utils'
  import { Toast } from 'vant'
  import { BaseApiUrl } from '../../config/url.js'
  import { getGenerateMessage, downloadPdfGenerate } from '../../api/api.js'
  import AudiButton from '@/components/audi-button'
  import api from '@/config/url'
  import dayjs from 'dayjs'
  import Vue from 'vue'
  Vue.use(Toast)
  export default {
    components: { AudiButton },
    data() {
      return {
        detail: null,
        dayjs,
        brandCode: {
          A: '奥迪',
          V: '大众品牌',
          S: '斯柯达品牌',
        },
        pdfUrl: ''
      }
    },
    async mounted() {
      this.getPdfFile()
      // const { orderId } = this.$route.query
      // const res = await getGenerateMessage({ orderId })
      // this.detail = res.data.data
    },
    methods: {
      // 一键下载(调用native)
      async downloadConfigpdf() {
        const { orderId } = this.$route.query
        const pdfurl = api.BaseApiUrl + '/api-wap/audi-contract-adapter/api/v1/certificate/generate?orderId=' + orderId
        const fileName = `三包凭证${new Date().getTime()}.pdf`
        callNative('downloadFile', { url: pdfurl, fileName, type: 'pdf' }).then((res) => {
          Toast({
            type: 'success',
            message: '下载成功',
            icon: require('../../assets/img/success.png')
          })
        })
      },
      async getPdfFile() {
        const { orderId } = this.$route.query
        // const res = await downloadPdfGenerate({orderId:'219005789584172'})
        const res = await downloadPdfGenerate({orderId}) //:'218974782684254'
        console.log(res.data)
        let urlPdf = window.URL.createObjectURL(new Blob([res.data], {type: 'application/pdf'}))
        console.log('urlPdf', urlPdf)
        
        // 'blob:http://localhost:8080/69089ff0-5194-4d70-9dbe-490fd3b10c60'
        // 'blob:https://uataudi-embedded-wap.saic-audi.mobi/474cc89d-5924-4137-9d8e-3e885b403bec'
        
        this.pdfUrl = './pdf/web/viewer.html?file=' + encodeURIComponent(urlPdf)
        console.log('pdfUrl',this.pdfUrl)
        
        // '/pdf/web/viewer.html?file=blob%3Ahttp%3A%2F%2Flocalhost%3A8080%2F69089ff0-5194-4d70-9dbe-490fd3b10c60'
        // '/pdf/web/viewer.html?file=blob%3Ahttps%3A%2F%2Fuataudi-embedded-wap.saic-audi.mobi%2F474cc89d-5924-4137-9d8e-3e885b403bec'
      },
    }
  }
</script>

<style lang='less' scoped>
  .btn-wrapper {
    position: fixed;
    bottom: 15px;
    width: 100%;
    padding: 0 15px;
    left: 0;
    box-sizing: border-box;
  }

  .border-bottom {
    border-bottom: 1px #E5E5E5 solid;
  }

  .margin-bottom {
    margin-bottom: 8px;
  }

  div p {
    box-sizing: border-box;
    font-family: "Audi-Normal";
  }

  p {
    margin: 0;
  }

  .font-WideBold {
    font-family: "Audi-WideBold";
  }

  ._container {
    /* padding: 0 16px;
    padding-bottom: 120px;
    display: flex;
    flex-flow: column;
    font-size: 16px;
    color: #000; */
    .prism-player{
      width: 100%;
      height: 100vh;
      box-sizing: border-box;
      overflow: hidden;
    }

    ._title {
      line-height: 52px;
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: space-between;
    }

    ._item {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 33px;
    }
  }
</style>
