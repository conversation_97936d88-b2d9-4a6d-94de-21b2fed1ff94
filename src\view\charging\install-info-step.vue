<template>
  <div class="unsubscribeSucceed">
    <van-steps direction="vertical" :active="0">
       <van-step v-for="(item, idx) in context" :key="idx" >
            <div style="color: #000; font-size: 16px;padding-top: 3px" >
              {{ item.status }}
            </div>
            <div style="color: #999; font-size: 12px ;padding-top: 10px">
              {{ item.createdAt }}
            </div>
          </van-step>
    </van-steps>
  </div>
</template>

<script>
import Vue from 'vue'
import { Step, Steps } from 'vant'
import { getChargingPileStatusList } from '@/api/api'

Vue.use(Step).use(Steps)
export default {
  data() {
    return {
      context: []
    }
  },
  mounted() {
    const { csmsOrderId } = this.$route.query

    this.getChargingPileStatusList(csmsOrderId)
  },
  methods: {
    async getChargingPileStatusList(csmsOrderId) {
      const { data } = await getChargingPileStatusList({
        csmsOrderId: csmsOrderId
      })
      if (data.code === '00') {
        this.context = data.data
      }
    }
  }
}
</script>

<style scoped lang="less">
</style>
