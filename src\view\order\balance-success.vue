<template>
  <div>
    <div class="img-wrapper">
      <img src="../../assets/img/contract-success.png" alt="">
    </div>
    <div class="c-font18 text-wrapper">
      您的支付结果将在一个工作日内审核确认
    </div>

    <div class="btn-wrapper">
      <AudiButton @click="toMyOrder" text="查看我的订单" font-size="16px" color="black" height="56px" />
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import { XIAN_JIAN_VERSION } from '@/config/constant'
  import AudiButton from '@/components/audi-button'

  export default {
    components: { AudiButton },
    computed: {
      ...mapState({
        orderId: (state) => state.orderId
      }),
      isXianJian() {
        return this.currentModelLineCode === XIAN_JIAN_VERSION
      }
    },

    methods: {
      toMyOrder() {
        const { orderId } = this.$route.query
        this.$router.push({
          path: '/order/money-detail',
          query: { orderId }
        })
      }
    }

  }
</script>

<style lang="less" scoped>
  @import "../../assets/style/common.less";

  .img-wrapper {
    width: 72px;
    margin: 30vw auto 0 auto;
  }

  .text-wrapper {
    padding: 0 70px;
    margin-top: 25px;
    line-height: 32px;
    text-align: center;
  }

  .btn-wrapper {
    padding: 0 16px;
    position: fixed;
    bottom: 58px;
    width: 100%;
    box-sizing: border-box;
  }
</style>
