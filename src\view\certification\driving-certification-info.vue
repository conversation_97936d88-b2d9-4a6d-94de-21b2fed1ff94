<template>
  <div class="unsubscribeSucceed">
    <!-- <div class="line-border-bottom">
      <div class="name">
        状态
      </div>
      <div class="str">
        {{ showGidType() }}
      </div>
    </div> -->
    <!-- <div class="interval_line" /> -->

    <!-- <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 80px; font-size: 16px; color: #000">
          车牌号码
        </div>
        <div
          class="str"
          v-if="plateNumber"
        >
          {{ plateNumber }}
        </div>
        <div
          class="str"
          style="color: #999"
          v-if="!plateNumber"
        >
          {{ "请输入您的车牌号" }}
        </div>
      </div>

      <div
        style="float: right"
        v-if="!plateNumber"
      >
        <van-icon
          class="btn-icon"
          name="arrow"
          size="16px"
          @click="onAddCarNo"
        />
      </div>
    </div> -->
    <div class="line-border-bottom">
      <div class="name">姓名</div>
      <div class="str">
        {{ owner }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">车牌号</div>
      <div class="str">
        {{ plateNumber }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">VIN码</div>
      <div class="str">
        {{ vin }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">注册日期</div>
      <div class="str">
        {{ ocrOrderDate }}
      </div>
    </div>
    <!--  -->
    <div class="btn" v-if="bindingStatus == '1'">
      <div @click="onCarUnbinding">解除绑定 ></div>
    </div>

    <van-popup
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal">
        <div class="modal-title">身份验证</div>

        <div style="font-size: 14px; color: #000; margin-top: 8px" v-if="phone">
          {{ phone.substr(0, 3) + '****' + phone.substr(7) }}
        </div>

        <!-- <div class="modal-line">
          <div style="width: 80px; font-size: 16px; color: #000">图形码</div>

          <van-field v-model="verifyCode" placeholder="请输入图形验证码" />
          <img
            style="height: 24px; width: 120px"
            :src="base64"
            @click="getCaptchaImage()"
          />
        </div> -->
        <div class="modal-line">
          <div style="width: 80px; font-size: 16px; color: #000">验证码</div>

          <van-field
            v-model="captcha"
            placeholder="请输入验证码"
            type="digit"
          />
          <AudiButton
            v-show="show"
            @click="onSendSMS"
            :text="'获取验证码'"
            color="white"
            font-size="12px"
            height="24px"
            width="120px"
          />
          <AudiButton
            v-show="!show"
            :text="count + 's'"
            color="white"
            font-size="12px"
            height="24px"
            width="120px"
          />
        </div>
        <div style="display: flex; justify-content: space-between; width: 90%">
          <div style="margin-left: 16px; width: 44%">
            <AudiButton
              @click="modalshow = false"
              :text="'不解绑啦'"
              color="white"
              font-size="15px"
              height="50px"
            />
          </div>
          <div style="margin-right: 16px; width: 44%">
            <AudiButton
              @click="onConfirmsubmit"
              :text="'确定'"
              color="black"
              font-size="15px"
              height="50px"
            />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Image as VanImage,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover,
  ActionSheet,
  Grid,
  GridItem
} from 'vant'
import { callNative } from '@/utils'
import {
  postSendSMS,
  unBindDrivingLicense,
  drivingLicenseInfo
} from '@/api/api'
import AudiButton from '@/components/audi-button'
import api from '@/config/url'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)
  .use(ActionSheet)
  .use(Grid)
  .use(GridItem)
  .use(VanImage)
export default {
  components: {
    AudiButton
  },
  data() {
    return {
      modalshow: false,
      bindingStatus: '',
      plateNumber: '',
      vin: '',
      ocrOrderDate: '',
      phone: '',
      captcha: '', // 手机验证码
      verifyCode: '', // 图形验证码
      base64: '',
      findCustomerCarList: [],
      show: true,
      count: '',
      timer: null,
      owner: ''
    }
  },
  mounted() {
    const { idx, phone } = this.$route.query
    this.phone = phone

    this.drivingLicenseInfo(idx)
  },
  methods: {
    async drivingLicenseInfo(idx) {
      const { data } = await drivingLicenseInfo({
        bindingStatus: 4,
        bindingType: 0
      })
      console.log(data, 'akakakakaka')
      this.bindingStatus = data.data[idx].bindingStatus
      this.plateNumber = data.data[idx].plateNum
      this.vin = data.data[idx].vin
      this.ocrOrderDate = data.data[idx].registerDate
      this.owner = data.data[idx].owner
    },
    showGidType() {
      if (this.bindingStatus == '0') {
        return '待审核'
      }
      if (this.bindingStatus == '1') {
        return '已认证'
      }
      if (this.bindingStatus == '2') {
        return '审核被拒'
      }
      if (this.bindingStatus == '3') {
        return '已解绑'
      }
      if (this.bindingStatus == '8') {
        return '已废弃'
      }
    },

    // 添加车牌号
    onAddCarNo() {
      this.$router.push({
        path: '/aftersales/input-car-no',
        query: { type: 'addCarNo', vin: this.vin }
      })
    },
    async onCarUnbinding() {
      this.show = true
      clearInterval(this.timer)
      this.timer = null

      this.modalshow = true
      this.getCaptchaImage()
    },
    // 获取图形验证码
    async getCaptchaImage() {
      this.base64 =
        `${api.BaseApiUrl}/api-wap/cop-cms/api/v1/captcha/image?random=202202` +
        `&${new Date().getTime()}`
      console.log(this.base64)
    },
    // 发送短信
    async onSendSMS() {
      // if (!this.verifyCode) {
      //   callNative("toast", { type: "fail", message: "请输入图形验证码" });
      //   return;
      // }

      const { data } = await postSendSMS({ phone: this.phone })
      if (data.code === '00') {
        // 短信发送成功后倒计时
        this.setCountdown()
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    setCountdown() {
      const TIME_COUNT = 60
      if (!this.timer) {
        this.count = TIME_COUNT
        this.show = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.show = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    // 解绑
    async onConfirmsubmit() {
      if (!this.captcha) {
        callNative('toast', { type: 'fail', message: '请输入验证码' })
        return
      }
      this.show = true
      clearInterval(this.timer)
      this.timer = null

      const param = {
        captcha: this.captcha,
        vin: this.vin
      }
      const { data } = await unBindDrivingLicense(param)
      if (data.code === '200') {
        callNative('toast', { type: 'success', message: '解绑成功' })
        this.$router.back(-1)
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/buttons.less');

.unsubscribeSucceed {
  padding: 0 16px;

  img {
    margin-top: 10px;
    width: 48px;
    height: 48px;
  }

  p {
    text-align: center;
    font-size: 16px;
    color: #000000;
    margin-top: 8px;
  }

  .content {
    text-align: center;
    font-size: 14px;
    color: #666;
  }

  .interval_line {
    margin-left: -16px;
    margin-right: -16px;
    height: 8px;
    background: #f2f2f2;
  }

  .line-border-bottom {
    width: 100%;
    display: flex;
    //   align-items: center;
    margin-top: 30px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f2f2f2;

    .name {
      width: 80px;
      font-size: 16px;
      color: #000;
    }

    .str {
      font-family: 'Audi-Normal';
      font-size: 16px;
      color: #333;
    }
  }

  .line {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
    white-space: nowrap;
  }
}

.btn {
  text-align: center;
  box-sizing: border-box;
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0px;
  padding-bottom: 26px;
  left: 0px;
}

._modal {
  width: 343px;
  background: #ffffff;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;

  .modal-title {
    font-size: 18px;
    font-weight: 500;
    color: #1a1a1a;

    font-family: 'Audi-WideBold';
  }

  .modal-line {
    width: 80%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
    white-space: nowrap;
  }
}
</style>
