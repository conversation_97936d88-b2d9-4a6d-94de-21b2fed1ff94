/**
 * 夏日礼包. 只有a7 和 q5e车型有
 */
export const SUMMER_PACKAGE = ["4D3+PV3+4A4+YED", "PS8+WAB+GZ2"];

/**
 * a7l 后排剧院级音响智能屏套装
 */
export const A7L_INTELLIGENT_AUDIO = {
  name: "后排剧院级音响智能屏套装",
  // 9WP+YED ,这个顺序在其他组件有依赖,如果要更新,注意对应的索引值
  optionCode: ["9VS", "9WP+YED"],
  price: 28000,
  price9wp: 20000
};

/**
 * a7l
 * 奥迪高级智能数字套装 WAO
 * 显示特殊文案: 支持部分手机机型
 *
 * 数字套装的子装备: 数字钥匙 2F1
 * 显示关联的兼容机型页面
 */
export const A7L_FIGURE_KEY = ["WAO", "2F1"];

/**
 * A5L的红色卡钳
 */
export const A5L_RED_RAD = {
  "1LQ": "1LQ", "1KX": "1KX", "2EF": "2EF"
};

/**
 * A5L的外饰颜色（绿、米、蓝）：价格
 */
export const A5L_EXERIOR_PRICE = {
  "F8A1": 6000, // 寻踪绿
  "I1A1": 6000, // 曙光米
  "7TA1": 6000 //  智韵蓝
};

/**
 * A5L 豪华型
 */
export const A5L_HAOHUA = new Set([
  "F08BZG-Y2026-V0-MLIA8G4-MHSW8IY",
  "F08BZG-Y2026-V1-MLIA8G4-MHSW8IY",
]);

/**
 * A5L 19吋轮毂组合包的labelValue
 */
export const A5L_RAD_PACKET_LABEL_VALUE_19 = {
  "1F08B0009": 4500,
  "1F08B0004": 4500,
};