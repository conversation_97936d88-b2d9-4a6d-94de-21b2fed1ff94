export default {
  // 爆款推荐列表混合接口
  recommendStyleFix:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/recommendStyleFix',

  updateNew: '/api-wap/audi-car-config/api/v1/cc/updateNew',
  policyUrl: '/api-wap/cop-system/api/v1/config/audi.loan.policy',
  byStyleUrl: '/api-wap/audi-car-config/api/v1/cc/public/modelLine/byStyle',
  styleListUrl:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/styleList',
  recomCarByStyleUrl:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/recommendCarByStyle',
  recommendStyleListUrl:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/recommendStyleList',
  packetEquityUrl:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/packetEquity',
  // 获取车系
  carList: '/api-wap/audi-car-config/api/v1/cc/public/customSeries',
  // 爆款推荐
  recommendCar: '/api-wap/audi-car-config/api/v1/cc/public/recommendCar',
  bestRecommendConfig:
    '/api-wap/audi-car-config/api/v1/cc/public/bestRecommendConfig',
  // 获取配置线
  modelList: '/api-wap/audi-car-config/api/v1/cc/public/modelLine',
  switchmodelList: '/api-wap/audi-car-config/api/v1/cc/switch/modelLine',
  // 获取配置线所有参数
  allModelParam: '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs',
  // 获取外饰
  exterior:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/color_exterieur',
  // 获取内饰
  interior:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/color_interieur',
  // 获取座椅
  vos: '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/vos',
  // 获取面料
  sib: '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/sib',
  // 获取饰条
  eih: '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/eih',
  // 获取装备
  equipment:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/optional_equipment',
  // 获取轮毂
  rad: '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/rad',
  // 个性化选装(私人订制)
  personalOption:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/personalOption',
  // 选装包详情
  packetItem:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/packetItem',
  // 内饰+面料
  sibColorInterieur:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/sib_color_interieur',
  // 价格计算
  priceCompute:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/priceCompute',
  // 半定制价格计算
  measurePriceCompute:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/measurePriceCompute',

  deliveryTimeCompute:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/estimateQuery', // 等删掉
  getDeliveryTimeByCcid:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/estimateQueryByCc', // 等删掉

  // 生成配置单
  getCc: '/api-wap/audi-car-config/api/v1/cc/public/',
  canBuyA7LEdtionOne: '/api-wap/audi-eshop/api/v1/canBuyA7LEdtionOne',
  canBuyA7LEdtionOne2: '/api-wap/audi-eshop/api/v1/canBuyA7LEdtionOne2',
  // optionIds获取配置项信息
  getOptionsInfoList:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/options',
  // optionIds获取配置项信息
  ccConfigration: '/api-wap/audi-car-config/api/v1/cc/',
  // optionIds获取配置项信息
  autoSaveCarShoppingCart:
    '/api-wap/audi-car-config/api/v1/carShoppingCart/autoSaveCarShoppingCart',
  // OMD畅销推荐车,虎年限定
  bestRecommendCar:
    '/api-wap/audi-car-config/api/v1/cc/public/bestRecommendCar',
  bestRecommendCarAgent:
    '/api-wap/audi-car-config/api/v1/cc/public/bestRecommendCarAgent',
  // 配置线ID查询配置线详情
  modelLineQuery: '/api-wap/audi-car-config/api/v1/cc/public/modelLineQuery',
  measureQuery:
    '/api-wap/audi-car-config/api/v1/cc/measure/made/public/modelLine/configs/measureQuery',
  getV2CcInfo:
    '/api-wap/audi-car-config/api/v1/cc/measure/made/public/carConfig',
  putMeasureInfo:
    '/api-wap/audi-car-config/api/v1/cc/measure/made/carConfig',

  // 新版ui获取动力,版本,配置线
  getEnergyStyleList:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/energyStyleList',
  // 新版获取选装推荐组合和全部选装包
  getPersonalOptionPlus:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/personalOptionPlus',
  // 新版 cc预计交付时间
  postCcEstimate:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/ccEstimate',
  // 匿名ccid 绑定用户
  bindUserByCCid: '/api-wap/audi-car-config/api/v1/cc/bind',

  // 获取权益图片
  getRightsImg:
    '/api-wap/audi-user-right/api/v1/rightRuleOverview/searchRightRuleOverview',
  // 获取每个车系金融服务的大图
  getPowerPageImg:
    '/api-wap/audi-page/api/floors',
  // 获取OMD 销售配车-车型名称列表
  omdModels: '/api-wap/audi-car-config/api/v1/cc/public/okapi/models',
  // 获取OMD 销售配车-车型价格
  omdModelsPrice: '/api-wap/audi-car-config/api/v1/cc/public/okapi/modelsPrice',
  // 获取OMD 销售配车-车系车型素材
  omdModelMaterial: '/api-wap/audi-car-config/api/v1/cc/public/okapi/modelMaterial',
  // 获取OMD 选中车型详情
  omdModelConfig: '/api-wap/audi-car-config/api/v1/cc/public/okapi/labelConfig',
  // 获取OMD 销售配车_装备组合查询
  omdEquipmentGroup: '/api-wap/audi-car-config/api/v1/cc/public/okapi/equipmentGroup/query',
   // 获取OMD 包中件查询
  omdGetlabel: '/api-wap/audi-car-config/api/v1/cc/public/okapi/pfCode/label/config',
    // 获取OMD ccid配车数据
  omdGetCcid: '/api-wap/audi-car-config/api/v1/cc/okapi',
  // 券码转发-COP查券
  copCouponInfo: '/api-wap/audi-car-config/api/v1/cc/cop/pre-sale/couponInfo',
  // 标装接口
  omdHighLight: '/api-wap/audi-car-config/api/v1/cc/public/okapi/highlight/config',
}
