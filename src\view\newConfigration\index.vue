<template>
  <div class="configration">
    <div class="tab-wrapper" v-if="carIdx !== a5lIdx">
      <van-tabs v-model="carModelActiveTab" :before-change="beforeChange">
        <van-tab title="动力" name="power">
          <Power />
        </van-tab>
        <van-tab title="版本" name="version">
          <CarVersion />
        </van-tab>
        <van-tab title="车型" name="carModel">
          <CarModel />
        </van-tab>
      </van-tabs>
    </div>
    <div v-if="carIdx === a5lIdx" class="A5content">
      <A5LCarVersion />
    </div>
  </div>
</template>

<script>
import { Tab, Tabs, Toast } from 'vant'
import Vue from 'vue'
import { mapState } from 'vuex'
import Power from './power.vue'
import CarVersion from './carVersion.vue'
import {default as A5LCarVersion} from '../a5lConfigration/carVersion.vue'
import CarModel from './carModel.vue'
import { getQueryParam } from '@/utils'

Vue.use(Toast)
Vue.use(Tab)
Vue.use(Tabs)

export default {
  name: 'configRation',
  components: {
    Power,
    A5LCarVersion,
    CarVersion,
    CarModel
  },
  data() {
    return {
      a5lIdx: "e0172bf8-d547-4ad7-adf7-1292f53ea0df",
    }
  },
  computed: {
    ...mapState({
      currentVersionData: (state) => state.configration.currentVersionData,
      currentVersion: (state) => state.configration.currentVersion,
      carIdx: (state) => state.configration.carIdx
    }),
    carModelActiveTab: {
      get() {
        return this.$store.state.configration.carModelActiveTab
      },
      set(val) {
        return this.$store.commit('updateCarModelTab', val)
      }
    },
    carIdx: {
      get() {
        return getQueryParam('idx')
      }
    }
  },

  beforeCreate() {
    const { idx } = this.$route.query
    if (idx === "3") {
      this.$router.push({
        path: '/configration',
        query: {
          idx: 'e0172bf8-d547-4ad7-adf7-1292f53ea0df'
        }
      })
    }
  },

  mounted() {
    // 更新query的参数到store
    this.$store.commit('updateQueryParams', this.$route.query)

    // 到此页面后，需要重置配置页为[外观]tab
    this.$store.commit('updateConfigrationActiveTab', 'exterior')

    // tag 暂时只有高定
    this.$storage.setPlus('semi-definite', '私人高定')

    this.$store.commit('setProjectStartTime')
  },
  activated() {
    // 更新query的参数到store
    this.$store.commit('updateQueryParams', this.$route.query)
  },

  methods: {
    beforeChange(targetTabName) {
      this.clickTabSensors(this.carModelActiveTab, targetTabName) // 埋点

      // 进入版本 tab 的限制
      if (targetTabName === 'version') {
        // 需要选中一个动力
        if (this.currentVersionData.length === 0) {
          this.hitToast('动力')
          return false
        }
      }


      // 进入车型 tab 的限制
      if (targetTabName === 'carModel') {
        // 不能直接从[动力]跳到[车型]
        if (this.carModelActiveTab === 'power') {
          this.hitToast('动力')
          return false
        }

        // 需要选择一个版本
        if (!this.currentVersion.styleId) {
          this.hitToast('版本')
          return false
        }
      }

      return true
    },

    hitToast(msg) {
      Toast(`请先选择${msg}`)
    },

    // 埋点
    clickTabSensors(tabName, targetTabName) {
      const tabMap = {
        power: '动力',
        version: '版本',
        carModel: '车型'
      }
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6',
        3: 'A5L'
      }
      const param = {
        source_module: 'H5',
        refer_tab_name: tabMap[tabName],
        car_series: carMap[this.carIdx],
        car_model: this.currentVersion.styleName ?? '',
        delivery_type: '定制交付', // 快速交付|定制交付
        belong_page: this.$router.history.current.meta?.title,
        tab_name: tabMap[targetTabName]
      }

      // console.log(param, tabName, targetTabName)
      this.$sensors.track('CC_Page_Tab_Click', param)
    }
  }

}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.tab-wrapper {
  min-height: calc(100vh - @TabHeight - @HeaderHeight);
  background-color: #F9F9F9;
}

.A5content {
  padding: 0 16px;
  overflow-y: auto;
  padding-bottom: 120px;
}

// 覆盖默认样式
/deep/.van-tabs__wrap {
  position: sticky;
  top: 0;
  z-index: 20;
  height: 36px;
  padding: 3px 15px;
  background-color: #fff;

  .van-tabs__nav,
  .van-tabs__nav--line {
    background-color: #F2F2F2;
    justify-content: space-between;
    border-radius: 18px;
    padding-bottom: 0;

    // .van-tab {
    //   flex: initial;
    //   padding: 0 30px;
    //   color: #000;
    // }
    .defaultvan {
      flex: initial;
      padding: 0 30px;
      color: #000;
    }

    .onlyv {
      width: 100%;
    }

    .onlyA5 {
      display: none !important;
    }

    .van-tab--active {
      background-color: #E5E5E5;
      border-radius: 18px;
      // font-weight: bold;
    }
  }

  .van-tabs__line {
    display: none;
  }
}
.sticky{
  position: sticky;
}

/deep/.van-tabs__content {
  padding: 0 16px;
  overflow-y: auto;
  padding-bottom: 120px;
}

</style>
