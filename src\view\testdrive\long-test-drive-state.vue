<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-09-07 13:53:18
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-10-24 13:50:45
 * @FilePath     : \src\view\testdrive\long-test-drive-state.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['test-drive-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <!--  :listening-native-back="true"
      :destroy-native-back="destroyNativeBack" -->
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      listening-emit-back
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`}"
    >
      <div
        class="test-drive-box"
      >
        <div
          class="info-box"
        >
          <div
            class="imgs"
          >
            <img :src="require('../../assets/img/Q5e-tron-02.jpg')">
          </div>
          <div class="text">
            <p>艺创未来</p>
            <p>上汽奥迪Q5 e-tron深度试驾体验欢迎预约！</p>
            <p>上汽奥迪率先打破传统的试驾模式，创造性的超长试驾模式，现在与Q5 e-tron一起等待您的预约</p>
            <p>不限路线，自由驾驶</p>
            <p>握紧方向盘，享受全新的新能源驾驶体验</p>
            <p>我们将与你一起，驶向诗与远方</p>
            <p>超长试驾，客户需知</p>
            <p>1. 客户至少需要支付意向金后，才可以预约超长试驾</p>
            <p>2. 若客户在30天超长试驾期间支付定金，则可免单超长试驾费用</p>
          </div>
        </div>
        <div
          class="button-box"
        >
          <audi-button
            text="下一步"
            color="black"
            height="56px"
            @click="handleGoToTestDriveBtn"
          />
        </div>
      </div>
    </div>
    <popup-custom-action-btn
      v-if="testDrive.conf && testDrive.conf.enabled || false"
      :btn-conf="testDrive.conf"
      :btn-items="testDrive.items"
    >
      <template #popup-custom-main>
        <div class="custom-align-center">
          该活动仅限小订及以上用户
        </div>
      </template>
    </popup-custom-action-btn>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import HeaderCustom from '@/components/header-custom.vue'
import audiButton from '@/components/audi-button.vue'
import { getVeryLongReservationValidAuth } from '@/api/api'
import popupCustomActionBtn from '@/components/popup-custom-action-btn.vue'
import { getUrlParamObj, callNative } from '@/utils'

export default {
  components: {
    'header-custom': HeaderCustom,
    'audi-button': audiButton,
    'popup-custom-action-btn': popupCustomActionBtn
  },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      testDrive: {
        conf: {
          enabled: true,
          show: false,
          customClass: 'lan-popup-custom-btn', // un-has-margin-top
          flex: 'main:left'
        },
        items: [
          {
            name: '',
            text: '我知道了'
          }
        ]
      },
      headerOutHeight: 0,
      isStandChance: 0
    }
  },
  created() {
    this.handleVeryLongReservationValidAuth()
  },
  methods: {
    ...mapGetters(['getDevice']),
    handleGoToTestDriveBtn() {
      const { testDrive, testDrive: { conf }, isStandChance } = this
      if (!isStandChance) {
        this.testDrive = { ...testDrive, ...{ conf: { ...conf, ...{ show: true } } } }
        return
      }
      this.$router.push({
        path: this.fromType ? '//testdrive/long-create?fromType=fromPurple' : '//testdrive/long-create'
      })
    },
    async handleVeryLongReservationValidAuth() {
      const { data: { data, code } } = await getVeryLongReservationValidAuth()
      this.isStandChance = code !== '200' ? 0 : 1
    },
    handleLeftBack() {
      const { from } = getUrlParamObj() || ''
      const { nativeApp } = this.getDevice() || ''
      if (from === 'favoriteCar' && nativeApp) {
        callNative('prepage', { times: 1 })
      } else {
        this.$router.go(-1)
      }
    }
  }
}

</script>
<style lang="less" scoped>
.test-drive-wrapper, .test-drive-box, .main-wrapper {
  box-sizing: border-box;
}
.test-drive-box {
  padding: 16px 16px 76px 16px;
  position: relative;
  .info-box {
    .text {
      margin-top: 15px;
      font-size: 14px;
      line-height: 22px;
      p {
        margin: 0;
        &:nth-child(3n) {
          margin-bottom: 25px;
        }
      }
    }
  }
  .button-box {
    padding: 16px;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 4px;
    // background-color: #fff;
  }
}
</style>
