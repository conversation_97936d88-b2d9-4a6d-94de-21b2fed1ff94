/* eslint-disable no-const-assign */
<template>
  <div class="badge-wall">
    <div class="header">
      <navigation
        title="身份墙"
        background="transparent"
        :nativeApp="nativeApp"
      />
      <div class="identity-info">
        <div class="avatar">
          <van-image
            class="imag"
            round
            width="65"
            height="65"
            :src="$loadWebpImage(identityBadgeInfo.avatarUrl)"
          />
          <van-image
            class="v-imag"
            v-if=" !identityManagementResp.isVirtualNumber && identityManagementResp.virtualNumberUrl === undefined && identityBadgeInfo.userIcon !== undefined && identityBadgeInfo.userIcon !== ' ' "
            :src="identityBadgeInfo.userIcon"
          />
        </div>
        <div class="text">
          <div class="top">
            <span>{{ identityBadgeInfo.displayName }}</span>
            <van-image
              class="badge-imag"
              v-if=" !identityManagementResp.isVirtualNumber && identityManagementResp.virtualNumberUrl === undefined && userCarWearBadge !== null && userCarWearBadge !== undefined && userCarWearBadge.badgePicture !== 'default'"
              :class="[{carOwnerBadge:userCarWearBadge.category === 0 },{achievementBadge: userCarWearBadge.category === 1 || userCarWearBadge.category === 2}]"
              :src="userCarWearBadge.badgePicture"
            />
            <!-- 针对人设号 有图片的返回的-->
            <van-image
              class="badge-imag managementResp"
              v-if=" identityManagementResp.isVirtualNumber === 1 && identityManagementResp.virtualNumberUrl !== undefined"
              :src="identityManagementResp.virtualNumberUrl"
            />
            <!-- 针对人设号 没有图片的-->
            <van-image
              class="badge-imag audi"
              v-if=" identityManagementResp.isVirtualNumber === 1 && identityManagementResp.virtualNumberUrl === undefined"
              :src="require('@/assets/wall/Audi.png')"
            />
          </div>
          <div
            class="bottom"
            v-if="wearBadge != null"
          >
            <span>{{ wearBadge.desc }}</span>
          </div>
        </div>
      </div>
      <!-- 弹出层 -->
      <van-popup
        v-model="isPopupShow"
        class="vanPopup"
        :overlay="false"
      >
        <div class="popup">
          <van-image
            class="popupImag"
            :src="require(`@/assets/wall/${popupImagSrc}`)"
          />
          <span class="popupText">{{ popupText }}</span>
        </div>
      </van-popup>
    </div>
    <div class="badge-info">
      <!-- 车主身份 -->
      <div v-if="ownershipBadges.length !== 0 ? true : false">
        <div>
          <span>车主身份</span>
        </div>
        <div
          class="car-owner-identity"
        >
          <div
            v-for="badge in ownershipBadges"
            :key="badge.badgeId"
            @click="wearOwnerBadgeClick(badge.carModel)"
          >
            <div
              class="car-owner-identity-imag"
            >
              <van-image
                class="identity-big-logo"
                :src="badge.iconUrl"
              />
              <van-image
                class="panel identity-small-logo"
                v-if="badge.wearState !== undefined && badge.wearState === 1 "
                :src="require('@/assets/wall/icon04.png')"
              />
            </div>
            <div class="identity-title">
              {{ badge.desc }}
            </div>
          </div>
        </div>
      </div>

      <!-- 活动身份 -->
      <div v-if="activeBadges.length !== 0">
        活动身份
      </div>
      <div
        class="achievement-badge"
      >
        <div
          v-for="badge in activeBadges"
          :key="badge.id"
          @click="routerDetail(badge.id,badge.levelName)"
        >
          <van-image
            class="achievement-image"
            :src="$loadWebpImage(badge.iconUrl)"
          >
          <van-image
            class="activityHook"
            v-if="wearBadge !== undefined && wearBadge.wearState === 1 && wearBadge.badgeId === badge.id"
            :src="require('@/assets/wall/icon04.png')"
          />
          </van-image>
          <span>{{ badge.levelName }}</span>
        </div>
      </div>
    </div>

    <van-popup
      class="wearPopup"
      v-model="wearPopupShow"
      overlay
    >
      <div class="wearPopupDiv">
        <div class="wearPopupTitle">
          您是否要展示选择的新徽章
        </div>
        <div
          class="sureBtn"
          @click="sureClick"
        >
          确定
        </div>
        <div
          class="cancelBtn"
          @click="cancelClick"
        >
          取消
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  Image, Icon, Popup, NavBar
} from 'vant'
import Vue from 'vue'
import { mapMutations, mapGetters } from 'vuex'
import {
  getUserAboutInfo, getAllBadges, wearIdentity, getUserInfo
} from '@/api/wall'
import navigation from './header.vue'


Vue.use(Image)
Vue.use(Image)
Vue.use(Popup)
Vue.use(NavBar)

export default {
  data() {
    return {
      isShow: false,
      popupImagSrc: 'icon12.png',
      userInfo: '', // 登录用户信息
      identityBadgeInfo: '', // 用户身份徽章信息
      wearBadge: '', // 佩戴徽章
      ownershipBadges: '', // 车主身份徽章
      activeBadges: '', // 活动徽章
      isShowHook: false, // 对钩的显示和隐藏
      isPopupShow: false,
      popupText: '',
      userCarWearBadge: '', // 用户徽章墙佩戴的徽章
      wearPopupShow: false,
      currentCarModel: '',
      pageUserId: this.$route.params.userId, // 访问页面用户id
      identityManagementResp: '', // 人设管理对象
      nativeApp: true

    }
  },
  components: {
    navigation
  },
  mounted() {
    this.setHeaderVisible(false)
    this.getUserBadgeInfo()
    this.setShowPopup()
    if (localStorage.getItem('token')) {
      this.getUserInfo()
    }
    const { nativeApp } = this.getDevice() || ''
    console.log(nativeApp, 'nativeAppnativeApp')
    if (nativeApp) {
      this.nativeApp = true
    } else {
      this.nativeApp = false
    }
  },
  methods: {
    ...mapMutations(['setHeaderVisible']),
    ...mapGetters(['getDevice']),
    toBack() {
      this.$refs.header.toBack()
    },
    wearOwnerBadgeClick(carModel) {
      if (this.userInfo.id === this.pageUserId) {
        this.wearPopupShow = true
        this.currentCarModel = carModel
      }
    },
    async getUserInfo() {
      try {
        const result = await getUserInfo()
        this.userInfo = result.data.data
      } catch (error) {}
    },
    async sureClick() {
      const result = await wearIdentity(this.currentCarModel, '1')
      const { code, message } = result.data

      this.wearPopupShow = false
      this.getUserBadgeInfo()
      this.setPopup(code)
    },
    cancelClick() {
      this.wearPopupShow = false
    },
    routerDetail(badgeId, levelName) {
      this.$router.push({ path: `/wall/identity-detail/${this.identityBadgeInfo.userId}/${badgeId}/${levelName}` })
    },
    async getUserBadgeInfo() {
      const ownerResult = await getUserAboutInfo(this.$route.params.userId)

      // 用户徽章信息
      this.identityBadgeInfo = ownerResult.data.data

      // 获取人设管理对象信息
      this.identityManagementResp = this.identityBadgeInfo.identityManagementResp
      // 获取用户佩戴的徽章墙的徽章
      this.userCarWearBadge = (this.identityBadgeInfo.badge)[0]

      // 车主点亮的身份
      const ownBadges = this.identityBadgeInfo.identityBadges
      // 获取佩戴身份
      if (this.identityBadgeInfo.identityBadge !== null) {
        this.wearBadge = this.identityBadgeInfo.identityBadge
        console.log(this.wearBadge)
      }
      // 获取车主身份
      if (ownBadges !== null && ownBadges !== undefined) {
        if (ownBadges.length !== 0) {
          this.ownershipBadges = ownBadges.filter((item) => item.type === 1)
        }
      }
      const allBadgeResult = await getAllBadges(1)
      // 所有徽章
      const allBadges = allBadgeResult.data.data
      // 所有未点亮
      const allNotLitBadges = allBadges.filter((item) => item.lightStatus === 0)
      // 所有点亮
      const allLitBadges = allBadges.filter((item) => item.lightStatus === 1)
      // 用户点亮的活动徽章
      const userLightBadges = []
      if (ownBadges !== null && ownBadges !== undefined) {
        // const i = ownBadges.filter((item) => { item.type === 2 })
        // userLightBadges = i
        ownBadges.forEach((item) => {
          if (item.type === 2) {
            userLightBadges.push(item)
          }
        })
      }

      // 从所有点亮的徽章中获取用户点亮的徽章
      const userAllLiBadges = []
      if (userLightBadges.length !== 0) {
        allLitBadges.forEach((allItem) => {
          userLightBadges.forEach((userItem) => {
            if (userItem.badgeId === allItem.id) {
              userAllLiBadges.push(allItem)
            }
          })
        })
      }

      // 点亮覆盖未点亮
      if (userAllLiBadges.length !== 0) {
        for (let i = 0; i < allNotLitBadges.length; i++) {
          userAllLiBadges.forEach((item) => {
            if (allNotLitBadges[i].identityType === item.identityType) {
              allNotLitBadges[i] = item
            }
          })
        }
      }

      this.activeBadges = allNotLitBadges.filter((item) => item.display)
      console.log(this.activeBadges)
    },
    setShowPopup() {
      const code = this.$route.params.code
      console.log(code)
      if (code !== null && code !== undefined && code !== ' ') {
        this.setPopup(code)
      }
    },
    setPopup(code) {
      this.isPopupShow = true
      if (code !== '00') {
        this.popupImagSrc = 'icon11.png'
        this.popupText = '网络出错,请稍后重试'
      } else {
        this.popupImagSrc = 'icon12.png'
        this.popupText = '佩戴成功!'
      }
      setTimeout(() => {
        this.isPopupShow = false
      }, 1000)
    }
  }
}
</script>
<style scoped lang="less">
   .panel{
          width: 32px;
          height: 32px;
          fit: 'cover'
    }
    .carOwnerBadge{
        width: 34px;
        height: 20px;
        object-fit: 'cover';
        margin-top: 0px;

    }
    .achievementBadge{
        width: 22px;
        height: 22px;
        fit: 'cover'
    }
  //  禁用点击事件
  .disabledClick{
    pointer-events:"none"
  }
   .header{
     position: relative;
     // height: 200px;
     padding-top: 45px;
     box-sizing: border-box;
     background: url('~@/assets/wall/badgebg.png');
     background-size: cover;
      ::v-deep .van-nav-bar__arrow{
       color: #000;
      }
      ::v-deep .van-nav-bar__title{
           font-weight: 700;
      }
      ::v-deep .van-nav-bar__content{
          height: 44px;
      }
  .nav-bar{
     background-color: rgba(255,255,255,0.1);
  }
     .identity-info{
       height: 100px;
      //  padding: 0px 0px 0px 12px;
       display: flex;
       padding-top: 62px;
       .avatar{
         width: 65px;
         height: 65px;
         margin-right: 9px;
         margin-left: 18px;
         position: relative;
         .imag{
           fit:"cover";
         }
         .v-imag{
           width: 16px;
           height: 16px;
           position: absolute;
           right: 0px;
           bottom: 0px;
         }
       }
       .text{
         .top{
           display: flex;
           margin-top: 14px;
           font-size: 16px;
            .badge-imag{
              fit: 'cover';
              margin-left: 5px;
            }
            .managementResp{
              width: 40px;
              height: 10px;
              margin-top: 5px;
            }
            .audi{
              width: 40px;
              height: 15px;
              margin-top: 3px;
            }
         }
         .bottom{
           font-size: 12px;
           color: #333333 ;
           margin-top: 6px;
         }
       }
     }
    // 弹出层
    .vanPopup{
      width: calc(100% - 75px);
      height: 56px;
      position: absolute;
      top: 90px;
      // left: 38px;
      padding: 16px;
      box-sizing: border-box;
      background-color: #FFFFFF ;
      font: 12px;
      .popup{
        display: flex;
        align-items: center;
        height: 24px;
        .popupImag{
          width: 24px;
          height: 24px;
          margin-right: 12px;
        }
      }
    }
   }
   .badge-info{
     padding: 24px 26px 0px 26px;
     font-size: 14px;
     font-weight: 700;
    //车主徽章
     .car-owner-badge{
       margin: 16px 0px 48px 16px;
       font-weight: 700;
       font-size: 12px;
      .car-owner-imag{
          width: 80px;
          height: 80px;
          position: relative;
           .small-logo{
             position: absolute;
             bottom: 9px;
             right: -6px;
           }
        }
        .badge-title{
           margin-left: 16px;
        }
     }
    //成就徽章
     .achievement-badge{
       display: flex;
       flex: 1;
       flex-wrap: wrap;
      //  justify-content: space-between;
       justify-content: flex-start;
       padding-right: 20px;
       padding: 23px 0 0 0;

       font-size: 12px;
       div{
            width: 33%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 44px;
            position: relative;
          .achievement-image{
            width: 80px;
            height: 80px;
            margin-bottom: 10px;
            fit:'cover'
           }
           .activityHook{
             width: 32px;
             height: 32px;
             fit:'cover';
             position: absolute;
             top: 54px;
             left: 50px;
           }
           span{
              text-align: center;
              font-size: 12px;
              height: 15px;
              font-weight: 400;
              color: #000000;
              line-height: 15px;
              width: 80px;
           }
       }
     }
    //车主身份
     .car-owner-identity{
       display: flex;
       flex-wrap: wrap;
       align-items: center;
       justify-content: space-between;
       font-size: 12px;
       padding-right: 20px;
       box-sizing: border-box;
       margin: 21px 0px 48px;
       div{
        //  margin-right: 10px;
         margin-bottom: 10px;
         display: flex;
         flex-direction: column;
         align-items: center;
        .car-owner-identity-imag{
         position: relative;
         .identity-big-logo{
            width: 129px;
            height: 14px;
            fit: 'cover'
         }
         .identity-small-logo{
           position: absolute;
           top: 5px;
           left: 113px;
         }
       }
      //  .identity-title{
      //   margin:16px 0px 0px 23px;
      //  }

       }
     }
   }

   .wearPopup{
     width: calc(100% - 20px);
  .wearPopupDiv{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 200px;
    padding: 18px 20px 24px 20px;
    background-color: #fff;
    font-size: 16px;
    box-sizing: border-box;
    .wearPopupTitle{
       margin-bottom: 20px;
       color: #000000;
       font-family: 700;
    }
    .sureBtn{
      width: 100%;
      height: 50px;
      margin-bottom: 8px;
      color: #FFFFFF;
      background-color: #000;
      border: 1px solid #000;
      text-align: center;
      line-height: 50px;
    }
    .cancelBtn{
      width: 100%;
      height: 50px;
      color: #000000;
      border: 1px solid #000;
      text-align: center;
      line-height: 50px;
    }
  }
}

</style>
