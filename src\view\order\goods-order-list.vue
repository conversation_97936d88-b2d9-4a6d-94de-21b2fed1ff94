<template>
  <div class="goods-order-list">
    <van-tabs
      v-model="activeName"
      color="#000000"
      line-height="2"
      line-width="64"
      title-inactive-color="#999999"
      title-active-color="#000000"
    >
      <van-tab
        title="全部"
        name="total"
      >
        全部
      </van-tab>
      <van-tab
        title="待支付"
        name="unpaid"
      >
        待支付
      </van-tab>
      <van-tab
        title="待发货"
        name="notshipped"
      >
        待发货
      </van-tab>
      <van-tab
        title="待收货"
        name="forgoods"
      >
        待收货
      </van-tab>
      <van-tab
        title="已完成"
        name="completed"
      >
        已完成
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import Vue from 'vue'
import { Tab, Tabs } from 'vant'

Vue.use(Tab)
Vue.use(Tabs)
export default {
  data() {
    return {
      activeName: 'total'
    }
  }
}
</script>

<style scoped lang="less">
</style>
