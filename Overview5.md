# 奥迪汽车订购H5项目 - 车辆配置接口调用时序图

## 1. 车辆配置流程概述

车辆配置是整个系统的核心功能，涉及多个接口的协调调用，包括车系获取、配置项加载、实时价格计算、配置保存等步骤。

## 2. 主要接口时序图

### 2.1 页面初始化时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Page as 配置页面
    participant Store as Vuex Store
    participant API as 配置API
    participant Server as 后端服务

    User->>Page: 进入配置页面
    Page->>Store: 检查登录状态
    Store->>API: getCarList() 获取车系列表
    API->>Server: GET /api/v1/cc/public/customSeries
    Server-->>API: 返回车系数据
    API-->>Store: 车系列表数据
    Store-->>Page: 更新车系状态

    Page->>Store: 选择默认车系
    Store->>API: getModellineList() 获取配置线
    API->>Server: GET /api/v1/cc/public/modelLine
    Server-->>API: 返回配置线数据
    API-->>Store: 配置线列表
    Store-->>Page: 显示配置选项

    Page->>Store: 初始化默认配置
    Store->>API: getExterior() 获取外观配置
    Store->>API: getModelHub() 获取轮毂配置
    Store->>API: getInterior() 获取内饰配置
    Store->>API: getPrivateOrder() 获取选装包

    par 并行获取配置数据
        API->>Server: GET /api/v1/cc/public/modelLine/configs/color_exterieur
        Server-->>API: 外观颜色数据
        and
        API->>Server: GET /api/v1/cc/public/modelLine/configs/RAD
        Server-->>API: 轮毂数据
        and
        API->>Server: GET /api/v1/cc/public/modelLine/configs/color_interieur
        Server-->>API: 内饰数据
        and
        API->>Server: GET /api/v1/cc/public/modelLine/configs/personalOption
        Server-->>API: 选装包数据
    end

    API-->>Store: 所有配置数据
    Store-->>Page: 渲染配置界面
    Page-->>User: 显示完整配置页面
```

### 2.2 外观配置选择时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Page as 外观配置页
    participant Store as Vuex Store
    participant API as 配置API
    participant Server as 后端服务

    User->>Page: 选择外观颜色
    Page->>Store: commit('updateCurrentExterior', item)
    Store->>Store: 清理相关配置状态
    Store->>Store: commit('clearSelectedConfig', ['interior', 'option'])

    Note over Store: 触发价格和交付时间计算
    Store->>Store: dispatch('setTimeAndPrice')

    par 并行计算价格和交付时间
        Store->>API: getPriceCompute() 或 getMeasurePriceCompute()
        API->>Server: POST /api/v1/cc/public/modelLine/configs/priceCompute
        Note right of Server: 参数: {modelLineId, optionIds}
        Server-->>API: 返回价格数据
        API-->>Store: 价格计算结果
        and
        Store->>API: getDeliveryTimeCompute()
        API->>Server: POST /api/v1/cc/public/modelLine/configs/deliveryTimeCompute
        Server-->>API: 返回交付时间
        API-->>Store: 交付时间数据
    end

    Store->>Store: commit('updateTotalPrice', priceData)
    Store->>Store: commit('updateDeliveryTimeData', timeData)
    Store-->>Page: 更新UI显示
    Page-->>User: 显示新的价格和交付时间

    Note over User,Server: 如果是半定制车型，需要额外查询
    alt 半定制车型 (measure === 1)
        Store->>API: measureQuery() 查询半定配置
        API->>Server: POST /api/v1/cc/measure/made/public/modelLine/configs/measureQuery
        Server-->>API: 半定配置数据
        API-->>Store: 更新半定状态
    end
```

### 2.3 内饰配置选择时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Page as 内饰配置页
    participant Store as Vuex Store
    participant API as 配置API
    participant Server as 后端服务

    User->>Page: 进入内饰配置
    Page->>Store: 获取内饰相关数据

    par 获取内饰配置项
        Store->>API: getInteriorChair() 获取座椅
        API->>Server: GET /api/v1/cc/public/modelLine/configs/vos
        Server-->>API: 座椅数据
        and
        Store->>API: getInteriorEih() 获取饰板
        API->>Server: GET /api/v1/cc/public/modelLine/configs/eih
        Server-->>API: 饰板数据
        and
        Store->>API: getInteriorSib() 获取面料
        API->>Server: GET /api/v1/cc/public/modelLine/configs/sib
        Server-->>API: 面料数据
        and
        Store->>API: getSibColorInterieur() 获取内饰颜色组合
        API->>Server: GET /api/v1/cc/public/modelLine/configs/sibColorInterieur
        Server-->>API: 内饰颜色组合数据
    end

    API-->>Store: 所有内饰配置数据
    Store-->>Page: 渲染内饰选项
    Page-->>User: 显示内饰配置界面

    User->>Page: 选择内饰配置
    Page->>Store: commit('updateCurrentII', selectedItem)
    Store->>Store: dispatch('computeA5LTotalPriceAndEquityPrice')

    Store->>API: 重新计算价格
    API->>Server: POST /api/v1/cc/public/modelLine/configs/priceCompute
    Server-->>API: 新价格数据
    API-->>Store: 更新价格
    Store-->>Page: 更新价格显示
    Page-->>User: 显示新价格
```

### 2.4 选装包配置时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Page as 选装配置页
    participant Store as Vuex Store
    participant API as 配置API
    participant Server as 后端服务

    User->>Page: 进入选装配置
    Page->>Store: 获取选装包数据
    Store->>API: getPersonalOptionPlus() 获取选装包
    API->>Server: GET /api/v1/cc/public/modelLine/configs/personalOptionPlus
    Server-->>API: 选装包数据
    API-->>Store: 选装包列表
    Store-->>Page: 渲染选装包
    Page-->>User: 显示选装包列表

    User->>Page: 选择/取消选装包
    Page->>Store: 更新选装包状态
    Store->>Store: commit('updateSelectedOptions', newOptions)

    Note over Store: 检查选装包冲突和依赖
    Store->>Store: 处理选装包互斥逻辑
    Store->>Store: 处理选装包依赖关系

    Store->>Store: dispatch('setTimeAndPrice')
    
    par 重新计算价格和交付时间
        Store->>API: getPriceCompute() 计算新价格
        API->>Server: POST /api/v1/cc/public/modelLine/configs/priceCompute
        Note right of Server: 包含所有选中的optionIds
        Server-->>API: 新的总价格
        API-->>Store: 价格数据
        and
        Store->>API: getDeliveryTimeCompute() 计算交付时间
        API->>Server: POST /api/v1/cc/public/modelLine/configs/deliveryTimeCompute
        Server-->>API: 新的交付时间
        API-->>Store: 交付时间数据
    end

    Store-->>Page: 更新价格和时间显示
    Page-->>User: 显示最新价格和交付时间
```

### 2.5 配置单生成时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Page as 配置页面
    participant Store as Vuex Store
    participant API as 配置API
    participant Server as 后端服务

    User->>Page: 点击生成配置单
    Page->>Store: 检查配置完整性
    Store->>Store: 验证必选配置项

    alt 配置完整
        Store->>Store: 收集所有配置参数
        Note over Store: 收集: modelLineId, optionIds, sibInterieurId

        alt 高定制车型
            Store->>API: getCcInfo() 生成高定配置单
            API->>Server: POST /api/v1/cc/public/
            Note right of Server: 参数: {modelLineId, optionIds, sibInterieurId, sourceId, entryPoint}
        else 半定制车型
            Store->>API: getV2CcInfo() 生成半定配置单
            API->>Server: POST /api/v1/cc/measure/made/public/carConfig
            Note right of Server: 参数: {measureId, sourceId, entryPoint}
        end

        Server-->>API: 返回配置单ID (ccid)
        API-->>Store: 配置单数据
        Store->>Store: commit('setCcid', ccid)
        Store->>Store: commit('setSkuid', skuid)

        Store->>API: autoSaveCarShoppingCart() 自动保存到购物车
        API->>Server: POST /api/v1/carShoppingCart/autoSaveCarShoppingCart
        Server-->>API: 保存成功
        API-->>Store: 保存状态

        Store-->>Page: 配置单生成成功
        Page->>Page: 跳转到报价页面
        Page-->>User: 显示配置单详情

    else 配置不完整
        Store-->>Page: 提示缺少配置项
        Page-->>User: 显示错误提示
    end
```

## 3. 关键接口详细说明

### 3.1 核心配置接口

| 接口名称 | 请求方式 | 接口地址 | 功能描述 |
|---------|---------|----------|----------|
| getCarList | GET | `/api/v1/cc/public/customSeries` | 获取车系列表 |
| getModellineList | GET | `/api/v1/cc/public/modelLine` | 获取配置线列表 |
| getExterior | GET | `/api/v1/cc/public/modelLine/configs/color_exterieur` | 获取外观颜色 |
| getModelHub | GET | `/api/v1/cc/public/modelLine/configs/RAD` | 获取轮毂配置 |
| getInteriorChair | GET | `/api/v1/cc/public/modelLine/configs/vos` | 获取座椅配置 |
| getInteriorEih | GET | `/api/v1/cc/public/modelLine/configs/eih` | 获取饰板配置 |
| getInteriorSib | GET | `/api/v1/cc/public/modelLine/configs/sib` | 获取面料配置 |
| getPrivateOrder | GET | `/api/v1/cc/public/modelLine/configs/personalOption` | 获取选装包 |

### 3.2 价格计算接口

| 接口名称 | 请求方式 | 接口地址 | 功能描述 |
|---------|---------|----------|----------|
| getPriceCompute | POST | `/api/v1/cc/public/modelLine/configs/priceCompute` | 高定制价格计算 |
| getMeasurePriceCompute | POST | `/api/v1/cc/measure/made/public/modelLine/configs/measurePriceCompute` | 半定制价格计算 |
| getDeliveryTimeCompute | POST | `/api/v1/cc/public/modelLine/configs/deliveryTimeCompute` | 交付时间计算 |

### 3.3 配置单生成接口

| 接口名称 | 请求方式 | 接口地址 | 功能描述 |
|---------|---------|----------|----------|
| getCcInfo | POST | `/api/v1/cc/public/` | 生成高定制配置单 |
| getV2CcInfo | POST | `/api/v1/cc/measure/made/public/carConfig` | 生成半定制配置单 |
| updateCcInfo | PUT | `/api/v1/cc/` | 更新配置单 |

## 4. 数据流转说明

### 4.1 配置数据结构
```javascript
// 配置状态数据结构
const configState = {
  currentCarSeriesData: {},      // 当前车系数据
  currentModelLineData: {},      // 当前配置线数据
  currentExterior: {},           // 当前外观配置
  currentHub: {},                // 当前轮毂配置
  currentII: {},                 // 当前内饰配置
  selectedOptions: [],           // 已选选装包
  totalPrice: 0,                 // 总价格
  deliveryTimeData: {}           // 交付时间数据
}
```

### 4.2 接口调用参数
```javascript
// 价格计算参数
const priceParams = {
  modelLineId: "配置线ID",
  optionIds: ["选项ID数组"]
}

// 配置单生成参数
const ccParams = {
  modelLineId: "配置线ID",
  optionIds: ["选项ID数组"],
  sibInterieurId: "内饰组合ID",
  sourceId: "来源ID",
  entryPoint: "入口点标识"
}
```

## 5. 性能优化策略

### 5.1 接口调用优化
- **并行请求**: 初始化时并行获取多个配置项数据
- **防抖处理**: 用户快速切换配置时，延迟价格计算请求
- **缓存策略**: 缓存车系和配置线数据，减少重复请求

### 5.2 用户体验优化
- **Loading状态**: 在接口调用期间显示加载状态
- **乐观更新**: 先更新UI，再调用接口验证
- **错误处理**: 接口失败时提供友好的错误提示

## 6. 错误处理机制

### 6.1 接口错误处理
```javascript
// 统一错误处理
const handleApiError = (error, context) => {
  console.error(`${context} 接口调用失败:`, error)
  
  if (error.code === '401') {
    // 跳转登录
    router.push('/login')
  } else if (error.code === '500') {
    // 服务器错误
    Toast.fail('服务器繁忙，请稍后重试')
  } else {
    // 业务错误
    Toast.fail(error.message || '操作失败')
  }
}
```

### 6.2 数据一致性保证
- **状态回滚**: 接口失败时恢复之前的配置状态
- **数据校验**: 接口返回数据的完整性校验
- **重试机制**: 关键接口失败时自动重试

## 总结

车辆配置过程涉及复杂的接口调用时序，通过合理的架构设计和优化策略，确保了用户配置体验的流畅性和数据的一致性。时序图清晰展示了各个环节的交互关系，为系统维护和优化提供了重要参考。
