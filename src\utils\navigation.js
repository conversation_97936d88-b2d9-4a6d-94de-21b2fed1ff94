import wxsdk from 'weixin-js-sdk'

/**
 * 给 url 添加参数
 * @param {String} url
 * @param {Object} query
 * @return {String}
 */
function appendQuery(url, query) {
  const _url = url.indexOf('?') < 0 ? `${url}?` : url.substring(0, url.length - 1)
  let qs = ''
  for (const key in query) {
    const value = query[key] !== undefined ? query[key] : ''
    qs += `&${key}=${encodeURIComponent(value)}`
  }
  return `${_url}${qs.substring(1)}`
}

/**
 * 小程序、h5公共跳转
 *
 * @export
 * @param {*} {
 *   relativePath,
 *   path: absolutePath,
 *   query,
 *   h5Router
 * }
 */
export function navigationMiniAppUrl({
  path: relativePath,
  query
}) {
  const { origin } = window.location
  console.log('origin', origin)
  console.log('relativePath', relativePath)
  console.log('query', query)
  console.log('window.__wxjs_environment', window.__wxjs_environment)
  const path = `${origin}/mallh5/index.html#${relativePath}`
  if (window.__wxjs_environment === 'miniprogram') {
    // 小程序内置浏览器环境
    const { miniProgram } = wxsdk
    miniProgram.getEnv((res) => {
      if (res.miniprogram) {
        const url = appendQuery(path, query)
        console.log('url', url)
        // 走在小程序的逻辑
        miniProgram.navigateTo({
          url: `/pages/web/index?url=${encodeURIComponent(url)}`,
          success: function () {
            console.log('success')
          },
          fail: function () {
            console.log('fail')
          },
          complete: function () {
            console.log('complete')
          }
        })
      }
    })
  }
}
