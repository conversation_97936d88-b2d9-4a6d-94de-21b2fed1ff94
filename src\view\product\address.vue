<template>
  <div class='_address'>
    <div class="_adre-box border-bottom" v-if="address">
      <div class="_adre-box-heard" @click="onAddress"  v-if="orderType === 1">
        <div v-if="!address.addreType">收货地址</div>
        <van-icon name="arrow" color='#999'/>
      </div>
      <div class="_adre-box-content">
        <div>{{address.receiverName}}</div>
        <div style="margin-left: 20px;">{{address.mobile}}</div>
      </div>
      <div class="_adre-box-foot">{{address.province+" "+ address.city +" "+ address.district+" "+ address.details}}</div>
    </div>
    <div class="adre_null" v-else @click="onAddress">
      <div class="adre_null-box border-bottom">
        <p>请填写收货地址</p>
        <van-icon name="arrow" color='#999' />
      </div>
    </div>
  </div>
</template>

<script>
  import { callNative } from '@/utils'
  import storage from "../../utils/storage";

  export default {
    data() {
      return {

      }
    },
    props: {
      address: null,
      orderType: null,
    },
    
    mounted() {

    },
    methods: {
      onAddress() {
        if(this.orderType > 1) return
        callNative('openRoutePath', { path: 'scaudi://user/address/list' })
        callNative('audiSetAddress', { type: 'address' }).then((res) => {
          console.log('audiSetAddress', res)
          if (res) {
            this.$store.commit('saveappUserAddress', res)
          }
        })
        console.log(address)
      },
    }
  }
</script>

<style lang='less' scoped>
  ._adre-box {
    padding: 6px 16px;
    display: flex;
    flex-flow: column;
    font-size: 12px;
    color: #000000;
    box-sizing: border-box;
    font-family: "Audi-Normal";

    &-heard {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      font-size: 16px;
      padding: 10px 0;
    }

    &-content {
      display: flex;
      align-items: center;
      line-height: 22px;
    }

    &-foot {
      padding: 6px 0;
      line-height: 15px;
    }
  }
  ._adre-box-content{
    font-size: 12px;
  }
  .adre_null {
    width: 100%;
    padding: 0 16px;
    box-sizing: border-box;
    
    &-box{
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 56px;
    }
  }
  .border-bottom{
    border-bottom: 1px solid #E5E5E5;
  }
</style>
