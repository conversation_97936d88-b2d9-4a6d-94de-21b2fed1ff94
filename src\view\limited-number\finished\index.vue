<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-05-07 11:08:12
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-05-28 21:12:02
 * @FilePath     : \src\view\limited-number\finished\index.vue
 * @Descripttion : 典藏号选择成功
-->
<script src="./index"></script>
<template>
  <div
    :class="
      ['limited-number-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <!--  :listening-native-back="true"
      :destroy-native-back="destroyNativeBack" -->
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      listening-emit-back
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`}"
    >
      <div
        class="limited-number-box code-box"
        data-flex="dir:top"
      >
        <div
          class="status-wrap"
          data-flex="dir:top cross:center main:center"
          data-block
        >
          <div class="status-box">
            <div
              class="badge-box"
            >
              <div
                class="number"
                v-if="limitNumber"
              >
                <img
                  class="num"
                  v-for="(n,i) in limitNumberImgs"
                  :key="i"
                  :src="require(`@/assets/limitNumber/number${n}.png`)"
                >
              </div>
            </div>
            <p>恭喜您！<br>您的专属限量号匹配成功！</p>
          </div>
        </div>
        <div
          class="button-box"
        >
          <audi-button
            text="查看限量号证书"
            color="black"
            height="56px"
            @click="handleCheckMineNumberBtn"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
@import url(../index.less);
.code-box {
  height: 100%;
}
.status-wrap {
  .status-box {
    text-align: center;
    position: relative;
    top: -40px;
    .badge-box {
      width: 280px;
      height: 108px;
      background: url("../../../assets/img/icon-q5e2.png") 50% no-repeat;
      background-size: cover;
      padding: 62px 0 0 42px;
      box-sizing: border-box;
      .num {
        width: 12px;
        height: 18px;
        margin: 0 -1px;
        opacity: .8;
      }
      .sprit {
        position: relative;
        margin: -6px 0 0 -2px;
        width: 13px;
      }
    }
    p {
      font-size: 18px;
      line-height: 180%;
    }
  }
}
</style>
