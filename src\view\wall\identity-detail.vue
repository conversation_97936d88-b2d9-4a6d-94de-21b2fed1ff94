/* eslint-disable vue/no-dupe-keys */
<template>
  <div class="badge-wall-detail">
    <div class="header">
      <navigation
        class="navigation"
        :title="navBarTitle"
        background="transparent"
        :nativeApp="nativeApp"
      />
    </div>
    <div class="badge-info">
      <van-image
        class="image"
        :src="showBadge.iconUrl"
      />
      <span class="badge-name">{{ showBadge.levelName }}</span>
      <span class="detail-info">{{ showBadge.obtainCondition }}</span>
    </div>

    <!-- 按钮 -->
    <!-- <div
      class="universeBtn"
      @click="universeClick"
      v-if="userInfo.id === pageUserId && showBadge.magicUrl && showBadge.lightStatus === 1 "
    >
      进入元宇宙领取徽章

      <div class="getMagicBadge"/>
    </div> -->

    <div
      class="btnWrapper"
      v-if="showBadge.lightStatus === 1 && userInfo.id === pageUserId"
    >
      <div
        class="lightTime"
      >
        {{ identityBadge.lightTime|data_format }}
      </div>

      <div
        class="btn"
        @click="wearIdentityClick('1',identityBadge.carModel)"
      >
        设置主页展示
      </div>
    </div>
    <Header
      ref="header"
      v-show="false"
    />
  </div>
</template>

<script>
import { Image, Button, NavBar } from 'vant'
import Vue from 'vue'
import { mapMutations, mapGetters } from 'vuex'
import {
  getUserAboutInfo, wearIdentity, getAllBadges, getUserInfo
} from '@/api/wall'
import Header from '@/components/header.vue'
import { callNative } from '@/utils/index'
import navigation from './header.vue'

Vue.use(Image, Button)
Vue.use(NavBar)


export default {
  components: {
    navigation,
    Header
  },
  data() {
    return {
      screeHeight: document.body.clientHeight,
      identityBadge: '', // 身份徽章
      showBadge: '',
      userInfo: '', // 登录用户信息
      pageUserId: this.$route.params.userId, // 访问页面用户id
      navBarTitle: '',
      navigationBarHeight: 50,
      statusBarHeight: 0,
      isUniverseShow: {},
      nativeApp: true
    }
  },
  created() {
    this.setHeaderVisible(false)
    this.getUserAboutInfo()
    if (localStorage.getItem('token')) {
      this.getUserInfo()
    }
    this.navBarTitle = this.$route.params.levelName
    this.getHeight()
  },
  mounted() {
    // this.$nextTick(() => {
    //   const navigation = document.querySelector('.navigation')
    //   navigation.style.display = 'block'
    //   navigation.style.zIndex = ''
    // })
    const { nativeApp } = this.getDevice() || ''
    console.log(nativeApp, 'nativeAppnativeApp')
    if (nativeApp) {
      this.nativeApp = true
    } else {
      this.nativeApp = false
    }
  },
  methods: {
    ...mapMutations(['setHeaderVisible']),
    ...mapGetters(['getDevice']),
    universeClick() {
      window.location.replace(this.showBadge.magicUrl)
    },
    async  getUserAboutInfo() {
      try {
        const userId = this.$route.params.userId
        // const levelName = this.$route.params.levelName
        const badgeId = parseInt(this.$route.params.badgeId)
        const allResult = await getAllBadges(1)
        const allBadges = allResult.data.data
        console.log('----------allBadges--------------', allBadges)
        const showBadgeArr = allBadges.filter((item) => item.id === badgeId)
        if (showBadgeArr === null) {
          this.showBadge = ''
        }
        this.showBadge = showBadgeArr[0]
        console.log('----------this.showBadge----------', this.showBadge)
        // 获取点亮用户的身份信息
        const result = await getUserAboutInfo(userId)
        const identityBadges = result.data.data.identityBadges
        const wear = identityBadges.filter((item) => item.badgeId === badgeId)
        this.identityBadge = wear[0]
        console.log(this.identityBadge)
      } catch (error) {}
    },
    // 获取导航栏高度??
    async getHeight() {
      const data = await callNative('navigationBarHeight', {})
      this.statusBarHeight = data.statusBarHeight
      this.navigationBarHeight = data.navigationBarHeight
    },
    async wearIdentityClick(wearState, carModel) {
      try {
        const result = await wearIdentity(carModel, wearState)
        const { code, message } = result.data
        this.$router.push({
          name: 'identity-wall',
          params: {
            code: code
          }
        })
      } catch (error) {}
    },
    async getUserInfo() {
      try {
        const result = await getUserInfo()
        this.userInfo = result.data.data
      } catch (error) {}
    },
    toBack() {
      history.back()
    }
  },
  filters: {
    data_format(lightTime) {
      if (lightTime !== null && lightTime !== undefined) {
        const dataArr = lightTime.split(' ')[0].split('-')
        return `点亮 于${dataArr[0]}年${dataArr[1]}月${dataArr[2]}日`
      }
    }
  }
}
</script>

<style scoped lang="less">
  #app{
    font-family: "Normal";
  }

.header{
  // margin-bottom: 44px;

  ::v-deep .van-nav-bar__arrow{
       color: #000;
  }
  ::v-deep .van-nav-bar__title{
       font-weight: 700;
  }
  ::v-deep .van-nav-bar__content{
      height: 40px;
  }
  .nav-bar{
     background-color: rgba(255,255,255,0.1);
  }
}
    // 徽章信息
     .badge-info{
       display: flex;
       flex-direction: column;
       justify-content: center;
       align-items: center;
       margin-top: 155px;
       margin-bottom: 42px;
       .image{
         width: 208px;
         height: 208px;
       }
       .badge-name{
          margin: 5px 0px 8px;
          font-size: 18px;
          font-weight: 700;
       }
       .detail-info{
        height: 17px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.5);
        line-height: 17px;
       }
     }

    // 按钮
    .btnWrapper{
      width: 100%;
      // height: 86px;
      position:fixed;
      bottom: 50px;
      color: #ffffff;
      .lightTime{
          width: 100%;
          text-align: center;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.5);
          margin-bottom: 16px;
        }
      .btn{
        width: calc(100% - 32px);
        line-height: 56px;
        text-align: center;
        margin: 0 auto;
        background-color: #000;
      }
    }
    .universeBtn{
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: calc(100% - 32px);
      height: 40px;
      margin: 0  auto;
      padding: 0px 12px;
      box-sizing: border-box;
      background-color: #F2F2F2;
      color: #333333;
      font-size: 14px;
      .getMagicBadge{
        height: 24px;
        width: 64px;
        background-image: url('~@/assets/wall/magicBtnBg.png');
        background-size: contain;
      }
    }
</style>
