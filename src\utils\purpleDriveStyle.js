

class InitPrupleStyle {

    constructor(fromType) {
        this.styleId = 'dynamic-styles'; // 标识动态样式
    }

    // 动态插入主题样式
    addStyles(fromType) {
        //是否来自purple项目 
        if(fromType != 'fromPurple') return;

        // 判断样式是否已添加，避免重复添加
        if (!document.getElementById(this.styleId)) {
            const style = document.createElement('style');
            style.id = this.styleId;
            style.innerHTML = `
            
                *{ 
                    background: #191919 !important;
                    color: #FFF !important;  
                    border-color:rgba(255, 255, 255, 0.10) !important;
                }

                svg{
                    background:transparent !important;
                }
                `; 
            document.head.appendChild(style);
        }
    }

    // 移除动态样式
    removeStyles() {
        const style = document.getElementById(this.styleId);
        if (style) {
            style.remove();
        }
    }
} 

export default new InitPrupleStyle();