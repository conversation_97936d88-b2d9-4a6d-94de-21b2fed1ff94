<template>
  <div style="padding-bottom: 100px;">
    <img
      @click="imagePreview"
      v-show="lazy"
      @load="handleLoad"
      :src="imgurl | imgFix(640, true)"
    >
    <div
      class="btn-wrapper"
      v-if="showDownloadBtn"
    >
      <AudiButton
        text="一键下载"
        v-if="wechatState === false"
        color="black"
        height="56px"
        font-size="16px"
        @click="downloadConfigpdf"
      />
    </div>
  </div>
</template>

<script>
import { ImagePreview, Toast } from 'vant'
import wxsdk from 'weixin-js-sdk'
import { mapState } from 'vuex'
import Vue from 'vue'
import AudiButton from '@/components/audi-button'
import { createOfflineContract } from '@/api/api'
import { callNative, handleTimeout } from '@/utils'
import confiUrl from '@/config/url'
import { A7MR } from '@/view/newConfigration/car/a7mr'

const baseConfigrationOssHost = confiUrl.BaseConfigrationOssHost

Vue.use(Toast)

export default {
  components: { AudiButton },
  data() {
    return {
      lazy: false,
      wechatState: false,
      imgurl: '',
      pdfurl: '',
      showDownloadBtn: false // 是否显示下载按钮，小程序里面不展示
    }
  },
  computed: {
    ...mapState({
      carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn
    })
  },
  mounted() {
    callNative('toggleLoading', { show: '1' })
    this.getimgurl()
    const { env } = this.$route.query
    this.showDownloadBtn = env !== 'minip'

    const ua = navigator.userAgent.toLowerCase() // 获取判断用的对象
    if (ua.match(/MicroMessenger/i) === 'micromessenger') {
      this.wechatState = true
      console.log('是微信浏览器')
    }
  },
  methods: {
    imagePreview() {
      ImagePreview([this.$loadWebpImage(this.imgurl)])
    },
    getimgurl() {
      const carModelName = this.$route.query?.carModelName ?? this.carModelName
      this.$store.commit('setTitle', `${carModelName}装备价格表`)

      if (carModelName.indexOf('Q5') != -1) {
        // const q5e2022Measure = 'G4ICF3002,G4IBF3001,G4ICF3001,G4IBC3001,G4ICC3001' // Q5e 2022款 私人高定
        const q5e2023Person = 'G4IBF3003,G4ICF3007,G4IBC3003,G4ICC3006,G4IBC3004,G4ICC3007' // Q5e 2023款 私人高定

        // 爆款推荐
        const q5e2023Recom = [
          'G4IBC3005', // 40星骑士锦衣
          'G4ICC3008', // 40星骑士机甲
          'G4ICC3003', // 40黑武士机甲
          'G4ICC3005', // 40白法师机甲
          'G4ICC3004', // 40影武士机甲
          'G4ICF3004', // 50黑武士机甲
          'G4ICF3006', // 50白法师机甲
          'G4ICF3005' // 50影武士机甲
        ]

        const modelLineCode = this.$route.query?.modelLineCode
        if (q5e2023Recom.includes(modelLineCode)) {
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表/SAIC Audi Q5 e-tron 特别版 配置表1.jpg`
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表/SAIC Audi Q5 e-tron 特别版 配置表1.pdf`
        } else if (q5e2023Person.includes(modelLineCode)) {
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表/SAIC Audi Q5 e-tron MP23 配置表1.jpg`
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表/SAIC Audi Q5 e-tron MP23 配置表1.pdf`
        } else {
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/%E9%85%8D%E7%BD%AE%E8%A1%A80210/Q5_e-tron%E9%85%8D%E7%BD%AE%E8%A1%A8.jpg`
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/Audi%20Q5%20e-tron%20%E9%85%8D%E7%BD%AE%E8%A1%A8%E4%BB%B7%E6%A0%BC%E7%89%88.pdf`
        }
      } else if (carModelName.indexOf('Q6') != -1) {
        const { modelLineCode } = this.$route.query
        // G6ICBY003 Audi Q6 50 TFSI quattro Roadjet 行云型 黑武士
        // G6ICBY004 Audi Q6 50 TFSI quattro Roadjet 行云型 影武士
        const str = [
          'G6ICAY002', // 45 影武士 7座 23款
          'G6ICAY003', // 45 影武士 6座 23款
          'G6ICAY004', // 45 黑武士 7座 23款
          'G6ICAY005', // 45 黑武士 6座 23款
          'G6ICBY004',
          'G6ICBY003',
          'G6ICAY013', // 45 逐云版 7座
          'G6ICAY014', // 45 逐云版 6座
          'G6ICAY011', // 45 黑武士 6座 24款
          'G6ICAY009', // 45 黑武士 7座 24款
          'G6ICAY010', // 45 影武士 7座
          'G6ICBY007', // 50 影武士 7座
          'G6ICAY006', // 45 rs line
          // new version
          'G6ICAY020', // 45 黑武士 6座 24款
          'G6ICAY018', // 45 黑武士 7座 24款
          'G6ICAY021', // 45 影武士 7座 24款
          'G6ICAY019', // 45 影武士 6座 24款
          'G6ICAY015', // 45 rs line
          'G6ICBY010', // 50 影武士
          'G6ICBY009' // 50 黑武士
        ]
        if (str.includes(modelLineCode)) {
          // special pdf
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/%E9%85%8D%E7%BD%AE%E8%A1%A8/%E5%A5%A5%E8%BF%AA_Q6%E7%89%B9%E5%88%AB%E7%89%88.pdf`
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/%E9%85%8D%E7%BD%AE%E8%A1%A8/%E5%A5%A5%E8%BF%AA_Q6%E7%89%B9%E5%88%AB%E7%89%88%E9%A2%84%E8%A7%88%E5%9B%BE.jpeg`
        } else {
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/%E9%85%8D%E7%BD%AE%E8%A1%A80210/%E5%A5%A5%E8%BF%AA_Q6%E9%85%8D%E7%BD%AE%E8%A1%A8.png`
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/%E9%85%8D%E7%BD%AE%E8%A1%A80210/%E5%A5%A5%E8%BF%AA_Q6%E9%85%8D%E7%BD%AE%E8%A1%A8.pdf`
        }
      }else if (carModelName.indexOf('A5L') != -1) {
        const { modelLineCode } = this.$route.query
        // G6ICBY003 Audi Q6 50 TFSI quattro Roadjet 行云型 黑武士
        // G6ICBY004 Audi Q6 50 TFSI quattro Roadjet 行云型 影武士
        const a5str = [
          "F08BZY003",
          "F08BZG001",
          "F08BZG002",
          "F08BZY001",
          "F08BZY002",
          "F08BAY001",
          "F08BAY002"
        ]
        if (a5str.includes(modelLineCode)) {
          // special pdf
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表20240506/a5l/config.pdf`
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表20240506/a5l/1.jpg`
        } else {
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表20240506/a5l/config.pdf`
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表20240506/a5l/1.jpg`
        }
      } else {
        const { special, modelLineCode } = this.$route.query

        /**
         * A7mr车型装备表
         */
        const mrCar = A7MR.find((item) => item.code === modelLineCode)
        if (mrCar) {
          // 2025款
          if (mrCar.year === 2025) {
            if (mrCar.isSpecial) {
              this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/1202奥迪A7L配置表_digital_特殊版装备表.jpg`
              this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/2025奥迪A7L配置表_digital_特殊版装备表.pdf`
            } else {
              this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/1202奥迪A7L配置表_digital_常规版装备表.jpg`
              this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/2025奥迪A7L配置表_digital_常规版装备表.pdf`
            }
          } else {
            if (mrCar.isSpecial) {
              this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7LMR装备表_特别版.jpg`
              this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7LMR装备表_特别版.pdf`
            } else {
              this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7LMR装备表.jpg`
              this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7LMR装备表.pdf`
            }
          }
          return
        }


        // 498BZG004 筑梦新生版
        if (special && modelLineCode !== '498BZG004') { // 爆款推荐车型
          const carModelName = this.$route.query?.carModelName ?? this.carModelName
          // 筑梦青春版
          if (carModelName == '筑梦青春版') {
            this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7L装备表_特别版_黑武士白法师先行先见预览图_无青春版.jpg`
            this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7L装备表_特别版_无青春版.pdf`
            return
          }
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7L装备表_特别版_黑武士白法师先行先见预览图.jpg`
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7L装备表_特别版_.pdf`
        } else {
          this.imgurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7L装备表_预览图.jpg`
          this.pdfurl = `${baseConfigrationOssHost}/audicc/app/配置表0210/上汽奥迪A7L装备表_.pdf`
        }
      }
      // 因为安卓HTTPS证书验证的问题，我们需要将https先变成http
      this.pdfurl = this.pdfurl.replace('https://audi-oss.saic-audi.mobi/', 'http://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/')
    },

    // 一键下载(调用native)
    async downloadConfigpdf() {
      const carModelName = this.$route.query?.carModelName ?? this.carModelName
      callNative('downloadFile', { url: this.pdfurl, fileName: `上汽${carModelName}_装备价格表.pdf`, type: 'pdf' }).then((res) => {
        Toast({
          type: 'success',
          message: '下载成功',
          icon: require('../../assets/img/success.png')
        })
      })
    },
    handleLoad(e) {
      callNative('toggleLoading', { show: '0' })
      this.lazy = true
    }
  }
}
</script>

<style lang='less' scoped>
  .btn-wrapper {
    position: fixed;
    bottom: 15px;
    padding: 0 15px;
    width: 100%;
    box-sizing: border-box;
  }

  .aButton {
    display: inline-block;
    height: 56px;
    font-size: 16px;
    background-color: black;
    color: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
</style>
<style>
  .el-loading-spinner i{
    color: #fff;
    font-size: 24px;
  }
  .el-loading-spinner .el-loading-text{
    color: #fff;
  }
</style>
