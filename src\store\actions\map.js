import AMapLoader from '@amap/amap-jsapi-loader'

export default {
  // 初始化高德地图
  initGD({ commit }) {
    return new Promise((res) => {
      // 挂载高德地图
      AMapLoader.load({
        key: '14e5933fd8cfbc2be9b2ab98e74feefc',
        plugins: ['AMap.Geocoder', 'AMap.Geocoder', 'AMap.Geolocation', 'AMap.Autocomplete', 'AMap.Marker', 'AMap.Driving', 'AMap.PlaceSearch'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        Loca: {}, // 是否加载 Loca， 缺省不加载
        AMapUI: {
          version: '1.1',
          plugins: ['overlay/SimpleMarker']
        }
      }).then((data) => {
        if (data) {
          res()
          commit('updateIsLoadedGDMap', data)
        }
      }).catch((err) => {
        res()
        console.error(err, '高德初始化失败')
      })
    })
  }

}
