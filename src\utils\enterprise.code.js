/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-12-05 11:20:39
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-12-05 11:38:00
 * @FilePath     : \src\utils\enterprise.code.js
 * @Descripttion :
 */
/**
 * 验证统一社会信用代码
 * @param { string } code 统一社会信用代码
 * @returns {
 *   isPass, // 验证是否通过，默认通过，为true,，否则为false
 *   errorMessage, // 错误信息，isPass为true则为''
 * }
 *
 */
export default function checkSocialCreditCode(code) {
  const reg = /^\w\w\d{6}\w{9}\w$/ // 18位及正则校验

  // 空值直接返回false
  if (!code) {
    return false
  } if (code.length !== 18 || !reg.test(code)) {
    return false
  }
  const letter = '0123456789ABCDEFGHJKLMNPQRTUWXY' // 统一社会信用代码可用字符 不含I、O、S、V、Z
  const str = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28] // 统一社会信用代码相对应顺序的加权因子

  let ci // 统一社会信用代码相应顺序的值
  let wi // 统一社会信用代码相应顺序的加权因子
  let total = 0 // 计算结果

  // 数值与加权因子相乘之和
  for (let i = 0; i < code.length - 1; i++) {
    ci = letter.indexOf(code[i])
    wi = str[i]
    total += ci * wi
  }

  // 最后一位校验
  let logicCheckCode = 31 - (total % 31)
  if (logicCheckCode === 31) logicCheckCode = 0
  logicCheckCode = letter[logicCheckCode]
  if (logicCheckCode !== code.slice(17)) {
    return false
  }
  return true
}
