<template>
  <div class="hub">
    <div class="hub-wrapper">
      <img
        @click="changeFullCar"
        class="exterior-3dIcon"
        v-show="hasPrice&&idx==='0'"
        src="../../assets/img/icon01.png"
        alt=""
      >
      <div class="hub-wrapper-img">
        <img
          :class="[fullCar ? 'hub-fullcarimg' : 'hub-hubimg']"
          :src="mainHubImg | imgFix(450)"
          alt=""
        >
        <img
          v-show="fullCar"
          v-for="(img, i) in extraImg"
          :key="i"
          class="hub-extraImg"
          :src="img | imgFix(640)"
          alt=""
        >
      </div>
    </div>
    <div class="hub-main">
      <div class="main-hubNameCn">
        {{ currentModelHub.optionName }}
      </div>
      <div
        class="main-descText"
        v-if="hasPrice"
      >
        {{ currentModelHub.price | finalFormatPrice }}
      </div>
      <div class="main-hubBox">
        <div
          v-for="item in modelHubList"
          :key="item.optionId"
          @click="selectedHub(item)"
          :class="[
            'main-imgItem',
            item.optionId === (currentModelHub && currentModelHub.optionId)
              ? 'selected'
              : '',
          ]"
        >
          <img
            :src="BaseConfigrationOssHost + item.imageUrl | imgFix(140)"
            alt=""
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import url from '@/config/url'
import { checkV2 } from '../../utils'
import carImgMap from '@/config/carImgMap'

const OSS_URL = url.BaseConfigrationOssHost

export default {
  props: {},
  data() {
    return {
      fullCar: false,
      // fullCarImg: null,
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      hasPrice: false
    }
  },
  watch: {
    // 'selectCarInfo.modelLineId': {
    //   async handler(next) {
    //     if (next) {
    //       if (!checkV2(this.selectCarInfo.modelLineId)) {
    //         await this.$store.dispatch('getModelHubList')
    //       }
    //     }
    //   },
    //   immediate: true
    // },
    async currentModelHub(next) {
      this.hasPrice = true

      if (next) {
        if (checkV2()) {
          await this.$store.dispatch('measureQuery', { step: '3' })
        }
      }
    }
  },
  computed: {
    ...mapState([
      'idx',
      'selectCarInfo',
      'currentExColor',
      'currentModelHub',
      'currentInteriorChair',
      'currentSibColorInterieur',
      'currentInteriorEih',
      'currentOptionsList',
      'currentPrice'
    ]),
    ...mapGetters(['modelHubList']),
    getImg() {
      const {
        modelHubList, currentModelHub, currentExColor, fullCar
      } = this
      return {
        modelHubList,
        currentModelHub,
        currentExColor,
        fullCar
      }
    },

    mainHubImg() {
      const { seriesName, modelLineCode } = this.selectCarInfo
      const imgName = this.currentModelHub.imageUrl

      if (!seriesName || !modelLineCode || !imgName) return ''

      const carName = seriesName.toLowerCase()

      // fullCar: A7l 才有切换入口
      if (this.fullCar) {
        const defaultImg = carImgMap[seriesName].defaltCarImg

        let color
        if (this.currentExColor.optionCode) {
          color = carImgMap[seriesName][modelLineCode][this.currentExColor.optionCode]
        } else {
          color = 'blue'
        }

        return `${OSS_URL}/ccpro-backend/${carName}/angle/car/${color}/${defaultImg}`
      }

      return `${OSS_URL + imgName}`
    },

    extraImg() {
      const { seriesName, modelLineCode } = this.selectCarInfo
      if (!seriesName || !modelLineCode) return []
      if (seriesName !== 'a7l') return []

      if (this.currentModelHub.optionCode) {
        const extraImg = carImgMap[seriesName].defaultHubImg
        const imgName = extraImg[this.currentModelHub.optionCode] || ''
        return [
          imgName ? `${OSS_URL}/ccpro-backend/a7l/angle/car/options/hub/${imgName}` : ''
        ]
      }
      return []
    }
  },
  methods: {
    selectedHub: function (hub) {
      let arr = this.currentOptionsList && this.currentOptionsList.map((res) => res.optionName)
      arr = arr.join('/')
      this.$sensors.track('hubSelection', {
        model_name: this.selectCarInfo.modelLineName,
        price: this.currentPrice,
        cars_appearance: this.currentExColor.optionName,
        hub_model: hub.optionName,
        interior_type: `${this.currentInteriorChair?.optionName}/${this.currentSibColorInterieur?.sibName}/${this.currentSibColorInterieur.interieurName}/${this.currentInteriorEih.optionName}`,
        personal_tailor: arr
      })

      this.$store.commit('setCurrentModelHub', hub)
    },
    changeFullCar: function () {
      this.fullCar = !this.fullCar
    }
  }
}
</script>

<style lang="less" scoped>
.hub-wrapper {
  overflow-x: hidden;
  position: relative;
  .exterior-3dIcon {
    width: 52px;
    height: 52px;
    position: absolute;
    top: 18px;
    left: 24px;
    z-index: 2;
  }
}
.hub-wrapper-img {
  height: 220px;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
  > img {
    height: 350px;
    width: auto;
  }
  .hub-hubimg {
    height: 220px;
    object-fit: contain;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }
  .hub-fullcarimg,
  .hub-extraImg {
    position: absolute;
    left: 20%;
  }
}

.hub-main {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
  line-height: 20px;
  .main-hubNameCn {
    color: #000000;
    margin-top: 32px;
    margin-bottom: 16px;
  }
  .main-descText {
    color: #333333;
    margin-bottom: 32px;
  }
  .main-hubBox {
    width: 100%;
    display: inline-flex;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    .main-imgItem {
      box-sizing: border-box;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 70px;
      height: 70px;
      padding: 6px;
      margin-right: 16px;
      &:first-of-type {
        margin-left: 16px;
      }
      &:last-of-type {
        margin-right: 16px;
      }
      &.selected {
        border: 1px solid #000;
      }
      > img {
        width: 58px;
        height: 58px;
      }
    }
  }
}
</style>
