<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-09 13:44:17
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-09-08 12:43:36
 * @FilePath     : \src\view\order\buyer-info.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['buyer-info-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
    >
      <template #right v-if="fields && +update && orderId">
        <span
          class="right-btn"
          @click="handleGoToEditOrderData"
        >更改</span>
      </template>
    </header-custom>
    <div
      class="main-wrapper"
      id="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <div class="order-forms-box">
        <order-buyer
          :buyer-info="buyerInfo"
          :encryption=true
        />
        <van-cell-group class="lan-cell-group lan-list-box" v-if="fields && +update && orderId && env === 'minip'" @click="handleGoToEditOrderData">
          <div class="cell-box">
            <div
              class="cell-list"
              data-flex="main:justify"
            >
              <div class="title" data-flex="cross:center"><span>更改信息</span><van-icon class="arrow" name="arrow" /></div>
            </div>
          </div>
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Icon,
  Cell,
  CellGroup
} from 'vant'
import HeaderCustom from '@/components/header-custom.vue'
import {
  modifyBuyType, certificateJson, paymentMethod
} from '@/config/constant'
import { getMyOrders } from '@/api/api'

import { ORDER_STATUS_DISTRICT } from '@/config/conf.data'
import OrderBuyer from './buyer/buyer.vue'

Vue.use(Cell).use(CellGroup).use(Icon)

export default {
  components: {
    'header-custom': HeaderCustom,
    'order-buyer': OrderBuyer
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      goods: {},
      optionList: [],
      buyerInfo: {},
      update: 0,
      fields: '',
      orderId: '',
      env: ''
    }
  },
  created() {
    this.handleProcessData()
  },
  methods: {
    handleProcessData() {
      const {
        $route: {
          query: {
            orderId, fields, update, env
          }
        }
      } = this
      this.orderId = orderId
      fields && (this.fields = fields)
      + update && (this.update = +update)
      env && (this.env = env)
      // 有orderId是为大定确认订单
      this.getMineOrderDetails(orderId || '')
    },
    getPageDataInfo(buyType, paymentMethodCode, moreContact, carOwnerCertificateType, carBuyerInfo, dealerInfo, carCustomInfo) {
      const buyTypeName = modifyBuyType?.filter((b) => b.type === buyType)[0]?.text || ''
      const paymentTypeName = paymentMethod?.filter((b) => b.type === paymentMethodCode)[0]?.text || ''
      const [IDType, IDNumber] = moreContact?.split(',') || ['', '']
      const IDTypeName = certificateJson[IDType] || ''
      const carOwnerCertificateTypeName = certificateJson[carOwnerCertificateType] || ''
      this.buyerInfo = {
        ...carBuyerInfo, ...dealerInfo, ...carCustomInfo, buyTypeName, paymentTypeName, IDTypeName, IDNumber, carOwnerCertificateTypeName
      }
    },
    async getMineOrderDetails(orderId) {
      if (orderId) {
        const { data: { data, code } } = await getMyOrders({ orderId })
        if (code === '00' && data && Object.keys(data).length) {
          const {
            carBuyerInfo, carBuyerInfo: { buyType, moreContact, carOwnerCertificateType }, dealerInfo, carCustomInfo, extInfo: { paymentMethod: paymentMethodCode }, orderStatus
          } = data
          this.getPageDataInfo(buyType, paymentMethodCode, moreContact, carOwnerCertificateType, carBuyerInfo, dealerInfo, carCustomInfo)
        }
      }
    },
    handleGoToEditOrderData() {
      const { orderId, fields } = this
      orderId && this.$router.push({
        path: '/order-info-modify',
        query: { from: orderId ? 'order' : 'forms', fields: orderId ? fields : 'all', orderId }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.buyer-info-wrapper {
  .main-wrapper {
    height: 100vh;
    box-sizing: border-box;
    background-color: #f2f2f2;
    .order-forms-box {
      padding-bottom: 50px;
      background-color: #f2f2f2;
    }
  }
  /deep/.right-btn {
    font-size: 14px;
  }
}

</style>
