<template>
  <div class="c-flex-center-between  header">
    <div class="left c-flex">
      <div class="btn back" @click="toBack">
        <img :src="require('../assets/img/icon03-back.png')" alt="">
      </div>
      <div class="btn close" @click="toClose">
        <img :src="require('../assets/img/icon5-close.png')" alt="">
      </div>
    </div>

    <div class="center">
      {{ $store.state.title }}
    </div>

    <div class="right">
      <!-- //todo -->
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { callNative } from '@/utils/index'


const ROUTE = {
  carModel: '/configration',
  configration: '/configrationContainer'
}

export default {
  computed: {
    ...mapGetters(['currentSeriesName']),
    ...mapState({
      carIdx: (state) => state.configration.carIdx,
      carModelActiveTab: (state) => state.configration.carModelActiveTab,
      configrationActiveTab: (state) => state.configration.configrationActiveTab,
      currentVersion: (state) => state.configration.currentVersion,
      projectStartTime: (state) => state.configration.projectStartTime
    })
  },

  methods: {
    // 返回按钮
    toBack() {
      const { path: currentRoute } = this.$router.history.current
      const { from, getback } = this.$route.query
      const { carModelActiveTab } = this
      // 从V4版车型解析进入，需原路返回
      if (from === 'app-model-analysis' && getback === 'strack' && carModelActiveTab === 'power') {
        return this.backtrackPage()
      }
      // console.log(currentRoute)

      // carmodel 页面下的tab跳转逻辑
      if (currentRoute === ROUTE.carModel) {
        this.clickBackOnCarModelPage()
      }

      // 在configration 页面下的tab跳转逻辑
      if (currentRoute === ROUTE.configration) {
        this.clickBackOnConfigrationPage()
      }
    },

    // 关闭按钮
    toClose(tag) {
      /**
       * 外饰,内饰,选装,权益
       * 则跳转到 cofigration 页面
       */
      const { path: currentRoute } = this.$router.history.current

      const { from, getback } = this.$route.query
      if (from === 'app-model-analysis' && getback === 'strack') {
        return this.backtrackPage()
      }

      if (currentRoute === ROUTE.configration) {
        // 恢复默认 tab
        this.$store.commit('updateConfigrationActiveTab', 'exterior')

        this.$router.push({
          path: ROUTE.carModel,
          query: {
            idx: this.carIdx
          }
        })

        // 埋点
        this.clickSensors('退出', this.configrationActiveTab)
        return
      }

      /**
       * 动力,版本,车型 为一个路由 configration
       * 调转返回到爱车页面
       */
      if (currentRoute === ROUTE.carModel) {
        this.toLoveCarPage()

        // 埋点 从动力tag触发的不执行埋点
        if (tag !== 'sensorsTag') {
          this.clickSensors('退出', this.carModelActiveTab)
        }
      }
    },

    // 车型页的点击返回逻辑
    clickBackOnCarModelPage() {
      if (this.carModelActiveTab === 'power') {
        this.toClose('sensorsTag')
      }

      if (this.carModelActiveTab === 'version') {
        this.$store.commit('updateCarModelTab', 'power')
      }

      if (this.carModelActiveTab === 'carModel') {
        this.$store.commit('updateCarModelTab', 'version')
      }

      // 埋点
      this.clickSensors('返回', this.carModelActiveTab)
    },

    // 配置页的点击返回逻辑
    clickBackOnConfigrationPage() {
      // 配置过程中点击返回
      if (this.configrationActiveTab === 'exterior') {
        this.$router.back()
      }
      if (this.configrationActiveTab === 'interior') {
        this.$store.commit('updateConfigrationActiveTab', 'exterior')
      }
      if (this.configrationActiveTab === 'option') {
        this.$store.commit('updateConfigrationActiveTab', 'interior')
      }
      if (this.configrationActiveTab === 'equity') {
        this.$store.commit('updateConfigrationActiveTab', 'option')
      }

      // 埋点
      this.clickSensors('返回', this.configrationActiveTab)
    },

    // 跳转到爱车页面
    toLoveCarPage() {
      callNative('goHome', {})
    },

    // 退出ccpro
    clickSensors(buttonName, tabName) {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const tabMap = {
        power: '动力',
        version: '版本',
        carModel: '车型',
        exterior: '外饰',
        interior: '内饰',
        option: '选装',
        equity: '权益'
      }

      const exitTime = new Date().getTime()

      const param = {
        source_module: 'H5',
        button_name: buttonName,
        car_series: carMap[this.carIdx],
        car_model: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        belong_page: this.$router.history.current.meta?.title,
        tab_name: tabMap[tabName],
        $event_duration: Math.floor((exitTime - this.projectStartTime) / 1000)
      }
      // console.log('param', param)
      this.$sensors.track('CC_Page_Exit', param)
    },
    backtrackPage() {
      callNative('prepage', { times: 1 })
    }
  }
}
</script>

<style lang="less" scoped>
@import "../assets/style/common.less";

.header {
  padding: 0 12px;

  >div {
    flex: 1;

    &.center {
      text-align: center;
    }

    &.right {
      text-align: right;
    }
  }

  .btn {
    img {
      vertical-align: middle;
    }

    &.back {
      padding: 0;
      width: 22px;
    }

    &.close {
      width: 22px;
      margin-left: 13px;
    }
  }


  .center {
    font-weight: bold;
  }
}
</style>
