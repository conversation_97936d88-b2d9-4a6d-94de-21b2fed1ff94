<template>
  <div class="container">
    <van-radio-group
      v-model="radioType"
      class="rights-box"
      style="background: #f2f2f2"
    >
      <template v-if="skukw === 'lite'">
        <van-radio
          name="5"
          class="radio-list"
        >
          奥迪品牌充电桩+3600元公共充电卡
          <template #icon="props">
            <img
              class="img-icon"
              :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
            >
          </template>
        </van-radio>
        <van-radio
          name="6"
          class="radio-list"
        >
          7200元公共充电卡（不安装充电桩）
          <template #icon="props">
            <img
              class="img-icon"
              :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
            >
          </template>
        </van-radio>
      </template>
      <template v-else>
        <van-radio
          name="1"
          class="radio-list"
        >
          私人充电桩
          <template #icon="props">
            <img
              class="img-icon"
              :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
            >
          </template>
        </van-radio>
        <van-radio
          name="2"
          class="radio-list"
        >
          {{ HUI_XING.WW }}奥金 (不安装充电桩)
          <template #icon="props">
            <img
              class="img-icon"
              :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
            >
          </template>
        </van-radio>
        <!-- <van-radio
          name="3"
          class="radio-list"
        >
          {{ HUI_XING.WW }}奥金+公共桩挂靠 (不安装充电桩)
          <template #icon="props">
            <img
              class="img-icon"
              :src="props.checked ? activeRadioIcon : inactiveRadioIcon"
            >
          </template>
        </van-radio> -->
      </template>
    </van-radio-group>

    <div>
      <div class="bottom_style">
        <!-- <div class="checkbox_style">
          <van-checkbox disabled="disabled" @click="ischecked = !ischecked">
            <img
              class="checkbox_button"
              slot="icon"
              :src="ischecked ? activeIcon : inactiveIcon"
            />
            <span style="color: #666666; font-size: 12px"> 我已阅读并同意</span>
            <span @click="onImportantNote" style="color: #000; font-size: 12px"
              >《安装服务指引、充电桩选择范围提示》
            </span>
          </van-checkbox>
        </div> -->

        <div class="btn-delete-wrapper">
          <AudiButton
            @click="onSubmit"
            :text="'确认'"
            color="black"
            font-size="16px"
            height="56px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  RadioGroup, Radio, Field, Checkbox, CheckboxGroup
} from 'vant'
import AudiButton from '@/components/audi-button'
import { callNative } from '@/utils'
import { postCreateChargingPile } from '@/api/api'
import Q5E_TRON_DATA from '@/config/Q5e-tron-map.data'
import { HUI_XING, CHARGING_STATUS_TEXT } from '@/config/conf.data'
import storage from '../../utils/storage'

Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Field).use(CheckboxGroup).use(Checkbox)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      radioType: '1',
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      activeRadioIcon: require('../../assets/img/radio_checked.png'),
      inactiveRadioIcon: require('../../assets/img/radio_normal.png'),
      orderId: '',
      isOrderIntegral: false, // 是否选择了奥金成功
      Q5E_TRON_DATA,
      skukw: '',
      orderStatus: '',
      HUI_XING
    }
  },

  async mounted() {
    const { orderId, skukw, orderStatus } = this.$route.query || { orderId: '', skukw: '' }
    if (skukw) {
      this.skukw = skukw
      skukw === 'lite' && (this.radioType = '5')
    }
    orderStatus && (this.orderStatus = orderStatus)
    orderId && (this.orderId = orderId)
    const isOrderIntegral = storage.get('isOrderIntegral') || ''

    if (isOrderIntegral) {
      storage.set('isOrderIntegral', '')
      this.$router.back(-1)
    }
  },

  methods: {
    onImportantNote() {},
    // 确认
    async onSubmit() {
      console.log('radio', this.radioType)
      if (['1', '5'].includes(this.radioType)) {
        this.$router.push({
          path: '/charging/select-charging-pile',
          query: { orderId: this.orderId, orderStatus: this.orderStatus, radioType: this.radioType }
        })
      } else if (this.radioType === '2') {
        // 奥金
        this.callNativePopup(2)
      } else if (this.radioType === '3') {
        /// 3500奥金
        this.callNativePopup(3)
      } else {
        this.postSubmit(+this.radioType)
      }
    },
    async callNativePopup(equityType) {
      // 调用APP Dialog
      callNative('popup', {
        type: 'alert',
        alertparams: {
          title: '',
          desc: '选择兑换奥金后，不可选择安装充电桩，请您确认是否选择兑换奥金',
          actions: [
            {
              type: 'fill',
              title: '确认兑换奥金'
            },
            {
              type: 'stroke',
              title: '我再想想'
            }
          ]
        }
      }).then((data) => {
        if (data.type === 'fill') {
          // 点击确定
          this.postSubmit(equityType)
        }
      })
    },

    async postSubmit(equityType) {
      const param = {
        orderId: this.orderId,
        equityType: equityType
      }
      this.$store.commit('showLoading')
      const { data } = await postCreateChargingPile(param)
      this.$store.commit('hideLoading')
      if (data.code === '00') {
        const { orderId } = this
        this.$router.push({
          path: '/charging/charging-pile-success',
          query: { type: equityType, orderStatus: this.orderStatus, ...(orderId ? { from: 'install-info', name: 'money-detail', param: `'orderId=${orderId}'` } : {}) }
        })
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.container {
  padding: 16px;
  background: #f2f2f2;
  height: calc(100% - 32px);
}

.flex1 {
  flex: 1;
}

.item-wrapper {
  .c-flex-between;
  align-items: center;
  background: #fff;
  margin-top: 16px;
  padding-right: 16px;
  height: 90px;
}

.navgation-wrapper {
  text-align: center;
}

.img-icon {
  height: 24px;
  width: 24px;
}

.bottom_style {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 34px;
  // border-top: 2px #f2f2f2 solid;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
  .checkbox_button {
    margin-left: 16px;
    margin-bottom: 5px;
    width: 18px;
    height: 18px;
    color: #000;
  }
  .checkbox_style {
    display: flex;
    // height: 20px;
    justify-content: space-between;
    font-family: "Audi-Normal";
    color: #999999;
    width: 100%;
    font-size: 16px;
    margin-bottom: 16px;
    span {
      font-size: 16px;
      color: #000;
      font-family: "Audi-Normal";
    }
    .checkbox_styleRight {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #000;
      margin-right: 16px;
    }
  }
}
.btn-delete-wrapper {
  margin: 0 16px;
}
.rights-box {
  .radio-list {
    font-size: 16px;
    height: 24px;
    line-height: 24px;
    color: #333;
    align-items: center;
    padding: 16px;
    margin-bottom: 16px;
    background: rgb(255, 255, 255);
    /deep/.van-radio__icon {
      height: 24px;
    }
    /deep/.van-radio__label {
      padding-left: 12px;
      margin: 0;
      line-height: 24px;
    }
  }
}

</style>
