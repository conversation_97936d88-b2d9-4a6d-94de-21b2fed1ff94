<template>
  <div>
    <div class="img-wrapper">
      <img v-for="url in imgs" :key="url" :src="url | imgFix(1000, true)" alt="">
    </div>
    <div class="btn-wrapper">
      <AudiButton
        text="一键下载"
        color="black"
        height="56px"
        font-size="16px"
        @click="downloadConfigpdf"
      />
    </div>
  </div>
</template>

<script setup>
import { Toast } from "vant";
import { onMounted, ref } from "vue";
import confiUrl from "@/config/url";
import AudiButton from "@/components/audi-button";
import { callNative } from "@/utils";

const baseConfigrationOssHost = confiUrl.BaseConfigrationOssHost;
const IMG_NUM = 1;
const imgs = ref([]);
const pdfUrl = ref("");

function getCarName() {
  return "a5l";
}

// 下载pdf
const downloadConfigpdf = () => {
  const carName = getCarName();

  pdfUrl.value = `${baseConfigrationOssHost}/audicc/app/%E9%85%8D%E7%BD%AE%E8%A1%A820240506/${carName}/config.pdf`;
  // 因为安卓HTTPS证书验证的问题，我们需要将https先变成http
  pdfUrl.value = pdfUrl.value.replace("https://audi-oss.saic-audi.mobi/", "http://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/");

  callNative("downloadFile", { url: pdfUrl.value, fileName: `上汽${carName}_装备价格表.pdf`, type: "pdf" }).then((res) => {
    Toast({
      type: "success",
      message: "下载成功",
      icon: require("@/assets/img/success.png")
    });
  });
};

onMounted(() => {
  const carName = getCarName();
  const result = Array.from({ length: IMG_NUM }, (v, i) => `${baseConfigrationOssHost}/audicc/app/%E9%85%8D%E7%BD%AE%E8%A1%A820240506/${carName}/${i + 1}.jpg`);
  imgs.value = result;
});


</script>

<style scoped lang="less">
.img-wrapper {
  padding-bottom: 140px;
}

.btn-wrapper {
  position: fixed;
  bottom: 15px;
  padding: 0 15px;
  width: 100%;
  box-sizing: border-box;
}

</style>
