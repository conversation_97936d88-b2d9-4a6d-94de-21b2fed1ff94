<template>
  <div>
    <navigation :custom='true' @onBack='toBack'></navigation>
    
    <div class="unsubscribeSucceed">
      <div>
        <img src="../../assets/img/contract-success.png">
      </div>
      <p>恭喜您报名成功</p>
      <p style="font-size: 14px;color: #666666;">您的管家会在活动开始前一天联系您，请耐心等待</p>
      <div class="btnWarp">
        <div class="buttons" @click="toActivityList">
          查看我的活动
        </div>
        <!-- <div class="bt" /> -->
      </div>
    </div>
  </div>
</template>

<script>
  import Vue from 'vue'
  import { mapState } from 'vuex'
  import { Icon, Toast } from 'vant'
  import model from '@/components/model.vue'
  import navigation from '../../components/navigation.vue'
  import { callNative } from "@/utils";
  import { getAudiMinipUrl } from "@/api/api.js"

  Vue.use(Icon)
  Vue.use(Toast)
  export default {
    components: {
      model,
      navigation
    },
    data() {
      return {

      }
    },
    mounted() {
      
    },
    computed: {
      
    },
    methods: {
      toBack() {
        this.$router.go(-2)
      },
      toActivityList() {
        callNative('openRoutePath', { path: 'scaudi://activity/list/' })
      },
      // onBack(){
      //   const { data } = await getAudiMinipUrl()
      //   const url = `${data.data.configValue}activity/appdetail/${1481171208162103297}?source=mine&source_module=list`
      //   callNative("audiOpen", { path: url });
      // },
    }
  }
</script>

<style scoped lang="less">
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/buttons.less");

  .unsubscribeSucceed {
    padding: 16px;
    text-align: center;

    img {
      margin-top: 110px;
      width: 72px;
      height: 72px;
    }

    p {
      text-align: center;
      font-size: 18px;
      color: #000000;
      line-height: 12px;
      margin-top: 25px;
    }

    .tips {
      text-align: left;
      font-size: 14px;
      line-height: normal;
      margin: 80px auto;
      width: 60vw;
    }

    .btnWarp {
      position: fixed;
      z-index: 2;
      height: 56px;
      width: 100%;
      bottom: 0;
      padding-bottom: 50px;
      background: #fff;

      .buttons2 {
        top: 60px;
      }
    }
  }
</style>
