<template>
  <div class="container">
    <div
      class="upload-preview"
      v-if="isUploader"
    >
      <div
        class="upload-status"
        data-flex="main:center cross:center"
      >
        <i class="icon" /><span>上传成功！</span>
      </div>
      <div class="preview">
        <div class="img">
          <img :src="fileList[0] && fileList[0].content || require('@/assets/img/bg06.jpg')">
        </div>
        <van-divider
          :hairline="false"
          :style="{ color: '#999999', borderColor: '#E5E5E5', padding: '0 18px' }"
        >
          请确认识别结果
        </van-divider>
      </div>
    </div>
    <div class="upload-form">
      <van-dropdown-menu
        v-if="!isUploader"
        class="lan-popover-menu"
        v-model="type"
        :overlay="false"
      >
        <van-dropdown-item
          v-model="typeText"
          :options="actions"
          @change="handleChangeDropdownMenu"
        />
      </van-dropdown-menu>
      <van-field
        is-link
        readonly
        v-if="!isUploader"
        class="field form-item is-link-up"
        :get-container="'lan-popover-box'"
        label="证件类型"
        v-model="typeText"
        placeholder="请选择证件类型"
      />
      <van-field
        class="field form-item"
        label="姓名"
        v-model="name"
        :readonly="isUploader"
        @input="onInput0"
        placeholder="请输入您的姓名"
        :error-message="message0"
      />
      <van-field
        class="field form-item"
        label="证件号"
        :readonly="isUploader"
        v-model="idCard"
        @input="onInput2"
        placeholder="请输入您的证件号"
        :error-message="message2"
      />
      <!-- <div class="label">
        试驾人电话
      </div>
      <van-field
        class="field"
        v-model="mobile"
        @input="onInput1"
        readonly
        placeholder="请输入试驾人电话"
        :error-message="message1"
      /> -->
    </div>
    <div
      :class="['upload-img', fileList.length ? 'is-uploaded' : '']"
      v-if="!isUploader"
    >
      <div
        :class="['uploader-masking', fileList.length ? 'be-ready' : '']"
        @click="handleActionSheet"
      />
      <van-uploader
        ref="uploader"
        v-model="fileList"
        multiple
        :closeable="false"
        :max-count="1"
        image-fit="contain"
        :after-read="afterRead"
        :accept="uploaderAccept"
        :capture="uploaderAccept === 'image/camera'"
      />
      <div class="text">
        上传证件
      </div>
    </div>
    <!-- <div class="uploader">
      <img
        class="bgimg"
        v-show="showUploaderBg&&type===2"
        src="../../assets/img/bg05.jpg"
      >
      <img
        class="bgimg"
        v-show="showUploaderBg&&type===1"
        src="../../assets/img/bg06.jpg"
      >
      <van-uploader
        v-model="fileList"
        multiple
        :max-count="1"
        image-fit="contain"
        :after-read="afterRead"
      />
    </div>
    <div class="tip">
      请您上传{{ type===2?'驾驶证':'身份证' }}正面照，请保证阳光充足！
    </div> -->
    <van-action-sheet
      class="lan-action-sheet"
      v-model="show"
      :round="false"
      :actions="sheetActions"
      cancel-text="取消"
      title="选择类型"
      close-on-click-action
      @select="handleSelectSelect"
    />
    <div class="bottom lan-button-box black-button">
      <van-button
        v-if="isUploader"
        class="lan-button"
        @click="$router.back(-1)"
      >
        返回
      </van-button>
      <van-button
        v-else
        class="lan-button"
        :loading="isLoading"
        :loading-text="isLoading ? '上传中...' : ''"
        @click="next"
      >
        下一步
      </van-button>
      <!-- <div
        class="button2"
        @click="changeType"
      >
        上传{{ type===2?'身份证':'驾驶证' }}
      </div> -->
    </div>
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols lan-dialog-content-f12"
      v-model="dialogShow"
      cancel-button-text="取消"
      confirm-button-text="确定"
      :title="`上汽奥迪申请获取${dialogKeyword}权限`"
      show-cancel-button
      :message="`上汽奥迪需要申请${dialogKeyword}权限,${dialogKeyword === '相机' ? '以便通过扫一扫、拍摄照片或视频为您提供上传/修改头像或车辆图片、上传行驶证、身份证件等证件信息、识别二维码和车辆VIN、录制视频、专属桩绑定、扫码充电、拍摄/录制发送动态、文章、口碑所用的图片/视频素材、客服及售后服务会话发送的图片和视频信息服务。拒绝或取消授权不影响使用其他服务。' : '以便通过访问相册为您提供扫描二维码、上传头像、保存发票、车辆图片、绑车、上传或保存发送动态/文章/口碑/用户互动（邀请好友）所用的图片/视频素材、客服及售后服务会话发送的图片和视频信息服务。拒绝或取消授权不影响使用其他服务。'}`"
      @confirm="dialogConfirm"
      @cancel="dialogCancel"
    />
  </div>
</template>
<script>
import Vue from 'vue'
import {
  Uploader, Field, DropdownMenu, DropdownItem, Popover, Toast, ActionSheet, Button, Divider
} from 'vant'
import url from '@/config/url'
import validateIdCard from '@/utils/idcard'
import { callNative } from '@/utils'
import { identifyIdCard, identifyDrivingLicence, updateTestDrive } from '../../api/test-driver'

const BaseApiUrl = url.BaseApiUrl
Vue.use(Uploader).use(Field).use(DropdownMenu).use(DropdownItem)
  .use(ActionSheet)
  .use(Toast)
  .use(Popover)
  .use(Button)
  .use(Divider)
export default {
  name: 'UploadLicense',
  data() {
    return {
      id: '',
      appoid: '',
      type: 2, // 1.上传身份证 2.上传驾驶证
      showUploaderBg: true,
      fileList: [],
      name: '',
      mobile: '',
      idCard: '',

      message0: '',
      message1: '',
      message2: '',
      showPopover: false,
      // 通过 actions 属性来定义菜单选项
      actions: [{ text: '驾驶证', value: 2 }, { text: '身份证', value: 1 }],
      typeText: '',
      show: false,
      sheetActions: [
        { name: '拍摄', accept: 'image/camera' },
        { name: '相册上传', accept: 'image/*' }
      ],
      beReady: false,
      uploaderAccept: '',
      isUploader: false,
      isLoading: false,
      filePreview: '',
      dialogShow: false,
      dialogKeyword: ''
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.appoid = this.$route.query.appoid
    this.type = +this.$route.query.type || 2
    this.mobile = this.$route.query.mobile || ''
    // this.$store.state.title = this.type === 2 ? '上传驾驶证' : '上传身份证'
    this.handleChangeDropdownMenu(this.type)
  },
  methods: {
    next() {
      //  this.testMobile(this.mobile) &&
      if (this.testName(this.name) && this.testIdcard(this.idCard)) {
        this.updateTestDrive()
        const params = {
          name: this.name,
          contact_information: this.mobile
        }
        this.$sensors.track('confirmSubmission', params)
      }
    },
    async updateTestDrive() {
      const data = {
        approveName: this.name,
        custIdCard: this.idCard,
        custIdType: this.type,
        id: this.id,
        testDriveType: 1
      }
      this.isLoading = true
      await updateTestDrive(data).then((res) => {
        if (res.status === 200 && res.data.code === '200') {
          this.isUploader = true
          // this.$router.push({
          //   path: '/testdrive/detail',
          //   query: {
          //     id: this.appoid
          //   }
          // })
        } else {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: res.data.message,
            forbidClick: true,
            duration: 800
          })
        }
      }).finally((s) => {
        this.isLoading = false
      })
    },
    onInput0(val) {
      this.testName(val)
    },
    onInput1(val) {
      this.testMobile(val)
    },
    onInput2(val) {
      this.testIdcard(val)
    },
    changeType() {
      this.type = +this.type === 1 ? 2 : 1
    },
    testName(val) {
      if (val.length > 0) {
        this.message0 = ''
        return true
      }
      this.message0 = '请输入姓名'
      return false
    },
    testMobile(val) {
      const re = /^1\d{10}$/
      if (re.test(val)) {
        this.message1 = ''
        return true
      }
      this.message1 = '手机号格式错误'
      return false
    },
    testIdcard(val) {
      // const re = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      // if (!(re.test(val))) {
      //   this.message2 = '身份证号格式错误'
      //   return false
      // }
      // const reg1 = new RegExp(/\s+/g)
      // if (val.length <= 0 || reg1.test(val)) {
      //   this.message2 = '身份证号不能为空或含有空格'
      //   return false
      // }
      // this.message2 = ''
      const [result, msg] = validateIdCard(val)
      if (!result) {
        this.message2 = `身份证${msg}`
        return result
      }
      this.message2 = ''
      return true
    },
    afterRead() {
      const formData = new FormData()
      formData.append('file', this.fileList[0].file)
      this.doShibie(formData)
    },
    async doShibie(file) {
      this.$store.commit('showLoading')
      if (+this.type === 2) {
        await identifyDrivingLicence(file)
          .then((response) => {
            if (response.status === 200 && response.data.code === '00') {
              this.name = response.data.data.name
              this.idCard = response.data.data.cardNo
            } else {
              Toast({
                className: 'toast-dark-mini toast-pos-middle',
                message: response.data.message,
                forbidClick: true,
                duration: 800
              })
            }
            this.$store.commit('hideLoading')
          })
      } else if (+this.type === 1) {
        await identifyIdCard(file)
          .then((response) => {
            if (response.status === 200 && response.data.code === '00') {
              this.name = response.data.data.name
              this.idCard = response.data.data.cardNo
              this.testIdcard(this.idCard)
            } else {
              Toast({
                className: 'toast-dark-mini toast-pos-middle',
                message: response.data.message,
                forbidClick: true,
                duration: 800
              })
            }
            this.$store.commit('hideLoading')
          })
      }
    },
    handleActionSheet() {
      // this.beReady = true
      if (this.$route.query.env === 'minip') {
        this.uploaderAccept = 'image/*'
        this.$nextTick(() => this.$refs.uploader.chooseFile())
        return
      }
      this.show = true
    },
    async handleSelectSelect({ accept, name }) {
      const [, camera] = accept.split('/')
      const { status } = await callNative('albumCameraEvent', { type: camera === 'camera' ? 1 : 2 }) || { status: false }
      if (!status) {
        this.dialogKeyword = camera === 'camera' ? '相机' : '相册'
        this.dialogShow = true
        return
      }
      this.uploaderAccept = accept
      this.$nextTick(() => this.$refs.uploader.chooseFile())
    },
    handleChangeDropdownMenu(v) {
      const { text, value } = this.actions.find((i) => i.value === v)
      this.type = value
      this.typeText = text
    },
    dialogCancel() {},
    dialogConfirm() {
      callNative('openpage', {})
    }
  },
  watch: {
    // type(val) {
    //   if (+val === 2) {
    //     this.$store.state.title = '上传驾驶证'
    //   } else if (+val === 1) {
    //     this.$store.state.title = '上传身份证'
    //   }
    // },
    fileList(val) {
      if (val.length > 0) {
        this.showUploaderBg = false
      } else {
        this.showUploaderBg = true
      }
    }
  }
}
</script>
<style lang="less" scoped>
.container{
  ::v-deep .van-uploader{
    // margin: 12px 16px 0 16px;
    width: 312px;
    height: 182px;
    .van-uploader__wrapper{
      width: 100%;height: 100%;
      .van-uploader__upload{
        width: 100%;height: 100%;
        margin: 0;
        background-color: transparent;
        // background-image: url("../../assets/img/bg05.jpg");
        // background-size: 100% 100%;
      }
      .van-uploader__preview{
        width: 100%;height: 100%;
        margin: 0;
        .van-uploader__preview-image{
          width: 100%;height: 100%;
        }
      }
    }
  }
  .uploader{
    position: relative;
    .bgimg{
      width: calc(100% - 32px);height: 200px;
      display: block;
      position: absolute;
      left: 16px;top: 12px;
    }
  }
  .tip{
    color: #999;
    font-size: 14px;
    margin: 10px 16px;
  }
  .label{
    color: #666;
    font-size: 12px;
    margin: 10px 16px 0 16px;
  }
  .field{
    font-size: 16px;
  }
  .bottom{
    background-color: #fff;
    padding: 5px 16px 50px 16px;
    width: 100vw;
    // height: 56px;
    position: fixed;
    bottom: 0;
    z-index: 10;
    .button1{
      margin: 0 16px;
      height: 56px;
      background-color: #000;
      color: #fff;
      text-align: center;
      line-height: 56px;
    }
    .button2{
      margin: 6px 16px 0 16px;
      height: 56px;
      border: solid 1px #000;
      box-sizing: border-box;
      text-align: center;
      line-height: 56px;
    }
  }
}

.upload-form {
  padding: 8px 0 45px;
  position: relative;
  .lan-popover-menu {
    position: absolute;
    width: 100%;
    top: 8px;
    z-index: 9;
    background-color: transparent;
    /deep/ .van-dropdown-menu__bar {
      height: 56px;
      background-color: transparent;
      box-shadow: none;
      .van-dropdown-menu__item {
        .van-dropdown-menu__title {
          display: none;
        }
      }
    }
    /deep/ .van-dropdown-item {
      margin-top: -1px;
      .van-dropdown-item__content {
        padding: 0 16px;
        width: calc(100% - 32px);
        .van-cell {
          background-color: #F2F2F2;
          font-size: 16px;
          color: #333;
          padding: 16px 0;
          height: 56px;
          .van-cell__value {
            color: #333;
          }
          &:last-child {
            border: solid .5px #E5E5E5;
          }
        }
      }
    }
  }
  .form-item {
    height: 56px;
    overflow: inherit;
    &:last-child {
        &::after {
        display: block;
      }
    }
    &::after {
      border-color: #e5e5e5;
    }
    /deep/ .van-cell__title , /deep/ .van-cell__value, /deep/ .van-cell__right-icon {
      height: 36px;
      line-height: 36px;
    }
    /deep/ .van-cell__title {
      color: #000;
      font-size: 16px;
      width: 80px;
      margin-right: 4px;
    }
    /deep/ .van-cell__right-icon {
      margin-right: 2px;
    }
    /deep/ .van-cell__value {
      .van-field__control {
        font-size: 16px;
        &:placeholder {
          color: #B3B3B3;
        }
      }
      .van-field__error-message {
        height: 22px;
        line-height: 22px;
        position: relative;
        top: 10px;
        margin-left: -84px;
        padding-left: 25px;
        border-top: solid 0.5px #BB0A30;
        &::before {
          content: "";
          position: absolute;
          left: 0;
          margin-top: -2px;
          display: block;
          width: 21px;
          height: 21px;
          background: url("~@/assets/img/error-icon.png") no-repeat 0 0 !important;
          background-size: 21px 21px;
          margin-right: 4px;
          top: 2px;
        }
      }
    }

  }

}
.upload-img {
  z-index: 6;
  padding: 0 32px;
  text-align: center;
  position: relative;
  .uploader-masking {
    position: absolute;
    z-index: 9;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    &.be-ready {
      z-index: -9;
    }
  }
  .text {
    font-size: 14px;
    height: 24px;
    line-height: 24px;
    color: #333;
  }
  /deep/ .van-uploader__upload {
    background: url(~@/assets/img/icon-identity10.png) !important;
    background-size: cover;
   .van-icon  {
      display: none;
    }
  }
}
.lan-action-sheet {
  /deep/ .van-action-sheet__header {
    height: 50px;
    line-height: 50px;
    font-size: 14px;
    color: #666;
    border-bottom: solid .6px #e5e5e5;
    .van-icon-cross {
      display: none;
    }
  }
  /deep/ .van-action-sheet__content {
    .van-action-sheet__item {
      height: 56px;
      line-height: 28px;
      color: #000;
      border-bottom: solid .6px #e5e5e5;
    }
  }
  /deep/ .van-action-sheet__gap {
    background-color: #F2F2F2;
  }
  /deep/ .van-action-sheet__cancel {
    height: 90px;
    padding: 16px 16px 34px 16px;
    font-size: 16px;
    color: #000;
  }

}

.upload-preview {
  padding-top: 16px;
  .upload-status {
    font-size: 16px;
    color: #000;
    line-height: 32px;
    padding-bottom: 16px;
    .icon {
      display: block;
      width: 26px;
      height: 26px;
      background: url(~@/assets/img/contract-success2.png) no-repeat 50% !important;
      background-size: contain;
      margin-right: 4px;
    }
  }
  .preview {
    .img {
      height: 200px;
      padding: 0 16px;
      text-align: center;
      img {
        width: auto;
        vertical-align: middle;
        height: 100%;
      }
    }
  }
}

.van-overflow-hidden {
  .upload-form {
    .form-item {
      &.is-link-up {
        /deep/ .van-cell__right-icon {
          &::before {
            transform: rotate(90deg);
          }
        }
      }
    }
  }

}
</style>
