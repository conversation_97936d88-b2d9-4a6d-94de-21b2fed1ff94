<template>
  <div class="serviceAppointmentOne border-top">
    <div class="buyMess box" @click="titleTypeStatus = false">
      <van-form ref="form" @failed="failed" @submit='onSubmit2'>

        <div class="box-field">
          <van-field value="增值税电子普通发票" disabled />
          <div :class="['posi-label', 'aniName']"> 发票类型 </div>
        </div>

        <div class="box-field" ref="titleType">
          <van-field @click.stop="titleTypeStatus = !titleTypeStatus" :value="submitParam.titleType === 1 ? '企业' : '个人' " disabled />
          <div :class="['posi-label', 'aniName']"> 抬头类型 </div>
          <van-icon style="position: absolute;right: 0;bottom: 35px;" name="arrow-down" color="#999" />
          
          <div class="actionSheet" v-if="titleTypeStatus">
            <div class="text1">抬头类型</div>
            <div class="text2" @click="getTitleType(2)">个人</div>
            <div class="text2" @click="getTitleType(1)">企业</div>
          </div>
        </div>

        <div class="box-field" @click.stop="animation('title')" id="title">
          <van-field :label-align="labelAlign" :label-width="labelWidth" v-model="submitParam.title" ref="title" type="text" @blur="handlerBlur('title')"
            @focus="handlerFocus('title')" :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写发票抬头！',
              },
            ]" />
          <div :class="
              animations.title
                ? ['posi-label', 'aniName']
                : ['posi-label', 'noAniName']
            ">
            发票抬头
          </div>
        </div>

        <div class="box-field" @click.stop="animation('dutyParagraph')" id="dutyParagraph" v-if="submitParam.titleType === 1">
          <van-field :label-align="labelAlign" :label-width="labelWidth" v-model="submitParam.dutyParagraph" ref="dutyParagraph" type="text"
            @blur="handlerBlur('dutyParagraph')" @focus="handlerFocus('dutyParagraph')" :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写税号！',
              },
            ]" />
          <div :class="
              animations.dutyParagraph
                ? ['posi-label', 'aniName']
                : ['posi-label', 'noAniName']
            ">
            税号
          </div>
        </div>

        <div class="box-field" @click.stop="animation('email')" id="email">
          <van-field :label-align="labelAlign" :label-width="labelWidth" v-model="submitParam.email" ref="email" type="text" @blur="handlerBlur('email')"
            @focus="handlerFocus('email')" :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写接收邮箱！',
              },
              {
                pattern: /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/,
                message: '请输入正确的邮箱',
              },
            ]" />
          <div :class="
              animations.email
                ? ['posi-label', 'aniName']
                : ['posi-label', 'noAniName']
            ">
            接收邮箱
          </div>
        </div>
        <div style="font-size: 12px;">开票服务由上海上汽大众汽车销售有限公司</div>

      </van-form>
    </div>
    <div class="bottom_style">
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onSubmit"
          :text="'提交申请'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import Vue from 'vue'
  import { mapState } from 'vuex'
  import {Form,Field,Button,Toast,Popover,} from 'vant'
  import { callNative } from '@/utils'
  import storage from "../../utils/storage";
  import AudiButton from '@/components/audi-button'

  Vue.use(Form).use(Button).use(Field).use(Toast).use(Popover)

  export default {
    components: {
      AudiButton
    },
    data() {
      return {
        showPopover: false,
        actions: [], //{ text: '15000876160' }

        labelWidth: 120,
        labelAlign: 'left',
        animations: {
          title: false,
          email: false,
          dutyParagraph: false,
        },

        submitParam: {
          title: '', // 发票抬头
          email: '', // 邮箱
          dutyParagraph: '', //税号
          titleType: 1, //抬头类型
        },
        titleTypeStatus:false, //抬头类型弹窗状态
      }
    },
    computed: {
      ...mapState({
        // dealerCode: (state) => state.dealerModel.code758 || '', // 服务商编号
      })
    },
    watch: {},
    mounted() {
      const { receiptInfo } = this.$route.query
      console.log(receiptInfo)
      if(receiptInfo){
        this.submitParam = receiptInfo
        this.animations = {
          title: true,
          email: true,
          dutyParagraph: true,
        }
      }
    },
    methods: {
      
      getTitleType(e){
        this.submitParam.titleType = e
      },
      
      onSubmit(){
        this.$refs.form.submit()
      },
      onSubmit2(e){
        this.$store.commit('setInvoiceInfo',this.submitParam)
        this.$router.back(-1)
      },

      animation(ref) {
        this.showPopover = false
        this.animations[ref] = true
        this.$refs[ref].focus()
        if (this.blur) {
          this.$refs[ref].blur()
        }
      },
      handlerFocus(prop) {
        setTimeout(() => {
          const pannel = document.getElementById(prop)
          // 让当前的元素滚动到浏览器窗口的可视区域内
          pannel.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
          // 此方法是标准的scrollIntoView()方法的专有变体
          // pannel.scrollIntoViewIfNeeded();
        }, 300)

        if (!this.animations[prop]) {
          this.animations[prop] = true
        }
      },
      handlerBlur(prop) {
        this.animations[prop] = !!this.submitParam[prop]
      },
      failed(err) {
        console.error('failed', err)
      },
    }
  }
</script>

<style scoped lang="less">
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/animation.less");
  @import url("../../assets/style/cell.less");

  .border-top {
    border-top: 1px solid #E5E5E5;
  }

  .serviceAppointmentOne {
    padding-bottom: 120px !important;
    padding: 16px;

    .buyMess {
      padding: 0;

      /deep/.van-form {
        & > .box-field {
          display: flex;
          flex-direction: column;
          position: relative;
          height: 72px;
          width: 100%;

          .box-label {
            width: 100%;
            font-size: 12px;
            color: #646566;
          }

          .van-cell {
            position: relative;
            height: 100%;

            .van-cell__value {
              .van-field__body {
                min-height: calc(100% - 25px);
                border-bottom: 1px solid #000;
                font-size: 16px;
                overflow: visible;
                flex-direction: column;
                justify-content: flex-end;
                align-items: flex-start;

                input {
                  margin-bottom: 6px;
                  font-size: 14px;
                  line-height: 20px;
                  height: 20px;
                }

                textarea {
                  margin-top: 16px;
                  line-height: 16px;
                  // padding-top: 25px;
                  min-height: 16px;
                }
              }
            }
          }

          .van-field--error {
            .van-field__body {
              border-bottom: 1px solid #9e1f32 !important;
            }

            .van-field__error-message {
              color: #9e1f32;
            }
          }
        }
      }
    }

    /deep/.van-field__error-message {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 16px;

      &::before {
        content: "";
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url("../../assets/img/error.png") no-repeat 0 0;
        background-size: 14px 14px;
        margin-right: 4px;
      }
    }
  }

  ::v-deep .van-field__control:disabled {
    color: #000 !important;
    opacity: 1;
    -webkit-text-fill-color: #000 !important;
  }
  
  .actionSheet{
    position: absolute;
    box-sizing: border-box;
    z-index: 9999;
    bottom: -60px;
    width: 100%;
    left: 0;
    background-color: #fff;
    border: 1px solid #000000;
    padding: 0 10px;
    display: flex;
    flex-flow: column;
    
    .text1{
      font-size: 16px;
      color: #999999;
      line-height: 24px;
    }
    .text2{
      font-size: 16px;
      line-height: 24px;
    }
  }
  
  .bottom_style {
    width: 100%;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: fixed;
    bottom: 0px;
    padding-top: 16px;
    padding-bottom: 16px;
    left: 0px;
  }
  .btn-delete-wrapper {
    margin: 0 16px;
  }
</style>
