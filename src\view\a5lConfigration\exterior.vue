<template>
  <div>
    <!-- <Tabs current-active="exterior" /> -->
    <div class="collapse-wrapper">
      <div class="main-wrapper">
        <ShowModelButton type="out" />
        <ImgSwiper :img-list="page2dCarList" />
        <DescriptionText />
      </div>

      <div class="wrapper-scroll">
        <!-- 外观颜色列表 -->
        <div class="desc-wrapper">
          <div class="c-font14 c-bold c-lh22">
            {{ currentExterior.externalFeatureNameZh }}
          </div>
          <div class="desc c-lh20" v-if="discountExteriorTagVisible">
            <template v-if="hasKey(A5L_EXERIOR_PRICE, currentExterior.featureCode)">
              <span class="relative line-through">&nbsp;&nbsp; {{ A5L_EXERIOR_PRICE[currentExterior.featureCode] | finalFormatPriceDesc }} &nbsp;</span>
              <span>￥0</span>
            </template>
            <template v-else>
              <span>{{ currentExterior.featurePrice | finalFormatPriceDesc }}</span>
            </template>
          </div>
          <div class="desc c-lh20" v-else>
            <template v-if="hasKey(A5L_EXERIOR_PRICE, currentExterior.featureCode)">
              <span class="relative line-through">&nbsp;&nbsp; {{ A5L_EXERIOR_PRICE[currentExterior.featureCode] | finalFormatPriceDesc }} &nbsp;</span>
              <span>{{ currentExterior.featurePrice | finalFormatPriceDesc }}</span>
            </template>
            <template v-else>
              <span>{{ currentExterior.featurePrice | finalFormatPriceDesc }}</span>
            </template>
          </div>
        </div>
        <div class="exterior-wrapper">
          <div class="wrapper">
            <div v-for="item in currentVersion.carColor" :key="item.featureCode" class="item" :class="{ selected: item.featureCode === currentExterior.featureCode, }" @click="toSelectExerior(item)">
              <div v-if="item.equipmentRights == 1  && isCouponValid" class="presale-tag">预售权益</div>
              <img :src="item.materialList?.[0]?.materialUrl | imgFix(140, true)" alt="">
            </div>
          </div>
        </div>
        <!-- 轮毂列表 -->
        <div class="desc-wrapper hub-margin-top">
          <div class="c-font14 c-bold c-lh22 hub-name">
            {{ currentHub.packet ? currentHub.packet.labelValueNameZh : currentHub.externalFeatureNameZh }}
          </div>
          <template v-if="discountHubVisible(currentHub)">
            <!-- 豪华型19寸轮毂 -->
            <template v-if="hasKey(A5L_HAOHUA, currentModelLineData?.modelUnicode) && hasKey(A5L_RAD_PACKET_LABEL_VALUE_19, currentHub?.packet?.labelValue) ">
              <span class="desc c-lh20 line-through">{{ A5L_RAD_PACKET_LABEL_VALUE_19[currentHub.packet.labelValue] | finalFormatPriceDesc }}</span>
              <span class="desc">￥{{ getHubDiscountPrice  | formatPrice }}</span>
            </template>
            <!-- 其它车型轮毂 -->
            <template v-else>
              <span class="desc c-lh20 line-through">{{ (currentHub.packet ? currentHub.packet.featurePrice : currentHub.price) | finalFormatPriceDesc }}</span>
              <span class="desc">￥{{ getHubDiscountPrice  | formatPrice }}</span>
            </template>
          </template>
          <template v-else>
            <template v-if="hasKey(A5L_HAOHUA, currentModelLineData?.modelUnicode) && hasKey(A5L_RAD_PACKET_LABEL_VALUE_19, currentHub?.packet?.labelValue) ">
              <span class="desc c-lh20 line-through">{{ A5L_RAD_PACKET_LABEL_VALUE_19[currentHub.packet.labelValue] | finalFormatPriceDesc }}</span>
              <span class="desc">￥{{ (currentHub.packet ? currentHub.packet.featurePrice : currentHub.price)  | formatPrice }}</span>
            </template>
            <template v-else>
              <span class="desc c-lh20">{{ (currentHub.packet ? currentHub.packet.featurePrice : currentHub.price) | finalFormatPriceDesc }}</span>
            </template>
          </template>
        </div>
        <div class="hub-wrapper">
          <div class="wrapper">
            <div v-for="item in currentVersion.carRad" :key="item.featureCode" class="item" :class="{
              selected: item.featureCode === currentHub.featureCode
            }" @click="toSelectHub(item)">
              <div v-if="discountHubVisible(item)" class="presale-tag">预售权益</div>
              <img :src="getHubImage(item) | imgFix(140, true)" alt="">
            </div>
          </div>
        </div>
      </div>

    </div>

    <CommonFooter @click="nextPage" />
  </div>
</template>

<script>
import Vue from "vue";
import { mapGetters, mapState } from "vuex";
import { Toast } from "vant";
import CommonFooter from "./components/commonFooter.vue";
import { postOmdModelConfig } from "@/configratorApi";
import url from "@/config/url";
import ImgSwiper from "./imgSwiper.vue";
import ShowModelButton from "./components/showModelButton.vue";
import DescriptionText from "./components/descriptionText.vue";
import { callNative, hasKey, isDev, isEmptyObj } from "@/utils";
import { A5L_EXERIOR_PRICE, A5L_HAOHUA, A5L_RAD_PACKET_LABEL_VALUE_19 } from "@/view/a5lConfigration/util/carModelSeatData";

const OSS_URL = url.BaseConfigrationOssHost;
Vue.use(Toast);

export default {
  name: "ConfigrationExterior",
  components: {
    ImgSwiper, CommonFooter, ShowModelButton, DescriptionText
  },
  data() {
    return {
      BaseConfigrationOssHost: OSS_URL,
      pageStartTime: 0,
      prompt: [],
      paramDto: {},
    };
  },
  computed: {
    A5L_EXERIOR_PRICE() {
      return A5L_EXERIOR_PRICE;
    },
    A5L_HAOHUA() {
      return A5L_HAOHUA;
    },
    A5L_RAD_PACKET_LABEL_VALUE_19() {
      return A5L_RAD_PACKET_LABEL_VALUE_19;
    },
    ...mapGetters([
      "currentSeriesName",
      "page2dCarList",
      "currentCarType",
      "getHubDiscountPrice",
      "isCouponValid"
    ]),
    ...mapState({
      userCoupon: (state) => state.configration.userCoupon,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentExterior: (state) => state.configration.currentExterior,
      currentHub: (state) => state.configration.currentHub,
      currentVersion: (state) => state.configration.currentVersion,
      carIdx: (state) => state.configration.carIdx,
      currentCarInfo: (state) => state.configration.currentCarInfo,
      configrationActiveTab: (state) => state.configration.configrationActiveTab,
      selectedOptions: (state) => state.configration.selectedOptions,
      requiredRadPackets: (state) => state.configration.requiredRadPackets,
      currentVersionMap: (state) => state.configration.currentVersionMap,
    }),

    // 移到 mapState 外部，作为独立计算属性
    discountExteriorTagVisible() {
      const currentExterior = this.currentExterior; // 这里通过 this 访问 mapState 映射的属性
      // 条件：currentExterior存在 + equipmentRights为1 + 优惠券有效
      return currentExterior && currentExterior.equipmentRights === "1" && this.isCouponValid;
    },
  },
  watch: {
    configrationActiveTab(val) {
      if (val === "exterior") {
        this.pageStartTime = Date.now();
      }
    },

  },
  mounted() {
    console.log("%c A5L exterior mounted", "font-size:16px;color:green;");
    this.pageStartTime = Date.now();
    console.log("%c A5L currentHub", "font-size:16px;color:green;", this.currentHub);
    console.log("%c A5L currentModelLineData:", "font-size:16px;color:green;", this.currentModelLineData);

  },
  methods: {
    hasKey,
    isEmptyObj,
    async resizeCar(item, key) {
      this.paramDto = await this.$store.dispatch("getOmdModelConfigParams", { item, key });
      const carConfig = await postOmdModelConfig(this.paramDto);
      if (carConfig.data.data.code !== "20000") return this.$toast.fail(carConfig.data.data.message);
      this.prompt = carConfig.data.data.result[0]?.prompt;
      await this.$store.commit("updateCurrentVersion", carConfig.data.data.result[0]);
      await this.$store.dispatch("setRadEquipmentGroup");
      await this.$store.dispatch("setIIEquipmentGroup");
      await this.$store.dispatch("doA5InteriorPageInitAction");
    },

    // 选择外观颜色
    async toSelectExerior(item) {
      if (item.featureCode === this.currentExterior.featureCode) {
        return;
      }
      this.$store.commit("showLoadingNospinner");
      console.log("%c A5L updateCurrentExterior:", "font-size:16px;color:green;", item);
      await this.$store.commit("clearSelectedConfig", ["interior", "option"]); // 清理配置
      await this.$store.commit("updateCurrentExterior", item);
      await this.resizeCar(item, "AADD");
      this.$store.dispatch("computeA5LTotalPriceAndEquityPrice", { isCouponValid: this.isCouponValid });
      this.$store.commit("hideLoadingNospinner");
    },

    // 选择轮毂
    async toSelectHub(item) {
      if (item.featureCode === this.currentHub.featureCode) {
        return;
      }
      this.$store.commit("showLoadingNospinner");
      console.log("%c A5L updateCurrentHub:", "font-size:16px;color:green;", item);
      await this.$store.commit("clearSelectedConfig", ["interior", "option"]); // 清理配置
      await this.$store.commit("updateCurrentHub", item);
      await this.resizeCar(item, "RAD");
      console.log("%c A5L prompt:", "font-size:16px;color:green;", this.prompt);
      console.log("%c A5L updateRequiredRadPackets:", "font-size:16px;color:green;", this.requiredRadPackets);
      if (this.prompt && Array.isArray(this.prompt) && this.prompt.length > 0 && this.prompt[0].labelCode === "PACKET") {
        const requiredPacket = {
          labelCode: this.prompt[0].labelCode,
          featureCode: this.prompt[0].featureCode,
          alterationType: this.prompt[0].alterationType,
          externalFeatureNameZh: this.prompt[0].externalFeatureNameZh,
        };

        // 记录轮毂+选装包
        await this.$store.commit("updateRequiredRadPackets", { [item.featureCode]: requiredPacket });

        this.paramDto.children.push(requiredPacket);
        const carConfig = await postOmdModelConfig(this.paramDto);
        if (carConfig.data.data.code !== "20000") return this.$toast.fail(carConfig.data.data.message);
        this.prompt = carConfig.data.data.result[0]?.prompt;
        await this.$store.commit("updateCurrentVersion", carConfig.data.data.result[0]);
        await this.$store.dispatch("setRadEquipmentGroup");
        await this.$store.dispatch("setIIEquipmentGroup");
        await this.$store.dispatch("doA5InteriorPageInitAction");
        for (const selectedPacket of this.selectedOptions) {
          if (selectedPacket.featureCode === requiredPacket.featureCode) {
            // 使用$set确保响应式更新
            this.$set(selectedPacket, "disabled", true);
            if (isDev) {
              this.$toast.success(requiredPacket.externalFeatureNameZh);
            } else {
              callNative("toast", { type: "success", duration: 3000, message: `${requiredPacket.externalFeatureNameZh}为必须选装` });
            }
            break;
          }
        }
        console.log("%c A5L selectedOptions:", "font-size:16px;color:green;", this.selectedOptions);
      } else {
        if (!isEmptyObj(this.requiredRadPackets)) {
          const requiredPacket = this.requiredRadPackets[item.featureCode];
          if (!isEmptyObj(requiredPacket)) {
            for (const selectedPacket of this.selectedOptions) {
              if (selectedPacket.featureCode === requiredPacket.featureCode) {
                this.$set(selectedPacket, "disabled", true);
                if (isDev) {
                  this.$toast.success(requiredPacket.externalFeatureNameZh);
                } else {
                  callNative("toast", { type: "success", duration: 3000, message: `${requiredPacket.externalFeatureNameZh}为必须选装` });
                }
                break;
              }
            }
            console.log("%c A5L selectedOptions:", "font-size:16px;color:green;", this.selectedOptions);
          } else {
            for (const selectedPacket of this.selectedOptions) {
              this.$set(selectedPacket, "disabled", false);
            }
            console.log("%c A5L selectedOptions:", "font-size:16px;color:green;", this.selectedOptions);
          }
        } else {
          // await this.$store.commit("updateSelectedOptions", []);
          for (const selectedPacket of this.selectedOptions) {
            this.$set(selectedPacket, "disabled", false);
          }
          console.log("%c A5L selectedOptions:", "font-size:16px;color:green;", this.selectedOptions);
        }
      }

      this.$store.dispatch("computeA5LTotalPriceAndEquityPrice", { isCouponValid: this.isCouponValid });
      this.$store.commit("hideLoadingNospinner");
    },

    discountHubVisible(item) {
      if (!this.isCouponValid) {
        return false;
      }
      // 轮毂有权益
      if (item.equipmentRights == "1") {
        return true;
      }

      // 卡钳有权益
      if (!isEmptyObj(item.packet)) {
        for (const children of item.packet.labelChildren) {
          const radOrBahOrBav = this.currentVersionMap.get(children.featureCode);
          if (radOrBahOrBav.equipmentRights === "1") {
            return true;
          }
        }
      }
      return false;
    },

    getHubImage(item) {
      let image = item.materialList?.[0]?.materialUrl;
      if (!isEmptyObj(item.packet) && Array.isArray(item.packet.materialList) && item.packet.materialList.length > 0) {
        image = item.packet.materialList[0].materialUrl;
      }
      return image;
    },

    // 下一步
    async nextPage() {
      this.$store.commit("showLoading");
      // await this.$store.dispatch("setIIEquipmentGroup");
      await this.$store.dispatch("doA5InteriorPageInitAction");
      await this.$store.commit("hideLoading");
      await this.$store.commit("updateConfigrationActiveTab", "interior");
      this.clickExteriorSensors();// 埋点
    },

    // 埋点
    clickExteriorSensors() {
      const { engine, customSeriesName } = this.currentModelLineData;
      const param = {
        source_module: "H5",
        car_series: "A5L",
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: "定制交付", // 快速交付|定制交付
        select_colour: this.currentExterior.optionName,
        select_hub: this.currentHub.optionName,
        button_name: "下一步",
        $event_duration: Math.floor((Date.now() - this.pageStartTime) / 1000)
      };

      // console.log(param)
      this.$sensors.track("CC_CarConfiguration_Exterior_BtnClick", param);
    },
  }
};
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.collapse-wrapper {
  padding-bottom: @FooterHeight;
}

// 滚动区域
@ExteiorHeight: 220px;
.wrapper-scroll {
  overflow-y: auto;
  height: calc(104vh - @HeaderHeight - @TabHeight - @ExteiorHeight - @FooterHeight);
}


.main-wrapper {
  position: relative;
  min-height: 220px;
  margin: 10px 0;

  > .to3d-btn {
    width: 52px;
    height: 52px;
    position: absolute;
    top: 18px;
    left: 24px;
    z-index: 2;
  }
}

.desc-wrapper {
  text-align: center;
  color: #333333;

  &.hub-margin-top {
    margin-top: 48px;
  }

  > .desc {
    font-size: 12px;
    margin-top: 4px;

    > .relative {
      position: relative;

      .discount {
        position: absolute;
        left: 110%;
        top: 50%;
        transform: translateY(-50%);
        background-color: #EB0D3F;
        padding: 0 5px;
        line-height: 16px;
        color: #fff;
        font-size: 10px;
        width: max-content;
        text-wrap: nowrap;
      }
    }
  }

  .hub-name {
    color: #000;
  }
}


.exterior-wrapper,
.hub-wrapper {
  overflow: auto;
  padding: 0 16px;
  padding-bottom: 15px;
  margin-bottom: 20px;

  .wrapper {
    width: max-content;
    margin-top: 14px;

    > .item {
      margin-left: 4px;
      display: inline-block;
      width: 70px;
      height: 70px;
      transform: scale(0.7);
      box-sizing: border-box;
      position: relative;

      &.selected {
        transform: scale(1);
        border: 1px solid #000;
        padding: 1px;
        box-sizing: border-box;
      }

      img {
        height: 100%;
        vertical-align: inherit;
      }
    }
  }
}

.line-through {
  text-decoration: line-through;
  color: gray;
}

// 预售权益标签样式
.presale-tag {
  position: absolute;
  top: -5px;
  left: -5px;
  background-color: #EB0D3F; // 红色背景
  color: white; // 白色文字
  font-size: 10px;
  padding: 2px 12px 2px 5px;
  line-height: 16px;
  // 上尖下缩，右侧带弧度的形状
  clip-path: polygon(0 0, 100% 0, 85% 100%, 0 100%);
  z-index: 2; // 确保在图片上方显示
  white-space: nowrap;
}
</style>
