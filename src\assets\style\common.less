

.c-flex {
  display: flex;
}

.c-flex-center {
  display: flex;
  align-items: center;
}

.c-flex-between {
  display: flex;
  justify-content: space-between;
}

.c-flex-center-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}


.c-p-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}

.c-p-v-center {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}


.c-font10 {
  // 兼容差
  font-size: 10px;
}

.c-font12 {
  font-size: 12px;
}

.c-font14 {
  font-size: 14px;
}

.c-font16{
  font-size: 16px;
}

.c-font18 {
  font-size: 18px;
}

.c-font20{
  font-size: 20px;
}

.c-bold {
  font-weight: bold;
}

.c-lh20{
  line-height: 20px;
}

.c-lh22{
  line-height: 22px;
}

.c-lh24{
  line-height: 24px;
}

.c-wideBold {
  font-weight: bold;
  font-family: "Audi-WideBold";
}

.c-extendedBold {
  font-family: "Audi-ExtendedBold";
}

.c-margin-top {
  margin-top: 20px;
}


.c-footer-shadow{
  box-shadow: 0px 0px 16px 0px rgba(0,0,0,0.08);
}

@FooterHeight: 132px;  // 底部footer栏高度
@HeaderHeight: 60px;   // header + bar 栏高度(动态获取, 只是做scroll滚动 高度计算设置,先写死)
@TabHeight: 42px;   // tab栏高度