<template>
  <div class="hot-recommended">
    <div style="margin-bottom: 10px;">
      <div
        v-for="({title, des, tag, img}, index) in recommArr"
        :key="index"
        class="box"
      >
        <div style="display: flex;justify-content: flex-start;">
          <div style="width:104px; margin: 10px">
            <img
              style="width: 104px;height: 104px;"
              :src="img[$route.query.idx]"
              alt=""
            >
          </div>
          <div style="padding-top: 28px">
            <div
              @click="clickConfigration(title, index)"
              style="font-size: 16px;font-weight: bold;color: #000000;"
            >
              {{ title }}
              <van-icon name="arrow" />
            </div>
            <div style="padding-top: 8px;font-size: 12px;color: #000000;">
              {{ des }}
            </div>
            <div style="padding-top: 5px;">
              <van-tag
                v-for="(item, i) in tag[idx]"
                :key="i"
                plain
                type="primary"
                color="#999"
                text-color="#333"
                style="margin-right:5px"
              >
                {{ $route.query.idx == 0 && index && i ? '定金50,000元' : (idx == 2 && index && i ? '定金50,000元' : item) }}
              </van-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="hot-recommended-main">
      <van-tabs
        v-model="activeName"
        color="#000000"
        shrink
        line-height="2"
        line-width="64"
        title-inactive-color="#999999"
        title-active-color="#000000"
      >
        <van-tab
          title="爆款推荐"
          name="hotrecommended"
          title-style="max-width: 50%;"
        >
          <div
            class="tab-common tab-hotrecommended"
            v-if="pageHotRecommendCarList.length > 0"
          >
            <div
              @click="selectRecommendCar(item)"
              v-for="(item, index) in pageHotRecommendCarList"
              :key="index"
              class="item-card-common1"
            >
              <img
                :src="BaseConfigrationOssHost + item.imageUrl | imgFix(400)"
                alt=""
              >
              <div class="card-info">
                <div
                  v-if="idx == 0"
                  class="card-carname"
                >
                  {{ item.modelLineName }}
                  <img
                    class="kmwl"
                    v-if="item.tags && item.tags.find(t=> t.tagName == '即将售罄')"
                    src="@/assets/img/kmwl.png"
                    alt=""
                  >
                </div>
                <div
                  v-if="idx == 1 || idx == 2"
                  class="card-carname"
                >
                  {{ formatName(item.modelLineName)[0] }}<br>
                  {{ formatName(item.modelLineName)[1] }}
                </div>
                <!-- <div v-if="idx == 2" class="card-carname">
                  {{item.modelLineName}}
                </div> -->

                <div
                  v-if="item.price"
                  class="card-price"
                >
                  ￥{{ item.price | formatPrice }} {{ postPriceText (item.modelLineCode) }}
                </div>
                <div
                  v-if="!item.styleName"
                  class="card-deliverydate"
                  style="min-height: 24px;"
                >
                  <span @click.stop="toConfigTable(item)">查看详情</span>
                  <img
                    class="card-arrow"
                    src="@/assets/img/icon_20.png"
                    alt=""
                  >
                </div>
              </div>
            </div>
          </div>
        </van-tab>
        <van-tab
          title="当季热销"
          name="seasonlimited"
          v-if="0 && $route.query.env != 'minip'"
        >
          <div class="tab-common tab-hotrecommended">
            <div
              @click="tobuy(item)"
              v-for="(item,index) in bestRecommendCarList"
              :key="index"
              class="item-card-common item-card-actived"
            >
              <img
                :src="BaseConfigrationOssHost + item.imageUrl | imgFix(400)"
                alt=""
              >
              <div class="card-info">
                <div class="card-carname">
                  {{ item.modelLine.modelLineName }}
                </div>
                <div
                  v-if="idx != 2"
                  class="card-price"
                >
                  ￥{{ item.totalPrice | formatPrice }}
                </div>
                <div
                  v-if="idx == 2"
                  class="card-price"
                >
                  {{ item.totalPrice | formatPrice }}
                </div>
                <!-- <div class="card-deliverydate">
                  {{ '2周内交付' }}
                </div> -->
              </div>
              <div class="card-buy">
                <span>立即订购</span>
                <img
                  class="card-arrow"
                  src="@/assets/img/icon_20.png"
                  alt=""
                >
              </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <network @reload="networkReload()" />
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import Vue from 'vue'
import {
  Tab, Tabs, Toast, Tag
} from 'vant'
import {
  getBestRecommendConfig
} from '@/configratorApi/index'
import {
  getOtds, getAPITimeOutTesting
} from '@/api/api'
import url from '@/config/url'
import network from '@/components/network.vue'

Vue.use(Tag)
Vue.use(Tab)
Vue.use(Tabs)
Vue.use(Toast)
export default {
  components: {
    network
  },
  data() {
    return {
      recommArr: [
        {
          id: 0,
          title: '个性定制',
          des: '当季热门装备，即享个性定制',
          tag: [
            ['快速交付', '定金20,000元'], // a7
            ['预计4周快速交付', '定金20,000元'], // q5e
            ['快速交付', '定金20,000元'] // q6
          ],
          img: [require('@/assets/car/recom/3.png'), require('@/assets/car/recom/1.png'), require('@/assets/car/recom/5.png')]
        },
        {
          id: 1,
          title: '私人高定',
          des: '百万种个性化组合，全方位私人定制',
          tag: [
            ['预计3个月交付', '定金50,000元'], // a7
            ['预计3个月交付', '定金50,000元'], // q5e
            ['预计3个月交付', '定金50,000元']// q6
          ],
          img: [require('@/assets/car/recom/4.png'), require('@/assets/car/recom/22.png'), require('@/assets/car/recom/6.png')]
        }
      ],
      idx: '0',
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      activeName: 'hotrecommended'
      // recommendCarList: []
    }
  },
  computed: {
    ...mapGetters({
      pageHotRecommendCarList: 'pageHotRecommendCarList',
      customSeriesId: 'currentCustomSeriesId'
    }),
    ...mapState({
      bestRecommendCarList: 'bestRecommendCarList'
    })
  },
  async created() {
    this.initData();
    // const res = await getCarList()
    // if (res.data?.data) {
    //   const carData = res.data.data[idx]
    //   if (!carData) {
    //     return
    //   }

    //   // a7
    //   if (idx === '0') {
    //     this.getA7lRecommendList(carData.customSeriesId)
    //   } else {
    //     this.getQ5Q6RecommendList(carData.customSeriesId)
    //   }

    //   // 获取当季推荐的列表(未上线..)
    //   // this.$store.dispatch('getBestRecommendCar', { customSeriesId })
    // }
  },
  mounted() {

  },
  methods: {
    // a7 热门推荐车列表
    // async getA7lRecommendList(customSeriesId) {
    //   const res1 = await getRecommendCar(customSeriesId)
    //   if (res1.data.code === '00' && res1.data.data.length > 0) {
    //     this.recommendCarList = res1.data.data
    //   }
    // },

    // // q5 q6热门推荐车列表
    // async getQ5Q6RecommendList(customSeriesId) {
    //   const res = await recomStyleFix({ customSeriesId: customSeriesId })
    //   const data = res.data.data
    //   let arr = []

    //   data.forEach((element, i) => {
    //     if (!element.styleVo) {
    //       element = { element, ...element.recommendCarSphereVo }
    //       arr.push(element)
    //     } else {
    //       element.imageUrl = element.styleVo.imageUrl
    //       element.modelLineName = element.styleVo.styleName
    //       element.styleName = element.styleVo.styleName
    //       element.modelLineId = element.styleVo.styleId
    //       element.price = `${element.styleVo.price}  起`
    //       // if (element.styleVo.styleId == "c44c97de-4d44-4ba7-a93d-2f00583b5d04") {
    //       //   element.price = '552,100' + '  起'
    //       // }
    //       arr.push(element)
    //     }
    //   })
    //   // if(data[0] && data[0]?.recommendCarSphereVo) {
    //   //   data[0] = data[0].recommendCarSphereVo
    //   // }
    //   if (arr && arr.length) {
    //     arr = arr.filter((f) => f.modelLineCode !== 'G4ICF3002')
    //   }
    //   this.recommendCarList = arr
    // },

    async initData(){
      const { idx } = this.$route.query
      this.idx = idx
      getAPITimeOutTesting();
      await this.$store.dispatch('getHotRecommendCarList')
    },

    clickConfigration(e, index) {
      this.$storage.setPlus('semi-definite', e)
      const minip = this.$route.query.env === 'minip' // debugger
      if (e === '个性定制') this.$storage.setPlus('entryPoint', minip ? 'MINIP_MEASURE' : 'ONEAPP_MEASURE')
      if (e === '私人高定') this.$storage.setPlus('entryPoint', minip ? 'MINIP_PERSONAL' : 'ONEAPP_PERSONAL')

      try {
        // 埋点
        let localuserInfo = localStorage.getItem('userInfo')
        localuserInfo = JSON.parse(JSON.stringify(localuserInfo))
        let user_id = ''
        if (localuserInfo?.accountId) {
          user_id = localuserInfo?.accountId
        } else {
          const userInfo = JSON.parse(localStorage.getItem('userInfo'))
          user_id = userInfo?.accountId
        }
        const isMobi = window?.location?.href?.includes('cc_mobi') // cc_mobi
        const sensorsName = ['clickPersonalTailor', 'clickAdvancedCustomization'][index] // personal_tailor
        const para = {
          user_id: user_id,
          time: `${new Date().getTime()}`,
          name: this.recommArr[index].title,
          channel: minip ? 3 : (isMobi ? 1 : 2) // 渠道id(1、奥迪官网：2、奥迪APP:3：上汽奥迪小程序),必填
        }
        user_id && this.$sensors.track(sensorsName, para)
        // 埋点 end
      } catch (e) {
        this.toConfigrationPage(index)
      }

      this.toConfigrationPage(index)
    },

    toConfigrationPage(carIndex) {
      const { ccid, orderStatus, orderId } = this.$route.query
      this.$router.push({
        path: '/configration',
        query: {
          ccid,
          orderStatus,
          orderId,
          idx: this.idx,
          tabIndex: '0',
          definedCar: carIndex // 0 个性定制 1 私人高定
        }
      })
    },

    formatName(val) {
      let index = val?.length
      if (!index) return ['', '']
      if (this.idx === '1' || this.idx === '2') {
        for (let i = 0, l = val.length; i < l; i++) {
          if (val.charCodeAt(i) > 255) {
            index = i
            break
          }
        }
      }
      const str1 = val.substring(0, index)
      const str2 = val.substring(index)
      return [str1, str2]
    },

    // 选择爆款推荐车型
    async selectRecommendCar(item) {
      const {
        ccid, orderStatus, orderId, env
      } = this.$route.query
      const minip = env === 'minip'
      this.$storage.setPlus('semi-definite', item.measure ? '个性定制' : '私人高定') // 1 '私人订制' : '0个性定制'
      this.$storage.setPlus('entryPoint', minip ? 'MINIP_RECOMMEND' : 'ONEAPP_RECOMMEND')
      try {
        const isMobi = window.location.href?.includes('cc_mobi') // cc_mobi

        this.$sensors.track('clickBestRecommendCar', {
          user_id: JSON.parse(this.$storage.get('userInfo')).accountId,
          time: `${new Date().getTime()}`,
          tab_name: '爆款推荐',
          channel: minip ? 3 : (isMobi ? 1 : 2), //	渠道id(1、奥迪官网：2、奥迪APP:3：上汽奥迪小程序)
          modelLineCode: item.modelLineCode, // 所选车型 typecode + cnName modelLineCode: "498B2Y002"
          modelLineName: item.modelLineName,
          tabIndex: 1
        })
      } catch (e) {
      }


      // selectHotCar
      if (this.idx === '0' || (this.idx === '2' && !item.styleName)) {
        const seriesName = {
          49: 'A7L',
          G4: 'Q5E',
          G6: 'Q6'
        }[item.customSeriesCode]
        this.$storage.setPlus('selectHotCar', {
          modelLineCode: item.modelLineCode,
          modelLineId: item.modelLineId,
          modelLineName: item.modelLineName,
          price: item.price,
          customSeriesCode: item.customSeriesCode,
          customSeriesId: item.customSeriesId,
          modelYear : item.modelYear,
          seriesName: seriesName,
          from: 'recomPage'
        })
      }

      // const { packetEquity } = item
      // console.log('id', item, item.modelLineCode)
      const toCarVersionPage = () => {
        this.$router.push({
          path: '/carVersion',
          query: {
            ccid,
            orderStatus,
            orderId,
            idx: this.idx,
            modelLineCode: item.modelLineCode,
            modelLineId: item.modelLineId,
            customSeriesId: this.customSeriesId,
            from: 'hotRecom'
          }
        })
      }

      const toConfigrationPage = () => {
        this.$router.push({
          path: '/configration',
          query: {
            ccid,
            orderStatus,
            orderId,
            modelLineId: item.modelLineId,
            tabIndex: '1',
            from: 'hot',
            idx: this.idx,
            definedCar: item.measure === 1 ? 0 : 1 // measure:1半订制
          }
        })
      }

      if (this.idx === '0') {
        // const packetEquityList = packetEquity.filter((f) => f.status !== 0)
        // if (packetEquityList.length === 0) {
        //   toConfigrationPage()
        // } else {
        //   toCarVersionPage()
        // }

        /**
         * a7l modelLineId 原数据里的styleId
         *'4548d1fc-0c6f-4e57-a77d-71795fd8eae5' 奥迪A7L 黑武士版
         *'c4136b39-6520-44ef-8c7b-0fac9a46d5fc' 奥迪A7L 影武士版
         */
        const A7L_CARVERSION = [
          '4548d1fc-0c6f-4e57-a77d-71795fd8eae5',
          'c4136b39-6520-44ef-8c7b-0fac9a46d5fc'
        ]

        if (A7L_CARVERSION.includes(item.modelLineId)) {
          toCarVersionPage()
        } else {
          toConfigrationPage()
        }
        return
      }

      if (item.styleName) {
        return toCarVersionPage()
      }

      toConfigrationPage()
    },
    async toConfigTable(item) {
      // if (item.modelLineName == '筑梦新生版' && this.$route.query?.env != 'minip') {
      //   return
      // }
      // const path = this.orderh5BaseUrl+ `car-config-table?modellineId=${item.modelLineId}&carModelName=${item.modelLineName}&seriesName=${item.customSeriesCode}`
      // await nativeCallback("audiOpen", {
      //   path: path,
      //   params: Base64.encode(JSON.stringify({ data: this.carTypeIndex }))
      // });
      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: item.modelLineId,
          carModelName: item.modelLineName,
          seriesName: item.customSeriesCode,
          special: true // 爆款推荐进入
        }
      })
    },
    tobuy(item) {
      const minip = this.$route.query.env == 'minip'
      this.$storage.setPlus('entryPoint', minip ? 'MINIP_HQ_RECOMMEND' : 'ONEAPP_HQ_RECOMMEND')
      try {
        const path = window?.location?.href?.includes('cc_mobi') // cc_mobi
        const minip = this.$route.query.env == 'minip'
        this.$sensors.track('clickHotRecommendCar', {
          user_id: JSON.parse(this.$storage.get('userInfo')).accountId,
          time: `${new Date().getTime()}`,
          tab_name: '当季热销',
          channel: minip ? 3 : (path ? 1 : 2), //	渠道id(1、奥迪官网：2、奥迪APP:3：上汽奥迪小程序)
          modelLineCode: item.modelLine.modelLineCode, // 所选车型 typecode + cnName modelLineCode: "498B2Y002"
          modelLineName: item.modelLine.modelLineName,
          car_config: Array.from(item.options || [], (x) => x.optionCode)
        })
      } catch (e) {
        this.getfn(item)
        return
      }
      this.getfn(item)
    },
    getfn(item) {
      getBestRecommendConfig(item.bestRecommendId, 'ONEAPP_HQ_RECOMMEND').then((res) => {
        let seriesId = ''
        if (this.idx === '0') {
          seriesId = 'ADA7'
        } else {
          seriesId = 'G4'
        }
        getOtds({ useType: 1, seriesId: seriesId }).then((res1) => {
          const ccid = res.data.data.ccId
          const skuid = res1.data.data.prodSkuId
          if (ccid && skuid) {
            this.$router.push({
              path: '/quotation',
              query: { ccid: ccid, skuid: skuid, dealerCode: '76600019' }
            })
          }
        })
      })
    },
    postPriceText(code) {
      return 'G6ICAY002,G6ICAY003,G6ICAY004,G6ICAY005'.includes(code) ? '起' : ''
    },

    async networkReload(){
      await this.initData();
      Toast.clear();
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  margin: 10px;
  margin-bottom: 0px;
  background: url('../../assets/car/recom/bg.png') no-repeat;
  background-size: cover;
}

.kmwl {
  width: 50px;
  height: 16px;
  position: relative;
  margin-bottom: 3px;
}

.hot-recommended {
  height: 100%;
  display: flex;
  flex-direction: column;

  .personalization-box {
    padding: 0 16px;
    box-sizing: border-box;
    margin-bottom: 24px;
  }

  .personalization {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 290px;
    border: 1px solid #E5E5E5;

    img {
      width: 100%;
      height: 208px;
    }

    .personalization-footer {
      padding: 19px 17px;
      display: flex;
      justify-content: space-between;
      color: #000000;
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .personalization-footer-info {
        font-family: "Audi-WideBold";

        .personalization-footer-carname {
          font-size: 16px;
          line-height: 20px;
        }

        .personalization-footer-price {
          font-size: 12px;
          line-height: 15px;
          color: #5B5B5B;
          margin-top: 2px;
        }
      }

      .personalization-footer-button {
        width: 112px;
        height: 44px;
        border: 1px solid #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        line-height: 20px;
      }
    }
  }

  .hot-recommended-main {
    height: 100%;
    flex: 1;

    .tab-common {
      padding: 16px;
      padding-top: 8px;
      box-sizing: border-box;

      .item-card-common {
        width: 100%;
        min-height: 120px;
        background: #FFFFFF;
        border: 1px solid #E5E5E5;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 5px;
        position: relative;
        margin-bottom: 16px;

        >img {
          height: 112px;
          width: 114px;
          margin-right: 13px;
          object-fit: contain;
        }

        .card-info {
          color: #000000;
          line-height: 20px;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding-bottom: 20px;

          .card-carname {
            font-family: Audi-WideBold;
            font-size: 16px;
          }

          .card-price {
            font-family: Audi-WideBold;
            font-size: 14px;
            color: #5B5B5B
          }

          .card-deliverydate {

            font-size: 14px;
            color: #5B5B5B;

            .card-arrow {
              height: 19px;
              width: auto;
              margin-top: -1px;
            }
          }
        }

        .card-buy {
          position: absolute;
          display: flex;
          align-items: center;
          bottom: 6px;
          right: 20px;
          color: #adadad;
          font-size: 12px;

          .card-arrow {
            height: 19px;
            width: auto;
          }
        }
      }

      .item-card-common1 {
        width: 100%;
        height: 120px;
        background: #FFFFFF;
        border: 1px solid #E5E5E5;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 16px;
        position: relative;
        margin-bottom: 16px;
        overflow: hidden;

        >img {
          position: absolute;
          display: block;
          height: 110px;
          width: 200px;
          right: -20px;
          bottom: -10px;
          object-fit: contain;
        }

        .card-info {
          color: #000000;
          line-height: 20px;
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .card-carname {
            font-family: Audi-WideBold;
            font-size: 16px;
          }

          .card-price {
            font-family: Audi-WideBold;
            font-size: 14px;
            color: #5B5B5B;
            margin-top: 12px;
          }

          .card-deliverydate {

            font-size: 14px;
            color: #5B5B5B;

            .card-arrow {
              height: 19px;
              width: auto;
              margin-top: -1px;
            }
          }
        }

        .card-buy {
          position: absolute;
          display: flex;
          align-items: center;
          bottom: 22px;
          right: 20px;
          color: #adadad;
          font-size: 12px;

          .card-arrow {
            height: 19px;
            width: auto;
          }
        }
      }

      .item-card-actived {
        border: 1px solid #000
      }
    }
  }
}

.van-icon::before {
  vertical-align: middle;
}

/deep/ .van-tabs {
  .van-tabs__wrap {
    height: 40px;
  }

  .van-tab--active {
    font-family: "Audi-WideBold";
  }

  .van-tabs__line {
    bottom: 19px;
  }

  .van-tab {
    font-size: 16px;
    line-height: 24px;
  }
}
</style>
