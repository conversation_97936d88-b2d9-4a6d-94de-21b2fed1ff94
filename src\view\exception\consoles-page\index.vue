<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-10-11 10:41:40
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-10-12 11:16:56
 * @FilePath     : \src\view\exception\consoles-page\index.vue
 * @Descripttion :
-->
<template>
  <div
    :class="[
      'exception-consoles-page',
      'page-wrapper',
      headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
    ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    >
      <!-- <template #right>
        <p class="btn" @click="$router.push({ name: 'xxxxxxx' })">
          说明
        </p>
      </template> -->
    </header-custom>
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <charging-pile-qrcode v-if="templateName === 'charging-pile-qrcode'" />
    </div>
  </div>
</template>

<script>
import store from '@/store'
import HeaderCustom from '@/components/header-custom.vue'
import ChargingPileQrcode from './errors/charging-pile-qrcode.vue'
import { EXCEPTION } from '@/config/conf.data'

const { CONSOLES_PAGE } = EXCEPTION
export default {
  name: 'ExceptionPage',
  components: {
    'header-custom': HeaderCustom,
    'charging-pile-qrcode': ChargingPileQrcode
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      templateName: ''
    }
  },
  created() {
    this.handleProcessData()
  },
  methods: {
    handleProcessData() {
      const { $route: { query: { error } } } = this || ''
      const { template, title } = CONSOLES_PAGE.filter((i) => i.error === error)[0] || ''
      this.templateName = template
      console.log('%c [ template ]-55', 'font-size:14px; background:#cf222e; color:#fff;', template)
      store.commit('setTitle', title || '解析错误')
    },
    handleLeftBack() { }
  }
}
</script>
<style lang="less" scoped>
.exception-consoles-page,
.main-wrapper {
  height: 100%;
  box-sizing: border-box;
}
</style>
