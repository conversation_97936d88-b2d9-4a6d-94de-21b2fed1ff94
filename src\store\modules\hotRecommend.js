import Vue from 'vue'
import { getQueryParam, checkV2, isEmptyObj } from '@/utils/index'
import {
  getCarList, getPriceCompute, getMeasurePriceCompute, getSibColorInterieur, getInteriorEih, getInteriorChair, getExterior, getModelHub, measureQuery, getV2CcInfo, getCcMeasure, updateCcInfo, getCcInfo, getRecommendCar, recomStyleFix, getStyleList
} from '@/configratorApi/index'


export default {
  state: {
    currentCarInfo: {}, // 当前车系数据
    hotRecommendCarList: [] // 爆款推荐车型列表
  },

  getters: {
    // return a7l | q5e | q6
    // currentSeriesName(state) {
    //   if (isEmptyObj(state.currentCarInfo)) {
    //     // console.error('currentCarInfo 为空')
    //     return ''
    //   }
    //   return state.currentCarInfo.seriesName.toLowerCase()
    // },

    // 当前车系id
    currentCustomSeriesId(state) {
      if (isEmptyObj(state.currentCarInfo)) {
        // run dispatch('getCurrentCarInfo')
        // console.error('currentCarInfo 为空')
        return ''
      }
      return state.currentCarInfo.customSeriesId
    },

    // 热门推荐页面显示的数据
    pageHotRecommendCarList(state, getters) {
      if (!getters.currentSeriesName) {
        console.error('currentSeriesName 为空')
        return []
      }
      // 数据结构改变,a7l 也需要处理数据
      // if (getters.currentSeriesName === 'a7l') {
      //   console.log('a7l', state.hotRecommendCarList)
      //   return state.hotRecommendCarList
      // }

      // q5e q6
      let arr = []
      state.hotRecommendCarList.forEach((element, i) => {
        if (!element.styleVo) {
          element = { element, ...element.recommendCarSphereVo }
          arr.push(element)
        } else {
          element.imageUrl = element.styleVo.imageUrl
          element.modelLineName = element.styleVo.styleName
          element.styleName = element.styleVo.styleName
          element.modelLineId = element.styleVo.styleId
          element.price = `${element.styleVo.price}  起`
          arr.push(element)
        }
      })

      // G4ICF3002 Audi Q5 e-tron edition one 艺创典藏版
      if (arr.length > 0) {
        arr = arr.filter((f) => f.modelLineCode !== 'G4ICF3002')
      }

      // a7l q6 价格升序
      if (getters.currentSeriesName !== 'q5e') {
        arr.sort((a, b) => parseInt(a.price) - parseInt(b.price))
      }

      return arr
    }
  },

  mutations: {
    setCurrentCarInfo(state, data) {
      state.currentCarInfo = data
    },
    setHotRecommendCarList(state, data) {
      state.hotRecommendCarList = data
    }
  },

  actions: {

    // 获取当前车系数据
    async getCurrentCarInfo({ commit, state }) {
      if (!isEmptyObj(state.currentCarInfo)) {
        // console.log('run CarInfo( cache')
        return
      }
      //  idx =>  0: a7l, 1: q5e, 2:q6
      const carIdx = getQueryParam('idx')
      if (!carIdx) {
        return console.error('URL 参数里无 idx, 无法获取车系id')
      }
      const res = await getCarList()
      if (res.data.code !== '00') {
        return console.error('getCarList 接口请求失败')
      }
      const currentCarInfo = res.data.data[carIdx]
      commit('setCurrentCarInfo', currentCarInfo)
    },

    // 获取爆款推荐车型列表
    async getHotRecommendCarList({
      state, dispatch, commit, getters
    }) {
      await dispatch('getCurrentCarInfo')
      const customSeriesId = getters.currentCustomSeriesId
      let res = null
      if (state.currentSeriesName === 'a7l') {
        res = await getRecommendCar(customSeriesId)
      } else {
        res = await recomStyleFix({ customSeriesId })
      }
      if (res.data.code !== '00') {
        return console.error('getHotRecommendCarList 接口请求失败')
      }
      commit('setHotRecommendCarList', res.data.data)
    },

    /**
     * 暂时未使用
     * 获取配置线
     * 使用场景：a7l + q6 + 高定q5e
     */
    async getCarModelLine({ state, dispatch }) {
      const { customSeriesId } = await dispatch('getCurrentCarInfo')
      const param = {
        customSeriesId,
        type: checkV2() ? 2 : 1
      }
      const res = await getStyleList(param)
    }
  }

}
