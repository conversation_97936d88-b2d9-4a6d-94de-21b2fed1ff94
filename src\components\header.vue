<template>
  <div class="header">
    <div class="content">
      <!-- 旧版关闭icon -->
      <!-- <div class="btn-back">
        <van-icon
          class="sc-padding"
          name="arrow-left"
          size="18px"
          @click="toBack"
        />
      </div> -->
      <!-- 新版关闭icon -->
      <div v-if="showBack" :class="['header-btn', btnBackPurple ? 'btn-backPurple' : 'btn-back']" @click="toBack"></div>
      <!-- <div
        class="header-btn btn-close"
        v-if="showAddConfigBtn"
        @click="close".btn-bac
      /> -->

      <div class="title" v-if="$route.query.path != '/theEquity'">
        {{
          $route.query.path == '/hot-recommended'
            ? '车辆推荐'
            : $store.state.title
        }}
        <img
          @click="audiShareFn"
          class="shB"
          v-if="shareIconVisible"
          src="@/assets/img/icon18.png"
        />
      </div>
      <div class="title" v-if="$route.query.path == '/theEquity'">
        {{ $store.state.title }}
      </div>
      <div class="layout-right">
        <span v-if="0" @click="addConfig" class="c-font14">加入配置单</span>

        <van-icon name="share-o" v-if="false" @click="audiShare" />
      </div>
    </div>
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols lan-tips"
      v-model="show"
      cancel-button-text="执意退出"
      confirm-button-text="继续下订"
      show-cancel-button
      message="抢先下订可锁定限时车主权益"
      @confirm="concel"
      @cancel="confirm"
    />
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols"
      v-model="showConfirm"
      cancel-button-text="执意退出"
      confirm-button-text="继续下订"
      show-cancel-button
      message="抢先下订优先提车"
      @confirm="concel"
      @cancel="confirm"
    />

    <canvasCard
      ref="canvasImg"
      @getCanvasImg="getCanvasImg"
      :imgurl="carImgUrl"
      :title="carModelName + ' 配置单 '"
    />
  </div>
</template>
<script>
import Vue from 'vue'
import { Dialog, Icon, Toast } from 'vant'
import { mapGetters, mapState } from 'vuex'
import { addCarShoppingCart, getShareUrl } from '@/api/api'
import { backFromCrossDomain, backFromUseDomain, callNative } from '@/utils/index'
import confiUrl from '@/config/url'
import canvasCard from '@/components/canvas-card.vue'
import { getEnergyStyleList, getModelLineQuery } from '@/configratorApi/index'
import { getCarType } from '@/view/newConfigration/util/helper.js'

const baseOssHost = confiUrl.BaseOssHost

Vue.use(Icon).use(Dialog).use(Toast)

export default {
  data() {
    return {
      statusBarHeight: 0,
      navigationBarHeight: 50,
      show: false,
      showConfirm: false,
      rightIcon: false,
      isQuo: false,
      shareIconVisible: false,
      btnBackPurple:'',
    }
  },
  components: {
    canvasCard
  },
  computed: {
    showAddConfigBtn() {
      const { orderStatus, env } = this.$route.query
      // 报价单页显示的按钮, orderStatus 代表从详情页跳转过来
      const url = window.location.href || ''
      const isQuotation = url.includes('/quotation?')
      return isQuotation && !orderStatus
    },

    showBack() {
      const { env } = this.$route.query
      // env = pub时不显示返回按钮
      return env !== 'pub'
    },

    ...mapState({
      standardConfigData: 'standardConfigData',
      carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn, // 车辆名称
      carImgUrl: (state) => (state.carDetail.configDetail?.carModel?.imageUrl
        ? `${baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl}`
        : ''), // 主图
      bestRecommendId: (state) => state.carDetail.bestRecommendId, // 是否是推荐车型
      carDetail: (state) => state.carDetail
    })
  },
  watch: {
    $route: {
      handler: function (val, oldVal) {
        const { fromPage } = this.$route.query


         //purple项目加主题色
        this.btnBackPurple = val.query?.fromType === 'fromPurple'

        this.isQuo = val.path == '/quotation'
          && !(fromPage && (fromPage == 'orderDetail' || fromPage == 'spec'))
        if (val.path == '/order/new-money-detail' && fromPage && fromPage == 'successStatus') {
          this.handleListeningNativeBack()
        } else {
          if (window.listeningBacktrack) {
            callNative('listeningNativeBacktrack', { from: 'header-back', page: this.$route.name, action: 'destroy' })
          }
          window.listeningBacktrack = null
        }

        // 是否显示分享按钮
        const isQuoPage = val.path === '/quotation'
        this.shareIconVisible = isQuoPage && fromPage !== 'orderDetail'
      },
      // 深度观察监听
      deep: true
    }
  },
  mounted() {
    console.log('$route.path==============', window.location.href)
    this.$bus.$on('rightShow', (bool, imgUrl = '') => {
      // 这些变量真的用了吗???
      this.rightIcon = bool
      this.imgUrl = imgUrl
    })
    console.log('sss', this.$route)
  },
  inject: ['reload'],
  methods: {
    ...mapGetters(['getDevice']),
    async handleListeningNativeBack() {
      const {
        $route
      } = this
      const action = 'stop'
      if (action === 'stop' && !window.listeningBacktrack) {
        // 原生调用Vue的方法，需要把方法挂在Window下面
        window.listeningBacktrack = this.listeningBacktrack
      }
      const backtrack = await callNative('listeningNativeBacktrack', { from: 'header-back', page: $route.name, action })
      console.log('%c [ action, backtrack ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', action, backtrack)
      console.log('9999', 99999)
    },
    listeningBacktrack() {
      this.handleLeftBackBtn()
    },
    async handleLeftBackBtn() {
      const { query, path } = this.$route
      if (path == '/order/new-money-detail' && query.fromPage && query.fromPage == 'successStatus') {
        callNative('goHome', {})
      } else {
        callNative('prepage', { times: 1 })
      }
    },
    async bridge() {
      console.log(this.$route.path)
      if (
        [
          '/order/payment-success',
          '/order/payment-error',
          '/order/submit-success',
          '/aftersales/service-success',
          '/hot-recommended'
        ].includes(this.$route.path)
      ) {
        if (
          '/hot-recommended'.includes(this.$route.path)
          && this.$route.query?.form == 'customerService'
        ) {
          // callNative('close', { type: 'service' })
          callNative('prepage', { times: 1 })
        } else {
          this.goLoveCar()
        }
      } else if (
        [
          '/order/unsubscribe-succeed',
          '/order/money-detail',
          '/testdrive/appointment-list'
        ].includes(this.$route.path)
      ) {
        await callNative('goMinePage', {}) // 返回“我的”页
      } else if (
        [
          '/order/new-money-detail'
        ].includes(this.$route.path)
      ) {
        // callNative('openRoutePath', { path: 'scaudi://mall/orderlist' })
        callNative('goHome', {})
      } else if (['/testdrive/detail'].includes(this.$route.path)) {
        this.$router.push({
          path:  this.$route.query?.fromType == 'fromPurple' ?  '/testdrive/appointment-list?fromType=fromPurple' : '/testdrive/appointment-list'
        })
      } else if (
        [
          '/contract-info',
          '/buycar-agreement',
          '/aftersales/store-address'
        ].includes(this.$route.path)
      ) {
        this.$router.go(-1)
        // const { orderId } = this.$store.state
        // this.$router.push({
        //   path: '/order/money-detail',
        //   query: { orderId }
        // })
      } else if (
        ['/certification/my-certification'].includes(this.$route.path)
      ) {
        // 我的车辆返回关闭activity
        localStorage.setItem('agreeToRealNameAuthentication', 0)
        callNative('close', { type: 'address' })
      } else if (
        this.$route.path === '/certification/my-confirm'
        && this.$route.query.replace === '1'
      ) {
        // 我的车辆返回关闭activity
        localStorage.setItem('agreeToRealNameAuthentication', 0)
        callNative('close', { type: 'address' })
      } else if (['/configration'].includes(this.$route.path)) {
        const { idx } = this.$route.query
        if (this.$route.query?.form == 'customerService') {
          // callNative('close', { type: 'service' })
          callNative('prepage', { times: 1 })
        } else {
          if (idx == 1) {
            // q5e
            this.goLoveCar()
          } else {
            this.$router.push({
              path: '/configration',
              query: {
                idx: this.$route.query.idx
              }
            })
          }
        }
      } else if (this.$route.path.includes('EquipmentBox')) {
        // this.$store.commit('setShowV2Popup', false)
        // tag EquipmentBox 路由在新版上线后会弃用
        this.$router.push({
          path: '/configration',
          query: {
            idx: this.$route.query.idx,
            tabIndex: '4',
            definedCar:
              this.$storage.getPlus('semi-definite') == '个性定制' ? 0 : 1,
            visible: 1
          }
        })
      } else if (
        [
          '/car-config-table',
          '/configrationOptionDetail',
          '/commpendOptionList',
          '/quotation'
        ].includes(this.$route.path)
        && this.$route.query?.customBack === 'newConfigration'
      ) {
        this.$router.back()
      } else {
        if (
          [
            '/certification/car-certification-in',
            '/certification/driving-certification-in'
          ].includes(this.$route.path)
        ) {
          const model = this.$storage.get('certificationList') || '{}'
          const customerCarList = JSON.parse(model)
          // 车辆绑定成功页面返回调用SC的接口
          callNative('vehicleStatusChange', {
            bind: true,
            vin:
              customerCarList.updateVehicleLicenseMainVin
              || customerCarList.vin,
            scene: 'default',
            source: this.$store.state.pageSource
          })
        }

        console.log('是否进入', process.env.NODE_ENV)
        if (process.env.NODE_ENV === 'development') {
          console.log('生产环境')
          this.$router.back()
        } else {
          const data = await callNative('prepage', { times: 1 })
          console.log('goBack=', data)
        }
      }
    },

    toBack() {
      const {
        $route: { query }
      } = this

      if (this.$route.path.includes('/car-config-table')) {
        const CarConfigTable = this.$parent.$children.find(
          (i) => i.$vnode.tag.indexOf('CarConfigTable') !== -1
        )
        if (CarConfigTable) {
          CarConfigTable.SetSensorsTrackData('H5_CarConfiguration_Exit')
        }
      }

      if (
        (this.$route.path.includes('/order/money-detail') || this.$route.path.includes('/order/new-money-detail'))
        && this.$store.state.pageFrom === 'order-list'
      ) {
        // localStorage.removeItem('page-from')
        this.$store.commit('setPageFrom', '')
        callNative('close', {})
        return
      }

      // 认证页面 ，返回直接关闭webview
      if (this.$route.path.includes('/certification/my-certification')) {
        callNative('close', {})
        return
      }

      console.log(
        '%c [ query ]-228',
        'font-size:14px; background:#cf222e; color:#fff;',
        query
      )
      if (query?.from) {
        // const { nativeApp } = this.getDevice() || {}
        const {
          from, path, param, name, sign, times
        } = query
        if (from && name) {
          const back = backFromUseDomain({
            from,
            path,
            param,
            name,
            sign,
            times
          })
          if (back && sign !== 'native' && Object.keys(back).length) {
            const { params } = back || {}
            if (from === 'order-list') {
              this.$store.commit('setPageFrom', from)
            }
            this.$router.push({
              name,
              query: params
            })
          }
          return
        }
        if (from && path) {
          const { nativeApp } = this.getDevice() || {}
          backFromCrossDomain(from, path, param, nativeApp ? 'native' : '')
          return
        }
      }

      if (this.$route.path.includes('/EquipmentBox')) {
        // this.$router.push({
        //   path: '/configration',
        //   query: {
        //     idx: this.idx,
        //     tabIndex: '4',
        //     definedCar: 0
        //   }
        // })
        // this.$store.state.selectCarInfo
        // debugger
      }
      // const orderType =
      //   sessionStorage.getItem("orderType") == "0" ? true : false;
      // 检测是否支持JSBridge,如果支持表示在App里，不支持走普通的返回
      console.log(this.$store.state.allowBackVal)
      if (this.$route.path === '/order/detail') {
        if (this.bestRecommendId) {
          this.showConfirm = true
        } else {
          this.show = true
        }
      } else if (
        this.$route.path === '/order/user-agreement'
        || this.$route.path === '/configration/legal-provision'
      ) {
        if (this.$store.state.allowBackVal === '0') {
          this.bridge()
        } else {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '需仔细阅读5秒',
            forbidClick: true,
            duration: 800
          })
        }
      } else if (this.$route.path === '/enterpriseUsers/agreements') {
        localStorage.setItem('enterpriseUsersAgreement', 1)
        this.bridge()
      } else if (this.$route.path === '/enterprisingElite/agreements') {
        localStorage.setItem('enterprisingEliteAgreement', 1)
        this.bridge()
      } else if (this.$route.path === '/supplier/agreements') {
        localStorage.setItem('supplierEliteAgreement', 1)
        this.bridge()
      } else if (this.$route.path === '/government/agreements') {
        localStorage.setItem('governmentEliteAgreement', 1)
        this.bridge()
      } else if (this.$route.path === '/returnedElite/agreements') {
        localStorage.setItem('returnedEliteEliteAgreement', 1)
        this.bridge()
      } else if (this.$route.path === '/relativeInformation/agreements') {
        localStorage.setItem('relativeInformationAgreement', 1)
        this.bridge()
      } else if (
        this.$route.path === '/userFlexibleAuthentication/agreements'
      ) {
        localStorage.setItem('userFlexibleAuthenticationAgreement', 1)
        this.bridge()
      } else {
        this.bridge()
      }
    },
    async close() {
      this.goLoveCar()
    },

    // 返回爱车页
    async goLoveCar() {
      await callNative('goHome', {})
    },

    confirm() {
      // 埋点
      this.$bus.$emit('getParams', false)
      setTimeout(() => {
        this.bridge()
      }, 0)
    },
    concel() {
      // 埋点
      this.$bus.$emit('getParams', true)
      setTimeout(() => {
        this.show = false
        this.showConfirm = false
      }, 0)
    },

    async audiShare() {
      const shareImg = this.imgUrl
      const data = await callNative('audiShare', { shareImg: shareImg })
      console.log('audiShareSuccess=', data)
    },

    audiShareFn() {
      this.time = Date.now() + 10
      this.$refs.canvasImg.getCanvasImg()

      console.log('share')
      this.clickQuotationSensors('分享')
    },

    async getCanvasImg(e) {
      console.log(`图片+${e.fileStorageId}`, baseOssHost + e.fileUrl)
      const subTitle = this.standardConfigData.map((i) => i.name)
      const res = await getShareUrl()
      const path = `${res.data.data.configValue}#/carConfig?ccid=${this.$route.query.ccid}`
      console.log(baseOssHost + e.fileUrl)
      callNative('audiShare', {
        type: 'carConfig',
        path: path, // `${window.location.origin}/order/index.html#/car-config-other?ccid=${this.$route.query.ccid}`,
        imageUrl: baseOssHost + e.fileUrl,
        ccid: this.$route.query.ccid,
        title: `欢迎围观我的${this.carModelName}`,
        subTitle: `我的配置${subTitle.join('|')}`
      }).then((data) => {
        console.log(data, '分享回调')
      })
    },

    // 添加配置单到购物车列表
    async addConfig() {
      const { ccid, skuid } = this.$store.state
      const { data } = await addCarShoppingCart({ ccid, skuid })
      if (data.code === '00') {
        Toast({
          message: data.message,
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      }
    },

    // 报价单分享埋点
    clickQuotationSensors(buttonName) {
      const carMap = {
        49: {
          carName: 'A7L',
          idx: 0
        },
        G4: {
          carName: 'Q5 e-tron',
          idx: 1
        },
        G6: {
          carName: 'Q6',
          idx: 2
        }
      }
      const configDetail = this.$store.state.carDetail.configDetail

      const { modelLineCode, modellineId } = configDetail.carModel
      const { customSeriesId, seriesCode } = configDetail?.carSeries
      if (!carMap[seriesCode]) {
        console.error('未找到对应的车型:', seriesCode)
        return
      }
      const carType = getCarType()

      Promise.all([
        getModelLineQuery(modellineId),
        getEnergyStyleList(customSeriesId, carType)
      ]).then((res) => {
        const currentModelLine = res[0].data.data[0]
        const carList = res[1].data.data
        // 获取carVersion 字段
        let carVersionName = ''
        for (const car of carList) {
          for (const i of car.styleVos) {
            for (const j of i.modelLineList) {
              if (j.modelLineCode === modelLineCode) {
                carVersionName = i.styleName
                break
              }
            }
          }
        }
        const param = {
          source_module: 'H5',
          car_series: carMap[seriesCode].carName,
          car_type: currentModelLine.typeFlag,
          power_type: `${carMap[seriesCode].carName} ${currentModelLine.engine}`,
          car_version: carVersionName,
          delivery_type: '定制交付', // 快速交付|定制交付
          button_name: buttonName
        }
        this.$sensors.track('CC_Quotation_BtnClick', param)
      })
    }
  }
}
</script>
<style scoped lang="less">
@import '../assets/style/common.less';
.shB {
  width: 22px;
  height: 22px;
  position: absolute;
  z-index: 999;
  top: 9px;
  right: 0;
}

.sc-padding {
  padding: 8px 5px;
}

.header {
  position: relative;
  text-align: center;
  // border-bottom: 1px solid #e5e5e5;
  .content {
    // .c-font18;
    position: relative;
    margin: 0 16px;
    //display: flex;
    //align-items: center;

    // 旧版后退icon样式
    // > .btn-back {
    //   .c-p-v-center;
    //   .c-flex-center;
    //   left: 0;
    //   z-index: 10100;
    // }
    > .header-btn {
      position: absolute;
      width: 24px;
      height: 24px;
      top: 50%;
      transform: translate(0, -50%);
      z-index: 10000;
      &.btn-back {
        background: url('~@/assets/img/icon03.png') center/contain no-repeat !important;
      }
      &.btn-backPurple {
        background: url('~@/assets/img/back-left.png') center/contain no-repeat !important;
      }

      &.btn-close {
        left: 40px;
        background: url('~@/assets/img/icon05.png') center/contain no-repeat !important;
      }
    }

    > .layout-right {
      .c-p-v-center;
      right: 0;
      z-index: 10100;
    }

    .title {
      position: relative;
      // height: 50px;
      min-height: 44px;
      // .c-font18;
      font-weight: 400;
      width: 100%;
      padding: 0 30px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      box-sizing: border-box;
      font-size: 16px;
      color: #1a1a1a;
    }
  }
}
.bor {
  background: transparent;
  border-color: transparent;
}
/deep/.dialog {
  border-radius: 0;
  text-align: center;
  padding: 20px;
  .van-dialog__content {
    min-height: 0;
    .van-dialog__message {
      font-size: 16px;
      line-height: 36px;
      padding: 0;
    }
  }

  .van-dialog__footer {
    display: flex;
    flex-direction: column;
  }
  .van-button {
    width: 90%;
    height: 56px;
    left: 0;
    right: 0;
    margin: 8px auto 8px;
    background: #000;
    cursor: pointer;
    color: #fff;
    line-height: 56px;
    text-align: center;
  }
  .van-dialog__confirm {
    color: #000;
    background: #fff;
    border: 1px solid #000;
    box-sizing: border-box;
  }
}
.lan-tips.dialog{
  padding:24px !important;
  top: 50%;
  /deep/.van-dialog__content{
    padding: 4px 0 28px;
  }
  /deep/.van-dialog__footer{
    height: 44px;
    .van-button{
      height: 44px;
      line-height: 44px;
      font-size: 14px;
    }
  }
}
</style>
