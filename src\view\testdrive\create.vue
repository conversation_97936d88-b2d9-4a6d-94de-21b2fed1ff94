<template>
  <div id="detail">
    <div class="box_head">
      <img src="../../assets/img/bg04.jpg">
    </div>
    <div class="buyMess box">
      <van-form
        ref="form"
        @failed="failed"
        @submit="onSubmit"
      >
        <div
          class="box-field jt"
        >
          <label class="box-label">试驾车系</label>
          <van-field
            :label-align="labelAlign"
            input-align="right"
            :label-width="labelWidth"
            readonly
            clickable
            placeholder="请选择试驾车系"
            @click="formDataShow.showPicker = true"
            v-model="formData.testdriveSeries.customSeriesName"
            :rules="[{ required: true, message: '请选试驾车系！' }]"
          />
          <van-icon name="arrow" />
          <van-popup
            v-model="formDataShow.showPicker"
            :lazy-render="false"
            round
            position="bottom"
          >
            <van-picker
              ref="testdriveSeries"
              show-toolbar
              :columns="columns"
              value-key="customSeriesName"
              @confirm="onConfirm({value:$event,label:'testdriveSeries'})"
              @cancel="onCancel"
              :default-index="defaultIndex.testdriveSeries"
            />
          </van-popup>
        </div>
        <div v-if="formData.testdriveSeries.customSeriesName == 'A7L'"
          class="box-field jt"
        >
          <label class="box-label">倾向车型</label>
          <van-field
            :label-align="labelAlign"
            input-align="right"
            :label-width="labelWidth"
            readonly
            clickable
            placeholder="请选择倾向车型"
            @click="formDataShow.inclinedModelPicker = true"
            v-model="formData.inclinedModel"
            :rules="[{ required: true, message: '请选倾向车型！' }]"
          />
          <van-icon name="arrow" />
          <van-popup
            v-model="formDataShow.inclinedModelPicker"
            :lazy-render="false"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              :columns="displacement"
              value-key="code"
              @confirm="onConfirm({value:$event, label: 'inclinedModel'})"
              @cancel="onCancel"
            />
          </van-popup>
        </div>
        <div
          class="box-field"
          @click.stop="animation('fullName')"
          id="fullName"
        >
          <label class="box-label">姓名</label>
          <van-field
            type="textarea"
            cols="42"
            input-align="right"
            placeholder="请输入姓名"
            :rows="rows.fullName"
            autosize
            max-witdh
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="formData.fullName"
            ref="fullName"
            :maxlength="maxlength.fullName"
            :rules="[ { trigger: 'onBlur', required: true, message: `请输入姓名！`, }, ]"
          />
          <!-- @blur="handlerBlur('fullName')"
            @focus="handlerFocus('fullName')"
            @input="inputValue('fullName')" -->
        </div>
        <div
          class="box-field"
          @click.stop="animation('mobile')"
          id="mobile"
        >
          <label class="box-label">联系方式</label>
          <van-field
            type="textarea"
            cols="42"
            :rows="rows.mobile"
            input-align="right"
            placeholder="联系方式"
            autosize
            max-witdh
            readonly
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="formData.mobile"
            ref="mobile"
            :maxlength="11"
            on-key-up="value = value.replace(/[^\d]/g, '')"
            :rules="[ { trigger: 'onBlur', required: true, message: `请填写联系方式！`, },
                      { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号！' }]"
          />
          <!--  @blur="handlerBlur('mobile')"
            @focus="handlerFocus('mobile')"
            @input="inputValue('mobile')" -->
        </div>
        <div class="box-field jt">
          <label class="box-label">城市</label>
          <van-field
            :label-align="labelAlign"
            input-align="right"
            :label-width="labelWidth"
            clickable
            readonly
            placeholder="请选择城市"
            v-model="formData.carLicenseCityName"
            @click="doShowCarCity"
            :rules="[ { required: true, message: '请选择城市', }, ]"
          />
          <van-icon name="arrow" />
          <van-popup
            v-model="formDataShow.showCarCity"
            round
            position="bottom"
          >
            <div class="carCitypicker">
              <div>省份</div>
              <div>城市</div>
            </div>
            <van-picker
              ref="vanPicker"
              v-model="cascaderValue"
              title="选择城市"
              show-toolbar
              :default-index="cityDefultIndex"
              :columns="areaList"
              @cancel="onCancel"
              @confirm="cityConfirm"
            />
          </van-popup>
        </div>
        <div class="box-field jt">
          <label class="box-label">试驾方式</label>
          <van-field
            :label-align="labelAlign"
            input-align="right"
            :label-width="labelWidth"
            readonly
            clickable
            placeholder="请选择试驾方式"
            v-model="formData.testdriveMode.text"
            :rules="[{ required: true, message: '请选择试驾方式！' }]"
          />
          <!-- <van-icon name="arrow-down" /> -->
          <van-popup
            v-model="formDataShow.showPicker1"
            :lazy-render="false"
            round
            position="bottom"
          >
            <van-picker
              ref="testdriveMode"
              show-toolbar
              :columns="columns1"
              @confirm="onConfirm({value:$event,label:'testdriveMode'})"
              @cancel="onCancel"
            />
          </van-popup>
        </div>
        <div
          class="box-field jt"
          v-if="formData.testdriveMode.id === 2"
        >
          <label class="box-label">预约地点</label>
          <van-field
            :label-align="labelAlign"
            input-align="right"
            :label-width="labelWidth"
            readonly
            clickable
            placeholder="请选择预约地点"
            v-model="formData.testdrivePlace.text"
            @click="formDataShow.showPicker3 = true"
            :rules="[{ required: true, message: '请选预约地点！' }]"
          />
          <van-icon name="arrow-down" />
          <van-popup
            v-model="formDataShow.showPicker3"
            :lazy-render="false"
            round
            position="bottom"
          >
            <van-picker
              ref="testdrivePlace"
              show-toolbar
              :columns="columns3"
              @confirm="onConfirm({value:$event,label:'testdrivePlace'})"
              @cancel="onCancel"
              :default-index="defaultIndex.testdrivePlace"
            />
          </van-popup>
        </div>
        <div class="box-field jt">
          <label class="box-label">代理商</label>
          <van-field
            :label-align="labelAlign"
            input-align="right"
            :label-width="labelWidth"
            readonly
            clickable
            placeholder="请选择代理商"
            v-model="formData.agent.dealerName"
            @click="doShowPicker2"
            :rules="[{ required: true, message: '请选择代理商！' }]"
          />
          <van-icon name="arrow" />
          <van-popup
            v-model="formDataShow.showPicker2"
            :lazy-render="false"
            round
            position="bottom"
          >
            <van-picker
              ref="agent"
              show-toolbar
              :columns="columns2"
              value-key="dealerName"
              @confirm="onConfirm({value:$event,label:'agent'})"
              @cancel="onCancel"
              :default-index="defaultIndex.agent"
            />
          </van-popup>
        </div>
        <div class="box-field jt">
          <label class="box-label">试驾日期</label>
          <van-field
            :label-align="labelAlign"
            input-align="right"
            :label-width="labelWidth"
            clickable
            readonly
            placeholder="请选择试驾日期"
            v-model="formData.monthDay"
            @click="selectMonthDay"
            :rules="[ { required: true, message: '请选择试驾日期'} ]"
          />
          <van-icon name="arrow-down" />
          <van-popup
            v-model="formDataShow.monthDay"
            round
            position="bottom"
          >
            <van-datetime-picker
              ref="vanDatePicker"
              v-model="formData.monthDay"
              type="date"
              show-toolbar
              :min-date="minDate"
              :max-date="maxDate"
              :formatter="formatter"
              @cancel="onCancel"
              @confirm="onConfirm({value:$event,label:'monthDay'})"
            />
            <!-- <van-picker
              ref="vanDatePicker"
              show-toolbar
              :columns="columns4"
              @confirm="onConfirm({value:$event,label:'monthDay'})"
              @cancel="onCancel"
            /> -->
          </van-popup>
        </div>
        <div class="box-field jt">
          <label class="box-label">试驾时间</label>
          <van-field
            :label-align="labelAlign"
            input-align="right"
            :label-width="labelWidth"
            clickable
            readonly
            placeholder="请选择试驾时间"
            v-model="formData.hourMinute"
            @click="selectHourMinute"
            :rules="[ { required: true, message: '请选择试驾时间' } ]"
          />
          <van-icon name="arrow-down" />
          <van-popup
            v-model="formDataShow.hourMinute"
            round
            position="bottom"
          >
            <!-- <van-datetime-picker
              ref="vanTimePicker"
              v-model="formData.hourMinute"
              type="time"
              show-toolbar
              @cancel="onCancel"
              @confirm="onConfirm({value:$event,label:'hourMinute'})"
            /> -->
            <van-picker
              ref="vanTimePicker"
              show-toolbar
              :columns="columns5"
              @confirm="onConfirm({value:$event,label:'hourMinute'})"
              @cancel="onCancel"
            />
          </van-popup>
        </div>
        <div
          class="box-field"
          @click.stop="animation('remarks')"
          id="remarks"
          v-if="false"
        >
          <van-field
            type="textarea"
            class="_remarks"
            placeholder="备注"
            input-align="right"
            cols="42"
            :rows="rows.remarks"
            autosize
            max-witdh
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="formData.remarks"
            ref="remarks"
            @blur="handlerBlur('remarks')"
            @focus="handlerFocus('remarks')"
            @input="inputValue('remarks')"
          />
        </div>
        <div
          class="box-field"
          @click.stop="animation('inviteUserMobile')"
          id="inviteUserMobile"
          v-if="inviteCode"
        >
          <label class="box-label">推荐人</label>
          <van-field
            type="textarea"
            cols="42"
            input-align="right"
            placeholder="请输入推荐人"
            :rows="rows.inviteUserMobile"
            autosize
            readonly
            max-witdh
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="formData.inviteUserMobile"
            ref="inviteUserMobile"
            :maxlength="maxlength.inviteUserMobile"
            :rules="[ { trigger: 'onBlur', required: true, message: `请输入推荐人！`, }, ]"
          />
        </div>
      </van-form>
      <!-- <div
        class="wanshan"
        @click="wanshan"
      >
        完善试驾信息
        <van-icon
          name="arrow"
          color="#999"
        />
      </div> -->
      <div
        class="btnWarp"
        v-show="showBottomButton"
      >
        <div
          class="buttons"
          @click="submit"
        >
          提交
        </div>
        <div class="bt" />
      </div>
      <model
        :modalshow.sync="modalshow"
        @update:modalshow="submitTestDriverOrder"
        title="确定提交预约试驾？"
      />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import dayjs from 'dayjs'
import {
  Form,
  Field,
  Icon,
  Picker,
  Popup,
  DatetimePicker,
  Toast
} from 'vant'
import { mapState } from 'vuex'
import wx from 'weixin-js-sdk'
import { Base64 } from 'js-base64'
import { getCity } from '../../api/detail'
import {
  getUserInfo, getDealerList, getNearestDealerList, getInviteUser, getDealerByCode
} from '@/api/api'
import { getQueryParam, callNative, getLocationCityName } from '../../utils'
import {
  createTestDriver, createInviteTestDriver, getModelList, getListResource, judgeChuangshi, getCustomSeries, getOrgsForAppointmentList
} from '../../api/test-driver'
import api from '../../config/url'
import model from '@/components/model.vue'

Vue.use(Form).use(Field).use(Icon).use(Picker)
  .use(Popup)
  .use(Toast)
  .use(DatetimePicker)
const codeType = ['00', '200']
export default {
  name: 'TestdriverCreate',
  inject: ['reload'],
  components: { model },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      idx: '0',
      cityDefultIndex: 0,
      listResource: [],
      BaseOssHost: api.BaseOssHost,
      modalshow: false,
      minDate: new Date(),
      maxDate: new Date(2022, 10, 30),
      formDataShow: {
        inclinedModelPicker: false,
        showPicker: false,
        showCarCity: false,
        showPicker1: false,
        showPicker2: false,
        showPicker3: false,
        monthDay: false,
        hourMinute: false
      },
      cascaderValue: '',
      // 2.0T/3.0T
      displacement: [
        { code: '2.0T', description: '2.0T' },
        { code: '3.0T', description: '3.0T' }
      ],
      columns: [
        // { code: '498B', description: '奥迪A7L' }
      ],
      // columns1: [{ id: 1, text: '到店试驾' }, { id: 2, text: '上门试驾' }, { id: 3, text: '深度试驾' }],
      columns1: [
        {
          text: '到店',
          children: [
            { text: '试驾', id: 1 },
            { text: '试乘', id: 4 }
          ]
        }
      ],
      columns2: [{ dealerCode: '76631019', dealerName: 'Audi house of progress SH', dealerAdrress: '上海市' }],
      columns3: [{ id: '76631019', text: '展厅' }],
      columns4: ['2021-09-01', '2021-09-02'], // 日期
      columns5: ['10:00-11:00', '11:00-12:00', '12:00-13:00', '13:00-14:00', '14:00-15:00'], // 时间段
      meishijian: false, // 是否有可选时间
      formData: {
        inclinedModel: '2.0T',
        testdriveSeries: '', // 试驾车系 试驾车型
        testdriveMode: { text: '试乘试驾', id: 1 }, // 试驾方式 columns1
        testdrivePlace: { id: '76631019', text: '展厅' }, // 预约地点
        agent: '', // 代理商
        fullName: '',
        remarks: '',
        monthDay: '',
        monthDayValue: 0,
        hourMinute: '',
        mobile: '',
        carLicenseCityName: '', // 省市区
        inviteUserMobile: ''
      },
      defaultIndex: {
        testdriveSeries: 0,
        testdrivePlace: 0,
        testdriveMode: 0,
        columns4: 0,
        agent: 0
      },
      labelWidth: 120,
      labelAlign: 'left',
      areaList: [],
      animations: {
        fullName: false,
        remarks: false,
        mobile: false
      },
      rows: {
        fullName: '1',
        mobile: '1',
        remarks: '1'
      },
      maxlength: {
        fullName: '32',
        inviteUserMobile: '32',
        remarks: '400'
      },
      dealerList: [], // 经过各种筛选之后的
      dealerListAll: [], // 根据接口获取的
      isChuanshika: false, // 是否是传世卡用户
      agentfromcar: {}, // 从爱车页传来的经销商数据
      inviteCode: '',
      cityName: ''
    }
  },
  computed: {
    ...mapState({
      env: 'env',
      wxLocation: 'wxLocation',
      userLocation: 'userLocation'
    }),
    showBottomButton() {
      return this.$store.state.showBottomButton
    }
  },
  methods: {
// A7L、创始卡 那么只能创始卡，然后如果有代理商只能代理商，没有代理商就可以选择
// Q5E的话 如果有代理商只能代理商，没有代理商就可以选择
    async tryCarfn() {
       const dat = {
        // custMobile: this.formData.mobile,
        seriesCode: this.formData.testdriveSeries.seriesCode
      }
      await judgeChuangshi(dat).then((res) => {
        if (res.status === 200 && res.data.code === '200') {
          if (res.data.data) {
            // 有数据说明是传世卡用户，替换返回的经销商,并且城市不可选
            this.isChuanshika = true
            const theagent = this.dealerListAll.filter((e) => e.dealerCode === res.data.data.orgCode)
            this.formData.carLicenseCityName = theagent[0].cityName
            this.formData.agent = theagent[0]
          } else if (this.agentfromcar) { // 如果有从爱车页传来的经销商数据，替换，但可选
            this.formData.agent = this.agentfromcar
            this.formData.carLicenseCityName = this.agentfromcar.cityName
          }
        }
      })

      let dealerCode = getQueryParam('dealerCode')
      if (!dealerCode) {
        return
      }

      let seriesCode = this.formData.testdriveSeries.seriesCode 
      if (this.isChuanshika && seriesCode == 49) { // 是创始卡 A7L customSeriesCode: "49"
        return
      }

      const { data } = await getDealerByCode({dealerCode})
      console.log("代理商信息：", data.data);
      let addr = data.data.dealerAdrress 
      let dealerAdrress = data.data.provinceName + '/' + data.data.cityName
      let dealerName = data.data.dealerName
      // let dealerName = getQueryParam('dealerName')
      // let dealerAdrress = getQueryParam('dealerAdrress')
      // let addr = getQueryParam('dealerAdrress')
      this.formDataShow.showCarCity = false
      this.formData.carLicenseCityName = dealerAdrress
      this.formData.agent = { dealerCode: dealerCode, dealerName: dealerName, dealerAdrress: dealerAdrress, addr: addr}
      console.log("参数log: this.formData.agent", this.formData.agent );
    },
    selectMonthDay() {
      if (this.meishijian) {
        Toast({
          type: 'fail',
          message: '暂无可预约时间',
          icon: require('../../assets/img/error.png')
        })
      } else {
        this.formDataShow.monthDay = true
      }
    },
    selectHourMinute() {
      if (this.columns5.length > 0) {
        this.formDataShow.hourMinute = true
      } else {
        Toast({
          type: 'fail',
          message: '暂无可预约时间',
          icon: require('../../assets/img/error.png')
        })
      }
    },
    wanshan() {
      this.$router.push({
        path: this.fromType ? '/testdrive/upload-license?fromType=fromPurple' : '/testdrive/upload-license',
        query: {
          mobile: this.formData.mobile
        }
      })
    },
    doShowCarCity() {
      this.formDataShow.showCarCity = true
      // if (getQueryParam('dealerCode') ) {
      //   this.formDataShow.showCarCity = false
      //   return
      // }
      
      // if (!this.isChuanshika) { // 是传世卡用户则城市不可选
      //   this.formDataShow.showCarCity = true
      // }
    },
    doShowPicker2() {
      if (getQueryParam('dealerCode')) {
        this.formDataShow.showPicker2 = false
        return
      }

      if (!this.isChuanshika) { // 是传世卡用户则经销商不可选
        if (this.columns2.length > 0) {
          this.formDataShow.showPicker2 = true
        } else {
          Toast({
            type: 'fail',
            message: '暂无可预约代理商',
            icon: require('../../assets/img/error.png')
          })
        }
      }
    },
    inputValue(val) {
      this.formData[val] = this.limitstr(this.formData[val], 32)
    },
    limitstr(strval, strnum) {
      let re = ''
      const strleng = strval.length
      // 返回字符串的总字节数
      // eslint-disable-next-line no-control-regex
      const byteleng = strval.replace(/[^\x00-\xff]/g, '**').length
      if (byteleng <= strnum) return strval
      for (let i = 0, bytenum = 0; i < strleng; i++) {
        const byte = strval.charAt(i)
        // eslint-disable-next-line no-control-regex
        if (/[\x00-\xff]/.test(byte)) {
          bytenum++ // 单字节字符累加1
        } else {
          bytenum += 2 // 非单字节字符累加2
        }
        if (bytenum <= strnum) {
          re += byte
        } else {
          return re
        }
      }
    },

    // 表单提交
    submit() {
      // console.log('提交表单1')
      // console.log('提交表单1', this.formData)
      this.$refs.form.submit()
      // 埋点
      // const timer = new Date().getTime()
      // this.dataCollection( timer )
      const params = {
        test_drive_car: this.formData.testdriveSeries.seriesName,
        name: this.formData.fullName,
        contact_information: this.formData.mobile,
        city: this.formData.carLicenseCityName,
        test_drive_type: this.formData.testdriveMode.text,
        agent: this.formData.agent.dealerName,
        test_drive_date: this.formData.monthDay,
        test_drive_time: this.formData.hourMinute,
        appointment_type: '试乘试驾'
      }
      this.$sensors.track('testDriveInformation', params)
    },
    dataCollection(timer) {
      const params = {
        types_name: this.formData.fullName,
        $event_duration: timer
      }
      this.$sensors.track('booktestdrive', params)
    },
    onSubmit() {
      console.log('提交表单')
      this.modalshow = true
    },
    submitTestDriverOrder(isSubmit) {
      if (isSubmit) {
        this.$store.commit('showLoading')
        const hminutes = this.formData.hourMinute.split('-')
        const appoStartTime = `${dayjs(this.formData.monthDayValue).format('YYYY-MM-DD')} ${hminutes[0]}`
        const appoStartEnd = `${dayjs(this.formData.monthDayValue).format('YYYY-MM-DD')} ${hminutes[1]}`
        this.formData.agent
        let dealerCode = getQueryParam('dealerCode')
       
        // 由单独选地址改为从经销商门店中取地址
        let address = !dealerCode ? this.formData.agent.dealerAdrress : this.formData.agent.addr
        
        const data = {
          inclinedModel: this.formData.testdriveSeries.customSeriesName == 'A7L' ? this.formData.inclinedModel : '',
          // custIdCard: '761235123123',
          seriesCode: this.formData.testdriveSeries.seriesCode,
          // appoPhone: this.formData.mobile,
          appoName: this.formData.fullName,
          appoRemark: this.formData.remarks,
          appoType: '90030',
          testDriverType: this.formData.testdriveMode.id, // 试驾方式 试驾类型
          appoBeginTime: appoStartTime,
          appoEndTime: appoStartEnd,
          orgCode: this.formData.agent.dealerCode,
          // oneId: '123123123123',
          address: address, 
          activityCode: '0001',
          channel: 2,
          inviteCode: this.inviteCode
        }
        console.log(hminutes[1], hminutes[1] == "00:00");
        if (hminutes[1] == "00:00") {
          data.appoEndTime = fn(appoStartEnd)
        }
        function fn(e) {
          let n = +e?.split(' ')[0]?.split('-')[2] + 1
          let m = e.slice(8, 10)
          return e.replace(m, n)
        }
        console.log('提交表单1', this.formData, data)
        createInviteTestDriver(data).then((res) => {
          console.log(" 预约试驾 提交表单 res: ", res);
          if (res.status === 200 && res.data.code === '200') {
            this.$router.push({
              path: this.fromType ? '/testdrive/cancel-success?fromType=fromPurple' : '/testdrive/cancel-success',
              query: {
                type: 0
              }
            })
            const params = {
              test_drive_id: '',
              is_it_successful: true,
              failure_reason: '',
              failure_code: '',
              orderFrom: this.env === 'minip' ? 1001 : 1000
            }
            this.$sensors.track('testDriveResults', params)
          } else {
            res.data.message && Toast({
              type: 'fail',
              message: res.data.message || '请求错误',
              icon: require('../../assets/img/error.png')
            })
            const params = {
              test_drive_id: '',
              is_it_successful: false,
              failure_reason: res.data.message,
              failure_code: res.data.code
            }
            this.$sensors.track('testDriveResults', params)
          }
          this.$store.commit('hideLoading')
        })
      }
    },
    failed(err) {
      console.log(err)
    },
    cityConfirm(value, index) {
      console.log('value', value, index)
      this.formDataShow.showCarCity = false
      this.formData.carLicenseCityName = `${value[0]}/${value[1]}`

      const cityCode = this.areaList[index[0]].children[index[1]].code
      this.columns2 = this.dealerList.filter((e) => cityCode === e.cityCode)
      this.columns2.sort(this.compare('distance'))
      this.columns2 = this.resort(this.columns2)
      console.log('columns2', this.columns2)
      this.formData.agent = this.columns2[0] || []
    },
    compare(prop) {
      return function (obj1, obj2) {
        const val1 = obj1[prop]
        const val2 = obj2[prop]
        if (val1 === null || val2 === null) {
          return val1 === null ? 1 : -1
        } if (val1 < val2) {
          return -1
        } if (val1 > val2) {
          return 1
        }
        return 0
      }
    },

    resort(dealerList) {
      const idx = dealerList.findIndex((item) => item.distance !== 0)
      const truncate = dealerList.slice(0, idx)
      dealerList.splice(0, idx)
      return dealerList.concat(truncate)
    },
    onConfirm(res, index) {
      console.log('res.index', res, index)
      if (res.label === 'testdriveSeries') {
        this.formData.testdriveSeries = res.value
        this.formDataShow.showPicker = false
        this.tryCarfn()
      } else if (res.label === 'testdriveMode') {
        // 只有2种 1.试驾 4.试乘
        this.formData.testdriveMode = { id: res.value[1] === '试驾' ? 1 : 4, text: res.value[0] + res.value[1] }
        this.formDataShow.showPicker1 = false
      } else if (res.label === 'agent') {
        this.formData.agent = res.value
        this.formDataShow.showPicker2 = false
      } else if (res.label === 'testdrivePlace') {
        this.formData.testdrivePlace = res.value
        this.formDataShow.showPicker3 = false
      } else if (res.label === 'monthDay') {
        this.formData.monthDay = dayjs(res.value).format('YYYY年MM月DD日')
        this.formData.monthDayValue = res.value
        this.formDataShow.monthDay = false
        this.defaultIndex.columns4 = this.columns4.findIndex((e) => e === dayjs(res.value).format('YYYY-MM-DD'))
        this.columns5 = this.trasfromTimeInfos(this.listResource[this.defaultIndex.columns4].appoTimeInfos) || []
      } else if (res.label === 'hourMinute') {
        this.formData.hourMinute = res.value.text
        this.formDataShow.hourMinute = false
      }

      if(res.label === 'inclinedModel') {
        this.formData.inclinedModel = res.value.code
        this.formDataShow.inclinedModelPicker = false
      }
    },
    onCancel() {
      this.formDataShow.showPicker = false
      this.formDataShow.showCarCity = false
      this.formDataShow.showPicker1 = false
      this.formDataShow.showPicker2 = false
      this.formDataShow.showPicker3 = false
      this.formDataShow.monthDay = false
      this.formDataShow.hourMinute = false
    },
    trasfromTimeInfos(info) {
      // appoStatus 可约状态  1.可约  2.不可约
      const tmp = []
      info.forEach((e) => {
        const str1 = e.beginTime.substring(11, 16)
        const str2 = e.endTime.substring(11, 16)
        const boo1 = e.appoStatus === 2
        tmp.push({ text: `${str1}-${str2}`, disabled: boo1 })
      })
      return tmp
    },
    async getCustomSeries() {
      await getCustomSeries().then(async (res) => {
        res.data.data.forEach((series) =>{
          series.customSeriesName = series.customSeriesName.replace("Audi ", "")
        })
        this.columns = res.data.data
        this.formData.testdriveSeries = this.columns[this.idx] || ''
        // this.getData()
        // this.getListResource()
        // let dealerCode = getQueryParam('dealerCode')
        // if (dealerCode) {
        //   return
        // }
        this.judgeChuangshi()
      })
    },
    async getListResource() {
      const data = {
        orgCode: this.formData.agent.dealerCode || this.columns2[0].dealerCode,
        seriesCode: this.formData.testdriveSeries.seriesCode || this.columns[0].seriesCode,
        appoType: '90030',
        testType: this.formData.testdriveMode.id || 1
      }
      this.$store.commit('showLoading')
      await getListResource(data).then((res) => {
        this.$store.commit('hideLoading')
        if (res.status === 200 && res.data.code === '200') {
          this.listResource = res.data.data
          this.formData.monthDay = ''
          if (this.listResource && this.listResource.length > 0) {
            this.meishijian = false
            const tmp = []
            this.listResource.forEach((e) => {
              tmp.push(e.appoDate)
            })
            this.columns4 = tmp
            const l = this.columns4.length
            this.minDate = new Date(this.columns4[0])
            this.maxDate = new Date(this.columns4[l - 1])
            this.columns5 = this.trasfromTimeInfos(this.listResource[this.defaultIndex.columns4].appoTimeInfos) || []
          } else {
            this.meishijian = true
            this.columns5 = []
          }
        }
      })
    },
    async getModelList() {
      await getModelList().then((res) => {
        if (res.status === 200 && res.data.code === '00') {
          this.columns = res.data.data
        }
      })
    },
    // 需要调用接口判断是否是传世卡用户，
    // 1).是传世卡用户，界面上将不能选择代理商，当前接口会返回默认的：代理商code，名称
    // 2).不是，接口会返回空。此时界面中可以调用代理商列表接口，让用户自己选择代理商
    async judgeChuangshi() {
      const data = {
        // custMobile: this.formData.mobile,
        seriesCode: this.formData.testdriveSeries.seriesCode
      }
      const res = await judgeChuangshi(data)
      console.log('judgeChuangshi', res);
      if (res.status === 200 && res.data.code === '200') {
        if (res.data.data) {
          // 有数据说明是传世卡用户，替换返回的经销商,并且城市不可选
          this.isChuanshika = true
          const theagent = this.dealerListAll.filter((e) => e.dealerCode === res.data.data.orgCode)
          this.formData.carLicenseCityName = theagent[0].cityName
          this.formData.agent = theagent[0]
        } else if (this.agentfromcar) { // 如果有从爱车页传来的经销商数据，替换，但可选
          this.formData.agent = this.agentfromcar
          this.formData.carLicenseCityName = this.agentfromcar.cityName
        }
      }

      let dealerCode = getQueryParam('dealerCode')
      if (!dealerCode) {
        return
      }

      let seriesCode = this.formData.testdriveSeries.seriesCode 
      if (this.isChuanshika && seriesCode == 49) { // 是创始卡 A7L customSeriesCode: "49"
        return
      }

      const res1 = await getDealerByCode({dealerCode})
      let dataBox = res1.data
      console.log("代理商信息：", dataBox.data);
      let addr = dataBox.data.dealerAdrress 
      let dealerAdrress = dataBox.data.provinceName + '/' + dataBox.data.cityName
      let dealerName = dataBox.data.dealerName
      this.formDataShow.showCarCity = false
      this.formData.carLicenseCityName = dealerAdrress
      this.formData.agent = { dealerCode: dealerCode, dealerName: dealerName, dealerAdrress: dealerAdrress, addr: addr}
      
    },
    async getDealerList() {
      const params = {}
      console.log("this.userLocation::", this.userLocation);
      if (this.userLocation !== '') {
        const location = this.userLocation.split(',')
        params.latitude = location[0]
        params.longitude = location[1]
      }else {
        if (this.env === 'minip') {
          let wxLocation = "31.01448585,121.42796184"
          this.cityName = '上海市'
          let location = wxLocation.split(',')
          params.latitude = location[0]
          params.longitude = location[1]
        } 
        console.log("--location---params---", location, params);
      }

      console.log("getNearestDealerList params", params);

      await getNearestDealerList(params)
        .then((res) => {
          console.log("getDealerList res", res);
          // 过滤掉上汽奥迪总部
          let data = res.data.data
          data = data.filter((e) => e.dealerCode !== '76600019')
          this.dealerListAll = data
          // this.getData()
          this.getUserInfo()
        })
    },
    async getCity(data) {
      let dealerCode = getQueryParam('dealerCode')
      if (dealerCode) {
        return
      }

      const that = this
      const setCity = (data) => {
        let cc = that.areaList.find((item) => {
          const a = item.children.find((it) => (it.text === data))
          return a !== undefined
        })
        if (cc && !this.isChuanshika) {
          const ies = cc.children.findIndex((item) => item.text === data)
          that.formData.carLicenseCityName = `${cc.text}/${cc.children[ies].text}`
          that.formData.carLicenseCityCode = cc.children[ies].code
          const a = that.areaList.filter((item) => item.children.find((it) => it.text === data))[
            0]

          const i = that.areaList.findIndex((item) => (
            item.children.findIndex((it) => it.text === data) !== -1
          ))
          that.cityDefultIndex = i
          const index = a.children.findIndex((item) => item.text === data)
          that.areaList[i].defaultIndex = index
        } else if (!cc && !this.isChuanshika) {
          cc = that.areaList[0]
          that.formData.carLicenseCityName = `${cc.text}/${cc.children[0].text}`
          that.formData.carLicenseCityCode = cc.children[0].code
          that.cityDefultIndex = 0
          that.areaList[0].defaultIndex = 0
        }

        that.columns2 = that.dealerList.filter((e) => that.formData.carLicenseCityCode === e.cityCode)
        if (that.columns2.length > 0 && !this.isChuanshika) {
          that.formData.agent = that.columns2[0]
        }
      }
      if (data) {
        setCity(data)
      } else {
        if (this.cityName) {
          setCity(this.cityName)
        }
      }
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`
      }
      if (type === 'month') {
        return `${val}月`
      }
      if (type === 'day') {
        return `${val}日`
      }
      return val
    },
    // lebel平移
    animation(ref) {
      this.animations[ref] = true
      this.$refs[ref].focus()
      if (this.blur) {
        this.$refs[ref].blur()
      }
    },
    handlerBlur(prop) {
      if (!this.formData[prop]) {
        this.animations[prop] = false
      } else {
        this.animations[prop] = true
      }
    },
    handlerFocus(prop) {
      setTimeout(() => {
        const pannel = document.getElementById(prop)
        // 让当前的元素滚动到浏览器窗口的可视区域内
        pannel.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
        // 此方法是标准的scrollIntoView()方法的专有变体
        // pannel.scrollIntoViewIfNeeded();
      }, 300)

      if (!this.animations[prop]) {
        this.animations[prop] = true
      }
    },

    async getData() {
      const { data } = await getCity({ code: '' })
      if (codeType.includes(data.code)) {
        const province = data.data[0]?.children
        const city = []

        const arr = province.map(async (item, index) => {
          const data2 = await getCity({ code: item.code })
          return data2.data.data[0].children
        })
        Promise.all(arr).then((res) => {
          const list = province.map((item, index) => {
            res[index].forEach((it, i) => {
              delete res[index][i].children
            })
            province[index].children = res[index]
            return province[index]
          })
          // list是全部的省市列表，再用dealerList中的城市筛选一遍
          if (list.length > 0 && this.dealerList.length > 0) {
            console.log('aaaaaa',this.areaList)
            this.areaList = list.filter((a) => {
              const children = a.children
              const tmp = []
              let flag = false
              const codetmp = []
              children.forEach((b) => {
                this.dealerList.forEach((c) => {
                  if (c.cityCode === b.code && codetmp.indexOf(b.code) < 0) {
                    tmp.push(b)
                    flag = true
                    if (codetmp.indexOf(b.code) < 0) { // 去重
                      codetmp.push(b.code)
                    }
                  }
                })
              })
              a.children = tmp
              return flag
            })
          }
          console.log('dddddd',this.areaList)
          // this.areaList = list
          if (sessionStorage.getItem('isFormDetail') === '1') {
            const newFormData = JSON.parse(localStorage.getItem('formDetail'))
            const str = newFormData.carLicenseCityName
            const index = str.indexOf('/')
            if (index !== -1) {
              this.getCity(str.slice(index + 1))
            } else {
              this.getCity(str)
            }
          } else {
            this.getCity()
          }
        })
      }
    },
    async getUserInfo() {
      const { data } = await getUserInfo()
      console.log("getUserInfo data", data);
      if (codeType.includes(data.code)) {
        this.formData.mobile = data.data.userInfo.mobile
        this.formData.fullName = data.data.userInfo.displayName
        localStorage.setItem('userInfo', JSON.stringify(data.data.userInfo))
        this.getCustomSeries()
      }
    },
    filterInput(val) {
      return val.replace(/[^-_a-zA-Z0-9\u4e00-\u9fa5]/, '')
    },

    async initUserLocation() {
      console.log("this.env", this.env);
      if (this.env === 'minip') {
        // this.getData()
        let p = this.$route.query.wxLocation
        let wxLocation = ''
        console.log(!(p && p.split(',')[0]));
        if (!(p && p.split(',')[0])) {
          wxLocation = "31.01448585,121.42796184"
          this.cityName = '上海市'
          return
        } else {
          wxLocation = this.wxLocation
        }

        console.log("wxLocation", wxLocation);

        const cityName = await getLocationCityName(wxLocation.split(','))
        console.log("cityName", cityName);
        if (cityName) {
          this.cityName = cityName
        }
      } else {
        const res = await callNative('getLocationCity', {})
        // const res = {city:'上海市',location:'31.01448585,121.42796184'}
        if (res.location) {
          let arrayLocation =res.location.split(',')
          city = await getLocationCityName([arrayLocation[1]*1,arrayLocation[0]*1])
          this.cityName = city
          // this.$store.commit('setUserLocation', res.location)
        }
      }
      console.log('获取定位信息, ', this.cityName)
    },
    getInviteUser(inviteCode) {
      getInviteUser({ inviteCode: inviteCode }).then((res) => {
        this.formData.inviteUserMobile = res.data.data.mobile
      })
    }
  },
  async mounted() {
    console.log("query:" ,this.$route.query );
    const idx = getQueryParam('idx')
    console.log('idxidx:', idx)
    if (idx.length > 0) {
      this.idx = idx
    }
    if (this.$route.query && this.$route.query.inviteCode) {
      this.inviteCode = this.$route.query.inviteCode
    }
    // 获取从爱车页跳转过来native携带的数据
    // const res = await callNative('getParmas', {})
    // if (res && res.params) {
    //   const params = JSON.parse(Base64.decode(res.params))
    //   console.log('从爱车来的params', params)
    //   this.agentfromcar = params.data
    // }

    await this.initUserLocation()

    this.getDealerList()
    // this.getData()
    // this.getUserInfo()
    // this.getCustomSeries()
    // this.getModelList()
  },
  created() {
    // this.$store.state.title = '上门试驾'
  },
  watch: {
    inviteCode(val) {
      if (val) {
        this.getInviteUser(val)
      }
    },
    async 'formData.testdriveSeries'(val) {
      const index = this.columns.findIndex((item) => item.content === val)
      this.defaultIndex.testdriveSeries = index
      const { data } = await getOrgsForAppointmentList({ appoDate: '', seriesCode: this.formData.testdriveSeries.seriesCode })
      console.log(data);
      if (data.code === '200') {
        const orgs = data.data
        // 根据返回的orgs筛选一遍dealerList
        const tmp = []
        for (let i = 0, l = this.dealerListAll.length; i < l; i++) {
          if (orgs.includes(this.dealerListAll[i].dealerCode)) {
            tmp.push(this.dealerListAll[i])
          }
        }
        this.dealerList = tmp
        this.getData()
      }
      if (this.formData.agent && this.formData.testdriveSeries) {
        this.getListResource()
      }
    },
    'formData.testdriveMode'(val) {
      const index = this.columns1.findIndex((item) => item === val)
      this.defaultIndex.testdriveMode = index
    },
    'formData.testdrivePlace'(val) {
      const index = this.columns3.findIndex((item) => item === val)
      this.defaultIndex.testdrivePlace = index
    },
    'formData.agent'(val) {
      const index = this.columns2.findIndex((item) => item === val)
      this.defaultIndex.agent = index
      if (this.formData.agent && this.formData.testdriveSeries) {
        this.getListResource()
      }
    },
    'formData.fullName'(val) {
      // this.formData.fullName = this.filterInput(val)
      this.formData.fullName = val
    },
    'formData.remarks'(val) {
      this.formData.remarks = this.filterInput(val)
    },
    'formData.mobile'(val) {
      this.formData.mobile = this.filterInput(val)
    },
    'formData.carLicenseCityName'(val) {
      // this.formData.name = this.filterInput(val)
    },
    'formData.monthDay'(val) {
      if (val instanceof Date) {
        this.formData.monthDay = dayjs(val).format('YYYY年MM月DD日')
      }
    }
  }
}
</script>

<style lang="less" scoped>
  @import url("../../assets/style/cell.less");
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/dialog.less");
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/animation.less");
  @import url("../../assets/style/nor.less");

  div,input,textarea,i{
    box-sizing: border-box;
  }

  .box_head {
    width: 100%;

    img {
      width: 100%;
      height: 230px;
      object-fit: cover;
    }
  }

  #detail {
    padding-bottom: 20px !important;

    .box {
      margin: 16px 15px 80px 17px;
      padding: 0;
    }

    padding-bottom: 40px;

    .toolimg {
      width: 100%;
    }

    .buyMess {
      /deep/.van-form {
        & > .box-field {
          display: flex;
          flex-direction: column;
          position: relative;
          height: 56px;

          .box-label {
            width: 25%;
            font-size: 16px;
            color: #000;
            position: absolute;
            line-height: 20px;
            left: 0;
            top: 0px;
            z-index: 999;
          }

          .van-cell {
            position: relative;
            height: 100%;
            &:active{
              background-color: #fff;
            }

            .van-cell__value {
              .van-field__body {
                // min-height: calc(100% - 25px);
                border-bottom: 1px solid #e5e5e5;
                font-size: 16px;
                overflow: visible;
                flex-direction: column;
                // justify-content: flex-end;
                align-items: flex-start;
                position: relative;
                top: 6px;

                &.border-none {
                  border: none;
                }

                input {
                  min-height: 20px !important;
                  line-height: 20px;
                  padding-top: 1px;
                  width: 100%;
                  position: relative;
                  top: -8px;
                }

                input::-webkit-input-placeholder {
                  font-size: 16px;
                }

                textarea {
                  line-height: 20px;
                  min-height: 30px;
                  width: 100%;
                  position: relative;
                  top: -6px;
                }

                textarea::-webkit-input-placeholder {
                  font-size: 16px;
                }
              }
            }
          }

          ._remarks {
            .van-cell__value {
              .van-field__body {
                border-bottom: none !important;
              }
            }
          }

          .van-field--error {
            .van-field__body {
              border-bottom: 1px solid #9e1f32 !important;
            }

            .van-field__error-message {
              color: #9e1f32;
              position: relative;
              top: 6px;
            }
          }
        }

        .jt {
          position: relative;

          i {
            position: absolute;
            right: 0;
            top: 2px;
            color: #999;
          }

          input {
            font-size: 16px;
            padding-right: 20px;
          }
        }
      }
      .wanshan{
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        height: 56px;
        top: -25px;
        border-bottom: solid 1px #000;
      }
    }
  }

  /deep/.van-field__error-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 16px;
    justify-content: flex-end;

    &::before {
      content: "";
      display: inline-block;
      width: 14px;
      height: 14px;
      background: url("../../assets/img/error.png") no-repeat 0 0px;
      background-size: 14px 14px;
      margin-right: 4px;
    }
  }

  /deep/.van-dialog {
    overflow-y: auto;
    max-height: 80%;
    padding: 15px 16px 16px;
    top: 52% !important;
    z-index: 33336;

    h3 {
      margin: 0;
    }

    .item {
      color: #000;
      font-size: 14px;
      text-align: left;
      margin-bottom: 24px;

      .title {
        line-height: 24px;
      }

      .itemCotent {
        display: flex;
        line-height: 17px;

        div {
          margin-top: 8px;
        }
      }
    }
  }

  /deep/.van-popup {
    border-radius: 0;
    // height: 400px;
    font-size: 14px;

    .carCitypicker {
      position: relative;
      z-index: 2333;
      transform: translateY(80px);
      text-align: center;
      width: 100%;
      display: flex;
      background-color: #fff;
      top: -20px;
      div {
        flex: 1;
      }
    }

    .van-picker__columns {
      // top: 80px;

      .van-hairline-unset--top-bottom {
        border-bottom: 1px solid #eee;
        border-top: 1px solid #eee;
      }

      .van-picker-column {
        font-size: 14px;
      }
    }
  }

  /deep/ .van-field__control {
    &:disabled {
      color: #000;
      -webkit-text-fill-color: #000;
    }
  }

  /deep/ .van-field__control::placeholder {
    color: #999;
    -webkit-text-fill-color: #999;
    font-size: 16px;
  }
</style>
