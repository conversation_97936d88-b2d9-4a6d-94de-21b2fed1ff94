<template>
  <div>
    <div class="img-wrapper">
      <img
        src="../../assets/img/contract-success.png"
        alt=""
      >
    </div>
    <div class="c-font18 text-wrapper">
      您的{{ isXianJian ?'先见':'优享' }}权益已锁定！现已开启预售，即
      刻支付定金可享受优先生产及交车服务！
    </div>

    <div class="btn-wrapper">
      <AudiButton
        @click="toMyOrder"
        text="查看我的订单"
        font-size="16px"
        color="black"
        height="56px"
      />
    </div>
  </div>
</template>

<script>
/** *
 *
 * 非先行版的支付成功页面
 * 这个页面只区分先见和量产版本的区别
 */
import { mapState } from 'vuex'
import { XIAN_JIAN_VERSION } from '@/config/constant'
import AudiButton from '@/components/audi-button'

export default {
  components: { AudiButton },
  computed: {
    ...mapState({
      currentModelLineCode: (state) => state.carDetail.configDetail?.carModel?.modelLineCode,
      orderId: (state) => state.orderId
    }),
    isXianJian() {
      return this.currentModelLineCode === XIAN_JIAN_VERSION
    }
  },

  methods: {
    toMyOrder() {
      this.$router.push({
        path: '/order/money-detail',
        query: {
          orderId: this.orderId
        }
      })
    }
  }

}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";


.img-wrapper {
  width: 72px;
  margin: 30vw auto 0 auto;
}
.text-wrapper{
  padding: 0 16px;
  margin-top: 25px;
}
.btn-wrapper {
  padding: 0 16px;
  position: fixed;
  bottom: 58px;
  width: 100%;
  box-sizing: border-box;
}

</style>
