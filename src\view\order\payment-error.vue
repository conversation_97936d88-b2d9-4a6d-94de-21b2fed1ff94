<template>
  <div id="">
    <div
      id="paymentError"
      class="paymentError"
      v-if="carSeries.seriesCode === '49'"
    >
      <div
        class="warp"
        :style="{
          marginTop: cipCampaign ? '16px' : '66px',
          marginBottom: cipCampaign ? 0 : '60px',
        }"
      >
        <p class="p3">
          预订成功
        </p>
        <div class="circle">
          <van-icon
            name="success"
            size="20"
          />
        </div>
        <div v-show="!isXianXing && !isXianJian">
          <p class="p2">
            您的优享权益已锁定！
          </p>
          <div>
            <p class="p1">
              现已开启预售，即刻支付定金
            </p>
            <p class="p1">
              可享受优先生产及交车服务！
            </p>
          </div>
        </div>
        <div v-show="isXianXing">
          <p class="p2">
            您的先行权益已锁定！
          </p>
          <div>
            <p class="p1">
              前方等待用户还有{{ total }}位
            </p>
            <p class="p1">
              现已开启预售，抢先完成定金支付
            </p>
            <p class="p1">
              仍有机会获取专属限量号
            </p>
          </div>
        </div>
        <div v-show="isXianJian">
          <p class="p2">
            您的先见权益已锁定！
          </p>
          <div>
            <p class="p1">
              现已开启预售，即刻支付定金
            </p>
            <p class="p1">
              可享受优先生产及交车服务！
            </p>
          </div>
        </div>
        <div
          class="body"
          v-if="cipCampaign"
        >
          <img
            :src="cipCampaign"
            alt=""
          >
          <p class="p4">
            微信扫一扫添加您的专属顾问
          </p>
        </div>
      </div>
      <div class="carBox">
        <img
          class="car"
          src="../../assets/img/car.png"
          alt=""
        >
      </div>
      <div class="box">
        <div class="btnWarp">
          <div
            class="buttons"
            @click="
              $router.push({
                path: '/order/money-detail',
                query: {
                  orderId: $route.query.orderId || '',
                },
              })
            "
          >
            查看购车订单
          </div>
        </div>
      </div>
    </div>

    <div v-else>
      <div class="_q5_content">
        <img
          class="_icon-success"
          src="../../assets/img/contract-success.png"
        >
        <div class="_title">
          恭喜您预约成功
        </div>
        <div class="_title">
          您的客户权益已锁定
        </div>
        <img
          v-if="cipCampaign"
          class="_qrCode"
          :src="cipCampaign"
        >
        <div
          class="_title2"
          v-if="cipCampaign"
        >
          企业微信激活码
        </div>
      </div>

      <div class="box">
        <div class="btnWarp">
          <div
            class="buttons"
            @click="
              $router.push({
                path: '/order/money-detail',
                query: {
                  orderId: $route.query.orderId || '',
                },
              })
            "
          >
            查看购车订单
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { XIAN_XING_VERSION, XIAN_JIAN_VERSION } from '@/config/constant'
import {
  getLimitedNumbersLine
} from '../../api/payment-error'
import { getCipCampaign } from '../../api/payment-success'
import api from '../../config/url'

const codeType = ['00', '200']

export default {
  data() {
    return {
      BaseOssHost: api.BaseOssHost,
      total: '',
      cipCampaign: '',
      backgroundimage: '',
      imgUrl: ''
    }
  },
  computed: {
    ...mapState({
      currentModelLineId: (state) => state.carDetail.configDetail?.carModel?.modellineId,
      carSeries: (state) => state.carSeries,
      currentModelLineCode: (state) => state.carDetail.configDetail?.carModel?.modelLineCode
    }),
    isXianXing() {
      return this.currentModelLineCode === XIAN_XING_VERSION
    },
    isXianJian() {
      return this.currentModelLineCode === XIAN_JIAN_VERSION
    }
  },
  methods: {
    async getLimitedNumbersLine() {
      const { data } = await getLimitedNumbersLine()
      if (codeType.includes(data.code)) {
        this.total = data.data
      }
    },
    async handleGetCipCampaign() {
      const { $route: { query: { dealerCode, areaCode } } } = this
      const param = {
        dealerCode: dealerCode,
        code: areaCode
      }
      const { data: { code, data } } = await getCipCampaign(param)
      if (codeType.includes(code)) {
        const { qrcode, bind } = data || {} // bind => '0': 未绑定， '1'，已绑定
        if (bind === '0' && qrcode) {
          this.cipCampaign = qrcode
        }
      }
    }

  },
  mounted() {
    this.handleGetCipCampaign()
    this.getLimitedNumbersLine()
    this.$emit('set-bottom', true)
  },
  destroyed() {
    this.$emit('set-bottom', false)
  }
}
</script>

<style lang="less" scoped>
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/scroll.less");
  @import url('../../assets/style/background.less');

  .paymentError {
    color: #fff;
    overflow-y: auto;
    text-align: center;
    background-image: url("../../assets/img/bgImg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-bottom: 60px;

    .headimg {
      height: 238px;
    }

    .warp {
      margin: 16px 16px 0;
      opacity: 0.32;
      background: #575757;
      padding-bottom: 24px;
    }

    p {
      margin-block-start: 0;
      margin-block-end: 0;
    }

    .p1 {
      line-height: 24px;
      letter-spacing: 3.5px;
      font-size: 14px;
    }

    .p2 {
      margin: 18px 0 16px;
      letter-spacing: 3.5px;
    }

    .p3 {
      font-size: 28px;
      font-family: "Audi-WideBold";
      padding: 22px 0 17px;
      letter-spacing: 7px;
    }

    .circle {
      position: relative;
      width: 62px;
      height: 62px;
      border: 1px solid #fff;
      border-radius: 50%;
      margin: 0 auto;

      /deep/.van-icon-success {
        width: 55px;
        height: 55px;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          width: 20px;
          height: 18px;
          bottom: 0;
          margin: auto;
        }
      }
    }

    .body {
      margin-top: 24px;

      img {
        width: 80px;
        height: 80px;
        margin: 0 auto 16px;
      }

      .p4 {
        font-size: 10px;
        letter-spacing: 1px;
      }
    }

    .box {
      padding: 0 15px 0 17px;

      .btnWarp {
        position: fixed;
        z-index: 2;
        height: 56px;
        width: 100%;
        bottom: 50;
        background: transparent;

        .buttons {
          width: calc(100% - 32px);
          position: absolute;
          top: 0;
          bottom: 0;
          margin: 0 auto;
          height: 56px;
          z-index: 20;
          background: #000;
          font-family: "Audi-Normal";
          color: #fff;
          line-height: 56px;
          text-align: center;
          font-size: 18px;
        }

        .bt {
          content: "";
          display: block;
          background: #000;
          opacity: 0.2;
          width: 134px;
          height: 5px;
          position: absolute;
          bottom: -50px;
          left: -32px;
          right: 0;
          margin: 0 auto;
          border-radius: 100px;
        }
      }
    }

    .bt {
      bottom: 10px !important;
    }

    .btnWarp {
      background-color: transparent !important;
    }
  }
  ._q5_content{
    display: flex;
    flex-flow: column;
    align-items: center;
    padding-top: 120px;

    ._icon-success{
      width: 80px;
      height: 80px;
      margin-bottom: 10px;
    }
    ._title{
      font-size: 17px;
      line-height: 30px;
      font-family: "Audi-Normal";
    }
    ._qrCode{
      width: 100px;
      height: 100px;
      margin-top: 80px;
      margin-bottom: 10px;
    }
    ._title2{
      font-size: 15px;
      line-height: 30px;
      font-family: "Audi-Normal";
      font-weight: 500;
    }
  }

</style>
