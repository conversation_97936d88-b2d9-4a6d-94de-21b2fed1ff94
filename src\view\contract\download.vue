<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-01-11 18:09:13
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-01-13 16:16:02
 * @FilePath     : \src\view\contract\download.vue
 * @Descripttion :
-->
<template>
  <div
    :class="[
      'download-page',
      'page-wrapper',
      headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
    ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <div
        class="download-wrapper"
        data-flex="dir:top"
      >
        <div
          class="download-box"
          data-flex="dir:top main:center"
          data-block
        >
          <div class="svg-box">
            <svg
              t="1673487811354"
              class="icon"
              style="width: 90px;height: 90px;vertical-align: middle;fill: currentColor;overflow: hidden;"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="2746"
            ><path
              d="M878 306.3V941c0 15.7-12.7 28.4-28.4 28.4H192c-12 0-21.6-9.7-21.6-21.6V89.6c0-8.8 7.1-15.9 15.9-15.9h443.1c0.9 0 1.4 1.1 0.8 1.8-0.4 0.4-0.4 1.1 0.1 1.5l247.5 228.6c0 0.2 0.2 0.5 0.2 0.7z"
              fill="#F4F4F4"
              p-id="2747"
            /><path
              d="M629.5 291V73.7L878 305.9H644.4c-8.2 0-14.9-6.7-14.9-14.9zM406.1 365H187.9c-58.4 0-105.7-47.3-105.7-105.7 0-58.4 47.3-105.7 105.7-105.7h218.2c58.4 0 105.7 47.3 105.7 105.7 0.1 58.4-47.3 105.7-105.7 105.7z"
              fill="#E52020"
              p-id="2748"
              data-spm-anchor-id="a313x.7781069.0.i0"
            /><path
              d="M458.8 772.9c-20.8 0-37.5-6.2-49.7-18.4-19.1-19.1-19.2-46.1-19.2-48.7 0.3-22.4 7.2-39.8 20.7-51.9 19.3-17.3 45.1-16.5 50.1-16.2h96c21.8 0 38.6-6.2 49.9-18.4 10.5-11.4 14.8-26.4 16.6-37 1.9-11.6 1.3-20.5 1.3-20.6v-1.2c0-21.3-6.6-37.3-20.2-48.8-19-16.2-48.4-20.7-69.8-21.7-12.6-0.6-23.8 0-31.7 0.7v112.7H475V466.5l11.7-1.9c3.6-0.6 88.7-14 135.6 25.9 19.8 16.8 29.9 40.2 30 69.5 0.4 6 2 48.6-25.1 78.1-16.7 18.2-40.4 27.4-70.3 27.4h-97.3l-0.5-0.1c-1-0.1-18.4-1.1-29.9 9.3-7.3 6.7-11.1 17.2-11.3 31.4v0.1c0 1 0.2 17.9 11.2 28.7 7.1 7 17.8 10.4 31.7 10.1h10l-0.5-41.7 27.8-0.3 0.9 69.8h-37.8c-0.9 0.1-1.7 0.1-2.4 0.1z"
              fill="#E82B29"
              p-id="2749"
            /><path
              d="M235.9 222.3c-5.4-4.7-13-7-22.6-7h-26.1v88h14.9v-31.4h8.9c9.3 0.3 17.3-2.3 23.5-7.7 6.3-5.5 9.6-12.7 9.6-21.5 0.1-8.8-2.7-15.6-8.2-20.4z m-33.7 6.5h9.2c11.9 0 17.4 4.5 17.4 14.2 0 5-1.5 8.7-4.5 11.3-3.1 2.6-7.8 4-13.9 4h-8.1v-29.5zM324.3 226.9c-8.9-7.7-20.1-11.6-33.4-11.6h-26.2v88H290c13.9 0 25.4-4.1 34.3-12.3 8.9-8.2 13.4-19.2 13.4-32.7 0.1-13.1-4.4-23.6-13.4-31.4z m-44.6 2h10.8c9.6 0 17.4 2.5 23.2 7.4 5.7 4.9 8.6 12.3 8.6 22.1 0 9.9-2.8 17.6-8.4 23-5.6 5.4-13.6 8.2-23.8 8.2h-10.3v-60.7zM406.8 228.9v-13.6h-47.6v87.9h14.9v-36.6h30.3V253h-30.3v-24.1z"
              fill="#FFFFFF"
              p-id="2750"
            /></svg>
          </div>
        </div>

        <div
          class="btn-box"
          data-flex="cross:bottom main:center"
        >
          <button @click="handleDownloadContractFile">
            下载合同
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderCustom from '@/components/header-custom.vue'
import { downloadContractFile } from '@/api/api'
import { downloadFile } from '@/utils'


export default {
  name: 'ExceptionPage',
  components: {
    'header-custom': HeaderCustom
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      contractId: ''
    }
  },
  created() {
    this.handleProcessData()
  },
  methods: {
    handleProcessData() {
      const { $route: { query: { contractId } } } = this || ''
      if (contractId) {
        this.contractId = contractId
      }
    },
    handleLeftBack() { },
    handleOnDownloadProgress(onDownloadProgress) {
      // console.log('%c [ onDownloadProgress ]-114', 'font-size:14px; background:#cf222e; color:#fff;', onDownloadProgress)
    },
    async handleDownloadContractFile() {
      const {
        contractId, handleOnDownloadProgress, hashCode
      } = this
      const { data } = await downloadContractFile({ contractId }, handleOnDownloadProgress)
      if (data) {
        downloadFile(data, '购车合同')
      }
    }
  }
}
</script>
<style lang="less" scoped>
.download-page,
.main-wrapper, .download-wrapper {
  height: 100%;
  box-sizing: border-box;
}
</style>
