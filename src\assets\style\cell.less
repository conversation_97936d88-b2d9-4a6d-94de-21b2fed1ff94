/deep/.van-cell {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 0;
    height: 48px;
    &::after{
        border: 0;
    }
    & .van-cell {
        border: 0;
    }
}

/deep/.van-address-edit__default {
    flex-direction: row;
    margin-top: 0;
}

/deep/.van-field__label {
    font-size: 12px;
    line-height: 12px;
    margin-top: 16px;
}

/deep/.van-field__value {
    input {
        font-size: 14px;
        line-height: 14px;;
        font-family: "Audi-Normal";
        height: 16px;
        color: #000;
        &::placeholder {
            font-size: 14px;
            line-height: 14px;
            margin: 12px 0;
            color: #000;
        }
    }

    .van-field__body {
        min-height: 30px;

        textarea {
            width: auto;
            height: 100%;
            line-height: 40px;
        }
    }
}