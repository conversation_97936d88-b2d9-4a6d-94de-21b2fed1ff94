/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-08-11 10:00:38
 * @LastEditors  : Lance Ma
 * @LastEditTime : 2022-09-19 16:39:48
 * @FilePath     : \src\config\popup-custom.data.js
 * @Descripttion : POPUP_CUSTOM
 */
export default {
  QUICK_LANGUAGE: {
    jumpIn: '跳转中 ...',
    turnOn: '打开中 ...'
  },
  INSURANCE: {
    desc: [
      '是否为爱车购买保险',
      '您将前往第三方“上海汽车集团保险销售有限公司”提供的页面',
      '1.第三方在提供服务的过程中向您做出的任何承诺、声明或行为仅适用于第三方与您之间的服务，不视为上汽奥迪的承诺、声明或行为。',
      '2.若您因未遵守第三方相关授权文件的规定或要求，造成您的任何损失，上汽奥迪不承担任何责任。',
      '3. 为保证车险购买及相关服务的顺利进行，需要传输给第三方“上海汽车集团保险销售有限公司”个人信息的必要字段，包括：姓名、电话号码、身份证号、手机号码、电子邮箱、邮寄地址、会员号、车辆信息（车辆VIN、车牌号、发动机号、车辆型号、款式、初登日期、车辆发票号码、车辆发票日期）。 ',
      '您存在未使用的保险权益，请在卡包查看，或咨询销售顾问'
    ],
    conf: {
      enabled: false,
      show: false,
      width: 'calc(100% - 2px)',
      customClass: 'lan-popup-custom-btn', // un-has-margin-top
      flex: 'main:left'
    },
    prepare: {
      cardBags: {
        native: 'app',
        name: 'card-bag',
        text: '前往卡包',
        click: 'GetActionBtn'
      },
      buyInsurance: {
        native: 'app',
        name: 'buy-insurance',
        text: '确定购买',
        click: 'GetActionBtn'
      }
    },
    items: [
      {
        plain: true,
        radius: '20px',
        text: '我再想想'
      }
    ]
  },
  REPLACEMENT: {
    desc: [
      '您将取消目前的贷款申请流程，取消后可重新选择金融机构进行申请，是否确认。'
    ],
    conf: {
      enabled: true,
      show: false,
      width: 'calc(100% - 2px)',
      customClass: 'lan-popup-custom-btn', // un-has-margin-top
      flex: 'main:left'
    },
    items: [
      {
        plain: true,
        radius: '20px',
        text: '取消'
      },
      {
        name: 'reset',
        text: '确认',
        click: 'GetActionBtn'
      }
    ]
  },
  CARS_SERVICE: {
    desc: [],
    conf: {
      enabled: true,
      show: false,
      width: 'calc(100% - 2px)',
      customClass: 'lan-popup-custom-btn', // un-has-margin-top
      flex: 'main:left'
    },
    items: [
      {
        plain: true,
        radius: '20px',
        text: '返回',
        click: 'GetBackBtn'
      },
      {
        name: 'reset',
        text: '立即前往',
        click: 'GetActionBtn'
      }
    ]
  }
}
