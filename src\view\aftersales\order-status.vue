<template>
  <div name="name" class="container2">
    <div id="container2" style="width: 100vw; height: 300px" v-show="driveList.length > 0 && statusList[0].status !== 20101100 "/>
    <div v-if="!loading">
      <div class="item-store" style="margin-top: 16px" v-if="orderInfo.driverName !== null">
        <div class="content-wrapper flex1">
          <div style="margin-top: 16px" />
          <div class="c-font14" style="font-size: 16px">
            {{  "代驾司机：" + orderInfo.driverName ? orderInfo.driverName : '' }}
          </div>
          <div style="margin-top: 16px" />
          <div class="c-font14" style="font-size: 16px">
            {{ "代驾司机工号：" + orderInfo.driverJobNo ? orderInfo.driverJobNo : '' }}
          </div>
        </div>
        <div class="navgation-wrapper">
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""
            @click="callPhone"
          />
        </div>
      </div>
      <div class="item-wrapper" style="margin-top: 16px" v-if="orderInfo.address">
        <div class="content-wrapper">
          <div class="c-font14" style="display: flex; flex-wrap: wrap; width: 100px;color: #333;">
            {{orderInfo.orderType === 20131010 ? "取车地址" :"送车地址" }}
          </div>

        </div>
        <div class="navgation-wrapper">
          {{orderInfo.address}}
        </div>
      </div>
      <div class="item-wrapper" style="margin-top: 16px" v-if="orderInfo.addressSupplement">
        <div class="c-font14" style="display: flex; flex-wrap: wrap; width: 100px;color: #333;">
          {{"详细地址"}}
        </div>
        <div class="navgation-wrapper">
          {{orderInfo.addressSupplement }}
        </div>
      </div>

      <div class="item-wrapper" style="margin-top: 16px" >
        <div class="c-font14" style="display: flex; flex-wrap: wrap; width: 100px;color: #333;">
          {{"需求订单号"}}
        </div>
        <div class="navgation-wrapper">
          {{orderInfo.orderId }}
        </div>
      </div>
      <div class="item-wrapper" style="margin-top: 16px" >
        <div class="c-font14" style="display: flex; flex-wrap: wrap;color: #333;">
          {{"收车验证码"}}
        </div>
        <div class="navgation-wrapper">
          {{ orderInfo.orderType === 20131020 ? orderInfo.receiveCode:"无" }}
        </div>
      </div>
      <div class="c-font12" style="display: flex; flex-wrap: wrap;color: #333;padding-left: 16px;margin-top: 8px">
        {{"代驾司机到达前请勿提供"}}
      </div>
      <div v-for="(item, index) in statusList" :key="index" style="margin-top: 16px" >
        <div class="item">
          <div class="item_header">
            <div class="vertical-line" v-if="index !== 0" />
          </div>
          <div class="item_foot">
            <div class="item-content">
              <div class="div" style="padding-left: 26px; font-size: 14px">
                {{ getTime(item.createdAt) }}
              </div>
              <div class="div" style="padding-left: 16px; font-size: 14px">
                {{ getDate(item.createdAt) }}
              </div>
            </div>
            <div
              class="item-content"
              style="padding-left: 16px; padding-top: 8px"
            >
            <span class="div">
              {{ item.statusName }}
            </span>
              <div class="sudoku_row" v-show="item.status === 20101070">
                <div
                  class="sudoku_item"
                  v-for="(item, index) in imgList"
                  :key="index"
                  @click.stop="toIMGDetail(item)"
                >
                  <img width="50px" height="50px" :src="'//' + item" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="height: 80px" />
    </div>
    <div v-else
         style='min-height: 60vh;display: flex;align-items: center;justify-content: center'>
      <van-loading
        color="#000"
        class="loadingVant"
        vertical
        type="spinner"
      >
        加载中...
      </van-loading>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { getOrderStatusList, getCoordsList, getCarImages } from "@/api/api";
import Vue from "vue";
import { Grid, GridItem, Image as VanImage } from "vant";
import { callNative } from "@/utils";

Vue.use(Grid);
Vue.use(GridItem);
Vue.use(VanImage);
export default {
  name: "Name",
  data() {
    return {
      orderInfo: {},
      statusList: [],
      imgList: [],
      driveList: [],
      map: null,
      startIconList:[],//开始坐标
      endIcon:'',//结束坐标
      appImgList: [],
      loading: true
    };
  },
  computed: {

  },
  async mounted() {

    const map = new AMap.Map(document.getElementById("container2"), {
      zoom: 16, // 显示范围
      //       center: [121.20215, 31.41444], // 初始显示的坐标
      resizeEnable: true,
    });
    this.map = map;

    const { orderId } = this.$route.query;
    await this.getCoordsList(orderId);
    await this.getOrderStatusList(orderId);
    this.loading = false
  },
  methods: {
    async getCoordsList(orderId) {
      const { data } = await getCoordsList({
        orderId: orderId,
      });
      if (data.code === "200") {
        this.startIconList = data.data.drive
        if (data.data.drive.length > 0) {
          data.data.drive.forEach((item) => {
            this.driveList.push([item.longitude, item.latitude]);
          });
        }
        if (this.driveList.length > 1) {
          this.polyLine();
        }
      }
    },
    async getOrderStatusList(orderId) {
      const { data } = await getOrderStatusList({
        orderId: orderId,
      });
      if (data.code === "200") {
        this.orderInfo = data.data;
        this.statusList = data.data.list;

        this.getCarImages(orderId);
      }
    },
    async getCarImages(orderId) {
      const { data } = await getCarImages({
        orderId: orderId,
      });
      if (data.code === "200") {
        this.imgList = data.data;
      }
    },
    callPhone() {
      window.location.href = `tel:${this.orderInfo.driverPhone}`;
    },
    getTime(createdAt) {
      return createdAt.substring(10, 16).replace(/\-/g, ".");
    },
    getDate(createdAt) {
      return (
        createdAt.substring(5, 7).replace(/\-/g, ".") +
        "月" +
        createdAt.substring(8, 10).replace(/\-/g, ".") +
        "日"
      );
    },
    polyLine() {
      this.map.setFitView();

      var  lineArr = this.driveList

      console.log('lineArr',lineArr)

      // 绘制轨迹
      var polyline = new AMap.Polyline({
        // map: this.map,
        path: lineArr,
        // showDir: true,
        strokeColor: "#02Be3e", //线颜色
        // strokeOpacity: 1,     //线透明度
        // strokeWeight: 4, //线宽
        // strokeStyle: "solid"  //线样式
        // strokeColor: "#FF33FF",
      strokeWeight: 6,
      strokeOpacity: 0.9,
      zIndex: 50,
      bubble: true,
      });
      this.map.add(polyline)

      const icon = new AMap.Icon({
        // 自定义图标
        size: new AMap.Size(33, 33),
        image: require("../../assets/img/map-index.png"),
        imageSize: new AMap.Size(33, 33),
        anchor: "center",
      });
      const icon2 = new AMap.Icon({
        // 自定义图标
        size: new AMap.Size(33, 33),
        image: require("../../assets/img/icon-map-position.png"),
        imageSize: new AMap.Size(33, 33),
        anchor: "center",
      });
      const marker1 = new AMap.Marker({
        // 插点
        icon: icon,
        position:new AMap.LngLat(this.startIconList[0].longitude,this.startIconList[0].latitude),
        offset: new AMap.Pixel(-13, -30)

      });
      this.map.add(marker1);
      let last = this.startIconList.pop()

      const marker2 = new AMap.Marker({
        // 插点
        icon: icon2,
        position:  new AMap.LngLat(last.longitude,last.latitude),
        offset: new AMap.Pixel(-13, -16)
      });
      this.map.add(marker2);
      this.map.panTo([last.longitude,last.latitude]);
      // this.map.setZoomAndCenter(18, [last.longitude,last.latitude]);
    },

    toIMGDetail(item){
      this.appImgList = []
      for (let i = 0; i < this.imgList.length; i++) {
        this.appImgList.push("https://"+this.imgList[i])
      }
      callNative("showImages", { index: item.index ,urls:this.appImgList});

    },
  },
};
</script>

<style lang='less' scoped>
@import "../../assets/style/common.less";

.item-store {
  margin: 16px;
  .c-flex-between;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 16px;
}
.item-wrapper {
  margin-left: 16px;
  margin-right: 16px;
  .c-flex-between;
}

.nav-icon {
  width: 24px;
  height: 24px;
  flex-direction: column;
  margin-top: 26px;
}

.item {
  width: 100%;
  // display: flex;
  // align-items: center;
  flex-flow: column;

  .item_header {
    // display: flex;
    // justify-content: space-between;
    // width: 100%;
    padding-left: 48px;
    padding-top: 4px;
    padding-bottom: 4px;
    .vertical-line {
      width: 1px;
      height: 40px;
      background: #d8d8d8;
    }
  }
  .item_foot {
    display: flex;
    width: 100%;
    .div {
      width: 100%;
      font-size: 16px;
      padding-top: 5px;
      color: #000000;
      text-align: left;
      white-space: normal;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      -webkit-line-clamp: 2;
      font-family: "Audi-Normal";
    }
    .item-content {
      box-sizing: border-box;
      display: flex;
      flex-flow: column;
      // justify-content: space-between;
      align-items: flex-start;

      .div {
        width: 100%;
        font-size: 16px;
        color: #000000;
        text-align: left;
        white-space: normal;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
        font-family: "Audi-Normal";
      }

      .item-p {
        font-size: 14px;
        font-family: "Audi-Normal";
        color: #000;
        line-height: 16px;
      }
    }
  }
}

.sudoku_row {
  margin-top: 8px;
  display: flex;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
}
.sudoku_item {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 70px;
  padding: 3px;
}
.sudoku_item img {
  width: 70px;
  height: 70px;
  display: block;
}
</style>
