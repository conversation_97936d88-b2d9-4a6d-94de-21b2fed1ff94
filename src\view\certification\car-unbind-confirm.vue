<template>
  <div class="unbindConfim">
    <div class="text-content">
      <h3 style="margin-top: 8px">解绑后您的服务将受到影响，包含但不限于:</h3>
      <p>
        1、移动互联车主专属服务将不再可用，包括车辆状态查看、远程控制、车载第三方应用等；
      </p>
      <p>2、所有授权用户权限将被收回，无法继续使用服务；</p>
      <p>3、如您的车辆曾领取充电权益，解绑车辆后将无法使用；</p>
      <p>
        4、如您的车辆享有首任车主权益，如首任车主卡券及凭证，解绑车辆后即使再次绑车到本账号，权益也不可恢复；
      </p>
      <p>
        5、如您的车辆曾领取过某些活动的卡券，解绑车辆后即使再次绑车到本账号或别的账号，权益也不可恢复；
      </p>
      <p>
        6、如您的车辆已经预约或报名某项服务，解绑车辆后即使再次绑车到本账号，相关预约或服务预留也可能会取消；
      </p>
      <p>
        7、解绑车辆将即时失去【认证车主】身份分，会员等级及权益将随之变化。城市车友会活动及其他线上活动的参与资格、权益奖励也将受影响；
      </p>
      <p>
        8、请注意，解绑车辆并不代表车辆解绑前的行为和相关责任得到豁免或减轻。
      </p>
    </div>
    <div class="footer-btn">
      <AudiButton
        @click="onConfirm"
        text="确认，我要解绑"
        color="black"
        font-size="15px"
        height="48px"
      />
    </div>
  </div>
</template>

<script>
import AudiButton from '@/components/audi-button'
export default {
  components: {
    AudiButton
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    onConfirm() {
      sessionStorage.setItem('unbindConfirm', '1')
      this.$router.back(-1)
    }
  }
}
</script>
<style lang="less" scoped>
.unbindConfim {
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-family: 'Audi-Normal';
  font-size: 14px;
  .text-content {
    overflow-y: auto;
    h3 {
      margin-bottom: 10px;
      color: #1a1a1a;
      line-height: 22px;
      font-weight: normal;
      font-family: 'Audi-WideBold';
    }
    p {
      color: #4c4c4c;
      line-height: 22px;
    }
  }
  .footer-btn {
    position: fixed;
    padding: 0 16px;
    bottom: 0;
    left: 0;
    margin-top: 8px;
    width: 100%;
    height: 86px;
    box-sizing: border-box;
  }
}
</style>
