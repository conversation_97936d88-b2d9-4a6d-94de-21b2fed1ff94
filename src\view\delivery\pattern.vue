<template>
  <div
    :class="
      ['delivery-pattern-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      background-color="transparent"
      header-left-icon-color="white"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
    />
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <div class="delivery_mode">
        <div class="_box">
          <van-radio-group
            class="lan-radio-group"
            v-model="seletIndex"
            :disabled="!!deliveryType"
          >
            <van-radio
              v-for="(type, index) in TYPE_MAP"
              :key="index"
              :name="type.value"
              :class="[!(type.value === seletIndex) || 'selected']"
            >
              <div
                class="list-box"
                data-flex="main:left cross:center"
              >
                <div class="icon-box">
                  <img
                    :class="type.icon"
                    :src="require(`@/assets/img/${type.icon}.png`)"
                  >
                </div>
                <div class="info-box">
                  <h4 class="h4">
                    {{ type.title }}
                  </h4>
                  <span class="desc">{{ type.desc }}</span>
                </div>
              </div>
            </van-radio>
          </van-radio-group>
          <div class="_box-card">
            <div v-if="seletIndex === 2">
              <div
                class="_box-card-list border-bottom"
                @click="cityShow = !deliveryType"
              >
                <div class="">
                  交车城市
                </div>
                <div class="_right">
                  <div class="c-font14">
                    {{ cityValue ? cityValue.name:'请选择交车城市' }}
                  </div>
                  <img src="../../assets/img/icon_20.png">
                </div>
              </div>
              <div
                class="_box-card-list border-bottom"
                @click="typeShow = true"
              >
                <div class="">
                  运输服务类型
                </div>
                <div class="_right">
                  <div class="c-font14">
                    {{ typeValue?typeValue.label:'请选择运输服务类型' }}
                  </div>
                  <img src="../../assets/img/icon_20.png">
                </div>
              </div>
              <div class="_tips">
                {{ typeValue.tips }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="btn-wrapper"
          v-if="!deliveryType"
        >
          <AudiButton
            text="确认"
            @click="onSubmit"
            font-size="16px"
            color="black"
            height="50px"
          />
        </div>

        <van-popup
          v-model="cityShow"
          position="bottom"
          :style="{ height: '48%' }"
        >
          <van-area
            :area-list="areaList"
            :value="cityValue && cityValue.code"
            :columns-num="2"
            :visible-item-count="5"
            @confirm="onCityConfirm"
            @cancel="cityShow = false"
          />
        </van-popup>

        <van-popup
          position="bottom"
          :style="{ height: '45%' }"
        >
          <van-picker
            :visible-item-count="5"
            show-toolbar
            value-key="label"
            :columns="columns"
            @confirm="onTypeConfirm"
            @cancel="typeShow = false"
          />
        </van-popup>
      </div>
    </div>
  </div>
</template>

<script>
import { areaList } from '@vant/area-data'
import Vue from 'vue'
import { mapState } from 'vuex'
import {
  Popup, Area, Picker, Toast, RadioGroup,
  Radio
} from 'vant'
import HeaderCustom from '@/components/header-custom.vue'
import { getMyOrders, updateAmsDeliverType, getNearestDealerList, afterServiceScGeocodeGeo } from '@/api/api'
import model from '@/components/model.vue'


import AudiButton from '@/components/audi-button'
import { getdiscount2 } from '@/utils'

const TYPE_MAP = [
  {
    value: 1,
    icon: 'icon-store',
    title: '到店提车',
    desc: '您将到店进行车辆交付'
  },
  {
    value: 2,
    icon: 'icon-location',
    title: '送车上门',
    desc: '相关费用将由代理商向您收取'
  }
]

Vue.use(Area).use(Picker).use(Popup).use(RadioGroup)
  .use(Radio)

const codeType = ['00', '200']
export default {
  components: { AudiButton, 'header-custom': HeaderCustom },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      deliveryType: '',
      TYPE_MAP,
      cityShow: false, // 省市区选择器
      areaList,
      cityValue: '', // 选中的城市
      seletIndex: 1, // 1到店提车 1上车上门

      typeShow: false, // 运输类型选择器
      columns: [{
        label: '同城送车上门',
        value: '2',
        tips: '代理商将会为您提供同城送车上门服务，相关运输费用将由您线下向代理商支付。'
      },
      {
        label: '常规异地交车',
        value: '3',
        tips: '相关运输费用将由上汽奥迪进行补贴，您无需支付任何费用。'
      },
      {
        label: '非常规异地交车',
        value: '4',
        tips: '由于您选择的代理商与您的交车城市属于非常规线路，相关运输费用将由您自行承担'
      }
      ],
      typeValue: '', // 选中的运输类型
      dealerInfo: null
    }
  },
  computed: mapState({
    dealerDetail: (state) => state.dealerInfo
  }),
  // 交车城市 是按照当前城市市政府的坐标
  // 代理商坐标是按照实际的
  watch: {
    cityValue(newValue, oldValue) {
      if (newValue && newValue.name === this.dealerInfo.city) {
        this.typeValue = {
          label: '同城送车上门',
          value: '2',
          tips: '代理商将会为您提供同城送车上门服务，相关运输费用将由您线下向代理商支付。'
        }
      } else if (newValue && newValue.name !== this.dealerInfo.city) {
        this.getLocationAddress()
      }
    }
  },
  mounted() {
    const { deliveryType } = this.$route.query
    if (deliveryType) {
      this.seletIndex = parseInt(deliveryType)
      this.deliveryType = deliveryType
    }
    this.getMyOrders()
  },
  methods: {
    async getMyOrders() {
      const { orderId, isluckybag } = this.$route.query
      if (isluckybag) {
        this.dealerInfo = this.dealerDetail
        this.dealerInfo.city = this.dealerDetail.cityName
        const { deliverAddr } = this.$route.query
        if (deliverAddr) {
          this.cityValue = { name: this.areaList.city_list[deliverAddr], code: deliverAddr }
        } else {
          this.cityValue = { name: this.dealerInfo.cityName, code: this.dealerInfo.cityCode }
        }
        this.getDealerLocation()
      } else {
        const { data } = await getMyOrders({ orderId })
        this.dealerInfo = data.data.dealerInfo
        const { deliverAddr } = this.$route.query
        if (deliverAddr) {
          this.cityValue = { name: this.areaList.city_list[deliverAddr], code: deliverAddr }
        } else {
          this.cityValue = { name: data.data.dealerInfo.city, code: data.data.dealerInfo.cityCode }
        }
        this.getDealerLocation()
      }
    },

    getDealerLocation() { // 获取订单代理商的地址坐标
      afterServiceScGeocodeGeo({
        address: this.dealerInfo.address
      }).then(res => {
        console.info('🚀 ~ file:select-user-address method: line:223 -----', res.data.data.data)
        let result = res.data.data.data
        this.dealerInfo.lat = result.geocodes[0].location.split(',')[1]
        this.dealerInfo.lng = result.geocodes[0].location.split(',')[0]
      })
    },

    getLocationAddress() { // 获取交车城市市政府的坐标  并拉取附近代理商列表
      afterServiceScGeocodeGeo({
        address: `${this.cityValue.name}人民政府`
      }).then(async res => {
        let result = res.data.data.data
        this.cityValue.lat = result.geocodes[0].location.split(',')[1]
        this.cityValue.lng = result.geocodes[0].location.split(',')[0]

        const data = await getNearestDealerList({ latitude: this.cityValue.lat, longitude: this.cityValue.lng, page : 'deliveryPattern' })
        const dealerlist = data.data.data
        const dealerlist1 = await Promise.all(dealerlist.map(async (i) => {
          const discount = await getdiscount2(this.cityValue.lng || 0, this.cityValue.lat || 0, i.longitude || 0, i.latitude || 0)
          return { ...i, discount: discount.toFixed(2) }
        }))
        const dealerlist2 = dealerlist1.sort((a, b) => parseFloat(a.discount) - parseFloat(b.discount))

        console.log('%c [ dealerlist2 ]-254', 'font-size:13px; background:pink; color:#bf2c9f;', dealerlist2)
        if (dealerlist2.length > 0 && dealerlist2[0].dealerName === this.dealerInfo.dealerName) {
          this.typeValue = {
            label: '常规异地交车',
            value: '3',
            tips: '相关运输费用将由上汽奥迪进行补贴，您无需支付任何费用。'
          }
        } else {
          this.typeValue = {
            label: '非常规异地交车',
            value: '4',
            tips: '由于您选择的代理商与您的交车城市属于非常规线路，相关运输费用将由您自行承担'
          }
        }
      })
    },

    async onSubmit() {
      if (!this.seletIndex) {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '请勾选交车方式',
          forbidClick: true,
          duration: 800
        })
        return false
      }
      const { orderId, isluckybag } = this.$route.query
      const param = { orderId }
      if (this.seletIndex === 1) {
        this.typeValue = { label: '到店提车', value: '1' }
        param.deliverAddr = this.dealerInfo.cityCode
        param.deliverType = '1'
      }
      if (this.seletIndex === 2) {
        if (!this.cityValue) {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '请选择交车城市',
            forbidClick: true,
            duration: 800
          })
          return false
        }
        if (!this.typeValue) {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '请选择运输服务类型',
            forbidClick: true,
            duration: 800
          })
          return false
        }
        param.deliverAddr = this.cityValue.code
        param.deliverType = this.typeValue.value
      }
      if (isluckybag) {
        param.deliveryName = this.typeValue.label
        console.log(param)
        this.$store.commit('updateDeliveryPattern', param)
        this.$router.go(-1)
      } else {
        const { data } = await updateAmsDeliverType(param)
        if (codeType.includes(data.code)) {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '设置成功',
            forbidClick: true,
            duration: 800
          })
          this.$router.go(-1)
        }
      }
    },
    onCityConfirm(e) {
      this.cityValue = e[1]
      this.cityShow = false
    },
    onTypeConfirm(item, index) {
      this.typeShow = false
      this.typeValue = item
    },
    getSeletIndex(e) {
      this.seletIndex = e
    }
  }
}
</script>


<style lang='less' scoped>
.delivery-pattern-wrapper {
  .main-wrapper {
    background: url(~@/assets/img/delivery-bg.jpg) no-repeat 50% 0;
    background-size: contain;
  }
  /deep/.header-wrapper {
    .header-middle {
      opacity: 0;
    }
  }
}

  .delivery_mode {
    padding-top: 300px;
    width: 100%;
    font-family: "Audi-Normal";
    padding-bottom: 20px;


    ._box {
      padding: 0 16px;
      width: 100%;
      box-sizing: border-box;
      margin-bottom: 100px;

      ._title {
        line-height: 52px;
        font-family: "Audi-WideBold";
      }

      ._box-card {
        width: 100%;
        background: #FFFFFF;
        margin-bottom: 20px;

        img {
          width: 100%;
        }

        &-list {
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          height: 58px;
          margin: 0 16px;

          img {
            width: 24px;
          }

          ._right {
            display: flex;
            align-items: center;
            color: #666666;
          }
        }

        ._tips {
          font-size: 12px;
          color: #666666;
          line-height: 15px;
          letter-spacing: 1px;
          padding: 15px 16px;
        }
      }
    }
  }

  .btn-wrapper {
    position: fixed;
    padding: 5px 16px 25px;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    background-color: white;
    z-index: 999;
    box-sizing: border-box;
    box-shadow: 0px -1px 16px 0px rgba(0,0,0,0.08);
  }

  .wideBold {
    font-family: "Audi-WideBold";
  }

  .border-bottom {
    border-bottom: 1px solid #E5E5E5;
  }
</style>
<style lang='less'>
.delivery_mode {
  .lan-radio-group {
    width: 100%;
    .van-radio {
      border: 1px solid rgba(0, 0, 0, .1);
      position: relative;
      padding: 28px 12px 28px 20px;
      margin-bottom: 16px;
      box-sizing: border-box;
      &.selected {
        border-color: #000;
        box-shadow: 0px 0px 8px 0px rgba(51,51,51,0.16);
      }
      .van-radio__icon {
        position: absolute;
        right: 28px;
      }
      .van-radio__label {
        .icon-box {
          width: 34px;
          img {
            width: 26px;
            height: 26px;
            &.icon-location {
              width: 30px;
              height: 30px;
            }
          }
        }
        .info-box {
          padding-left: 16px;
          .h4 {
            color: #000;
            margin: 0 0 4px 0;
            font-size: 14px;
            line-height: 17px;
            font-weight: 400;
            text-shadow: 0px 0px 8px rgba(51,51,51,0.16);
          }
          .desc {
            line-height: 12px;
            font-size: 10px;
            color: rgba(0, 0, 0, .5);
          }
        }
        &.van-radio__label--disabled {
          .h4 {
            color: #000;
          }
        }
      }

    }
    .van-radio__icon--round {
      .van-icon {
        width: 24px;
        height: 24px;
        box-sizing: border-box;
      }
      &.van-radio__icon--checked {
        .van-icon {
        }
      }
    }
  }
}
</style>
