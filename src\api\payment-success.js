/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-06-23 13:24:50
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-08-23 15:49:44
 * @FilePath     : \src\api\payment-success.js
 * @Descripttion :
 */
import request from '../router/axios'
import api from '../config/url'
import { getToken } from '../utils/auth'

const baseUrl = api.BaseApiUrl
// 限量号介绍
export const floorsIntroduce = () => request({
  url: `${baseUrl}/api-wap/audi-page/api/floors?pageAccessType=2&pageFrontCode=audi-limit-number`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})
export const getMyOrders = (data) => request({
  url: `${baseUrl}/api-wap/cop-order-query/api/v1/my-orders/${data.orderId}`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})
// 限量号查询
export const limitedNumbers = (data) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/limited_numbers/getLimitedNumberByOrderId?orderId=${data.orderId}`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
  // data: {
  //     ...data
  // }
})

// 修改限量号
export const putLimitedNumbers = (data) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/limited_numbers/updateNumberDisplay`,
  method: 'POST',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  data: {
    ...data
  }
})

// export const getLimitImg = () => {
//     return request({
//         url: baseUrl + "/api-wap/audi-page/api/floors?pageAccessType=2&pageFrontCode=audi-limit-number",
//         method: "GET",
//         headers: {
//             'x-access-token': localStorage.getItem('token')
//         },
//     })
// }
// 生成企业微信码
export const getCipCampaign = (params) => request({
  url: `${baseUrl}/api-wap/audi-task/api/cipCampaign/queryQRCodeByPlaceOrder`,
  method: 'GET',
  params
})
