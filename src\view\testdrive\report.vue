/* eslint-disable block-scoped-var */
/* eslint-disable block-scoped-var */
<template>
  <div
    class="container"
    id="container"
  >
    <img
      v-if="seriesCode==='49'"
      class="bgimg"
      :src="'https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/bgImg1.png' | osswebp"
    >
    <img
      v-if="seriesCode==='G4'"
      class="bgimg"
      :src="'https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/bgImg2.png' | osswebp"
    >
    <template v-if="$route.query.env !== 'minip'">
      <img
        class="backicon"
        src="../../assets/img/icon03.png"
        @click="doback"
        v-show="!toPngTime"
      >
      <img
        class="shareicon"
        src="../../assets/img/icon18.png"
        @click="doshare"
        v-show="!toPngTime"
      >
    </template>
    <div class="zhizhen">
      <img
        class="zhizhen-img1"
        src="https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/zhizhen/zhizhen1.png"
      >
      <img
        class="zhizhen-img2"
        src="https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/zhizhen/zhizhen2.png"
        :style="zhizhenRotate2"
      >
      <img
        class="zhizhen-img3"
        src="https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/zhizhen/zhizhen3.png"
        :style="zhizhenRotate3"
      >
      <div class="score">
        <div class="score-item1">
          {{ data.level }}
        </div>
        <div class="score-item2">
          {{ data.levelName }}
        </div>
      </div>
      <div class="parameter">
        <div
          class="item"
          v-for="item in parameter"
          :key="item.title"
        >
          <img
            class="item-data1"
            :src="item.img | osswebp"
          >
          <div class="item-data2">
            {{ item.title }}
          </div>
          <div class="item-data3">
            {{ item.data }}
          </div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="left">
        <div class="item">
          <div class="lebal">
            试驾门店：
          </div>
          <div class="text">
            {{ data.orgName }}
          </div>
        </div>
        <div class="item">
          <div class="lebal">
            试驾地址：
          </div>
          <div class="text">
            {{ data.orgAddress }}
          </div>
        </div>
      </div>
      <img
        class="right"
        src="https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/zhizhen/qrcode.png"
      >
    </div>
    <!-- <canvas
      class="canvas"
      id="canvas"
    /> -->
    <!-- <img :src="canvasImg"> -->
  </div>
</template>
<script>
// import domtoimage from 'dom-to-image'
import domtoimage from '@/utils/lib/dom-to-image'
import {
  callNative
} from '@/utils/index'
import url from '@/config/url'
import {
  getShareReport, shareReport, syncReport
} from '../../api/test-driver'


const BaseApiUrl = url.BaseApiUrl
export default {
  name: 'Report',
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      id: '',
      appoid: '',
      seriesCode: '49',
      parameter: [
        { img: 'https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/zhizhen/icon12.png', title: '驾驶时长', data: 'XX MIN' },
        { img: 'https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/zhizhen/icon13.png', title: '驾驶里程', data: 'XX MKM' },
        { img: 'https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/zhizhen/icon14.png', title: '紧急加速', data: 'XX 次' },
        { img: 'https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/zhizhen/icon15.png', title: '紧急制动', data: 'XX 次' }
      ],
      canvasImg: '',
      data: {},
      toPngTime: false,
      zhizhenRotate2: {},
      zhizhenRotate3: {}
    }
  },
  created() {
    this.$store.commit('setHeaderVisible', false)
  },
  mounted() {
    this.$store.commit('setHeaderVisible', false)
    this.id = this.$route.query.id
    this.appoid = this.$route.query.appoid
    this.seriesCode = this.$route.query.seriesCode
    this.getShareReport(this.id)
    // shareReport({ testDriveId: this.id })
  },
  destroyed() {
    const { query } = this.$route
    if (query.env !== 'minip') {
      this.$store.commit('setHeaderVisible', true)
    }
  },
  methods: {
    doback() {
      this.$router.push({
        path: this.fromType ? '/testdrive/detail?fromType=fromPurple' : '/testdrive/detail',
        query: {
          id: this.appoid
        }
      })
    },
    async doshare() {
      const params = {
        test_drive_id: this.id
      }
      this.$sensors.track('clickShare', params)
      // domtoimage.toJpeg(document.getElementById('container'), { quality: 0.95 })
      //   .then((dataUrl) => {//jpeg
      //     const link = document.createElement('a')
      //     link.download = 'my-image-name.jpeg'
      //     link.href = dataUrl
      //     link.click()
      //   })

      // function filter(node) {
      //   return (node.tagName !== 'i')
      // }
      // domtoimage.toSvg(document.getElementById('container'), { filter: filter })
      //   .then((dataUrl) => { // base64
      //   })

      // domtoimage.toBlob(document.getElementById('container'))
      //   .then((blob) => {
      //     this.syncReport(blob)
      //   })
      this.$store.commit('showLoading')
      this.toPngTime = true
      domtoimage.toBlob(document.getElementById('container'), {
        scale: 2,
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
      })
        .then((blob) => {
          // const img = new Image()
          // img.src = dataUrl
          // document.body.appendChild(img)
          const formData = new FormData()
          formData.append('file', blob)
          formData.append('id', this.id)
          console.log('dataUrl11111112', blob, formData)
          this.syncReport(formData)
          this.toPngTime = false
        })
    },
    async getShareReport(id) {
      await getShareReport({ id: id }).then((res) => {
        this.$store.commit('hideLoading')
        if (res.status === 200 && res.data.code === '200') {
          // res.data.data.level = 28
          this.data = res.data.data
          this.parameter[0].data = `${this.data.duration} MIN`
          this.parameter[1].data = `${this.data.mileage} KM`
          this.parameter[2].data = `${this.data.accelerate} 次`
          this.parameter[3].data = `${this.data.backspace} 次`
        }
      })
    },
    async syncReport(data) {
      this.$store.commit('showLoading')
      syncReport(data)
        .then((response) => {
          this.$store.commit('hideLoading')
          if (response.status === 200 && +response.data.code === 200) {
            const shareImg = response.data.data.reportUrl
            callNative('audiShare', { shareImg: shareImg, type: 'testDrive', id: this.id }).then((res) => {
              console.log('audiSharedata', res)
              if (res.isShare) {
                shareReport({ testDriveId: this.id })
              }
            })
          }
        })
    },
    base64ToFile(data) {
      // 将base64 的图片转换成file对象上传 atob将ascii码解析成binary数据
      const binary = atob(data.split(',')[1])
      const mime = data.split(',')[0].match(/:(.*?);/)[1]
      const array = []
      for (let i = 0; i < binary.length; i++) {
        array.push(binary.charCodeAt(i))
      }
      const fileData = new Blob([new Uint8Array(array)], {
        type: mime
      })

      const file = new File([fileData], `${new Date().getTime()}.jpeg`, { type: mime })

      return file
    }
  },
  watch: {
    data(val) {
      if (+val.level >= 20 && +val.level <= 100) {
        // level值20~100对应旋转-108deg~108deg
        let b = 0
        if (+val.level < 60) {
          b = ((+val.level - 60) / 41) * 108
        } else {
          b = ((+val.level - 60) / 40) * 108
        }
        this.zhizhenRotate3 = { transform: `rotate(${b}deg)`, 'transform-origin': '50% 88%' }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.container{
  width: 100vw;height: 100vh;
  // background-image: url("../../assets/img/bgImg1.jpg");
  // background-size: 100% auto;
  position: relative;
  font-family: "Audi-Normal";
  overflow: hidden;
  .bgimg{
    width: 100vw;height: auto;
  }
  .backicon{
    width: 24px;height: 24px;
    position: absolute;
    left: 16px;
    top: 60px;
  }
  .shareicon{
    width: 24px;height: 24px;
    position: absolute;
    right: 16px;
    top: 60px;
  }
  .zhizhen{
    width: 92vw;height: 55.2vw;
    position: absolute;
    top: 100px;left: 4vw;
    .zhizhen-img1{
      width: 100%;height: 100%;
      display: block;
    }
    .zhizhen-img2{
      width: 25.6vw;height: 8.8vw;
      display: block;
      position: absolute;
      bottom: 7.5vw;left: 33.33vw;
    }
    .zhizhen-img3{
      width: 5.6vw;height: 44.26vw;
      display: block;
      position: absolute;
      bottom: 7.5vw;left: 43.2vw;
    }
    .score{
      position: absolute;
      width: 100%;height: 60px;
      // bottom: -50px;
      // text-align: center;
      .score-item1{
        font-size: 28px;
        font-weight: bold;
        display: flex;
        justify-content: center;
      }
      .score-item2{
        font-size: 12px;
        font-weight: bold;
        margin: 6px 0 0 0;
        display: flex;
        justify-content: center;
      }
    }
    .parameter{
      position: absolute;
      left: 0;bottom: -150px;
      width: 100%;
      display: flex;
      .item{
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        // text-align: center;
        .item-data1{
          width: 24px;height: 24px;
        }
        .item-data2{
          font-size: 12px;
          margin-top: 8px;
        }
        .item-data3{
          font-size: 12px;
          margin-top: 8px;
        }
      }
    }
  }
  .bottom{
    width: calc(100vw - 32px);
    position: absolute;
    bottom: 30px;left: 16px;
    display: flex;
    color: #fff;
    .left{
      height: 76px;
      flex: 1;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .item{
        display: flex;
        line-height: 20px;
        padding: 4px 0;
        .text{
          flex: 1;
        }
      }
    }
    .right{
      width: 76px;height: 76px;
      display: block;
      margin-left: 8px;
    }
  }
  .canvas{
    position: fixed;
    width: 100vw;height: 100vh;
    pointer-events: none;
  }
}
</style>
