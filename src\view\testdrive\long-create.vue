<template>
  <div class="_container">
    <div class="_heardImg">
      <img :src="$loadWebpImage('https://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/bg11.png')" />
    </div>

     <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 80px; font-size: 16px; color: #000">试驾车系</div>
        <div class="str" v-if="seriesName">{{ seriesName  }}</div>
        <div class="str" style="color: #999" v-if="!seriesName">{{ "请选择" }}</div>
      </div>
      <div style="float: right">
        <van-icon class="btn-icon" name="arrow" size="16px" @click="showPickerSeries = true"/>
      </div>
    </div>

     <van-popup
        v-model="showPickerSeries"
        :lazy-render="false"
        round
        position="bottom"
      >
        <van-picker
          ref="testdriveSeries"
          show-toolbar
          :columns="columns"
          value-key="seriesName"
          @confirm="onConfirm({value:$event,label:'testdriveSeries'})"
          @cancel="onCancel"
        />
      </van-popup>

    <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 100px; font-size: 16px; color: #000">姓名</div>
        <!-- <div class="str" >{{ appoName  }}</div> -->
        <van-field
          class="str"
          type="text"
          v-model="appoName"
          placeholder="请输入姓名"
          rows="1"
        />
      </div>
    </div>
   
   <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 100px; font-size: 16px; color: #000">联系方式</div>
        <!-- <div class="str" >{{ mobile  }}</div> -->
         <van-field
          class="str"
          type="number"
          v-model="mobile"
          placeholder="请输入手机号"
          maxlength="11"
          rows="1"
        />
      </div>
    </div>


 <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 80px; font-size: 16px; color: #000">试驾城市</div>
        <div class="str" v-if="cityName">{{ cityName  }}</div>
        <div class="str" style="color: #999" v-if="!cityName">{{ "请选择" }}</div>
      </div>
      <div style="float: right">
        <van-icon class="btn-icon" name="arrow" size="16px" @click="showPickerCityName = true"/>
      </div>
    </div>
     <van-popup
        v-model="showPickerCityName"
        round
        position="bottom"
      >
        <van-picker
          ref="vanPicker"
          v-model="cityName"
          title="选择城市"
          show-toolbar
          :columns="areaList"
          @cancel="onCancel"
          @confirm="cityConfirm"
        />
    </van-popup>

    
      <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 80px; font-size: 16px; color: #000">代理商</div>
        <div class="str" v-if="orgName">{{ orgName  }}</div>
        <div class="str" style="color: #999" v-if="!orgName">{{ "请选择" }}</div>
      </div>
      <div style="float: right">
        <van-icon class="btn-icon" name="arrow" size="16px" @click="onPickerOrgName"/>
      </div>
    </div>

     <van-popup
        v-model="showPickerOrgName"
        :lazy-render="false"
        round
        position="bottom"
      >
        <van-picker
          ref="testdealer"
          show-toolbar
          :columns="orgList"
          value-key="dealerName"
          @confirm="onConfirm({value:$event,label:'testdealer'})"
          @cancel="onCancel"
        />
      </van-popup>

    <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 80px; font-size: 16px; color: #000">试驾时间</div>
        <div class="str" v-if="endDate">{{ beginDate.substring(5, 7).replace(/\-/g, ".")+"月"+beginDate.substring(8, 10).replace(/\-/g, ".") 
          +"日 - "+ endDate.substring(5, 7).replace(/\-/g, ".")+"月"+endDate.substring(8, 10).replace(/\-/g, ".")+"日" }}</div>
        <div class="str" style="color: #999" v-if="!endDate">{{ "请选择" }}</div>
      </div>
      <div style="float: right">
        <van-icon class="btn-icon" name="arrow" size="16px" @click="onSelectDate"/>
      </div>
    </div>

    <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 80px; font-size: 16px; color: #000">到店时间</div>
        <div class="str" v-if="time">{{ time  }}</div>
        <div class="str" style="color: #999" v-if="!time">{{ "请选择" }}</div>
      </div>
      <div style="float: right">
        <van-icon class="btn-icon" name="arrow" size="16px" @click="onPickerTime"/>
      </div>
    </div>
     <van-popup
        v-model="showPickerTime"
        :lazy-render="false"
        round
        position="bottom"
      >
        <van-picker
          ref="testdealer"
          show-toolbar
          :columns="timeList"
          @confirm="onConfirm({value:$event,label:'pickerTime'})"
          @cancel="onCancel"
        />
      </van-popup>
      <div class="interval_line"></div>
    <div class="line">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 80px; font-size: 16px; color: #000">上传证件</div>
        <div class="str" >{{ custIdCard  }}</div>
      </div>
      <div style="float: right">
        <van-icon class="btn-icon" name="arrow" size="16px" @click="onUploadIdCard()"/>
      </div>
    </div>


    <div class="btn-delete-height"></div>
    <div>
      <div class="bottom_style">
        <div style="margin-left: 16px;margin-right: 16px; font-size: 12px; color: #333">* 
超过“到店时间”所选择的时间后，预约超长试驾的客户将无法无条件取消超长试驾。详情请咨询当地上汽奥权代理商。</div>
        <div class="btn-delete-wrapper">
          <AudiButton
            @click="onNext"
            :text="'提交'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import Vue from "vue";
import {
  Form,
  Field,
  Button,
  ActionSheet,
  Cell,
  Popup,
  Area,
  Toast,
  Checkbox,
  CheckboxGroup,
  Icon,
  Picker,
  DatetimePicker,
  
} from "vant";
import AudiButton from "@/components/audi-button";
import { getUserInfo, getDealerList, getNearestDealerList } from '@/api/api'
import {
   getCustomSeries, getVeryLongReservationDealerList,postVeryLongReservationSubmit,
} from '../../api/test-driver'
import { callNative } from "@/utils";
import storage from "../../utils/storage";

Vue.use(Form)
  .use(Field)
  .use(Button)
  .use(ActionSheet)
  .use(Cell)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Area)
  .use(Toast)
  .use(Icon)
  .use(Picker)
  .use(DatetimePicker)
  ;
export default {
  components: {
    AudiButton,
  },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      showPickerSeries:false,
      columns: [
        // { code: '498B', description: '奥迪A7L' }
      ],
      showPickerCityName:false,//试驾城市
      areaList:[],
      showPickerOrgName:false,//代理商
      orgList:[],
      mobile:'',

      seriesCode:'',//	车系code(车系传OMD的车系数据),必填		false	
      seriesName:'',//	车系名称，必填		false	
      appoName:'',//	预约者名称，必填		false	
      cityName	:'',//城市名称,必填		false	
      channel:'',//	渠道id(1、奥迪官网：2、奥迪APP:3：上汽奥迪小程序),必填		false	

      beginDate:'',//	预约开始时间(yyyy-MM-dd)，必填		false	
      custIdCard	:'',//证件号码		false	
      endDate:'',//	预约结束时间(yyyy-MM-dd)，必填		false	
      orgCode:'',//	代理商编码，必填		false	
      orgName:'',//	代理商名称,必填		false	
      time:'',//	到店时间HH:mm,必填
      
      dateModel:{},
      showPickerTime:false,//时间
      timeList:[],
    };
  },

  async mounted() {
    
    var model = storage.get("saveLongCreate") || "{}";
    this.seriesCode = JSON.parse(model).seriesCode;
    this.seriesName = JSON.parse(model).seriesName
    this.appoName = JSON.parse(model).appoName;
    this.mobile = JSON.parse(model).mobile;
    this.orgCode = JSON.parse(model).orgCode;
    this.orgName = JSON.parse(model).orgName;
    this.cityName = JSON.parse(model).cityName
    this.areaList = JSON.parse(model).areaList
    this.orgList = JSON.parse(model).orgList
    this.time = JSON.parse(model).time


    var dateModel = storage.get("saveLongDate") || "{}";
    this.beginDate = JSON.parse(dateModel).start;
    this.endDate = JSON.parse(dateModel).end;
    this.timeList = JSON.parse(dateModel).list;

    var identityModel = storage.get("saveDrivingLicence") || "{}";
    this.custIdCard = JSON.parse(identityModel).idNumber;

    this.getCustomSeries()
    this.getUserInfo()
  },
  methods: {
    async getCustomSeries() {
      await getCustomSeries().then(async (res) => {
        this.columns = [res.data.data[1]]
        this.seriesCode = res.data.data[1] .seriesCode
        this.seriesName = res.data.data[1] .customSeriesName
        this.getVeryLongReservationDealerList()
      })
    },
     async getUserInfo() {
      const { data } = await getUserInfo()
      this.mobile = data.data.userInfo.mobile
      this.appoName = data.data.userInfo.displayName
    },
    //选择代理商
    onPickerOrgName(){
      if(!this.cityName){
        callNative('toast', { type: 'fail', message: '请选择试驾城市！' })
      }
      if(this.orgList.length < 1){
        callNative('toast', { type: 'fail', message: '您选择的试驾城市没有代理商' })
          return
      }
      this.showPickerOrgName = true
    },
    onPickerTime(){
      if(!this.beginDate){
        callNative('toast', { type: 'fail', message: '请选择试驾时间！' })
        return
      }
      this.showPickerTime = true
    },
    //选择城市
    async getVeryLongReservationDealerList(){
        const { data } = await getVeryLongReservationDealerList({seriesCode:this.seriesCode})
        if(data.code === '200'){
          this.areaList = data.data.filter((a) => {
              const children = a.list
              a.text = a.name
              const tmp = []
              let flag = false
              const codetmp = []
              children.forEach((b) => {
                // this.dealerList.forEach((c) => {
                  // if (c.cityCode === b.code && codetmp.indexOf(b.code) < 0) {
                    b.text = b.name
                    tmp.push(b)
                    flag = true
                    if (codetmp.indexOf(b.code) < 0) { // 去重
                      codetmp.push(b.code)
                    }
                  // }
                // })
              })
              a.children = tmp
              return flag
          })
        }
    },
    //上传证件
    onUploadIdCard(){
      this.sverSubmitParam()
      this.$router.push({
        path: this.fromType ? '/testdrive/long-upload-drivinglicence?fromType=fromPurple' : '/testdrive/long-upload-drivinglicence',
        query: {
         mobile: this.mobile
        }
      })
    },
    //选择试驾时间
    onSelectDate(){
     this.sverSubmitParam()
      if (this.seriesCode === '') {
        callNative('toast', { type: 'fail', message: '请选择试驾车系' })
        return
      }
      if (this.orgCode === '') {
        callNative('toast', { type: 'fail', message: '请选代理商' })
        return
      }
      this.$router.push({
        path: this.fromType ? '/testdrive/long-select-date?fromType=fromPurple' : '/testdrive/long-select-date',
        query: {
          orgCode: this.orgCode,
          seriesCode: this.seriesCode
        }
      })
    },
     sverSubmitParam() {
       const param = {
          seriesName: this.seriesName,
          seriesCode: this.seriesCode,
          appoName: this.appoName,
          mobile: this.mobile,
          orgCode:this.orgCode,
          orgName:this.orgName,
          cityName:this.cityName,
          areaList:this.areaList,
          orgList:this.orgList,
          time:this.time

        };
       storage.set("saveLongCreate", JSON.stringify(param));

    },

     onConfirm(res, index) {
      console.log('res.index', res, index)
      if (res.label === 'testdriveSeries') {
        this.showPickerSeries = false

        if(!this.seriesName){
          this.seriesCode = res.value.seriesCode
          this.seriesName = res.value.customSeriesName
          this.getVeryLongReservationDealerList()
        }else if(this.seriesName !== res.value.customSeriesName){ 
          this.seriesCode = res.value.seriesCode
          this.seriesName = res.value.customSeriesName
          this.getVeryLongReservationDealerList()
          //选择是车系不同时先清除数据
          this.orgList = [],
          this.cityName = ''//城市名称,必填		false	
          this.beginDate = ''//	预约开始时间(yyyy-MM-dd)，必填		false	
          this.endDate = ''//	预约结束时间(yyyy-MM-dd)，必填		false	
          this.orgCode = ''//	代理商编码，必填		false	
          this.orgName = ''//	代理商名称,必填		false	
          this.time = ''//	到店时间HH:mm,必填
          this.dateModel ={}
          this.timeList = []
        }
        
      } else if (res.label === 'testdealer') {
        //代理商
        this.showPickerOrgName = false
        if(!this.orgCode){
          this.orgCode = res.value.dealerCode
          this.orgName = res.value.dealerName
        }else if(this.orgCode !== res.value.dealerCode){
          this.orgCode = res.value.dealerCode
          this.orgName = res.value.dealerName
          //选择是代理商不同时先清除数据
          this.beginDate = ''//	预约开始时间(yyyy-MM-dd)，必填		false	
          this.endDate = ''//	预约结束时间(yyyy-MM-dd)，必填		false	
          this.time = ''//	到店时间HH:mm,必填
          this.dateModel ={}
          this.timeList = []
        }

      } else if (res.label === 'pickerTime') {
        this.time = res.value
        this.showPickerTime = false
      } 

    },
    //选择城市
     cityConfirm(value, index) {
       this.showPickerCityName = false
       if(!this.cityName){
          const cityCode = this.areaList[index[0]].children[index[1]].code
          this.cityName  = this.areaList[index[0]].children[index[1]].name
          this.orgList = this.areaList[index[0]].children[index[1]].dealers
       }else if(this.cityName !== this.areaList[index[0]].children[index[1]].name){
         const cityCode = this.areaList[index[0]].children[index[1]].code
          this.cityName  = this.areaList[index[0]].children[index[1]].name
          this.orgList = this.areaList[index[0]].children[index[1]].dealers
         //选择是城市不同时先清除数据
          this.beginDate = ''//	预约开始时间(yyyy-MM-dd)，必填		false	
          this.endDate = ''//	预约结束时间(yyyy-MM-dd)，必填		false	
          this.orgCode = ''//	代理商编码，必填		false	
          this.orgName = ''//	代理商名称,必填		false	
          this.time = ''//	到店时间HH:mm,必填
          this.dateModel ={}
          this.timeList = []
       }
      console.log('value', value, index)
     },
     onCancel() {
      this.showPickerSeries = false
      this.showPickerCityName = false
    },

    onApproachAddress(item) {
      this.showBuyingOrderApproachAddress = false;
      this.buyingOrderApproachAddress = item.buyingOrderApproachName;
      this.buyingOrderApproachCode = item.buyingOrderApproachCode
    },


    async onNext() {
      
        if (!this.seriesName) {
          callNative('toast', { type: 'fail', message: '请选择车系！' })
          return
        }
         if (!this.cityName) {
          callNative('toast', { type: 'fail', message: '请选择试驾城市！' })
          return
        }
         if (!this.orgCode) {
          callNative('toast', { type: 'fail', message: '请选择代理商' })
          return
        }
         if (!this.beginDate) {
          callNative('toast', { type: 'fail', message: '请选择试驾时间！' })
          return
        }

         if (!this.time) {
          callNative('toast', { type: 'fail', message: '请选择到店时间！' })
          return
        }
         if (!this.custIdCard) {
          callNative('toast', { type: 'fail', message: '请上传证件！' })
          return
        }
      this.goNext();
    },
    async goNext(){
      const param = {
          seriesCode:this.seriesCode,//	车系code(车系传OMD的车系数据),必填		false	
          seriesName:this.seriesName,//	车系名称，必填		false	
          appoName:this.appoName,//	预约者名称，必填		false	
          cityName	: this.cityName,//城市名称,必填		false	
          channel:'2',//	渠道id(1、奥迪官网：2、奥迪APP:3：上汽奥迪小程序),必填		false	

          beginDate:this.beginDate,//	预约开始时间(yyyy-MM-dd)，必填		false	
          custIdCard: this.custIdCard,//证件号码		false	
          endDate: this.endDate,//	预约结束时间(yyyy-MM-dd)，必填		false	
          orgCode:this.orgCode,//	代理商编码，必填		false	
          orgName:this.orgName,//	代理商名称,必填		false	
          time:this.time,//	到店时间HH:mm,必填
        };
     const { data } = await postVeryLongReservationSubmit(param)
      if(data.code === '200'){
        storage.set("saveLongCreate", JSON.stringify({}));
        storage.set("saveLongDate", JSON.stringify({}));
        storage.set("saveDrivingLicence", JSON.stringify({}));

        const param = {
          appoId: data.data.appoId,
          testDriveId: data.data.testDriveId,
          payUrl: data.data.payUrl,

        };
       storage.set("saveLongTestdrivePayUrl", JSON.stringify(param));

         this.$router.push({
              path: this.fromType ?  '/testdrive/long-create-success?fromType=fromPurple' : '/testdrive/long-create-success',
              query: {
                type: 0
              }
            })
      }else{
        callNative('toast', { type: 'fail', message: data.message })
      }

    },
  },
};
</script>

<style lang='less' scoped>
::v-deep .van-cell {
  padding: 0px;
}

::v-deep .van-cell::after {
  border-bottom: 1px #e5e5e5 solid;
}

::v-deep .van-button {
  border-radius: 0;
}

::v-deep .van-field__label {
  font-size: 16px;
  color: #000;
  width: fit-content;
}
::v-deep .van-field {
  .van-field__body {
    textarea {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }

    input {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }
  }
}

._heardImg {
  width: 100%;
}

._content {
  width: 100%;
  padding: 8px;
}

.interval_line {
  // margin-left: -16px;
  // margin-right: -16px;
  height: 8px;
  background: #f2f2f2;
}

.line {
   box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    margin-left: 16px;
    margin-right: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f2f2f2;
    white-space: nowrap;
  }
   .str {
      font-family: "Audi-Normal";
      font-size: 16px;
      color: #333;
    }
.btn-delete-height {
  height: 120px;
}
.btn-delete-wrapper {
  margin: 16px;
}
.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: absolute;
  bottom: 0px;
  // padding: 16px;
  left: 0px;
}

.certificate-type {
  display: flex;
  align-items: center;
  background: #f2f2f2;
  padding: 14px;
  border-bottom: 1px solid #e5e5e5;
  font-size: 14px;
  color: #333;
}
</style>
