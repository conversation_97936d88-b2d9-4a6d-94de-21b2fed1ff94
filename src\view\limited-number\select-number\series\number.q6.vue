<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-04-29 15:03:53
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-11-05 12:28:16
 * @FilePath     : \src\view\limited-number\select-number\series\number.q6.vue
 * @Descripttion : 限量号(Q6)首页
-->
<script src="../index"></script>
<template>
  <div
    :style="{background: `#95a5b4 url(${require('@/assets/limitNumber/q6.bg.jpg')}) 50% ${-pageEleOffsetHeight}px no-repeat`}"
    :class="
      ['limited-number-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : '',
       step === 3 ? 'has-shade' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :background-color="'transparent'"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    >
      <template #right>
        <p
          class="btn"
          @click="$router.push({ name: 'limited-number-rule-note', query: { seriesCode } })"
        >
          规则
        </p>
      </template>
    </header-custom>
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <template v-if="step && step < 5">
        <div
          :class="`limited-number-box code-box code-box-s${step}`"
          :style="{bottom: `${20 + (step === 1 ? pageEleOffsetHeight : 0)}px`}"
          data-flex="dir:top"
        >
          <template v-if="step === 1">
            <div class="explain-box">
              <h3 class="h3">
                请输入您的意向号码
              </h3>
              <p>
                若您的意向号码未被占用，系统将为您自动匹配； 若您的意向号码已被占用，须进入随机选号环节
              </p>
            </div>
          </template>
          <template v-else>
            <div
              class="sign-box"
              v-if="step > 1 && step < 4"
            >
              <div
                class="sign"
              >
                <h3 class="h3">
                  本轮选号还有
                  <van-count-down
                    class="lan-count-down"
                    :time="time"
                    format="HH : mm : ss"
                    auto-start
                    ref="countDown"
                    @finish="handleCountDownFinished"
                  >
                    <template #default="time">
                      <span class="block">{{ time.hours < 10 ? '0' + time.hours : time.hours }}</span>
                      <span class="colon">:</span>
                      <span class="block">{{ time.minutes < 10 ? '0' + time.minutes : time.minutes }}</span>
                      <span class="colon">:</span>
                      <span class="block">{{ time.seconds < 10 ? '0' + time.seconds : time.seconds }}</span>
                    </template>
                  </van-count-down>
                  结束
                </h3>
                <p>
                  {{ numberPool.length === 1 && [2, 3].includes(step) ? '选号时间不超过30分钟，确认选号后将无法更改' : '本轮选号结束前，若不完成选号或不进行换号，系统将在以下号码中自动为您匹配一个限量号。' }}
                </p>
              </div>
            </div>
          </template>
          <div
            v-if="step === 1"
            class="code-input-box"
            data-flex="dir:top cross:top"
            data-block
          >
            <div
              class="code"
              data-flex="main:center cross:center"
            >
              <!-- type 为 number会发有异常 -->
              <input
                readonly
                id="limited-number"
                type="tel"
                pattern="[0-9]*"
                :maxlength="numberLength"
                v-model="limitedNumber"
                :autofocus="false"
                @focus="focused = true"
                @blur="focused = false"
              >
              <label
                for="limited-number"
                class="line"
                v-for="(item,index) in numberLength"
                v-text="numbers[index]"
                :key="index"
                :class="{'animated': focused && cursorIndex === index}"
              />
            </div>
          </div>
          <div
            class="number-pools"
            v-if="step > 1"
          >
            <div
              :class="['number-box', `number-s${step}`]"
            >
              <div
                data-flex="main:center cross:center"
                v-if="!numberPool.length"
              >
                <p
                  class="numbers"
                  style="width:100%"
                />
              </div>
              <div
                :data-flex="`${numberPool.length < 3 ? 'main:center' : 'main:left'} box:wrap`"
                v-else
              >
                <p
                  :class="['numbers', pitchNumber === num || !pitchNumber && index === 0 ? 'pitch-on' : '']"
                  v-for="(num,index) of numberPool"
                  :key="index"
                  @click="handlePitchOnNumber(num)"
                >
                  <span>{{ num }}</span>
                </p>
              </div>
            </div>
          </div>
          <div class="code-tips">
            <template v-if="step === 1">
              <p
                v-if="limitedNumberIllegality"
                class="error"
              >
                需输入001-999范围内的限量号
              </p>
            </template>
            <template v-else>
              <p v-if="step === 2 && numberPool.length === 3">
                您可从限量号中任选一个，也可花费{{ refreshIntegral }}奥金更新备选号
              </p>
              <p v-if="numberPool && numberPool.length < (step === 2 ? 3 : 20)">
                剩余限量号不足，仅有以上限量号供您选择
              </p>
            </template>
          </div>
          <div
            class="button-box"
          >
            <template v-if="step === 1">
              <audi-button
                :text="dispositionBtnText"
                color="black"
                height="56px"
                :class="['black-btn', dispositionBtnEnabled ? 'btn-enabled' : 'btn-un-enabled']"
                @click="handleDispositionBtn"
              />
            </template>
            <div
              data-flex="main:justify"
              v-else
            >
              <audi-button
                :text="dispositionBtnText"
                color="black"
                height="56px"
                style="margin-bottom: 5px; width: calc(50% - 2px);"
                :class="['black-btn', numberPool.length && dispositionBtnEnabled ? 'btn-enabled' : 'btn-un-enabled']"
                @click="handleDispositionBtn"
              />
              <audi-button
                :text="spendingBtnStatus ? '正在选号中...' : `消耗${refreshIntegral}奥金更新备选号`"
                height="56px"
                style="margin-bottom: 5px; width: calc(50% - 2px);"
                :class="['white-btn', spendingBtnStatus || !numberPool.length || !numberNotEnough ? 'btn-un-enabled' : 'btn-enabled']"
                @click="handleIntegralBuyBtn"
              />
            </div>
          </div>
        </div>
        <van-number-keyboard
          v-if="step === 1"
          class="lan-number-keyboard"
          :show="focused"
          v-model="limitedNumber"
          :maxlength="numberLength"
          ref="numberKeyboard"
          @show="handleKeyboardStatus('show')"
          @hide="handleKeyboardStatus('hide')"
        />
      </template>
      <van-popup
        v-model="popShow"
        class="popup-custom"
        :close-on-click-overlay="false"
      >
        <div class="popup-custom-box">
          <div class="popup-custom-main">
            <div class="text align-center">
              {{ popMainText }}
              <p v-if="popMainTips">
                {{ popMainTips }}
              </p>
            </div>
          </div>
          <div
            class="popup-custom-btn"
            :data-flex="popBtnReign.length > 1 ? 'main:justify' : 'dir:top'"
          >
            <audi-button
              v-for="(b,i) of popBtnReign"
              height="56px"
              :key="i"
              :text="b.text"
              :color="b.color"
              :width="popBtnReign.length === 2 ? 'calc(50% - 3px)' : '100%'"
              :class="[`${b.color}-btn`, b.enabled ? 'btn-enabled' : 'btn-un-enabled']"
              @click="handleClickPopupBtn(i)"
            />
          </div>
        </div>
      </van-popup>
      <van-popup
        v-model="popCircle"
        class="popup-custom popup-circle"
        :close-on-click-overlay="false"
        data-flex="main:center"
      >
        <div
          class="popup-custom-box"
          data-flex="cross:center dir:top"
        >
          <van-circle
            v-model="loadingRate"
            :speed="20"
            color="#000"
            layer-color="#f6f6f6"
            size="120px"
            :text="`绑定中...`"
          />
          <p>请您耐心等待</p>
        </div>
      </van-popup>
    </div>
  </div>
</template>
<style lang="less">
.popup-custom {
  width: calc(100vw - 32px);
  &.popup-circle {
    width: 68vw;
    padding: 40px 0 20px;
    p {
      margin-top: 24px;
      font-size: 14px;
    }
  }
  padding: 20px 24px;
  box-sizing: border-box;
  .popup-custom-main {
    padding-top: 15px;
    .text {
      font-size: 16px;
      line-height: 180%;
      &.align-center {
        text-align: center;
      }
      p {
        font-size: 12px;
        color: #666;
        line-height: 24px;
        margin: 6px 0 -12px 0;
      }
    }
  }
  .popup-custom-btn {
    margin-top: 30px;
  }
}
.explain-box {
  color: #fff;
  text-align: center;
  line-height: 20px;
  padding-bottom: 5px;
  .h3 {
    font-size: 14px;
    margin-bottom: 8px;
  }
  p {
    font-size: 12px;
  }
}
.sign-box {
  padding: 18px 18px 0 18px;
  margin-bottom: -10px;
  color: #fff;
  text-align: center;
  .sign {
    line-height: 160%;
    .h3 {
      font-size: 14px;
      margin: 0 0 16px;
    }
    p {
      font-size: 12px;
      margin-top: 0;
    }
  }
  .lan-count-down {
    display: inline-block;
    height: 20px;
    margin: 0 3px;
    vertical-align: text-bottom;
    .block {
      padding: 1px 4px 0;
      margin: 0 3px;
      border-radius: 1px;
      line-height: 20px;
      height: 20px;
      font-weight: 600;
      background-color: rgba(255,255,255,0.63);
      overflow: hidden;
    }
  }
}
.number-pools {
  .number-box {
    text-align: center;
    width: 100%;
    max-height: 280px;
    &.number-s2, &.number-s3 {
      .numbers {
        &::before {
          top: 8px;
          left: -13px;
          bottom: 8px;
          border-left: 1px solid  rgba(#000, .15);
        }
      }
    }
    &.number-s2 {
      height: 66px;
      .numbers {
        width: calc(33.333333% - 24px);
        &:nth-child(3n+1) {
          &::before {
            border-left-color: transparent;
          }
        }
      }
    }
    &.number-s3 {
      padding-bottom: 10px;
      .numbers {
        width: calc(24.999999% - 20px);
        margin: 12px 10px 0;
        font-size: 24px;
        // max-height: 32px;
        &:nth-child(4n+1) {
        &::before {
          border-left-color: transparent;
        }
      }
      }
    }
    .numbers {
      z-index: 2;
      position: relative;
      margin: 12px;
      padding: 4px 0;
      font-weight: bold;
      font-size: 30px;
      max-height: 58px;
      color: #000;
      font-family: 'Audi-WideBold';
      border: 3px solid rgba(#95a5b4, 0);
      box-sizing: border-box;
      &::before, &::after {
        content: '';
        position: absolute;
        transition: all .35s;
      }
      &::after {
        display: block;
        border: 1px solid rgba(#fff, 0);
      }
      &.pitch-on {
        border-color: #95a5b4;
        background-color: rgba(255,255,255,0.25);
        &::after {
          z-index: -2;
          left: -3px;
          bottom: -3px;
          top: -3px;
          right: -3px;
          border-color: rgba(#fff, .9);
        }
      }
    }
  }
}

@media screen and (max-height: 700px) {
  .main-wrapper {
    .sign-box {
      font-size: 14px;
    }
    .code-box {
      &.code-box-s3 {
        padding: 10px 14px 16px;
      }
    }
  }

}

.lan-number-keyboard {
  background-color: #DEDEDE;
  .van-key {
    border-radius: 5px;
    background-color: #fcfcfe;
    color: #000;
    box-shadow: 1px 1px 1px #898A8D;
    &.van-key--active {
      background-color: #e2e2e2;
      color: #333;
    }
  }
  .van-key__wrapper {
    &:nth-last-child(1) {
      .van-key {
        background-color: transparent;
        box-shadow: none
      }
    }
    &:nth-last-child(3) {
      .van-key {
        position: absolute;
        left: -999px;
        display: none;
      }
    }
  }

}
.limited-number-wrapper {
  &.has-shade {
    background-position-y: -200px !important;
    .header-wrapper {
      &::after {
        content: '';
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(180deg, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.1) 100%);
      }
    }
  }
}
</style>
<style lang="less" scoped>
@import url(../../index.less);
html, body {
  font-family: 'Audi-WideBold';
}
.limited-number-wrapper {
  transition: background .5s;
}
.limited-number-box {
  transition: bottom .3s;
}
.page-wrapper {
  background-size: 100% auto !important;
  .main-wrapper {
    position: relative;
    background-size: 90% !important;
  }
}
.button-box, .popup-custom-btn {
  .button {
    transition: all .35s;
  }
  .black-btn {
    &.btn-un-enabled {
      background-color: #e2e2e2 !important;
      border-color: #e2e2e2 !important;
      color: #aaa !important;
    }
  }
  .white-btn {
    background-color: transparent !important;
    &.btn-un-enabled {
      opacity: .5;
    }
  }
}
@media screen and (max-height: 700px) {
  .code-box.code-box-s1 {
    margin-top: 18% !important;
  }
  .main-wrapper {
    background-position-y: 60px !important;
  }
}

.header-wrapper {
  .header-right {
    .btn {
      font-size: 14px;
    }
  }
}
.code-box {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  margin: 16px 0;
  padding: 20px 24px 16px;
  // &.code-box-s1{
  //   margin-top: 25%;
  // }
  .code-tips {
    margin: 12px 0 0;
    font-size: 14px;
    color: #fff;
    min-height: 20px;
    position: relative;
    p {
      margin: 0;
      font-size: 12px;
      line-height: 160%;
      text-align: center;
      &.error {
        color: #F50537;
      }
    }
  }
  .code-input-box {
    text-align: center;
    color: #2e2f33;
    font-size: 16px;
    .code {
      position: relative;
      margin: 0 auto;
      input {
        position: absolute;
        top: -100%;
        left: -9999px;
        opacity: 0;
      }
      .line {
        position: relative;
        margin: 0 7px;
        width: 48px;
        height: 64px;
        line-height: 64px;
        text-align: center;
        font-size: 36px;
        font-weight: bold;
        font-family: 'Audi-WideBold';
        color: #2e2f33;
        background-color: rgba(255,255,255,0.5);
        border: 3px solid #95a5b4;
        cursor: text;
        &::after {
          display: block;
          position: absolute;
          content: "";
          left: -3px;
          bottom: -3px;
          top: -3px;
          right: -3px;
          border: 1px solid #fff;
          background-color: none;
        }
        &.animated::before {
          display: block;
          position: absolute;
          left: 50%;
          top: 35%;
          width: 1px;
          height: 40%;
          content: "";
          background-color: #2e2f33;
          animation-name: coruscate;
          animation-duration: 1s;
          animation-iteration-count: infinite;
          animation-fill-mode: both;
        }
      }
    }
  }
  .button-box {
    padding-top: 12px;
    position: relative;
  }
}


@keyframes coruscate {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
</style>
