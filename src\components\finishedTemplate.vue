<template>
    <div class="finished" v-show="finishedText">
        <img src="@/assets/img/notLine.png" alt="">
        <div>{{ finishedText }}</div>
        <img src="@/assets/img/notLine.png" alt="">
    </div>
</template>

<script>
export default {
    props: {
        finishedText: {
            type: String,
            default: '抵达终点'
        }
    }
}
</script>

<style lang="less" scoped>
.finished {
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    padding-bottom:26px;
    color: #606372;
    font-size: 12px;
    div{
        margin: 0 8px;
        line-height: normal;
    }
    img{
        width: 54px;
        height: 1px;
        &:last-of-type{
            transform: rotate(180deg);
        }
    }
}
</style>
