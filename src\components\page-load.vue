<!-- 页面加载 -->
<template>
  <div>
    <van-pull-refresh
      v-model="downloading"
      success-text="刷新成功"
      pulling-text="下拉刷新"
      loosing-text="释放刷新"
      @refresh="onRefresh"
    >
      <van-list
        v-if="pullupstate"
        v-model="uploading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        offset="10"
      >
        <div :style="minHeight">
          <slot />
        </div>

      </van-list>
      <div
        v-else
        :style="minHeight"
      >
        <slot />
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>

import Vue from 'vue'
import { PullRefresh, List } from 'vant'
import { debounce } from '@/utils'

Vue.use(PullRefresh)
Vue.use(List)
export default {
  name: 'PageLoad',
  data() {
    return {
      downloading: false,
      uploading: false,
      finished: false // 判断是否可以拉取更多的数据  有的话就是true  没有就是stop
    }
  },
  props: {
    minHeight: {
      type: String,
      default: 'min-height: 100vh'
    },
    pulldown: {
      // 是否开启下拉开关
      type: [String, Boolean],
      default: false
    },
    pullup: {
      // 是否开启上拉  stop是 当不需要上拉的时候 会关闭上拉刷新 并显示没有更多了
      type: [String, Boolean],
      default: false
    },
    pullupstate: {
      // 是否需要渲染上拉刷新组件
      type: Boolean,
      default: true
    }
  },
  watch: {
    pulldown(val, oldVal) {
      this.downloading = false
      this.$emit('update:pulldown', false)
    },
    pullup(val, oldVal) {
      if (val === 'stop') {
        this.finished = true
      } else {
        this.finished = false
        this.uploading = this.pullup
      }
    }
  },
  mounted() {},
  methods: {
    // 下拉事件触发
    onRefresh() {
      this.$emit('onRefresh', '')
    },
    onLoad: debounce(function (e) {
      this.$emit('onLoad', '')
    }, 1000)
  }
}
</script>


<style scoped lang="less">
</style>
