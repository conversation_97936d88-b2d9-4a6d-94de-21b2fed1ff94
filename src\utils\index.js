/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-18 16:59:43
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-05-22 15:31:27
 * 通用方法
 */
import AMapLoader from '@amap/amap-jsapi-loader'
import wx from 'weixin-js-sdk'
import dayjs from 'dayjs'
import Bridge from '@/utils/JSbridge.js'
import { wxLocation, openLocation } from '@/utils/wxutil.js'
import store from '@/store'
import V2Car from '@/utils/carBox'
import storage from '@/utils/storage'
import { testToken } from '@/testToken/testToken'
import { FILE_MIME_HEX, AUTOMOBILE } from '@/config/conf.data'
import { saveKfLog, afterServiceScGeo, afterServiceScGeocodeGeo } from '@/api/api'
// 计算两点之间距离

// 获取url参数
export const getQueryString = (name) => {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i')
  const r = window.location.search.substr(1).match(reg)
  if (r != null) {
    return decodeURIComponent(r[2])
  }
  return null
}

// 原生跳转页面
export const nativeCallback = (action, param) => {
  if (isDev) return
  return new Promise((resolve, reject) => {
    console.log(`执行:${action}`)
    window.webViewJavascriptBridge.callHandler(action, param, (err, data) => {
      if (err) {
        console.log(err)
        reject(err)
        return
      }
      // console.log('跳转成功, ' + param.path);
      resolve(data)
    })
  })
}

export const isIos = !!navigator.userAgent.match(
  /\(i[^;]+;( U;)? CPU.+Mac OS X/
)

// 获取url参数
export const getQueryParam = function (name) {
  const val =
    (document.URL.match(new RegExp(`(?:[?#&])${name}=([^&#]*)`, 'i')) ||
      [])[1] || ''
  return decodeURIComponent(val)
}

// 调用原生app的方法
export const callNative = async (name, param,orderInfo = {}) => {


  if(name === 'callPayCenter'){
    await saveKfLog({
      key:"原生支付的接口：callNative('callPayCenter')",
      value:orderInfo,
      source:"audi-order-h5"
    })
  }

  // 模拟非app环境的逻辑
  const { env, token } = getUrlParamObj()
  if (env === 'test') {
    // // 在这里输入测试环境的数据
    // console.log(testToken);
    const testData = {
      token: 'RasIYYJOQsEACwNu_t3dy6SOEf3ZLD7R',
      navigationBarHeight: 50,
      location: '31.285732,121.176221',
      city: '上海市'
    }
    console.warn('当前是开发环境, 模拟app数据:', testData)
    return Promise.resolve(testData)
  }
  // pub代表公共环境,不需要使用token,其他浏览器可正常使用
  if (env === 'pub') {
    const testData = {
      token: '',
      statusBarHeight: 0,
      navigationBarHeight: 50
    }
    console.warn('当前是公共环境, 模拟app数据:', testData)
    return Promise.resolve(testData)
  }
  // 兼容小程序环境
  if (env === 'minip') {
    return new Promise((res) => {
      doMiniPAction(name, param, (data) => {
        console.log('====data====', data)
        res(data)
      })
    })
  }

  return new Promise((res) => {
    //
    try {
      Bridge.webViewJavascriptBridge.callHandler(name, param, (err, data) => {
        if (err) {
          console.error(`call native 返回结果错误, name: ${name},err: ${err}`)
        } else {
          res(data)
        }
      })
    } catch (err) {
      console.error(`native内部错误: nativeName:${name}`, err)
      res({})
    }
  })
}

const doMiniPAction = (name, param, callback) => {
  const { token, refreshToken } = getUrlParamObj()
  if (!token) {
    console.error('当前为小程序环境, url中无token信息')
  }

  const res = {}
  if (name === 'getLocationCity') {
    // res.location = '31.2304,121.4737'
    wxLocation(callback)
  } else {
    switch (name) {
      case 'getAudiUserInfo':
        res.token = token
        res.refreshToken = refreshToken
        break

      case 'navigationBarHeight':
        res.statusBarHeight = 0
        res.navigationBarHeight = 0
        break

      case 'toggleLoading':
        break
      case 'prepage':
        break
      case 'goMinePage':
        break

      case 'audiOpen':
        wx.miniProgram.navigateTo({
          url: `/pages/web/index?url=${encodeURIComponent(param.path)}`,
          success: function () {
            console.log('success')
          },
          fail: function () {
            console.log('fail')
          },
          complete: function () {
            console.log('complete')
          }
        })

        break

      case 'audiShare':
        break

      case 'business':
        switch (param.callFunc.functionName) {
          case 'downloadContract':
            const sampleId = param.callFunc.functionParams.sampleId
            console.log(sampleId)
            break
          default:
            break
        }
        break

      case 'navigationMap':
        openLocation(param, callback)
        break
      case 'callPayCenter':
        break

      case 'ccInfoPage':
        break
      case 'toast':
        break

      case 'openNativeBrowser':
        break
      case 'getParmas':
        break

      case 'bankOpen':
        break
      case 'popup':
        break
      case 'callpayResult':
        res.payType = '当前是小程序环境'
        break

      case 'appCallJs':
        break

      default:
        console.error(`小程序无法识别的 name: ${name}`)
    }
    return callback(res)
  }
}

// 超时设置
export const handleTimeout = (param, time) =>
  new Promise((res) => {
    setTimeout(() => res(param), time)
  })

// 原生app的回调
export const dispatchCallback = (jsonString) => {
  // 这里看参数应该是接受一个json string
  // 但是现在没有联调, 不知道具体业务逻辑
  // Bridge.webViewJavascriptBridge.dispatchMessageFromNative(jsonString)
  // 是否 development 环境
}

export const isDev = process.env.NODE_ENV === 'development'

// 获取url参数
export const getUrlParamObj = () => {
  const url = window.location.href
  // eslint-disable-next-line no-new-object
  const theRequest = new Object()
  // eslint-disable-next-line no-useless-escape
  const strs = url.split(/[?&#\/]/)
  strs.forEach((e) => {
    if (e.indexOf('=') > -1) {
      const splitArr = e.split('=')
      theRequest[splitArr[0]] = splitArr[1]
    }
  })
  return theRequest
}

export const Obj2UrlParam = (obj) => {
  const search = new URLSearchParams()
  for (const key in obj) {
    search.set(key, obj[key])
  }
  return search.toString()
}

// 防抖
export const debounce = function (func, time) {
  let timeout
  return function (event) {
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.call(this, event)
    }, time)
  }
}

/**
 * 证件的图片压缩  把压缩改了一下
 * @param item
 * @return file
 */
export const compressCertificationImg = (item) => {
  const img = new Image()
  // 缩放图片需要的canvas
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')

  // 最大长宽限制
  img.src = item.content

  return new Promise((resolve) => {
    img.onload = () => {
      // 图片原始尺寸
      const originWidth = img.width
      const originHeight = img.height

      let targetWidth = originWidth
      let targetHeight = 2000 * (originHeight / originWidth)

      // canvas对图片进行缩放
      canvas.width = targetWidth
      canvas.height = targetHeight
      // 清除画布
      context.clearRect(0, 0, targetWidth, targetHeight)
      // 图片压缩
      context.drawImage(img, 0, 0, targetWidth, targetHeight)

      // [0].file.name
      canvas.toBlob((blob) => {
        const file = new File([blob], item.file.name, { type: item.file.type })
        resolve(file)
      }, item.file.type)
    }
  })
}

/**
 * 图片压缩 (主要通过长宽的限制来处理)
 * @param item  vant  v-model array 的子项
 * @return file
 */
export const compressFileImg = (item) => {
  const img = new Image()
  // 缩放图片需要的canvas
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')

  // 最大长宽限制
  const maxWidth = 1000
  const maxHeight = 1000
  img.src = item.content

  return new Promise((resolve) => {
    img.onload = () => {
      // 图片原始尺寸
      const originWidth = img.width
      const originHeight = img.height

      let targetWidth = originWidth
      let targetHeight = originHeight

      if (originWidth > maxWidth || originHeight > maxHeight) {
        if (originWidth / originHeight > maxWidth / maxHeight) {
          targetWidth = maxWidth
          targetHeight = Math.round(maxWidth * (originHeight / originWidth))
        } else {
          targetHeight = maxHeight
          targetWidth = Math.round(maxHeight * (originWidth / originHeight))
        }
      }

      // canvas对图片进行缩放
      canvas.width = targetWidth
      canvas.height = targetHeight
      // 清除画布
      context.clearRect(0, 0, targetWidth, targetHeight)
      // 图片压缩
      context.drawImage(img, 0, 0, targetWidth, targetHeight)

      // [0].file.name
      canvas.toBlob((blob) => {
        const file = new File([blob], item.file.name, { type: item.file.type })
        resolve(file)
      }, item.file.type)
    }
  })
}
// 高德地图api  距离计算
export const getdiscount = (lat1, lng1, lat2, lng2) =>
  new Promise((resolve) => {
    AMapLoader.load({
      key: '********************************',
      plugins: [], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      Loca: {} // 是否加载 Loca， 缺省不加载
    })
      .then((AMap) => {
        const p1 = [lat1, lng1]
        const p2 = [lat2, lng2]
        const dis = AMap.GeometryUtil.distance(p1, p2)
        console.log('两点距离(米)', dis)
        resolve(dis)
      })
      .catch((e) => {
        console.error('getdiscount', e)
      })
  })
// 地址逆解析
export const getLocation = async (coordinate) =>
  new Promise((resolve) => {
    afterServiceScGeo({
      location: (coordinate[0] + ',' + coordinate[1]),
      extensions: 'all'
    }).then(res => {
      let adcode = res.data.data.data.regeocode.addressComponent.adcode
      adcode = `${adcode.slice(0, adcode.length - 2)}00`
      console.log('cityCode:', adcode)
      resolve(adcode)
    }).catch(e => {
      resolve('')
    })
  })
// 逆向地理编码方法 获取完整地址
export const getLocationAddress = async (coordinate) =>
  new Promise((resolve) => {
    afterServiceScGeo({
      location: (coordinate[0] + ',' + coordinate[1]),
      extensions: 'all'
    }).then(res => {
      console.info('🚀 ~ file:index method: line:398 -----', res.data.data.data)
      resolve(res.data.data.data.regeocode.formatted_address)
    }).catch(e => {
      console.info('🚀 ~ file:index method: line:401 -----', e)
      resolve('')
    })
  })
export const getLocationCityName = async (coordinate) =>
  new Promise((resolve) => {
    afterServiceScGeo({
      location: (coordinate[0] + ',' + coordinate[1]),
      extensions: 'all'
    }).then(res => {
      resolve(res.data.data.data.regeocode.addressComponent.city || res.data.data.data.regeocode.addressComponent.province)
    }).catch(e => {
      resolve('')
    })
  })

export const getLocationProvince = async (coordinate) =>
  new Promise((resolve) => {
    afterServiceScGeo({
      location: (coordinate[0] + ',' + coordinate[1]),
      extensions: 'all'
    }).then(res => {
      resolve(res.data.data.data)
    }).catch(e => {
      resolve('')
    })
  })

// *暂无使用
export const getLocationCoordinate = async ({ address = '', city = '' }) =>
  new Promise((resolve) => {
    afterServiceScGeocodeGeo({
      address: address
    }).then(res => {
      resolve(res.data.data.data)
    }).catch(e => {
      resolve('')
    })
  })

export const getdiscount2 = async (lat1, lng1, lat2, lng2) =>
  new Promise((resolve) => {
    const p1 = [lat1, lng1]
    const p2 = [lat2, lng2]
    const dis = AMap.GeometryUtil.distance(p1, p2)
    console.log('两点距离(米)', p1, p2, dis)
    resolve(dis)
  })

export const arraySortByKeys = function (data, keys = []) {
  // keys可以传一个数组
  const c = []
  const d = {}
  for (const element of data) {
    let elementKeyStr = ''
    const elementKey = []
    const elementKeyObj = {}
    for (const key of keys) {
      elementKey.push(element[key])
      elementKeyObj[key] = element[key]
    }
    elementKeyStr = elementKey.join('_')
    if (!d[elementKeyStr]) {
      c.push({
        ...elementKeyObj,
        children: [{ ...element, show: true }]
      })
      d[elementKeyStr] = { ...element, show: true }
    } else {
      for (const ele of c) {
        const isTrue = keys.some((key) => ele[key] !== element[key])
        if (!isTrue) {
          ele.children.push({ ...element, show: true })
        }
      }
    }
  }
  return c
}

export const InsertUrlShearPlate = function (text) {
  const copyString = text
  // 创建input标签存放需要复制的文字
  const myInput = document.createElement('input')
  // 把文字放进input中，供复制
  myInput.value = copyString
  document.body.appendChild(myInput)
  // 选中创建的input
  myInput.select()
  // 执行复制方法， 该方法返回bool类型的结果，告诉我们是否复制成功
  const copyResult = document.execCommand('copy')
  // 操作中完成后 从Dom中删除创建的input
  document.body.removeChild(myInput)
  return copyResult
}

export const getChannel = (channel) => {
  const { env } = getUrlParamObj()
  const isMinip = env === 'minip' ? 'minip' : 'oneapp'
  return isMinip
}
// 判断是否是2.0T ,半定制车,参数可以是modelLineId或modelLineCode
export const checkV2 = () => {
  // 如果2.0T配置线没有库存，执行全配置操作
  // if (!store.state.hasV2Car) {
  //   return false
  // }
  const b = storage.getPlus('semi-definite')
  if (b == '个性定制') {
    return true
  }
  if (b == '私人高定') {
    return false
  }

  // const l = V2Car.length
  // for (let i = 0; i < l; i++) {
  //   if (V2Car[i].modelLineCode === str || V2Car[i].modelLineId === str) { return true }
  // }
  // return false
}

// 获得当前日期在当月第几周
// a: 年 b: 月 c: 日
export const getMonthWeek = (a, b, c) => {
  const date = new Date(a, parseInt(b) - 1, c)
  const w = date.getDay()
  const d = date.getDate()
  return Math.ceil((d + 6 - w) / 7)
}

// 在等待指定时间后调用（睡眠）
export const delay = (fn, wait, ...args) => setTimeout(fn, wait, ...args)
export const sleep = (delay) =>
  new Promise((resolve) => setTimeout(resolve, delay))
// 数据类型检测
export const checkType = (data) =>
  Object.prototype.toString.call(data).match(/\s+(\w+)/)[1]

// 小写数字转换成大写,[0 ~ 99]
export const numberToUppercase = (num) => {
  num = Number(num)
  const upperCaseNumber = [
    '零',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
    '十',
    '百',
    '千',
    '万',
    '亿'
  ]
  const length = String(num).length
  if (length === 1) {
    return upperCaseNumber[num]
  }
  if (length === 2) {
    if (num === 10) {
      return upperCaseNumber[num]
    }
    if (num > 10 && num < 20) {
      return `十${upperCaseNumber[String(num).charAt(1)]}`
    }
    return `${upperCaseNumber[String(num).charAt(0)]}十${upperCaseNumber[
      String(num).charAt(1)
    ].replace('零', '')}`
  }
}

/* 参数(滤重)处理(严格)
 * // ! 防止参数传输(重复)导致(数据解耦)异常
 * cover [pop、shift]
 * params {}
 */
export const paramsStrict = (params, cover = 'pop') => {
  Object.keys(params).forEach((i) => {
    if (checkType(params[i]) === 'Array') {
      const query = [...params[i]]
      params[i] = query[['pop', 'shift'].includes(cover) ? cover : 'pop']()
    }
  })
  return params
}

/* 拼接字符串
 */
export const combiningStrings = (string, prefix = '/') =>
  string ? prefix + string : string

//
/* 计算车型图（cc） 仅限于 /audi-car-config/api/v1/cc/detail 接口使用
 * autoHubCode 轮毂编码，outsideColorCode 外饰编码，modelLineCode车型编码
 * return String
 */
export const getCalcCMTImages = (configDetail) => {
  const {
    carModel: { modelLineCode },
    optionList,
    outsideColor: { colorCode: outsideColorCode },
    carSeries: { seriesCode }
  } = configDetail
  if (modelLineCode && outsideColorCode && optionList?.length && seriesCode) {
    const [hubs] = optionList.filter(
      (option) => option.optionClassification === 'RAD'
    ) || [{ optionCode: '' }]
    const { optionCode: autoHubCode } = hubs || {}
    if (autoHubCode && hubs?.optionCode) {
      const [{ seriesPathName }] = AUTOMOBILE.filter(
        (a) => a.seriesCode === seriesCode
      ) || [{ seriesPathName: '' }]
      const pinyinSeriesCode = seriesPathName.toLowerCase()
      return `/ccpro-backend/${pinyinSeriesCode}/carImages/${modelLineCode}/${outsideColorCode}/${autoHubCode}/Front45.png`
      // return `/ccpro-backend/storebest/${modelLineCode}_${autoHubCode}_${outsideColorCode}.png`
    }
  }
  return ''
}

export const backFromCrossDomain = (
  from = '',
  path = '',
  param = '',
  sign = 'native'
) => {
  if (sign === 'native') {
    const url =
      param?.indexOf("'") !== -1 ? param?.replace(/'/g, '').split(',') : ''
    const params = url
      ? url.reduce((o, l) => {
          const [key, val] = l.split('=')
          return { ...o, ...{ [key]: val } }
        }, {})
      : {}
    callNative('openRoutePath', { path, params })
  }
}

export const backFromUseDomain = ({ from, name, path, param, sign, times }) => {
  const url =
    param?.indexOf("'") !== -1 ? param?.replace(/'/g, '').split(',') : ''
  const params = url
    ? url.reduce((o, l) => {
        const [key, val] = l.split('=')
        return { ...o, ...{ [key]: val } }
      }, {})
    : {}
  if (sign === 'native') {
    if (+times) {
      callNative('prepage', { times: +times })
    } else if (path) {
      callNative('openRoutePath', { path, params })
    }
  } else {
    return { path, params, name }
  }
}

export const getFileMIME = (file) =>
  new Promise((resolve) => {
    const reader = new FileReader()
    reader.readAsArrayBuffer(file)
    reader.onload = (e) => {
      const typeHex = new Uint8Array(reader.result)
        .subarray(0, 4)
        .reduce((str, index) => str + index.toString(16), '')
        .toLowerCase()
      resolve(typeHex ? FILE_MIME_HEX[typeHex] : '')
    }
  })

export const downloadFile = async (file, name, type) => {
  let mimeType = ''
  if (!type) {
    mimeType = await getFileMIME(file)
  }

  console.log(
    '%c [ mimeType ]-589',
    'font-size:14px; background:#cf222e; color:#fff;',
    mimeType
  )
  const BlobFile = new Blob([file], {
    type: `application/${mimeType};charset=utf-8`
  })
  const link = document.createElement('a')
  const time = dayjs().format('YYYY-MM-DD-HHmmss')
  if (window.navigator && window.navigator.meSaveOrOpenBlob) {
    window.navigator.meSaveOrOpenBlob(file, `${name}_${time}_${mimeType}`)
  } else {
    const objectUrl = window.URL.createObjectURL(BlobFile)
    link.href = objectUrl
    link.download = `${name}_${time}_`
    link.click()
    window.URL.revokeObjectURL(objectUrl)
  }
}

// 个性订制(半定) minip ?  'MINIP_MEASURE' :'ONEAPP_MEASURE'
// 私人高定(高定) minip ?  'MINIP_PERSONAL' :'ONEAPP_PERSONAL'
// 爆款推荐  minip ? 'MINIP_RECOMMEND' : 'ONEAPP_RECOMMEND'
// 'ONEAPP_ESTIMATE' ?? 不确定什么情况下用到
export const getEntryPoint = () => {
  const env = getQueryString('env')
  const entryPoint = env === 'minip' ? 'MINIP_RECOMMEND' : 'ONEAPP_RECOMMEND'
  return entryPoint
}

// 判断空对象
export const isEmptyObj = (obj) => obj === undefined || obj === null || Object.keys(obj).length === 0

// 判断对象是否包含键，支持：Map.has(key), Object.prototype.hasOwnProperty.call(target, key), Set.has(key)
export const hasKey = (target, key) => {
  if (target instanceof Map || target instanceof WeakMap) {
    return target.has(key);
  }
  if (target instanceof Set || target instanceof WeakSet) {
    return target.has(key);
  }
  if (typeof target === 'object' && target !== null) {
    return Object.prototype.hasOwnProperty.call(target, key);
  }
  return false;
}

// 树结构统一
export const addNameToTree = (tree, fields) => {
  if (Array.isArray(tree) && Object.keys(fields)?.length) {
    const { value, text, children } = fields
    tree.forEach((node) => {
      const key = Object.keys(node)
      if (value && value !== 'value' && key.includes(value))
        node.value = node[value]
      if (text && text !== 'text' && key.includes(text)) node.text = node[text]
      if (children && children !== 'children' && key.includes(children)) {
        node.children = node[children]
        delete node[children]
      }
      if (Array.isArray(node.children)) addNameToTree(node.children, fields)
    })
  }
  return tree
}
