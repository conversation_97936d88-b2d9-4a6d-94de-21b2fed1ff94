<template>
  <div class="unsubscribeSucceed">
    <div
      class="nav-box"
      :style="{ 'padding-top': statusBarHeight + 'px', 'height':statusBarHeight + navigationBarHeight+'px'}"
    >
      <div
        class="nav-content"
        :style="{'height': + navigationBarHeight+'px'}"
      >
        <div
          @click="routeBack"
          class="nav-back-box"
        >
          <img
            class="nav-back"
            src="../../assets/img/back_white.png"
          >
        </div>
        <div class="nav-title">
          {{ $store.state.title }}
        </div>
      </div>
    </div>
    <div class="title-bold">
      您只需满足以下条件，即可申请购买上汽奥迪免税车型
    </div>
    <p style="margin-bottom:24px">
      以学习和进修为目的，在国外、港澳地区正规大学（学院）或科研机构求学、攻读学位或从事科学研究或学术交流一学年以上（留学期间实际在外总时间达到270天以上）。<br>
      2、完成第1条所述学业后两年内回国。<br>
      3、毕业后首次入境一年内至海关提交材料申报
      <span>注：实际资格认证标准与可享优惠以国家最新公布政策为准。</span>
    </p>

    <!-- <div class="title-bold">
      上汽奥迪留学生专享权益
    </div>
    <p>
      1、尊享金融方案。<br>
      2、免税车保险礼遇。<br>
      3、专享车辆资源。<br>
      4、叠加享受车型购车权益。<br>
      5、完备的售前售后服务。
    </p> -->
    <div class="title-bold">
      上汽奥迪留学生购车流程
    </div>
    <p  style="margin-bottom:24px">
      1、办理《回国人员购买国产汽车准购单》。<br>
      2、下载上汽奥迪APP，提交个人资料并完成资质审核。<br>
      3、收到站内信通过审核后完成下定。<br>
      4、小订后收到抵扣券，金额确认无误后完成大定。<br>
      5、车辆到店，验车并支付全款。<br>
      6、交付。
    </p>

    <div class="title-bold">
      如您有任何疑问，可通过以下方式联系我们：
    </div>
    <!-- <p> -->
      <span style="padding-left:16px;margin-top:8px;">021-695-56182</span>
    <!-- </p> -->
    <div class="checkbox-agreement">
      <van-checkbox
        v-model="checked"
        shape="square"
      /> <span style="margin-left:7px">我已阅读并同意</span><span
        style="color:#edeeef"
        @click="agreement"
      >《留学生授权声明》</span>
    </div>
    <div
      class="buttons"
      @click="onCreate"
    >
      立即认证
    </div>
  </div>
</template>

<script>
import Vue from 'vue'

import {
  Toast,
  Checkbox
} from 'vant'
import { callNative } from '@/utils'

Vue.use(Checkbox)
  .use(Toast)
export default {
  data() {
    return {
      identityCardFront: '',
      statusBarHeight: 0,
      navigationBarHeight: 50,
      checked: false
    }
  },
  created() {
    this.getHeight()
  },
  mounted() {
  },
  methods: {

    // 获取导航栏高度??
    async getHeight() {
      const data = await callNative('navigationBarHeight', {})
      this.statusBarHeight = data.statusBarHeight
      this.navigationBarHeight = data.navigationBarHeight
    },
    onCreate() {
      if (this.checked) {
        this.$router.push({
          path: '/dutyfreecar/data-submit'
        })
      } else {
        callNative('toast', { type: 'fail', message: '请阅读并同意《留学生授权声明》！' })
      }
    },

    agreement() {
      this.$router.push({
        name: 'dutyfreecar-agreement'
      })
    },
    async routeBack() {
      const data = await callNative('prepage', { times: 1 })
    }
  }
}
</script>

<style scoped lang="less">
.unsubscribeSucceed {
  background-image:url('../../assets/img/charging-authentication-img.png');
  background-size:100% 100%;
  background-repeat: no-repeat;
  background-color:#0a1920;
  padding-bottom:50px;
  .title-bold {
    font-size: 14px;
    line-height: 24px;
    color: #FFFFFF;
    font-weight: normal;
    font-family: "Audi-WideBold";
    padding-left: 16px;
    padding-right: 16px;
  }
  p {
    padding-left: 16px;
    padding-right: 16px;
    font-size: 14px;
    color:#dfe2e4;
    line-height:22px;
    // margin:4px 0;
  }
  span{
    display:inline-block;
    font-size: 12px;
    color:#9ca7ab;
  }

  .item-layout {
    display: flex;
    padding-left: 16px;
    align-items: center;

    .title {
      font-size: 12px;
      text-align: center;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      color: #666;
      background: #ffffff;
      border-radius: 50%;
      border: 1px solid #666666;
    }
    .content {
      padding-left: 10px;
      font-size: 14px;
      color: #666;
      font-weight: normal;
    }
  }
  .dashed {
    width: 26px;
    height: 18px;
    border-right: 1px dashed #dddddd;
  }
}

.nav-box {
    display: flex;
    width: 100%;
    text-align: center;
    margin-bottom:131px;
    .nav-content {
      display: flex;
      align-items: center;
      position: relative;
      width: 100vw;
    }

    .nav-back-box {
      width: 10vw;
      position: absolute;
      left: 0;
      bottom: 0;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1025;
    }
    .nav-back {
      width: 20px;
      height: 20px;
      margin-left: 10px;
    }
    .nav-title {
      font-size: 18px;
      font-weight: 500;
      line-height: 20px;
      width: 100vw;
      position: absolute;
      bottom: 0;
      overflow: hidden;
      text-align: center;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
    }
  }

  .checkbox-agreement{
    display:flex;
    padding-left: 16px;
    align-items: flex-start;
    margin-top: 24px;
  }

  .buttons{
    margin-top:41px;
    margin-left:16px;
    border-radius: 0;
            border:1px solid #FFFFFF;
            width: 90%;
            height: 56px;
            text-align: center;
            line-height: 56px;
            font-size:16px;
            color:#FFFFFF;
  }
/deep/ .van-checkbox__icon{
  width:16px;
  height: 16px;
}

  /deep/ .van-checkbox__icon--checked .van-icon{
  background-color:#ffffff !important;
  color:#000000;
  width:16px;
  height: 16px;
  line-height: 16px;
  border:none;
}

/deep/ .van-checkbox__icon .van-icon{
  width:16px;
  height: 16px;
}
</style>
