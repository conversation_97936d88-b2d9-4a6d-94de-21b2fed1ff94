<!-- 弹出框 -->
<template>
  <div>
    <div class="config-wrapper">
      <div v-if="0" class="c-flex-between padding">
        <div>车辆配置</div>
        <div class="c-flex-center small-font" @click="showCarConfigDetail = !showCarConfigDetail">
          查看全部
          <van-icon :name="showCarConfigDetail ? 'arrow-up' : 'arrow-down'" />
        </div>
      </div>
      <div class="c-flex-between ">
        <div class="c-flex-center">
          <div class="sc-left" style="height: 56px;width: 56px;">
            <img style="width: 100%;
              vertical-align: middle;
              object-fit: cover;
              height: 100%;" :src="page2dCarList[4] | imgFix(100)" alt="" @click="imagePreview(page2dCarList[4])">
          </div>
          <div class="sc-height">
            <div class="f14" style="width: 160px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
              {{ currentModelLineData.modelLineName }}
            </div>
            <div class="small-font" style="padding-top: 8px;" v-if="$route.query.idx == 2">
              {{ currentModelLineData.price | formatPrice }}
            </div>
            <div v-else class="small-font">
              ¥{{ currentModelLineData.price | formatPrice }}
            </div>
          </div>
        </div>

        <div class=" small-font sc-nowrap" style="padding-top: 15px;" @click="toConfigTable">
          查看参数
          <van-icon name="arrow" />
        </div>
      </div>


      <!-- 配置组件 -->
      <div>
        <div class="config-item-wrapper" v-show="true">

          <!-- //外观色 -->
          <div class="config-item c-flex-center">
            <div class="sc-left">
              <img :src="excolorImg | imgFix(100)" alt="" @click="imagePreview(excolorImg)">
            </div>
            <div class="sc-height">
              <div class="f14"> {{ currentExterior.externalFeatureNameZh }} </div>
              <div class="small-font">
                <!-- {{ currentExterior.featurePrice | finalFormatPriceDesc }} -->
                <span class="relative" :class="{ 'line-through':discountTagVisible }">
               {{ currentExterior.featurePrice | finalFormatPriceDesc }}  &nbsp;
            </span><span class="balcks" v-show="discountTagVisible">￥0</span>
            <span v-if="discountTagVisible" class="presale-tag">预售权益</span>
                <!-- <span
                  v-if="A7L_INTELLIGENT_AUDIO.optionCode.every(code => standardConfigData.map(i => i.optionCode).includes(code)) && item.optionCode === A7L_INTELLIGENT_AUDIO.optionCode[1]"
                  class="down-price">
                  ¥{{ A7L_INTELLIGENT_AUDIO.price9wp | formatPrice }}
                </span> -->
              </div>
            </div>
          </div>

          <!-- 内饰面料 -->
          <div class="config-item c-flex-center">
            <div class="sc-left">
              <img :src="sibImg | imgFix(100)" alt="" @click="imagePreview(sibImg)">
            </div>
            <div class="sc-height">
              <div class="f14"> {{ currentSib.externalFeatureNameZh }} </div>
              <div class="small-font">
                {{ currentSib.featurePrice | finalFormatPriceDesc }}
                <!-- <span
                  v-if="A7L_INTELLIGENT_AUDIO.optionCode.every(code => standardConfigData.map(i => i.optionCode).includes(code)) && item.optionCode === A7L_INTELLIGENT_AUDIO.optionCode[1]"
                  class="down-price">
                  ¥{{ A7L_INTELLIGENT_AUDIO.price9wp | formatPrice }}
                </span> -->
              </div>
            </div>
          </div>

          <!-- 饰板 -->
          <div class="config-item c-flex-center">
            <div class="sc-left">
              <img :src="eihImg | imgFix(100)" alt="" @click="imagePreview(eihImg)">
            </div>
            <div class="sc-height">
              <div class="f14"> {{ currentEih.externalFeatureNameZh }} </div>
              <div class="small-font">
                {{ currentEih.featurePrice | finalFormatPriceDesc }}
              </div>
            </div>
          </div>

          <!-- 轮毂 -->
          <div class="config-item c-flex-center">
            <div class="sc-left">
              <img :src="hubImg | imgFix(100)" alt="" @click="imagePreview(hubImg)">
            </div>
            <!-- <div class="sc-height"> -->
            <div>
              <div class="f14"> {{ currentHub.packet ? currentHub.packet.labelValueNameZh : currentHub.externalFeatureNameZh }} </div>
              <!-- <div class="small-font">
                {{ currentHub.featurePrice | finalFormatPriceDesc }}
              </div> -->
              <template v-if="discountHubVisible(currentHub)">
                <span class="desc c-lh20 line-through">{{ (currentHub.packet ? currentHub.packet.featurePrice : currentHub.price) | finalFormatPriceDesc }}</span>
                <span class="desc balcks">￥{{ getHubDiscountPrice  | formatPrice }}</span> <span class="presale-tag">预售权益</span>
              </template>
              <template v-else>
                <span class="desc c-lh20">{{ (currentHub.packet ? currentHub.packet.featurePrice : currentHub.price) | finalFormatPriceDesc }}</span>
              </template>
            </div>
          </div>
        </div>
        <template v-if="selectedOptions && Array.isArray(selectedOptions) && selectedOptions.length > 0">
          <div class="titleBox">
            选装
          </div>
          <div class="config-item-wrapper">
            <div class="config-item c-flex-center " v-for="(item, index) in selectedOptions" :key="index">
              <div class="sc-left">
                <img :src="item.materialList?.[0]?.materialUrl | imgFix(100)" alt="" @click="imagePreview(hubImg)">
              </div>
              <div>
                <div class="f14">
                  {{ item.externalFeatureNameZh || item.labelValueNameZh }}
                </div>
                <template v-if="item.equipmentRights && item.equipmentRights == 1 && isCouponValid">
                  <span class="desc c-lh20 line-through"> {{ item.featurePrice | finalFormatPriceDesc }}</span>
                  <span class="desc balcks">￥0</span> <span class="presale-tag">预售权益</span>
                </template>
                <template v-else>
                  <span class="desc c-lh20">{{ item.featurePrice | finalFormatPriceDesc }}</span>
                </template>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { ImagePreview, Popup } from 'vant'
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import confiUrl from '@/config/url'
import { isEmptyObj } from "@/utils";
import { XIAN_XING_VERSION, XIAN_JIAN_VERSION, XIAN_XING_VERSION_Q5 } from '@/config/constant'
import { getOmdGetlabel } from "@/configratorApi";

const OSS_URL = confiUrl.BaseConfigrationOssHost

Vue.use(Popup)
export default {

  data() {
    return {
      showCarConfigDetail: true,
    }
  },
  computed: {
    ...mapGetters([
      'currentSeriesName',
      'page2dCarList',
      'pageOutColorArray',
      'pageHubArray',
      'currentCarType',
      'getHubDiscountPrice'
    ]),
    ...mapState({
      userCoupon: (state) => state.configration.userCoupon,
      // carHeadImageUrl: (state) => (state.carDetail.configDetail?.carModel?.headImageUrl ? baseOssHost + state.carDetail.configDetail?.carModel?.headImageUrl : ''),
      // carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn, // 车辆名称
      // carImgUrl: (state) => (state.carDetail.configDetail?.carModel?.imageUrl ? baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl : ''), // 主图
      totalPrice: (state) => state.carDetail.configDetail?.totalPrice,
      // carSeries: (state) => state.carSeries,

      modelUnicode: (state) => state.configration.currentModelLineData.modelUnicode,
      modellineId: (state) => state.configration.currentModelLineData.cc.modellineId,
      modelLineCode: (state) => state.configration.currentModelLineData.cc.modelLineCode,

      carHeadImage: (state) => state.configration.page2dCarList[0], // 车辆组合45度图
      // carModelName: (state) => state.configration.currentModelLineData.modelLineName, // 车辆名称
      currentModelLineData: (state) => state.configration.currentModelLineData.cc, // 车辆价格
      currentExterior: (state) => state.configration.currentExterior, // 当前外观颜色数据
      currentSib: (state) => state.configration.currentII, // 当前内饰颜色数据
      currentEih: (state) => state.configration.currentEih, // 当前饰板数据
      currentHub: (state) => state.configration.currentHub, // 当前轮毂数据
      selectedOptions: (state) => state.configration.selectedOptions, // 当前选装包数据
      currentVersionMap: (state) => state.configration.currentVersionMap,
    }),
    discountTagVisible() {
      console.log(this.userCoupon,'this.userCoupon');
      const currentExterior = this.currentExterior; // 这里通过 this 访问 mapState 映射的属性
      // 条件：currentExterior存在 + equipmentRights为1 + 优惠券有效
      return currentExterior && currentExterior.equipmentRights === "1" && this.isCouponValid;
    },
    isCouponValid() {
      if (!this.userCoupon) {
        return false;
      }
      // 获取两个字段的值
      const { activityCode, mpEquityNo } = this.userCoupon;

      // 检查两个字段是否都存在且有实际值（非空字符串、非null、非undefined）
      return Boolean(
        activityCode !== undefined &&
        activityCode !== null &&
        activityCode !== "" &&
        mpEquityNo !== undefined &&
        mpEquityNo !== null &&
        mpEquityNo !== ""
      );
    },
    excolorImg() {
      return this.currentExterior?.materialList?.[0]?.materialUrl
    },
    sibImg() {
      return this.currentSib?.packet?.materialList?.[0]?.materialUrl || this.currentSib?.materialList?.[0]?.materialUrl
    },
    eihImg() {
      return this.currentEih?.materialList?.[0]?.materialUrl
    },
    hubImg() {
      return this.currentHub?.materialList?.[0]?.materialUrl
    },
    serverName() {
      if (this.carSeries?.seriesCode === 'G4') {
        if (this.modelLineCode === XIAN_XING_VERSION_Q5) {
          return '艺领权益服务包'
        }
        return '艺创权益服务包'
      }
      if (this.modelLineCode === XIAN_XING_VERSION || this.modelLineCode === XIAN_JIAN_VERSION) {
        return '领尊权益服务包'
      }
      return '尊享权益服务包'
    }
  },
  watch: {},
  async mounted() {
    if(this.selectedOptions && Array.isArray(this.selectedOptions) && this.selectedOptions.length > 0) {
      for (const item of this.selectedOptions) {
        if(item.labelCode === "PACKET") {
          const label = await getOmdGetlabel(item.featureCode, this.modelUnicode);
          console.log("%c A5L label:", "font-size:16px;color:green;",  label);
          this.$set(item, "materialList", label?.data?.data?.result?.children[0]?.materialList);
          console.log("%c A5L item.materialList:", "font-size:16px;color:green;",  item.materialList);
        }
      }
    }
  },

  methods: {
    discountHubVisible(item) {
      if (!this.isCouponValid) {
        return false;
      }
      // 轮毂有权益
      if (item.equipmentRights == "1") {
        return true;
      }

      // 卡钳有权益
      if (!isEmptyObj(item.packet)) {
        for (const children of item.packet.labelChildren) {
          const radOrBahOrBav = this.currentVersionMap.get(children.featureCode);
          if (radOrBahOrBav.equipmentRights === "1") {
            return true;
          }
        }
      }
      return false;
    },
    imagePreview(url) {
      ImagePreview([url])
    },
    isNumber(value) {
      return typeof value === 'number' && !isNaN(value)
    },
    toConfigTable() {
      // console.log(this.$store.state.carDetail)
      // this.$emit('dataTrack', '查看参数')
      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: this.currentModelLineData.modelLineId,
          carModelName: this.currentModelLineData.modelLineName,
          seriesName: this.currentModelLineData.customSeriesCode,
          from: 0,
          customBack: 'newConfigration'
        }
      })
    },
    toEquity() {
      const configDetail = this.$store.state.carDetail.configDetail
      let caseCode = ''
      configDetail?.optionList && configDetail?.optionList.forEach((e) => {
        if (e.optionCode == 'YEG' || e.optionCode == 'YEA') {
          caseCode = e.optionCode
        }
      })
      this.$router.push({
        path: '/theEquity',
        query: {
          modelLineCode: this.modelLineCode,
          caseCode: caseCode || '',
          orderId: this.$route.query?.orderId || '',
          seriesName: this.carSeries.seriesCode,
          modelVersion: this.carSeries.modelVersion
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";

@leftWith: 18vw;
@rightMargin: 15px;
@topMargin: 20px;
@fontColor: #999;

//向上的边距
.sc-m-top {
  margin-top: @topMargin;
}

.sc-left {
  // width: @leftWith;
  margin-right: @rightMargin;
  height: 56px;
  width: 56px;
}

//box阴影
.sc-shadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
}

// 下划线
.sc-u-line {
  border-bottom: 1px solid #e5e5e5;
}


.f14 {
  font-size: 14px;
}

.small-font {
  .c-font12;
  color: #999;
}

.container {
  .sc-m-top;
  position: relative;
  padding: 0 18px;
}

.hint-wrapper {
  background-color: #f2f2f2;
  font-size: 14px;
  padding: 10px 18px;
}

.sc-nowrap {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
}

.sc-height {
  height: auto !important;
  .c-flex-between;
  flex-direction: column;
  flex: 1;
  height: @leftWith;
  padding: 14px 0;
  box-sizing: border-box;
}

// 我的配置
.config-wrapper {
  // .sc-shadow;
  padding: 0 0 0 16px;
  margin-top: 15px;

  >.padding {
    .sc-u-line;
    padding: 10px 0;
  }

  .config-item {
    position: relative;
    padding: 12px 0;
    height: @leftWith;
  }
}

// 客户权益
.client-wrapper {
  .sc-shadow;

  margin-top: 20px;
  padding: 5px 20px;
  border-bottom: 1px solid #e5e5e5;

  >.title {
    padding: 5px 0;
    margin-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
  }
}

@leftWith: 14.5vw;

.f14 {
  font-size: 14px;
  line-height: 14px;
  max-width: 265px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.config-item-wrapper {
  position: relative;
}

.titleBox {
  margin-left: -16px;
  height: 26px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 26px;
}


//向上的边距
.sc-m-top {
  margin-top: @topMargin;
}

.sc-left {
  width: 14.5vw;
  margin-right: @rightMargin;

  img {
    height: 56px;
    width: 56px;
  }
}

.small-font {
  .c-font12;
  color: #999;
  padding-top: 8px;

  >.down-price {
    .c-font10;
    color: #ccc;
    margin-left: 6px;
    text-decoration: line-through;
  }
}

.container {
  .sc-m-top;
  position: relative;
  padding: 0 18px;
}

.hint-wrapper {
  background-color: #f2f2f2;
  font-size: 14px;
  padding: 10px 18px;
}

.sc-nowrap {
  white-space: nowrap;
}


.sc-height {
  .c-flex-between;
  flex-direction: column;
  flex: 1;
  height: @leftWith;
  padding: 8px 0;
  box-sizing: border-box;
}


// 客户权益
.client-wrapper {
  .sc-shadow;

  margin-top: 20px;
  padding: 5px 20px;
  border-bottom: 1px solid #e5e5e5;

  >.title {
    padding: 5px 0;
    margin-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
  }
}
.line-through {
  text-decoration: line-through;
  color: gray;
}
.desc {
  font-size: 12px;
  margin-top: 4px;
  color: #999;
  }
// 预售权益标签样式
.presale-tag {
  background-color: #EB0D3F; // 红色背景
  color: white; // 白色文字
  font-size: 10px;
  padding: 2px 6px 2px 6px;
  margin-left: 6px;
  line-height: 16px;
  // 上尖下缩，右侧带弧度的形状
  // clip-path: polygon(0 0, 100% 0, 85% 100%, 0 100%);
  z-index: 2; // 确保在图片上方显示
  white-space: nowrap;
}
.balcks{
  color: black;
}
</style>
