<template>
  <div class="isPayment">
    <!-- 订单的状态： //待付款  00，已取消 98，已付款 30，退款中 80，已退款 81  -->
    <div class="order-status">
      <div
        class="align-center"
      >
        <h3>{{ orderStatusMap.title }}</h3>
      </div>
      <div
        class="desc"
        v-if="orderStatusMap.desc"
      >
        {{ orderStatusMap.desc }}
      </div>
    </div>
  </div>
</template>

<script>
// import { mapState } from 'vuex'
import {
  ORDER_STATUS_DISTRICT, ENGINE_TYPE
} from '@/config/conf.data'

const PAY_STATUS_DESC = {
  98: {
    status: '已取消'
  },
  99: {
    status: '已关闭'
  },
  90: {
    status: '已完成'
  },
  80: {
    status: '退款中'
  },
  81: {
    status: '已退款'
  },
  '00': {
    status: '待支付意向金'
  },
  30: {
    status: '已支付意向金，待支付定金'
  },
  31: {
    status: '已支付定金'
  },
  301: {
    status: '已支付意向金，待支付定金'
  },
  32: {
    status: '待交车'
  },
  84: {
    status: '大定退款中'
  }
}

export default {
  props: {
    // 订单（分支）状态
    maxStatus: {
      type: Object,
      default: () => ({
        message: '',
        orderStatus: ''
      })
    },
    countDown: {
      type: Array,
      default: () => ([])
    },
    knapStatus: {
      type: String,
      default: ''
    },
    isOneStep: {
      type: Boolean,
      default: false
    }
    // countDownTime: {
    //   type: String,
    //   default: ''
    // }
  },
  data() {
    return {
      payStatusDesc: PAY_STATUS_DESC,
      orderStatusMap: {},
      timeDown: []
    }
  },
  created() {

  },
  watch: {
    maxStatus: {
      handler(value) {
        this.handleInitStatus(value)
      },
      deep: true,
      immediate: true
    },
    countDown: {
      handler([hour, minute, second]) {
        const { orderStatusMap: { title }, maxStatus: { orderStatus } } = this
        const { LIFE_MAX_CYCLE } = ORDER_STATUS_DISTRICT
        if (title && (orderStatus === '00' || (this.isOneStep && orderStatus === '301'))) {
          this.orderStatusMap.title = `${LIFE_MAX_CYCLE[this.isOneStep && orderStatus === '301' ? 10 : 0].text}，还剩 ${hour}:${minute}:${second}`
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleInitStatus(statusMap) {
      const { LIFE_MAX_CYCLE } = ORDER_STATUS_DISTRICT
      const { BEVS } = ENGINE_TYPE
      const {
        message, seriesCode, paymentMethod, invalid
      } = statusMap
      let {
        status, orderStatus
      } = statusMap

      if (this.isOneStep && orderStatus === '301') {
        status = '30O'
      }
      status = status === '301' ? '30' : status
      orderStatus = orderStatus === '301' ? '30' : orderStatus
      const { knapStatus } = this
      // paymentMethod 10 => 全款，20=> 贷款

      console.log('%c [ orderStatus ]-141', 'font-size:14px; background:#cf222e; color:#fff;', orderStatus)
      if (orderStatus) {
        const STATUS = knapStatus || status?.toString() || orderStatus
        const [{ text, desc }] = LIFE_MAX_CYCLE.filter((i) => (i.status === (STATUS === '107' ? orderStatus : STATUS)))
        this.orderStatusMap = {
          title: message || text || '',
          desc
        }
        if (STATUS === '00') {
          this.orderStatusMap.desc = desc.replace(/COUNTDOWN_TIME/g, '1小时')
        } else if (['30'].includes(STATUS)) {
          this.orderStatusMap.desc = desc[BEVS.includes(seriesCode) ? 1 : 0]
        } else if (['41', '101', '97IN'].includes(STATUS)) {
          this.orderStatusMap.desc = desc[paymentMethod !== '10' ? 1 : 0]
          // eslint-disable-next-line no-nested-ternary
          // this.orderStatusMap.desc = desc[['41', '101'].includes(STATUS) ? 0 : (paymentMethod !== '10' ? 1 : 0)]
        } else if (['97UP'].includes(STATUS)) {
          console.log('%c [ invalid ]-135', 'font-size:14px; background:#cf222e; color:#fff;', invalid, paymentMethod)
          if (invalid === 0) {
            this.orderStatusMap.desc = desc[invalid]
          } else if (invalid === 2) {
            this.orderStatusMap.desc = desc[paymentMethod !== '10' ? 2 : 3]
          } else if (invalid === 3) {
            this.orderStatusMap.desc = desc[1]
          }
        }
      }
    }

    // onClick() {
    //   if (this.isSignedOnlineContract) {
    //     this.$emit('onClick', true)
    //   }
    // },

    // 显示下单时间
    // orderTime : 00 98 301
    // payTime: 30 31 80 81
    // showTime(status) {
    //   if (['00', '98', '301'].includes(status)) {
    //     return this.orderTime
    //   }
    //   if (['30', '31', '80', '81'].includes(status)) {
    //     return this.payTime
    //   }
    // },

    // getTranStatus(status) { // 支付大定才开放物流透明化入口
    //   if (['31', '32', '90'].includes(status)) {
    //     return true
    //   }
    // },

    // showDesc(status) {
    //   if (status === '30') {
    //     return '预售已开启，点击前往支付定金，享优先交付。'
    //   }
    //   if (status === '31') {
    //     if (this.vstatus === '2') {
    //       this.payStatusDesc[31].status = '待支付尾款'
    //       return '您已在上汽奥迪APP上确认验车，请及时依选定方式支付尾款。'
    //     }
    //     return '您已成功支付定金，完成合同签署后将为您匹配车辆资源，车辆到达展厅后将通知您验收车辆并完成剩余款项支付。'
    //   }
    //   if (status === '90') {
    //     return '您已完成该订单，祝您用车愉快。'
    //   }
    //   if (status === '32') {
    //     return '您已支付尾款，待厂家确认支付后，您的专属奥迪管家会与您沟通交车事宜。'
    //   }
    //   if (status === '00') {
    //     // console.log(22)
    //     return `${this.countDownNum}，如有疑问请联系客服`
    //   }
    // }
  }

}
</script>

<style scoped lang="less">
  .align-center {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .isPayment {
    // border-bottom: 1px solid #e5e5e5;
    padding: 16px 16px;
    background: linear-gradient(180deg, #F0F0F0 0%, #FFFFFF 100%);
    position: relative;
    overflow: hidden;
    &::after {
      content: '';
      position: absolute;
      z-index: 0;
      bottom: 0;
      right: 32px;
      width: 138px;
      height: 32px;
      background: url("~@/assets/img/audi-logo-gray-gradient.png") no-repeat center/contain;
    }
    .order-status {
      position: relative;
      z-index: 2;
    }

    h3 {
      font-size: 14px;
      font-family: "Audi-WideBold";
      line-height: 24px;
      font-weight: 500;
      margin: 0 0 8px 0;
    }

    .desc {
      font-size: 10px;
      color: #000000;
      line-height: 18px;
      margin-bottom: 4px;
    }

    .timer {
      font-size: 10px;
      line-height: 18px;
    }
  }
</style>
