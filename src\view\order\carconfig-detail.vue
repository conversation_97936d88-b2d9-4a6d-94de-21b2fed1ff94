<template>
  <div class="">
    <img
      :src="imgUrl"
      alt=""
    >
  </div>
</template>

<script>
import { getCarConfigDetail } from '../../api/smallDetail'
import api from '../../config/url'

const codeType = ['00', '200']
export default {
  beforeRouteEnter(to, from, next) {
    if (['carconfig-detail'].includes(to.name)) {
      next((vm) => {
        vm.$emit('set-bottom', true)
      })
    } else {
      next()
    }
  },
  beforeRouteLeave(to, form, next) {
    if (['detail'].includes(to.name)) {
      this.$emit('set-bottom', false)
      next()
    } else {
      next()
    }
  },
  name: '',
  data() {
    return {
      BaseOssHost: api.BaseOssHost,
      imgUrl: ''
    }
  },
  components: {},
  created() {},
  mounted() {
    this.getCarConfigDetail()
  },
  methods: {
    async getCarConfigDetail() {
      const { data } = await getCarConfigDetail()
      if (codeType.includes(data.code)) {
        this.imgUrl = this.BaseOssHost + data.data[0].content.contentDetailList[0].imageUrl
      }
    }
  }
}
</script>
<style scoped lang="less">
div {
  width: 100%;
  height: 100%;
  display: block;
  .chi{
    height: 100%;
    min-height: inherit;
      background-size: 100% 100%;
      background-repeat: no-repeat;
  }
}
</style>
