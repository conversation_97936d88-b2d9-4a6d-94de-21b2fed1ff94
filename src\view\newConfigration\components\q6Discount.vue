<template>
    <!-- 限时优惠的标签 -->
    <div class="tag-wrapper" :class="{
        black: props.blackIcon
    }">
        <div class="icon-tag" v-if="props.blackIcon">
            <img src="@/assets/icon-youhui-black.png" alt="">
        </div>

        <div class="icon-tag" v-else>
            <img src="@/assets/icon-youhui.png" alt="">
        </div>
        <div class="text"> {{ props.price | finalFormatPriceDesc }}</div>
    </div>
</template>

<script setup>
/**
 * q6的限时优惠组件
 * 齐云的七座显示黑， 其他显示红
 * 7座modellinecode: G6IBAY002
 */
import { defineProps, ref, getCurrentInstance } from 'vue'

// 获取 props
const props = defineProps({
  blackIcon: {
    type: Boolean,
    required: true,
    default: false
  },
  price: {
    type: String,
    required: false,
    default: '20000'
  }
})


</script>

<style lang="less" scoped>
img {
    height: 100%;
    width: auto;
    vertical-align: top;
}

.tag-wrapper {
    position: absolute;
    left: -10px;
    top: -5px;
    display: flex;
    align-items: center;
    border: 1px solid #EB0D3F;
    height: 14px;
    margin-left: 5px;
    background-color: #fff;
    color: #EB0D3F;

    &.black {
        color: #000;
        border: 1px solid black;
    }

    >.icon-tag {
        height: 100%;
        // width: auto;
    }

    >.text {
        font-size: 10px;
        line-height: 14px;
        padding: 0 4px;
    }
}
</style>
