<template>
  <div class="_container">
    <navigation
      title=""
      :back-type="backType"
    />
    <van-collapse
      v-model="activeNames"
      @change="changeTab"
    >
      <van-collapse-item
        title="身份证"
        name="1"
      >
        <template #right-icon>
          <van-icon
            v-if="activeNames.includes('1')"
            name="arrow-down"
          />
          <van-icon
            v-else
            name="arrow"
          />
        </template>
        <p
          class="identity-sample"
          @click="seeTheSample(1)"
        >
          示例
        </p>
        <div class="identity">
          <div class="identity-img">
            <img
              v-if="!idCardPositive.materialUrl"
              class="item-img"
              src="../../assets/img/icon-identity11.png"
              @click="onUploadingImg(1, disabled1)"
            >
            <img
              class="item-img-i"
              v-if="idCardPositive.materialUrl"
              :src="idCardPositive.materialUrl"
              @click="onUploadingImg(1, disabled1)"
            >
            <p>身份证正面</p>
          </div>

          <div class="identity-img">
            <img
              v-if="!idCardReverse.materialUrl"
              class="item-img"
              src="../../assets/img/icon-identity11.png"
              @click="onUploadingImg(2, disabled1)"
            >
            <img
              class="item-img-i"
              v-if="idCardReverse.materialUrl"
              :src="idCardReverse.materialUrl"
              @click="onUploadingImg(2, disabled1)"
            >
            <p>身份证反面</p>
          </div>
        </div>
      </van-collapse-item>
      <p
        style="background: #f2f2f2;height: 8px;"
        v-if="activeNames.includes('1')"
      />

      <van-collapse-item
        title="护照"
        name="2"
      >
        <template #right-icon>
          <van-icon
            v-if="activeNames.includes('2')"
            name="arrow-down"
          />
          <van-icon
            v-else
            name="arrow"
          />
        </template>
          <p
          class="identity-sample"
          @click="seeTheSample(2)"
        >
          示例
        </p>
        <draggable
          class="img_upload"
          :list="fileList"
          :sort="false"
          group="list"
          :delay="20"
          tag="div"
          :disabled="disabled2"
          @end="end($event, 2)"
          animation="200"
          filter=".pop-up-layer"
        >
          <div
            class="img_upload_item"
            v-for="(item, index) in fileList"
            :key="index"
            @click.stop="previewImg(fileList, 2,dataList&&dataList.passportStatus)"
            @touchmove="touchmove($event, disabled2)"
            @touchend="up"
          >
            <div class="van-image van-uploader__preview-image">
              <img
                :src="item.materialUrl"
                alt="图片"
                class="van-image__img"
              >
            </div>
          </div>
          <!-- v-if="!disabled2&&fileList&&fileList.length<9" -->

          <div
            class="pop-up-layer"
            @click="createImg(5)"
            v-show="(!disabled2 )"
          >
            <div class="pop-up-layer-connent">
              <img
                class="pop-up-layer-img"
                src="../../assets/img/forward-small.png"
              >
            </div>
          </div>
          <!-- </div> -->
        </draggable>
      </van-collapse-item>
       <p
        style="background: #f2f2f2;height: 8px;"
        v-if="activeNames.includes('2')"
      />
      <van-collapse-item
        name="6"
        title="出入境证明"
      >
        <template #right-icon>
          <van-icon
            v-if="activeNames.includes('6')"
            name="arrow-down"
          />
          <van-icon
            v-else
            name="arrow"
          />
        </template>
        <p
          class="identity-sample"
          @click="seeTheSample(6)"
        >
          示例
        </p>
        <draggable
          class="img_upload"
          :list="fileListSix"
          :sort="false"
          group="listf"
          :delay="20"
          @end="end($event, 6)"
          animation="200"
          :disabled="disabled4"
          filter=".pop-up-layer"
        >
          <!-- <div class="img_upload"> -->
          <div
            class="img_upload_item"
            v-for="(item, index) in fileListSix"
            :key="index"
            @click.stop="previewImg(fileListSix, 6,dataList&&dataList.entryExitProveStatus)"
            @touchmove="touchmove($event, disabled4)"
            @touchend="up"
          >
            <div class="van-image van-uploader__preview-image">
              <img
                :src="item.materialUrl"
                alt="图片"
                class="van-image__img"
              >
            </div>
          </div>
          <!-- <van-uploader :after-read="onRead" /> -->
          <div
            class="pop-up-layer"
            v-show="(!disabled4)"
            @click="createImg(8)"
          >
            <div class="pop-up-layer-connent">
              <img
                class="pop-up-layer-img"
                src="../../assets/img/forward-small.png"
              >
            </div>
          </div>
          <!-- </div> -->
        </draggable>
      </van-collapse-item>
      <p
        style="background: #f2f2f2;height: 8px;"
        v-if="activeNames.includes('6')"
      />
      <van-collapse-item name="3">
        <template #right-icon>
          <van-icon
            v-if="activeNames.includes('3')"
            name="arrow-down"
          />
          <van-icon
            v-else
            name="arrow"
          />
        </template>
        <template #title>
          <div class="title-prompt">
            学历认证
            <!-- <van-icon
              name="info-o"
              @click.stop="prompt"
            /> -->
            <!-- <img
              class="img1"
              src="../../assets/img/enter-switch1.jpg"
              @click.stop="prompt"
            >
            <div
              class="tost-icon-text"
              v-show="iconText"
            >
              如同集团不同公司主体员工需要
            </div> -->
          </div>
        </template>
        <p
          class="identity-sample"
          @click="seeTheSample(3)"
        >
          示例
        </p>
        <draggable
          class="img_upload"
          :list="fileListThree"
          :sort="false"
          group="lists"
          :delay="20"
          :disabled="disabled3"
          filter=".pop-up-layer"
          @end="end($event, 3)"
          animation="200"
        >
          <!-- <div class="img_upload"> -->
          <div
            class="img_upload_item"
            v-for="(item, index) in fileListThree"
            :key="index"
            @click.stop="previewImg(fileListThree, 3,dataList&&dataList.educationCertificationStatus)"
            @touchmove="touchmove($event, disabled3)"
            @touchend="up"
          >
            <div class="van-image van-uploader__preview-image">
              <img
                :src="item.materialUrl"
                alt="图片"
                class="van-image__img"
              >
            </div>
          </div>
          <!-- <van-uploader :after-read="onRead" /> -->
          <div
            class="pop-up-layer"
            v-show="(!disabled3)"
            @click="createImg(6)"
          >
            <div class="pop-up-layer-connent">
              <img
                class="pop-up-layer-img"
                src="../../assets/img/forward-small.png"
              >
            </div>
          </div>
          <!-- </div> -->
        </draggable>
      </van-collapse-item>
     
      <p
        style="background: #f2f2f2;height: 8px;"
        v-if="activeNames.includes('3')"
      />
      <van-collapse-item
        name="4"
        title="直系亲属证明材料"
        v-if="relatives == 1"
      >
        <template #right-icon>
          <van-icon
            v-if="activeNames.includes('4')"
            name="arrow-down"
          />
          <van-icon
            v-else
            name="arrow"
          />
        </template>
        <draggable
          class="img_upload"
          :list="fileListFour"
          :sort="false"
          group="listf"
          :delay="20"
          @end="end($event, 4)"
          animation="200"
          :disabled="disabled4"
          filter=".pop-up-layer"
        >
          <!-- <div class="img_upload"> -->
          <div
            class="img_upload_item"
            v-for="(item, index) in fileListFour"
            :key="index"
            @click.stop="previewImg(fileListFour, 4,dataList&&dataList.additionalBuyersStatus)"
            @touchmove="touchmove($event, disabled4)"
            @touchend="up"
          >
            <div class="van-image van-uploader__preview-image">
              <img
                :src="item.materialUrl"
                alt="图片"
                class="van-image__img"
              >
            </div>
          </div>
          <!-- <van-uploader :after-read="onRead" /> -->
          <div
            class="pop-up-layer"
            v-show="(!disabled4)"
            @click="createImg(7)"
          >
            <div class="pop-up-layer-connent">
              <img
                class="pop-up-layer-img"
                src="../../assets/img/forward-small.png"
              >
            </div>
          </div>
          <!-- </div> -->
        </draggable>
      </van-collapse-item>
      <p
        style="background: #f2f2f2; height: 8px;"
        v-if="activeNames.includes('4')"
      />
      <van-collapse-item
        :title="'直系亲属身份证'"
        name="5"
        v-if="relatives == 1"
      >
        <template #right-icon>
          <van-icon
            v-if="activeNames.includes('5')"
            name="arrow-down"
          />
          <van-icon
            v-else
            name="arrow"
          />
        </template>
        <p
          class="identity-sample"
          @click="seeTheSample(1)"
        >
          示例
        </p>
        <div class="identity">
          <div class="identity-img">
            <img
              v-if="!idCardPositiveRelatives.materialUrl"
              class="item-img"
              src="../../assets/img/icon-identity11.png"
              @click="onUploadingImg(3, disabled5)"
            >
            <img
              class="item-img-i"
              v-if="idCardPositiveRelatives.materialUrl"
              :src="idCardPositiveRelatives.materialUrl"
              @click="onUploadingImg(3, disabled5)"
            >
            <p>身份证正面</p>
          </div>

          <div class="identity-img">
            <img
              v-if="!idCardReverseRelatives.materialUrl"
              class="item-img"
              src="../../assets/img/icon-identity11.png"
              @click="onUploadingImg(4, disabled5)"
            >
            <img
              class="item-img-i"
              v-if="idCardReverseRelatives.materialUrl"
              :src="idCardReverseRelatives.materialUrl"
              @click="onUploadingImg(4, disabled5)"
            >
            <p>身份证反面</p>
          </div>
        </div>
      </van-collapse-item>
    </van-collapse>
    <input
      class="hide_file"
      ref="onTheJob"
      id="on-the-job"
      type="file"
      @change="getOnTheJob($event)"
      accept="image/*"
      multiple="multiple"
    >
    <input
      class="hide_file"
      ref="onTheJobX"
      id="upload"
      type="file"
      @change="getOnTheJobX($event)"
      capture="camera"
      accept="image/camera"
    >
    <van-popup
      v-model="show"
      position="bottom"
      :style="{ height: '30%' }"
      @click-overlay="clickOverlay"
    >
      <p
        class="click-button"
        @click="onPhotoAlbumImgX()"
      >
        拍摄
      </p>
      <p
        class="click-button"
        @click="onPhotoAlbumImg()"
      >
        从相册选择
      </p>
      <p
        class="click-button-b"
        @click="show = false"
      >
        取消
      </p>
    </van-popup>
    <div
      class="delete-img"
      v-if="isShowDelete"
    >
      {{ placeholder }}
    </div>
    <div class="btn-delete-height" />
    <div v-if="onSubmitColor()">
      <div class="bottom_style">
        <div class="btn-delete-wrapper">
          <AudiButton
            @click="onSubmit"
            :text="'提交'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
      </div>
    </div>
    <div v-if="!onSubmitColor()">
      <div class="bottom_style">
        <div class="btn-delete-wrapper-s">
          提交
        </div>
      </div>
    </div>
    <div
      class="mask"
      v-show="isMask"
    >
      <draggable
        class="mask-drag"
        :list="fileListFours"
        :sort="false"
        group="lista"
        @end="end($event, 4)"
        animation="200"
        :disabled="disabled4"
      />
    </div>
    <div v-if="imgShow">
      <!-- <div class="nijao">
        你好
      </div> -->
      <div
        class="image-delete"
        v-if="isDeleteImg"
        @click="deleteImg"
      >
        <van-icon
          name="delete-o"
          color="#eeeeee"
          size="24"
        />
      </div>
      <van-image-preview
        v-model="imgShow"
        :images="images"
        @change="onChange"
        :closeable="true"
        :is-image="true"
        @close="close"
        close-icon-position="top-left"
      >
        <!-- <template #indexs>
          第{{ indexs }}页
        </template> -->
      </van-image-preview>
    </div>

    <van-overlay :show="isShowMessageId">
      <div class="wrapper">
        <div class="block">
          <p class="block-text">
            您已重新提交过资料
          </p>
          <p
            class="block-btn"
            @click="appGoBack"
          >
            我知道了
          </p>
        </div>
      </div>
    </van-overlay>

    <div
      v-if="isShowTostFail"
      class="tost-fail"
    >
      {{ TostFailText }}
    </div>

    <van-popup
      v-if="modalshow"
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal">
        <div class="modal-title">
          {{ '是否确认删除该照片' }}
        </div>

        <div
          class="modal-confirm center"
          @click.stop="onConfirm"
        >
          {{ '确定' }}
        </div>
        <div
          class="modal-cancel center"
          @click.stop="onCancel"
        >
          {{ '取消' }}
        </div>
      </div>
    </van-popup>

  <van-overlay :show="isCamera" @click="isCamera = false">
      <div class="wrapper">
        <div class="block" style="height: 134px">
          <p class="block-text" style="margin-bottom: 15px">
            允许上汽奥迪访问您的{{cameraRoll}}
          </p>
          <p
            class="block-btn"
            @click="openCameraRoll"
          >
            确定
          </p>
        </div>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Button,
  ActionSheet,
  Cell,
  Popup,
  Area,
  Toast,
  Checkbox,
  CheckboxGroup,
  Switch,
  Collapse, CollapseItem,
  Uploader,
  ImagePreview,
  Overlay,
  Dialog
} from 'vant'
import HtmlImageCompress from 'html-image-compress'
import draggable from 'vuedraggable'
import { userInfo } from 'os'
import { mapState, mapMutations, mapGetters } from 'vuex'
import AudiButton from '@/components/audi-button'
import navigation from '../../components/navigation'
import {
  saveReturnedEliteInformation,
  returnedEliteMaterialUploadOSSFile,
  queryReturnedEliteMaterials,
  queryReturnedEliteInformationById
} from '@/api/api'
// import { paramsFile } from '@/utils'
import { callNative } from '@/utils/index'


Vue.use(Form)
  .use(Field)
  .use(Button)
  .use(ActionSheet)
  .use(Cell)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Area)
  .use(Switch)
  .use(Collapse)
  .use(CollapseItem)
  .use(Overlay)
  .use(Uploader)
  .use(Toast)
  .use(Dialog)
export default {
  components: {
    AudiButton,
    navigation,
    draggable,
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  data() {
    return {
      backType: 'app',
      activeNames: ['1'],
      idCardPositive: {
        materialUrl: '',
        materialData: '',
        materialType: ''
      }, // 本人身份证正面
      idCardReverse: {
        materialUrl: '',
        materialData: '',
        materialType: ''
      }, // 本人身份证反面
      idCardPositiveRelatives: {
        materialUrl: '',
        materialData: '',
        materialType: ''
      }, // 亲属身份证正面
      idCardReverseRelatives: {
        materialUrl: '',
        materialData: '',
        materialType: ''
      }, // 亲属身份证反面
      fileList: [], // 在职敲章证明和社保参保证明
      fileListThree: [], // 关联公司证明（选填）
      fileListFour: [], // 直系亲属证明材料
      fileListSix: [],
      fileListFours: [],
      fileType: 0, //
      imgType: 1, // 判断身份证

      show: false,
      placeholder: '拖拽此处删除',
      clientWidth: 0,
      clientHeight: 0,
      clientY: 0,
      isShowDelete: false,
      isMask: false, // 透明遮罩防止拖拽删除时换位
      associatedId: '',
      relatives: 1,
      indexs: 0,
      listindex: '',
      images: [],
      imgShow: false,
      isDeleteImg: false,
      dataList: null,
      disabled2: false,
      disabled3: false,
      disabled4: false,
      disabled1: false,
      disabled5: false,
      iconText: false,
      isShowMessageId: false,
      isShowTostFail: false,
      modalshow: false,
      TostFailText: '您还有资料未填写完毕',
      isCamera:false,
      cameraRoll:''
    }
  },
  mounted() {
    this.clientHeight = document.documentElement.clientHeight - 68
    const ismessageId = this.$route.query.messageId
    const params = {
      messageId: ismessageId
    }
    if (ismessageId) {
      queryReturnedEliteInformationById(params).then((res) => {
        if (res.data.data && res.data.data.informationStatus === 2) {
          this.isShowMessageId = false
          console.log(res, 'hahahha')

          this.getDataList()
        } else {
          this.isShowMessageId = true
        }
      })
    } else {
      this.getDataList()
    }


    document.addEventListener('click', this.hidder, true)
  },
  methods: {
    ...mapMutations(['setIdCardPositiveElite', 'setIdCardReverseElite', 'setIdCardPositiveRelativesElite', 'setIdCardReverseRelativesElite', 'setFileListThreeElite', 'setFileListElite', 'setFileListFourElite', 'setfileListSix']),
    ...mapGetters(['getIdCardPositiveElite', 'getIdCardReverseElite', 'getIdCardPositiveRelativesElite', 'getIdCardReverseRelativesElite', 'getFileListThreeElite', 'getFileListElite', 'getFileListFourElite', 'getfileListSix']),

    hidder(e) {
      // 打印一下e可以看到具体内容和其他解决方法
      // 判断点击的位置在弹出框之外
      // console.log(e.srcElement.className, 'e.srcElement.className')
      if (e.srcElement.className !== 'img1') {
        if (e.srcElement.className !== 'tost-icon-text') {
          this.iconText = false // 等于false隐藏弹出框
        }
      }
    },

    getDataList() {
      queryReturnedEliteMaterials().then((res) => {
        // console.log(this.getFileListElite(), 'this.getIdCardPositiveElite()')
        console.log(res, 'lalalal')

        if (res.data.code === '00') {
          this.dataList = res.data.data
          this.associatedId = res.data.data.id
          if (res.data.data.returnedEliteIdCard.length > 0) {
            this.idCardPositive = res.data.data.returnedEliteIdCard[0]
            this.idCardReverse = res.data.data.returnedEliteIdCard[1]
            this.disabled1 = false
          } else {
            if (this.getIdCardPositiveElite()) {
              this.idCardPositive = this.getIdCardPositiveElite()
            }
            if (this.getIdCardReverseElite()) {
              this.idCardReverse = this.getIdCardReverseElite()
            }

            this.activeNames.push('1')
          }
          if (res.data.data.additionalBuyersIdcardMaterials.length > 0) {
            this.idCardPositiveRelatives = res.data.data.additionalBuyersIdcardMaterials[0]
            this.idCardReverseRelatives = res.data.data.additionalBuyersIdcardMaterials[1]
            this.disabled5 = false
          } else {
            if (this.getIdCardPositiveRelativesElite()) {
              this.idCardPositiveRelatives = this.getIdCardPositiveRelativesElite()
            }
            if (this.getIdCardReverseRelativesElite()) {
              this.idCardReverseRelatives = this.getIdCardReverseRelativesElite()
            }
            if (res.data.data.id && res.data.data.additionalBuyers == 1) {
              this.activeNames.push('5')
            }
          }
          if (res.data.data.passports.length > 0) {
            this.fileList = res.data.data.passports
            this.disabled2 = false
          } else {
            if (this.getFileListElite()) {
              this.fileList = this.getFileListElite()
            }
            if (res.data.data.id) {
              this.activeNames.push('2')
            }
          }
          if (res.data.data.educationCertifications.length > 0) {
            this.fileListThree = res.data.data.educationCertifications
            this.disabled3 = false
          } else {
            if (this.getFileListThreeElite()) {
              this.fileListThree = this.getFileListThreeElite()
            }
            if (res.data.data.id) {
              this.activeNames.push('3')
            }
          }
          if (res.data.data.additionalBuyersMaterials.length > 0) {
            this.fileListFour = res.data.data.additionalBuyersMaterials
            this.disabled4 = false
          } else {
            if (this.getFileListFourElite()) {
              this.fileListFour = this.getFileListFourElite()
            }
            if (res.data.data.id && res.data.data.additionalBuyers == 1) {
              this.activeNames.push('4')
            }
          }

          if (res.data.data.entryExitProves.length > 0) {
            this.fileListSix = res.data.data.entryExitProves
            this.disabled4 = false
          } else {
            if (this.getfileListSix()) {
              this.fileListSix = this.getfileListSix()
            }
            if (res.data.data.id) {
              this.activeNames.push('6')
            }
          }

          if (res.data.data.additionalBuyers) {
            this.relatives = res.data.data.additionalBuyers
          } else {
            const str = JSON.parse(localStorage.getItem('returnedEliteElite'))
            this.relatives = str && str.additionalBuyers ? 1 : 0
          }
        } else {
          const str = JSON.parse(localStorage.getItem('returnedEliteElite'))
          this.relatives = str && str.additionalBuyers ? 1 : 0
        }
      })
    },
    onSubmitColor() {
      if (this.relatives == 1) {
        if (this.fileListFour.length > 0
          && this.idCardPositiveRelatives.materialUrl
          && this.idCardReverseRelatives.materialUrl
          && this.idCardPositive.materialUrl
          && this.idCardReverse.materialUrl
          && this.fileList.length > 0
          && this.fileListThree.length > 0
        ) {
          return true
        }

        return false
      }
      if (this.idCardPositive.materialUrl
          && this.idCardReverse.materialUrl
          && this.fileList.length > 0
          && this.fileListThree.length > 0) {
        return true
      }

      return false
    },
    // 提交
    async onSubmit() {
      // if (this.idCardPositive.materialUrl
      // && this.idCardReverse.materialUrl
      // && this.idCardPositiveRelatives.materialUrl
      // && this.idCardReverseRelatives.materialUrl
      // && this.fileList.length > 0
      // && this.fileListFour.length > 0
      // )
      // console.log(this.getIdCardPositiveElite(), '提取出阿卡')
      if (!this.idCardPositive.materialUrl || !this.idCardReverse.materialUrl) {
        this.isShowTostFail = true
        setTimeout(() => {
          this.isShowTostFail = false
        }, 1000)
        return
      }
      // if (this.fileList.length < 1) {
      //   this.TostFailText = '最少上传一张在职敲章证明和社保参保证明'
      //   this.isShowTostFail = true
      //   setTimeout(() => {
      //     this.isShowTostFail = false
      //     this.TostFailText = '您还有资料未填写完毕'
      //   }, 1000)

      //   return
      // }
      // if (this.fileListThree.length < 2) {
      //   this.TostFailText = '最少上传两张职称证书'
      //   this.isShowTostFail = true
      //   setTimeout(() => {
      //     this.isShowTostFail = false
      //     this.TostFailText = '您还有资料未填写完毕'
      //   }, 1000)

      //   return
      // }
      // if (this.relatives == 1 && this.fileListFour.length < 1) {
      //   this.isShowTostFail = true
      //   this.TostFailText = '最少上传一张直系亲属材料'

      //   setTimeout(() => {
      //     this.isShowTostFail = false
      //     this.TostFailText = '您还有资料未填写完毕'
      //   }, 1000)

      //   return
      // }
      if (this.relatives == 1 && (!this.idCardPositiveRelatives.materialUrl || !this.idCardReverseRelatives.materialUrl)) {
        this.isShowTostFail = true
        setTimeout(() => {
          this.isShowTostFail = false
        }, 1000)
        return
      }
      this.$store.commit('showLoading')
      // const strArray = []
      // let strData = []
      // strArray.push(this.idCardPositive)
      // strArray.push(this.idCardReverse)
      // strArray.push(this.idCardPositiveRelatives)
      // strArray.push(this.idCardReverseRelatives)
      // strData = [...strArray, ...this.fileList, ...this.fileListThree, ...this.fileListFour]
      // console.log(strData, 'strData')
      // const res = await Promise.all(strData.map((item, index) => {
      //   if (item.materialData) {
      //     const params = {
      //       materialType: item.materialType
      //     }
      //     const formData = new FormData()
      //     formData.append('file', item.materialData)
      //     return returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
      //       if (val.data && val.data.code == '00') {
      //         return val
      //       }
      //       return {}
      //     })
      //     // return returnedEliteMaterialUploadOSSFile(params, formData)
      //   }
      //   return item
      // }))
      // const listA = []
      // const listB = []
      // const listC = []
      // const listD = []
      // const listE = []
      // console.log(res, 'res')
      // const dataList = strData.map((val) => {
      //   console.log(val, 'valvalvalval')
      //   if (val.id) {
      //     return val
      //   }
      //   return val
      // })
      // console.log(dataList, 'dataList')
      // if (dataList.length > 0) {
      //   dataList.forEach((item) => {
      //     if (item) {
      //       if (item.materialType === 1) {
      //         listA.push({
      //           ...item
      //         })
      //       } else if (item.materialType === 2) {
      //         listB.push({
      //           ...item
      //         })
      //       } else if (item.materialType === 3) {
      //         listC.push({
      //           ...item
      //         })
      //       } else if (item.materialType === 4) {
      //         listD.push({
      //           ...item
      //         })
      //       } else if (item.materialType === 5) {
      //         listE.push({
      //           ...item
      //         })
      //       }
      //       this.idCardPositive = listA[0]
      //       this.idCardReverse = listA[1]
      //       this.fileList = listB
      //       this.fileListThree = listC
      //       this.fileListFour = listD
      //       this.idCardPositiveRelatives = listE[0]
      //       this.idCardReverseRelatives = listE[1]
      //     }
      //   })
      this.getDate()
      //   // console.log(this.fileList, 'this.fileList')
      //   // console.log(this.fileListThree, 'this.fileListThree')
      //   // console.log(this.fileListFour, 'this.fileListFour')
      // }
    },
    getDate() {
      setTimeout(() => {
        let basicData = null
        // if (this.associatedId) {
        //   basicData = this.dataList
        // } else {
        //   basicData = JSON.parse(localStorage.getItem('returnedEliteElite'))
        // }
        if(JSON.parse(localStorage.getItem('returnedEliteElite'))){
          basicData = JSON.parse(localStorage.getItem('returnedEliteElite'))
        }else{
          if (this.associatedId) {
            basicData = this.dataList
          }
        }

        const params = {
          userFullName: basicData.userFullName,
          idCard: basicData.idCard,
          // employerName: basicData.employerName,
          phoneNumber: basicData.phoneNumber,
          handlingMethod:basicData.handlingMethod,
          additionalBuyers: basicData.additionalBuyers ? 1 : 0,
          additionalBuyersName: basicData.additionalBuyersName,
          additionalBuyersPhoneNumber: basicData.additionalBuyersPhoneNumber,
          additionalBuyersIdCard: basicData.additionalBuyersIdCard,
          id: this.associatedId,
          passports: this.fileList,
          educationCertifications: this.fileListThree,
          additionalBuyersMaterials: this.fileListFour,
          entryExitProves: this.fileListSix,
          additionalBuyersIdcardMaterials: this.idCardPositiveRelatives.materialUrl && this.idCardReverseRelatives ? [
            {
              ...this.idCardPositiveRelatives
            },
            {
              ...this.idCardReverseRelatives
            }
          ] : [],
          returnedEliteIdCard: this.idCardPositive && this.idCardReverse ? [
            {
              ...this.idCardPositive
            },
            {
              ...this.idCardReverse
            }
          ] : []
        }
        // console.log(params, 'paramsparams')
        saveReturnedEliteInformation(params).then((res) => {
          if (res.data.code === '00') {
            localStorage.removeItem('returnedEliteElite')
            localStorage.removeItem('returnedEliteEliteAgreement')
            this.$store.commit('setIdCardPositiveElite', '')
            this.$store.commit('setIdCardReverseElite', '')
            this.$store.commit('setIdCardPositiveRelativesElite', '')
            this.$store.commit('setIdCardReverseRelativesElite', '')
            this.$store.commit('setFileListThreeElite', '')
            this.$store.commit('setFileListElite', '')
            this.$store.commit('setFileListFourElite', '')
            this.$store.commit('setfileListSix', '')

            this.$router.push({ name: 'returnedElite-uploaded-success' })
          }
        })
        this.$store.commit('hideLoading')
      })
    },
    // 上传图
    onUploadingImg(type, isShow) {
      // if (!isShow) {
        this.show = true
        this.fileType = type
      // }
    },


    async onPhotoAlbumImg() {
      // const data = await callNative('albumCameraEvent', { type: 2 })
      // console.log(data,'是否开启相册权限')
      // if(data.status){
        this.$refs.onTheJob.click()
      // }else{
      //   this.show=false
      //   this.fileType=0
      //   this.cameraRoll='相册'
      //   this.isCamera=true
      // }

    },
    async onPhotoAlbumImgX() {
      // const data = await callNative('albumCameraEvent', { type: 1 })
      // console.log(data,'是否开启相机权限')
      // if(data.status){
        this.$refs.onTheJobX.click()
      // }else{
      //   this.show=false
      //   this.fileType=0
      //   this.cameraRoll='相机'
      //   this.isCamera=true
      // }
    },

    async openCameraRoll(){
       const data = await callNative('openpage', { type: 1 })
    },
    async getOnTheJobX(e) {
      const file = e.target.files[0]
      const reader = new FileReader()
      if (file) {
        // 通过文件流将文件转换成Base64字符串
        reader.readAsDataURL(file)
        this.show = false
        // 转换成功后
        const this1 = this
        reader.onloadend = function () {
          // 输出结果
          this1.getOnTheJobformData(file, reader.result)
        }
      }
    },
    async getOnTheJob(e) {
      const file = e.target.files
      if (file.length > 9) {
        Toast({
          type: 'fail',
          message: '一次最多上传6张'
        })
        return
      }

      if (file) {
        for (let i = 0; i < file.length; i++) {
          const reader = new FileReader()
          reader.readAsDataURL(file[i])
          this.show = false
          // 转换成功后
          const this1 = this
          reader.onloadend = function () {
            // 输出结果
            this1.getOnTheJobformData(file[i], reader.result)
          }
        }
      }
    },
    async getOnTheJobformData(file, base64) {
      new HtmlImageCompress(file, { quality: 0.5 })
        .then((result) => {
          if (this.fileType == 1) {
            const params = {
              materialType: 1
            }
            const formData = new FormData()
            formData.append('file', result.file)
            returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
              if (val.data && val.data.code == '00') {
                console.log(val, 'valvalval')
                this.idCardPositive = {
                  ...val.data.data
                }
                this.$store.commit('setIdCardPositiveElite', this.idCardPositive)
              }
            })
          } else if (this.fileType == 2) {
            const params = {
              materialType: 1
            }
            const formData = new FormData()
            formData.append('file', result.file)
            returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
              if (val.data && val.data.code == '00') {
                console.log(val, 'valvalval')
                this.idCardReverse = {
                  ...val.data.data
                }
                this.$store.commit('setIdCardReverseElite', this.idCardReverse)
              }
            })
          } else if (this.fileType == 3) {
            const params = {
              materialType: 5
            }
            const formData = new FormData()
            formData.append('file', result.file)
            returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
              if (val.data && val.data.code == '00') {
                console.log(val, 'valvalval')
                this.idCardPositiveRelatives = {
                  ...val.data.data
                }
                this.$store.commit('setIdCardPositiveRelativesElite', this.idCardPositiveRelatives)
              }
            })
          } else if (this.fileType == 4) {
            const params = {
              materialType: 5
            }
            const formData = new FormData()
            formData.append('file', result.file)
            returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
              if (val.data && val.data.code == '00') {
                console.log(val, 'valvalval')
                this.idCardReverseRelatives = {
                  ...val.data.data
                }
                this.$store.commit('setIdCardReverseRelativesElite', this.idCardReverseRelatives)
              }
            })
          } else if (this.fileType == 6) {
            const params = {
              materialType: 3
            }
            const formData = new FormData()
            formData.append('file', result.file)
            returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
              if (val.data && val.data.code == '00') {
                console.log(val, 'valvalval')
                this.fileListThree.push({
                  ...val.data.data
                })
                this.$store.commit('setFileListThreeElite', this.fileListThree)
              }
            })
          } else if (this.fileType == 5) {
            const params = {
              materialType: 2
            }
            const formData = new FormData()
            formData.append('file', result.file)
            returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
              if (val.data && val.data.code == '00') {
                console.log(val, 'valvalval')
                this.fileList.push({
                  ...val.data.data
                })
                this.$store.commit('setFileListElite', this.fileList)
              }
            })
          } else if (this.fileType == 7) {
            const params = {
              materialType: 4
            }
            const formData = new FormData()
            formData.append('file', result.file)
            returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
              if (val.data && val.data.code == '00') {
                console.log(val, 'valvalval')
                this.fileListFour.push({
                  ...val.data.data
                })
                this.$store.commit('setFileListFourElite', this.fileListFour)
              }
            })
          } else if (this.fileType == 8) {
            const params = {
              materialType: 6
            }
            const formData = new FormData()
            formData.append('file', result.file)
            returnedEliteMaterialUploadOSSFile(params, formData).then((val) => {
              if (val.data && val.data.code == '00') {
                console.log(val, 'valvalval')
                this.fileListSix.push({
                  ...val.data.data
                })
                this.$store.commit('setfileListSix', this.fileListSix)
              }
            })
          }
          // this.onSubmitColor()
        })
    },
    clickOverlay() {
      this.isMask = false
    },
    // 当鼠标移动时触发
    touchmove(e, isShow) {
      this.clientY = e.targetTouches[0].clientY
      this.isMask = true

      if (!isShow) {
        this.isShowDelete = true
        if (this.clientY > this.clientHeight) {
          this.placeholder = '松开即可删除'
        } else {
          this.placeholder = '拖拽此处删除'
        }
      } else {
        this.isMask = false
      }
    },
    // 当鼠标松开时
    up() {
      // removeEventListener('touchmove', this.end, false)
      this.isShowDelete = false
      this.isMask = false
    },
    // 拖拽结束后 获取刚刚拖动的数据
    end(e, val) {
      this.isShowDelete = false
      this.isMask = false
      if (this.clientY > this.clientHeight) {
        this.delImg(e.newIndex, val)
      }
      this.$forceUpdate()
      // this.end()
      // removeEventListener('touchmove', this.touchmove, false)
    },
    createImg(val) {
      this.show = true
      this.fileType = val
    },
    // 删除图片
    delImg(index, val) {
      let dataArray = []
      if (val == 2) {
        dataArray = this.fileList
      } else if (val == 3) {
        dataArray = this.fileListThree
      } else if (val == 4) {
        dataArray = this.fileListFour
      } else if (val == 6) {
        dataArray = this.fileListSix
      }
      if (isNaN(index) || index >= dataArray.length) {
        return false
      }
      const tmp = []
      for (let i = 0, len = dataArray.length; i < len; i++) {
        if (dataArray[i] !== dataArray[index]) {
          tmp.push(dataArray[i])
        }
      }
      if (val == 2) {
        this.fileList = tmp
      } else if (val == 3) {
        this.fileListThree = tmp
      } else if (val == 4) {
        this.fileListFour = tmp
      } else if (val == 6) {
        this.fileListSix = tmp
      }
      // this.fileList = tmp
    },

    // 点击预览
    previewImg(imageItem, index, isDeleteImg) {
      this.imgShow = true
      this.listindex = index
      // this.images = [...imageItem]
      this.images = imageItem.map((item) => item.materialUrl)
      // this.images.push(item.materialUrl)
      if (isDeleteImg !== 1) {
        this.isDeleteImg = true
      } else {
        this.isDeleteImg = false
      }
    },
    onChange(index) {
      this.indexs = index
      // console.log(this.indexs, '11111')
    },
    deleteImg() {
      this.modalshow = true
    },

    onConfirm() {
      // console.log(this.indexs, this.listindex, 'this.indexs, this.listindex')
      this.delImg(this.indexs, this.listindex)
      this.indexs = 0
      this.modalshow = false
      this.imgShow = false
    },
    onCancel() {
      this.modalshow = false
    },
    close() {
      this.imgShow = false

      // this.images = []
    },

    seeTheSample(val) {
      this.$router.push({
        path: '/returnedElite/sampleIdCard',
        query: {
          type: val
        }
      })
    },

    prompt() {
      this.iconText = !this.iconText
    },

    changeTab(val) {
      // console.log(val, 'val')
    },

    // 点击返回键
    async appGoBack() {
      const data = await callNative('prepage', { times: 1 })
    }
  }
}
</script>

<style lang='less' scoped >
::v-deep .van-cell__title {
  font-size: 16px;
  color: #000;
  font-family: 'Audi-Normal';
  // font-family: 'Audi-WideBold';
}

::v-deep .van-uploader {
  width: 33%;
  height: 112px;

}

::v-deep .van-cell__title {
  display: flex;
  align-items: center;

}

::v-deep .van-icon {
  display: flex;
  align-items: center;
}

::v-deep .van-uploader__preview-image {
  width: 100%;
  height: 112px;
}


::v-deep .van-uploader__upload {
  width: 100%;
  height: 112px;
}

::v-deep .van-cell {
  overflow: inherit;
}

::v-deep .van-collapse-item__title {

  height: 64px;
}

::v-deep .van-image-preview {
  /* 取消样式设置 */
  top: 37px;
  left: 0;
  right: 0;
  bottom: 0;
}

.img1 {
  width: 18px;
  height: 18px;
}

::v-deep .van-collapse-item__title {
  height: 55px;
}

.title-prompt {
  display: flex;
  align-items: center;
}

.tost-icon-text {
  position: absolute;
  top: 37px;
  left: 163px;
  height: 22px;
  font-family: 'Audi-Normal';
  font-size: 12px;
  color: #FFFFFF;
  background-image: url("../../assets/img/tost-text.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 3px 10px;
  z-index: 999;
}

.pop-up-layer {
  width: 32%;
  height: 112px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px #DDDDDD dotted;

  .pop-up-layer-connent {
    width: 36px;
    height: 36px;
    background: #E5E5E5;
    border-radius: 50%;
    text-align: center;
    line-height: 33px;
    filter: drop-shadow(1px 2px 4px hsl(0deg 0% 70%));

    .pop-up-layer-img {
      width: 24px;
      height: 24px;
    }
  }
}

.img_upload_item {
  width: 31%;
  margin-right: 7px;
  margin-bottom: 7px;
}

.img_upload {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
}

.hide_file {
  display: none;
}

.identity-sample {
  text-align: right;
  margin: 0;
  margin-bottom: 8px;
  font-family: 'Audi-Normal';
  font-size: 12px;
  color: #000000;
}

.identity {
  display: flex;
  justify-content: space-between;

  .identity-img {
    // display:flex;
    // justify-content: center;
    text-align: center;
    width: 48%;
    font-family: 'Audi-Normal';
    font-size: 12px;
    color: #000000;

    .item-img {
      height: 111px;
    }

    .item-img-i {
      height: 111px;
    }
  }
}

.delete-img {
  height: 52px;
  background: red;
  color: #eeeeee;
  position: fixed;
  bottom: 16px;
  // padding: 16px;
  left: 16px;
  z-index: 1;
  /* margin: 0 16px; */
  right: 16px;
  text-align: center;
  line-height: 52px;

}

.btn-delete-height {
  height: 80px;
}

.click-button {
  margin: 0px;
  text-align: center;
  height: 60px;
  line-height: 60px;
  border-bottom: 1px solid #CCCCCC;
  font-family: 'Audi-Normal';
  font-size: 16px;
}

.click-button-b {
  margin: 0px;
  text-align: center;
  height: 60px;
  line-height: 60px;
  font-family: 'Audi-Normal';
  font-size: 16px;
}

.btn-delete-wrapper {
  margin: 16px;
}

.btn-delete-wrapper-s{
  margin: 16px;
  background:#E5E5E5;
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  height: 56px;
  color:#FFFFFF
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: absolute;
  bottom: 0px;
  // padding: 16px;
  left: 0px;
}

.mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  // padding: 16px;
  left: 0;
  z-index: 9;

  .mask-drag {
    width: 100%;
    height: 100%;
  }
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 90%;
  height: 168px;
  // line-height: 120px;
  text-align: center;
  background-color: #fff;
  display:flex;
  flex-wrap: wrap;
    justify-content: center;

}

.block-text{
margin-bottom:0;
margin-top: 24px
}
.block-btn{
  width: 90%;
    color: #fff;
    background-color: #000;
    line-height: 56px;
    margin: 0;
    height: 56px;
}

.tost-fail {
  width: 178px;
  line-height: 46px;
  text-align: center;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9;
  background-color: #000000;
  color: #FFFFFF;
  font-size: 14px;
}

.image-delete {
  position: fixed;
  top: 50px;
  right: 12px;
  z-index: 3000;
}

.nijao{
  background:#FFFFFF;
  height: 44px;
  width:100%;
  position: fixed;
  top: 50px;
  // right: 12px;
  z-index: 3000;
  color:#000000;

}

._modal {
  width: 343px;
  background: #FFFFFF;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;

  .modal-title {
    font-size: 18px;
    font-weight: 500;
    color: #1A1A1A;
    margin-bottom: 16px;
    font-family: 'Audi-WideBold';
  }

  .modal-confirm {
    margin-top: 16px;
    width: 85%;
    height: 56px;
    background: #1A1A1A;
    font-size: 16px;
    color: #FFFFFF;
    text-align: center;
    line-height: 56px;
  }

  .modal-cancel {
    width: 85%;
    border: 1px solid #1A1A1A;
    height: 56px;
    background: #fff;
    font-size: 16px;
    color: #000;
    margin-top: 8px;
    text-align: center;
    line-height: 56px;
  }

  .modal-confirm center {
    font-size: 18px;
    color: #1A1A1A;
    line-height: 32px;
    padding: 0 25px;
    font-weight: normal;
    font-family: "Audi-WideBold";

  }
}
</style>
