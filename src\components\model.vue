<!-- 弹出框 -->
<template>
  <div @click="prevent">
    <van-popup
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal" style="white-space: pre-wrap">
        <div
          class="modal-title"
          v-if="!boldContent"
          v-html="title"
        >
          <!-- {{ title }} -->
        </div>
        <div
          class="modal-content"
          v-if="content && !boldContent"
        >
          {{ content }}
        </div>
        <div
          class="modal-bold-content"
          v-if="boldContent"
        >
          {{ boldContent }}
        </div>
        <div v-if="column">
          <div
            class="modal-confirm center"
            @click.stop="onConfirm"
          >
            {{ confirmText }}
          </div>
          <div
            class="modal-cancel center"
            @click.stop="onCancel"
          >
            {{ cancelText }}
          </div>
        </div>
        <div v-else class="align-button">
          <div
            class="modal-cancel center"
            @click.stop="onCancel"
          >
            {{ cancelText }}
          </div>
          <div
            class="modal-confirm center"
            @click.stop="onConfirm"
          >
            {{ confirmText }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { Popup } from 'vant'
import Vue from 'vue'

Vue.use(Popup)
export default {
  props: {
    modalshow: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      default: '是否取消关注？'
    },
    content: {
      type: String,
      default: ''
    },
    boldContent: { // 不需要标题 只暂时加重语气的内容
      type: String,
      default: ''
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    column:{
      type: Boolean,
      value: true
    }

  },
  methods: {
    onConfirm() {
      this.$emit('update:modalshow', true)
      this.$emit('onConfirm', true)
    },
    onCancel() {
      this.$emit('update:modalshow', false)
    },
    prevent() {
      event.stopPropagation()
    },
    
  }
}
</script>

<style lang="less" scoped>
  /deep/.van-popup {
    // height: 230px !important;
  }

  .center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ._modal {
    width: 343px;
    background: #FFFFFF;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    padding: 24px 0;

    .modal-title {
      font-size: 18px;
      font-weight: 500;
      color: #1A1A1A;
      margin-bottom: 12px;
      width: 100%;
      text-align: center;
      font-family: 'Audi-WideBold';
    }

    .modal-content {
      width: 100%;
      word-break:break-all;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
      padding: 0 16px;
      box-sizing: border-box;
      font-family: 'Audi-Normal';
    }

    .modal-confirm {
      margin-top: 24px;
      width: 85%;
      height: 56px;
      background: #1A1A1A;
      font-size: 16px;
      color: #FFFFFF;
    }

    .modal-cancel {
      width: 85%;
      border: 1px solid #1A1A1A;
      height: 56px;
      background: #fff;
      font-size: 16px;
      color: #000;
      margin-top: 5px;
    }

    .modal-bold-content {
      font-size: 18px;
      color: #1A1A1A;
      line-height: 32px;
      padding: 0 25px;
      font-weight: normal;
      font-family: "Audi-WideBold";
    }
  }
  .align-button{
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 15px;
    margin-top: 10px;
    justify-content: space-between;
    box-sizing: border-box;
    
    div{
      width: 48% !important;
      margin-top: 0 !important;
      box-sizing: border-box;
    }
  }
</style>
