import Vue from 'vue'
import Router from 'vue-router'
import { Loading } from 'vant'
import { callNative, getUrlParamObj } from '@/utils/'
import storage from '@/utils/storage'
import store from '../store/index'

const { env } = getUrlParamObj()

const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject)
  }
  return originalPush.call(this, location).catch((err) => err)
}
Vue.use(Router).use(Loading)
const routes = [
  {
    path: '*',
    redirect: '/order/detail'
  },
  {
    name: 'confirm-order',
    path: '/confirm-order',
    component: () => import('@/view/confirm-order/index.vue'),
    meta: {
      title: '系统消息'
    }
  },

  {
    name: 'theOptionDetails',
    path: '/theOptionDetails',
    component: () => import('@/view/configration/theOptionDetails.vue'),
    meta: {
      title: '权 益'
    }
  },

  {
    name: 'EquipmentBox',
    path: '/configration/EquipmentBox',
    component: () => import('@/view/configration/EquipmentBox.vue'),
    meta: {
      title: '选装组合'
    }
  },
  {
    name: 'carVersion',
    path: '/carVersion',
    component: () => import('@/view/configration/carVersion.vue'),
    meta: {
      title: '版本选择'
    }
  },

  {
    name: 'oldConfigration',
    path: '/oldConfigration',
    component: () => import('@/view/configration/index.vue'),
    meta: {
      title: '选择车辆'
    }
  },
  /**
   * 新版配车的首页
   */
  {
    name: 'Configration',
    path: '/configration',
    component: () => import('@/view/newConfigration/index.vue'),
    meta: {
      title: '选择车辆'
    }
  },
  {
    name: 'ConfigrationContainer',
    path: '/configrationContainer',
    component: () => import('@/view/newConfigration/container.vue'),
    meta: {
      title: '车辆配置'
    }
  },
  {
    name: 'commpendOptionList',
    path: '/commpendOptionList',
    component: () => import('@/view/newConfigration/option/commpendOptionList.vue'),
    meta: {
      title: '推荐组合'
    }
  },
  {
    name: 'configrationOptionDetail',
    path: '/configrationOptionDetail',
    component: () => import('@/view/newConfigration/option/optionDetail.vue'),
    meta: {
      title: '详情'
    }
  },
  {
    name: 'configrationEquityDetail',
    path: '/configrationEquityDetail',
    component: () => import('@/view/newConfigration/equityDetail.vue'),
    meta: {
      title: '权益详情'
    }
  },
  {
    name: 'CarParameter',
    path: '/carParameter',
    component: () => import('@/view/newConfigration/carParameter.vue'),
    meta: {
      title: '车辆参数'
    }
  },
  {
    name: 'configrationMobileCompatible',
    path: '/configrationMobileCompatible',
    component: () => import('@/view/newConfigration/mobileCompatible.vue'),
    meta: {
      title: '兼容机型'
    }
  },
  {
    name: 'modelDetailMofidy',
    path: '/modelDetailMofidy',
    component: () => import('@/view/newConfigration/modifyOrder.vue'),
    meta: {
      title: '配置与权益',
      showHeader: false
    }
  },

    /**
   * A5配车的首页
   */
  {
    name: 'a5lConfigrationContainer',
    path: '/a5lConfigrationContainer',
    component: () => import('@/view/a5lConfigration/container.vue'),
    meta: {
      title: '车辆配置'
    }
  },
  {
    name: 'a5lConfigrationOptionDetail',
    path: '/a5lConfigrationOptionDetail',
    component: () => import('@/view/a5lConfigration/option/optionDetail.vue'),
    meta: {
      title: '详情'
    }
  },
  {
    name: 'a5lConfigrationEquityDetail',
    path: '/a5lConfigrationEquityDetail',
    component: () => import('@/view/a5lConfigration/equityDetail.vue'),
    meta: {
      title: '权益详情'
    }
  },
  {
    name: 'a5lCarParameter',
    path: '/car/parameter/a5l',
    component: () => import('@/view/a5lConfigration/carParameter.vue'),
    meta: {
      title: '车辆参数'
    }
  },
  {
    name: 'a5lConfigrationMobileCompatible',
    path: '/a5lConfigrationMobileCompatible',
    component: () => import('@/view/a5lConfigration/mobileCompatible.vue'),
    meta: {
      title: '车辆推荐'
    }
  },
  {
    name: 'a5lmodelDetailMofidy',
    path: '/a5lmodelDetailMofidy',
    component: () => import('@/view/a5lConfigration/modifyOrder.vue'),
    meta: {
      title: '配置与权益',
      showHeader: false
    }
  },
  {
    name: 'HotRecommended',
    path: '/hot-recommended',
    redirect: {
      name: 'Configration',
      path: '/configration',
      component: () => import('@/view/newConfigration/index.vue'),
      meta: {
        title: '选择车辆'
      }
    },
    component: () => import('@/view/configration/hot-recommended.vue'),
    meta: {
      title: '车辆推荐'
    }
  },
  {
    name: 'tabDeliveryPage',
    path: '/tabDeliveryPage',
    component: () => import('@/view/configration/tabDeliveryPage.vue'),
    meta: {
      title: '选择交付周期'
    }
  },
  {
    name: 'OptionDetail',
    path: '/optiondetail',
    component: () => import('@/view/configration/optionDetail.vue')
    // meta: {
    //   title: '选择车型'
    // }
  },
  {
    name: 'optionDetailPlus',
    path: '/optionDetailPlus',
    component: () => import('@/view/configration/optionDetailPlus.vue')
    // meta: {
    //   title: '选择车型'
    // }
  },
  {
    name: 'FinancialCalculator',
    path: '/configration/financial-calculator',
    component: () => import('@/view/configration/financial-calculator-new.vue'),
    meta: {
      title: '计算器'
    }
  },
  {
    name: 'modelCompatible',
    path: '/configration/modelCompatible',
    component: () => import('@/view/configration/model-compatible.vue'),
    meta: {
      title: '兼容机型'
    }
  },
  {
    name: 'SelectModel',
    path: '/configration/select-model',
    component: () => import('@/view/configration/select-model.vue'),
    meta: {
      title: '特选车'
    }
  },
  {
    name: 'SelectModel2',
    path: '/configration/select-model2',
    component: () => import('@/view/configration/select-model2.vue'),
    meta: {
      title: '更改车型'
    }
  },
  {
    name: 'LegalProvision',
    path: '/configration/legal-provision',
    component: () => import('@/view/configration/legal-provision.vue'),
    meta: {
      title: '法律条款'
    }
  },

  {
    name: 'quotation',
    path: '/quotation',
    component: () => import('@/view/quotation/index.vue'),
    meta: {
      title: '报价单',
      showHeader: false
    }
  },

  {
    name: 'models-contrast',
    path: '/quotation/models-contrast',
    component: () => import('@/view/quotation/models-contrast.vue'),
    meta: {
      title: '车型对比',
      showHeader: false
    }
  },
  {
    name: 'models-contrast2',
    path: '/quotation/models_contrast',
    component: () => import('@/view/quotation/models-contrast2.vue'),
    meta: {
      title: '车型对比'
    }
  },
  {
    name: 'detail',
    path: '/order/detail',
    component: () => import('@/view/order/detail.vue'),
    meta: {
      title: '完善购买信息'
    }
  },
  {
    name: 'payment-success',
    path: '/order/payment-success',
    component: () => import('@/view/order/payment-success.vue'),
    meta: {
      title: '支付成功'
    }
  },
  // {
  //   name: 'other-payment-success',
  //   path: '/other-payment-success',
  //   component: () => import('@/view/order/other-payment-success.vue'),
  //   meta: {
  //     title: '支付成功'
  //   }
  // },

  {
    name: 'money-detail',
    path: '/order/money-detail',
    component: () => import('@/view/order/money-detail.vue'),
    meta: {
      title: '整车订单'
    }
  },
  //nga1.1 新订单
  {
    name: 'new-money-detail',
    path: '/order/new-money-detail',
    component: () => import('@/view/order/new-money-detail.vue'),
    meta: {
      title: '整车订单'
    }
  },
  {
    name: 'model-detail',
    path: '/order/model-detail',
    component: () => import('@/view/order/model-detail.vue'),
    meta: {
      title: '整车订单'
    }
  },
  {
    name: 'goods-order-list',
    path: '/order/goods-order-list',
    component: () => import('@/view/order/goods-order-list.vue'),
    meta: {
      title: '商品订单列表',
      showHeader: false
    }
  },
  {
    name: 'goods-order-detail',
    path: '/order/goods-order-detail',
    component: () => import('@/view/order/goods-order-detail.vue'),
    meta: {
      title: '订单详情' // 商品订单详情
    }
  },
  {
    name: 'information-cause',
    path: '/order/information-cause',
    component: () => import('@/view/order/information-cause.vue'),
    meta: {
      title: '退订原因'
    }
  },
  {
    name: 'unsubscribe-success',
    path: '/order/unsubscribe-succeed',
    component: () => import('@/view/order/unsubscribe-succeed.vue'),
    meta: {
      title: '退订成功'
    }
  },
  {
    name: 'refund-success',
    path: '/order/refund-success',
    component: () => import('@/view/order/unsubscribe-succeed.vue'),
    meta: {
      title: '退款成功'
    }
  },
  {
    name: 'user-agreement',
    path: '/order/user-agreement',
    component: () => import('@/view/order/user-agreement.vue'),
    meta: {
      title: '意向金协议'
    }
  },
  {
    name: 'new-user-agreement',
    path: '/order/new-user-agreement',
    component: () => import('@/view/order/new-user-agreement.vue'),
    meta: {
      title: '意向金协议'
    }
  },
  {
    name: 'limit-number',
    path: '/order/limit-number',
    component: () => import('@/view/order/limit-number.vue'),
    meta: {
      title: '限量号'
    }
  },
  {
    name: 'payment-error',
    path: '/order/payment-error',
    component: () => import('@/view/order/payment-error.vue'),
    meta: {
      title: '支付成功'
    }
  },
  {
    name: 'pay-flow',
    path: '/order/pay-flow',
    component: () => import('@/view/order/pay-flow.vue'),
    meta: {
      title: '数字人民币支付流程'
    }
  },
  // {
  //   name: "service-regulations",
  //   path: "/order/service-regulations",
  //   component: () => import('@/view/order/service-regulations.vue'),
  //   meta: {
  //     title: "数字人民币使用规则"
  //   }
  // },
  {
    name: 'upload-documents',
    path: '/order/upload-documents',
    component: () => import('@/view/order/upload-documents.vue'),
    meta: {
      title: '上传支付凭证'
    }
  },
  {
    name: 'submit-success',
    path: '/order/submit-success',
    component: () => import('@/view/order/submit-success.vue'),
    meta: {
      title: '提交成功'
    }
  },

  // tag del ?
  {
    name: 'rights-detail',
    path: '/order/rights-detail',
    component: () => import('@/view/order/rights-detail.vue'),
    meta: {
      title: '权益详情'
    }
  },
  {
    name: 'carconfig-detail',
    path: '/order/carconfig-detail',
    component: () => import('@/view/order/carconfig-detail.vue'),
    meta: {
      title: '车辆配置详情'
    }
  },
  {
    name: 'dealerList',
    path: '/dealerList',
    component: () => import('@/view/dealer/list.vue'),
    meta: {
      title: '选择代理商'
    }
  },
  {
    name: 'equity-detail',
    path: '/equity-detail',
    component: () => import('@/view/order/equity-detail.vue'),
    meta: {
      title: '权益'
    }
  },
  {
    name: 'theEquity',
    path: '/theEquity',
    component: () => import('@/view/order/equity-detail-plus.vue'),
    meta: {
      title: ''
    }
  },
  {
    name: 'equity-detail-moreinfo',
    path: '/equity-detail-moreinfo',
    component: () => import('@/view/order/equity-detail-moreinfo.vue'),
    meta: {
      title: '先行权益详情'
    }
  },
  {
    name: 'order-list',
    path: '/order/order-list',
    component: () => import('@/view/order/order-list.vue'),
    meta: {
      title: '订单列表',
      showHeader: false
    }
  },

  // 已经迁移到mall项目,待删除
  {
    name: 'shopping-cart-list',
    path: '/order/shopping-cart-list',
    component: () => import('@/view/order/shopping-cart-list.vue'),
    meta: {
      title: '购物车列表',
      showHeader: false
    }
  },
  {
    name: 'shopping-cart',
    path: '/order/shopping-cart',
    component: () => import('@/view/order/shopping-cart.vue'),
    meta: {
      title: '我的购物车',
      showHeader: false
    }
  },
  {
    name: 'appointment-list',
    path: '/testdrive/appointment-list',
    component: () => import('@/view/testdrive/appointment-list.vue'),
    meta: {
      title: '我的预约'
    }
  },
  {
    name: 'TestdriverCreate',
    path: '/testdrive/create',
    component: () => import('@/view/testdrive/createBox.vue'),
    meta: {
      title: '预约试驾',
      keepAlive: true
    }
  },
  {
    name: 'car-malls',
    path: '/car-malls',
    component: () => import('@/view/testdrive/car-malls.vue'),
    meta: {
      title: '授权代理商'
    }
  },
  {
    name: 'testdrive_detail',
    path: '/testdrive/detail',
    component: () => import('@/view/testdrive/detail.vue'),
    meta: {
      title: '到店试驾订单'
    }
  },
  {
    name: 'UploadLicense',
    path: '/testdrive/upload-license',
    component: () => import('@/view/testdrive/upload-license'),
    meta: {
      title: '上传证件'
    }
  },
  {
    name: 'select-testdrive-type',
    path: '/testdrive/select-testdrive-type',
    component: () => import('@/view/testdrive/select-testdrive-type'),
    meta: {
      title: '请选择试驾类型'
    }
  },
  {
    name: 'Report',
    path: '/testdrive/report',
    component: () => import('@/view/testdrive/report'),
    meta: {
      title: '试驾报告'
    }
  },
  {
    name: 'Way',
    path: '/testdrive/way',
    component: () => import('@/view/testdrive/way'),
    meta: {
      title: '路线选择'
    }
  },
  {
    name: 'Evaluation',
    path: '/testdrive/evaluation',
    component: () => import('@/view/testdrive/evaluation'),
    meta: {
      title: '评价'
    }
  },
  {
    name: 'cancel-success',
    path: '/testdrive/cancel-success',
    component: () => import('@/view/testdrive/cancel-success.vue'),
    meta: {
      title: '预约取消成功'
    }
  },
  {
    name: 'agent-list',
    path: '/testdrive/agent-list',
    component: () => import('@/view/testdrive/agent-list'),
    meta: {
      title: '选择代理商'
    }
  },
  {
    name: 'configuration',
    path: '/testdrive/configuration',
    component: () => import('@/view/testdrive/configuration'),
    meta: {
      title: '配置表'
    }
  },
  {
    name: 'activity-registration',
    path: '/activity-registration',
    component: () => import('@/view/testdrive/activity-registration'),
    meta: {
      title: '活动报名'
    }
  },
  {
    name: 'registration-success',
    path: '/registration-success',
    component: () => import('@/view/testdrive/registration-success'),
    meta: {
      title: '活动报名'
    }
  },
  {
    name: 'contract-info',
    path: '/contract-info',
    component: () => import('@/view/contract/index.vue'),
    meta: {
      title: '汽车购买协议'
    }
  },
  {
    name: 'contract-status',
    path: '/contract-status',
    component: () => import('@/view/contract/status.vue'),
    meta: {
      title: '合同状态'
    }
  },
  {
    name: 'upload-contract',
    path: '/upload-contract',
    component: () => import('@/view/contract/upload.vue'),
    meta: {
      title: '上传合同'
    }
  },
  {
    name: 'finance-list',
    path: '/finance-list',
    component: () => import('@/view/finance/list.vue'),
    meta: {
      title: '金融服务'
    }
  },
  {
    name: 'carConfigTable',
    path: '/car-config-table',
    component: () => import('@/view/quotation/car-config-table.vue'),
    meta: {
      title: '参数表'
    }
  },
  {
    name: 'dading-success',
    path: '/dading-success',
    component: () => import('@/view/order/dading-success.vue'),
    meta: {
      title: '支付成功'
    }
  },
  {
    name: 'order-info-modify',
    path: '/order-info-modify',
    component: () => import('@/view/order/order-info-modify.vue'),
    meta: {
      title: '完善购买信息'
    }
  },
  {
    name: 'config-pdf',
    path: '/config-pdf',
    component: () => import('@/view/quotation/config-pdf.vue'),
    meta: {
      title: '全部车辆参数表'
    }
  },
  {
    name: 'buycar-agreement',
    path: '/buycar-agreement',
    component: () => import('@/view/order/buycar-agreement.vue'),
    meta: {
      title: '购车协议'
    }
  },
  {
    name: 'order-balance',
    path: '/order/balance',
    component: () => import('@/view/order/balance.vue'),
    meta: {
      title: '尾款支付'
    }
  },
  {
    name: 'order-balance-uploader',
    path: '/order/balance-uploader',
    component: () => import('@/view/order/balance-uploader.vue'),
    meta: {
      title: '上传支付凭证'
    }
  },
  {
    name: 'order-balance-success',
    path: '/order/balance-success',
    component: () => import('@/view/order/balance-success.vue'),
    meta: {
      title: '提交成功'
    }
  },

  {
    name: 'car-config-other',
    path: '/car-config-other',
    component: () => import('@/view/quotation/car-config-other.vue'),
    meta: {
      title: '奥迪A7L',
      showHeader: false
    }
  },

  {
    name: 'service-order-list',
    path: '/aftersales/service-order-list',
    component: () => import('@/view/aftersales/serviceorder/new-service-order-list.vue'),
    meta: {
      title: '服务订单列表',
      showHeader: false
    }
  },

  {
    name: 'afterservice-order-list',
    path: '/aftersales/afterservice-order-list',
    component: () => import('@/view/aftersales/service-order-list.vue'),
    meta: {
      title: '服务订单列表'
    }
  },

  {
    name: 'insaicservice-order-list',
    path: '/aftersales/insaicservice-order-list',
    component: () => import('@/view/aftersales/serviceorder/insaic-service-order-list.vue'),
    meta: {
      title: '出行服务列表'
    }
  },

  {
    name: 'road-service-order-list',
    path: '/aftersales/roadservice-order-list',
    component: () => import('@/view/aftersales/serviceorder/road-service-order-list.vue'),
    meta: {
      title: '道路救援列表'
    }
  },

  {
    name: 'select-models-list',
    path: '/aftersales/select-models-list',
    component: () => import('@/view/aftersales/select-models-list.vue'),
    meta: {
      title: '选择车型'
    }
  },
  {
    name: 'select-series-list',
    path: '/aftersales/select-series-list',
    component: () => import('@/view/aftersales/select-series-list.vue'),
    meta: {
      title: '选择车系'
    }
  },

  {
    name: 'service-success',
    path: '/aftersales/service-success',
    component: () => import('@/view/aftersales/service-success.vue'),
    meta: {
      title: '提交成功'
    }
  },
  {
    name: 'service-appointment-one',
    path: '/aftersales/service-appointment-one',
    component: () => import('@/view/aftersales/service-appointment-one.vue'),
    meta: {
      title: '服务预约 (1/2)'
    }
  },
  {
    name: 'service-appointment-two',
    path: '/aftersales/service-appointment-two',
    component: () => import('@/view/aftersales/service-appointment-two.vue'),
    meta: {
      title: '服务预约 (2/2)'
    }
  },
  {
    name: 'service-appointment-order-detail',
    path: '/aftersales/service-appointment-order-detail',
    component: () => import('@/view/aftersales/service-appointment-order-detail.vue'),
    meta: {
      title: '服务预约订单详情'
    }
  },
  {
    name: 'affirm-order',
    path: '/aftersales/affirm-order',
    component: () => import('@/view/aftersales/affirm-order.vue'),
    meta: {
      title: '确认订单'
    }
  },
  {
    name: 'select-service-providers-list',
    path: '/aftersales/select-service-providers-list',
    component: () => import('@/view/aftersales/select-service-providers-list.vue'),
    meta: {
      title: '选择服务商',
      showHeader: false
    }
  },
  {
    name: 'service-type',
    path: '/aftersales/service-type',
    component: () => import('@/view/aftersales/service-type.vue'),
    meta: {
      title: '服务类型'
    }
  },
  {
    name: 'select-time-appoin',
    path: '/aftersales/select-time-appoin',
    component: () => import('@/view/aftersales/select-time-appoin.vue'),
    meta: {
      title: '预约时间'
      // showHeader: false
    }
  },
  {
    name: 'select-time-arbitrar',
    path: '/aftersales/select-time-arbitrar',
    component: () => import('@/view/aftersales/select-time-arbitrar.vue'),
    meta: {
      title: '预约时间'
      // showHeader: false
    }
  },
  {
    name: 'service-state',
    path: '/aftersales/service-state',
    component: () => import('@/view/aftersales/service-state.vue'),
    meta: {
      title: '服务说明'
    }
  },
  {
    name: 'upkeep-detail',
    path: '/aftersales/upkeep-detail',
    component: () => import('@/view/aftersales/upkeep-detail.vue'),
    meta: {
      title: '保养详情'
    }
  },
  {
    name: 'create-deliver-car',
    path: '/aftersales/create-deliver-car',
    component: () => import('@/view/aftersales/create-deliver-car.vue'),
    meta: {
      title: '取送车服务'
    }
  },
  {
    name: 'send-car-price',
    path: '/aftersales/send-car-price',
    component: () => import('@/view/aftersales/send-car-price.vue'),
    meta: {
      title: '取送车价格明细'
    }
  },
  // 取送车选择服务商
  {
    name: 'select-car-service-providers-list',
    path: '/aftersales/select-car-service-providers-list',
    component: () => import('@/view/aftersales/select-car-service-providers-list.vue'),
    meta: {
      title: '选择服务商',
      showHeader: false
    }
  },
  {
    name: 'seek-agent',
    path: '/aftersales/seek-agent',
    component: () => import('@/view/aftersales/seek-agent.vue'),
    meta: {
      title: '查找代理商',
      showHeader: false
    }
  },
  {
    name: 'SeekStore',
    path: '/aftersales/seek-store',
    component: () => import('@/view/aftersales/seek-store.vue'),
    meta: {
      title: '查找门店'
    }
  },
  {
    name: 'deliver-car-order-detail',
    path: '/aftersales/deliver-car-order-detail',
    component: () => import('@/view/aftersales/deliver-car-order-detail.vue'),
    meta: {
      title: '取送车订单详情'
    }
  },
  {
    name: 'aftersales-map',
    path: '/aftersales/aftersales-map',
    component: () => import('@/view/aftersales/aftersales-map.vue'),
    meta: {
      title: '地图demo'
    }
  },

  {
    name: 'select-user-address',
    path: '/aftersales/select-user-address',
    component: () => import('@/view/aftersales/select-user-address.vue'),
    meta: {
      title: '选择地址'
    }
  },
  {
    name: 'order-status',
    path: '/aftersales/order-status',
    component: () => import('@/view/aftersales/order-status.vue'),
    meta: {
      title: '订单状态'
    }
  },
  {
    name: 'input-car-no',
    path: '/aftersales/input-car-no',
    component: () => import('@/view/aftersales/input-car-no.vue'),
    meta: {
      title: '输入车牌号'
    }
  },

  {
    name: 'cancel-service-order',
    path: '/aftersales/cancel-service-order',
    component: () => import('@/view/aftersales/cancel-service-order.vue'),
    meta: {
      title: '取消订单'
    }
  },
  {
    name: 'my-confirm',
    path: '/certification/my-confirm',
    component: () => import('@/view/certification/my-confirm.vue'),
    meta: {
      title: '信息确认'
    }
  },
  {
    name: 'rich-text',
    path: '/certification/rich-text',
    component: () => import('@/view/certification/rich-text.vue'),
    meta: {
      title: '隐私协议'
    }
  },
  {
    name: 'my-certification',
    path: '/certification/my-certification',
    component: () => import('@/view/certification/my-certification.vue'),
    meta: {
      title: '我的爱车'
    }
  },
  {
    name: 'identity-certification',
    path: '/certification/identity-certification',
    component: () => import('@/view/certification/identity-certification.vue'),
    meta: {
      title: '身份认证'
    }
  },
  {
    name: 'identity-certification-status',
    path: '/certification/identity-certification-status',
    component: () => import('@/view/certification/identity-certification-status.vue'),
    meta: {
      title: '身份认证'
    }
  },
  {
    name: 'identity-certification-in',
    path: '/certification/identity-certification-in',
    component: () => import('@/view/certification/identity-certification-in.vue'),
    meta: {
      title: '身份认证'
    }
  },
  // 身份认证（其他认证）
  {
    name: 'else-certification',
    path: '/certification/else-certification',
    component: () => import('@/view/certification/else-certification.vue'),
    meta: {
      title: '身份认证'
    }
  },
  {
    name: 'car-certification',
    path: '/certification/car-certification',
    component: () => import('@/view/certification/car-certification.vue'),
    meta: {
      title: '车辆认证'
    }
  },
  {
    name: 'driving-certification',
    path: '/certification/driving-certification',
    component: () => import('@/view/certification/driving-certification.vue'),
    meta: {
      title: '行驶证'
    }
  },
  {
    name: 'driving-certification-info',
    path: '/certification/driving-certification-info',
    component: () => import('@/view/certification/driving-certification-info.vue'),
    meta: {
      title: '行驶证信息'
    }
  },
  {
    name: 'driving-certification-error',
    path: '/certification/driving-certification-error',
    component: () => import('@/view/certification/driving-certification-error.vue'),
    meta: {
      title: '认证失败'
    }
  },
  {
    name: 'car-certification-error',
    path: '/certification/car-certification-error',
    component: () => import('@/view/certification/car-certification-error.vue'),
    meta: {
      title: '认证失败'
    }
  },
  {
    name: 'input-car-no',
    path: '/certification/input-car-no',
    component: () => import('@/view/certification/components/input-car-no.vue'),
    meta: {
      title: '输入车牌号'
    }
  },
  {
    name: 'car-certification-info',
    path: '/certification/car-certification-info',
    component: () => import('@/view/certification/car-certification-info.vue'),
    meta: {
      title: '车辆信息'
    }
  },
  {
    name: 'identity-certification-info',
    path: '/certification/identity-certification-info',
    component: () => import('@/view/certification/identity-certification-info.vue'),
    meta: {
      title: '身份信息'
    }
  },
  {
    name: 'car-certification-in',
    path: '/certification/car-certification-in',
    component: () => import('@/view/certification/car-certification-in.vue'),
    meta: {
      title: '车辆认证'
    }
  },
  {
    name: 'driving-certification-in',
    path: '/certification/driving-certification-in',
    component: () => import('@/view/certification/driving-certification-in.vue'),
    meta: {
      title: '车辆认证'
    }
  },
  {
    name: 'car-unbind-confirm',
    path: '/certification/car-unbind-confirm',
    component: () => import('@/view/certification/car-unbind-confirm.vue'),
    meta: {
      title: '解除绑定'
    }
  },

  {
    name: 'select-equities',
    path: '/charging/select-equities',
    component: () => import('@/view/charging/select-equities.vue'),
    meta: {
      title: '选择权益'
    }
  },
  {
    name: 'select-charging-pile',
    path: '/charging/select-charging-pile',
    component: () => import('@/view/charging/select-charging-pile.vue'),
    meta: {
      title: '选择充电桩'
    }
  },
  {
    name: 'charging-pile-success',
    path: '/charging/charging-pile-success',
    component: () => import('@/view/charging/charging-pile-success.vue'),
    meta: {
      title: '兑换成功'
    }
  },
  {
    name: 'install-create',
    path: '/charging/install-create',
    component: () => import('@/view/charging/install-create.vue'),
    meta: {
      title: '安装信息'
    }
  },
  {
    name: 'install-info',
    path: '/charging/install-info',
    component: () => import('@/view/charging/install-info.vue'),
    meta: {
      title: '充电桩安装详情',
      showHeader: false
    }
  },
  {
    name: 'install-info-step',
    path: '/charging/install-info-step',
    component: () => import('@/view/charging/install-info-step.vue'),
    meta: {
      title: '充电桩安装详情'
    }
  },
  {
    name: 'install-service-guide',
    path: '/charging/install-service-guide',
    component: () => import('@/view/charging/install-service-guide.vue'),
    meta: {
      title: '安装服务指引'
    }
  },
  {
    name: 'car-guide-index',
    path: '/dutyfreecar',
    component: () => import('@/view/dutyfreecar/index.vue'),
    meta: {
      title: '购车指南',
      showHeader: false
    }
  },
  {
    name: 'car-guide',
    path: '/dutyfreecar/car-guide',
    component: () => import('@/view/dutyfreecar/car-guide.vue'),
    meta: {
      title: '购车指南',
      showHeader: false
    }
  },
  {
    name: 'data-submit',
    path: '/dutyfreecar/data-submit',
    component: () => import('@/view/dutyfreecar/data-submit.vue'),
    meta: {
      title: '留学生购车',
      showHeader: false
    }
  },
  {
    name: 'upload-certificate',
    path: '/dutyfreecar/upload-certificate',
    component: () => import('@/view/dutyfreecar/upload-certificate.vue'),
    meta: {
      title: '上传证件',
      showHeader: false
    }
  },
  {
    name: 'example',
    path: '/dutyfreecar/example',
    component: () => import('@/view/dutyfreecar/example.vue'),
    meta: {
      title: '示例'
    }
  },
  {
    name: 'success',
    path: '/dutyfreecar/success',
    component: () => import('@/view/dutyfreecar/success.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'dutyfreecar-agreement',
    path: '/dutyfreecar/agreement',
    component: () => import('@/view/dutyfreecar/agreement.vue'),
    meta: {
      title: '授权书'
    }
  },

  {
    name: 'long-create',
    path: '/testdrive/long-create',
    component: () => import('@/view/testdrive/long-create.vue'),
    meta: {
      title: '试驾预约'
    }
  },
  {
    name: 'long-create-success',
    path: '/testdrive/long-create-success',
    component: () => import('@/view/testdrive/long-create-success.vue'),
    meta: {
      title: '预约试驾成功'
    }
  },
  {
    name: 'long-detail',
    path: '/testdrive/long-detail',
    component: () => import('@/view/testdrive/long-detail.vue'),
    meta: {
      title: '超长试驾订单'
    }
  },
  {
    name: 'long-service-detail',
    path: '/testdrive/long-service-detail',
    component: () => import('@/view/testdrive/long-service-detail.vue'),
    meta: {
      title: '预约试驾订单'
    }
  },
  {
    name: 'long-service-list',
    path: '/testdrive/long-service-list',
    component: () => import('@/view/testdrive/long-service-list.vue'),
    meta: {
      title: '超长试驾服务'
    }
  },
  {
    name: 'long-select-date',
    path: '/testdrive/long-select-date',
    component: () => import('@/view/testdrive/long-select-date.vue'),
    meta: {
      title: '开始日期'
    }
  },

  {
    name: 'long-upload-drivinglicence',
    path: '/testdrive/long-upload-drivinglicence',
    component: () => import('@/view/testdrive/long-upload-drivinglicence.vue'),
    meta: {
      title: '上传证件'
    }
  },
  {
    name: 'long-report',
    path: '/testdrive/long-report',
    component: () => import('@/view/testdrive/long-report'),
    meta: {
      title: '试驾报告'
    }
  },

  {
    name: 'check-contract',
    path: '/testdrive/check-contract',
    component: () => import('@/view/testdrive/check-contract.vue'),
    meta: {
      title: '超长试驾协议'
    }
  },
  {
    name: 'check-img',
    path: '/testdrive/check-img',
    component: () => import('@/view/testdrive/check-img.vue'),
    meta: {
      title: '文件预览'
    }
  },
  {
    name: 'delivery-pattern',
    path: '/delivery-pattern',
    component: () => import('@/view/delivery/pattern.vue'),
    meta: {
      title: '交车方式'
    }
  },
  {
    name: 'delivery-contract',
    path: '/delivery-contract',
    component: () => import('@/view/delivery/contract.vue'),
    meta: {
      title: '交车确认书模板'
    }
  },
  {
    name: 'delivery-status',
    path: '/delivery-status',
    component: () => import('@/view/delivery/status.vue'),
    meta: {
      title: '合同状态'
    }
  },
  {
    name: 'delivery-upload',
    path: '/delivery-upload',
    component: () => import('@/view/delivery/upload.vue'),
    meta: {
      title: '上传合同'
    }
  },
  {
    name: 'confirm-order',
    path: '/confirm-order',
    component: () => import('@/view/product/confirm-order.vue'),
    meta: {
      title: '确认订单'
    }
  },
  {
    name: 'product-detail',
    path: '/product-detail',
    component: () => import('@/view/product/detail.vue'),
    meta: {
      title: '商品详情',
      showHeader: false
    }
  },
  {
    name: 'product-invoice',
    path: '/product-invoice',
    component: () => import('@/view/product/invoice.vue'),
    meta: {
      title: '开具发票'
    }
  },
  {
    name: 'logistics-status',
    path: '/logistics-status',
    component: () => import('@/view/order/logistics-status.vue'),
    meta: {
      title: '订单状态'
    }
  },
  {
    name: 'guarantees-voucher',
    path: '/guarantees-voucher',
    component: () => import('@/view/order/guarantees-voucher.vue'),
    meta: {
      title: '三包凭证'
    }
  },
  {
    name: 'finance-programme',
    path: '/finance-programme',
    component: () => import('@/view/finance/programme.vue'),
    meta: {
      title: '金融服务须知'
    }
  },

  // 徽章墙
  {
    name: 'badge-wall',
    path: '/wall/badge-wall/:userId',
    component: () => import('@/view/wall/badge-wall.vue'),
    meta: {
      title: '徽章墙'
    }
  },
  {
    name: 'identity-wall',
    path: '/wall/identity-wall/:userId',
    component: () => import('@/view/wall/identity-wall.vue'),
    meta: {
      title: '身份墙'
    }
  },

  // 徽章墙详情
  {
    name: 'badge-detail',
    path: '/wall/badge-detail/:userId/:badgeId/:levelName',
    component: () => import('@/view/wall/badge-detail.vue')
  },

  // 身份徽章详情
  {
    name: 'identity-detail',
    path: '/wall/identity-detail/:userId/:badgeId/:levelName',
    component: () => import('@/view/wall/identity-detail.vue')
  },
  {
    name: 'lucky-bag-cars',
    path: '/lucky-bag-cars',
    component: () => import('@/view/lucky-bag/index.vue'),
    meta: {
      title: '进取周岁福包'
    }
  },
  {
    name: 'lucky-bag-cars-lottery',
    path: '/lucky-bag-cars/lottery',
    component: () => import('@/view/lucky-bag/lottery.vue'),
    meta: {
      title: '活动详情'
    }
  },
  {
    name: 'lucky-bag-cars-details',
    path: '/lucky-bag-cars/details',
    component: () => import('@/view/lucky-bag/details.vue'),
    meta: {
      title: '福包详情'
    }
  },
  {
    name: 'lucky-bag-cars-rule-node',
    path: '/lucky-bag-cars/rule-node',
    component: () => import('@/view/lucky-bag/rule-node.vue'),
    meta: {
      title: '福包规则'
    }
  },
  {
    name: 'limited-number-q5',
    path: '/limited-number/select-number/q5',
    component: () => import('@/view/limited-number/select-number/series/index.vue'),
    meta: {
      title: '限量号'
    }
  },
  {
    name: 'limited-number-q6',
    path: '/limited-number/select-number/q6',
    component: () => import('@/view/limited-number/select-number/series/number.q6.vue'),
    meta: {
      title: '限量号'
    }
  },
  {
    name: 'limited-number-credentials',
    path: '/limited-number/credentials',
    component: () => import('@/view/limited-number/credentials/index.vue'),
    meta: {
      title: '限量号'
    }
  },
  // 国资委用户
  {
    name: 'enterprise-users',
    path: '/enterpriseUsers/certification',
    component: () => import('@/view/enterpriseUsers/certification.vue'),
    meta: {
      title: '',
      showHeader: false
    }
  },
  {
    name: 'basic-information',
    path: '/enterpriseUsers/basicInformation',
    component: () => import('@/view/enterpriseUsers/basicInformation.vue'),
    meta: {
      title: '基本信息',
      showHeader: false
    }
  },
  {
    name: 'data-upload',
    path: '/enterpriseUsers/dataUpload',
    component: () => import('@/view/enterpriseUsers/dataUpload.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'sample-id-card',
    path: '/enterpriseUsers/sampleIdCard',
    component: () => import('@/view/enterpriseUsers/sampleIdCard.vue'),
    meta: {
      title: '示例',
      showHeader: false
    }
  },
  {
    name: 'uploaded-success',
    path: '/enterpriseUsers/uploadedSuccess',
    component: () => import('@/view/enterpriseUsers/uploadedSuccess.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'enterpriseUsers-agreement',
    path: '/enterpriseUsers/agreements',
    component: () => import('@/view/enterpriseUsers/agreements.vue'),
    meta: {
      title: '授权书'
    }
  },

  {
    name: 'limited-number-rule-note',
    path: '/limited-number/rule-note',
    component: () => import('@/view/limited-number/rule-note/index.vue'),
    meta: {
      title: '规则'
    }
  },
  {
    name: 'limited-number-succeed',
    path: '/limited-number/succeed',
    component: () => import('@/view/limited-number/finished/index.vue'),
    meta: {
      title: '限量号'
    }
  },
  {
    name: 'order-verification-coupons',
    path: '/order/verification-coupons',
    component: () => import('@/view/order/verification-coupons.vue'),
    meta: {
      title: '尾款支付'
    }
  },
  {
    name: 'storeAddress',
    path: '/aftersales/store-address',
    component: () => import('@/view/aftersales/store-address.vue'),
    meta: {
      title: '查询渠道商'
    }
  },
  {
    name: 'rights-sales-card-voucher',
    path: '/rights/sales-card-voucher/index',
    component: () => import('@/view/rights/sales-card-voucher/index.vue'),
    meta: {
      title: '专属权益'
    }
  },
  {
    name: 'exception-consoles-page',
    path: '/exception/consoles-page',
    component: () => import('@/view/exception/consoles-page/index.vue'),
    meta: {
      title: ''
    }
  },
  {
    name: 'long-test-drive-state',
    path: '/testdrive/long-test-drive-state',
    component: () => import('@/view/testdrive/long-test-drive-state.vue'),
    meta: {
      title: '超长试驾'
    }
  },

  // 进取精英
  {
    name: 'enterprisingElite',
    path: '/enterprisingElite/certification',
    component: () => import('@/view/enterprisingElite/certification.vue'),
    meta: {
      title: '',
      showHeader: false
    }
  },
  {
    name: 'enterprisingElite-basic-information',
    path: '/enterprisingElite/basicInformation',
    component: () => import('@/view/enterprisingElite/basicInformation.vue'),
    meta: {
      title: '基本信息',
      showHeader: false
    }
  },
  {
    name: 'enterprisingElite-data-upload',
    path: '/enterprisingElite/dataUpload',
    component: () => import('@/view/enterprisingElite/dataUpload.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'enterprisingElite-sample-id-card',
    path: '/enterprisingElite/sampleIdCard',
    component: () => import('@/view/enterprisingElite/sampleIdCard.vue'),
    meta: {
      title: '示例',
      showHeader: false
    }
  },

  {
    name: 'enterprisingElite-uploaded-success',
    path: '/enterprisingElite/uploadedSuccess',
    component: () => import('@/view/enterprisingElite/uploadedSuccess.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'enterprisingElite-agreement',
    path: '/enterprisingElite/agreements',
    component: () => import('@/view/enterprisingElite/agreements.vue'),
    meta: {
      title: '授权书'
    }
  },

  // 供应商
  {
    name: 'supplier',
    path: '/supplier/certification',
    component: () => import('@/view/supplier/certification.vue'),
    meta: {
      title: '',
      showHeader: false
    }
  },
  {
    name: 'supplier-basic-information',
    path: '/supplier/basicInformation',
    component: () => import('@/view/supplier/basicInformation.vue'),
    meta: {
      title: '基本信息',
      showHeader: false
    }
  },
  {
    name: 'supplier-data-upload',
    path: '/supplier/dataUpload',
    component: () => import('@/view/supplier/dataUpload.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'supplier-sample-id-card',
    path: '/supplier/sampleIdCard',
    component: () => import('@/view/supplier/sampleIdCard.vue'),
    meta: {
      title: '示例',
      showHeader: false
    }
  },

  {
    name: 'supplier-uploaded-success',
    path: '/supplier/uploadedSuccess',
    component: () => import('@/view/supplier/uploadedSuccess.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'supplier-agreement',
    path: '/supplier/agreements',
    component: () => import('@/view/supplier/agreements.vue'),
    meta: {
      title: '授权书'
    }
  },

  // 特定政府机构员工
  {
    name: 'government',
    path: '/government/certification',
    component: () => import('@/view/government/certification.vue'),
    meta: {
      title: '',
      showHeader: false
    }
  },
  {
    name: 'government-basic-information',
    path: '/government/basicInformation',
    component: () => import('@/view/government/basicInformation.vue'),
    meta: {
      title: '基本信息',
      showHeader: false
    }
  },
  {
    name: 'government-data-upload',
    path: '/government/dataUpload',
    component: () => import('@/view/government/dataUpload.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'government-sample-id-card',
    path: '/government/sampleIdCard',
    component: () => import('@/view/government/sampleIdCard.vue'),
    meta: {
      title: '示例',
      showHeader: false
    }
  },

  {
    name: 'government-uploaded-success',
    path: '/government/uploadedSuccess',
    component: () => import('@/view/government/uploadedSuccess.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'government-agreement',
    path: '/government/agreements',
    component: () => import('@/view/government/agreements.vue'),
    meta: {
      title: '授权书'
    }
  },

  // 归国
  {
    name: 'returnedElite',
    path: '/returnedElite/certification',
    component: () => import('@/view/returnedElite/certification.vue'),
    meta: {
      title: '',
      showHeader: false
    }
  },
  {
    name: 'returnedElite-basic-information',
    path: '/returnedElite/basicInformation',
    component: () => import('@/view/returnedElite/basicInformation.vue'),
    meta: {
      title: '基本信息',
      showHeader: false
    }
  },
  {
    name: 'returnedElite-data-upload',
    path: '/returnedElite/dataUpload',
    component: () => import('@/view/returnedElite/dataUpload.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'returnedElite-sample-id-card',
    path: '/returnedElite/sampleIdCard',
    component: () => import('@/view/returnedElite/sampleIdCard.vue'),
    meta: {
      title: '示例',
      showHeader: false
    }
  },

  {
    name: 'returnedElite-uploaded-success',
    path: '/returnedElite/uploadedSuccess',
    component: () => import('@/view/returnedElite/uploadedSuccess.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'returnedElite-agreement',
    path: '/returnedElite/agreements',
    component: () => import('@/view/returnedElite/agreements.vue'),
    meta: {
      title: '授权书'
    }
  },

  {
    name: 'dealer-list',
    path: '/dealer/list',
    component: () => import('@/view/dealer/list/list.vue'),
    meta: {
      title: '选择代理商'
    }
  },
  {
    name: 'order-affirm',
    path: '/order/confirm',
    component: () => import('@/view/order/confirm/confirm.vue'),
    meta: {
      title: '确认信息'
    }
  },
  {
    name: 'order-pay-succeed',
    path: '/order/pay-status/succeed',
    component: () => import('@/view/order/pay-status/succeed/succeed.vue'),
    meta: {
      title: '支付成功'
    }
  },
  {
    name: 'order-buyer-info',
    path: '/order/buyer-info',
    component: () => import('@/view/order/buyer-info.vue'),
    meta: {
      title: '购买信息'
    }
  },
  {
    name: 'order-cabinet-agreement',
    path: '/order/cabinet-agreement',
    component: () => import('@/view/order/cabinet-agreement.vue'),
    meta: {
      title: '购车协议'
    }
  },
  {
    name: 'contract-guide',
    path: '/contract/guide',
    component: () => import('@/view/contract/guide.vue'),
    meta: {
      title: '购车合同签署说明'
    }
  },
  {
    name: 'contract-enterprise-online',
    path: '/contract/enterprise/online',
    component: () => import('@/view/contract/enterprise/online.vue'),
    meta: {
      title: '企业客户合同签署说明'
    }
  },
  {
    name: 'contract-download',
    path: '/contract/download',
    component: () => import('@/view/contract/download.vue'),
    meta: {
      title: '购车合同下载'
    }
  },
  {
    name: 'contract-enterprise-knows',
    path: '/contract/enterprise/knows',
    component: () => import('@/view/contract/enterprise/knows.vue'),
    meta: {
      title: '企业客户线上签署合同说明'
    }
  },
  {
    name: 'contract-enterprise-finished',
    path: '/contract/enterprise/finished',
    component: () => import('@/view/contract/enterprise/finished.vue'),
    meta: {
      title: '企业客户合同签署说明'
    }
  },
  {
    name: 'user-policy',
    path: '/user-policy',
    component: () => import('@/view/testdrive/user-policy.vue'),
    meta: {
      title: ''
    }
  },

  // 政企用户
  {
    name: 'relativeInformation-users',
    path: '/relativeInformation/certification',
    component: () => import('@/view/relativeInformation/certification.vue'),
    meta: {
      title: '',
      showHeader: false
    }
  },
  {
    name: 'relativeInformation-information',
    path: '/relativeInformation/basicInformation',
    component: () => import('@/view/relativeInformation/basicInformation.vue'),
    meta: {
      title: '基本信息',
      showHeader: false
    }
  },
  {
    name: 'relativeInformation-data-upload',
    path: '/relativeInformation/dataUpload',
    component: () => import('@/view/relativeInformation/dataUpload.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'relativeInformation-sample-id-card',
    path: '/relativeInformation/sampleIdCard',
    component: () => import('@/view/relativeInformation/sampleIdCard.vue'),
    meta: {
      title: '示例',
      showHeader: false
    }
  },
  {
    name: 'relativeInformation-uploaded-success',
    path: '/relativeInformation/uploadedSuccess',
    component: () => import('@/view/relativeInformation/uploadedSuccess.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'relativeInformation-agreement',
    path: '/relativeInformation/agreements',
    component: () => import('@/view/relativeInformation/agreements.vue'),
    meta: {
      title: '授权书'
    }
  },
  {
    name: 'order-guide-page-download-app',
    path: '/order/guide-page/download-app',
    component: () => import('@/view/order/guide-page/download-app.vue'),
    meta: {
      title: '我的奥迪'
    }
  },
  {
    name: 'deposit-agreement',
    path: '/order/deposit-agreement',
    component: () => import('@/view/order/deposit-agreement.vue'),
    meta: {
      title: '定金协议'
    }
  },

  // 大客户身份灵活认证
  {
    name: 'userFlexibleAuthentication-selectUserList',
    path: '/userFlexibleAuthentication/selectUserList',
    component: () => import('@/view/userFlexibleAuthentication/selectUserList.vue'),
    meta: {
      title: '',
      showHeader: false
    }
  },
  {
    name: 'userFlexibleAuthentication-users',
    path: '/userFlexibleAuthentication/certification',
    component: () => import('@/view/userFlexibleAuthentication/certification.vue'),
    meta: {
      title: '',
      showHeader: false
    }
  },
  {
    name: 'userFlexibleAuthentication-information',
    path: '/userFlexibleAuthentication/basicInformation',
    component: () => import('@/view/userFlexibleAuthentication/basicInformation.vue'),
    meta: {
      title: '基本信息',
      showHeader: false
    }
  },
  {
    name: 'userFlexibleAuthentication-upload',
    path: '/userFlexibleAuthentication/dataUpload',
    component: () => import('@/view/userFlexibleAuthentication/dataUpload.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'userFlexibleAuthentication-sample-id-card',
    path: '/userFlexibleAuthentication/sampleIdCard',
    component: () => import('@/view/userFlexibleAuthentication/sampleIdCard.vue'),
    meta: {
      title: '示例',
      showHeader: false
    }
  },
  {
    name: 'userFlexibleAuthentication-uploaded-success',
    path: '/userFlexibleAuthentication/uploadedSuccess',
    component: () => import('@/view/userFlexibleAuthentication/uploadedSuccess.vue'),
    meta: {
      title: '资料上传',
      showHeader: false
    }
  },
  {
    name: 'userFlexibleAuthentication-agreement',
    path: '/userFlexibleAuthentication/agreements',
    component: () => import('@/view/userFlexibleAuthentication/agreements.vue'),
    meta: {
      title: '授权书'
    }
  },
  {
    name: 'userFlexibleAuthentication-success',
    path: '/userFlexibleAuthentication/success',
    component: () => import('@/view/userFlexibleAuthentication/success.vue'),
    meta: {
      title: '审核成功',
      showHeader: false
    }
  },
  {
    name: 'userFlexibleAuthentication-successView',
    path: '/userFlexibleAuthentication/successView',
    component: () => import('@/view/userFlexibleAuthentication/successView.vue'),
    meta: {
      title: '基本信息'
    }
  },
  //取送车协议
  {
    name: 'userAgreement',
    path: '/userAgreement/create-deliver-car',
    component: () =>
      import('@/view/userAgreement/create-deliver-car.vue'),
    meta: {
      title:'《上汽奥迪取送车服务协议》'
    }
  },
  //e代驾协议
  {
    name: 'userAgreement',
    path: '/userAgreement/proxy-driving',
    component: () =>
    import('@/view/userAgreement/proxy-driving.vue'),
    meta: {
      title:'《车安科技服务协议》'
    }
  },
]

// add route path
routes.forEach((route) => {
  route.path = route.path || `/${route.name || ''}`
})

const router = new Router({
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 滚动条滚动到哪个的位置
    return {
      x: 0,
      y: 0
    }
  }
})

router.beforeEach((to, from, next) => {
  //! 返回【back,gp】携参处理，新增到query参数中
  const {
    params: { backCords }
  } = from || {}
  if (backCords && Object.keys(backCords)?.length) {
    for (const iterator in backCords) {
      to.query[iterator] = backCords[iterator]
    }
  }

  const ele = document.getElementById('common-view-wrapper')
  // if (ele) ele.style.backgroundColor = '#fff'

  if (to.query.wxLocation && to.query.wxLocation.length > 0) {
    store.commit('setWxLocation', `${to.query.wxLocation}`)
  }
  if (to.query.inviteBuyCarCode && to.query.inviteBuyCarCode.length > 0) {
    store.commit('setInviteBuyCarCode', `${to.query.inviteBuyCarCode}`)
  }
  store.commit('showLoading')
  if (to.meta.showHeader === false || env === 'minip') {
    store.commit('setHeaderVisible', false)
  } else {
    store.commit('setHeaderVisible', true)
  }
  if (to.query.dealerCode && to.query.dealerCode.length > 0) {
    console.log('参数里面有代理商code', to.query.dealerCode)
    store.commit('updateBestRecommandDealerCode', to.query.dealerCode)
  }
  if (to.path === '/configration' && from.path === '/optiondetail') {
    console.log('beforeEach', { to, from, next })
    store.commit('updateNowTabIndex', '4')
  }

  if (to.path === '/configration' && from.path === '/hot-recommended') {
    store.commit('updateNowTabIndex', '')
  }

  if (to.path === '/testdrive/create') {
    console.log('/testdrive/create from===', from)
  }

  /**
   * 从配置器进去报价的话说明是用户在配车这里，我们需要重置storage里面的 oldccid
   *
   */
  if (
    (from.path === '/configration' && to.path === '/quotation')
    || (from.path === '/lucky-bag-cars/details' && to.path === '/order/detail')
    || (from.path === '/order/detail' && to.path === '/order/confirm')
  ) {
    storage.remove('oldCcid')
  }

  // every route call toggleNavigation false
  callNative('business', {
    callFunc: {
      functionName: 'toggleNavigation',
      functionParams: {
        show: false
      }
    },
    bizCode: '000003'
  })

  const title = to.meta && to.meta.title
  if (title) {
    document.title = title
    store.commit('setTitle', title)
  }
  // env=test 增加本地测试url
  if (to.query.env) {
    return next()
  }
  if (from.query.env) {
    const toQuery = JSON.parse(JSON.stringify(to.query))
    toQuery.env = from.query.env
    toQuery.token = from.query.token
    next({
      path: to.path,
      query: toQuery
    })
  } else {
    next()
  }

  store.commit('setInitTimeout', false)
})
router.afterEach((to, from) => {
  store.commit('hideLoading')
})

export { router }
