<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-07-26 15:50:21
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-12-19 16:11:57
 * @FilePath     : \src\view\order\verification-coupons.vue
 * @Descripttion : 核销(验证)卡券
-->
<template>
  <div
    class="container"
    :style="{
      height: 'calc(100% - 34px)'
    }"
    data-flex="dir:top"
    data-block
  >
    <automobile-item
      :is-do-verification.sync="isVerification"
      :error-message.sync="errorsMessage"
      page-name="verification"
    />
    <div
      class="info-box custom-ux-btn"
      data-flex="cross:bottom"
      data-block
    >
      <!-- :text="isDoVerification ? '核销卡券中...' : '确认支付'" -->
      <audi-button
        v-if="isVerification"
        :text="`${isDoVerification ? '支付中 ...' : '确认支付'}`"
        :class="['black-btn', isDoVerification ? 'btn-un-enabled' : 'btn-enabled', isVerification === 2 ? 'text-white-btn' : '']"
        color="black"
        height="56px"
        @click="handleConfirmPayment()"
      />
    </div>
    <van-popup
      v-model="deliveryDialog"
      class="popup-custom"
      :close-on-click-overlay="false"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="text align-center">
            {{ errorsMessage || '优惠券核销失败' }}
          </div>
        </div>
        <div
          class="popup-custom-btn"
        >
          <audi-button
            text="我知道了"
            color="black"
            height="56px"
            @click="deliveryDialog = false"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Dialog, Toast
} from 'vant'
import AutomobileItem from './components/automobile-item.vue'
import AudiButton from '@/components/audi-button.vue'
import {
  postUseMpCoupon
} from '@/api/api'
import { paramsStrict, delay, checkType } from '@/utils'

Vue.use(Dialog).use(Toast)
export default {
  name: 'VerificationCoupons',
  components: {
    'automobile-item': AutomobileItem,
    'audi-button': AudiButton
  },
  data() {
    return {
      dataSource: {},
      isDoVerification: false,
      deliveryDialog: false,
      isVerification: 0,
      errorsMessage: '',
      headerHeight: 0
    }
  },
  watch: {
    isVerification(num) {
      if (num > 1) {
        this.errorsMessage = this.errorsMessage || '卡券已过期，请您联系奥迪专属管家'
        this.deliveryDialog = true
      }
    }
  },
  created() {
    // const [VW_DOM] = document.getElementsByClassName('view-wrapper')
    // if (VW_DOM) {
    //   this.headerHeight = VW_DOM.getBoundingClientRect()?.top || 0

    //   console.log('%c [ this.headerHeight ]-106', 'font-size:14px; background:#cf222e; color:#fff;', this.headerHeight)
    // }
  },
  methods: {
    async handleConfirmPayment() {
      if (this.isDoVerification) return
      const { orderId, ccid } = paramsStrict(this.$route.query)
      if (this.isVerification > 1 || this.errorsMessage) {
        if (this.isVerification > 2) {
          this.errorsMessage = this.errorsMessage || '网络错误'
          this.deliveryDialog = true
        }
        return
      }
      this.isDoVerification = true
      delay(() => {
        this.isDoVerification = false
      }, 6000)
      const { data: { data, message, code } } = await postUseMpCoupon({ orderId })

      this.isDoVerification = false
      if (code !== '00') {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: message || '网络请求错误',
          forbidClick: true,
          duration: 800
        })
      }

      this.$router.push({
        path: '/order/balance',
        query: {
          orderId,
          ccid
        }
      })
    }
  }
}
</script>

<style lang="less">
</style>
