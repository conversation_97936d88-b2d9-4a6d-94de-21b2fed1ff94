
<template>
  <van-overlay :show="props.show" z-index="1000">

    <div class="wrapper c-font14">
      <div class="whtie-bg">

        <div class="c-bold">请做出一个选择</div>
        <div class="c-bold margin-top">您想增加:</div>
        <van-divider />
        <!-- 8I6 -->
        <div class="c-flex-center-between">
          <div> {{ toAddOption.optionName }} </div>
          <div>{{ toAddOption.price | finalFormatPriceDesc }} </div>
        </div>

        <div class="c-bold margin-top"> 以下选装配置将会被移除: </div>
        <van-divider />
        <div class="c-flex-center-between">
          <div class="c-flex-center-between">
            <div class="img-wrapper">
              <img :src="OSS_URL + toHideOption.imageUrl" alt="">
            </div>
            <div> {{ toHideOption.optionName }} </div>
          </div>
          <div>{{ toHideOption.price | finalFormatPriceDesc }} </div>
        </div>

        <div class='min-height'/>

        <van-divider />
        <div class="line c-flex-center-between c-bold c-font12">
          <div>当前选项的价格已更新</div>
          <div> {{ toAddOption.price  | finalFormatPriceDesc }} </div>
        </div>

        <AudiButton color="black" text="确认" height="52px" @click="confirm" />
        <AudiButton style='margin-top:6px' color="white" text="取消" height="52px" @click="cancel" />
      </div>
    </div>

  </van-overlay>
</template>

<script setup>

import {
  defineProps, defineEmits, computed, onMounted
} from 'vue'
import AudiButton from '@/components/audi-button.vue'
import { useStore } from '../util/vueApi.js'
import url from '@/config/url'

const OSS_URL = url.BaseConfigrationOssHost
const store = useStore()
const props = defineProps({
  show: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['cancel'])
/**
 * q5e荣耀型 选装和标装互斥的逻辑
 * G4IBF3003 荣耀锦衣  选装8I6,隐藏标装4D3
 * G4ICF3007 荣耀机甲  选装4D3,隐藏标装8I6
 * G4ICF3012 光耀机甲  选装4D3,隐藏标装8I6
 * G4IBF3005 光耀锦衣  选装8I6,隐藏标装4D3
 * 2025/3 新增
 * G4ICF3017 光耀机甲  选装8I6,隐藏标装4D3
 * G4IBF3007 光耀锦衣  选装8I6,隐藏标装4D3
 */
const addOptionMap = {
  G4IBF3003: '8I6',
  G4IBF3005: '8I6',
  G4IBF3007: '8I6',
  G4ICF3007: '4D3',
  G4ICF3012: '4D3',
  G4ICF3017: '4D3'
}

// const modelineCode = computed(() => store.state.configration.currentmodelLineCode)

// 获取要添加的选装包
const toAddOption = computed(() => {
  const code = store.state.configration.currentModelLineData.modelLineCode
  const option = store.state.configration.personalOptions.find((item) => item.optionCode === addOptionMap[code])
  if (!option) {
    return console.error(`toAddOption 未找到对应的选装配置 ${addOptionMap[code]}`)
  }
  return option
})

// 要删除的选装包
const toHideOption = computed(() => {
  const toRemoveCode = toAddOption.value.optionCode === '4D3' ? '8I6' : '4D3'
  const option = store.state.configration.personalOptions.find((item) => (item.optionCode === toRemoveCode))
  if (!option) {
    return console.error(`toHideOption 未找到对应的选装配置 ${toRemoveCode}`)
  }
  return option
})


const confirm = () => {
  // 选中要添加的选装包
  const handledOptions = store.getters.pageAllOptionList.find((i) => i.optionCode === toAddOption.value.optionCode)
  if (!handledOptions) {
    return console.error(`添加配置: 未找到对应的选装配置 ${toAddOption.value.optionCode}`)
  }
  handledOptions.selected = true

  // 计算价格和交付时间
  store.dispatch('setTimeAndPrice')

  // 埋点触发
  emit('triggerBuriedPoint', '确定', '选装替换弹窗')
  emit('cancel')
}

const cancel = () => {
  // 埋点触发
  emit('triggerBuriedPoint', '取消', '选装替换弹窗')

  emit('cancel')
}

</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";

.wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  width: calc(100% - 40px);
  box-sizing: border-box;
  padding: 20px 16px;

  .img-wrapper {
    width: 45px;
    height: 45px;
    margin-right:6px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .line {
    margin-bottom: 20px;
  }
}

.min-height {
  min-height: 200px;
}

.margin-top {
  margin-top: 30px;
}

.white-bg {
}
</style>
