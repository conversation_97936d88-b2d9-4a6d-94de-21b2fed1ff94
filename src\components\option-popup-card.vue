<template>
  <div class="option-popup-card">
    <div class="card-title">
      {{ title }}
    </div>
    <div class="card-main">
      <div v-if="type === 'text'">
        <div
          class="main-text"
          v-for="item in list"
          :key="item.optionId"
        >
          <div class="text-optionName">
            <word-detail
              :title="item.optionName ||item.interieurName|| ''"
              :id="'option_popup_' + item.optionId"
              :show="true"
              :fontsize="14"
            />
          </div>
          <div class="text-price" v-if="!'YEG,YEA,CCPRO-YEB'.includes(item.optionCode)">
            {{ item.price | finalFormatPrice }}
          </div>
        </div>
      </div>
      <div v-if="type === 'checkbox'">
        <div
          class="main-checkbox list"
          v-for="(item,index) in list"
          :key="'main-checkbox-'+index"
        >
          <div
            class="item-checkbox"
            v-for="childItem in item.children"
            :key="childItem.optionId"
          >
            <div class="checkbox-optionName">
              <div
                @click="handleCheck(childItem)"
                :class="{check:true,checked:getChecked(childItem)}"
              />
              <img
                :src="$loadWebpImage(BaseConfigrationOssHost + childItem.imageUrl + '?x-oss-process=image/resize,w_512')"
                alt=""
              >
              <div class="check-text">
                <word-detail
                  :title="childItem.description || childItem.optionName"
                  :id="'option_popup_' + (childItem.sibInterieurId || childItem.optionId)"
                  :show="true"
                  :fontsize="14"
                />
              </div>
            </div>
            <div class="checkbox-price">
              {{ childItem.price| finalFormatPrice }}
            </div>
          </div>
        </div>
      </div>
      <div v-if="type === 'imgbox'">
        <div
          class="main-checkbox"
          v-for="item in list"
          :key="item.optionId"
        >
          <div v-if="isShow(item)">
            <div class="checkbox-optionName">
              <img
                :src="$loadWebpImage(BaseConfigrationOssHost + item.imageUrl + '?x-oss-process=image/resize,w_512')"
                alt=""
              >
              <div class="check-text img-text">
                <word-detail
                  :title="item.description || item.optionName"
                  :id="'option_popup_' + (item.sibInterieurId || item.optionId)"
                  :show="true"
                  :fontsize="14"
                />
              </div>
            </div>
            <div class="checkbox-price" v-if="!'YEG,YEA,CCPRO-YEB'.includes(item.optionCode)">
              {{ item.price| finalFormatPrice }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import url from '@/config/url'
import WordDetail from './word-detail.vue'
import {
  checkV2
} from '@/utils'
import {
  patchesChangeFabric, patchesChangeSport, patchesChangeVR6Sport, patchesPS1, defPatchesWA3
} from '@/view/configration/fix_configration/ccFn.js'

export default {
  components: { WordDetail },
  props: {
    title: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true
    },
    list: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      PACKET: {},
      sib: {},
      vos: {},
      rad: {},
      eih: {},
      him: {}
    }
  },
  watch: {
    changeList: {
      handler: function (next, old) {
        this.$store.commit('setChangeList', next)
      },
      deep: true
    }
  },
  computed: {
    ...mapState({
      idx: (state) => state.idx,
      currentOptionsList: (state) => state.currentOptionsList,
      selectCarInfo: (state) => state.selectCarInfo,
      clickOption: (state) => state.clickOption,
      otherClickOption: (state) => state.otherClickOption,
      stateChangeList: (state) => state.changeList
    }),
    getChecked() {
      return function (item) {
        let bool
        switch (item.category ? item.category : item.sibInterieurCategory) {
          case 'F_SIB_COLOR_INTERIEUR':
            bool = this.sib.sibInterieurId === item.sibInterieurId
            break
          case 'VOS':
            bool = this.vos.optionId === item.optionId
            break
          case 'RAD':
            bool = this.rad.optionId === item.optionId
            break
          case 'EIH':
            bool = this.eih.optionId === item.optionId
            break
          case 'HIM':
            bool = this.him.optionId === item.optionId
            break
          default:
            bool = false
            break
        }
        if (('G4IBC3001,G4ICC3001'.includes(this.selectCarInfo.modelLineCode) && 'N4X'.includes(this.clickOption.sibOptionCode))) {
          bool = this.otherClickOption.optionId === item.optionId
        }

//         modelLineCode: "G6IBCY001"
// modelLineId: "f8f2838a-fb92-4c05-897a-edcbfe50c6dd"
// modelLineName: "Audi Q6 40 TFSI quattro Roadjet 观云型 羽林套装"

        let G6IBCY001 = ('G6IBCY001'.includes(this.selectCarInfo.modelLineCode) && 'N5U-TI,N5U-TP'.includes(this.clickOption.sibInterieurCode))
        let G6ICCY001 = ('G6ICCY001'.includes(this.selectCarInfo.modelLineCode) && 'N7K-TO'.includes(this.clickOption.sibInterieurCode))
        if (G6IBCY001 || G6ICCY001) {
          bool = this.otherClickOption.optionId === item.optionId
        }

        // "G6ICAY001"  齐云型 飞骑套装
        let sib = this.$store.state.currentSibColorInterieur?.sibInterieurCode
        let G6IBAY001 = ('G6IBAY001'.includes(this.selectCarInfo.modelLineCode) && "N5D-TX,N5D-TT".includes(this.clickOption.sibInterieurCode || sib))
        let G6ICAY001 = ('G6ICAY001'.includes(this.selectCarInfo.modelLineCode) && "N5D-AW".includes(this.clickOption.sibInterieurCode || sib))
        if (G6IBAY001 || G6ICAY001) {
          bool = this.otherClickOption.optionId === item.optionId
        }
        return bool
      }
    },
    changeList() {
      const {
        sib, vos, rad, eih, him, PACKET
      } = this
      return {
        sib, vos, rad, eih, him, PACKET
      }
    },
    isShow() {
      return function (data) {
        let flag = true
        for (const key in this.stateChangeList) {
          if (Object.hasOwnProperty.call(this.stateChangeList, key)) {
            const element = this.stateChangeList[key]
            if ((data.sibInterieurCode && element.sibInterieurCode === data.sibInterieurCode)) {
              flag = false
              break
            }
            if (data.optionCode && element.optionCode === data.optionCode) {
              flag = false
              break
            }
          }
        }

        const sour = ['WE8', '4A4', '8I6+PV3+4A4', '4D3+PV3+4A4', '8I6+WE8', '4D3+WE8']
        if (!checkV2() && this.idx == 1 && sour.includes(data.optionCode)) {
          flag = true
        }
        return flag
      }
    }
  },
  created() {
      if (this.list && this.list.length > 0 &&  this.list[0] && this.list[0].children)  {
       this.$store.commit('setOtherClickOption', this.list[0]?.children[0] || {})
      }
  },
  methods: {
    // selectedHub: function (hub) {
    //   this.$store.commit('setCurrentModelHub', hub)
    // }

    handleCheck(item) {
      console.log("clickOption", this.clickOption);
      console.log("item", item,);

      // 齐云羽林，选择TT 或 TX时
      // 齐云飞骑，选择AW时  "G6ICAY001"  
      // "G6ICAY001"  齐云型 飞骑套装
      let sib = this.$store.state.currentSibColorInterieur?.sibInterieurCode
      let G6IBAY001 = ('G6IBAY001'.includes(this.selectCarInfo.modelLineCode) && '"N5D-TX,N5D-TT"'.includes(this.clickOption.sibInterieurCode || sib))
      let G6ICAY001 = ('G6ICAY001'.includes(this.selectCarInfo.modelLineCode) && "N5D-AW".includes(this.clickOption.sibInterieurCode || sib))
      if (G6IBAY001 || G6ICAY001) {
        this.$store.commit('setOtherClickOption', item)
      }

// 观云羽林：
// 观云 羽林套装 选择TI时 ，需要提示一定要加装WA3

// 观云飞骑： G6ICCY001
// 选择TO时 ，需要提示一定要加装WA3

      let G6IBCY001 = ('G6IBCY001'.includes(this.selectCarInfo.modelLineCode) && 'N5U-TI'.includes(this.clickOption.sibInterieurCode))
      let G6ICCY001 = ('G6ICCY001'.includes(this.selectCarInfo.modelLineCode) && 'N7K-TO'.includes(this.clickOption.sibInterieurCode))
      if (G6IBCY001 || G6ICCY001) {
        this.$store.commit('setOtherClickOption', item)
      }

      if (('G4IBC3001,G4ICC3001'.includes(this.selectCarInfo.modelLineCode) && 'N4X'.includes(this.clickOption.sibOptionCode))) {
        this.$store.commit('setOtherClickOption', item)
      }

      switch (item.category ? item.category : item.sibInterieurCategory) {
        case 'F_SIB_COLOR_INTERIEUR':
          this.sib = item
          break
        case 'VOS':
          this.vos = item
          break
        case 'RAD':
          this.rad = item
          break
        case 'EIH':
          this.eih = item
          break
        case 'HIM':
          this.him = item
          break
        default:
          break
      }
      
    }
  },
  mounted() {
    if (this.list && this.list.length > 0 &&  this.list[0] && this.list[0].children)  {
      this.$store.commit('setOtherClickOption', this.list[0]?.children[0] || {})
    }
    
    if (this.type === 'checkbox') {
      console.log('%c this.list', 'font-size:25px;color:green;', this.list)

      for (const item of this.list) {
        switch (item.children[0].category ? item.children[0].category : item.children[0].sibInterieurCategory) {
          case 'F_SIB_COLOR_INTERIEUR':
            this.sib = item.children[0]
            break
          case 'VOS':
            this.vos = item.children[0]
            break
          case 'RAD':
            this.rad = item.children[0]
            break
          case 'EIH':
            this.eih = item.children[0]
            break
          case 'HIM':
            this.him = item.children[0]
            break
          default:
            break
        }
      }
      // switch (this.list[0].category ? this.list[0].category : this.list[0].sibInterieurCategory) {
      //   case 'F_SIB_COLOR_INTERIEUR':
      //     this.sib = this.list[0]
      //     break
      //   case 'VOS':
      //     this.vos = this.list[0]
      //     break
      //   case 'RAD':
      //     this.rad = this.list[0]
      //     break
      //   case 'EIH':
      //     this.eih = this.list[0]
      //     break
      //   default:
      //     break
      // }
    }
  }
}
</script>

<style lang="less" scoped>
.option-popup-card {
  margin-bottom: 26px;
  .card-title {
    width: 100%;
    font-size: 14px;
    font-family: Audi-WideBold;
    color: #000000;
    line-height: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #E5E5E5;
    margin-bottom: 22px;
  }
  .card-main {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 20px;
    .main-text {
      display: flex;
      justify-content: space-between;
      .text-optionName {
        max-width: 70%;
      }
    }

    .main-checkbox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      >div{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .checkbox-optionName {
        max-width: 70%;
        display: flex;
        align-items: center;
        .check-text {
          max-width:62%;
          &.img-text {
            max-width:75.5%;
          }
        }
        .check {
          width: 24px;
          height: 24px;
          border: 1px solid #808080;
          flex-shrink: 0;
          border-radius: 50%;
          position: relative;
          margin-right: 6px;
          &::after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background-color: #E5E5E5;
            border-radius: 50%;
          }
          &.checked {
            &::after {
              background-color: #333333;
            }
          }
        }
        img {
          width: 40px;
          height: 40px;
          object-fit: cover;
          margin-right: 8px;
        }
      }
    }
    .main-checkbox.list{
      display: flex;
      flex-direction: column;
      border-bottom: 1px solid #E5E5E5;
      &:last-of-type {
        border-bottom: 0px;
      }
      .item-checkbox {
        width: 100%;
        display: flex;
        justify-content:space-between;
        align-items: center;
        margin-bottom: 12px;
      }
    }
  }

}
</style>
