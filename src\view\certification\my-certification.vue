<template>
  <div class="certification">
    <div style="padding-bottom: 145px">
      <div
        v-if="certificationStatus === '3' || certificationStatus === '4'"
        class="c-font14"
        style="
          background: #f2f2f2;
          line-height: 40px;
          color: #000;
          padding-left: 16px;
        "
      >
        {{
          certificationStatus === '3'
            ? '您的身份认证正在审核中'
            : '您的身份认证未通过审核，请尝试重新上传'
        }}
      </div>
      <div v-for="(item, idx) in findCustomerCarList" :key="idx">
        <div class="item-wrapper" @click="onCarInfo(idx)">
          <div class="content-wrappers">
            <div class="model-name">
              <span>
                {{ item.modelNameCn ? item.modelNameCn : '' }}
              </span>
              <!-- <van-icon name="arrow" color="#F50537" /> -->
              <img src="@/assets/right.png" />
            </div>
            <div class="plate-number">
              {{ item.plateNumber ? item.plateNumber : ' ' }}
            </div>

            <div class="item_btn">
              {{ showReviewStatus(item) }}
            </div>
          </div>

          <div class="navgation-wrapper">
            <img
              v-if="item.headImageUrl"
              :src="
                $loadWebpImage(
                  (item.headImageUrl || '').includes('http')
                    ? item.headImageUrl
                    : baseOssHost + item.headImageUrl
                )
              "
            />
            <img v-else :src="require('../../assets/img/vehicle.png')" />
          </div>
        </div>
      </div>
      <!-- 暂无数据 -->
      <div
        v-if="findCustomerCarList.length < 1"
        class="not_data"
        style="margin-top: 116px; padding-bottom: 145px; padding: 0 16px"
      >
        <div style="margin-bottom: 2px">
          <img src="../../assets/attestation-bg.png" />
        </div>
        <p>您还未进行车辆认证</p>
      </div>
    </div>

    <div
      class="bottom_style"
      v-if="certificationStatus && certificationStatus !== '3'"
    >
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onCarCertification"
          :text="btnText"
          color="#000"
          font-size="15px"
          height="48px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Toast,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover,
  Tab,
  Tabs
} from 'vant'
import { callNative, getCalcCMTImages } from '@/utils'
import {
  drivingLicenseInfo,
  getManCarMemberInfo,
  postManCarJoinClub,
  getFindCustomerCarList,
  getCCIDbyVin,
  getCarConfig
} from '@/api/api'
import AudiButton from '@/components/audi-button'
import api from '../../config/url'
import DefTabs from './components/DefTabs'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)
  .use(Tab)
  .use(Tabs)

export default {
  components: {
    AudiButton,
    DefTabs
  },
  data() {
    return {
      baseOssHost: api.BaseOssHost,
      btnText: '',
      certificationStatus: '', // 身份认证状态,1-认证用户，2-未认证用户，3-认证审核中，4-认证审核被拒'
      findCustomerCarList: [],
      memberInfoModel: {}, // 我的认证信息
      tabType: 0,
      carArr: [],
      visible: true,
      loading: false
    }
  },
  async created() {
    await this.getManCarMemberInfo()
    if (this.$route.query && this.$route.query.source === 'sc') {
      this.$store.commit('setPageFrom', 'sc')
      this.$store.commit('setPageSource', 'sc')
    }

    if (this.$route.query && this.$route.query.scene) {
      this.$store.commit('setPageFromScene', this.$route.query.scene)
      if (
        this.$route.query.scene === 'carList' ||
        this.$route.query.scene === 'latentGuestPageBindCarFromSA'
      ) {
        if (
          this.certificationStatus == '1' ||
          this.certificationStatus == '2'
        ) {
          this.$router.push({
            path: '/certification/my-confirm?replace=1'
          })
        }
      }
    }
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    // 获取会员信息
    async getManCarMemberInfo() {
      const { data } = await getManCarMemberInfo({})
      // this.$store.commit('hideLoading')
      // this.loading = false
      // 是否会员,0-否，1-是
      this.certificationStatus = `${data.data.certificationStatus}`
      if (data.data.boolMember === 0) {
        postManCarJoinClub()
      } else {
        // 判断 身份认证状态,1-认证用户，2-未认证用户，3-认证审核中，4-认证审核被拒'

        this.memberInfoModel = data.data
        // 未认证的直接跳转认证页面
        // if (this.certificationStatus === "2") {
        //   this.routerIdCertification();
        // } else {
        // }
      }
      this.getFindCustomerCarList()
    },
    // 查询车辆是否绑定
    async getFindCustomerCarList() {
      const { data } = await getFindCustomerCarList({
        bindingStatus: 4,
        bindingType: 0
      })
      if (data.code === '200') {
        if (data.data.length > 0) {
          this.btnText = '绑定更多车辆'
          this.findCustomerCarList = data.data

          const res = await Promise.all(
            this.findCustomerCarList.map((i) => this.getCCIDbyVin(i.vin))
          )
          res.forEach((e, i) => {
            this.$set(
              this.findCustomerCarList[i],
              'modelNameCn',
                e?.carSeries?.seriesNameCn ||
                (this.findCustomerCarList[i].carConfig?.seriesCode
                  ? { 49: 'Audi A7L', G4: 'Audi Q5 e-tron', G6: 'Audi Q6',F0:'Audi A5L' }[
                      this.findCustomerCarList[i].carConfig?.seriesCode
                    ]
                  : (this.findCustomerCarList[i].carConfig?.modelNameCn || this.findCustomerCarList[i].carConfig?.seriesNameCn ))
            )

            if (e) {
              const ccImagesUrl = getCalcCMTImages(e)
              this.$set(
                this.findCustomerCarList[i],
                'headImageUrl',
                ccImagesUrl
                  ? this.baseOssHost + ccImagesUrl
                  : e.carModel.imageUrl
              )
              this.$set(
                this.findCustomerCarList[i],
                'colorNameCn',
                e.outsideColor.colorNameCn
              )
            }
          })

          console.log(this.findCustomerCarList)
        } else {
          if (
            this.certificationStatus === '2' ||
            this.certificationStatus === '4'
          ) {
            this.btnText = '前往身份认证'
          } else {
            this.btnText = '绑定车辆'
          }
        }
      }
    },

    async getCCIDbyVin(vin) {
      const { data } = await getCCIDbyVin({ vin })
      if (data.data) {
        return this.getOrderCarConfig(data.data)
      }
      return ''
    },
    async getOrderCarConfig(carCustomId) {
      const { data } = await getCarConfig({ ccid: carCustomId })

      if (data.data) {
        return data.data.configDetail
      }
      return ''
    },

    // 身份认证
    routerIdCertification() {
      this.$router.push({
        path: '/certification/identity-certification',
        query: {}
      })
    },
    showMember() {
      if (this.certificationStatus == '1') {
        return '已认证'
      }
      if (this.certificationStatus == '2') {
        return '未认证'
      }
      if (this.certificationStatus == '3') {
        return '审核中'
      }
      if (this.certificationStatus == '4') {
        return '审核被拒'
      }
    },
    // 显示审核状态车辆状态，0：待审核，1：审核通过，2：审核被拒，3：已解绑8:废弃
    bindingStatus(item) {
      if (item.bindingStatus == '0') {
        return '待审核'
      }
      if (item.bindingStatus == '1') {
        return '已认证'
      }
      if (item.bindingStatus == '2') {
        return '审核被拒'
      }
      if (item.bindingStatus == '3') {
        return '已解绑'
      }
      if (item.bindingStatus == '8') {
        return '已废弃'
      }
    },
    showReviewStatus(item) {
      if (item.reviewStatus == '0') {
        return '待审核'
      }
      if (item.reviewStatus == '1') {
        return '已认证'
      }
      if (item.reviewStatus == '2') {
        return '审核被拒'
      }
      if (item.reviewStatus == '3') {
        return '已解绑'
      }
      if (item.reviewStatus == '8') {
        return '已废弃'
      }
    },
    // 车辆认证
    onCarCertification() {
      this.$router.push({
        path:
          '/certification/my-confirm?certificationStatus=' +
          this.certificationStatus
      })
    },
    // 车辆认证
    routerCarCertification() {
      this.$router.push({
        path: '/certification/car-certification',
        query: {}
      })
    },
    // 查看车辆详情
    onCarInfo(idx) {
      sessionStorage.setItem(
        'currentCar',
        JSON.stringify(this.findCustomerCarList[idx])
      )
      this.$router.push({
        path: '/certification/car-certification-info',
        query: {
          // reviewStatus: item.reviewStatus,
          // plateNumber:item.plateNumber,
          // vin:item.vin,
          // ocrOrderDate:item.ocrOrderDate,
          phone: this.memberInfoModel.mobile
        }
      })
    },
    // 查看行驶证详情
    onDrivingInfo(idx) {
      this.$router.push({
        path: '/certification/driving-certification-info',
        query: {
          // reviewStatus: item.reviewStatus,
          // plateNumber:item.plateNumber,
          // vin:item.vin,
          // ocrOrderDate:item.ocrOrderDate,
          idx: idx,
          phone: this.memberInfoModel.mobile
        }
      })
    },
    onIdInfo() {
      this.$router.push({
        path: '/certification/identity-certification-info',
        query: {}
      })
    }
  }
}
</script>

<style scoped lang="less">
@import '../../assets/style/common.less';
.line-border-bottom {
  // width: 100%;
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f2f2f2;

  .name {
    width: 90px;
    font-size: 16px;
    color: #000;
    font-family: 'Audi-Normal';
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #000;
  }
}
.not_data {
  text-align: center;
  // padding-top: 150px;
  img {
    width: 286px;
    height: 161px;
  }
  p {
    margin: 0;
    line-height: 24px;
    font-size: 16px;
    color: #1a1a1a;
    font-family: 'Audi-Normal';
  }
}
.certification {
  overflow: hidden;
  background: #f9fafb;
  // height: 100%;
  // padding-left: 16px !important;
  // padding-right: 16px;
}

.flex1 {
  flex: 1;
}

.item-wrapper {
  position: relative;
  margin: 16px;
  padding: 13px 16px;
  height: 35.5vw;
  background-image: url(../../assets/img/car_mask.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-sizing: border-box;
  .content-wrappers {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .model-name {
    margin-bottom: 8px;
    font-size: 18px;

    font-family: 'Audi-ExtendedBold';
    span {
      color: #1a1a1a;
      line-height: 26px;
    }
    img {
      display: inline;
      width: 6px;
      height: 20px;
      object-fit: cover;
      vertical-align: text-top;
      padding-top: 1px;
      margin-left: 2px;
    }
  }
  .plate-number {
    margin-bottom: auto;
    font-size: 14px;
    color: #808080;
    line-height: 22px;
  }

  .item_btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 53px;
    height: 24px;
    font-size: 11px;
    color: #1a1a1a;
    background: #ffffff;
  }

  .navgation-wrapper {
    img {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 200px;
      aspect-ratio: 200/110;
      object-fit: cover;
    }
  }
}

.bottom_style {
  width: 100%;
  background-color: #ffffff;
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  position: fixed;
  bottom: 0;
  padding-bottom: 38px;
  left: 0px;
  .checkbox_button {
    margin-left: 16px;
    margin-bottom: 5px;
    width: 18px;
    height: 18px;
    color: #000;
  }
}
.btn-delete-wrapper {
  margin: 0 16px;

  > div {
    background-color: rgba(255, 255, 2551);
    color: black;
    font-size: 16px;
    width: 100%;
    line-height: 56px;
    border: 1px solid #000;
    text-align: center;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.btn-delete-height {
  height: 130px;
}

.text-two-hidd {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
