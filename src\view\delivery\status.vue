<template>
  <div>
    <div class="img-wrapper">
      <img :src="statusImgUrl" alt="" />
    </div>
    <div class="text-status">
      {{ contractStatusText }}
    </div>

    <div class="btn-wrapper">
      <AudiButton
        text="查看我的订单"
        color="black"
        font-size="16px"
        height="56px"
        @click="toOrderPage"
      />
    </div>
  </div>
</template>

<script>
import { getOnlineContractStatus, getMyOrders } from '@/api/api'
import AudiButton from '@/components/audi-button'
import { callNative } from '@/utils/'

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      contractStatusText: '',
      successImgurl: require('@/assets/img/contract-success.png'),
      failImgurl: require('@/assets/img/contract-fail.png'),
      statusImgUrl: '',
      handleOrderid: ''
    }
  },
  mounted() {
    const { orderId } = this.$route.query
    if (!orderId) {
      return console.error(`orderid:${orderId}`)
    }

    // 处理下url 被法大大强塞了一个'?contract='的bug
    const decodeUrl = decodeURIComponent(orderId)
    if (decodeUrl.includes('?')) {
      this.handleOrderid = decodeUrl.split('?')[0]
    }
    if (!this.handleOrderid) {
      console.warn(`handleOrderid:${this.handleOrderid}`)
    }

    this.getContractStatus(this.handleOrderid)
  },

  methods: {
    _sleep(time) {
      return new Promise((res) => {
        setTimeout(() => {
          res()
        }, time)
      })
    },
    async getContractStatus(orderId) {
      this.contractStatusText = '正在获取合同状态...'
      await this._sleep(3000) // 等个三秒获取状态
      const { data } = await getOnlineContractStatus({ orderId })
      if (data.code === '00') {
        // 这里可能缺状态 0:未签署, 1: 用户(线下)已签署 2:线上合同已签署, 10: 客户已签署
        if (data.data.ngaContractVO.contractStatus === '2') {
          const orderData = await getMyOrders({ orderId })
          this.statusImgUrl = this.successImgurl
          this.contractStatusText = '您的交车合同申请将在一个工作日内审核通过'
          this.$store.commit('setTitle', '签署成功')

          callNative('vehicleStatusChange', {
            bind: true,
            vin: orderData.data.data.extInfo.vin,
            scene: 'signCarDeliveryConfirmation',
            source: this.$store.state.pageSource
          })
        } else {
          this.statusImgUrl = this.failImgurl
          this.contractStatusText = '您的交车合同已签署失败'
          this.$store.commit('setTitle', '签署失败')
        }
        this.toOrderPage()
      }
    },

    // 全款跳转到到订单详情页
    toOrderPage() {
      const { orderType } = this.$route.query
      this.$router.push({
        path: '/order/model-detail',
        query: {
          orderId: this.handleOrderid,
          orderType
        }
      })
    },

    // 跳转到金融服务页面
    toFinancePage() {
      this.$router.push({
        path: '/finance-list',
        query: {
          orderId: this.handleOrderid
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.img-wrapper {
  width: 20vw;
  margin: 18vh auto 0 auto;
}
.text-status {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  padding: 0 16px;
  margin-top: 20px;
}
.btn-wrapper {
  position: fixed;
  bottom: 50px;
  width: 100%;
  left: 0;
  padding: 0 16px;
  box-sizing: border-box;
}
</style>
