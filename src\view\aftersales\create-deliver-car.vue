<template>
  <div class="serviceAppointmentOne">
    <div
      v-if="isShowBindCard"
      class="top-banner-tips no-both-sides"
      data-flex="main:justify cross:center"
    >
      <div class="left">您还未绑定车辆，绑定后自动填写相关信息</div>
      <div class="right" data-flex="cross:center">
        <span class="btn" @click="handleGotoBindCar">去绑定</span
        ><van-icon class="arrow icon" name="arrow" />
      </div>
    </div>
    <!-- <van-tabs
      style="width: 100%; position: fixed; left: 0; z-index: 90"
      v-model="active"
      color="#000"
      line-width="60px"
      line-height="2"
      title-active-color="#000"
      @click="onTabClick"
    >
      <van-tab title="取车服务" />
      <van-tab title="送车服务" />
    </van-tabs> -->
    <img
      :src="
        'https://audi-oss.saic-audi.mobi/audicc/app/order/afterservice/service4.png'
          | audiwebp
      "
      class="item-img"
    />
    <div class="order-forms-box">
      <van-form ref="form" @failed="failed" @submit="confirmVisible = true">
        <van-cell-group>
          <div class="cell-title van-hairline--bottom service-line">
            <h2
              class="h2"
              v-if="
                orderType === '20131010' ||
                (orderType === '20131020' && !isTakeAndSend)
              "
            >
              服务商信息
            </h2>
          </div>
          <van-field
            readonly
            is-link
            label="服务商"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="dealerName"
            ref="dealerName"
            type="text"
            @click="onSelectDealer"
            placeholder="请选择服务商"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择服务商'
              }
            ]"
          />

          <template v-if="dealerName">
            <van-field
              readonly
              label="服务地址"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="dealerAddress"
              ref="dealerAddress"
              type="text"
              :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择服务商地址'
              }
            ]"
            />

            <van-field
              readonly
              label="服务电话"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="dealerPhone"
              ref="dealerPhone"
              type="text"
              :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择服务商电话'
              }
            ]"
            />
          </template>

          <van-field
            is-link
            readonly
            v-if="orderType === '20131010'"
            label="服务类型"
            type="text"
            cols="24"
            autosize
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="serviceName"
            ref="serviceName"
            @click="onSelectServiceType"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择服务类型！'
              }
            ]"
          />
        </van-cell-group>

        <van-cell-group>
          <div class="cell-title van-hairline--bottom">
            <h2 class="h2">
              基本信息
            </h2>
          </div>
          <!-- <div @click.stop="animation('vin')" id="vin" style="height: 60px"> -->
          <van-field
            id="vin"
            type="text"
            label="VIN码"
            :readonly="loading"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="vin"
            clearable
            ref="vin"
            maxlength="17"
            placeholder="请输入您的车辆VIN码"
            on-key-up="value=value.replace(/[\W]/g,'')"
            @blur="handlerBlur('vin')"
            @focus="handlerFocus('vin')"
            @click="animation('vin')"
            :rules="[
              {
                trigger: 'onChange',
                required: true,
                validator: asyncValidatorVin,
                message: '未查询到该VIN，请检查填写是否正确'
              },
              // {
              //   trigger: 'onBlur',
              //   required: true,
              //   validator: asyncValidatorVin,
              //   message: '未查询到该VIN，请检查填写是否正确',
              // },
              {
                pattern: /\w{17}/,
                message: '请输入正确的VIN码'
              }
            ]"
          >
          </van-field>

          <div id="vinHistory" style="margin-top: -20px">
            <van-popover
              :close-on-click-outside="true"
              get-container="#vinHistory"
              v-model="showPopoverVin"
              trigger="click"
              :actions="actionsVin"
              placement="bottom-start"
              @select="onSelectVin"
            />
          </div>
          <van-loading
            class="lan-loading-custom lan-custom-center lan-loading-darkly enable-masking-out lan-loading-decephaly"
            size="24px"
            vertical
            v-if="vanloading"
          >
            正在加载···
          </van-loading>
          <!-- <van-popover
                get-container="#vin"
                v-model="showPopoverVin"
                trigger="click"
                :actions="actionsVin"
                placement="bottom-start"
                @select="onSelectVin"
              />             -->
          <!-- </div> -->
          <van-field
            is-link
            :placeholder="placeholder"
            label="车系"
            :label-align="labelAlign"
            :label-width="labelWidth"
            @click="onSelectSeries"
            v-model="seriesName"
            ref="seriesName"
            type="text"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择车系'
              }
            ]"
          />

          <van-field
            v-if="false"
            is-link
            readonly
            v-show="!omdHasVin"
            label="车型"
            placeholder="请选择车型"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="modelName"
            ref="modelName"
            type="text"
            @click="onSelectModels"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择车型'
              }
            ]"
          />

          <div class="box-field" @click="onInputCarNo" id="carNo">
            <label class="box-label" />
            <van-field
              placeholder="请输入您的车牌"
              label="车牌"
              type="text"
              cols="24"
              autosize
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="submitParam.carNo"
              ref="carNo"
              @blur="handlerBlur('carNo')"
              @focus="handlerFocus('carNo')"
              :rules="[
                {
                  trigger: 'onBlur',
                  required: true,
                  message: '请填写车牌！'
                }
              ]"
            />
          </div>

          <div
            class="box-field"
            @click.stop="animation('takeConnectName')"
            id="takeConnectName"
          >
            <van-field
              label="姓名"
              placeholder="请输入您的姓名"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="submitParam.takeConnectName"
              ref="takeConnectName"
              type="text"
              clearable
              @blur="handlerBlur('takeConnectName')"
              @focus="handlerFocus('takeConnectName')"
              @change="saveTakeConnectName()"
              :rules="[
                {
                  trigger: 'onBlur',
                  required: true,
                  message: '请填写姓名！'
                }
              ]"
            />
          </div>

          <div
            class="box-field"
            @click.stop="animation('takeConnectPhone')"
            id="takeConnectPhone"
          >
            <van-field
              placeholder="请输入您的联系电话"
              label="联系电话"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="submitParam.takeConnectPhone"
              ref="takeConnectPhone"
              maxlength="11"
              type="number"
              clearable
              on-key-up="value=value.replace(/[\W]/g,'')"
              @blur="handlerBlur('takeConnectPhone')"
              @focus="handlerFocus('takeConnectPhone')"
              @change="saveTakeConnectPhone()"
              :rules="[
                {
                  trigger: 'onBlur',
                  required: true,
                  message: '请填写联系方式！'
                },
                {
                  pattern: /\w{11}/,
                  message: '请输入正确的手机号'
                }
              ]"
            />
            <!-- <van-popover
            v-model="showPopover"
            :close-on-click-outside="true"
            trigger="click"
            :actions="actions"
            placement="bottom-start"
            @select="onSelect"
          /> -->
          </div>
        </van-cell-group>

        <van-cell-group>
          <div class="cell-title van-hairline--bottom">
            <h2 class="h2">
              {{ orderType === '20131010' ? '取车信息' : '送车信息' }}
            </h2>
          </div>


          <div @click="onTakeAddress">
            <van-field
              is-link
              readonly
              v-if="orderType === '20131010'"
              label="取车地址"
              placeholder="请选择"
              type="text"
              cols="24"
              autosize
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="takeAddress"
              ref="takeAddress"
              :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写取车地址！'
              }
            ]"
            />
          </div>

          <van-field
            v-if="orderType === '20131010'"
            label="详细地址"
            type="text"
            clearable
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="takeAddressSupplement"
            placeholder="请输入详细地址(选填)"
            rows="1"
            @blur="ontakeAddressBlur"
          />

          <div @click="onSendAddress">
            <van-field
              is-link
              readonly
              placeholder="请选择"
              v-if="orderType === '20131020'"
              label="送还地址"
              type="text"
              cols="24"
              autosize
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="sendAddress"
              ref="sendAddress"
              :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写送还地址！'
              }
            ]"
            />
          </div>

          <van-field
            label="详细地址"
            v-if="orderType === '20131020'"
            type="text"
            cols="24"
            autosize
            clearable
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="sendAddressSupplement"
            placeholder="请输入详细地址(选填)"
            rows="1"
          />

          <van-field
            is-link
            readonly
            placeholder="请选择"
            v-if="orderType === '20131010'"
            label="取车时间"
            type="text"
            cols="24"
            autosize
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="takeDatetime"
            ref="takeDatetime"
            @click="onTakeDatetime"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写取车时间！'
              }
            ]"
          />

          <van-field
            is-link
            readonly
            v-if="orderType === '20131020'"
            label="送车时间"
            placeholder="请选择"
            type="text"
            cols="24"
            autosize
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="sendDatetime"
            ref="sendDatetime"
            @click="onSendDatetime"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写送车时间！'
              }
            ]"
          />
        </van-cell-group>

        <van-cell-group v-if="isTakeAndSend">
          <div class="cell-title van-hairline--bottom">
            <h2 class="h2">
              送车信息
            </h2>
          </div>
          <div class='send-list-warp'>
            <div class='a-list-left'>服务结束后送还到原取车地址</div>
            <div class='a-list-right'>
              <van-switch v-model="sendOrTakeChecked" size="24px" active-color="#000" inactive-color="#eee" @change="changeSendOrTakeChecked"/>
            </div>
          </div>
          <div @click="onSendAddress">
            <van-field
              is-link
              readonly
              v-if="orderType === '20131010' && !sendOrTakeChecked"
              label="送还地址"
              placeholder="请选择"
              type="text"
              cols="24"
              autosize
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="sendAddress"
              ref="sendAddress"
              :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择服务类型！'
              }
            ]"
            />
          </div>
          <van-field
            v-if="orderType === '20131010' && !sendOrTakeChecked"
            label="详细地址"
            type="text"
            cols="24"
            autosize
            clearable
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="sendAddressSupplement"
            ref="sendAddressSupplement"
            placeholder="请输入详细地址(选填)"
            rows="1"
          />
          <van-field
            is-link
            readonly
            v-if="orderType === '20131010'"
            label="送车时间"
            placeholder="请选择"
            type="text"
            cols="24"
            autosize
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="sendDatetime"
            ref="sendDatetime"
            @click="onSendDatetime"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写送车时间！'
              }
            ]"
          />
        </van-cell-group>

        <van-cell-group>
          <div class="cell-title van-hairline--bottom service-line">
            <div><h2 class="h2">取送车券</h2></div>
            <div class="btn-change" @click="onSendCarPrice">
              <img class="btn-icon" src="../../assets/img/icon_explain.png" />
            </div>
          </div>

          <!-- 单订单（取车or送车） -->
          <div v-if="!isTakeAndSend">
            <!-- <span v-if="orderType === '20131010'">{{ takeCouponName }}</span>
          <span v-if="orderType === '20131020'">{{ sendCouponName }}</span> -->
            <van-field
              is-link
              readonly
              placeholder="请选择卡券"
              :label="orderType === '20131010' ? '取车卡券' : '送车卡券'"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="handleCouponName"
              type="text"
              @click="onSelectEticket('')"
            />
          </div>

          <!-- 取送车订单 -->
          <div v-if="isTakeAndSend">
            <van-field
              is-link
              readonly
              placeholder="请选择卡券"
              label="取车卡券"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="takeCouponName"
              type="text"
              @click="onSelectEticket('takeCar')"
            />
            <van-field
              is-link
              readonly
              placeholder="请选择卡券"
              label="送车卡券"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="sendCouponName"
              type="text"
              @click="onSelectEticket('sendCar')"
            />
          </div>
        </van-cell-group>
      </van-form>
    </div>

    <van-popup
      v-model:show="selectvehicle"
      position="bottom"
      safe-area-inset-bottom
      :style="{ 'max-height': '80vh', 'min-height': '50vh' }"
    >
      <div class="pop-coupons-box">
        <div
          class="pop-coupons-hd van-hairline--bottom"
          data-flex="main:justify cross:center"
        >
          <p class="btn close">
            <van-icon
              class="icon-close"
              name="cross"
              size="20"
              @click="handleActionPopCoupons"
            />
          </p>
          <h3 class="h3">选择车辆</h3>
          <p class="btn finish"></p>
        </div>
        <div class="pop-coupons-main">
          <template v-if="mineCoupons && mineCoupons.length">
            <div class="coupons">
              <van-radio-group v-model="mineCouponsChecked">
                <van-cell-group inset>
                  <van-cell
                    :class="[
                      'list',
                      list.vin === mineCouponsChecked ? 'radio-checked' : ''
                    ]"
                    @click="chooseVin(list)"
                    v-for="(list, index) in mineCoupons"
                    :key="list.vin"
                  >
                    <template #title>
                      <div class="coupon-box">
                        <h4 class="h4 text-one-hid">{{ list.modelNameCn }}</h4>
                        <p><span>VIN：</span>{{ list.vin }}</p>
                      </div>
                    </template>
                    <template #right-icon>
                      <van-radio :name="list.vin" checked-color="#000" />
                    </template>
                  </van-cell>
                </van-cell-group>
              </van-radio-group>
            </div>
          </template>
        </div>
      </div>
    </van-popup>

    <div
      v-if="qusongbianl"
      style="color: #999; font-size: 12px; margin-top: 16px"
    >
      {{ kmPromptTitle }}
    </div>
    <!-- <div
      v-if="orderType === '20131020' && sendDistanceShow"
      style="color: #999; font-size: 12px; margin-top: 16px"
    >
      您的服务距离已超出免费服务距离，关于超出部分处理方式，稍后由售后专家与您取得联系
    </div> -->
     <div class="btn-delete-height" />
    <div>
      <div class="bottom_style"  >
        <div class="checkbox_style" @click="checboxCick">
          <van-checkbox
            v-model="isSonsent"
            >
            <template slot="icon">
              <div :class='["checkbox_button_default", isSonsent && "checkbox_button_active"]'>
                <img
                  class="checkbox_button"
                  :src="activeRadioIcon"
                  alt=''/>
              </div>
            </template>
            <span class='agreement-text-pre' style="color: #666666; font-size: 12px"> 我已阅读并确认 </span>
            <span @click="onImportantNote" class='agreement-text'
              >《上汽奥迪取送车服务协议》
            </span>
          </van-checkbox>
        </div>

        <div class="btn-delete-wrapper page-submit-btn">
          <AudiButton
            @click="onAffirm"
            :text="'提交'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
      </div>
    </div>

    <van-action-sheet
      v-model="show"
      :safe-area-inset-bottom="true"
      :title="actualMileage"
      @cancel="onCouponCancel"
      @click-overlay="onCouponClose"
    >
      <div class="input-line" style="margin: 18px" v-if="bindCarVIN !== vin">
        <div class="input-change">
          <div style="width: 100px">卡券券码</div>
          <input class="input-name" type="text" v-model="mpEquityNo" rows="1" />
        </div>
      </div>


      <!--  v-if="item.showItem" -->
      <div v-for="(item, idx) in eticketList" :key="idx">

        <div  :class="item.nochoose ? 'eticket nochoose' : 'eticket'" >
          <div>
            <div :class="  item.nochoose ? 'eticket-title nochoose-title' : 'eticket-title' "  >
              {{ item.mainTitle }}
            </div>
            <div
              :class="
                item.nochoose ? 'eticket-date nochoose-date' : 'eticket-date'
              "
            >
              可覆盖距离：{{ item.mileage }}km
            </div>
            <div
              :class="
                item.nochoose ? 'eticket-date nochoose-date' : 'eticket-date'
              "
            >
              有效期至{{ item.effectiveEndDate.substring(0, 10) }}
            </div>
          </div>

          <div class="eticket-btn-change">
            <div
              :class="
                item.nochoose
                  ? 'eticket-change-name nochoose-change-name'
                  : 'eticket-change-name'
              "
            >
              {{ '1张' }}
            </div>

            <van-checkbox
              class="eticket-btn-icon"
              v-model="item.checked"
              :disabled="item.nochoose"
              @click="onCheckbox(item)"
            >
              <img
                style="height: 18px; width: 18px"
                slot="icon"
                :src=" item.checked && !item.nochoose  ? activeRadioIcon  : inactiveRadioIcon " />
            </van-checkbox>
          </div>
        </div>
      </div>
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onEticketSelect"
          :text="'确定使用'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </van-action-sheet>

    <van-action-sheet
      v-model="showSelectService"
      :safe-area-inset-bottom="true"
      title="请选择服务类型"
      @cancel="onSheetCancel"
      @click-overlay="onSheetClose"
    >
      <div class="eticket" @click="onTabClick(2)">
        <div>
          <div class="eticket-title">
            {{ '取送车服务' }}
          </div>
          <div style="color: #999; font-size: 12px; margin-top: 8px">
            {{ '包含完整的取车和送车服务' }}
          </div>
        </div>
        <div class="eticket-btn-change">
          <van-icon class="btn-icon" name="arrow" size="16px" />
        </div>
      </div>
      <div class="eticket" @click="onTabClick(0)">
        <div>
          <div class="eticket-title">
            {{ '取车服务' }}
          </div>
          <div style="color: #999; font-size: 12px; margin-top: 8px">
            {{ '从您指定地点取车后送至上汽奥迪授权服务商处' }}
          </div>
        </div>
        <div class="eticket-btn-change">
          <van-icon class="btn-icon" name="arrow" size="16px" />
        </div>
      </div>
      <div class="eticket" @click="onTabClick(1)">
        <div>
          <div class="eticket-title">
            {{ '送车服务' }}
          </div>
          <div style="color: #999; font-size: 12px; margin-top: 8px">
            {{ '从上汽奥迪授权服务商处取车后送至您指定地点' }}
          </div>
        </div>
        <div class="eticket-btn-change">
          <van-icon class="btn-icon" name="arrow" size="16px" />
        </div>
      </div>
    </van-action-sheet>

    <van-popup
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal">
        <div class="modal-content" style="margin: 16px">
          {{ modalTitle }}
        </div>

        <div>
          <div class="modal-line">
            <div class="title-bold">取车订单</div>
            <div class="btn-change" @click.stop="onclickTake">
              <img
                class="btn-icon"
                :src="isTake ? activeRadioIcon : inactiveRadioIcon"
              />
            </div>
          </div>
          <div class="modal-line" style="margin-top: 8px">
            <div class="title-bold">送车订单</div>
            <div class="btn-change" @click.stop="onclicklSend">
              <img
                class="btn-icon"
                :src="isSend ? activeRadioIcon : inactiveRadioIcon"
              />
            </div>
          </div>
        </div>
        <div class="modal-confirm center" @click.stop="onConfirm">
          {{ '确定' }}
        </div>
        <div class="modal-cancel center" @click.stop="onCancel">
          {{ '取消' }}
        </div>
      </div>
    </van-popup>
    <popup-custom-action-btn
      v-if="CreateDeliverCarPop.conf.enabled || false"
      :btn-conf.sync="CreateDeliverCarPop.conf"
      :btn-items="CreateDeliverCarPop.items"
      @emitGetBackBtn="onSheetCancel"
      @emitGetActionBtn="
        $router.push({ name: 'afterservice-order-list', query: { type: 2 } })
      "
    >
      <template #popup-custom-main>
        <div class="popup-content-text custom-align-center">
          已存在未结束的取送车需求订单，请前往<br />
          “我的订单-服务-取送车”，查看订单或<br />
          结束订单后重新发起
        </div>
      </template>
    </popup-custom-action-btn>
    <van-dialog
      v-model="updateVis"
      :title="`您还有取送车券未选取，请点击对应的取、送车服务使用。`"
    >
      <div class="button">
        <div class="ContinueSubmit" @click="ContinueSubmit">继续提交</div>
        <div class="goUse" @click="goUse">去使用卡券</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Toast,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Tab,
  Tabs,
  ActionSheet,
  Switch,
  Popover
} from 'vant'
import popupCustomActionBtn from '@/components/popup-custom-action-btn.vue'
import { callNative, getLocationAddress, getdiscount2 } from '@/utils'
import {
  postCreateDeliverCar,
  postBindCardInfo,
  getCarCoupon,
  getDeliverCarQueryCityList,
  getDeliverCarQueryDealer,
  getAfterSalesByVin,
  getBindCarInfoList
} from '@/api/api'
import storage from '../../utils/storage'

import AudiButton from '@/components/audi-button'
import POPUP_CUSTOM from '@/config/popup-custom.data'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Tab)
  .use(Tabs)
  .use(ActionSheet)
  .use(Popover)
  .use(Switch)

export default {
  components: {
    AudiButton,
    'popup-custom-action-btn': popupCustomActionBtn
  },
  data() {
    return {
      isSonsent:false,
      actualMileage: '可用卡券', // 实际公里数
      kmPromptTitle: '',
      takeCarMileage: '', // 取车公里数
      sendCarMileage: '', // 送车公里数
      mileage: '', // 里程
      vouchersstate: '',
      updateVis: false,
      CreateDeliverCarPop: { ...POPUP_CUSTOM.CARS_SERVICE },
      showSelectService: false,
      modalshow: false,
      isTake: false,
      isSend: false,
      showPopover: false,
      actions: [], // { text: '15000876160' }
      showPopoverVin: false,
      actionsVin: [],
      show: false,
      labelWidth: 84,
      labelAlign: 'left',
      isTakeAndSend: true, // 是否取送车
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      activeRadioIcon: require('../../assets/img/radio_checked.png'),
      inactiveRadioIcon: require('../../assets/img/radio_normal.png'),
      animations: {
        takeConnectName: false,
        takeConnectPhone: false,
        carNo: false,
        vin: false
      },

      submitParam: {
        takeConnectName: '', // 取车联系人
        takeConnectPhone: '', // 取车联系人电话

        carNo: '' // 车牌
      },
      vin: '', // VIN码

      isInit: '0', // 是否是初次进来
      // modelCode: "", // 车型代码
      // modelName: "", // 车型中文名称
      orderType: '20131010', // 订单类型(20131010:取车;20131020:送车;20131030:取送车;)
      // remark:'',//备注

      handleCouponName: '暂无卡券可使用',
      // sendDatetime:'',//送车时间
      sendIndexNo: '', // 送车券码
      sendEticketType:"",//送车卡券使用业务类型
      sendCouponName: '暂无卡券可使用', // 送车券名称
      // seriesCode: "", // 车系代码
      // seriesName: "", // 车系中文名称
      // serviceType:1,//服务类型(20141010:保养;20141020:维修;20141030:其他;)

      // takeDatetime:'',//取车时间(yyyy-MM-dd HH:mm:ss)
      takeIndexNo: '', // 取车券码,eg:K6SHo94VB0HESM
      takeEticketType:"",//取车卡券使用业务类型
      takeCouponName: '暂无卡券可使用', // 取车券名称
      eticketList: [],
      sendDistanceShow: false,
      takeDistanceShow: false,
      initCityName: '', // 初始进来的城市
      cityAddressList: [],
      initTakeAddress: '', // 初始进来的地址
      initSenndAddress: '', // 初始进来的地址

      modelCode: '', // 车型code
      modelName: '', // 车型code
      seriesCode: '', // 车系代码
      seriesName: '', // 车系名称

      dealerCode: '', // 服务商代码
      dealerName: '', // 服务商名称
      dealerPhone: '', // 服务商电话
      dealerAddress: '', // 服务商地址
      dealerLatitude: 0, // 服务商经纬度
      dealerLongitude: 0, // 服务商经纬度

      takeAddressSupplement: '', // 取车补充地址
      sendAddressSupplement: '', // 送车补充地址
      mpEquityNo: '', // 卡券券码
      bindCardList: [], // 绑定人车关系的列表
      couponModel: {}, // 选择卡券使用方式时的卡券数据
      validCouponList: [], // 有效的券
      couponList: [], // 选择的券
      preCouponList: [], // 打开选券弹窗的前的选中数据
      bindCarVIN: '', // 绑定的vin码
      modalTitle: '因您只有一张卡券可用，需要选择使用至哪项服务',

      bindDealerCode: '',
      qusongbianl: false,
      isShowBindCard: false,
      mineCoupons: [],
      mineCouponsChecked: 0,
      mineCouponsCheckedObject: {},
      selectvehicle: false,
      omdHasVin: true, // omd查询是否能查到vin
      loading: false,
      placeholder: '请选择',
      vanloading: false,
      // 新增服务结束后送还到原取车地址
      sendOrTakeChecked: false,

    }
  },
  computed: {
    ...mapState({
      serviceType: (state) => state.selectServiceType.serviceType || '1', // 服务类型
      serviceName: (state) => state.selectServiceType.serviceName || '保养', // 服务类型
      remark: (state) => state.selectServiceType.inputServiceRemark || ' ', // 备注

      takeDatetime: (state) => state.takeDatetime.appointmentTime || '', // 取车预约时间
      sendDatetime: (state) => state.sendDatetime.appointmentTime || '', // 送车预约时间

      takeAddress: (state) => state.takeAddress1.takeAddress || '', // 取车地址
      takeLat: (state) => state.takeAddress1.takeLat, // 取车纬度
      takeLng: (state) => state.takeAddress1.takeLng, // 取车经度
      takeCityCode: (state) => state.takeAddress1.takeCityCode || '', // 取车城市

      sendAddress: (state) => state.sendAddress1.sendAddress || '', // 送车地址
      sendLat: (state) => state.sendAddress1.sendLat, // 送车纬度
      sendLng: (state) => state.sendAddress1.sendLng, // 送车经度
      sendCityCode: (state) => state.sendAddress1.sendCityCode || '' // 送车城市
    })
  },
  watch: {
    // 判断vin码
    vin(value) {
      if (!value) {
        return
      }
      if (value.length === 17) {
        this.couponList = []
        this.getCarCoupon()
        this.showPopoverVin = false
        storage.set('vin', value)
      } else {
        storage.set('vin', value)
        this.takeIndexNo = ''
        this.takeEticketType = ''
        this.takeCouponName = ''
        this.sendIndexNo = ''
        this.sendEticketType = ''
        this.sendCouponName = ''
        this.clearCouponList()
        this.eticketList = []
        // 判断前三位是否是LSV
        if (value.length > 0 && value.substring(0, 1) !== 'L') {
          this.vin = ''
          this.removeVin()
          return
        }
        if (value.length > 1 && value.substring(1, 2) !== 'S') {
          this.vin = 'L'
          this.removeVin()
          return
        }
        if (value.length > 2 && value.substring(2, 3) !== 'V') {
          this.vin = 'LS'
          this.removeVin()
        }
      }
    },
    // 输入的卡券券码监听
    mpEquityNo(valNo) {
      this.changeEquityNo(valNo)
    }
  },


  mounted() {
    let routePreLink = sessionStorage.getItem('routePreLink')
    if (routePreLink && routePreLink === '/userAgreement/create-deliver-car') {
      this.isSonsent = sessionStorage.getItem('isSonsent') === '1'
      // 使用完重置
      sessionStorage.removeItem('routePreLink')
      sessionStorage.removeItem('isSonsent')
    }
    this.sendOrTakeChecked = sessionStorage.sendOrTakeChecked == 1
    this.initMapData()
    console.log('查看旧地址++++++', this.couponList)
    const selectModel = storage.get('ownerVinInfo') || '{}'
    this.modelName = JSON.parse(selectModel).modelLineName || ''
    this.modelCode = JSON.parse(selectModel).modelCode || ''
    this.seriesCode = JSON.parse(selectModel).seriesCode || ''
    this.seriesName = JSON.parse(selectModel).seriesName || ''

    const dealerModel = storage.get('dealerCarModel') || '{}'
    this.dealerCode = JSON.parse(dealerModel).serviceCode || '' // 服务商代码
    this.dealerName = JSON.parse(dealerModel).dealerName // 服务商名称
    this.dealerPhone = JSON.parse(dealerModel).dealerPhone // 服务商电话
    this.dealerAddress = JSON.parse(dealerModel).dealerAdrress || '' // 服务商地址
    this.dealerLatitude = JSON.parse(dealerModel).latitude || 0 // 服务商经纬度
    this.dealerLongitude = JSON.parse(dealerModel).longitude || 0 // 服务商经纬度

    if (this.$store.state?.isInitView) {
      this.isInit = this.$store.state.isInitView
    }
    if (this.$store.state?.takeConnectName) {
      this.submitParam.takeConnectName = this.$store.state.takeConnectName
    } else {
      this.submitParam.takeConnectName = storage.get('takeConnectName') || ''
    }
    if (this.$store.state?.takeConnectPhone) {
      this.submitParam.takeConnectPhone = this.$store.state.takeConnectPhone
    } else {
      this.submitParam.takeConnectPhone = storage.get('takeConnectPhone') || ''
    }

    if (this.$store.state?.takeAddressSupplement) {
      this.takeAddressSupplement = this.$store.state.takeAddressSupplement
    }
    if (this.$store.state?.sendAddressSupplement) {
      this.sendAddressSupplement = this.$store.state.sendAddressSupplement
    }
    this.submitParam.carNo = storage.get('carNo') || ''
    if (this.$store.state?.vin) {
      this.vin = this.$store.state.vin
    }
    this.vin = storage.get('vin') || ''

    if (this.$store.state?.orderType) {
      this.orderType = this.$store.state.orderType
      if (this.orderType === '20131010') {
        this.$store.state.title = '取车服务'
      } else if (this.orderType === '20131020') {
        this.$store.state.title = '送车服务'
      }
    }
    if (this.$store.state?.showSelectService !== undefined) {
      this.showSelectService = this.$store.state.showSelectService
    }

    if (this.$store.state?.takeIndexNo) {
      this.takeIndexNo = this.$store.state.takeIndexNo
      this.takeEticketType = this.$store.state.takeEticketType
    }
    if (this.$store.state?.takeCouponName) {
      this.takeCouponName = this.$store.state.takeCouponName
    }
    if (this.$store.state?.sendIndexNo) {
      this.sendIndexNo = this.$store.state.sendIndexNo
      this.sendEticketType = this.$store.state.sendEticketType
    }
    if (this.$store.state?.sendCouponName) {
      this.sendCouponName = this.$store.state.sendCouponName
    }

    if (this.$store.state?.takeAndSend !== undefined) {
      this.isTakeAndSend = this.$store.state.takeAndSend
      if (this.isTakeAndSend) {
        this.$store.state.title = '取送车服务'
      }
    }

    if (this.$store.state?.bindCarVIN) {
      this.bindCarVIN = this.$store.state.bindCarVIN
    }
    if (this.$store.state?.bindDealerCode) {
      this.bindDealerCode = this.$store.state.bindDealerCode
    }
    const mobile = storage.get('serviceUserMobile') || ''
    if (mobile) {
      const mobileList = mobile.split(',')
      for (let i = 0; i < mobileList.length; i++) {
        if (mobileList[i] && i < 3) {
          this.actions.push({ text: mobileList[i] })
        }
      }
    }

    // const userVin = storage.get('serviceUserVIN') || ''
    // if (userVin) {
    //   const userVinList = userVin.split(',')
    //   for (let i = 0; i < userVinList.length; i++) {
    //     if (userVinList[i] && i < 3) {
    //       this.actionsVin.push({ text: userVinList[i] })
    //     }
    //   }
    // }

    if (this.isInit === '0') {
      this.showSelectService = true
    } else {
      this.showSelectService = false
      // 需送车时 如果取车地址偶的话赋值给送车地址
      if (this.isTakeAndSend && this.takeAddress && !this.sendAddress) {
        this.$store.commit('saveSendDAddress', {
          sendAddress: this.takeAddress, // 送车地址
          sendLat: this.takeLat, // 送车纬度
          sendLng: this.takeLng, // 送车经度
          sendCityCode: this.takeCityCode // 选择的城市
        })
      }
    }
    this.changeEdit()
  },
  methods: {
    checboxCick(){
      console.log(12323)
    },
    async initMapData(){
      const data = await callNative('getLocationCity', {})
      console.log("initMapData原生返回的地理数据：",data)
      this.location = data.location
      this.initCityName = data.city
    },
    saveTakeConnectName(val) {
      storage.set('takeConnectName', this.submitParam.takeConnectName)
    },
    saveTakeConnectPhone(val) {
      storage.set('takeConnectPhone', this.submitParam.takeConnectPhone)
    },
    chooseVin(list) {
      this.mineCouponsChecked = list.vin
      this.mineCouponsCheckedObject = list
      this.handleFinishPopCoupons()
    },
    handleActionPopCoupons() {
      this.selectvehicle = false
    },
    handleFinishPopCoupons() {
      this.selectvehicle = false
      console.log('mineCouponsChecked', this.mineCouponsCheckedObject)
      this.modelCode = this.mineCouponsCheckedObject.modelCode
      this.vin = this.mineCouponsCheckedObject.vin
      this.modelName = this.mineCouponsCheckedObject.modelNameCn
      this.seriesCode = this.mineCouponsCheckedObject.seriesCode
      this.seriesName = this.mineCouponsCheckedObject.seriesName
      this.bindCarVIN = this.mineCouponsCheckedObject.vin
      this.$store.state.vin = this.vin
    },
    async asyncValidatorVin(val) {
      if (val.length != 17) {
        this.omdHasVin = false
        this.$store.state.vin = val
        this.vin = val
        return false
      } else {
        this.loading = true
        this.placeholder = '查询中'
        const vinInfo = JSON.parse(storage.get('ownerVinInfo'))
        console.log('111111111111111', vinInfo)

        if (vinInfo) {
          this.seriesName = vinInfo.seriesName || ''
          this.modelName = vinInfo.modelLineName || ''
          this.modelCode = vinInfo.modelCode || ''
          this.seriesCode = vinInfo.seriesCode || ''
        } else {
          this.seriesName = ''
          this.modelName = ''
          this.modelCode = ''
          this.seriesCode = ''
        }

        const { data } = await getBindCarInfoList({ vins: val })
        if (!data || data.data == null || data.data.length === 0) {
          this.omdHasVin = false
          this.loading = false
          this.placeholder = '请选择'

          return false
        }
        if (data.data.length > 0) {
          this.omdHasVin = true
          this.modelCode = data.data[0].modelCode
          this.vin = data.data[0].vin
          this.modelName = data.data[0].modelNameCn
          this.seriesCode = data.data[0].seriesCode
          this.seriesName = data.data[0].seriesName
          this.bindCarVIN = data.data[0].vin
          this.$store.state.vin = this.vin
          this.loading = false
          return true
        }
        return false
      }
    },
    async goUse() {
      if (this.eticketList.length > 0) {
        this.show = true
        this.updateVis = false
        this.$forceUpdate()
        const takeDistance = await getdiscount2(
          this.dealerLongitude,
          this.dealerLatitude,
          this.takeLng,
          this.takeLat
        )
        const sendDistance = await getdiscount2(
          this.dealerLongitude,
          this.dealerLatitude,
          this.sendLng,
          this.sendLat
        )
        // 计算实际公里数
        if (this.isTakeAndSend) {
          if (
            this.takeCouponName === '有可用卡券' &&
            this.sendCouponName === '有可用卡券'
          ) {
            this.vouchersstate = 'takeCar'
          } else if (this.takeCouponName === '有可用卡券') {
            this.vouchersstate = 'takeCar'
          } else if (this.sendCouponName === '有可用卡券') {
            this.vouchersstate = 'sendCar'
          }
          if (this.vouchersstate === 'takeCar' && this.takeAddress) {
            this.actualMileage = `取车距离${Math.round(takeDistance / 1000)}km`
          }
          if (this.vouchersstate === 'sendCar' && this.sendAddress) {
            this.actualMileage = `送车距离${Math.round(sendDistance / 1000)}km`
          }
        } else {
          if (this.orderType === '20131010') {
            if (this.takeAddress) {
              this.actualMileage = `取车距离${Math.round(
                takeDistance / 1000
              )}km`
            }
          } else {
            if (this.sendAddress) {
              this.actualMileage = `送车距离${Math.round(
                sendDistance / 1000
              )}km`
            }
          }
        }
        if (this.isTakeAndSend) {
          if (
            this.takeCouponName === '有可用卡券' &&
            this.sendCouponName === '有可用卡券'
          ) {
            // 两种都未选
            console.log('两张都未选择，默认跳转取车')
            this.vouchersstate = 'takeCar'
            const obj_send = this.couponList.find((e) => e.send == 1) || {}
            const obj_take = this.couponList.find((e) => e.take == 1) || {}
            console.log('取车选中的数据obj_take', obj_take)
            this.eticketList.forEach((e) => {
              if (obj_send.couponEquityId == e.couponEquityId) {
                e.nochoose = true // 不可选
                e.checked = false
              }
              if (obj_take.couponEquityId == e.couponEquityId) {
                e.nochoose = false // 可选、
                e.checked = true // 已选中、
                console.log('quche可选')
              }
            })
            return
          }
          if (this.takeCouponName === '有可用卡券') {
            console.log('取车未选择，默认跳转取车')
            this.vouchersstate = 'takeCar'
            const obj_send = this.couponList.find((e) => e.send == 1) || {}
            const obj_take = this.couponList.find((e) => e.take == 1) || {}
            console.log('取车选中的数据obj_take', obj_take)
            this.eticketList.forEach((e) => {
              if (obj_send.couponEquityId == e.couponEquityId) {
                e.nochoose = true // 不可选
                e.checked = false
              }
              if (obj_take.couponEquityId == e.couponEquityId) {
                e.nochoose = false // 可选、
                e.checked = true // 已选中、
                console.log('quche可选')
              }
            })
            return
          }
          if (this.sendCouponName === '有可用卡券') {
            console.log('送车未选择，默认跳转取车')
            this.vouchersstate = 'sendCar'
            const obj_send = this.couponList.find((e) => e.send == 1) || {}
            const obj_take = this.couponList.find((e) => e.take == 1) || {}
            this.eticketList.forEach((e) => {
              if (obj_send.couponEquityId == e.couponEquityId) {
                e.nochoose = false
                e.checked = true // 已选中、
              }
              if (obj_take.couponEquityId == e.couponEquityId) {
                e.nochoose = true
                e.checked = false
              }
            })
          }
        }
      }
    },
    ContinueSubmit() {
      this.updateVis = false
      let orderType1 = this.orderType
      if (this.isTakeAndSend) {
        orderType1 = '20131030'
      }
      this.toSubmit(orderType1)
    },
    ontakeAddressBlur(e) {
      // 当送车地址详情没有数据的是好事取车详细地址
      if (!this.sendAddressSupplement) {
        this.sendAddressSupplement = this.takeAddressSupplement
      }
    },
    removeVin() {
      callNative('toast', {
        type: 'fail',
        message: '输入的VIN码不正确'
      })
    },
    // 选处理当前地址
    async getBindCardInfo() {
      this.postBindCardInfo()

      const data = await callNative('getLocationCity', {})
      console.log("原生返回的地理数据：",data)
      this.location = data.location
      this.initCityName = data.city
      console.log('getLocationCity', data)
      // 默认当前经纬度
      // this.takeLat = this.location.split(',')[0]
      // this.takeLng = this.location.split(',')[1]
      // this.sendLat = this.location.split(',')[0]
      // this.sendLng = this.location.split(',')[1]

      const address = await getLocationAddress([
        this.location.split(',')[1],
        this.location.split(',')[0]
      ])
      console.log('getLocationAddress:', address)
      // 先默认当前地址

      this.initTakeAddress = address
      this.initSenndAddress = address

      this.$store.commit('saveTakeAddress', {
        takeAddress: '', // 取车地址
        takeLat: this.location.split(',')[0], // 取车纬度
        takeLng: this.location.split(',')[1] // 送车经度
      })

      this.$store.commit('saveSendDAddress', {
        sendAddress: '', // 送车地址
        sendLat: this.location.split(',')[0], // 送车纬度
        sendLng: this.location.split(',')[1] // 送车经度
      })
      // 获取服务商信息
      this.getDeliverCarQueryCityList()
    },
    // 获取服务商城市
    async getDeliverCarQueryCityList() {
      const { data } = await getDeliverCarQueryCityList()
      if (data.code === '200') {
        this.columns = data.data
        const item = this.columns.find((i) => i.cityName === this.initCityName)
        // 当前城市没有服务商暂时先不显示服务商
        if (item !== undefined) {
          this.getCityAddressList(item)
        }
      }
    },
    async getCityAddressList(value) {
      this.$store.commit('showLoading')
      const { data } = await getDeliverCarQueryDealer({
        code: value.cityCode
      })
      if (data.code === '200') {
        this.cityAddressList = data.data
        // 如果只有一条数据直接取
        if (this.cityAddressList.length === 1) {
          this.$store.commit('saveDealer', this.cityAddressList[0])
          this.$store.commit('hideLoading')
        } else {
          // 高德api计算两点之间的距离 取离自己最近的一条选中
          const location = this.location.split(',')
          const results = await Promise.all(
            this.cityAddressList.map(async (dealer) => {
              if (dealer.latitude && dealer.longitude) {
                console.log(
                  '距离计算 ',
                  location[1],
                  location[0],
                  dealer.longitude,
                  dealer.latitude
                )

                return getdiscount2(
                  location[1],
                  location[0],
                  dealer.longitude,
                  dealer.latitude
                )
              }
              return 0
            })
          )

          this.cityAddressList.forEach((dealer, i) => {
            dealer.distance = results[i]
          })
          console.log(this.cityAddressList)
          this.cityAddressList.sort(this.compare('distance'))
          this.cityAddressList = this.resort(this.cityAddressList)
          console.log(this.cityAddressList)
          this.$store.commit('hideLoading')
          this.$store.commit('saveDealer', this.cityAddressList[0])
        }
      }
    },
    compare(prop) {
      return function (obj1, obj2) {
        const val1 = obj1[prop]
        const val2 = obj2[prop]
        if (val1 < val2) {
          return -1
        }
        if (val1 > val2) {
          return 1
        }
        return 0
      }
    },

    resort(addressAllList) {
      const idx = addressAllList.findIndex((item) => item.distance !== 0)
      const truncate = addressAllList.slice(0, idx)
      addressAllList.splice(0, idx)
      return addressAllList.concat(truncate)
    },
    async getBindCarInfoList() {
      if (this.actionsVin && this.actionsVin.length > 0) {
        return
      }
      const { data } = await postBindCardInfo({})
      if (data.data.length > 0) {
        if (!this.actionsVin || this.actionsVin.length === 0) {
          this.actionsVin = []
          for (let item of data.data) {
            if (item.vin) {
              this.actionsVin.push({ text: item.shortName + ' - ' + item.vin })
            }
            if (this.vin && this.vin === item.vin) {
              this.submitParam.carNo =
                item.plateNumber || storage.get('carNo') || ''
              storage.set('carNo', this.submitParam.carNo)
            }
          }
        }
      }
    },
    // 获取绑定车辆
    async postBindCardInfo() {
      this.vanloading = true

      try {
        const { data } = await postBindCardInfo({})
      this.bindCardList = data.data
      this.isShowBindCard = !data.data?.length
      if (this.bindCardList.length > 0) {
        if (!this.actionsVin || this.actionsVin.length === 0) {
          this.actionsVin = []
          for (let item of this.bindCardList) {
            if (item.vin) {
              this.actionsVin.push({ text: item.shortName + ' - ' + item.vin })
            }
            if (this.vin && this.vin === item.vin) {
              this.submitParam.carNo =
                item.plateNumber || storage.get('carNo') || ''
              storage.set('carNo', this.submitParam.carNo)
            }
          }
        }

        // this.seriesCode = data.data[0].seriesCode;
        // this.modelCode = data.data[0].modelCode;
        // this.modelName = data.data[0].modelName;
        // 如果有用户信息默认填写上
        if (data.data[0].memberVO) {
          this.submitParam.takeConnectName = data.data[0].memberVO.name
          this.submitParam.takeConnectPhone = data.data[0].memberVO.mobile
        }
        this.changeEdit()

        let vins = ''
        data.data.forEach((item, index) => {
          vins += item.vin + ','
        })
        const bindCarInfoList = await getBindCarInfoList({
          vins: vins.substring(0, vins.length - 1)
        })
        if (bindCarInfoList && bindCarInfoList.data.data.length > 1) {
          this.selectvehicle = true
          this.mineCoupons = bindCarInfoList.data.data
        } else if (bindCarInfoList && bindCarInfoList.data.data.length === 1) {
          this.selectvehicle = false
          this.mineCouponsCheckedObject = bindCarInfoList.data.data[0]
          this.bindCarVIN = this.mineCouponsCheckedObject.vin
          this.vin = this.mineCouponsCheckedObject.vin
          this.$store.state.vin = this.vin
          this.modelCode = this.mineCouponsCheckedObject.modelCode
          this.modelName = this.mineCouponsCheckedObject.modelNameCn
          this.seriesCode = this.mineCouponsCheckedObject.seriesCode
          this.seriesName = this.mineCouponsCheckedObject.seriesName
        }
      } else {
        this.vin = storage.get('vin') || ''
        // 调用APP Dialog
        // callNative('popup', {
        //   type: 'alert',
        //   alertparams: {
        //     title: '',
        //     desc: '您还未绑定车辆，取送车服务需绑定车辆。',
        //     actions: [
        //       {
        //         type: 'fill',
        //         title: '绑定车辆'
        //       },
        //       {
        //         type: 'stroke',
        //         title: '取消'
        //       }
        //     ]
        //   }
        // }).then((data) => {
        //   if (data.type === 'fill') {
        //     // 点击确定{"bizCode":"000003","callFunc":{"functionName":"bindCar"}}
        //     callNative('business', {
        //       callFunc: {
        //         functionName: 'bindCar'
        //       },
        //       bizCode: '000003'
        //     })
        //   } else if (data.type === 'stroke') {
        //     // this.$router.back(-1);
        //   }
        // })
        // this.changeEdit()
      }

      } catch (error) {

      }finally{
        //之前代码可能不健壮，在获取vin后异常，堵塞以下代码
        this.vanloading = false
        this.getAfterSalesByVin()
      }


    },
    // 获取绑定是Vin的服务商
    async getAfterSalesByVin() {
      const { data } = await getAfterSalesByVin({ vin: this.vin })
      if (data && data.data) {
        this.bindDealerCode = data.data.dealerCode
        console.log('this.bindDealerCode', this.bindDealerCode)
      }
    },
    async changeEdit() {
      if (this.submitParam.takeConnectName !== '') {
        this.animations.takeConnectName = true
      }
      if (this.submitParam.takeConnectPhone !== '') {
        this.animations.takeConnectPhone = true
      }

      if (this.submitParam.carNo !== '') {
        this.animations.carNo = true
      }
      if (this.vin !== '') {
        this.animations.vin = true
      }
    },
    // 当选了卡券且公里数不足提示
    async kmPrompt() {
      this.takeDistanceShow = false
      this.sendDistanceShow = false
      this.kmPromptTitle = ''
      // console.log('获取取车公里数', this.takeCarMileage)
      // console.log('获取送车公里数', this.sendCarMileage)
      // console.log('获取取车地址', this.takeAddress)
      // console.log('获取送车地址', this.sendAddress)
      // console.log(this.dealerLatitude)
      // 计算取送车地址和经销商距离
      if (this.dealerLatitude > 0) {
        if (this.isTakeAndSend) {
          // 取送车订单
          if (this.takeCarMileage && this.sendCarMileage) {
            if (this.takeAddress) {
              const takeDistance = await getdiscount2(
                this.dealerLongitude,
                this.dealerLatitude,
                this.takeLng,
                this.takeLat
              )
              if (
                takeDistance > 0 &&
                takeDistance / 1000 > this.takeCarMileage
              ) {
                this.takeDistanceShow = true
                // this.kmPromptTitle = '取车可能产生额外费用，具体服务商将与您取得联系'
              } else {
                this.takeDistanceShow = false
              }
            }
            if (this.sendAddress) {
              const sendDistance = await getdiscount2(
                this.dealerLongitude,
                this.dealerLatitude,
                this.sendLng,
                this.sendLat
              )
              if (
                sendDistance > 0 &&
                sendDistance / 1000 > this.sendCarMileage
              ) {
                this.sendDistanceShow = true
                // this.kmPromptTitle = '送车可能产生额外费用，具体服务商将与您取得联系'
              } else {
                this.sendDistanceShow = false
              }
            }
            if (this.takeDistanceShow && this.sendDistanceShow) {
              this.kmPromptTitle =
                '取送车可能产生额外费用，具体服务商将与您取得联系'
              this.qusongbianl = true
              return
            }
            if (this.takeDistanceShow) {
              console.log(111111111111)
              this.kmPromptTitle =
                '取车可能产生额外费用，具体服务商将与您取得联系'
              this.qusongbianl = true
              return
            }
            if (this.sendDistanceShow) {
              this.kmPromptTitle =
                '送车可能产生额外费用，具体服务商将与您取得联系'
              this.qusongbianl = true
              return
            }
          }
          if (this.takeCarMileage) {
            if (this.takeAddress) {
              const takeDistance = await getdiscount2(
                this.dealerLongitude,
                this.dealerLatitude,
                this.takeLng,
                this.takeLat
              )
              if (
                takeDistance > 0 &&
                takeDistance / 1000 > this.takeCarMileage
              ) {
                this.takeDistanceShow = true
                this.qusongbianl = true
                this.kmPromptTitle =
                  '取车可能产生额外费用，具体服务商将与您取得联系'
              } else {
                this.takeDistanceShow = false
              }
              return
            }
          }
          if (this.sendCarMileage) {
            if (this.sendAddress) {
              const sendDistance = await getdiscount2(
                this.dealerLongitude,
                this.dealerLatitude,
                this.sendLng,
                this.sendLat
              )
              if (
                sendDistance > 0 &&
                sendDistance / 1000 > this.sendCarMileage
              ) {
                this.sendDistanceShow = true
                this.qusongbianl = true
                this.kmPromptTitle =
                  '送车可能产生额外费用，具体服务商将与您取得联系'
              } else {
                this.sendDistanceShow = false
              }
            }
          }
        } else {
          // 单订单
          if (this.orderType === '20131010') {
            if (this.takeCarMileage && this.takeAddress) {
              const takeDistance = await getdiscount2(
                this.dealerLongitude,
                this.dealerLatitude,
                this.takeLng,
                this.takeLat
              )
              if (
                takeDistance > 0 &&
                takeDistance / 1000 > this.takeCarMileage
              ) {
                this.takeDistanceShow = true
                this.qusongbianl = true
                this.kmPromptTitle =
                  '取车可能产生额外费用，具体服务商将与您取得联系'
              } else {
                this.takeDistanceShow = false
              }
            }
          } else {
            if (this.sendCarMileage && this.sendAddress) {
              const sendDistance = await getdiscount2(
                this.dealerLongitude,
                this.dealerLatitude,
                this.sendLng,
                this.sendLat
              )
              if (
                sendDistance > 0 &&
                sendDistance / 1000 > this.sendCarMileage
              ) {
                this.sendDistanceShow = true
                this.qusongbianl = true
                this.kmPromptTitle =
                  '送车可能产生额外费用，具体服务商将与您取得联系'
              } else {
                this.sendDistanceShow = false
              }
            }
          }
        }
      }
    },
    onTabClick(code) {
      this.showSelectService = false
      this.isTakeAndSend = false
      if (code === 0) {
        this.orderType = '20131010'
        this.$store.state.title = '取车服务'
      } else if (code === 1) {
        this.orderType = '20131020'
        this.$store.state.title = '送车服务'
      } else {
        this.orderType = '20131010'
        this.$store.state.title = '取送车服务'
        this.isTakeAndSend = true
      }
      this.getBindCardInfo()
    },
    onSheetClose() {
      console.log('onSheetClose')
      this.getBindCardInfo()
      callNative('prepage', { times: 1 })
    },
    onSheetCancel() {
      console.log('onSheetCancel')
      this.getBindCardInfo()
      callNative('prepage', { times: 1 })
    },
    // 选择车系
    onSelectSeries() {
      // if (this.omdHasVin) {
      //   return
      // }
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/select-series-list',
        query: {}
      })
    },
    // 选择车型
    onSelectModels() {
      if (this.omdHasVin) {
        return
      }
      if (!this.seriesCode) {
        callNative('toast', { type: 'fail', message: '请先选择车系' })
        return
      }
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/select-models-list',
        query: {}
      })
    },
    // 选择车牌
    onInputCarNo() {
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/input-car-no',
        query: {}
      })
    },
    // 选择取车地址
    onTakeAddress() {
      if (this.dealerCode === '') {
        callNative('toast', { type: 'fail', message: '请选择服务商' })
        return
      }
      this.sverSubmitParam()
      //  callNative('audiSetAddress', {type: 'address'}).then((res) => {
      //   debugger
      //   console.log('audiSetAddress', res.params)

      //    this.$store.commit("saveTakeAddress", {
      //       takeAddress: res.params.split("#")[0], // 取车地址
      //       takeLat: res.params.split("#")[1], // 取车纬度
      //       takeLng: res.params.split("#")[2], // 送车经度
      //       takeCityCode: res.params.split("#")[3], // 选择的城市
      //     });
      //    //需送车时 如果取车地址偶的话赋值给送车地址
      //   if (this.isTakeAndSend && this.takeAddress && !this.sendAddress) {
      //     this.$store.commit("saveSendDAddress", {
      //       sendAddress: res.params.split("#")[0], // 送车地址
      //       sendLat: res.params.split("#")[1], // 送车纬度
      //       sendLng: res.params.split("#")[2], // 送车经度
      //       sendCityCode: res.params.split("#")[3], // 选择的城市
      //     });
      //   }
      //   changeEdit()
      // })
      // const { origin, pathname } = window.location;
      //   const url = `${origin}${pathname}#/aftersales/select-user-address?takeAddress=${encodeURI(this.takeAddress || this.initTakeAddress)}&type=takeAddress&latitude=${this.takeLat}&longitude=${this.takeLng}&cityCode=${this.takeCityCode}`;
      //   callNative("audiOpen", { path: url });
      console.log(this.takeLat,"this.takeLat")
      console.log(this.takeLat,"this.takeLng")

      this.$router.push({
        path: '/aftersales/select-user-address',
        query: {
          takeAddress:this.takeAddress !== '' ? this.takeAddress : this.initTakeAddress,
          type: 'takeAddress',
          latitude: this.location ? this.location.split(',')[0] : '',
          longitude: this.location ? this.location.split(',')[1] : '',

          // latitude: this.takeLat,
          // longitude: this.takeLng,

          cityCode: this.initCityName
        }
      })
    },
    // 选择送车地址
    onSendAddress() {
      if (this.dealerCode === '') {
        callNative('toast', { type: 'fail', message: '请选择服务商' })
        return
      }
      this.sverSubmitParam()
      // callNative('audiSetAddress', {type: 'address'}).then((res) => {

      //   console.log('audiSetAddress', res.params)
      //    this.$store.commit("saveSendDAddress", {
      //       sendAddress: res.params.split("#")[0], // 送车地址
      //       sendLat: res.params.split("#")[1], // 送车纬度
      //       sendLng: res.params.split("#")[2], // 送车经度
      //       sendCityCode: res.params.split("#")[3], // 选择的城市
      //     });
      //     changeEdit()
      // })

      //  const { origin, pathname } = window.location;
      //   const url = `${origin}${pathname}#/aftersales/select-user-address?takeAddress=${encodeURI(this.sendAddress || this.initSenndAddress)}&type=sendAddress&latitude=${this.sendLat}&longitude=${this.sendLng}&cityCode=${this.sendCityCode}`;
      //   callNative("audiOpen", { path: url });

      // 拿不到store的值
      // this.sendLat
      // this.sendLng

      this.$router.push({
        path: '/aftersales/select-user-address',
        query: {
          sendAddress:
            this.sendAddress !== '' ? this.sendAddress : this.initSenndAddress,
          type: 'sendAddress',
          latitude: this.location ? this.location.split(',')[0] : '',
          longitude: this.location ? this.location.split(',')[1] : '',
          // latitude: this.sendLat,
          // longitude: this.sendLng,
          cityCode: this.initCityName
        }
      })
    },
    // 选择取车预约时间
    onTakeDatetime() {
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/select-time-arbitrar',
        query: {
          ownerCode: this.dealerCode,
          type: 'TakeDatetime',
          sendDate: this.sendDatetime
        }
      })
    },
    // 选择送车预约时间
    onSendDatetime() {
      this.sverSubmitParam()

      this.$router.push({
        path: '/aftersales/select-time-arbitrar',
        query: {
          ownerCode: this.dealerCode,
          type: 'SendDatetime',
          takeDate: this.takeDatetime
        }
      })
    },
    // 选择服务商
    onSelectDealer() {
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/select-car-service-providers-list',
        query: { bindDealerCode: this.bindDealerCode }
      })
    },
    // 选择服务类型
    onSelectServiceType() {
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/service-type',
        query: { serviceType: this.serviceType, inputRemark: this.remark }
      })
    },

    // 取送车价格详情
    onSendCarPrice() {
      this.sverSubmitParam()
      this.$router.push({
        path: '/aftersales/send-car-price',
        query: {}
      })
    },
    onSelect(action) {
      this.submitParam.takeConnectPhone = action.text
      this.changeEdit()
      setTimeout(() => {
        this.showPopover = false
      }, 300)
    },

    onSelectVin(action) {
      let vinStr = action.text.split('-')[1]
      this.vin = vinStr.substring(1, vinStr.length)
      this.changeEdit()
      setTimeout(() => {
        this.showPopoverVin = false
      }, 300)
    },
    // 临时保存的数据
    sverSubmitParam() {
      this.$store.state.takeConnectName = this.submitParam.takeConnectName
      this.$store.state.takeConnectPhone = this.submitParam.takeConnectPhone
      // this.$store.state.carNo = this.submitParam.carNo;
      this.$store.state.vin = this.vin

      // this.$store.state.sendAddress = this.sendAddress
      // this.$store.state.takeAddress = this.takeAddress
      this.$store.state.orderType = this.orderType
      this.$store.state.showSelectService = this.showSelectService

      this.$store.state.isInitView = '1'

      this.$store.state.takeAddressSupplement = this.takeAddressSupplement
      this.$store.state.sendAddressSupplement = this.sendAddressSupplement
      // this.$store.state.modelName = this.modelName;

      this.$store.state.takeIndexNo = this.takeIndexNo
      this.$store.state.takeEticketType = this.takeEticketType 
      this.$store.state.takeCouponName = this.takeCouponName

      this.$store.state.sendIndexNo = this.sendIndexNo
      this.$store.state.sendEticketType = this.sendEticketType
      this.$store.state.sendCouponName = this.sendCouponName

      this.$store.state.takeAndSend = this.isTakeAndSend
      this.$store.state.bindCarVIN = this.bindCarVIN
      this.$store.state.bindDealerCode = this.bindDealerCode
      sessionStorage.sendOrTakeChecked = this.sendOrTakeChecked ? 1 : null
    },
    // 是否取送车一起
    checkTakeAndSend() {
      // 需送车时 如果取车地址偶的话赋值给送还地址
      if (this.isTakeAndSend && this.takeAddress && !this.sendAddress) {
        this.$store.commit('saveSendDAddress', {
          sendAddress: this.takeAddress, // 送还地址
          sendLat: this.takeLat, // 送车纬度
          sendLng: this.takeLng, // 送车经度
          sendCityCode: this.takeCityCode // 选择的城市
        })
      }
    },
    // 获取优惠券 startDate ：一年之前的日期
    async getCarCoupon() {
      const tempDate = new Date() // 获取今天的日期
      tempDate.setDate(tempDate.getDate() - 365) // 今天的前N天的日期，N自定义
      const startDate = `${tempDate.getFullYear()}-${
        tempDate.getMonth() + 1
      }-${tempDate.getDate()}`
      const { data } = await getCarCoupon({
        startDate: startDate,
        status: '1',
        vinList: this.vin,
        brand:1,
      })
      this.eticketList = []
      if (data.code === '00') {
        if (data.data.length > 0) {
          if (this.bindCarVIN === this.vin) {
            console.log('是否进入')
            // 初始化进来 如果是自己的先默认选中
            // 优先获取有效的券
            this.validCouponList = []
            data.data.forEach((element) => {
              console.log('2222222222')
              if (element.freeze !== 1) {
                this.validCouponList.push(element)
              }
            })
            // 取送车
            if (this.isTakeAndSend) {
              console.log('获取卡券', this.validCouponList)
              // 只有一种里程的卡券且大于等于两张时，默认勾选两张分别对应在取车和送车的订单上
              if (
                this.validCouponList.length > 1 &&
                this.validCouponList.every(
                  (v) => v.mileage === this.validCouponList[0].mileage
                )
              ) {
                // 默认第一张为取车券
                this.takeIndexNo = this.validCouponList[0].couponEquityId
                this.takeEticketType = this.validCouponList[0].businessTypeCode
                this.takeCouponName = this.validCouponList[0].mainTitle
                // 第二张为送车券
                this.sendIndexNo = this.validCouponList[1].couponEquityId
                this.sendEticketType = this.validCouponList[1].businessTypeCode
                this.sendCouponName = this.validCouponList[1].mainTitle
                // 同一里程
                this.takeCarMileage = this.validCouponList[1].mileage
                this.sendCarMileage = this.validCouponList[1].mileage
              } else {
                this.takeCouponName = '有可用卡券'
                this.sendCouponName = '有可用卡券'
              }
            } else if (
              this.validCouponList.length > 0 &&
              this.validCouponList.every(
                (item) => item.mileage === this.validCouponList[0].mileage
              )
            ) {
              // 分开订单
              if (this.orderType === '20131010') {
                // 取车券
                this.takeIndexNo = this.validCouponList[0].couponEquityId
                this.takeEticketType = this.validCouponList[0].businessTypeCode
                this.takeCouponName = this.validCouponList[0].mainTitle
                this.handleCouponName = this.validCouponList[0].mainTitle
                this.takeCarMileage = this.validCouponList[0].mileage
              } else {
                // 送车券
                this.sendIndexNo = this.validCouponList[0].couponEquityId
                this.sendEticketType = this.validCouponList[0].businessTypeCode
                this.sendCouponName = this.validCouponList[0].mainTitle
                this.handleCouponName = this.validCouponList[0].mainTitle
                this.sendCarMileage = this.validCouponList[0].mileage
              }
            } else {
              this.takeCouponName = '有可用卡券'
              this.sendCouponName = '有可用卡券'
              this.handleCouponName = '有可用卡券'
            }
          } else {
            this.takeCouponName = '有可用卡券'
            this.sendCouponName = '有可用卡券'
            this.handleCouponName = '有可用卡券'
          }
          this.eticketList = data.data
          //  地址变化,重新选择卡券
          if (this.isTakeAndSend) {
            if (this.takeAddress !== this.sendAddress) {
              this.takeIndexNo = ''
              this.takeEticketType = ''
              this.takeCouponName = '有可用卡券'
              this.sendIndexNo = ''
              this.sendEticketType = ''
              this.sendCouponName = '有可用卡券'
              this.takeCarMileage = ''
              this.sendCarMileage = ''
            }
          } else {
            const oldtakeAddress = this.$store.state.oldtakeAddress
            if (this.orderType === '20131010') {
              if (this.takeAddress === oldtakeAddress && oldtakeAddress) {
                this.takeIndexNo = ''
                this.takeEticketType = ''
                this.takeCouponName = '有可用卡券'
                this.takeCarMileage = ''
                this.handleCouponName = '有可用卡券'
              }
            } else {
              if (this.sendAddress === oldtakeAddress && oldtakeAddress) {
                this.sendIndexNo = ''
                this.sendEticketType = ''
                this.sendCouponName = '有可用卡券'
                this.sendCarMileage = ''
                this.handleCouponName = '有可用卡券'
              }
            }
          }
          this.kmPrompt()
          for (const element of this.eticketList) {
            if (
              this.sendIndexNo === element.couponEquityId ||
              this.takeIndexNo === element.couponEquityId
            ) {
              this.$set(element, 'showItem', true)
              if (
                this.sendCouponName !== '有可用卡券' &&
                this.takeCouponName !== '有可用卡券'
              ) {
                this.$set(element, 'checked', true)
                if (this.sendIndexNo === element.couponEquityId)
                  this.$set(element, 'send', '1')
                if (this.takeIndexNo === element.couponEquityId)
                  this.$set(element, 'take', '1')
                this.couponList.push(element)
                console.log('===============11', this.couponList)
              } else if (this.handleCouponName !== '有可用卡券') {
                if (this.orderType === '20131010') {
                  if (this.takeIndexNo === element.couponEquityId) {
                    console.info('🚀 ~ file:create-deliver-car method:getCarCoupon line:2232 -----', '执行')
                    this.couponList.push(element)
                    this.$set(element, 'checked', true)
                  }
                } else {
                  if (this.sendIndexNo === element.couponEquityId) {
                    console.info('🚀 ~ file:create-deliver-car method:getCarCoupon line:2238 -----', '执行')
                    this.$set(element, 'checked', true)
                    this.couponList.push(element)
                  }
                }
              }
            } else {
              // 是自己的都显示
              if (this.bindCarVIN === this.vin) {
                this.$set(element, 'checked', false)
                this.$set(element, 'showItem', true)
              } else {
                this.$set(element, 'checked', false)
                this.$set(element, 'showItem', false)
              }
            }
          }
        } else {
          this.takeCouponName = '暂无卡券可使用'
          this.sendCouponName = '暂无卡券可使用'
          this.handleCouponName = '暂无卡券可使用'
        }
      }
    },

    onCouponCancel() {
      this.couponList = JSON.parse(JSON.stringify(this.preCouponList))
    },
    onCouponClose() {
      this.couponList = JSON.parse(JSON.stringify(this.preCouponList))
    },
    // 打开卡券
    async onSelectEticket(vouchers) {
      // await this.getCarCoupon();
      console.log('============vouchers===========', vouchers)
      if (this.eticketList.length > 0) {
        this.show = true
        this.preCouponList = JSON.parse(JSON.stringify(this.couponList))
        if (vouchers) {
          this.vouchersstate = vouchers
        } else {
          // 单个取送车
          let check_obj = this.couponList.length ?  this.couponList[0] : {couponEquityId : ''}
          this.eticketList.forEach((e) => {
            e.nochoose = false
            if (check_obj.couponEquityId == e.couponEquityId) {
              e.checked = true // 已选中、
            } else {
              e.checked = false // 已选中、
            }
          })
        }
        console.log('展示卡券时的当前选中卡券数据：couponList', this.couponList)
        let obj_send = this.couponList.find((e) => e.send == 1) || {}
        let obj_take = this.couponList.find((e) => e.take == 1) || {}

        if (this.vouchersstate === 'takeCar') {
          this.eticketList.forEach((e) => {
            e.nochoose = false
            if (obj_send.couponEquityId == e.couponEquityId) {
              e.nochoose = true // 不可选
              // e.checked = false
            }
            if (obj_take.couponEquityId == e.couponEquityId) {
              e.checked = true // 已选中、
            } else {
              if (obj_send.couponEquityId != e.couponEquityId) {
                e.checked = false
              }
            }
          })
        }
        if (this.vouchersstate === 'sendCar') {
          this.eticketList.forEach((e) => {
            e.nochoose = false
            if (obj_take.couponEquityId == e.couponEquityId) {
              e.nochoose = true
              // e.checked = false
            }
            if (obj_send.couponEquityId == e.couponEquityId) {
              e.checked = true // 已选中、
            } else {
              if (obj_take.couponEquityId != e.couponEquityId) {
                e.checked = false
              }
            }
          })
        }
        console.info('🚀 ~ file:2276 -----', this.eticketList)

        this.$forceUpdate()
        const takeDistance = await getdiscount2(
          this.dealerLongitude,
          this.dealerLatitude,
          this.takeLng,
          this.takeLat
        )
        const sendDistance = await getdiscount2(
          this.dealerLongitude,
          this.dealerLatitude,
          this.sendLng,
          this.sendLat
        )
        // 计算实际公里数
        if (this.isTakeAndSend) {
          if (this.vouchersstate === 'takeCar' && this.takeAddress) {
            this.actualMileage = `取车距离${Math.round(takeDistance / 1000)}km`
          }
          if (this.vouchersstate === 'sendCar' && this.sendAddress) {
            this.actualMileage = `送车距离${Math.round(sendDistance / 1000)}km`
          }
        } else {
          if (this.orderType === '20131010') {
            if (this.takeAddress) {
              this.actualMileage = `取车距离${Math.round(
                takeDistance / 1000
              )}km`
            }
          } else {
            if (this.sendAddress) {
              this.actualMileage = `送车距离${Math.round(
                sendDistance / 1000
              )}km`
            }
          }
        }
      }
    },
    // 卡券的输入框监听
    changeEquityNo(valNo) {
      console.log(this.eticketList, '查看券码列表')
      // 优先去掉空格
      valNo = valNo.replace(/\s+/g, '')
      // 先取第一张
      const oneNo = valNo.substring(0, 14)
      console.log('oneNo===', oneNo)

      // 输入的卡券号码小于14位不是正确的卡券
      if (oneNo.length < 14) {
        console.log('oneNo===111', oneNo)
        this.clearCouponList()
      } else {
        if (oneNo.length === 14) {
          console.log('oneNo===222', oneNo)
          const item = this.eticketList.find((i) => i.mpEquityNo === oneNo)
          if (!item) {
            this.clearCouponList()
            callNative('toast', { type: 'fail', message: '您输入的券码不正确' })
            return
          }
          // 判断卡券是否可以使用
          if (item.freeze === 1) {
            callNative('toast', { type: 'fail', message: '该卡券已冻结' })
            return
          }
          // 可用需要显示出来券
          item.checked = true
          item.showItem = true
          this.couponList = []
          this.couponList.push(item)
        }

        // if (!this.isTakeAndSend) {
        //   console.log('oneNo===333', oneNo)
        //   this.clearCouponList()
        //   return
        // }

        const twoNo = valNo.substring(14)
        console.log('twoNo', twoNo)
        if (twoNo.length === 14) {
          console.log('oneNo===444', oneNo)
          if (oneNo === twoNo) {
            callNative('toast', {
              type: 'fail',
              message: '请勿输入的相同的券码'
            })
            return
          }
          const item = this.eticketList.find((i) => i.mpEquityNo === twoNo)
          if (!item) {
            callNative('toast', { type: 'fail', message: '您输入的券码不正确' })
          } else {
            // 判断卡券是否可以使用
            if (item.freeze === 1) {
              callNative('toast', { type: 'fail', message: '该卡券已冻结' })
              return
            }
            // 可用需要显示出来券
            item.checked = true
            item.showItem = true
            this.couponList.push(item)
            console.log('++++++++++++++++++++++++++++++++++++++++++')
          }
        } else {
          console.log('oneNo===555', oneNo)
          if (this.couponList.length > 0) {
            this.eticketList.forEach((element) => {
              if (element.mpEquityNo === this.couponList[0].mpEquityNo) {
                this.$set(element, 'checked', true)
                this.$set(element, 'showItem', true)
                if (this.isTakeAndSend) {
                  if (this.vouchersstate === 'takeCar') {
                    if (!element.send) {
                      this.$set(element, 'take', '1')
                    }
                  }
                  if (this.vouchersstate === 'sendCar') {
                    if (!element.take) {
                      this.$set(element, 'send', '1')
                    }
                  }
                }
              } else {
                this.$set(element, 'checked', false)
                this.$set(element, 'showItem', false)
              }
            })
          }
        }
      }
    },
    // 清空选择的优惠券
    clearCouponList() {
      this.couponList = []
      // 输入的券码不正确 全部不显示
      this.eticketList.forEach((element) => {
        element.checked = false
        element.showItem = false
      })
    },
    // 选择优惠券
    onCheckbox(item) {
      console.log('选择优惠券', item)
      console.log("选择优惠券列表", this.couponList);
      if (this.isTakeAndSend) {
        if (item.nochoose == true) {
          return
        }
      }
      const types = this.vouchersstate
      if (item.freeze !== null && item.freeze === 1) {
        callNative('toast', { type: 'fail', message: '该卡券已冻结' })
        item.checked = false
        return
      }
      // if (this.isTakeAndSend && this.couponList.length == 2 && item.checked) {
      //   // 取送车时最多选两张券
      //   // callNative('toast', { type: 'fail', message: '您当前最多可选两张券' })
      //   item.checked = false
      //   console.log('取送车时最多选两张券')
      //   return
      // }
      // 单个订单最多选一张券
      if (this.couponList.length == 1 && item.checked && !this.isTakeAndSend) {
        // callNative('toast', {
        //   type: 'fail',
        //   message: '您当前最多可选一张券'
        // })
        // item.checked = false
        const arr = []
        this.eticketList.forEach((e) => {
          if (e.couponEquityId !== item.couponEquityId) {
            e.checked = false
          } else {
            arr.push(e)
          }
        })
        this.couponList = arr
        console.log('单个订单最多选一张券, 新改切换券', arr)
        return
      }
      if (this.isTakeAndSend) {
        // 不同公里
        // 查找取车or送车券，是否有选中的
        let sendLen = -1
        let takeLen = -1
        let obj_send = null
        let obj_take = null
        if (this.couponList.length) {
          this.couponList.map((req, i) => {
            if (req.send == 1) {
              sendLen = i
              obj_send = req
            }
            if (req.take == 1) {
              takeLen = i
              obj_take = req
            }
          })
        }
        const sendLength = sendLen + 1
        const takeLength = takeLen + 1
        // 查找取车券，是否有选中的
        if (types === 'takeCar') {
          this.cancelSelectFn(takeLength, 'take', item, obj_send)
        }
        if (types === 'sendCar') {
          this.cancelSelectFn(sendLength, 'send', item, obj_take)
        }
      }
      if (!this.isTakeAndSend) {
        const arr = []
        this.eticketList.forEach((e) => {
          if (e.checked) {
            arr.push(e)
          }
        })
        this.couponList = arr
      }
    },
    cancelSelectFn(length, types, item, otherLen) {
      let _this = this
      if (length > 0 && item.checked && this.couponList.length > 0) {
        if (otherLen) {
          // 如果另一个已选中
          // 找到对应类型的选中的 并清空
          let selectLId = (this.couponList.find(e => e[types] == 1)).couponEquityId
          let arr = []
          let j = 0
          this.eticketList.map((v, d) => {
            let innerV = JSON.parse(JSON.stringify(v))
            if (v.couponEquityId == selectLId) {
              _this.couponList = this.couponList.filter(
                (e) => v.couponEquityId != e.couponEquityId
              )
              innerV[types] = ''
              innerV.checked = false
            }
            if (item.checked && v.couponEquityId === item.couponEquityId) {
              j = d
              innerV.checked = true
              innerV[types] = '1'
            }
            arr.push(innerV)
          })
          _this.couponList.push(arr[j])
          this.eticketList = arr
          this.$forceUpdate()
        } else {
          this.couponList = []
          this.eticketList.forEach((v) => {
            if (item.checked && v.couponEquityId === item.couponEquityId) {
              let aItem = {
                ...item
              }
              aItem[types] = 1
              _this.couponList.push(aItem)
              this.$set(v, types, '1')
            } else {
              this.$set(v, types, '')
              this.$set(v, 'checked', '')
            }
          })
        }
        // 存在已选中的券
        // callNative('toast', { type: 'fail', message: '每次只可选中一张卡券' })
        // item.checked = false
        return
      }
      if (length > 0 && !item.checked && this.couponList.length > 0) {
        // 再次点击自己的,已选中状态，从数组中去除，点击的那条数据
        this.couponList = this.couponList.filter(
          (e) => item.couponEquityId != e.couponEquityId
        )
        if (this.eticketList == false) return
        this.eticketList.forEach((e) => {
          if (item.couponEquityId == e.couponEquityId) {
            e.checked = false
            if (e.take) {
              e.take = ''
            }
            if (e.send) {
              e.send = ''
            }
          }
        })
      }
      if (length <= 0) {
        // 未找到已选中的取车券
        this.eticketList.forEach((v) => {
          if (item.checked && v.couponEquityId === item.couponEquityId) {
            console.log(types, 'set的到底是什么')
            let aItem = {
              ...item
            }
            aItem[types] = 1
            this.couponList.push(aItem)
            this.$set(v, types, '1')
          }
        })
      }
      console.log('查看卡券列表数据有几条', this.couponList)
    },
    // 选择好的优惠券
    onEticketSelect() {
      console.log(
        'this.couponList>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>',
        this.couponList
      )
      this.show = false
      // 优先获取有效的券
      this.validCouponList = []
      this.eticketList.forEach((element) => {
        if (element.freeze !== 1) {
          this.validCouponList.push(element)
        }
      })

      // 判断是否有人车关系= 用户绑定的vin码和当前输入的vin码是否一至

      // 判断当前vin码只有一张券并且选中状态，，需要弹出选择券使用的方式
      if (this.validCouponList.length === 1) {
        console.log('只有一张卡券', this.couponList)
        this.takeIndexNo = ''
        this.takeEticketType = ''
        this.takeCouponName = ''
        this.sendIndexNo = ''
        this.sendEticketType = ''
        this.sendCouponName = ''
        this.takeCarMileage = ''
        this.sendCarMileage = ''
        if (this.couponList.length == 0) {
          this.sendCouponName = '有可用卡券'
          this.takeCouponName = '有可用卡券'
          this.handleCouponName = '有可用卡券'
        } else {
          // 是取送车订单
          if (this.isTakeAndSend) {
            const obj_send = this.couponList.find((e) => e.send == 1) || {}
            const obj_take = this.couponList.find((e) => e.take == 1) || {}
            if (obj_take.take) {
              // 取车
              this.takeIndexNo = obj_take.couponEquityId
              this.takeEticketType = obj_take.businessTypeCode
              this.takeCouponName = obj_take.mainTitle
              this.takeCarMileage = obj_take.mileage
              this.sendCouponName = '无卡券可使用'
            }
            if (obj_send.send) {
              this.sendIndexNo = obj_send.couponEquityId
              this.sendEticketType = obj_send.businessTypeCode
              this.sendCouponName = obj_send.mainTitle
              this.sendCarMileage = obj_send.mileage
              this.takeCouponName = '无卡券可使用'
            }
          } else {
            // 分开订单
            if (this.orderType === '20131010') {
              // 取车券
              this.takeIndexNo = this.couponList[0].couponEquityId
              this.takeEticketType = this.couponList[0].businessTypeCode
              this.takeCouponName = this.couponList[0].mainTitle
              this.takeCarMileage = this.couponList[0].mileage
              this.handleCouponName = this.couponList[0].mainTitle

            } else {
              // 送车券
              this.sendIndexNo = this.couponList[0].couponEquityId
              this.sendEticketType = this.couponList[0].businessTypeCode
              this.sendCouponName = this.couponList[0].mainTitle
              this.sendCarMileage = this.couponList[0].mileage
              this.handleCouponName = this.couponList[0].mainTitle
            }
          }
        }
      } else {
        console.log('多张卡券')
        // 当前有多张券可使用的时候
        // 判断当前选中的券
        if (this.couponList.length > 0) {
          console.log('是否有选中卡券')
          // 取送车
          if (this.isTakeAndSend) {
            const obj_send = this.couponList.find((e) => e.send == 1) || {}
            const obj_take = this.couponList.find((e) => e.take == 1) || {}
            if (this.vouchersstate === 'takeCar') {
              // 取车
              this.takeIndexNo = obj_take.couponEquityId || ''
              this.takeEticketType = obj_take.businessTypeCode || ''
              this.takeCouponName = obj_take.mainTitle || '有可用卡券'
              this.takeCarMileage = obj_take.mileage || ''
            } else {
              this.sendIndexNo = obj_send.couponEquityId || ''
              this.sendEticketType = obj_send.businessTypeCode || ''
              this.sendCouponName = obj_send.mainTitle || '有可用卡券'
              this.sendCarMileage = obj_send.mileage || ''
            }
            // }
          } else {
            // 单个订单只取第一张选择的券
            // takeIndexNo: "", // 取车券码,eg:K6SHo94VB0HESM
            if (this.orderType === '20131010') {
              // 取车券
              this.takeIndexNo = this.couponList[0].couponEquityId
              this.takeEticketType = this.couponList[0].businessTypeCode
              this.takeCouponName = this.couponList[0].mainTitle
              this.takeCarMileage = this.couponList[0].mileage
              this.handleCouponName = this.couponList[0].mainTitle
            } else {
              // 送车券
              this.sendIndexNo = this.couponList[0].couponEquityId
              this.sendEticketType = this.couponList[0].businessTypeCode
              this.sendCouponName = this.couponList[0].mainTitle
              this.sendCarMileage = this.couponList[0].mileage
              this.handleCouponName = this.couponList[0].mainTitle
            }
          }
        } else {
          this.sendCouponName = '有可用卡券'
          this.takeCouponName = '有可用卡券'
          this.handleCouponName = '有可用卡券'
          this.takeIndexNo = ''
          this.takeEticketType = ''
          this.sendIndexNo = ''
          this.sendEticketType = ''
          this.takeCarMileage = ''
          this.sendCarMileage = ''
        }
      }
      console.log('点击确定时的当前选中卡券数据：couponList', this.couponList)
      this.kmPrompt()
    },

    onclickTake() {
      this.isTake = !this.isTake
      if (this.isTake) {
        this.isSend = false
      }
    },
    onclicklSend() {
      this.isSend = !this.isSend
      if (this.isSend) {
        this.isTake = false
      }
    },
    onCancel() {
      this.isTake = false
      this.isSend = false
      this.modalshow = false
    },
    async onConfirm() {
      if (this.isTake) {
        // 取车券
        this.takeIndexNo = this.couponModel.couponEquityId
        this.takeEticketType = this.couponModel.businessTypeCode
        this.takeCouponName = this.couponModel.mainTitle
        // 送车券
        this.sendIndexNo = ''
        this.sendEticketType = ''
        this.sendCouponName = ''
      } else if (this.isSend) {
        // 取车券
        this.takeIndexNo = ''
        this.takeEticketType = ''
        this.takeCouponName = ''
        // 送车券
        this.sendIndexNo = this.couponModel.couponEquityId
        this.sendEticketType = this.couponModel.businessTypeCode
        this.sendCouponName = this.couponModel.mainTitle
      }
      this.modalshow = false
    },
    toSubmit(orderType1) {


      //勾选协议
      if(!this.isSonsent){
        callNative('toast', { type: 'fail', message: '请勾选确认《上汽奥迪取送车服务协议》' })
        return
      }

      // 保存联系方式
      const mobile = storage.get('serviceUserMobile') || ''
      if (!mobile) {
        storage.set('serviceUserMobile', this.submitParam.takeConnectPhone)
      } else {
        const tel = this.submitParam.takeConnectPhone
        if (mobile.indexOf(tel) === -1) {
          storage.set(
            'serviceUserMobile',
            `${this.submitParam.takeConnectPhone},${mobile}`
          )
        }
      }

      let sendDatetime = ''
      if (this.sendDatetime !== '') {
        sendDatetime = `${this.sendDatetime}:00`
      }
      let takeDatetime = ''
      if (this.takeDatetime !== '') {
        takeDatetime = `${this.takeDatetime}:00`
      }
      let seviceType1 = ''
      // serviceType:1,//服务类型(20141010:保养;20141020:维修;20141030:其他;)
      if (this.serviceType === '1') {
        seviceType1 = '20141010'
      } else if (this.serviceType === '2') {
        seviceType1 = '20141020'
      } else {
        seviceType1 = '20141030'
      }
      const param = {
        carNo: this.submitParam.carNo, //	车牌号，沪AD12345		false
        dealerCode: this.dealerCode, //	服务商代码		false
        modelCode: this.modelCode || ' ', //	车型代码		false
        modelName: this.modelName || ' ', //	车型中文名称		false
        orderType: parseInt(orderType1), //	订单类型(20131010:取车;20131020:送车;20131030:取送车;)		false
        remark: this.remark, //	备注		false
        sendAddress: this.sendOrTakeChecked ? this.takeAddress : this.sendAddress, //	送还地址		false
        sendConnectName: this.submitParam.takeConnectName, //	送车联系人		false
        sendConnectPhone: this.submitParam.takeConnectPhone, //	送车联系人电话		false
        sendDatetime: sendDatetime, //	送车车时间：(yyyy-MM-dd HH:mm:ss)		false
        sendIndexNo: `${this.sendIndexNo}`, //	送车券码		false
    
        seriesCode: this.seriesCode || ' ', //	车系代码		false
        seriesName: this.seriesName || ' ', //	车系中文名称		false
        serviceType: parseInt(seviceType1), //	服务类型(20141010:保养;20141020:维修;20141030:其他;)		false

        takeAddress: this.takeAddress, //	取车地址		false
        takeConnectName: this.submitParam.takeConnectName, //	取车联系人		false
        takeConnectPhone: this.submitParam.takeConnectPhone, //	取车联系人电话		false
        takeDatetime: takeDatetime, //	取车时间(yyyy-MM-dd HH:mm:ss)		false
        takeIndexNo: `${this.takeIndexNo}`, //	取车券码,eg:K6SHo94VB0HESM		false
        vin: this.vin, // this.submitParam.vin //	车架号 "LSVCC6F25M2010191"
        takeLongitude: parseFloat(this.takeLng), // 取车经度
        takeLatitude: parseFloat(this.takeLat), // 取车纬度
        sendLatitude: parseFloat(this.sendLat),
        sendLongitude: parseFloat(this.sendLng),
        takeAddressSupplement: this.takeAddressSupplement,
        sendAddressSupplement: this.sendOrTakeChecked ? this.takeAddressSupplement : this.sendAddressSupplement,
        mpEquityNo: this.mpEquityNo,
        takeEticketType:this.takeEticketType,
        sendEticketType:this.sendEticketType,
      }
      console.log(param, '查看提交信息')

      // 判断当前时间是否在9-15点时间以内下单
      const hoursDate = new Date().getHours()

      if (hoursDate > 8 && hoursDate < 18) {
        this.confirmOrder(param)
      } else {
        // 调用APP Dialog
        callNative('popup', {
          type: 'alert',
          alertparams: {
            title: '',
            desc: '当前服务商处于非营业时间，暂无法及时获悉您的服务需求，服务商将在上班后与您尽快取得联系，请您保持手机畅通，感谢您的理解。',
            actions: [
              {
                type: 'fill',
                title: '确定'
              }
            ]
          }
        }).then((data) => {
          if (data.type === 'fill') {
            // 点击确定
            this.confirmOrder(param)
          }
        })
      }
    },
    // 提交
    async onAffirm() {
      // 新增送车快捷切换按钮
      // if (this.sendOrTakeChecked) {
      //   this.sendAddress = this.takeAddress
      //   this.sendAddressSupplement = this.takeAddressSupplement
      // }
      


      let orderType1 = this.orderType
      if (this.isTakeAndSend) {
        orderType1 = '20131030'
      }
      if (this.submitParam.carNo === '') {
        callNative('toast', { type: 'fail', message: '请输入车牌号' })
        return
      }
      if (this.vin === '' || this.vin.length < 17) {
        callNative('toast', { type: 'fail', message: '请输入正确的VIN码' })
        return
      }
      storage.set('carNo', this.submitParam.carNo)
      storage.set('vin', this.vin)

      const userVIN = storage.get('serviceUserVIN') || ''
      if (!userVIN) {
        storage.set('serviceUserVIN', this.vin)
      } else {
        const tel = this.vin
        if (userVIN.indexOf(tel) === -1) {
          storage.set('serviceUserVIN', `${this.vin},${userVIN}`)
        }
      }
      if (this.submitParam.takeConnectName === '') {
        callNative('toast', { type: 'fail', message: '请填写姓名' })
        return
      }
      if (this.submitParam.takeConnectPhone === '') {
        callNative('toast', { type: 'fail', message: '请填写联系方式' })
        return
      }

      if (orderType1 === '20131030' || this.orderType === '20131010') {
        // 取车判断

        if (this.takeDatetime === '') {
          callNative('toast', { type: 'fail', message: '请选择取车预约时间' })
          return
        }
        if (!this.takeAddress) {
          callNative('toast', { type: 'fail', message: '请选择取车地址' })
          return
        }

        storage.set('takeConnectName', this.submitParam.takeConnectName)
        storage.set('takeConnectPhone', this.submitParam.takeConnectPhone)
      }
      if (orderType1 === '20131030' || this.orderType === '20131020') {
        // 送车判断

        if (this.sendDatetime === '') {
          callNative('toast', { type: 'fail', message: '请选择送车预约时间' })
          return
        }
        if (!this.sendAddress) {
          callNative('toast', { type: 'fail', message: '请选择送还地址' })
          return
        }
      }

      if (this.dealerCode === '') {
        callNative('toast', { type: 'fail', message: '请选择服务商' })
        return
      }
      console.log(this.takeCouponName, '取车卡券判断')
      console.log(this.sendCouponName, '送车卡券判断')
      // 未选择卡券
      // 取送车订单未选择卡券
      if (this.isTakeAndSend) {
        if (
          this.takeCouponName === '有可用卡券' ||
          this.sendCouponName === '有可用卡券'
        ) {
          this.updateVis = true
          return
        }
      }
      // 取车卡券未选择
      if (this.orderType === '20131010') {
        if (this.takeCouponName === '有可用卡券') {
          this.updateVis = true
          return
        }
      } else {
        if (this.sendCouponName === '有可用卡券') {
          this.updateVis = true
          return
        }
      }

      this.toSubmit(orderType1)
    },
    async confirmOrder(param) {
      this.$store.commit('showLoading')
      const { data } = await postCreateDeliverCar(param)
      this.$store.commit('hideLoading')
      if (data.code === '200') {
        // 创建成功
        this.onRouter()
      } else {
        const { message } = data
        // 当前接口无法区分错误CODE，故使用字符串匹配方法
        if (message.includes('已存在未结束的取送车需求订单请前往')) {
          const {
            CreateDeliverCarPop: { conf, desc }
          } = this
          desc[0] = message
          this.CreateDeliverCarPop.conf = { ...conf, ...{ show: true } }
          return
        }
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    onRouter() {
      // 清空保存的数据
      //   dealerModel: {}, // 选择的服务商
      // selectModel: {}, // 服务预约选择的车型
      // selectServiceType: {},// 服务类型
      // selectServiceTime: {},// 服务预约选择的时间
      // this.$store.commit("dealerModel", {});
      // this.$store.commit("selectServiceType", {});
      this.$store.commit('saveSelectServiceTime', {})
      // this.$store.commit("saveSelectServiceShop", []);

      this.$router.push({
        path: '/aftersales/service-success',
        query: { type: 2 }
      })
    },
    animation(ref) {
      if (ref === 'takeConnectPhone') {
        this.showPopover = true
      } else {
        this.showPopover = false
      }
      if (ref === 'vin') {
        this.getBindCarInfoList()
        this.showPopoverVin = true
      } else {
        this.showPopoverVin = false
      }

      this.animations[ref] = true
      this.$refs[ref].focus()
      if (this.blur) {
        this.$refs[ref].blur()
      }
    },
    handlerFocus(prop) {
      setTimeout(() => {
        const pannel = document.getElementById(prop)

        // 让当前的元素滚动到浏览器窗口的可视区域内
        pannel.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
        // 此方法是标准的scrollIntoView()方法的专有变体
        // pannel.scrollIntoViewIfNeeded();
      }, 300)

      if (!this.animations[prop]) {
        this.animations[prop] = true
      }
    },
    handlerBlur(prop) {
      if (prop === 'vin') {
        this.animations[prop] = !!this.vin
      } else {
        this.animations[prop] = !!this.submitParam[prop]
      }
    },
    failed(err) {
      console.error('failed', err)
    },
    // 重要提醒
    onImportantNote() {
      sessionStorage.setItem("routePreLink", '/userAgreement/create-deliver-car')
      sessionStorage.setItem('isSonsent', this.isSonsent ? '1' : '0')
      this.$router.push('/userAgreement/create-deliver-car')
    },
    handleGotoBindCar() {
      // callNative('business', {
      //   callFunc: {
      //     functionName: 'bindCar'
      //   },
      //   bizCode: '000003'
      // })
      const { origin, pathname } = window.location
      const url = `${origin}${pathname}#/certification/my-certification?scene=latentGuestPageBindCarFromSA`
      callNative('audiOpen', { path: url })
      this.changeEdit()
    },
    handleCouponLineName(orderType) {
      if (this.eticketList.length === 0) {
        return '暂无卡券可使用'
      } else {
        if (orderType === '20131010') {
          return this.takeCouponName
        }
        if (orderType === '20131020') {
          return this.sendCouponName
        }
        return '暂无卡券可使用'
      }
    },
    changeSendOrTakeChecked (e) {
      console.info('🚀 ~ file:create-deliver-car method:changeSendOrTakeChecked line:3112 -----', e)
      sessionStorage.sendOrTakeChecked = e ? 1 : null
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/buttons.less');
@import url('../../assets/style/forms-cell.less');
// @import url("../../assets/style/animation.less");
// @import url("../../assets/style/cell.less");

.serviceAppointmentOne {
  padding-bottom: 120px !important;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;
  .item-img {
    width: 100%;
    margin-top: 16px;
  }

  // /deep/.van-field__error-message {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   height: 16px;

  //   &::before {
  //     content: "";
  //     display: inline-block;
  //     width: 14px;
  //     height: 14px;
  //     background: url("../../assets/img/error.png") no-repeat 0 0;
  //     background-size: 14px 14px;
  //     margin-right: 4px;
  //   }
  // }
}

.input-line {
  display: flex;
  flex-direction: column;
  position: relative;
  // padding-bottom: 16px;
  border-bottom: 1px solid #000;
  .input-title {
    font-size: 12px;
    color: #666;
  }
  .input-change {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .input-name {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      border: none;
      height: 24px;
      width: 100%;
      font-size: 16px;
      color: #000;
    }
    .input-icon {
      padding-left: 10px;
      align-items: center;
      color: #a3a3a3;
    }
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  padding-bottom: 16px;
  left: 0px;
  .checkbox_button {
    position: absolute;left: 0;top: 0;bottom: 0;right: 0;margin: auto;
    width: 18px;
    height: 18px;
    color: #000;
    opacity: 0;
  }
  .checkbox_button_default{
    position: relative;
    margin-left: 16px;
    margin-top: 2px;
    background-color: #fff;
    width: 18px;
    height: 18px;
    color: #000;
    box-sizing: border-box;
    border-radius: 50%;
    border: 1px solid #B3B3B3;
  }
  .checkbox_button_active{
    border: none;
    .checkbox_button{
      opacity: 1;
    }
  }
  .checkbox_style {
    display: flex;
    height: 20px;
    justify-content: space-between;
    font-family: 'Audi-Normal';
    color: #999999;
    width: 100%;
    font-size: 16px;
    padding-top: 16px;
    span {
      font-size: 16px;
      color: #000;
      font-family: 'Audi-Normal';
    }
    .agreement-text{
      font-family: 'Audi-WideBold';
      font-size: 12px;
      color: #000000;
    }
    .agreement-text-pre{
      font-family: "Audi-Normal";
      font-size: 12px;
      color: #4C4C4C;
    }
    .checkbox_styleRight {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #000;
      margin-right: 16px;
    }
  }
}
.btn-delete-wrapper {
  margin: 16px;
}
.page-submit-btn{
  margin: 20px 16px 0;
}
.btn-delete-height {
  height: 40px;
}

.eticket {
  height: 82px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px;
  padding-right: 16px;
  padding-left: 16px;
  // box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e5e5;
  .eticket-title {
    font-size: 16px;
    color: #000;
    font-family: 'Audi-Normal';
  }
  .eticket-date {
    color: #999;
    font-size: 12px;
    margin-top: 8px;
  }
  .eticket-btn-change {
    display: flex;
    align-items: center;
    height: 100%;
    .eticket-change-name {
      font-size: 16px;
      color: #000;
      margin-right: 10px;
      font-family: 'Audi-Normal';
    }
    .nochoose-change-name {
      color: #c2c2c2;
    }
    .eticket-btn-icon {
      align-items: center;
      width: 28px;
      height: 28px;
    }
  }
}
.nochoose {
  background: #fff;
  border: 1px solid rgba(217, 210, 210, 0.3);
  .nochoose-title {
    color: #b2b2b2;
  }
  .nochoose-date {
    color: #e1e1e1;
  }
}
._modal {
  width: 343px;
  background: #ffffff;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;

  .modal-title {
    font-size: 18px;
    font-weight: 500;
    color: #1a1a1a;
    margin-bottom: 16px;
    font-family: 'Audi-WideBold';
  }

  .modal-content {
    font-size: 16px;
    color: #333333;
    line-height: 18px;
    padding: 0 16px;
    font-family: 'Audi-Normal';
  }
  .modal-line {
    width: 295px;
    height: 48px;
    background: #f2f2f2;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-bold {
      padding-left: 16px;
      font-size: 16px;
      color: #000;
      font-weight: normal;
      font-family: 'Audi-Normal';
    }
    .btn-change {
      padding-right: 16px;
      .btn-icon {
        width: 20px;
        height: 20px;
      }
    }
  }
  .modal-confirm {
    margin-top: 24px;
    width: 85%;
    height: 56px;
    background: #1a1a1a;
    font-size: 16px;
    color: #ffffff;
  }

  .modal-cancel {
    width: 85%;
    border: 1px solid #1a1a1a;
    height: 56px;
    background: #fff;
    font-size: 16px;
    color: #000;
    margin-top: 8px;
  }

  .modal-bold-content {
    font-size: 18px;
    color: #1a1a1a;
    line-height: 32px;
    padding: 0 25px;
    font-weight: normal;
    font-family: 'Audi-WideBold';
  }
}
.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/deep/.van-field__control {
  font-size: 15px !important;
  line-height: 17px !important;
  height: 20px !important;
}
::v-deep .van-field {
  .van-field__body {
    textarea {
      &::placeholder {
        color: #999;
        font-size: 16px !important;
      }
    }

    input {
      &::placeholder {
        color: #999;
        font-size: 16px !important;
      }
    }
  }
}
.van-dialog {
  width: 81%;
  border-radius: 0;
  padding: 30px 20px;
  /deep/ .van-dialog__header {
    padding-top: 0;
    margin-bottom: 30px;
  }
  .button {
    display: flex;
    justify-content: space-between;
    .ContinueSubmit {
      width: 48%;
      height: 56px;
      border: 1px solid #000000;
      line-height: 56px;
      text-align: center;
      color: #000;
    }
    .goUse {
      width: 48%;
      height: 56px;
      background: #000;
      line-height: 56px;
      text-align: center;
      color: #fff;
    }
  }
}
.van-popup--bottom.van-popup--round {
  border-radius: 0;
}

.pop-coupons-box {
  .pop-coupons-hd {
    height: 52px;
    padding: 0 16px;

    .icon-close {
      position: relative;
      top: 2px;
    }

    .h3 {
      font-family: Audi-WideBold;
      font-weight: 400;
      font-size: 16px;
    }

    .btn {
      width: 32px;
    }

    &::after {
      border-color: #d9d9d9;
      //border-width: 0.5px;
    }
  }

  .pop-coupons-main {
    max-height: calc(80vh - 52px);
    padding-bottom: 20px;
    box-sizing: border-box;
    overflow-y: auto;

    .coupons {
      .van-cell {
        padding: 16px;
      }
      .van-radio-group {
        padding: 0;
      }

      .van-cell-group--inset {
        margin: 0;
        padding: 6px 0 40px;
        transition: all 0.3s;

        .van-cell.list {
          position: relative;
          width: auto;
          margin: 16px;
          padding: 16px;
          border: 1px solid #e5e5e5;
          min-height: 64px;
          overflow: visible;

          &.radio-checked {
            box-shadow: 1px 1px 6px rgba(0, 0, 0, 0.02),
              -1px -1px 6px rgba(0, 0, 0, 0.02);
          }

          &.radio-checked,
          &.radio-checked::before,
          &.radio-checked::after {
            border-color: #ccc;
          }

          &.radio-checked::before {
            box-shadow: inset -1px -1px 6px rgba(0, 0, 0, 0.02);
          }

          &.radio-checked::after {
            box-shadow: inset 1px 1px 6px rgba(0, 0, 0, 0.02);
          }

          &::before,
          &::after {
            z-index: 9;
            position: absolute;
            content: '';
            right: 84px;
            width: 6px;
            height: 3px;
            border: 1px solid #e5e5e5;
            background-color: #fff;
            transition: all 0.3s;
          }

          &::before {
            top: -1px;
            border-radius: 0 0 7px 7px;
            border-top: 0;
          }

          &::after {
            left: auto;
            bottom: -1px;
            border-radius: 7px 7px 0 0;
            border-bottom: 0;
            -webkit-transform: scaleY(1);
            transform: scaleY(1);
            box-sizing: content-box;
          }
        }

        &::after {
          display: none;
        }
      }

      .coupon-box {
        .h4,
        p {
          margin: 0;
        }

        .h4 {
          font-size: 16px;
          line-height: 24px;
          color: #333333;
          font-family: AudiTypeGB-WideBold, AudiTypeGB;
          font-weight: normal;
        }

        p {
          margin-top: 4px;
          height: 15px;
          line-height: 15px;
          color: rgba(0, 0, 0, 0.5);
        }
      }

      .van-radio {
        padding-left: 8px;
        .van-icon {
          &::before,
          &::after {
            content: '';
            width: 22px;
            height: 22px;
            border-color: #808080;
          }

          &::after {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #e5e5e5;
            transform: translate(-50%, -50%);
          }
        }

        .van-radio__icon--checked {
          .van-icon {
            background-color: #fff;
            border-color: #808080;

            &::after {
              position: absolute;
              width: 10px;
              height: 10px;
              background-color: #333;
            }
          }
        }
      }
    }
  }
}
</style>

<style>
.van-popover__action {
  width: auto;
}
</style>

<style lang="less">
// .view-wrapper {
//   height: calc(100vh - 60px) !important;
// }
.send-list-warp{
  width: 100%;padding: 20px 16px 16px;position: relative;box-sizing: border-box;
  display: flex;align-items: center;justify-content: space-between;
  &:after {
    content: '';
    position: absolute;
    box-sizing: border-box;
    pointer-events: none;
    right: 16px;
    bottom: 0;
    left: 16px;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
  }
}
.order-forms-box {
  margin-bottom: 100px;
  .van-cell-group {
    border-top: 8px solid #f2f2f2;
    margin: 0 -16px;
    // padding-bottom: 24px;
    &:first-child {
      border-top-width: 0;
    }
    &.van-hairline--top-bottom {
      &::after {
        display: none;
      }
    }
    .van-hairline--bottom:after,
    .van-cell::after {
      border-color: #e5e5e5;
    }
    .van-field__label {
      margin-right: 0;
    }
    .cell-title {
      box-sizing: content-box;
      margin: 0 16px;
      padding: 16px 0;
      .h2 {
        margin: 0;
        font-size: 16px;
        line-height: 24px;
        font-family: AudiTypeGB-WideBold, AudiTypeGB;
        &.car-owner {
          margin-top: 26px;
        }
        &.mtb {
          margin-top: 8px;
        }
      }
      .sub {
        margin: 0;
        font-size: 10px;
        line-height: 20px;
        color: rgba(#000, 0.4);
      }
      .btn-change {
        .btn-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
    .van-cell {
      padding: 20px 0 16px;
      min-height: 24px;
      font-size: 16px;
      &.van-field--error {
        padding-bottom: 36px;
        &::after {
          bottom: 20px;
        }
      }
      .van-cell__title {
        flex: none;
        //margin-right: 20px;
        color: #000;
      }
      .van-field__control {
        height: 24px;
        line-height: 24px;
        &:disabled {
          color: #000;
          -webkit-text-fill-color: #000;
        }
      }
      .van-radio-group {
        .van-radio {
          margin-right: 24px;
        }
      }
      &:last-child::after {
        display: block;
      }
      &.van-field--error {
        .van-field__control {
          color: #333;
          &::placeholder {
            color: #ccc;
          }
        }
        .van-field__error-message {
          z-index: 19;
          position: absolute;
          bottom: -35px;
          margin-left: -85px;
          color: #eb0d3f;
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 16px;
          &::before {
            content: '';
            position: relative;
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url('~@/assets/img/error-icon.png') no-repeat 0 0;
            background-size: 14px 14px;
            margin-right: 4px;
            top: 0;
          }
        }
        &::after {
          border-color: #eb0d3f;
        }
      }
      &.lan-cell-switch {
        .van-cell__value {
          overflow: visible;
        }
        .lan-switch {
          position: absolute;
          top: -3px;
          right: 0;
          .van-switch__node {
            width: 0.86em;
            height: 0.86em;
            margin: 0.07em;
          }
          &::before {
            content: '';
            position: absolute;
            right: 11px;
            top: 50%;
            width: 4px;
            height: 4px;
            border: solid 2px #8b8b8b;
            border-radius: 50%;
            transform: translateY(-50%);
          }
          &.van-switch--on {
            &::before {
              right: auto;
              left: 15px;
              width: 1px;
              height: 8px;
              border: none;
              border-radius: 0;
              border-left: solid 2px #fff;
            }
          }
        }
      }
    }

    .service-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 0;
      padding-bottom: 16px;
    }

    .van-popover {
      left: 100px !important;
      top: 105px !important;
      margin: 0px;
    }
  }
}
</style>
