<!-- 弹出框 -->
<template>
  <div>
    <div class="config-wrapper">
      <div
        v-if="0"
        class="c-flex-between padding"
      >
        <div>车辆配置</div>
        <div
          class="c-flex-center small-font"
          @click="showCarConfigDetail = !showCarConfigDetail"
        >
          查看全部
          <van-icon :name="showCarConfigDetail ? 'arrow-up' : 'arrow-down'" />
        </div>
      </div>

      <div class="c-flex-between align-center">
        <div class="c-flex-center">
          <div
            class="sc-left"
            style="height: 56px;width: 56px;"
          >
            <img
              style="width: 100%;
              vertical-align: middle;
              object-fit: cover;
              height: 100%;"
              :src="$loadWebpImage(carHeadImageUrl)"
              alt=""
              @click="imagePreview(carHeadImageUrl)"
            >
          </div>
          <div class="sc-height">
            <div
              class="f14"
              style="width: 160px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"
            >
              {{ carModelName }}
            </div>
            <div
              class="small-font"
              style="padding-top: 8px;"
              v-if="$route.query.idx == 2"
            >
              {{ modelPrice | formatPrice }}
            </div>
            <div
              v-else
              class="small-font"
            >
              ¥{{ modelPrice | formatPrice }}
            </div>
          </div>
        </div>

        <div
          class="sc-nowrap"
          @click="toConfigTable"
        >
          查看参数
          <van-icon name="arrow" />
        </div>
      </div>

      <StandardConfig :visible="showCarConfigDetail" />
    </div>
  </div>
</template>

<script>
import { ImagePreview, Popup } from 'vant'
import Vue from 'vue'
import { mapState } from 'vuex'
import StandardConfig from './standard-config.vue'
import confiUrl from '@/config/url'
import { XIAN_XING_VERSION, XIAN_JIAN_VERSION, XIAN_XING_VERSION_Q5 } from '@/config/constant'

const baseOssHost = confiUrl.BaseOssHost

Vue.use(Popup)
export default {
  components: { StandardConfig },

  data() {
    return {
      showCarConfigDetail: true
    }
  },
  computed: {
    ...mapState({
      standardConfigData: 'standardConfigData',
      carHeadImageUrl: (state) => (state.carDetail.configDetail?.carModel?.headImageUrl ? baseOssHost + state.carDetail.configDetail?.carModel?.headImageUrl : ''),
      carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn, // 车辆名称
      carImgUrl: (state) => (state.carDetail.configDetail?.carModel?.imageUrl ? baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl : ''), // 主图
      totalPrice: (state) => state.carDetail.configDetail?.totalPrice,
      modelPrice: (state) => state.carDetail.configDetail?.carModel.modelPrice,
      modellineId: (state) => state.carDetail.configDetail?.carModel?.modellineId,
      modelLineCode: (state) => state.carDetail.configDetail?.carModel?.modelLineCode,
      carSeries: (state) => state.carSeries
    }),
    serverName() {
      if (this.carSeries?.seriesCode === 'G4') {
        if (this.modelLineCode === XIAN_XING_VERSION_Q5) {
          return '艺领权益服务包'
        }
        return '艺创权益服务包'
      }
      if (this.modelLineCode === XIAN_XING_VERSION || this.modelLineCode === XIAN_JIAN_VERSION) {
        return '领尊权益服务包'
      }
      return '尊享权益服务包'
    }
  },
  watch: {},
  mounted() {
    console.log(this.carImgUrl)
  },

  methods: {
    imagePreview(url) {
      ImagePreview([url])
    },
    toConfigTable() {
      // console.log(this.$store.state.carDetail)
      this.$emit('dataTrack', '查看参数')
      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: this.modellineId,
          carModelName: this.carModelName,
          seriesName: this.carSeries.seriesCode,
          from: 0,
          customBack: 'newConfigration'
        }
      })
    },
    toEquity() {
      console.log('qqqqqqqqqq', this.$store.state?.carDetail?.configDetail)
      const configDetail = this.$store.state.carDetail.configDetail
      let caseCode = ''
      configDetail?.optionList && configDetail?.optionList.forEach((e) => {
        if (e.optionCode == 'YEG' || e.optionCode == 'YEA') {
          caseCode = e.optionCode
        }
      })
      this.$router.push({
        path: '/theEquity',
        query: {
          modelLineCode: this.modelLineCode,
          caseCode: caseCode || '',
          orderId: this.$route.query?.orderId || '',
          seriesName: this.carSeries.seriesCode,
          modelVersion: this.carSeries.modelVersion
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
  @import "../assets/style/common.less";

  @leftWith: 18vw;
  @rightMargin: 15px;
  @topMargin: 20px;
  @fontColor: #999;

  //向上的边距
  .sc-m-top {
    margin-top: @topMargin;
  }

  .sc-left {
    // width: @leftWith;
    margin-right: @rightMargin;
    height: 56px;
    width: 56px;
  }

  //box阴影
  .sc-shadow {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
  }

  // 下划线
  .sc-u-line {
    border-bottom: 1px solid #e5e5e5;
  }

  //box阴影
  .sc-shadow {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
  }

  // 下划线
  .sc-u-line {
    border-bottom: 1px solid #e5e5e5;
  }

  //
  .bold18 {
    .c-font18;
    .c-bold;
  }

  .f14 {
    font-size: 14px;
    line-height: 22px;
  }

  .small-font {
    .c-font12;
    color: #999;
    line-height: 20px;
  }

  .container {
    .sc-m-top;
    position: relative;
    padding: 0 18px;
  }

  .hint-wrapper {
    background-color: #f2f2f2;
    font-size: 14px;
    padding: 10px 18px;
  }

  .sc-nowrap {
    .c-font12;
    color:#999;
    white-space: nowrap;
    padding: 5px;
    display: flex;
    align-items: center;
    >.van-icon{
      margin-left: 5px;
    }
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    word-break: break-all;
  }

  .sc-height {
    height: auto !important;
    .c-flex-between;
    flex-direction: column;
    flex: 1;
    height: @leftWith;
    padding: 14px 0;
    box-sizing: border-box;
  }

  // 我的配置
  .config-wrapper {
    // .sc-shadow;
    padding: 0 0 0 16px;
    margin-top: 15px;

    > .padding {
      .sc-u-line;
      padding: 10px 0;
    }

    .config-item {
      position: relative;
      padding: 12px 0;
      height: @leftWith;
    }
  }

  // 客户权益
  .client-wrapper {
    .sc-shadow;

    margin-top: 20px;
    padding: 5px 20px;
    border-bottom: 1px solid #e5e5e5;

    > .title {
      padding: 5px 0;
      margin-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
    }
  }
  .align-center {
    align-items: center;
  }
</style>
