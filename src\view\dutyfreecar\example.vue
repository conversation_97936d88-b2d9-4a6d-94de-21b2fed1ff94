<template>
  <div class="unsubscribeSucceed">
    <div v-if="remark">
      <div class="title-bold">
        失败原因
      </div>
      <div class="chat-input">
        <div class="textarea">
          {{ remark }}
        </div>
      </div>
    </div>

    <div v-if="type === '0'">
      <div class="title-bold">
        身份证（正反面）
      </div>
      <p>请于光线充足的环境下，纯色背景下，拍摄清晰</p>
      <div class="_heardImg">
        <img src="../../assets/img/icon_example1.png">
      </div>
      <div class="_heardImg">
        <img src="../../assets/img/icon_example2.png">
      </div>
    </div>

    <div v-if="type === '1'">
      <div class="title-bold">
        护照
      </div>
      <p
        v-html="
          `上传护照扫描件注意事项：<br/>
	        请上传所持《中华人民共和国护照》个人信息页的图片版本，有效信息不可遮挡<br/>`
        "
      />
      <div class="_heardImgs">
        <img src="../../assets/img/icon_example3.png">
      </div>
    </div>

    <div v-if="type === '2'">
      <div class="title-bold">
        学历认证
      </div>
      <p>请于光线充足的环境下，纯色背景下，拍摄清晰</p>

      <div class="_heardImgs">
        <img src="../../assets/img/icon_example4.png">
      </div>
    </div>

    <div v-if="type === '3'">
      <div class="title-bold">
        准购单
      </div>
      <p
        v-html="
          `上传准购单注意事项：<br/>
          请上传有效的准购单扫描件，需包含：：<br/>
          1、 编号、姓名、护照号<br/>
          2、 海关盖章<br/>`
        "
      />
      <div class="_heardImgs">
        <img src="../../assets/img/icon_example5.png">
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      type: '',
      remark: ''
    }
  },
  mounted() {
    this.type = this.$route.query.type
    this.remark = this.$route.query.remark
    console.log('+++++', this.type)
  },

  methods: {}
}
</script>

<style scoped lang="less">
@import url("../../assets/style/scroll.less");
@import url("../../assets/style/buttons.less");

.unsubscribeSucceed {
  padding: 16px;
  ._heardImg {
    display: flex;
        flex-wrap: wrap;
        justify-content: center;
       img{
        display: block;
        width:268px;
        height: 170px;
        margin-bottom: 24px;

    }
    }
  .title-bold {
    font-size: 16px;
    color: #000;
    font-weight: normal;
    font-family: "Audi-WideBold";
  }
  p {
    font-size: 14px;
    color: #666;
    line-height:22px
  }

  .item-layout {
    display: flex;
    padding-left: 16px;
    align-items: center;

    .title {
      font-size: 12px;
      text-align: center;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      color: #666;
      background: #ffffff;
      border-radius: 50%;
      border: 1px solid #666666;
    }
    .content {
      padding-left: 10px;
      font-size: 14px;
      color: #666;
      font-weight: normal;
    }
  }
}
.chat-input {
  width: 100%;
  display: flex;
  padding-top: 10px;
  margin-bottom: 16px;
  .textarea {
    width: 100%;
    background: #ffffff;
    border: 1px solid #e5e5e5;
    padding: 8px;
    padding-bottom: 30px;
    font-size: 14px;
    color: #000000;
    border-radius: 0;
    -webkit-appearance: none;
  }
}
</style>
