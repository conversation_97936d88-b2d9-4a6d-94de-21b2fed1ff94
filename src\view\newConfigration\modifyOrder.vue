<template>
  <div id="quoPage" class="quo-wrapper">
    <div class="hint-wrapper" v-if="invalid == 0">
      已失效
    </div>
    <div class="hint-wrapper" v-if="updateFlag == 1">
      此配置单价格有更新
    </div>

    <div class="tip-orderlock c-font12" v-show="orderLocked === '1'">
      *温馨提示：当前订单已锁定，暂不支持修改，如有疑问请联系门店管家
    </div>

    <div style="position: relative;padding-top: 30px; background-color: #fff;">
      <div class="carna">
        <div v-if="isA7MrCar" style="padding: 0 16px;" class="model-name">{{ carModelName | a7MrTextWrap }} </div>
        <div v-else style="padding: 0 16px;" class="model-name">{{ carModelName | textWrap }} </div>
      </div>
      <div class="carimg-wrapper">
        <img :src="page2dCarList[0] | imgFix(750)" alt="" srcset="">
      </div>
    </div>

    <div class="container" style="height: 100%;">
      <!-- 我的配置 -->
      <div class="sc-m-top">
        <div class="c-flex-between">
          <div class="c-font16">
            车辆配置
          </div>
          <div class="c-flex-center c-font12" style="color: #333333;" @click="toPowerPage"
            v-if="allowReconfig && !bestRecommendId">
            重新配置
          </div>

          <div class="c-flex-center c-font12" style="color: #333333;" @click="toPowerPageByModify"
            v-if="orderLocked === '0'">
            修改配置
          </div>
        </div>

        <CarConfigList @dataTrack="clickQuotationSensors" />

        <div class="buy-car-equity-wrapper" style="margin-top: 30px;margin-bottom: 16px;" v-if="equityList.length > 0">
          <div class="c-font16">
            购车权益
          </div>
          <div class="equity-wrapper">
            <div v-for="item, idx in equityList" :key="idx" class="card">
              <div class="name-wrapper c-flex-between">
                <div class="name c-bold c-font14"> {{ item.ruleName }}</div>
                <div class="btn-detail c-font12" v-if="item.ruleDetails" @click="showEquityDetail(item)">查看详情</div>
              </div>

              <div class="equity-desc c-font12" v-html="item.ruleDesc"></div>
            </div>
          </div>
          <div class="c-font12" style="color:#999999; margin-top: 12px;">您可享受的购车权益，将按照支付定金的时间予以确定</div>
        </div>
      </div>

      <div class="layout-bottom">
        <div class="left">
          <div class="price-wrapper">
            <div>
              <span class="bold">{{ totalPrice | prefixFormatPrice }}</span>
            </div>
          </div>
        </div>
        <div class="right align-center" >
          <!-- <AudiButton @click="addConfigFun" v-if="showAddConfigBtn" style="margin-right: 4px;" text="保存配置" color="white"
            height="45px" font-size="16px" /> -->
          <AudiButton @click="toModelDetail" style="width: 112px;" text="保存修改" color="black" height="56px"
            font-size="16px" />
        </div>
      </div>
    </div>
    <!-- <canvasCard ref="canvasImg" @getCanvasImg="getCanvasImg" :imgurl="carImgUrl" :title="carModelName + '配置单'" /> -->
    <model :modalshow.sync="modalshow" @update:modalshow="submit" title="在App内打开" confirm-text="复制链接"
      content="点击下方复制链接，前往浏览器打开" />
    <van-dialog v-model="orgShow" :show-cancel-button="false" :show-confirm-button="false">
      <div class="dialog-title">
        你已经在{{ orgName }}建卡，请前往{{ orgName }}选择车辆。
      </div>
      <div class="D-buttons" @click="toDealerDetail">
        确认前往
      </div>
    </van-dialog>

    <!-- <van-dialog v-model="visible" :title="invalidReason">
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div class="goB" @click="handleBack">
          返回
        </div>
        <div class="okB" @click="handleReCC">
          重新配置
        </div>
      </div>
    </van-dialog> -->
    <!-- <van-dialog v-model="updateVis" title="此配置单价格有更新">
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div class="goB" @click="handleBack">
          返回
        </div>
        <div class="okB" @click="handleMoney">
          确认更新
        </div>
      </div>
    </van-dialog> -->

    <network @reload="networkReload()" />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  RadioGroup,
  Radio,
  Cell,
  CellGroup,
  Toast,
  Dialog
} from 'vant'
import { mapState, mapGetters } from 'vuex'
import {
  getCarConfig,
  getProductDetail,
  addCarShoppingCart,
  getShareUrl,
  canUpdateCCConfig,
  getOrgBankList,
  judgeReservationClient,
  getUserInfo,
  getAudiCarUrl,
  getNgaPrice,
  getAPITimeOutTesting,
  getUserRightsByCarModelId
} from '@/api/api'
import {
  getUpdateNew,
  getModelLineQuery,
  getEnergyStyleList
} from '@/configratorApi/index'
import CarConfigList from './carConfigList/car-config.vue'
import model from '@/components/model.vue'
import confiUrl from '@/config/url'
import {
  callNative, getUrlParamObj, getMonthWeek
} from '@/utils'
import canvasCard from '@/components/canvas-card.vue'
import network from '@/components/network.vue'
import AudiButton from '../../components/audi-button.vue'
import debounce from '../quotation/debounce.js'
import { getCarType } from '@/view/newConfigration/util/helper.js'
import { A7MR } from '@/view/newConfigration/car/a7mr'

const baseOssHost = confiUrl.BaseOssHost
Vue.use(Cell)
Vue.use(CellGroup)
Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Toast)

/**
 * 配置与权益页面（基于报价单修改）
 * 从整车订单跳转到报价单-修改配置
 * 修改后的车配列表在这个页面展示
 * 与报价单的区别：报价单的配置基于ccid从接口请求，此页面的配置是用户配置后的本地数据。
 * 需要在这个页面进行修改配置单的接口操作。
 */
export default {
  inject: ['reload', 'checkLoginFn'],
  components: {
    AudiButton,
    CarConfigList,
    model,
    // canvasCard,
    network
  },
  data() {
    return {
      youkeVis: true,
      imageUrlBox: '',
      invalidReason: '',
      measure: '',
      updatePrice: '1',
      invalid: {},
      updateFlag: {},
      packageItem: '',
      updateVis: false,
      visible: false,
      showBox: false,
      showPDE: false,
      allPayPrice: 0,
      earnestPrice: 0, // 小订金额
      mainBtnText: '立即定购',
      orderStatus: '',
      modalshow: false,
      time: Date.now(),
      scopeShowHeader: true,
      isConfig: 0,
      dealerCode: '',
      orgBankList: [],
      orgShow: false,
      orgName: '',
      orgCode: '',
      deliveryTimeByCcid: '',
      orderDetail: false,
      prodId: '',
      carDetailByCcid: {},
      currentModelLine: {},
      isOneStep: true,
      orderLocked: null, // string 0 未锁单 1 锁单
      equityList: [] // 权益列表
    }
  },
  computed: {
    ...mapGetters([
      'page2dCarList'
    ]),
    ...mapState({
      dealerMainImg: (state) => {
        const imgurl = state.dealerInfo?.imageUrl
        return imgurl ? `${baseOssHost}${imgurl}` : ''
      },
      standardConfigData: 'standardConfigData',
      dealerName: (state) => state.dealerInfo.dealerName,
      dealerDistance: (state) => state.dealerInfo.distance,
      dealerAddress: (state) => state.dealerInfo.dealerAdrress,
      carSeries: (state) => state.carSeries,
      // carImgUrl: (state) => (state.carDetail.configDetail?.carModel?.imageUrl ? `${baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl}`
      // : ''), // 主图
      // totalPrice: (state) => state.carDetail.configDetail?.totalPrice,
      // carHeadImageUrl: (state) => (state.carDetail.configDetail?.carModel?.headImageUrl ? baseOssHost + state.carDetail.configDetail?.carModel
      // ?.headImageUrl : ''),
      // carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn, // 车辆名称
      // deliveryTime: (state) => state.carDetail.configDetail?.carModel?.deliveryTime, // 车辆名称
      bestRecommendId: (state) => state.carDetail.bestRecommendId, // 是否是推荐车型
      isReserveCard: 'isReserveCard',
      bestRecommandDealerCode: 'bestRecommandDealerCode',
      env: 'env',
      ccid: 'ccid',
      skuid: 'skuid',
      loginCheck: 'loginCheck',
      isA7MrCar: (state) => A7MR.map((i) => i.code).includes(state.carDetail?.configDetail?.carModel?.modelLineCode),
      queryParams: (state) => state.configration.queryParams,
      orderTime: (state) => state.configration.orderTime,

      carModelName: (state) => state.configration.currentModelLineData.modelLineName, // 车辆名称
      currentModelLineData: (state) => state.configration.currentModelLineData, // 当前车型数据
      currentExterior: (state) => state.configration.currentExterior, // 当前外观颜色数据
      currentSib: (state) => state.configration.currSibSib, // 当前内饰颜色数据
      currentEih: (state) => state.configration.currentEih, // 当前饰板数据
      currentHub: (state) => state.configration.currentHub, // 当前轮毂数据,
      totalPrice: (state) => state.configration.totalPrice
    }),
    payment: {
      get() {
        return this.$store.state.payment
      },
      set(value) {
        this.$store.commit('updatePayment', value)
      }
    },

    showAddConfigBtn() {
      const { orderStatus } = this.$route.query
      // 报价单页显示的按钮, orderStatus 代表从详情页跳转过来
      const boo = this.$route.path === '/quotation' && !orderStatus && !this.bestRecommendId
      // console.log('showAddConfigBtn>>', boo)
      return boo
    },

    allowReconfig() {
      const { orderStatus } = this.$route.query
      return orderStatus !== '31' && orderStatus !== '301' && orderStatus !== '00' && this.isConfig === 1
    },

    // 定金要付的金额
    toPayPrice() {
      return (this.allPayPrice - this.earnestPrice) / 100
    },
    // 定金的总金额
    payedPrice() {
      return this.allPayPrice / 100
    }

  },
  created() {
    this.$store.commit('setHeaderVisible', true)
    const {
      ccid, skuid, orderStatus, invalid, fromPage, dealerCode, env, step, deposited, orderTime, orderLocked
    } = this.$route.query
    this.orderStatus = orderStatus
    // this.$store.commit('saveSkuId', skuid)
    this.$store.commit('updateCcid', ccid)
  },

  mounted() {
    this.fixStyle()
    this.initData()
  },

  methods: {
    // 修复滚动条以及各种hardcode样式
    fixStyle() {
      document.getElementById('common-view-wrapper').scrollTop = 0// 滚动条置顶

      // // 导航栏
      // const app = document.getElementById('app')
      // const header = app.querySelector('.header')
      // if (!header) {
      //   return console.error('找不到 classname: header')
      // }
      // const paddingTop = header.style.paddingTop
      // const currentPaddingTopValue = parseFloat(paddingTop)
      // header.style.paddingTop = `${currentPaddingTopValue + 15}px`
    },
    // 查看全部
    lookAll() {
      this.clickQuotationSensors('客户权益查看全部') // 埋点

      const configDetail = this.$store.state.carDetail.configDetail
      let caseCode = ''
      configDetail?.optionList && configDetail?.optionList.forEach((e) => {
        if (e.optionCode === 'YEG' || e.optionCode === 'YEA') {
          caseCode = e.optionCode
        }
      })
      const obj = {
        modelLineCode: configDetail?.carModel?.modelLineCode || '',
        caseCode: caseCode || '',
        orderId: this.$route.query?.orderId || '',
        seriesName: this.carSeries?.seriesCode || '',
        modelYear: configDetail?.carModel?.modelYear,
        modelVersion: configDetail?.carModel?.omdModelVersion
      }
      this.$router.push({
        path: '/theEquity',
        query: obj
      })
    },
    // 预约试驾
    tryCar() {
      this.clickQuotationSensors('预约试驾')// 埋点

      const seriesCode = this.carSeries.seriesCode
      // console.log(seriesCode, ['49', 'G4', 'G6'].findIndex((e) => e == seriesCode))
      this.$router.push({
        path: '/testdrive/create',
        query: {
          idx: ['49', 'G4', 'G6'].findIndex((e) => e == seriesCode)
        }
      })
    },

    handleBack() {
      this.visible = false
      this.updateVis = false
    },
    async handleMoney() {
      this.$store.commit('showLoading')
      // 点击确认，则自动将有更新的价格
      const { ccid, skuid } = this.$route.query
      const res = await getUpdateNew(ccid)
      this.$store.commit('hideLoading')
      this.updateVis = false
      this.updatePrice = ''
      this.reload()
    },


    // 返回到动力页面
    toPowerPage() {
      this.$store.commit('updateCarModelTab', 'power')

      const { orderStatus, orderId, shoppingCartId } = this.$route.query
      const carIdx = { 49: 0, G4: 1, G6: 2 }
      const code = this.carSeries.seriesCode

      /**
       * measureType: 1高定 2:半定
       * 这里的需求是:
       * 动力页默认是高定+半定的混合车型.
       * 半定的车型选择重新到动力页,要过滤掉高定的车型,只能选择半定
       * 高定同理
       */
      this.$router.push({
        path: '/configration',
        query: {
          idx: carIdx[code],
          orderStatus,
          orderId,
          shoppingCartId: shoppingCartId || '',
          measureType: this.measure ? 2 : 1
        }
      })

      this.clickQuotationSensors('重新配置') // 埋点
    },

    // （配置与权益）未锁单的情况下，可修改配置
    toPowerPageByModify() {
      this.$store.commit('updateCarModelTab', 'power')
      // const { deposited, orderTime, orderLocked } = this.$route.query
      const { orderStatus, orderId } = this.$route.query

      const carIdx = { 49: 0, G4: 1, G6: 2 , F0:3}
      const code = this.carSeries.seriesCode

      Dialog.confirm({
        message: '是否确认修改配置',
        className: 'customstyle',
        confirmButtonText: '取消',
        cancelButtonText: '确认'
      }).then(() => {
        // null
      }).catch(() => {
        this.$router.push({
          path: '/configration',
          query: {
            idx: carIdx[code],
            orderStatus,
            orderId,
            action: 'modelDetailModify', // 配置权益修改
            measureType: this.measure ? 2 : 1
          }
        })
      })
    },

    // 获取权益数据
    async getEquityData() {
      const {
        modelLineCode, modelYear, version
      } = this.currentModelLineData

      const { orderId } = this.queryParams

      let serachTime = Date.now()
      if (this.orderTime) {
        const decodedTimeStr = this.orderTime.replace(/\+/g, ' ')
        const date = new Date(decodedTimeStr) // 转为日期对象
        serachTime = Math.floor(date.getTime() / 1000) // 转为时间戳（秒）
      }

      const params = {
        carModelId: modelLineCode, //
        modelYear: modelYear,
        modelVersion: version,
        searchTime: serachTime,
        // 有orderId 的时候需要传orderType
        ...orderId ? { orderId } : {}
      }

      const res = await getUserRightsByCarModelId(params)
      const data = JSON.parse(res.data.data.rights)
      console.log('🚀 ~ getEquityData ~ data:', data)
      this.equityList = data
    },

    // 跳转到权益详情
    showEquityDetail(item) {
      this.$store.commit('updateEquityDetail', item.ruleDetails)
      this.$router.push({
        path: '/configrationEquityDetail'
      })
    },

    /**
     * 查看ccid是否还能更新配置
     * 0 不可以 1 可以
     */
    // async setCanUpdateCCConfig() {
    //   const param = { ccid: this.$route.query.ccid }
    //   const res = await canUpdateCCConfig(param)
    //   this.isConfig = res.data.data
    // },

    // 保存修改
    async toModelDetail() {
      this.$store.commit('showLoading')

      const { ccid, orderStatus, orderId } = this.$route.query
      // console.log('🚀 ~ toModelDetail ~ ccid:', ccid, orderStatus)
      const res = await canUpdateCCConfig({ ccid })

      const isUpdate = res.data.data // 0 不可以 1 可以
      if (isUpdate === 0) {
        this.$store.commit('hideLoading')
        Toast('温馨提示：当前订单已锁定，暂不支持修改，如有疑问请联系门店管家')
        return
      }

      // 更新配置单
      await Promise.all([
        this.$store.dispatch('getSkuId'), // this.skuid
        this.$store.dispatch('getCCid', {
          orderStatus
        })
      ])
      this.$store.commit('hideLoading')

      const { orderType } = this.queryParams
      // 跳转回整车订单页
      this.$router.push({
        path: '/order/model-detail',
        query: {
          orderId,
          orderType: orderType
        }
      })
    },

    submit(isSubmit) {
      if (isSubmit) {
        // 复制到粘贴板
        const copyResult = this.InsertShearPlate()
        this.modalshow = false
        if (copyResult) {
          Toast({
            type: 'fail',
            message: '已复制到剪贴板',
            icon: require('../../assets/img/success.png')
          })
        } else {
          Toast({
            type: 'fail',
            message: '复制失败',
            icon: require('../../assets/img/error.png')
          })
        }
      }
    },
    InsertShearPlate() {
      // 复制到粘贴板
      const text = 'https://app.saic-audi.mobi/index.html#/?channel=miniapp_VehiclePurchase'
      const copyString = text
      // 创建input标签存放需要复制的文字
      const myInput = document.createElement('input')
      // 把文字放进input中，供复制
      myInput.value = copyString
      document.body.appendChild(myInput)
      // 选中创建的input
      myInput.select()
      // 执行复制方法， 该方法返回bool类型的结果，告诉我们是否复制成功
      const copyResult = document.execCommand('copy')
      // 操作中完成后 从Dom中删除创建的input
      document.body.removeChild(myInput)
      return copyResult
    },
    audiShare() {
      this.time = Date.now() + 10
      this.$refs.canvasImg.getCanvasImg()
    },

    async getCanvasImg(e) {
      console.log(`图片+${e.fileStorageId}`, baseOssHost + e.fileUrl)
      const subTitle = this.standardConfigData.map((i) => i.name)
      const res = await getShareUrl()
      const path = `${res.data.data.configValue}#/carConfig?ccid=${this.$route.query.ccid}`
      console.log(baseOssHost + e.fileUrl)
      callNative('audiShare', {
        type: 'carConfig',
        path: path, // `${window.location.origin}/order/index.html#/car-config-other?ccid=${this.$route.query.ccid}`,
        imageUrl: baseOssHost + e.fileUrl,
        ccid: this.$route.query.ccid,
        title: `欢迎围观我的${this.carModelName}`,
        subTitle: `我的配置${subTitle.join('|')}`
      }).then((data) => {
        console.log(data, '分享回调')
      })
    },
    addConfigFun() {
      this.clickQuotationSensors('保存配置')

      debounce(() => {
        this.addConfigFn()
      }, 1000)
    },

    // 添加配置单到购物车列表
    async addConfigFn() {
      console.log('检查是否需要跳转到登录页 ')
      if (!this.checkLoginFn()) return // 检查是否需要跳转到登录页
      this.$store.commit('showLoading')
      const { ccid, skuid, inviteBuyCarCode } = this.$store.state
      const shoppingCartId = this.$route.query?.shoppingCartId || ''
      const entryPoint = this.$storage.getPlus('entryPoint')
      const { data } = await addCarShoppingCart({
        ccid, skuid, invitationCode: inviteBuyCarCode, shoppingCartId, entryPoint
      })
      if (data.code === '00') {
        this.$store.commit('hideLoading')
        Toast({
          message: data.message,
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      }
    },
    // 跳转到代理商详情
    async toDealerDetail() {
      const { data } = await getAudiCarUrl()
      const url = `${data.data.configValue}dealerDetail?dealerCode=${this.orgCode}`
      callNative('audiOpen', { path: url })
    },

    async initData() {
      this.$store.commit('showLoading')
      getAPITimeOutTesting()
      // this.getCarConfigfn()
      // const isToken = this.$storage.get('token')
      // if (isToken) {
      //   this.setCanUpdateCCConfig()
      // }
      await this.getEquityData()
      Toast.clear()
      this.$store.commit('hideLoading')
    },

    networkReload() {
      this.initData()
    },

    // 埋点
    clickQuotationSensors(buttonName) {
      const carMap = {
        49: {
          carName: 'A7L',
          idx: 0
        },
        G4: {
          carName: 'Q5 e-tron',
          idx: 1
        },
        G6: {
          carName: 'Q6',
          idx: 2
        }
      }
      const configDetail = this.$store.state.carDetail.configDetail

      const { modelLineCode, modellineId } = configDetail.carModel
      const { customSeriesId, seriesCode } = configDetail?.carSeries
      if (!carMap[seriesCode]) {
        console.error('未找到对应的车型:', seriesCode)
        return
      }
      const carType = getCarType()

      Promise.all([
        getModelLineQuery(modellineId),
        getEnergyStyleList(customSeriesId, carType)
      ]).then((res) => {
        const currentModelLine = res[0].data.data[0]
        const carList = res[1].data.data
        // 获取carVersion 字段
        let carVersionName = ''
        for (const car of carList) {
          for (const i of car.styleVos) {
            for (const j of i.modelLineList) {
              if (j.modelLineCode === modelLineCode) {
                carVersionName = i.styleName
                break
              }
            }
          }
        }
        const param = {
          source_module: 'H5',
          car_series: carMap[seriesCode].carName,
          car_type: currentModelLine.typeFlag,
          power_type: `${carMap[seriesCode].carName} ${currentModelLine.engine}`,
          car_version: carVersionName,
          delivery_type: '定制交付', // 快速交付|定制交付
          button_name: buttonName
        }
        console.log('CC_Quotation_BtnClick埋点:', param)
        this.$sensors.track('CC_Quotation_BtnClick', param)
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "../../assets/style/common.less";
@import url("../../assets/style/dialog.less");
@import url("../../assets/style/buttons.less");
/** 公共样式 */

@leftWith: 18vw;
@rightMargin: 15px;
@topMargin: 20px;
@fontColor: #999;

.quo-wrapper {
  margin-bottom: 60px;
}

.tip-orderlock {
  background-color: #000;
  color: #fff;
  padding: 3px 16px;
  line-height: 20px;
}

.shB {
  width: 22px;
  height: 22px;
  position: absolute;
  z-index: 999;
  top: 7px;
  right: 16px;
}

.qeB {
  color: #62615F;
  font-size: 12px;
  margin-top: 4px;
}

.texB {
  color: #B1B1B1;
  font-size: 10px;
  text-align: center;
  margin-top: 4px;
}

.imgC {
  width: 24px;
  margin-top: 8px;
}

.goPay {
  font-size: 16px;
  width: 100%;
  line-height: 45px;
  max-width: 70%;
  background: #E5E5E5;
  color: #FFFFFF;
  line-height: 45px;
  text-align: center;
}

.goB {
  border: 1px solid;
  padding: 15px 0;
  width: 50%
}

.okB {
  background: black;
  color: white;
  padding: 15px 0;
  width: 50%;
  margin-left: 2px;
}


.carna {
  position: absolute;
  width: 100%;
  top: 12px;
  line-height: 24px;

  >.model-name {
    font-size: 16px;
    white-space: pre;
  }
}

//向上的边距
.sc-m-top {
  margin-top: @topMargin;
}

.sc-left {
  width: @leftWith;
  margin-right: @rightMargin;
}

.sc-nowrap {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
}

.sc-height {
  .c-flex-between;
  flex-direction: column;
  flex: 1;
  height: @leftWith;
  padding: 12px 0;
  box-sizing: border-box;
}

//box阴影
.sc-shadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
}

// 下划线
.sc-u-line {
  border-bottom: 1px solid #e5e5e5;
}

//
.bold18 {
  font-size: 16px;
  .c-bold;
}

.small-font {
  .c-font12;
  color: #999;
}

.container {
  .sc-m-top;
  position: relative;
  padding: 0 18px;
  padding-bottom: 100px;
}

.hint-wrapper {
  background-color: #f2f2f2;
  font-size: 14px;
  padding: 10px 18px;
}

.carimg-wrapper {
  height: 185px;
  background: linear-gradient(to bottom, #fff, #f0f0f0);
  > img {
    object-fit: cover;
    width: 100%;
    height: 104%;
  }
}

/** 其他样式 */
//代理商
.proxy-wrapper {
  .sc-shadow;
  .c-flex-center;

  position: relative;
  margin-top: 15px;

  >.left {
    .c-flex-center;
    width: 30%;
    margin-right: 15px;
  }

  >.right {
    position: relative;
    height: 110px;
    flex: 1;

    .title {
      margin: 10px 0;
    }

    .bottom {
      .small-font;
      position: absolute;
      bottom: 4px;
    }
  }
}

.layout-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 5px 16px 35px 16px;
  box-sizing: border-box;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);

  .left {
    font-size: 12px;
    color: #999999;
    margin-top: 6px;

    .price-wrapper {
      >div {
        display: flex;
      }
    }

    .bold {
      font-size: 16px;
      color: #000;
      line-height: 24px;
    }

    .font14 {
      font-size: 12px;
      color: #999999;
    }
  }

  .right {
    width: 65%;
  }
}

.align-center {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.dialog-title {
  .c-font16;
  .c-bold;
  text-align: left;
  margin-bottom: 20px;
}


.equity-wrapper {
  .card {
    border: 1px solid #E5E5E5;
    border-radius: 4px;
    padding: 12px;
    margin-top: 14px;

    .name{
      line-height: 22px;
    }
    .btn-detail {
      line-height: 22px;
      color: #333333;
    }

    >.equity-desc {
      margin-top: 12px;
      white-space: pre-wrap;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.5);
    }
  }
}

/deep/.van-dialog {
  overflow-y: auto;
  max-height: 80%;
  padding: 15px 16px 16px;
  top: 52% !important;
  z-index: 33336;

  h3 {
    margin: 0;
  }

  .item {
    color: #000;
    font-size: 14px;
    text-align: left;
    margin-bottom: 24px;

    .title {
      line-height: 24px;
    }

    .itemCotent {
      display: flex;
      line-height: 17px;

      div {
        margin-top: 8px;
      }
    }
  }
}
</style>
