<template>
  <div
    class="loading"
    v-if="$store.state.loading && !fromType"
  >
    <van-loading
      color="#000"
      class="loadingVant"
      vertical
      type="spinner"
    >
      加载中...
    </van-loading>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  data() {
    return {
      fromType:this.$route.query?.fromType === 'fromPurple',//全局loading - audi时候不显示
    }
  },
  mounted(){
    console.log("this.$route.query?.fromType ------ loading",this.$route.query?.fromType,'---',this.$store.state.loading)
  }
}
</script>
<style scoped lang="less">
.loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
/deep/.van-loading {
  margin-top: 100px;
}
</style>
