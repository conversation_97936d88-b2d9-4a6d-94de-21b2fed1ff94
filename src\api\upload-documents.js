import request from '../router/axios'
import api from '../config/url'
import {
  getToken
} from '../utils/auth'
import {
  getSubmitToken
} from './detail'

const baseUrl = api.BaseApiUrl
export const uploadImg = async (data) => {
  const res = await getSubmitToken()
  if (['200', '00'].includes(res.data.code)) {
    const headers = {
      'Content-Type': 'multipart/form-data',
      'x-access-token': localStorage.getItem('token'),
      'x-submit-token': res.data.data
    }

    return request({
      url: `${baseUrl}/api-wap/cop-oss/api/v1/files/upload`,
      method: 'POST',
      headers,
      data: data
    })
  }
}

// 添加数字支付凭证信息
export const addOrderDigitalCertificate = (data) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/v1/addOrderDigitalCertificate`,
  method: 'POST',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  data: {
    ...data
  }
})
