<template>
  <div>
    <div class="main-img-wrapper" v-if="!currentDetail?.labelChildren?.length && currentDetail?.materialList?.length">
      <img :src="currentDetail.materialList?.[0]?.materialUrl | imgFix(640, true)" alt="">
    </div>
    <div class="content">
      <div class="c-bold c-font14 c-lh22">
        {{ currentDetail.externalFeatureNameZh }}
      </div>

      <span class="price c-font12 c-lh20" :class="{ 'line-through': disabledRights }">
        {{ currentDetail.featurePrice | finalFormatPriceDesc }}
      </span> <span v-if="disabledRights" class="balcks">￥0</span>
      <div class="list-wrapper" v-if="labelsList">
        <div v-for="item in labelsList"  :key="item.featureCode" class="item c-flex-center">
          <div class="img-wrapper">
            <!-- <img :src="ossUrl + item.imageUrlList | imgFix(100, true)" alt=""> -->
            <img :src="currentDetail.materialList?.[0]?.materialUrl | imgFix(640, true)" alt="">
          </div>
          <div class="">
            <div class="c-font12"> {{ item.externalFeatureNameZh }} </div>
          </div>
        </div>
      </div>

      <div class="list-wrapper" v-else>
        <div class="item c-flex-center">
          <div class="img-wrapper">
            <img :src="currentDetail.materialList?.[0]?.materialUrl | imgFix(640, true)" alt="">
          </div>
          <div class="">
            <div class="c-font12"> {{ currentDetail.externalFeatureNameZh }} </div>
          </div>
        </div>
      </div>

    </div>

    <!-- <div class="footer-wrapper" v-if="!footerVisible">
      <div class="footer c-footer-shadow c-flex-between c-font14">

        <div class="left">
          <div class="price-wrapper c-bold c-font16">
            ￥{{ currentDetail.featurePrice | formatPrice }}
          </div>
          <div class="deposit-wrapper  c-font14">
          </div>
        </div>
      </div>
    </div> -->

  </div>
</template>

<script>
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import { Icon } from 'vant'
import url from '@/config/url'
import { getOmdGetlabel } from "@/configratorApi";
import { SUMMER_PACKAGE, A7L_FIGURE_KEY } from '../util/carModelSeatData'

Vue.use(Icon)

const OSS_URL = url.BaseConfigrationOssHost
export default {
  data() {
    return {
      ossUrl: OSS_URL,
      // fromComposePage: false, // 如果从组合页面跳转来, 就不显示按钮
      summerPackage: SUMMER_PACKAGE,
      a7lFigureKey: A7L_FIGURE_KEY,
      labelsList:[],
      disabledRights: false
    }
  },
  computed: {
    ...mapGetters(['pageAllOptionList']),
    ...mapState({
      currentDetail: (state) => state.configration.currentOptionDetail,
      currentCarInfo: (state) => state.configration.currentCarInfo
    }),
    footerVisible: function () {
      return this.$route.query.disabled === 'true'
    }
  },

  async mounted() {
    setTimeout(() => {
      console.log(this.currentDetail);
    }, 2000);
    
      // 从路由参数中获取disabledRights并转换为布尔值
    const disabledRightsStr = this.$route.query.disabledRights;
    this.disabledRights = disabledRightsStr === 'true'; // 字符串转布尔值
    if (this.currentDetail?.labelChildren?.length > 0) {
      // 遍历labelChildren，收集所有接口调用的Promise
      const promiseList = this.currentDetail.labelChildren.map(labelChild => {
        // 假设每个labelChild包含需要的featureCode，根据实际结构调整
        return getOmdGetlabel(labelChild.featureCode, this.currentCarInfo.modelUnicode);
      });

      // 并行执行所有接口调用
      const results = await Promise.all(promiseList);

      // 合并所有结果中的children数组
      this.labelsList = results.flatMap(result => {
        // 确保存在嵌套结构，避免报错
        return result?.data?.data?.result?.children || [];
      });
    } else {
      // 原有逻辑：单次调用接口
      const labelList = await getOmdGetlabel(
        this.currentDetail.featureCode,
        this.currentCarInfo.modelUnicode
      );
      this.labelsList = labelList.data.data.result.children;
    }

    console.log(this.labelsList, 'this.labelsList');
  },

  methods: {
    toAddRemoveOption() {
      this.$router.push({
        path: '/configrationContainer',
        query: {
          optionCode: this.currentDetail.optionCode
        }
      })
    },

    toCompotiblePage() {
      this.$router.push({
        path: '/configrationMobileCompatible',
        query: {
          // optionCode: this.currentDetail.optionCode
        }
      })
    }

  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";

@HEIGHT: 100px;

.main-img-wrapper {
  min-height: 208px;
}
.mt8{
  margin-top: 8px;
}
.black {
  color: #000;
}

.content {
  padding: 9px 16px;
  margin-top: 8px;
  padding-bottom: 120px;
  >.name {
    // position: relative;
    display: flex;
    align-items: center;
    >.tag-wrapper {
      position: relative;
      margin-left: 8px;
      background:#EB0D3F;
      color: #fff;
      font-weight: normal;
      font-size: 10px;
      line-height: 16px;
      box-sizing: border-box;
      padding:0 13px 0 5px;
      &::before{
        content: '';
        display: block;
        position: absolute;
        right: 0;
        top: 0;
        background-color: #fff;
        border-top: 9px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 7px solid #EB0D3F;
        border-left: 5px solid #EB0D3F;
      }
    }
  }
}

.price {
  color: #333333;
  margin-top: 4px;
  font-weight: normal;
  font-family: "Audi-ExtendedBold";
  opacity: 0.6;
}

.list-wrapper {
  margin-top: 12px;

  >.item {
    color: #808080;
    margin-top: 8px;

    .img-wrapper {
      flex-shrink: 0;
      width: 50px;
      height: 50px;
      margin-right: 13px;

      >img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.van-icon {
  font-size: 14px;

  &::before {
    vertical-align: middle;
  }
}

.footer {
  background: #fff;
  position: fixed;
  z-index: 20;
  bottom: 0;
  left: 0;
  width: 100%;
  height: @HEIGHT;
  padding: 5px 13px 0 13px;
  box-sizing: border-box;

  .left {
    height: min-content;

    .price-wrapper {
      margin-top: 10px;
    }

    .deposit-wrapper {
      color: #999999;
      margin-top: 4px;
    }
  }

  .right {
    border: 1px solid;
    height: min-content;

    >.next-btn {
      .c-font16;
      width: 140px;
      line-height: 56px;
      background-color: #000;
      color: #fff;
      text-align: center;

      &.bg-white {
        background-color: #fff;
        color: #000;
      }
    }
  }
}
.balcks{
  font-size: 12px;
  color: #000;
}
.line-through {
  text-decoration: line-through;
  color: gray;
}
</style>
