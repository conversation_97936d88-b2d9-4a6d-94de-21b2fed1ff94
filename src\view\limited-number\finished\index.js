/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-05-07 11:07:25
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-02-21 09:15:46
 * @FilePath     : \src\view\limited-number\finished\index.js
 * @Descripttion : 典藏号选择成功
 */

import HeaderCustom from '@/components/header-custom.vue'
import audiButton from '@/components/audi-button.vue'

import { getHasLimitedNumber } from '@/api/api'
import { paramsStrict } from '@/utils'

export default {
  components: {
    'header-custom': HeaderCustom,
    'audi-button': audiButton
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      orderId: 0,
      seriesCode: '',
      destroyNativeBack: false,
      limitNumber: 0,
      limitNumberImgs: [],
      orderStatus: ''
    }
  },
  created() {
    this.handleCheckNumber()
  },
  methods: {
    async handleCheckNumber() {
      const {
        orderId, seriesCode, modelLineCode, orderStatus
      } = paramsStrict(this.$route.query)
      const { data: { data, code } } = await getHasLimitedNumber({ orderId })
      this.orderStatus = orderStatus
      this.orderId = orderId
      this.seriesCode = seriesCode
      this.modelLineCode = modelLineCode
      if (code === '00' && data) {
        this.limitNumber = data
        this.limitNumberImgs = [...data]
      }
      console.log(`查询当前[订单]${orderId}限量号：${data}`)
    },
    handleCheckMineNumberBtn() {
      const {
        limitNumber, orderId, seriesCode, modelLineCode, orderStatus
      } = this
      this.$router.push({
        name: 'limited-number-credentials',
        query: {
          orderId, limitNumber, seriesCode, modelLineCode, orderStatus
        }
      })
    },
    handleLeftBack() {
      const { orderId } = this
      this.$router.push({ name: 'money-detail', query: { orderId } })
    }
  }
}
