<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-04-12 11:32:04
 * @LastEditors  : <PERSON> Ma
 * @LastEditTime : 2022-05-09 17:53:41
 * @FilePath     : \src\view\lucky-bag\lottery.vue
 * @Descripttion :
-->
<template>
  <div
    class="lucky-bag-wrapper"
  >
    <div class="lucky-bag-box">
      <div class="lucky-bag-list thank-you-note">
        <div class="bag-box gray text">
          <h4>致每一位进取的你：</h4>
          <p>2022年4月18日 上汽奥迪迎来了一周岁生日。 过去的一年间，我们朝着光的方向，步步前行，携手走过进取之路，共同见证朝阳般的新生。 </p>
          <p>今天，上汽奥迪以一周年为契机 与您共同庆祝这一年来所收获的欣喜与成长 为此，特推出限时限量「进取周岁福包」的活动， 感谢并回馈每一位一路相陪的汇友们。</p>
          <p>「进取周岁福包」包含了针对见远和志远两款车型，合计60台的限量礼包。您可以在限定时间内抽取，并可直接用于上汽奥迪A7L的大定，而礼包也将自动膨胀成与包中选装组合等同的价值，以此为您的进取之路助力，与您共同开启全新旅程。</p>
          <div class="note-footer">
            <h4>上汽奥迪</h4>
            <h4>写于一周岁生日</h4>
          </div>
        </div>
      </div>
      <div class="lucky-bag-box">
        <!-- <div class="lucky-bag-list">
          <h3 class="lucky-bag-title">
            这是什么福袋？
          </h3>
          <div class="bag-box gray text">
            <p>福袋是一种非传统购物模式，</p>
            <p>是一种商家组织的娱乐互动</p>
            <p>感恩回馈活动充满冒险惊喜。</p>
            <p>产品的款式随机抽取，颜色可以选择！</p>
            <p>靠的是手气，玩的是心跳。</p>
          </div>
        </div> -->
        <div class="lucky-bag-list">
          <h3 class="lucky-bag-title">
            福包里有什么？
          </h3>
          <div
            class="bag-box gray gifts"
          >
            <div
              class="flex-box"
              data-flex="main:justify cross:center"
            >
              <div class="left">
                <div
                  class="box"
                  data-flex="main:center cross:center"
                >
                  <img
                    class="img"
                    :src="require(`@/assets/bags/${luckyBagId}.png`)"
                  >
                </div>

                <p>·{{ carModelName }}·</p>
              </div>
              <div class="midd">
                <div
                  class="box"
                  data-flex="main:center cross:center"
                >
                  <img
                    class="img"
                    :src="require('@/assets/bags/icon-plus.png')"
                  >
                </div>
              </div>
              <div class="right">
                <div
                  class="box"
                  data-flex="main:center cross:center"
                >
                  <img
                    class="img"
                    :src="require('@/assets/bags/icon-gift.png')"
                  >
                </div>
                <p>·选装组合 随机·</p>
              </div>
            </div>
          </div>
        </div>
        <div class="lucky-bag-list">
          <h3 class="lucky-bag-title">
            您可随机获得：
          </h3>
          <div class="bag-box">
            <div
              class="list"
              v-for="(item, index) in list"
              :key="item.id"
            >
              <h4
                class="h4"
                data-flex="main:justify"
              >
                <em>福包{{ item.blessedPackTitle }} × {{ item.overBlessedPackAllCount }}</em>
                <span>¥{{ item.totalPrice }}</span>
              </h4>
              <p
                data-flex="main:justify"
                v-for="i in item.blessedPackConfItems"
                :key="i.id"
              >
                <em>{{ i.confItemName }} <span
                  v-if="i.tips"
                  class="tips"
                >{{ i.tips }}</span></em>
                <span>¥{{ i.confItemPrice }}</span>
              </p>
            </div>
            <div class="tips">
              * 福包的抽取概率为该类别福包的数量除以该车型福包数量的总和
            </div>
          </div>
        </div>
      </div>
      <div class="protocol">
        <van-checkbox
          v-model="checked"
          icon-size="15px"
          shape="square"
          checked-color="#000000"
          @click="handleCheckBox"
        >
          阅读并同意<router-link :to="{name: 'lucky-bag-cars-rule-node'}">
            《上汽奥迪进取周岁福包活动规则》
          </router-link>
        </van-checkbox>
      </div>
      <van-popup
        v-model:show="warningPopShow"
        class="lottie-lucky-bags warning-pop"
        :close-on-click-overlay="false"
      >
        <div
          class="warning-pop-box"
        >
          <h3 class="h3">
            {{ warningTitle }}
          </h3>
          <template v-if="warningStatus === 1">
            <audi-button
              @click="handleCancelCheck"
              color="black"
              :text="`${ warningStatusText || '好的' }`"
              height="46px"
            />
          </template>
          <template v-if="warningStatus === 2">
            <div
              class="btn"
              data-flex="main:justify"
            >
              <audi-button
                @click="handleCancelCheck"
                text="取消"
                height="46px"
                width="calc(50% - 2px)"
              />
              <audi-button
                @click="handleConfirmNativeRoutePath('scaudi://mine/sign/home')"
                color="black"
                text="去赚奥金"
                width="calc(50% - 2px)"
                height="46px"
              />
            </div>
          </template>
        </div>
      </van-popup>
      <van-popup
        v-model:show="popLuckyBagShow"
        class="lucky-bags-pop"
        :closeable="popLuckyBagCloseable"
        @click-close-icon="() => popLuckyBagCloseable = false"
      >
        <div class="lottie-lucky-bags">
          <Lottie
            v-show="luckyBagBtnStatus !== 'opened'"
            :class="['lottie-box', luckyBagBtnStatus]"
            :options="luckyOpenOptions.opening"
            @animCreated="handleAnimation($event, 'opening')"
          />
          <Lottie
            v-show="luckyBagBtnStatus === 'opened'"
            :class="['lottie-box', luckyBagBtnStatus]"
            :options="luckyOpenOptions.opened"
            @animCreated="handleAnimation($event, 'opened')"
          />
          <audi-button
            :class="luckyBagBtnStatus"
            @click="handleLuckyBagOpen"
            :color="buttonColor"
            :text="luckyBagBtnText"
            width="240px"
            :height="buttonHeight"
          />
        </div>
      </van-popup>
      <div class="fixed-bottom-box">
        <audi-button
          @click="handleLuckyBagStart"
          :color="buttonColor"
          :text="`使用${integral}奥金抽取福包`"
          :height="buttonHeight"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Popup, Checkbox, Toast } from 'vant'
import Lottie from '@/components/lottie.vue'
import AudiButton from '@/components/audi-button.vue'
import {
  delay, checkType, callNative, numberToUppercase
} from '@/utils'
import * as luckyOpenData from '@/assets/lottie/lucky-bags/open/data.json'
import * as luckyAfterOpenData from '@/assets/lottie/lucky-bags/after-open/data.json'
import {
  getLuckyBagInfo, getLuckyBagOpen, getLuckyBagIdInfo
} from '@/api/lucky-bag'


Vue.use(Popup).use(Checkbox).use(Toast)
const luckyBagStatus = Object.freeze({
  open: '打开福包',
  opening: '打开中...',
  opened: '查看福包'
})
const imgStr = 'img_'
luckyAfterOpenData.assets.forEach((item) => handleFilterData(item, 'after-open'))
luckyOpenData.assets.forEach((item) => handleFilterData(item, 'open'))

function handleFilterData(item, path = '') {
  if (item.p && item.p.includes(imgStr) && item.u) {
    item.u = ''
    item.p = require(`@/assets/lottie/lucky-bags/${path}/${item.p}`)
  } else if (item.id && item.layers?.length && checkType(item.layers) === 'array') {
    item.layers.forEach((i) => {
      if (i.nm && i.nm.includes(imgStr)) {
        const img = i.nm.substring(0, i.nm.lastIndexOf('.')).replace(new RegExp(imgStr, 'g'), '')
        i.nm = require(`@/assets/lottie/lucky-bags/${path}/img_${img}.png`)
      }
    })
  }
}

export default {
  name: 'LuckyBagCarsDetails',
  components: { AudiButton, Lottie },
  data() {
    return {
      luckyBagId: 0, // 福袋ID
      luckyPackageId: 0, // 福包ID
      params: {},
      warningPopShow: false,
      popLuckyBagCloseable: false,
      luckyBagBtnText: luckyBagStatus.open,
      luckyBagBtnStatus: Object.keys(luckyBagStatus)[0],
      checked: false,
      warningStatus: 1,
      warningTitle: '',
      warningStatusText: '',
      list: [],
      lottie: 0,
      buttonColor: 'black',
      buttonHeight: '56px',
      popLuckyBagShow: false,
      popLuckyBagOpen: false,
      popLuckyBagIsOpen: [false], // 是否满足开福袋
      integral: 0, // 奥金
      carModelName: '',
      luckyOpenOptions: { opening: { animationData: luckyOpenData.default }, opened: { animationData: luckyAfterOpenData.default } },
      anim: {}
    }
  },
  mounted() {
  },
  created() {
    // this.getLuckyBagStatusData()
    const {
      luckyBagId, skuid
    } = this.$route.query || 0
    if (luckyBagId) {
      const { luckyPackageId } = this
      this.luckyBagId = luckyBagId
      this.params = {
        luckyBagId, skuid, luckyPackageId
      }
      this.getLuckyBagIdInfoData(luckyBagId)
      this.getLuckyBagInfoData(luckyBagId)
    }
  },
  methods: {
    handleConfirmNativeRoutePath(path = '') {
      if (!path) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '跳转原生路由页面不存在'
        })
      }
      callNative('openRoutePath', { path })
    },
    // 开福袋所需积分
    async getLuckyBagIdInfoData(luckyBagId) {
      const { data } = await getLuckyBagIdInfo({ blessedBagId: luckyBagId })
      if (data?.data) {
        const { integral, carModelName } = data.data
        if (integral) {
          this.integral = data.data.integral
        }
        if (carModelName) {
          this.carModelName = data.data.carModelName
        }
      }
    },
    // 福袋内容(列表)
    async getLuckyBagInfoData(luckyBagId) {
      const blessedBags = [
        [],
        [
          {
            blessedPackTitle: '福包一',
            overBlessedPackAllCount: 3,
            blessedPackConfItems: [
              { confItemName: '20吋10幅 星芒轮毂', confItemPrice: 7000 },
              { confItemName: '檀棕 天然桦木饰条', confItemPrice: 3000 }
            ]
          },
          {
            blessedPackTitle: '福包二',
            overBlessedPackAllCount: 9,
            blessedPackConfItems: [
              { confItemName: '20吋10幅 星芒轮毂', confItemPrice: 7000 },
              { confItemName: '岩灰 天然栓木饰条', confItemPrice: 3000 }
            ]
          },
          {
            blessedPackTitle: '福包三',
            overBlessedPackAllCount: 2,
            blessedPackConfItems: [
              { confItemName: '20吋5幅 星斗轮毂', confItemPrice: 6000 },
              { confItemName: '檀棕 天然桦木饰条', confItemPrice: 3000 }
            ]
          }, {
            blessedPackTitle: '福包四',
            overBlessedPackAllCount: 10,
            blessedPackConfItems: [
              { confItemName: '20吋5幅 星斗轮毂', confItemPrice: 6000 },
              { confItemName: '岩灰 天然栓木饰条', confItemPrice: 3000 }
            ]
          }
        ],
        [
          {
            blessedPackTitle: '福包一',
            overBlessedPackAllCount: 20,
            blessedPackConfItems: [
              { confItemName: '行政尊享全套装', confItemPrice: 43700 },
              { confItemName: '行政静音套装', confItemPrice: 2000 },
              { confItemName: '奥迪城市驾驶辅助套装', confItemPrice: 12000 },
              { confItemName: '冬暖套装', tips: '(标价¥3500)', confItemPrice: 1000 },
              { confItemName: '20吋10幅 星芒轮毂', confItemPrice: 1000 }
            ]
          },
          {
            blessedPackTitle: '福包二',
            overBlessedPackAllCount: 2,
            blessedPackConfItems: [
              { confItemName: '行政尊享全套装', confItemPrice: 43700 }
            ]
          },
          {
            blessedPackTitle: '福包三',
            overBlessedPackAllCount: 8,
            blessedPackConfItems: [
              { confItemName: '前排行政套装', confItemPrice: 27000 },
              { confItemName: '行政静音套装', confItemPrice: 2000 },
              { confItemName: '奥迪城市驾驶辅助套装', confItemPrice: 12000 },
              { confItemName: '20吋10幅 星芒轮毂', confItemPrice: 1000 }
            ]
          },
          {
            blessedPackTitle: '福包四',
            overBlessedPackAllCount: 6,
            blessedPackConfItems: [
              { confItemName: '前排行政套装', confItemPrice: 27000 },
              { confItemName: '行政静音套装', confItemPrice: 2000 },
              { confItemName: '冬暖套装', tips: '(标价¥3500)', confItemPrice: 1000 },
              { confItemName: '20吋10幅 星芒轮毂', confItemPrice: 1000 }
            ]
          }
        ]
      ]
      const data = blessedBags[luckyBagId]
      const list = data.map((i, x) => {
        i.totalPrice = i.blessedPackConfItems.reduce((a, b) => a + b.confItemPrice || 0, 0)
        this.sortArray(i.blessedPackConfItems, 'confItemPrice')
        return i
      })
      this.sortArray(list, 'totalPrice')
      list.forEach((i, x) => {
        i.blessedPackTitle = numberToUppercase(x + 1)
      })
      this.list = list
    },
    handleCheckBox() {
      this.checked = !!this.checked
    },
    handleCancelCheck() {
      this.warningPopShow = false
    },
    handleWarningConfirm() {
      this.warningPopShow = false
    },
    // 打开福袋弹窗
    async handleLuckyBagStart() {
      if (!this.checked) {
        if (this.warningStatus !== 1) {
          this.warningStatus = 1
        }
        this.warningPopShow = true
        this.warningTitle = '请阅读并勾选《上汽奥迪进取周岁福包活动规则》'
        this.warningStatusText = '好的'
        return
        // return Toast({
        //   className: 'toast-dark-mini toast-pos-middle',
        //   message: '请阅读并勾选《福包规则》'
        // })
      }
      this.handleLuckyBagOpen('will-open')
    },
    // 打开福袋
    async handleLuckyBagOpen(status = '') {
      if (this.luckyBagBtnStatus === 'opening') return
      if (this.luckyBagBtnStatus === 'opened' && status !== 'will-open') {
        const { params } = this
        this.$router.push({ name: 'lucky-bag-cars-details', query: params })
      }
      const opening = 'opening'
      const opened = 'opened'

      // 开盒失败 重新开启动画（标识）
      if (this.popLuckyBagOpen) {
        this.anim[opening].play()
      }
      const { data } = await getLuckyBagOpen({ blessedBagId: this.luckyBagId })
      // 开福袋积分不足
      if (data.data?.code === '01') {
        this.warningStatus = 2
        this.warningPopShow = true
        this.warningTitle = '您的奥金不足，暂时无法抽取福包' || data.data.message
        this.luckyBagBtnStatus = 'open'
        return
      }

      // 开福袋 => 已有大定订单
      if (data.data?.code === '02') {
        this.warningPopShow = true
        this.warningStatus = 1
        this.warningTitle = '您存在尚未完成的爱车订单，无法参加本次活动' || data.data.message
        this.luckyBagBtnStatus = 'open'
        return
      }

      this.popLuckyBagShow = true
      this.luckyBagBtnText = luckyBagStatus.opening
      this.luckyBagBtnStatus = opening

      if (data.data?.code === '00') {
        this.params.ccid = data.data.ccid
        this.params.luckyPackageId = data.data.blessedPackId

        delay(
          () => {
            this.luckyBagBtnText = luckyBagStatus.opened
            this.luckyBagBtnStatus = opened
          }, 2000
        )

        if (this.popLuckyBagOpen) {
          this.popLuckyBagOpen = false
        }
        return
      }

      // 宏任务(延迟)处理开启福袋异常业务
      delay(
        () => {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '福包开启失败'
          })
          this.popLuckyBagCloseable = true // 福袋开启失败 显示关闭弹窗按钮
          this.popLuckyBagOpen = true
          this.anim[opening].stop()
          this.luckyBagBtnText = luckyBagStatus.open
          this.luckyBagBtnStatus = 'open'
        }, 500
      )
    },
    handleAnimation(anim, type) {
      this.anim[type] = anim
    },
    sortArray: (data, key, order = 'desc') => data.sort((a, b) => (order === 'asc' ? a[key] - b[key] : b[key] - a[key]))
  }
}
</script>

<style lang="less">
.lucky-bag-wrapper {
  background-color: #fff;
  padding: 16px 16px 165px 16px;
  .warning-pop {
    width: 72%;
  }
  .warning-pop-box {
    padding-top: 30px;
    .h3 {
      font-size: 16px;
      font-weight: normal;
      text-align: center;
      margin:  18px 0 35px;
    }
  }
}
.lucky-bag-box {
  .tips {
    padding: 16px;
    font-size: 12px;
    color: #808080;
  }
  .lucky-bags-pop {
    overflow: visible;
    .van-popup__close-icon {
      top: -40px;
      right: 50%;
      transform: translateX(50%);
    }
  }
  .lottie-lucky-bags {
    padding: 0 25px 25px 25px;
    margin-top: -20px;
    .button {
      &.opening {
        background-color: #808080 !important;
        border-color: #808080 !important;
      }
    }
  }
  .lucky-bag-title {
    margin: 0;
    font-size: 16px;
    line-height: 160%;
  }
  .lucky-bag-list {
    margin-top: 33px;

    .bag-box {
      margin-top: 12px;
      &.gray {
        background-color: #F8F8F8;
        &.text {
          padding: 10px 16px;
        }
      }
      p {
        margin: 0;
        font-size: 14px;
        line-height: 26px;
      }
      &.gifts {
        text-align: center;
        padding: 45px 0 20px;
        .midd {
          margin: 0 8px 0 13px;
          .img {
            width: 21px;
            height: 22px;
          }
        }
        .left {
          .img {
            position: relative;
            top: 6px;
          }
        }
        .box {
          height: 80px;
          margin-bottom: 25px;
        }
      }
      .list {
        margin-bottom: 5px;
        padding: 14px 12px;
        background:linear-gradient(#efefef 0%,#fefefe 100%);
        box-shadow: 0 0 6px rgba(30, 30, 30, 0.08);
        .h4 {
          margin: 0 0 8px 0;
          font-size: 14px;
        }
        em {
          flex: 1;
          font-style: normal;
          .tips {
            padding: 0;
            margin-left: 5px;
          }
        }
        p{
          font-size: 14px;
          line-height: 200%;
          position: relative;
          &::before{
            content: '·';
            margin-right: 6px;
          }
        }
      }
      &.gray-text {
        color: #a4a4a4;
        line-height: 160%;
      }
    }
    &.thank-you-note {
      margin: 0;
      .bag-box {
        margin: 0;
      }
      h4 {
        font-size: 14px;
        margin: 0 0 10px 0;
      }
      p {
        color: #000;
        margin-bottom: 20px;
      }
      .note-footer {
        text-align: right;
      }
    }
  }
}
.protocol {
  padding-top: 15px;
  font-size: 12px;
  .van-checkbox__label {
    color: #808080;
    a {
      &:link, &:visited {
        color: #000;
      }
    }
  }

}

.fixed-bottom-box {
  position: fixed;
  left: 0;
  right: 0;
  bottom: -1px;
  padding: 25px 16px;
  background: #fff;
  box-shadow: -1px 0 5px rgba(30, 30, 30, 0.05);
}
</style>
