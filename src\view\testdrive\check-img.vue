<template>
  <div class="_container">
    <div class="prism-player" v-for="(item, index) in files" :key="index">
      <img :src="item" />
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import Vue from 'vue'
import storage from "../../utils/storage";


Vue.use(Toast)
export default {
  data() {
    return {
      files: [],
    }
  },
  async mounted() {
    this.appoId = this.$route.query.appoId;
    var model = storage.get("saveLongItem") || "{}";
    this.files = JSON.parse(model).files;
  },

  methods: {

  }
}
</script>

<style lang='less' scoped>
._container {

  /* padding: 0 16px;
    padding-bottom: 120px;
    display: flex;
    flex-flow: column;
    font-size: 16px;
    color: #000; */
  .prism-player {
    width: 100%;
  }

  img {
    width: 100%;
  }

  ._item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 33px;
  }
}
</style>
