/*
 * <AUTHOR> <PERSON>
 * @Date         : 2023-05-05 11:12:11
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-05-05 15:24:51
 * @FilePath     : \src\utils\weixin-js-sdk.js
 * @Descripttion :
 */
import request from '@/router/axios'
import wx from 'weixin-js-sdk'

const weiChatJsSdkConf = async (method = ['getLocation'], params = {}) => {
  const [url] = window.location.href.split('#') || ['']
  const { data: { data: wxd } } = await request({
    url: `https://audi-api.saic-audi.mobi/api-wap/audi-task/api/task/signature?url=${url}`,
    method: 'get',
    data: params
  }) || { data: { data: '' } }

  wx.config({
    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    appId: wxd.appId, // 必填，公众号的唯一标识
    timestamp: wxd.timestamp, // 必填，生成签名的时间戳
    nonceStr: wxd.nonceStr, // 必填，生成签名的随机串
    signature: wxd.signature, // 必填，签名
    jsApiList: [
      ...method
      // 'checkJsApi',
      // 'onMenuShareTimeline',
      // 'updateTimelineShareData',
      // 'onMenuShareAppMessage',
      // 'onMenuShareQQ',
      // 'onMenuShareWeibo',
      // 'translateVoice',
      // 'onMenuShareQZone',
      // 'hideMenuItems',
      // 'showMenuItems',
      // 'hideAllNonBaseMenuItem',
      // 'showAllNonBaseMenuItem',
      // 'updateAppMessageShareData',
      // 'translateVoice',
      // 'startRecord',
      // 'stopRecord',
      // 'onVoiceRecordEnd',
      // 'playVoice',
      // 'onVoicePlayEnd',
      // 'pauseVoice',
      // 'stopVoice',
      // 'uploadVoice',
      // 'downloadVoice',
      // 'chooseImage',
      // 'previewImage',
      // 'uploadImage',
      // 'downloadImage',
      // 'getNetworkType',
      // 'openLocation',
      // 'getLocation',
      // 'hideOptionMenu',
      // 'showOptionMenu',
      // 'closeWindow',
      // 'scanQRCode',
      // 'chooseWXPay',
      // 'openProductSpecificView',
      // 'addCard',
      // 'chooseCard',
      // 'openCard'
    ] // 必填，需要使用的JS接口列表
  })
}


export const minipLocation = async () => {
  weiChatJsSdkConf()
  wx.error((res) => {
    console.log('%c [ minipLocation wx.error ]-26', 'font-size:14px; background:#cf222e; color:#fff;', res)
  })
  return new Promise((resolve, reject) => {
    wx.ready(() => {
      wx.getLocation({
        type: 'gcj02',
        success: async (res) => {
          console.log('%c [ getLocation success ]-88', 'font-size:14px; background:#cf222e; color:#fff;', res)
          resolve(res)
        },
        fail: (e) => {
          console.log('%c [ getLocation error ]-89', 'font-size:14px; background:#cf222e; color:#fff;', e)
          resolve({}, e)
        }
      })
    })
  })
}

export const minipOpenLocation = async ({
  latitude, longitude, name = '', scale = 0
}) => {
  weiChatJsSdkConf(['openLocation'])
  wx.error((res) => {
    console.log('%c [ minipOpenLocation wx.error ]-26', 'font-size:14px; background:#cf222e; color:#fff;', res)
  })
  wx.openLocation({
    latitude: Number(latitude), // 纬度，浮点数，范围为90 ~ -90
    longitude: Number(longitude), // 经度，浮点数，范围为180 ~ -180。
    name, // 位置名
    scale: scale || 28, // 地图缩放级别,整型值,范围从1~28。默认为最大
    success: function (res) {
      console.log('%c [ success res.address ]-111', 'font-size:14px; background:#cf222e; color:#fff;')
    },
    fail(e) {
      console.log('%c [ fail e ]-114', 'font-size:14px; background:#cf222e; color:#fff;', e)
    }
  })
}
