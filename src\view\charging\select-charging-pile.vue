<template>
  <div class="container">
    <div>
      <div
        class="item-wrapper"
        @click="isSelectType = true"
      >
        <div style="display: flex; align-items: center">
          <img
            class="img-icon"
            style="padding-left: 16px"
            :src="isSelectType ? activeRadioIcon : inactiveRadioIcon"
          >
          <div style="margin-right: 26px; padding-left: 14px">
            <div
              class="c-font16"
              style=""
            >
            7.2kW充电桩
            </div>
            <div
              class="c-font14"
              style="color: #666; padding-top: 10px"
            >
              价格已包含
            </div>
          </div>
        </div>

        <div class="navgation-wrapper">
          <img
            style="width: 142px; height: 58px"
            :src="require('@/assets/img/icon-charging.png')"
          >
        </div>
      </div>

      <!-- <div
        class="item-wrapper"
        @click="isSelectType = false"
        style="margin-top: 16px"
      >
        <div style="display: flex; align-items: center">
          <img
            class="img-icon"
            style="padding-left: 14px"
            :src="!isSelectType ? activeRadioIcon : inactiveRadioIcon"
          />
          <div style="margin-right: 26px; padding-left: 14px">
            <div class="c-font16" style="">11KW充电桩</div>
            <div class="c-font14" style="color: #666; padding-top: 10px">
              需额外支付费用
            </div>
          </div>
        </div>
        <div class="navgation-wrapper">
          <img
            style="width: 142px; height: 58px"
            :src="require('@/assets/img/icon-charging.png')"
          />
        </div>
      </div> -->
    </div>

    <div>
      <div class="bottom_style">
        <div class="checkbox_style">
          <van-checkbox
            disabled="disabled"
            @click="ischecked = !ischecked"
          >
            <img
              class="checkbox_button"
              slot="icon"
              :src="ischecked ? activeIcon : inactiveIcon"
            >
            <span style="color: #666666; font-size: 12px"> 我已阅读并同意</span>
            <span
              @click="onInstallServiceGuide"
              style="color: #000; font-size: 12px"
            >《安装服务指引》
            </span>
          </van-checkbox>
        </div>
        <div class="text">
          <p class=MsoNormal><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
font-size:10.5000pt;mso-font-kerning:1.0000pt;">
              <font face="等线">*为具备安装条件的客户免费提供7</font>
            </span><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">.2</span><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
              <font face="等线">kW充电墙盒及</font>
            </span><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">60</span><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
              <font face="等线">米内的基础安装服务。</font>
            </span></p>
          <p class=MsoNormal><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
              <font face="等线">如需选择奥迪品牌充电墙盒</font>
            </span><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;"></span><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
              <font face="等线">权益，</font>
            </span><b style="mso-bidi-font-weight:normal"><u><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      mso-ansi-font-weight:bold;text-decoration:underline;text-underline:single;
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
                  <font face="等线">请务必在购车后一年内（自交车确认之日起</font>
                </span></u></b><b style="mso-bidi-font-weight:normal"><u><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      mso-ansi-font-weight:bold;text-decoration:underline;text-underline:single;
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">365天）</span></u></b><b style="mso-bidi-font-weight:normal"><u><span
                  style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      mso-ansi-font-weight:bold;text-decoration:underline;text-underline:single;
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
                  <font face="等线">完成申请与安装。若在该期限内因您无法满足安装条件导致无法安装充电墙盒，则视为您</font>
                </span></u></b><b style="mso-bidi-font-weight:normal"><u><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      mso-ansi-font-weight:bold;text-decoration:underline;text-underline:single;
      font-size:10.5000pt;mso-font-kerning:1.0000pt;"><span class="msoIns"><ins cite="mailto:Chen Wenyi (SVW ML-2)"
                      datetime="2023-08-23T09:56">
                      <font face="等线">放弃充电墙盒权益并</font>
                    </ins></span></span></u></b><b style="mso-bidi-font-weight:normal"><u><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      mso-ansi-font-weight:bold;text-decoration:underline;text-underline:single;
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
                  <font face="等线">选择价值</font>
                </span></u></b><b style="mso-bidi-font-weight:normal"><u><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      mso-ansi-font-weight:bold;text-decoration:underline;text-underline:single;
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">5000元的上汽奥迪APP积分权益</span></u></b><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
              <font face="等线">。</font>
            </span></p>
          <p class=MsoNormal><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
              <font face="等线">充电墙盒产品受国家</font>
              <font face="等线">/地方相关法律法规的强制要求、技术进步、产品更新和供货商调整等影响，产品型号、外观、颜色、功能等与实际不符时，以实际提供为准。</font>
            </span></p>
          <p class=MsoNormal><span style="mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';
      font-size:10.5000pt;mso-font-kerning:1.0000pt;">
              <font face="等线">充电墙盒产品的具体情况可咨询奥迪管家确认。</font>
            </span></p>
        </div>
        <!-- <p
          style="color: #000; font-size: 12px;margin-right: 16px;margin-left: 16px;"
          v-html="`*为具备安装条件的客户免费提供7.2KW充电墙盒及60米内的基础安装服务。<br/>
                如需选择充电权益，请务必在购车后一年内（自交车确认之日起365天）完成申请与安装。若在该期限内因您无法满足安装条件导致无法安装充电墙盒，则视为您放弃充电墙盒权益并选择价值5000元的上汽奥迪APP积分权益。<br/>
                充电墙盒产品受国家/地方相关法律法规的强制要求、技术进步、产品更新和供货商调整等影响，产品型号、外观、颜色、功能等与实际不符时，以实际提供为准。<br/>
                充电墙盒产品的具体情况可咨询你的奥迪管家确认。`"
        /> -->

        <div style="" />
        <div class="btn-delete-wrapper">
          <AudiButton
            @click="onSubmit"
            :text="'确认'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  RadioGroup, Radio, Field, Checkbox, CheckboxGroup
} from 'vant'
import AudiButton from '@/components/audi-button'
import { callNative } from '@/utils'
import { postCreateChargingPile } from '@/api/api'

Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Field).use(CheckboxGroup).use(Checkbox)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      activeRadioIcon: require('../../assets/img/radio_checked.png'),
      inactiveRadioIcon: require('../../assets/img/radio_normal.png'),
      ischecked: false,
      isSelectType: true,
      orderId: '',
      radioType: 1
    }
  },

  async mounted() {
    const { orderId, radioType } = this.$route.query
    orderId && (this.orderId = orderId)
    orderId && (this.radioType = +radioType)
  },

  methods: {
    onInstallServiceGuide() {
      this.$router.push({
        path: '/charging/install-service-guide',
        query: { type: 0 }
      })
    },
    // 确认
    onSubmit() {
      if (!this.ischecked) {
        // 请勾选
        callNative('toast', {
          type: 'fail',
          message: '请阅读安装服务指引提示'
        })
        return
      }
      if (this.isSelectType) {
        // 7KW
        this.postSubmit('7KW')
      } else {
        // 11KW
        this.postSubmit('11KW')
      }
    },
    async postSubmit(powerType) {
      const param = {
        orderId: this.orderId,
        powerType: powerType,
        equityType: this.radioType
      }
      this.$store.commit('showLoading')
      const { data } = await postCreateChargingPile(param)
      this.$store.commit('hideLoading')
      if (data.code === '00') {
        const { orderId } = this
        this.$router.push({
          path: '/charging/charging-pile-success',
          query: { type: 0, ...(orderId ? { from: 'install-info', name: 'money-detail', param: `'orderId=${orderId}'` } : {}) }
        })
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.container {
  padding: 16px;
  background: #f2f2f2;
  height: 100%;
}

.flex1 {
  flex: 1;
}

.item-wrapper {
  .c-flex-between;
  align-items: center;
  background: #fff;
  padding-right: 16px;
  height: 90px;
}

.navgation-wrapper {
  text-align: center;
}

.img-icon {
  height: 20px;
  width: 20px;
  padding-bottom: 8px;
}

.bottom_style {
  width: 100%;
  background: #f2f2f2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  border-top: 2px #f2f2f2 solid;
  padding-top: 16px;
  padding-bottom: 39px;
  left: 0px;
  .checkbox_button {
    margin-left: 16px;
    margin-bottom: 5px;
    width: 18px;
    height: 18px;
    color: #000;
  }
  .checkbox_style {
    display: flex;
    // height: 20px;
    justify-content: space-between;
    font-family: "Audi-Normal";
    color: #999999;
    width: 100%;
    font-size: 16px;
    span {
      font-size: 16px;
      color: #000;
      font-family: "Audi-Normal";
    }
    .checkbox_styleRight {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #000;
      margin-right: 16px;
    }
  }
  .text {
    padding: 16px;
    p {
      margin: 0;
    }
  }
}
.btn-delete-wrapper {
  margin: 0 16px;
}
</style>
