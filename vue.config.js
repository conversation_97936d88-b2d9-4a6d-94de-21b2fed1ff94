module.exports = {
  // outputDir: 'dist',
  publicPath: process.env.VUE_APP_PUBLICPATH,
  assetsDir: 'public',
  outputDir: 'dist_orderh5',
  lintOnSave: false, // 是否开启eslint
  productionSourceMap: false,
  devServer: {
    // 环境配置
    host: '0.0.0.0',
    // public:'**********:8080',
    port: 8080,
    https: false,
    hotOnly: false,
    disableHostCheck: true,
    open: true, // 配置自动启动浏览器
    proxy: {
      '/dev-api': {
        target: 'https://devaudi-api.saic-audi.mobi',
        changeOrigin: true,
        pathRewrite: { '^/dev-api': '' },
        headers: {
          Referer: ''
        }
      }
    }
  },
  chainWebpack: (config) => {
    config.optimization.splitChunks({
      chunks: 'async'
    })
    // 删除 prefetch 插件
    config.plugins.delete('prefetch')
  },
  configureWebpack: (config) => {
    config.devtool = process.env.VUE_APP_ENV === 'prod' ? undefined : 'source-map'
    if (process.env.NODE_ENV === 'production') {
      // 启用Terser插件进行代码压缩
      config.optimization.minimizer[0].options.terserOptions.compress = {
        drop_console: true // 移除所有的console.log语句
      }
    }
  }
}
