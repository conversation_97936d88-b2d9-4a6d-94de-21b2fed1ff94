import wx from 'weixin-js-sdk'
import axios from 'axios'
import { getWXConfig } from '../api/api'
import { getLocation } from '@/utils'

async function wxConf(url) {
  // const { data } = await getWXConfig({
  //   url
  // })
  // await getWXConfig({
  //   url: url,
  // }).then((data) => {
  //   if (data.code != '200') {
  //     throw '微信config出错'
  //   }
  const data = await axios.get(`https://audi-api.saic-audi.mobi/api-wap/audi-task/api/task/signature?url=${url}`)
  console.log(data.data)
  wx.config({
    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    appId: data.data.data.appId, // 必填，公众号的唯一标识
    timestamp: data.data.data.timestamp, // 必填，生成签名的时间戳
    nonceStr: data.data.data.nonceStr, // 必填，生成签名的随机串
    signature: data.data.data.signature, // 必填，签名
    jsApiList: [
      'checkJsApi',
      'onMenuShareTimeline',
      'updateTimelineShareData',
      'onMenuShareAppMessage',
      'onMenuShareQQ',
      'onMenuShareWeibo',
      'translateVoice',
      'onMenuShareQZone',
      'hideMenuItems',
      'showMenuItems',
      'hideAllNonBaseMenuItem',
      'showAllNonBaseMenuItem',
      'updateAppMessageShareData',
      'translateVoice',
      'startRecord',
      'stopRecord',
      'onVoiceRecordEnd',
      'playVoice',
      'onVoicePlayEnd',
      'pauseVoice',
      'stopVoice',
      'uploadVoice',
      'downloadVoice',
      'chooseImage',
      'previewImage',
      'uploadImage',
      'downloadImage',
      'getNetworkType',
      'openLocation',
      'getLocation',
      'hideOptionMenu',
      'showOptionMenu',
      'closeWindow',
      'scanQRCode',
      'chooseWXPay',
      'openProductSpecificView',
      'addCard',
      'chooseCard',
      'openCard'
    ] // 必填，需要使用的JS接口列表
  })
}

async function wxShare(detail) {
  wx.ready(() => {
    // 分享到朋友圈接口
    wx.onMenuShareTimeline({
      title: detail.title, // 分享标题
      link: window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      imgUrl: `${detail.imgUrl}?x-oss-process=image/resize,w_200,h_200,q_100`, // 分享图标
      success: function () {
        // 设置成功
      }
    })

    // 发送给朋友接口
    wx.onMenuShareAppMessage({
      title: detail.title, // 分享标题
      desc: detail.desc, // 分享描述
      link: window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      imgUrl: `${detail.imgUrl}?x-oss-process=image/resize,w_200,h_200,q_100`, // 分享图标
      success: function () {
        // 设置成功
      }
    })
  })
}

function wxLocation(callback) {
  wxConf(window.location.href.split('#')[0])

  wx.error((res) => {
    console.error('wxconfig 失败:', res)
  })
  wx.ready(() => {
    wx.getLocation({
      type: 'gcj02',
      success: async (res) => {
        const location = `${res.latitude},${res.longitude}`
        const cityCode = await getLocation([res.longitude, res.latitude])

        // 从citycode 获取城市 添加到页面中
        const ret = {
          location: location,
          city: cityCode
        }
        console.log('getLocation success', res)
        callback(ret)
      },
      fail: (e) => {
        console.error('getLocation error', e)
        callback({
          location: ','
        })
      }
    })
  })
}

function openLocation({ lat, long, des }, callback) {
  wxConf(window.location.href.split('#')[0])

  wx.error((res) => {
    console.error('wxconfig 失败:', res)
  })
  wx.ready(() => {
    wx.openLocation({
      latitude: lat / 1,
      longitude: long / 1,
      name: des,
      scale: 18,
      success: (res) => callback(res),
      fail: (err) => callback(err)
    })
  })
}

export {
  wxConf, wxShare, wxLocation, openLocation
}
