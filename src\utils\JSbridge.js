/* eslint-disable */
/**
   * 自定义错误类
   * @param {*} code 错误码
   * @param {*} message 错误描述信息
   */
function CustomError(code, message, fileName, lineNumber) {
  const instance = new Error(message, fileName, lineNumber)
  instance.code = code
  if (Error.captureStackTrace) {
    Error.captureStackTrace(instance, CustomError)
  }
  return instance
}

CustomError.prototype = Object.create(Error.prototype, {
  constructor: {
    value: Error,
    enumerable: false,
    writable: true,
    configurable: true
  }
})
// 错误码常量
CustomError.Code = {
  CALL_PARAM_ERROR: 'E0001', // callHandler参数错误
  OS_ERROR: 'E0002', // 系统不支持
  NATIVE_METHOD_ERROR: 'E0003', // native方法不存在
  NATIVE_RESULT_ERROR: 'E0004' // native返回结果异常
}

let uniqueId = 1
const responseCallbacks = {}
const ua = window.navigator.userAgent
const isAndroid = ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1
const isiOS = !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)

/**
   * 供native调用，传递执行结果
   * @param {*} messageJson native返回的结果
   * messageJson 的格式为
   * {
   *    error: '', // 错误描述
   *    data: {}, // 响应数据
   *    callbackId: '', 对应回调函数id
   * }
   */
function dispatchMessageFromNative(messageJson) {
  setTimeout(() => {
    try {
      var message = JSON.parse(messageJson)
    } catch (e) {
      throw new CustomError(CustomError.Code.NATIVE_RESULT_ERROR, 'native返回结果是json字符串')
    }
    const responseCallBackId = message.callbackId
    if (responseCallBackId) {
      const responseCallback = responseCallbacks[responseCallBackId]
      if (responseCallback) {
        responseCallback(message.error, message.data)
        delete responseCallbacks[responseCallBackId]
      } else {
        throw new CustomError(CustomError.Code.NATIVE_RESULT_ERROR, 'responseCallback回调方法不存在')
      }
    } else {
      throw new CustomError(CustomError.Code.NATIVE_RESULT_ERROR, 'callbackId不存在')
    }
  })
}

/**
   * 供JavaScript调用，执行指定native方法
   * @param {*} handlerName native方法名
   * @param {*} data native方法需要的参数
   * @param {*} responseCallback 匿名回调函数
   */
function callHandler(handlerName, data, responseCallback) {
  if (!responseCallback && typeof data !== 'function') {
    responseCallback = data
    data = null
    throw new CustomError(CustomError.Code.CALL_PARAM_ERROR, 'responseCallback是函数类型')
  }

  if (responseCallback && typeof responseCallback !== 'function') {
    throw new CustomError(CustomError.Code.CALL_PARAM_ERROR, 'responseCallback是函数类型')
  }

  if (!(typeof data === 'object' && Object.prototype.toString.call(data) === '[object Object]')) {
    const e = new CustomError(CustomError.Code.CALL_PARAM_ERROR, 'data是对象类型')
    throw e
  }

  if (typeof handlerName !== 'string') {
    throw new CustomError(CustomError.Code.CALL_PARAM_ERROR, 'handlerName是字符串类型')
  }

  const message = {
    data: '',
    callbackId: ''
  }
  if (data) {
    message.data = data
  }
  if (responseCallback) {
    const callbackId = `cb_${uniqueId++}_${new Date().getTime()}`
    responseCallbacks[callbackId] = responseCallback
    message.callbackId = callbackId
  }

  const messageStr = JSON.stringify(message)
  try {
    if (isAndroid) {
      window.oneapp[handlerName](messageStr)
    } else if (isiOS) {
      window.webkit.messageHandlers[handlerName].postMessage(messageStr)
    } else {
      throw new CustomError(CustomError.Code.OS_ERROR, '当前操作系统不被支持')
    }
  } catch (e) {
    throw new CustomError(CustomError.Code.NATIVE_METHOD_ERROR, `native方法${handlerName}不存在`)
  }
}

const webViewJavascriptBridge = {
  callHandler: callHandler,
  dispatchMessageFromNative: dispatchMessageFromNative
}
window.webViewJavascriptBridge = webViewJavascriptBridge

export default window
