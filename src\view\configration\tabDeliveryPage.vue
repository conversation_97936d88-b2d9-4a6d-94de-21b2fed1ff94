<template>
  <div class="container">
    <div
      v-for="({ title, des, tag, img }, index) in recommArr"
      :key="index"
      class="box"
    >
      <div
        style="
          display: flex;
          justify-content: flex-start;
          padding: 18px 0;
          margin-bottom: 24px;
        "
      >
        <div style="width: 104px; margin: 10px">
          <img style="width: 104px; height: 104px" :src="img[1]" alt="" />
        </div>
        <div style="padding-top: 28px">
          <div
            @click="toConfigration(title, index)"
            style="font-size: 16px; font-weight: bold; color: #000000"
          >
            {{ title }}
            <van-icon style="top: 2px" name="arrow" />
          </div>
          <div style="padding-top: 8px; font-size: 12px; color: #000000">
            {{ des }}
          </div>
          <div style="padding-top: 5px">
            <van-tag
              v-for="(item, i) in tag"
              :key="i"
              plain
              type="primary"
              color="#999"
              text-color="#333"
              style="margin-right: 5px"
            >
              {{ item }}
            </van-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Tab, Tabs, Toast, Tag } from 'vant'
import { getQueryParam } from '@/utils'

Vue.use(Tag)
export default {
  data() {
    return {
      optionDesc: '',
      recommArr: [
        {
          id: 0,
          title: '快速交付',
          des: '当季热门装备，即享快速提车',
          tag: ['预计4周快速交付', '定金20,000元', '限时优惠'],
          img: [
            require('@/assets/car/recom/3.png'),
            require('@/assets/car/recom/1.png'),
            require('@/assets/car/recom/5.png')
          ]
        },
        {
          id: 1,
          title: '定制交付',
          des: '百万种个性化组合，全方位私人定制',
          tag: ['预计3个月交付'],
          img: [
            require('@/assets/car/recom/4.png'),
            require('@/assets/car/recom/22.png'),
            require('@/assets/car/recom/6.png')
          ]
        }
      ]
    }
  },
  created() {},
  methods: {
    toConfigration({ id }, index) {
      // if (!index) {
      //     this.$storage.setPlus('semi-definite', '个性定制')
      //     let minip = this.$route.query.env == 'minip'      // debugger
      //     this.$storage.setPlus('entryPoint', minip ? 'MINIP_MEASURE' : 'ONEAPP_MEASURE')
      //     this.$router.push({
      //         path: '/configration',
      //         query: {
      //             tabIndex: '0',
      //             idx: 1
      //         }
      //     })
      // }

      // index && this.$router.push({
      //     path: '/hot-recommended',
      //     query: { idx: 1 }
      // })

      /**
       * ---------更新
       */

      const env = getQueryParam('env')

      // 快速交付(个性订制)
      if (index === 0) {
        this.$storage.setPlus('semi-definite', '个性定制')
        this.$storage.setPlus(
          'entryPoint',
          env === 'minip' ? 'MINIP_MEASURE' : 'ONEAPP_MEASURE'
        )
      }

      // 订制交付(私人高定)
      if (index === 1) {
        this.$storage.setPlus('semi-definite', '私人高定')
      }

      this.$router.push({
        path: '/configration',
        query: { idx: 1 }
      })
    }
  }
}
</script>

<style scoped lang="less">
.container {
  padding: 16px;
}

.box {
  margin: 10px;
  margin-bottom: 0px;
  background: url('../../assets/car/recom/bg.png') no-repeat;
  background-size: 100% 100%;
}
</style>
