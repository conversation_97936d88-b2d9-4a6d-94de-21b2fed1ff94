<template>
  <div
    class="userAgreement"
    @touchstart="start($event)"
    @touchmove="move($event)"
    @touchend="end($event)"
  >
      <ul>
        <li>
          <p class="list">
            本《购车定金协议条款》（下称“[本协议]”），由购买汽车用户（下称“您”或“购车用户”）与上海上汽大众汽车销售有限公司或其关联公司（下称“上汽大众”）及授权代理商（下称“代理商”）达成一致意见后签署。
          </p>
        </li>
        <li>
          <div class="fontbold">一、同意购买</div>
          <div class="list">
            1.1上汽大众作为销售主体，将按照您在上汽奥迪官方平台客户个人订单页中（下称“个人订单页”）所选车辆的总价及配置等其他相关信息，向您出售一辆上汽大众奥迪品牌汽车（下称“上汽奥迪汽车”或“车辆”）。该车辆由代理商协助您与上汽大众完成交易（包括但不限于履行车辆交付、代为收取尾款、代为开具机动车销售统一发票等）。
          </div>
          <div class="list">
            <span class="bold">您签署[本协议]代表您确认了您的购买决定及上述车辆配置，</span> 您需就购买上汽奥迪汽车向上汽大众支付个人订单页中所列明的实际购车总价（下称“实际购车总价”），即个人订单页中所列明的定金（下称“定金”）和尾款（下称“尾款”）的总额，车款将由代理商代为收取。
          </div>
          <div class="list">
            1.2<span class="bold">您同意在支付定金（定金金额以个人订单页面显示的定金金额为准）后3日内与上汽大众、代理商签署《汽车购买协议》，如因您的问题（包括但不限于您逾期未予签署《汽车购买协议》、您的购车意愿问题、未申请汽车消费信贷、汽车消费信贷被拒、购车资格问题等）导致车辆交易无法完成，上汽大众、代理商有权单方面终止[本协议]而无需承担任何违约责任，且您已支付的定金将不予退还。</span>
          </div>
        </li>
        <li>
          <div class="fontbold">二、付款</div>
          <div class="list">
            2.1详见《车辆配置清单》中的订单信息。<span class="bold">当您签署[本协议]时，您已确认了您的购买决定以及车辆的配置信息。</span>您需在签署[本协议]时支付人民币             元的<span class="bold">不可退还的定金</span>。您所支付的定金自动构成实际购车总价的一部分。此外，<span class="bold">>您需在验收车辆的当日，以现金存入或借记卡转账方式向代理商支付尾款。</span>
          </div>
          <div class="list">
            2.2车辆到店后七天内验车并向上汽大众完成尾款支付，支付尾款的方式包括以下一项：（a）一次性付款：向代理商一次性付清尾款；（b）贷款付款：您可向上汽大众推荐的金融机构申请汽车消费贷款。<span class="bold">如您由于汽车消费贷款申请被拒而自主提出了车辆的退定申请，经由上汽大众核实及批准后【本协议】可予解除但不可退还定金。</span>
          </div>
          <div class="list">
            <span class="bold">您在此明确知悉并同意：</span>
          </div>
          <div class="list">
            <span class="bold">为协助您办理汽车贷款，上汽大众可根据金融机构的要求，将您的《汽车购买协议》与之共享。同时，上汽大众可以在不事先另行获得您书面同意的情况下向金融机构披露下述信息：（1）如果您逾期未能偿还购车贷款，经金融机构要求，上汽大众可以向金融机构提供您的保养保修记录及其他相关记录，以帮助金融机构确定您的联系信息；以及（2）如果您逾期偿还购车贷款超过30日，经金融机构书面要求，上汽大众可以向金融机构提供上汽奥迪汽车的实时定位信息、路径轨迹信息及其它信息，直至上汽奥迪汽车相关的购车贷款已经全额清偿。</span>
          </div>
          <div class="list">
            2.3<span class="bold">支付日期以代理商代为收到尾款日期为准。</span>
          </div>
          <div class="list">
            2.4您所支付的所有费用（代付代缴除外），代理商将根据法律法规规定提供相应的发票，若遇到国家税率（包括但不限于增值税）调整，代理商将按照当时国家政策向您提供发票。
          </div>
        </li>
        <li>
          <div class="fontbold">三、配置确认</div>
          <div class="list">
            3.1车辆配置详见个人订单页所列明的配置详情。
          </div>
          <div class="list">
            3.2<span class="bold">定金支付后，您可以继续修改车型配置方案。《汽车购买协议》签署后，您将不可更改车型配置方案。</span>
          </div>
        </li>

        <li>
          <div class="fontbold">四、交付</div>
          <div class="list">
            4.1您可在上汽奥迪APP查询预计交付时间（该时间仅供参考，实际交付时间可能根据车辆配置、提车城市等因素提前或延后）。<span class="bold">代理商将在车辆到店后，与您沟通车辆的验收和尾款支付时间。代理商将明确车辆的验收期限与地点等信息，您需在前述期限与地点验收车辆。您支付尾款的行为视为对车辆状态已确认。</span>为顺利完成车辆的验收与交付，您还需提供交付通知中所载明的相关信息与文件。
          </div>
          <div class="list">
            4.2如您因特殊情况需要延期验收车辆的，请您及时联系上汽大众/代理商为您安排延期交付事宜，上汽大众/代理商可以给予您额外7日的期限供您安排验收事宜。<span class="bold">请您理解，如您未能在相关期限内完成车辆的验收并支付尾款，上汽大众/代理商有权采取以下一项或多项措施：（a）为您保留车辆，并要求您按每逾期一天应付上汽大众/代理商剩余款项的千分之三计算，支付由于车辆额外运输、仓储等产生的必要合理费用；（b）将车辆另行销售给其他购车用户，但在您要求之时，可以立即为您重新安排车辆的生产；（c）立即或在任何其他时候（即使是在上汽大众已经采取相关措施的情形下）以提前书面通知您的方式单方面终止[本协议]而无需承担任何违约责任，且已收取的定金将不予退还；以及（d）法律法规允许上汽大众/代理商采取的其他措施。若由于不可抗力导致无法按时提车或交付，一方应立即书面通知另一方，各方应友好协商约定更新的日期。</span>
          </div>
          <div class="list">
            4.3代理商保证验收/交付的车辆已经过售前的调试、检验和清洁，符合上汽大众的相关质量标准，<span class="bold">不改变车辆的出厂状态，即不改动或改装车辆，不添加任何其它标记、标识。您同意在验收车辆时完成对汽车的验收，且一旦您签署《交车确认书》即代表您认可代理商交付的汽车之数量和质量均符合协议要求，交车确认完成之时起，您对车辆将承担全部风险，包括因不当使用车辆而造成的损坏和/或损害。</span>
          </div>
          <div class="list">
            4.4代理商将在您签署《交车确认书》且收到尾款后立即向您交付车辆，并随同车辆一并向您交付与之相关的《产品合格证》、《家用汽车产品三包凭证》、《使用维护说明书》等随车交付文件（下称“随车文件”）。
          </div>
          <div class="list">
            4.5签署《交车确认书》即代表您同意提供或由我们收集所交付车辆的车辆信息及证件（车型、车系、购车日期、上牌日期、行驶证、发票等）。
          </div>
          <div class="list">
            4.6<span class="bold">若您无法亲自完成车辆验收交付，您可以选择授权代理商为您提供上门交付服务。若您选择了代理商的上门交付服务，即视为您委托代理商作为您的代表为您配送车辆，运输过程中的风险事宜（若有的话）由您与代理商自行处理。配送前，您需完成车辆远程验收并签署《交车确认书》代表您认可交付的汽车之数量和质量均已符合《汽车购买协议》的要求。</span>
          </div>
        </li>
        <li>
          <div class="fontbold">五、补贴约定</div>
          <div class="list">
            5.1<span class="bold">鉴于您所购买车辆享有的政府补贴（下称“购车补贴“）（如有）已由上汽大众先行为您垫付（即您所支付的实际购车总价为已扣除购车补贴后的结款），为便于上汽大众能够从相关主管部门获得该等补贴或保障上汽大众在无法获得该等补贴时权益不受损失，您同意按照上汽大众的不时要求采取必要的行动以及提供必要的信息和文件。您承诺，如因您拒绝提供必要的信息和文件，或实施了其他不当行为，导致您不符合享受补贴的政策要求，或上汽大众不符合补贴申领的政策要求，您将按照上汽大众要求的期限和方式向上汽大众返还上述垫付的购车补贴金额。</span>
          </div>
          <div class="list">
            5.2您知晓个人用户、非个人用户购买车辆的申请补贴的要求。
          </div>
        </li>
        <li>
          <div class="fontbold">六、信息收集及保护</div>
          <div class="list">
            <span class="bold">您确认您已经完全阅读并理解以下内容：</span>
          </div>
          <div class="list">
            <span class="bold">信息收集及保护需符合上汽大众奥迪品牌《隐私政策》</span>
          </div>
          <div class="list">
            <span class="bold">（详见https://audi-embedded-wap.saic-audi.mobi/download/yinsi.html ）</span>
          </div>
          <div class="list">
            <span class="bold">及上汽大众奥迪品牌《用户协议》</span>
          </div>
          <div class="list">
            <span class="bold">（详见https://audi-embedded-wap.saic-audi.mobi/download/xieyi.html）的规定。</span>
          </div>
          <div class="list">
            <span class="fontbold">请您知悉，在您购买上汽大众奥迪品牌车辆后，为了向您提供相关车联网服务及售后服务支持，制造商需要将您的车辆识别码（VIN）传输至境外接收方。关于境外接收方处理您的车辆识别码（VIN）、您向其行使相关个人信息权利的更多详细内容，请见品牌隐私政策个人信息跨境提供专章。</span>
          </div>
        </li>
        <li>
          <div class="fontbold">七、不可抗力</div>
          <div class="list">
            因不可抗力致使[本协议]一方不能履行的，则根据不可抗力的影响部分或全部免除其责任。但是，该方因不可抗力不能履行[本协议]，负有及时通知和10天内提供证明的责任。在迟延履行后发生不可抗力的，不能免除责任。
          </div>
        </li>
        <li>
          <div class="fontbold">八、违约责任</div>
          <div class="list">
            <span class="bold">[本协议]任何一方违约，违约方应赔偿守约方的实际经济损失，除非[本协议]另有约定。</span>
          </div>
        </li>
        <li>
          <div class="fontbold">九、适用法律</div>
          <div class="list">
            9.1[本协议]应受中华人民共和国法律管辖并据其进行解释。
          </div>
          <div class="list">
            9.2因[本协议]引起的以及与[本协议]有关的一切争议，首先应由双方通过友好协商解决。<span class="bold">如果双方未能通过协商解决该等争议，则任何一方有权向上汽大众所在地的有管辖权的人民法院提起诉讼解决。</span>
          </div>
        </li>
        <li>
          <div class="fontbold">十、各方约定</div>
          <div class="list">
            10.1<span class="bold">您和上汽大众及代理商三方申明，对[本协议]项下各条款内容已经仔细阅读并表示理解，保证履行。有关车辆的任何广告、宣传单张、推介资料、或其他媒体形式的信息仅供您作购车的参考，具体应以[本协议]的约定和交付的实车为准。</span>
          </div>
          <div class="list">
            10.2<span class="bold">您保证，您是所购车辆的最终用户，您不会将车辆用于有损车辆品牌形象的活动及行为，所购车辆仅在中国使用，不再出口至其它国家或地区。</span>
          </div>
          <div class="list">
            10.3[本协议]在收到您支付的定金后正式生效。
          </div>
          <div class="list">
            10.4<span class="bold">您确认，对上汽大众的上述告知和提醒已充分理解并自愿购买车辆。</span>
          </div>
        </li>
      </ul>
    <div
      class="btn-warp"
      v-if="read === '0'"
    >
      <div
        class="buttons tsbtn"
        v-if="time > 0"
        @click="confim"
      >
        还需阅读{{ time }}秒
      </div>
      <div
        class="buttons"
        v-else
        @click="confim"
      >
        我已确认
      </div>
  </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import { Toast, Checkbox } from 'vant'

Vue.use(Toast).use(Checkbox)
export default {
  data() {
    return {
      time: 5,
      timer: null,
      checkbox: true,
      isbuyType: true,
      read: '0'
    }
  },
  methods: {
    confim() {
      if (this.time > 0) {
        Toast({
          message: '需仔细阅读5秒',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      } else {
        this.$router.back()
      }
    },
    getAngle(angx, angy) {
      return (Math.atan2(angy, angx) * 180) / Math.PI
    },
    getDirection(startx, starty, endx, endy) {
      const angx = endx - startx
      const angy = endy - starty
      let result = 0 // 默认标记没有滑动
      // 如果滑动距离太短
      if (Math.abs(angx) < 2 && Math.abs(angy) < 2) {
        return result
      }
      const angle = this.getAngle(angx, angy)
      console.log(angle)
      if (angle >= -135 && angle <= -45) {
        result = 1 // 向上
      } else if (angle > 45 && angle < 135) {
        result = 2 // 向下
      } else if (
        (angle >= 170 && angle <= 180)
      ) {
        result = 3 // 向左
      } else if (angle >= -45 && angle <= 45) {
        result = 4 // 向右
      }
      return result
    },
    start(event) {
      const tabbarRef = this.$refs.tabbarRef
      const touchS = event.targetTouches[0] // touches数组对象获得屏幕上所有的touch，取第一个touch
      this.startPos = {
        x: touchS.pageX,
        y: touchS.pageY,
        time: new Date()
      } // 取第一个touch的坐标值
    },
    move(event) {
      // 当屏幕有多个touch或者页面被缩放过，就不执行move操作
    },
    end(event) {
      const touchE = event.changedTouches[0]
      this.endPos = {
        x: touchE.pageX,
        y: touchE.pageY,
        timeStemp: new Date()
      }
      const direction = this.getDirection(
        this.startPos.x,
        this.startPos.y,
        this.endPos.x,
        this.endPos.y
      )
      console.log('direction', direction)
      if (direction === 3) {
        // 左滑
        const param = {
          isGoBack: false
        }
        if (this.time > 0) {
          Toast({
            message: '需仔细阅读5秒',
            className: 'toast-dark-mini toast-pos-middle',
            forbidClick: true,
            duration: 800
          })
          return false
        }
        param.isGoBack = true
        if (this.$route.query.status) {
          this.$router.push({
            path: '/order/new-money-detail',
            query: {
              orderId: this.$route.query.orderId || ''
            }
          })
        } else {
          this.$router.push({
            path: '/order/detail',
            query: {
              formDetail: true,
              orderId: this.$route.query.orderId || '',
              skuid: this.$route.query.skuid,
              ccid: this.$route.query?.ccid,
              carCustomId: this.$route.query?.carCustomId
            }
          })
        }

        // this.bridge.callHandler("isOnKeyDown", param, (err, res) => {
        //   if (err) {
        //     console.log("isOnKeyDownError=", err);
        //   }
        //   console.log("isOnKeyDownSuccess=", res);
        // });
      } else {
        return false
      }
    }
  },
  computed: {
    ...mapState({
      carSeries: (state) => state.carSeries
    })
  },
  mounted() {
    this.read = this.$route.query.read || '0'
    if (+this.$route.query.buyType === 1) {
      this.isbuyType = true
    } else {
      this.isbuyType = false
    }

    window.document.documentElement.scrollTop = 0
    this.$store.commit('allowBack', this.read === '1' ? '0' : '1')
    console.log(this.$store.state.allowBackVal)
    this.timer = setInterval(() => {
      if (this.time > 0) {
        this.time--
      } else {
        this.$store.commit('allowBack', '0')
      }
    }, 1000)
  },

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="less">
  // @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/buttons.less");

  .btn-warp {
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 100%;
    background: #fff;
    padding: 10px 17px 30px 17px;
    box-sizing: border-box;
    z-index: 100;
  }

  /deep/.van-checkbox__icon--checked {
    .van-icon {
      background: #fff;
      color: #666;
      border-color: #666;
    }
  }

  .userAgreement {
    padding: 0 16px 150px 16px;

    .title {
      line-height: 24px;
      font-size: 16px;
      font-family: "Audi-ExtendedBold";
      margin-top: 10px;
    }

    ul {
      font-size: 14px;

      li {
        div {
          &:first-child {
            line-height: 48px;
          }
        }

        .list {
          line-height: 20px;
          margin-bottom: 10px;
          
          .bold{
            font-weight: bold;
            border-bottom: 2px solid #1a1a1a;
          }
         
        }
        .fontbold{
          font-weight: bold;
        }
        
      }
    }

    .tsbtn {
      background: #999 !important;
      color: #fff;
      border: 0;
    }

    .buttons {
      background: #1a1a1a;
    }
  }

  .box {
    padding-bottom: 0;
  }

  /deep/.van-checkbox__label {
    margin: 0;
  }
</style>
