/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-23 22:47:00
 * @Last Modified by: mikey.zhaopeng
 * @Last Modified time: 2024-02-Th 03:56:40
 *
 * oss 图片处理
 */

import Vue from 'vue';

/**
 * 检测当前环境是否支持 webp
 */
(() => {
  const img = new Image()
  img.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA'

  return new Promise((res) => {
    img.onload = () => {
      const result = img.width > 0 && img.height > 0
      res(!!result)
      window.supportsWebp = true
    }
    img.onerror = () => {
      res(false)
      window.supportsWebp = false
    }
  })
})()

// 处理 oss 图片
const OSS_POSTFIX = '?x-oss-process=image'
const OSS_CONFIG = {
  jpg: '/interlace,1/format,jpg', // jpg
  webp: '/format,webp' // jpg
}
// /interlace,1/format,jpg
// 价格过滤的filter
Vue.filter('imgFix', (val, width, useJpg) => {
  if (!val) return ''
  /**
   * 优先用webp格式
   * 不支持webp的情况下 检查是否用jpg
   */
  let lastName = ''
  if (window.supportsWebp) {
    lastName = OSS_CONFIG.webp
  } else {
    lastName = useJpg ? OSS_CONFIG.jpg : ''
  }
  if (window.audiios) {
    val = val.replace('https://', 'customschema://')
    val = val.replace('http://', 'customschema://')
  }
  return `${val}${OSS_POSTFIX}/resize,w_${width}${lastName}`
})


// 中英文字符串换行的正则
Vue.filter('textWrap', (text) => {
  if (!text) return ''
  // if (/^[\u4e00-\u9fa5]/.test(text)) return text
  return text.replace(/([a-zA-Z])\s*([\u4e00-\u9fa5])/, '$1\n$2')
})

// A7MR 车型里都包含了 24VX款，
// 需要在 24VX款 之后插入换行
Vue.filter('a7MrTextWrap', (text) => {
  if (!text) return ''
  // 仅在 '24VX款' || '25款‘ 之后插入换行
  return text.replace(/(24VX款)\s*/, '$1\n').replace(/(25款)\s*/, '$1\n')
})
