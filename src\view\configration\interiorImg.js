/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-30 12:27:52
 * @Last Modified by: z<PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-30 15:24:13
 * 内饰图
 */

import Vue from 'vue'
import { mapState } from 'vuex'
import url from '@/config/url'

const ossUrl = url.BaseConfigrationOssHost
const InteriorImg = Vue.component('InteriorImgComponent', {
  render(h) {
    if (this.imgUrl) {
      return h('img', {
        attrs: {
          src: this.imgUrl,
          alt: this.test
        }
      })
    }
    return null
  },
  computed: mapState({
    imgUrl: (state) => {
      const {
        currentInteriorChair, selectCarInfo, currentInteriorEih, currentSibColorInterieur, currentOptionsList
      } = state
      const { seriesName, modelLineCode } = selectCarInfo
      if (!seriesName) { return }

      const series = seriesName.toLowerCase()
      // if (series === 'a7l') {
      //   this.changeHas3D(true)
      // } else if (series === 'q5e') {
      //   this.changeHas3D(true)
      // } else {
      //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      //   this.has3D = true
      // }

      if (currentInteriorChair && series !== 'q5e') {
        let arr = []
        let img
        if (currentInteriorChair.optionCode === 'Q4Q' && '498B2Y004,498B2Y003'.includes(modelLineCode)) {
          return `${ossUrl}/ccpro-backend/${series}/tmp/interieur/Q4Q.png?x-oss-process=image/resize,h_440`
        }
        const hsdp = currentOptionsList.find((res) => res.optionCode === '6NQ')
        if (hsdp) {
          arr.push(hsdp.optionCode)
        }
        if ('498BZY002'.includes(modelLineCode)) {
          arr.push('6NQ')
        }
        if (Object.keys(currentSibColorInterieur).length > 0 && Object.keys(currentInteriorChair).length > 0 && Object.keys(currentInteriorEih).length > 0) {
          if (seriesName === 'q6') {
            // 座椅+面料+内饰颜色+饰条
            // https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/q6/tmp/interieur/G6IBAY001/Q2J-N5D-TT-7TL.png
            arr = [currentInteriorChair.optionCode, currentSibColorInterieur.sibInterieurCode, currentInteriorEih.optionCode]
            img = arr.join('-')
            return `${ossUrl}/ccpro-backend/${series}/tmp/interieur/${modelLineCode}/${img}.png?x-oss-process=image/resize,h_440`
          }
          arr = [...arr, currentSibColorInterieur.interieurOptionCode, currentInteriorChair.optionCode, currentInteriorEih.optionCode]
          img = arr.join('-')
          if ('498BZG004'.includes(modelLineCode)) {
            img = `N5J-${img}`
          }
          return `${ossUrl}/ccpro-backend/${series}/tmp/interieur/${img}.png?x-oss-process=image/resize,h_440`
        }
        return null
      }
      if (series === 'q5e') {
        let arr = []
        let img
        if (Object.keys(currentSibColorInterieur).length > 0 && Object.keys(currentInteriorEih).length > 0) {
          arr = [...arr, currentSibColorInterieur.sibInterieurCode, currentInteriorEih.optionCode]

          img = arr.join('-')
          // q5e车型 内饰大图 区分锦衣和机甲 锦衣的2D图是白顶棚，机甲是黑色
          if (selectCarInfo.modelLineName.includes('机甲')) {
            img = `6NQ-${img}`
          }

          return `${ossUrl}/ccpro-backend/q5e/tmp/interieur/${img}.png?x-oss-process=image/resize,h_440`
        }
      }
      return null
    }
  })

})


export default InteriorImg
