/**
 * 全站http配置
 *
 * axios参数说明
 * isToken是否需要token
 */
import axios from 'axios'
import { Toast } from 'vant'
import NProgress from 'nprogress'
import { callNative, getChannel } from '@/utils/'
import URL from '@/api/url'
import api from '@/config/url'

import { getToken, setToken } from '@/utils/auth'
import { needTimeout } from '@/utils/timeout'
import Bridge from '@/utils/JSbridge.js'
import option from '@/view/order/option/option'
import store from '../store/index'
import 'nprogress/nprogress.css'
import storage from '../utils/storage'
import EventBus from '../utils/bus'

import { router } from './router';



const baseUrl = api.BaseApiUrl
// 默认超时时间
axios.defaults.timeout = 1000 * 30
// 返回其他状态码
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500
}
// 跨域请求，允许保存cookie
axios.defaults.withCredentials = true
// NProgress 配置
NProgress.configure({
  showSpinner: false
})
// http request拦截
// axios.interceptors.request.use((config) => {
//   // 开启 progress bar
//   // NProgress.start()
//   //  const meta = (config.meta || {});
//   // const isToken = meta.isToken === false;
//   //  //让每个请求携带token
//   //  if (getToken() && !isToken) {
//   //    config.headers['x-access-token'] =  getToken()
//   //  }
//   //  //headers中配置text请求
//   //  if (config.text === true) {
//   //    config.headers["Content-Type"] = "text/plain";
//   // }
//   // console.log("getToken"+getToken())

//   return config
// }, (error) => Promise.reject(error))
// http response 拦截

const refreshToken = (params) =>
  axios
    .post(`${baseUrl}${URL.refreshToken}`, { ...params })
    .then((response) => {
      if (response.status === 200) {
        return response
      }
    })
    .catch((error) => {
      console.error('%chttp-error', 'font-size:20px;color:red', error)
    })

axios.interceptors.response.use(
  (res) => {
    // 关闭 progress bar
    // NProgress.done()
    const {
      config: { headers }
    } = res || {}
    if (
      res.data.code &&
      !['00', 200, '200'].includes(res.data.code) &&
      res.status !== 401
    ) {
      // 在uat debug 用
      if (process.env.VUE_APP_ENV === 'pre') {
        console.error(
          `----data.code: ${res.data.code}, url: ${res.config.url}, message: ${res.data.message}`
        )
      }

      // ! 特殊异常返回可在请求接口中自定义【headers】处理单独显示，避免当前文件【混乱，臃肿】
      if (headers['unified-intercept-enabled']) {
        return res
      }
      // 添加 201 状态的弹窗处理
      // 201 状态是验证法大大合同签名的时候,身份证信息和姓名可能不一致导致的
      // if (res.data.code === '201') {
      //   Toast({
      //     type: 'fail',
      //     message: '请联系客服',
      //     icon: require('../assets/img/error.png')
      //   })
      // } else
      if (res.data.code === '50011') {
        // 处理金融服务报错的文案提示问题  Bug #9703
        if (res.config.url.include(URL.applyFinanceServer)) {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '已选择别的贷款方式',
            forbidClick: true,
            duration: 800
          })
        }
      } else if (res.data.code === '04') {
        // 处理金融服务报错的文案提示问题  Bug #9703
        if (res.config.url.include(URL.applyFinanceServer)) {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '当前订单购车合同未签署成功',
            forbidClick: true,
            duration: 800
          })
        }
      } else {
        // Toast({
        //   type: 'fail',
        //   message: res.data.message || '请求错误',
        //   icon: require('../assets/img/error.png')
        // })
      }

      // store.commit('hideLoading')
    }
    // 获取状态码
    // const status = res.status;
    // 白名单：http的status默认放行列表
    // const statusWhiteList = [];
    // const message = res.data.msg || res.data.error_description || '未知错误';
    // 如果在白名单里则自行catch逻辑处理
    // if (statusWhiteList.includes(status)) return Promise.reject(res);
    // 如果是401则跳转到登录页面
    //  if (status === 401) store.dispatch('FedLogOut').then(() => router.push({path: '/login'}));
    // 如果请求为非200否者默认统一处理
    // if (status !== 200) {
    //   Toast.fail(message);
    //   return Promise.reject(new Error(message))
    // } else {
    //   Toast.success(message);
    // }
    // if (res && res.status === 401) {
    //     });
    //   }
    return res
  },
  (error) => {
    if (
      error.code === 'ECONNABORTED' ||
      error.message == 'Network Error' ||
      error.message.includes('timeout')
    ) {
      EventBus.$emit('audi_api_timeout', {
        method: error.config.method,
        url: error.config.url.replace(baseUrl, ''),
        data: error.config.params ? error.config.params : error.config.data
      })
      // return new Promise((resolve, reject) => { })
    }
    Promise.reject(error)
  }
)

export default function request(options) {
  const token = localStorage.getItem('token') || ''
  // let channel
  // if (getChannel()) {
  //   channel = { channel: getChannel() }
  // } else {
  //   channel = options.channel ? { channel: options.channel } : {}
  // }
  const channel = options.channel ? { channel: options.channel } : {}
  const channelid = options.channelid ? { channelid: options.channelid } : {} // 渠道id(1、奥迪官网：2、奥迪APP 3：上汽奥迪小程序)

  const submitToken = options.submitToken
    ? { 'x-submit-token': options.submitToken }
    : {}

  let isToken = {}
  if (token) {
    isToken = {
      'x-access-token': token,
      'X-Source' : router.currentRoute.query.fromType === "fromPurple" ? 'AUDI' : '4ring'
    }
  } else {
    isToken = {}
  }

  const defaultOptions = {
    headers: {
      ...isToken,
      ...submitToken,
      ...channel,
      ...channelid,
      ...options.customHeader
    },
    url: options.url
  }

  const param =
    options.method === 'get'
      ? { params: options.params }
      : { data: options.params ?? options.data }

  if(options.customHeader) {
    delete options.customHeader
  }
  const newOptions = {
    ...defaultOptions,
    ...options,
    ...param
  }

  const timeout = storage.get('audiApiTimeOut')
  if (
    timeout &&
    needTimeout(options.url.replace(baseUrl, ''), options.method.toLowerCase())
  ) {
    // 请求超时接口的代码就不添加超时的逻辑
    if (options.url.includes(URL.getAPITimeOutTesting)) {
      newOptions.timeout = JSON.parse(timeout).inited
    } else {
      newOptions.timeout = JSON.parse(timeout).action
    }
  }

  return axios
    .request(newOptions)
    .then(checkStatus)
    .catch((error) => Promise.reject(error))
}

let isRefreshing = true

function checkStatus(response) {
  if (response && (response.status === 401 || response.status === 40101)) {
    // 刷新token的函数,这需要添加一个开关，防止重复请求
    if (isRefreshing) {
      refreshTokenRequst()
    }
    isRefreshing = false
    // 这个Promise函数很关键
    const retryOriginalRequest = new Promise((resolve) => {
      addSubscriber(() => {
        const config = response.config
        config.headers['x-access-token'] = localStorage.getItem('token')
        resolve(request(config))
      })
    })
    return retryOriginalRequest
  }
  return response
}

let subscribers = []

function addSubscriber(callback) {
  subscribers.push(callback)
}

// 获取url参数
function getUrlParamObj() {
  const url = window.location.href
  // eslint-disable-next-line no-new-object
  const theRequest = new Object()
  // eslint-disable-next-line no-useless-escape
  const strs = url.split(/[?&#\/]/)
  strs.forEach((e) => {
    if (e.indexOf('=') > -1) {
      const splitArr = e.split('=')
      theRequest[splitArr[0]] = splitArr[1]
    }
  })
  return theRequest
}

function refreshTokenRequst() {
  // if(!token) return false
  const onAccessTokenFetched = function () {
    subscribers.forEach((callback) => {
      callback()
    })
    subscribers = []
  }
  new Promise(() => {
    setTimeout(() => {
      if (process.env.NODE_ENV !== 'development') {
        const { env } = getUrlParamObj()
        if (env === 'minip') {
          refreshToken({
            refresh_token: localStorage.getItem('refreshToken'),
            appKey: 'AUDIMINIAPP',
            grant_type: 'refresh_token'
          }).then((ress) => {
            console.log('刷新用户信息', ress)
          })
        } else {
          callNative('getAudiUserInfo', {}).then((ress) => {
            localStorage.setItem('token', ress.token)
            // console.log('reload token', ress.token)
            setToken(ress.token)
            onAccessTokenFetched()
            isRefreshing = true
          })
        }
      }

      // Bridge.webViewJavascriptBridge.callHandler('getAudiUserInfo', param, (err, ress) => {
      //   if (err) {
      //     console.log('getAudiUserInfoError=', err)
      //     return false
      //   }
      //   localStorage.setItem('token', ress.token)
      //   console.log('token2=', ress.token)
      //   setToken(ress.token)
      //   onAccessTokenFetched()
      //   isRefreshing = true
      // })
    }, 0)
  })
}
