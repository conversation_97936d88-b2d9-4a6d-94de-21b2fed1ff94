<template>
  <div class="container">
    <navigation
      title=""
      :back-type="backType"
      :custom="true"
      @onBack="toBack"
    />
    <div class="connter">
      <h5>上汽奥迪特定机构人群购车计划</h5>
      <img
        src="../../assets/enterprisingElite.png"
      >
      <p>您只需满足以下条件，购买上汽奥迪车型即可享受专属购车礼遇：</p>
      <ul>
        <li>· 购车人就职于特定政府机构</li>
        <li>· 提供完整的资质审核材料（相关证件、身份证、公司开具的在职敲章证明）</li>
      </ul>
      <p>购车流程：</p>
      <ol>
        <li><i class="icon-certif">1</i><span>了解专属权益</span> </li>
        <li><i class="icon-certif">2</i><span>下载上汽奥迪APP和进店看车试驾或联系专职销售</span> </li>
        <li><i class="icon-certif">3</i><span>通过上汽奥迪APP扫特定政府机构专属购车二维码，在线填写个人信息提交审核材料，进行认证</span></li>
        <li><i class="icon-certif">4</i><span>APP站内信推送：审核通过/补充资料</span></li>
        <li><i class="icon-certif">5</i><span>APP内选车，小订后收到抵扣券再进行大定</span></li>
        <li><i class="icon-certif">6</i><span>车辆到店验车，付全款开票</span></li>
        <li><i class="icon-certif">7</i><span>提车</span></li>
      </ol>
      <p>如您有任何疑问，可通过以下方式联系我们：</p>
      <ul>
        <li>021-695-56182</li>
      </ul>
      <div class="checkbox-agreement">
        <van-checkbox
          v-model="checked"
          shape="square"
          @change="checkeds"
        /> <span style="margin-left:7px;color:#999999">我已阅读并同意</span><span
          @click="agreement"
        >《特定机构授权声明》</span>
      </div>
    </div>
    <div
      class="submit"
      :class="(isAgreement &&checked)?'':'submit-agreement'"
    >
      <p @click="submit">
        立即认证
      </p>
    </div>

    <van-dialog
      v-model="isShowBack"
      overlay
      :show-cancel-button="true"
    >
      <div class="title">
        您确定离开吗？相关材料需重新上传
      </div>
      <div class="cotton-buttons">
        <div
          class="D-buttons"
          @click="cancel"
        >
          取消
        </div>
        <div
          class="D-buttons2"
          @click="confim"
        >
          确认退出
        </div>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import Vue from 'vue'
import {
  Toast,
  Checkbox
} from 'vant'

import navigation from '../../components/navigation'
import {
  queryGovernmentApparatusInformation
} from '@/api/api'
import { callNative } from '@/utils/index'

Vue.use(Checkbox)
  .use(Toast)

export default {
  components: { navigation },
  data() {
    return {
      backType: 'app',
      isShowBack: false,
      checked: false,
      isAgreement: false

    }
  },
  created() {
    this.queryGovernmentApparatusInformation()
  },
  mounted() {
    // 判断跳转来源
    const { backType } = this.$route.query
    if (backType) {
      this.backType = backType
    } else {
      this.backType = 'app'
    }
    const greemen = localStorage.getItem('governmentEliteAgreement')
    this.isAgreement = !!(greemen && greemen == 1)
    this.checked = !!(greemen && greemen == 1)
  },
  methods: {
    submit() {
      if (this.isAgreement && this.checked) {
        this.$router.push({
          name: 'government-basic-information'
        })
      }
    },
    toBack() {
      const dataFrom = JSON.parse(localStorage.getItem('governmentElite'))
      if (dataFrom) {
        this.isShowBack = true
      } else {
        callNative('prepage', { times: 1 })
      }
    },


    queryGovernmentApparatusInformation() {
      queryGovernmentApparatusInformation().then((res) => {
        if (res.data.data) {
          // this.isShowBack = true
          if (res.data.data.informationStatus === 0) {
            this.$router.push({ name: 'government-uploaded-success' })
          }
        }
      })
    },
    confim() {
      if (this.isShowBack) {
        this.isShowBack = false
        localStorage.removeItem('governmentElite')
        localStorage.removeItem('governmentEliteAgreement')
        this.$store.commit('setIdCardPositiveElite', '')
        this.$store.commit('setIdCardReverseElite', '')
        this.$store.commit('setIdCardPositiveRelativesElite', '')
        this.$store.commit('setIdCardReverseRelativesElite', '')
        this.$store.commit('setFileListThreeElite', '')
        this.$store.commit('setFileListElite', '')
        this.$store.commit('setFileListFourElite', '')
        this.$store.commit('setfileListSix', '')

        callNative('prepage', { times: 1 })
      }
    },
    cancel() {
      this.isShowBack = false
    },

    agreement() {
      this.$router.push({
        name: 'government-agreement'
      })
    },

    checkeds() {
      if (!this.isAgreement) {
        this.checked = false
        Toast({
          type: 'fail',
          message: '请阅读特定机构授权声明',
          icon: require('../../assets/img/error.png'),
          forbidClick: true
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import url("../../assets/style/cell.less");
@import url("../../assets/style/scroll.less");
@import url("../../assets/style/dialog.less");
@import url("../../assets/style/buttons.less");
@import url("../../assets/style/animation.less");
@import url("../../assets/style/nor.less");
@import url("../../assets/style/common.less");
.connter{
  padding: 0 16px;
  padding-bottom: 120px;
  background:#FFFFFF;
  h5{
    font-size :16px;

    font-family: 'Audi-WideBold';
    margin:0;
    padding:12px 0;
  }
  img{
    width:100%;
    height: 193px;
  }
  p{
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-family: 'Audi-WideBold';
    text-align: left;
    line-height: 22px;
    margin:0;
    padding:16px 0 8px 0;
  }
  ul{
    li{
      color: rgba(0, 0, 0, 1);
      font-size: 12px;
      font-family: 'Audi-Normal';
      text-align: left;
      line-height: 22px;
      padding: 0 8px;
      opacity: .7;
    }
  }
  ol{
    li{
      line-height: 22px;
      display:flex;
      margin-bottom:8px;
      padding: 0 8px;
      .icon-certif{
        width:16px;
        height:16px;
        // display:block;
        border:1px solid #848587;
        border-radius: 50%;
        display:flex;
        align-items: center;
        justify-content: center;
        font-style:normal;
        margin-right:7px;
        color: #848587;
        font-size: 12px;
        font-family: 'Audi-Normal';
        margin-top:2px;
      }
      span{
        flex:1;
        color: rgba(0, 0, 0, 1);
        font-size: 12px;
        font-family: 'Audi-Normal';
        text-align: left;
        opacity: .7;
      }
    }
    :last-child{
      margin-bottom:0;
    }
  }
}
.title{
  font-size:16px;
  color:#000000;
  font-family: 'Audi-Normal';
  margin-top: 20px;
}
.cotton-buttons{
  display: flex;
  margin-top: 60px;
  .D-buttons{
    margin-right: 2px;
    color: #000;
    background: #fff;
        border: 1px solid #000;
  }
  .D-buttons2{
    height: 100%;
    margin-left: 2px;
    background: #000;
    color: #fff;
    border: 1px solid #000;
  }
}
.submit{
  position: fixed;
  left: 0;
  bottom: 0;
  width:100%;
  height:95px;
  background-color: #FFFFFF;
  padding-top: 16px;
  // padding: 0 16px;
  p{
  // width:100%;
  height:56px;
  background-color: #1A1A1A;
  color:#FFFFFF;
  font-size: 16px;
  font-family: 'Audi-Normal';
   display:flex;
        align-items: center;
        justify-content: center;
        margin: 0 16px;

  }
}
.submit-agreement{
  p{
     background-color: #E5E5E5;
  color:#FFFFFF;
  }


}
.checkbox-agreement{
    display:flex;
    align-items: center;
    margin-top:16px;
    font-size:12px;
  }

  ::v-deep .van-checkbox__icon {
  width: 24px;
  height: 24px;
}

::v-deep .van-checkbox__icon--checked .van-icon {
  color: #000000;
  width: 24px;
  height: 24px;
  line-height: 24px;
  border: none;
  background: #ffffff;
  // background-image: url('../../assets/img/icon04.png');
  // background-size: 100% 100%;
  // border-color: #666;
}

::v-deep .van-checkbox__icon .van-icon {
  width: 24px;
  height: 24px;
  border: none;
  border: 1px solid #808080;

  ::before {
    width: 24px;
    height: 24px;
  }
}

::v-deep .van-checkbox__icon .van-icon-success {}

::v-deep .van-checkbox__icon--checked .van-icon.van-icon-success {
  &::before {
    content: "";
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url("../../assets/img/icon20.png") no-repeat 0 0;
    background-size: 24px 24px;
    // margin-right: 4px;
    position: relative;
    left: -1px;
    top: -1px;
  }
}

</style>
