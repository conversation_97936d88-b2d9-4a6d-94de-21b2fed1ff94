<template>
  <div class="upload-documents box">
    <van-field
      v-model="remark"
      autosize
      type="textarea"
      rows="1"
      placeholder="补充"
    />
    <van-uploader
      v-model="fileList"
      :before-read="beforeRead"
      :after-read="afterRead"
      :show-upload="fileList.length <= 3"
      max-count="3"
      multiple
    />
    <div class="btnWarp">
      <div
        class="buttons"
        @click="submitUpload"
      >
        确认上传
      </div>
      <div class="bt" />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Field, Toast, Uploader } from 'vant'
import {
  uploadImg,
  addOrderDigitalCertificate
} from '../../api/upload-documents'
import api from '../../config/url'

Vue.use(Toast).use(Field).use(Uploader)
const codeType = ['00', '200']
export default {
  name: '',
  data() {
    return {
      remark: '',
      fileList: [],
      BaseOssHost: api.BaseOssHost,
      BaseApiUrl: api.BaseApiUrl
    }
  },
  components: {},

  methods: {
    uploadImg(formData) {
      uploadImg(formData)
        .then((res) => {
          if (codeType.includes(res.data.code)) {
            this.fileList.push({
              url: this.BaseOssHost + res.data.data[0].fileUrl
            })
            console.log(this.fileList, Toast)
            Toast({
              type: 'success',
              message: '上传成功',
              icon: require('../../assets/img/success.png')
            })
          } else {
            Toast({
              type: 'fail',
              message: '上传失败',
              icon: require('../../assets/img/error.png')
            })
          }
          this.$store.commit('hideLoading')
        })
        .catch((err) => {
          console.log(err)
          this.$store.commit('hideLoading')
        })
    },
    beforeRead(event) {
      this.$store.commit('showLoading')
      if (!Array.isArray(event)) {
        const formData = new FormData()
        formData.append('file', event)
        formData.append('fileType', 1)
        formData.append('categoryType', 1)
        formData.append('fileCategoryId', 1)
        this.uploadImg(formData)
      } else {
        if (event.length > 3) {
          event.splice(3)
        }
        event.forEach((item) => {
          const formData = new FormData()
          formData.append('file', item)
          formData.append('fileType', 1)
          formData.append('categoryType', 1)
          formData.append('fileCategoryId', 1)
          this.uploadImg(formData)
        })
      }
    },
    afterRead(file) {
      // 此时可以自行将文件上传至服务器
      console.log(file)
    },
    async submitUpload() {
      this.$store.commit('hideLoading')
      let str = ''
      if (this.fileList.length > 0) {
        this.fileList.forEach((item) => {
          str += `${item.url};`
        })
        const params = {
          orderId:
            this.$route.query.orderId || localStorage.getItem('orderId') || '',
          digitalCertificateAddress: str,
          remark: this.remark
        }
        const { data } = await addOrderDigitalCertificate(params)
        if (codeType.includes(data.code)) {
          this.$router.push({
            path: '/order/submit-success',
            query: {
              orderId: this.$route.query.orderId
            }
          })
        }
      } else {
        Toast({
          type: 'fail',
          message: '请先上传凭证',
          icon: require('../../assets/img/error.png')
        })
      }
    },

    compressImage(file) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
    }
  },
  mounted() {}
}
</script>
<style lang="less" scoped>
@import url("../../assets/style/scroll.less");
@import url("../../assets/style/buttons.less");
.upload-documents {
  padding-top: 10px;
  /deep/.van-cell {
    padding: 0;
    &::after {
      border: 0;
    }
    textarea {
      font-size: 16px;
      min-height: 100px;
    }
  }
  /deep/.van-uploader {
    border: 1px dashed #eee;

    .van-uploader__preview,
    .van-uploader__upload {
      background: #fff;
      margin: 0;
    }
    .van-uploader__preview {
      margin-right: 25px;
    }
  }
}
</style>
