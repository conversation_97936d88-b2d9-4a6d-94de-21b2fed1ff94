<template>
  <div class="certification">
    <div class="top">
      为了向您发放权益并提供车联网相关服务，必须确认您对车辆的所有权，需要您提供相应
      <span>购车发票（或行驶证）</span
      ><span
        >与身份证件照片（居民身份证、外籍护照、港澳台居民往内地通行证（回乡证）、台湾居民来往大陆通行证（台胞证）、港澳居民居住证、台湾居民居住证、中国人民解放军军官证（军官证）、中国人民武装警察部队警官证（武警证）、外国人永久居留居住证（外国人永居证）之一）</span
      >。
      <p>
        此流程收集的所有信息仅用于上述用途，我们将依据相关法规要求，严格保密您的个人信息。
      </p>
    </div>
    <div class="bottom">
      <p>为保障您的权益，请仔细阅读并同意以下条款以确保实名认证的顺利进行。</p>
      <div class="checkbox_style">
        <div class="checkbox_style_btn">
          <div
            v-if="!ischecked"
            class="checkbox_button-n"
            @click="ischecked = !ischecked"
          ></div>
          <img
            v-if="ischecked"
            class="checkbox_button"
            @click="ischecked = !ischecked"
            :src="ischecked ? activeRadioIcon : inactiveRadioIcon"
          />
          <span class="checkbox_button-rodi" @click="ischecked = !ischecked">
            我已阅读并同意</span
          >
          <span @click="handleBtn(1)">《隐私政策》 </span>
          <span @click="handleBtn(2)"> 《敏感个人信息处理规则》 </span>
        </div>
      </div>
      <div class="checkbox_style">
        <div class="checkbox_style_btn">
          <div
            v-if="!isVehiclechecked"
            class="checkbox_button-n"
            @click="isVehiclechecked = !isVehiclechecked"
          ></div>
          <img
            v-if="isVehiclechecked"
            class="checkbox_button"
            @click="isVehiclechecked = !isVehiclechecked"
            :src="isVehiclechecked ? activeRadioIcon : inactiveRadioIcon"
          />
          <span
            class="checkbox_button-rodi"
            @click="isVehiclechecked = !isVehiclechecked"
          >
            我已阅读并同意</span
          >
          <span @click="handleBtn(3)"> 《车辆识别码(汽车VIN号)出境规则》 </span>
        </div>
      </div>
      <div
        :class="
          ischecked && isVehiclechecked
            ? 'btn-delete-wrappers'
            : 'btn-delete-wrapper'
        "
        @click="onSubmit"
      >
        确定并继续
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  RadioGroup, Radio, Field, Checkbox, CheckboxGroup
} from 'vant'
import AudiButton from '@/components/audi-button'
import { callNative } from '@/utils'
import { getprivacy, getpolicy, getManCarMemberInfo } from '@/api/api'
import storage from '../../utils/storage'

Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Field).use(CheckboxGroup).use(Checkbox)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      activeRadioIcon: require('../../assets/img/radio_rz.png'),
      inactiveRadioIcon: require('../../assets/img/radio_normal.png'),
      ischecked: false,
      isVehiclechecked: false,
      isSelectType: true,
      orderId: '',
      certificationStatus: ''
    }
  },

  async created() {
    const { data } = await getManCarMemberInfo({})
    this.certificationStatus = data.data.certificationStatus
  },

  methods: {
    async handleBtn(e) {
      this.$router.push({
        path: '/certification/rich-text',
        query: {
          type: e
        }
      })
    },
    // 确认
    async onSubmit() {
      if (!this.ischecked || !this.isVehiclechecked) {

      } else {
        if (this.certificationStatus == '1') {
          // 身份认证通过去绑车页面
          this.$router.replace({
            path: '/certification/car-certification'
          })
        } else {
          // 其他身份状态去身份认证
          this.$router.replace({
            path: '/certification/identity-certification'
          })
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import '../../assets/style/common.less';

.certification {
  margin-top: 8px;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .top {
    font-size: 14px;
    color: #333333;
    line-height: 24px;
    flex: 1;
    span {
      font-family: 'Audi-WideBold';
    }
    p {
      margin: 12px -16px 0 -16px;
      padding: 10px 16px;
      background: #f2f2f2;
      font-size: 12px;
      color: #1a1a1a;
      line-height: 20px;
      font-family: 'Audi-Normal';
    }
  }
  .bottom {
    position: fixed;
    left: 0;
    padding: 0 16px;
    bottom: 38px;
    p {
      margin-bottom: 19px;
      font-size: 13px;
      color: #808080;
      line-height: 20px;
    }
    .checkbox_button {
      width: 18px;
      height: 18px;
      color: #979797;
      border-radius: 50%;
    }
    .checkbox_button-n {
      width: 17px;
      height: 17px;
      border-radius: 50%;
      border: 1px solid #979797;
    }
    .checkbox_style {
      display: flex;
      height: 22px;
      justify-content: space-between;
      margin-bottom: 4px;
      font-family: 'Audi-Normal';
      color: #999999;
      width: 100%;
      font-size: 12px;
      .checkbox_style_btn {
        display: flex;
        align-items: center;
      }
      .checkbox_button-rodi {
        font-family: 'Audi-Normal';
        color: #999999;
        margin-left: 4px;
      }
      span {
        font-size: 12px;
        color: #333333;
        font-family: 'Audi-WideBold';
      }
    }
    .btn-delete-wrapper {
      margin-top: 16px;
      height: 56px;
      line-height: 56px;
      background-color: #b3b3b3;
      color: #e5e5e5;
      font-size: 16px;
      text-align: center;
    }
    .btn-delete-wrappers {
      margin-top: 16px;
      height: 56px;
      line-height: 56px;
      background-color: #000000;
      color: #e5e5e5;
      font-size: 16px;
      text-align: center;
    }
  }
}
::v-deep .van-checkbox__label {
  margin-top: 5px;
}

::v-deep .van-checkbox__label {
  margin-left: 4px;
}
</style>
