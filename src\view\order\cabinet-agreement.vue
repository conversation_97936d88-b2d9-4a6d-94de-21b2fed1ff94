<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-10 16:40:13
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-16 17:52:58
 * @FilePath     : \src\view\order\cabinet-agreement.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['cabinet-agreement-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
    />
    <div
      class="main-wrapper"
      id="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <div class="agreement-box lan-cell-box">
        <van-cell-group class="lan-cell-group">
          <div
            v-for="(list, index) of agreement"
            :key="index"
          >
            <van-cell
              v-if="list.show == true"
              class="cell-box"
              :title="list.title"
              value=""
              is-link
              @click="handleGoToAgreementPage(list.name, list.status)"
            >
              <template #default>
                <span :class="['has-warning', list.isWarning ? 'is-warning' : '']" />
              </template>
            </van-cell>
          </div>
        </van-cell-group>
        <rights-q5e-lite-prevent
          v-if="$route.query.skukw === 'lite'"
          :rights-prevent.sync="rightsPreventShow"
          :order-id="$route.query.orderId"
        />
      </div>
    </div>
  </div>
</template>

<script>
// list.status !== orderStatus || !(orderStatus === '31' && !isSignedContract)
import Vue from 'vue'
import {
  Cell,
  CellGroup
} from 'vant'
import {
  callNative
} from '@/utils'
import wx from 'weixin-js-sdk'
import HeaderCustom from '@/components/header-custom.vue'
import rightsQ5eLitePrevent from '@/components/rights.q5e.lite.prevent.vue'
import { ORDER_STATUS_DISTRICT, RES_SUCCEED_CODE, ORDER_AGREEMENT } from '@/config/conf.data'
import {
  getMyOrders,getCarOrderInfoH5
} from '@/api/api'

Vue.use(Cell)
  .use(CellGroup)

export default {
  components: {
    'header-custom': HeaderCustom,
    'rights-q5e-lite-prevent': rightsQ5eLitePrevent
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      agreement: [],
      ORDER_STATUS_DISTRICT,
      orderStatus: '',
      orderId: '',
      isSignedContract: '',
      rightsPreventShow: false,
      isClickDownloadContract: 0,
      isOneStep: 0,
      boolPurchaseSignable:false,
      boolReceivePurchaseInvoice:false
    }
  },
  created() {
    this.handleProcessData()
  },
  methods: {
    async handleProcessData() {
      const {
        $route: {
          query: {
            orderId, isSignedContract, isDeliveryContract, vehicleStatus, isClickDownloadContract, isOneStep,orderType
          }
        }
      } = this
      isSignedContract && (this.isSignedContract = +isSignedContract)
      isDeliveryContract && (this.isDeliveryContract = +isDeliveryContract)
      vehicleStatus && (this.vehicleStatus = +vehicleStatus)
      isClickDownloadContract && (this.isClickDownloadContract = +isClickDownloadContract)
      isOneStep && (this.isOneStep = +isOneStep)
      console.log('%c [this.isSignedContract   ]-118', 'font-size:14px; background:#cf222e; color:#fff;', isSignedContract)
      const { data } = await getMyOrders({ orderId })
      if (data?.code && RES_SUCCEED_CODE.includes(data.code)) {
        const { data: { orderStatus: dataStatus } } = data
        this.boolPurchaseSignable = data.data.extInfo.boolPurchaseSignable
        this.boolReceivePurchaseInvoice = data.data.extInfo.boolReceivePurchaseInvoice
        console.log('%c [ dataStatus ]-110', 'font-size:14px; background:#cf222e; color:#fff;', dataStatus)
        const orderStatus = dataStatus === '301' ? '30' : dataStatus
        if (orderStatus) {
          orderStatus && (this.orderStatus = orderStatus)
          const { DIES, REFUND } = ORDER_STATUS_DISTRICT || []
          this.agreement = ORDER_AGREEMENT.map((i) => {
            if (+isOneStep === 1 && i.name === 'user-agreement') {
              i.title = i.title.replace(/意向/g, '定')
              i.name = 'deposit-agreement'
            }
            i.show = false
            i.isWarning = false
            if (orderStatus) {
              if(orderType == '07') {
                if (i.status === '31') {
                  i.isWarning = +this.isSignedContract === 0 && [i.sign].includes(orderStatus)          
                } else if (i.status === '32') {
                    i.isWarning = +this.vehicleStatus === 3 && [i.sign].includes(orderStatus)
                  
                } else {
                    i.isWarning = [i.sign].includes(orderStatus)
                }
              }else {
                if (i.status === '31') {
                  i.isWarning = (+this.isSignedContract === 0 && [i.sign].includes(orderStatus) ) || ['84'].includes(orderStatus)    
              } else if (i.status === '32') {
                  let willStatus = data.data.contractInfo?.purchaseContractStatus == '2' && (orderStatus == '31' || orderStatus == '32')
                  i.isWarning = (this.boolReceivePurchaseInvoice && orderStatus!= '90') || willStatus || [i.sign].includes(orderStatus)
              } else {
                  i.isWarning = [i.sign,'98'].includes(orderStatus)

                }
              }

              const status = Number(orderStatus) >= Number(i.status) && ![...DIES, ...REFUND].includes(orderStatus)
              // eslint-disable-next-line no-nested-ternary
              if(orderType == '07') {
                i.show = i.status === '00' ? true : (i.status === '32' ? (status && this.vehicleStatus > 2) : status)
              }else {
                let willStatus = data.data.contractInfo?.purchaseContractStatus == '2' && (orderStatus == '31' || orderStatus == '32')
                i.show = i.status === '00' ? true : (i.status === '32' ? (status || this.boolReceivePurchaseInvoice || willStatus) : status)
              }
              

              console.log('%c [ i.show ]-140', 'font-size:14px; background:#cf222e; color:#fff;', i.show)
            }
            return i
          })
        }
      }
    },
    async handleGoToAgreementPage(name, status = '') {
      const {
        $route: {
          query: {
            orderId, orderStatus, skukw, isSelectOnline, isClickDownloadContract, env, ccid,orderType
          }
        }
      } = this
      // if (name === 'contract-info' && env === 'minip') {
      //   const { origin, pathname } = location
      //   const url = `/pages/order/contract/index?orderId=${orderId}&ccid=${ccid}&isClickDownloadContract=${isClickDownloadContract}&prod=${process.env.VUE_APP_ENV === 'pre' ? 0 : 1}&url=${encodeURIComponent(`${origin}${pathname}#/contract/guide`)}`
      //   return wx.miniProgram.navigateTo({ url })
      // }
      if (name === 'delivery-contract' && skukw === 'lite') {
        this.rightsPreventShow = true
        return
      }
      if(orderType !='07' && name == 'user-agreement') {
        name = 'new-user-agreement'
      }
      if (name === 'delivery-contract') {
        const {data} = await getCarOrderInfoH5()
        let query = {
          orderId,
            isSelectOnline,
            isClickDownloadContract,
            orderType,
            ...(status !== '00' || { status: true, read: '1' }),
        }
        const params = Object.keys(query).reduce((str, list) => `${str}&${list}=${query[list]}`, '?')
        callNative('audiOpen', {
          path: `${data.data.configValue}delivery-contract${params}`
       })
      } else {
        this.$router.push({
          name,
          query: {
            orderId,
            isSelectOnline,
            isClickDownloadContract,
            orderType,
            ...(status !== '00' || { status: true, read: '1' }),
          }
        })
      }
    }
  }
}
</script>
<style lang="less">
@import url("~@/assets/style/forms-cell.less");
</style>
<style lang="less" scoped>
.cabinet-agreement-wrapper {
  /deep/ .lan-cell-box  {
    .van-cell-group {
      border-top: 0;
      .van-cell__right-icon {
        font-weight: 600;
      }
      .van-cell {
        padding-top: 24px;
      }
    }
  }
}

</style>
