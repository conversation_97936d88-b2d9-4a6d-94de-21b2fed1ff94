<!-- 弹出框 -->
<template>
  <div>
    <div class="client-wrapper">
      <div class="c-font14 title">
        客户权益
      </div>
      <div class="c-flex-center-between">
        <div class="c-flex-center">
          <div class="sc-left">
            <img
              :src="require('../assets/mock/quanyi.png')"
              alt=""
            >
          </div>
          <div>
            <div class="c-font12">
              {{ serverName }}
            </div>
            <div class="small-font">
              价格已包含
            </div>
          </div>
        </div>

        <div
          class="c-flex-center small-font"
          @click="toEquity"
        >
          查看详情
          <van-icon name="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { XIAN_XING_VERSION, XIAN_JIAN_VERSION, XIAN_XING_VERSION_Q5 } from '@/config/constant'

export default {
  data() {
    return {

    }
  },
  computed: {
    ...mapState({
      modelLineCode: (state) => state.carDetail.configDetail?.carModel?.modelLineCode,
      carSeries: (state) => state.carSeries
    }),
    serverName() {
      if (this.carSeries?.seriesCode === 'G4') {
        if (this.modelLineCode === XIAN_XING_VERSION_Q5) {
          return '艺领权益服务包'
        }
        return '艺创权益服务包'
      }
      if (this.modelLineCode === XIAN_XING_VERSION || this.modelLineCode === XIAN_JIAN_VERSION) {
        return '领尊权益服务包'
      }
      return '尊享权益服务包'
    }
  },
  watch: {},
  mounted() {},

  methods: {
    toEquity() {
      this.$router.push({
        path: '/equity-detail'
      })
    }
  }
}
</script>

<style lang="less" scoped>
  @import "../assets/style/common.less";

  @leftWith: 18vw;
  @rightMargin: 15px;
  @topMargin: 20px;
  @fontColor: #999;

  //向上的边距
  .sc-m-top {
    margin-top: @topMargin;
  }

  .sc-left {
    width: @leftWith;
    margin-right: @rightMargin;
  }

  //box阴影
  .sc-shadow {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
  }

  // 下划线
  .sc-u-line {
    border-bottom: 1px solid #e5e5e5;
  }

  //box阴影
  .sc-shadow {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
  }

  // 下划线
  .sc-u-line {
    border-bottom: 1px solid #e5e5e5;
  }

  //
  .bold18 {
    .c-font18;
    .c-bold;
  }

  .small-font {
    .c-font12;
    color: #999;
  }

  .container {
    .sc-m-top;
    position: relative;
    padding: 0 18px;
  }

  .hint-wrapper {
    background-color: #f2f2f2;
    font-size: 14px;
    padding: 10px 18px;
  }

  .sc-nowrap {
    white-space: nowrap;
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    word-break: break-all;
  }

  .sc-height {
    .c-flex-between;
    flex-direction: column;
    flex: 1;
    height: @leftWith;
    padding: 14px 0;
    box-sizing: border-box;
  }

  // 我的配置
  .config-wrapper {
    .sc-shadow;
    padding: 0px 16px;
    margin-top: 15px;

    > .padding {
      .sc-u-line;
      padding: 10px 0;
    }

    .config-item {
      position: relative;
      padding: 12px 0;
      height: @leftWith;
    }
  }

  // 客户权益
  .client-wrapper {
    .sc-shadow;

    margin-top: 20px;
    padding: 5px 20px;
    border-bottom: 1px solid #e5e5e5;

    > .title {
      padding: 5px 0;
      margin-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
    }
  }
</style>
