<template>
  <div class="container">
    <navigation
      title=""
      :back-type="backType"
      :custom="true"
      @onBack="onSubmit"
    />
    <div class="connter">
      <div class="card-img">
        <img src="../../assets/img/contract-success.png">
      </div>
      <p>您的审核材料已上传成功，我们将于2~3个工作日内通过站内信的形式通知您审核结果，请耐心等待</p>
    </div>
    <div class="bottom_style">
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onSubmit"
          :text="'返回首页'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import navigation from '../../components/navigation'
import AudiButton from '@/components/audi-button'
import { callNative } from '@/utils'

export default {
  components: { navigation, AudiButton },
  data() {
    return {
      backType: 'app'
    }
  },
  mounted() {
    // 判断跳转来源
    const { backType } = this.$route.query
    if (backType) {
      this.backType = backType
    } else {
      this.backType = 'app'
    }
  },
  methods: {
    onSubmit() {
      callNative('openRoutePath', { path: 'scaudi://bindCar/home' }).then((data) => {})
    }
  }
}
</script>

<style lang="less" scoped>
.container{
    display: flex;
    flex-wrap: wrap;
    .connter{
    padding: 0 16px;

       p{
        text-align: center;
        margin:0;
        line-height:32px;
        font-size: 18px;
        margin-bottom: 16px;
        font-family: 'Audi-Normal';
        color:#000000;
    }
    .card-img{
        flex:1;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        // margin-top: 96px;

        img{
            display: block;
            width:72px;
            height: 72px;
            margin-bottom: 24px;
        }
    }

}
    .bottom_style {
        width: 100%;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: absolute;
        bottom: 30px;
        // padding: 16px;
        left: 0px;
    }
    .btn-delete-wrapper {
       margin: 16px;
    }
}

</style>
