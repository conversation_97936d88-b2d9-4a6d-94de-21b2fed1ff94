<template>
  <div class="container" :class="[father === 'configration' ? 'container1' : '']">
    <template v-if="dataList">
      <div class="layout-wrapper">
        <div class="layout-item-wrapper" v-for="(item, index) in dataList" :key="'dataList' + index"
          v-show="item.type !== 'smallTitle'">
          <div class="name-wrapper">
            <span class="name" @click="showDetail(item, '查看详情')">
              {{ item.desc }}
            </span>
            <div @click="showDetail(item, '查看详情')" v-if="item.show">
              查看详情
            </div>
          </div>

          <div class="item-wrapper">
            <div class="item" @click="showDetail(item, ind)" v-for="(j, ind) of item.children" :key="'children' + ind">
              <div class="name" v-html="j.desc" />
            </div>
          </div>
        </div>

        <!-- // 底部的type 字段 -->

        <div class="loan-item" style="margin-top:20px;" v-for="(item, i) in dataList" :key="'dataList2' + i"
          v-show="item.type === 'smallTitle'">
          {{ item.desc }}
        </div>
      </div>
    </template>
    <img class="quanyiimage" v-if="dataImage" :src="dataImage">
    <!-- <div
      class="toquanyidetail"
      @click="toquanyidetail"
      v-if="carSeries.seriesCode === 'G4' || selectCarInfo.seriesCode === 'G4'"
    >
      查看详情 >
    </div> -->
    <!-- // 弹窗详情 -->
    <swipe3d ref="swipe3dRef"></swipe3d>


    <div style="width: 100%;height: 30px;" />
  </div>
</template>

<script>
import Vue from 'vue'
import { Popup } from 'vant'
import { mapState } from 'vuex'
import { getUserRightsByCarModelId } from '@/api/api'
import swipe3d from '@/components/swipe3d.vue'

console.log('swipe3d', swipe3d)

Vue.use(Popup)
export default {
  components: { swipe3d },
  data() {
    return {
      dataImage: '',
      dataList: [],
      detailVisible: false,
      currentDetail: '',
      currentTitle: '',
      quanyititle: ''
    }
  },
  props: {
    father: {
      type: String,
      required: false,
      default: 'configration'
    },
    carModelId: {
      type: String,
      required: false,
      default: ''
    }
  },
  computed: mapState({
    // skuid: 'skuid',
    env: 'env',
    carSeries: (state) => state.carSeries,
    selectCarInfo: (state) => state.selectCarInfo
  }),
  watch: {
    // skuid() {
    //   this.getEquity()
    // },
    carModelId(val) {
      this.getEquity()
    }
  },

  mounted() {
    this.getEquity()
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const arr = [
        {
          optionName: '新生权益',
          optionCode: 'YEG'
        },
        {
          optionName: '轻装权益包',
          optionCode: 'YEA'
        },
        {
          optionName: '重装权益包',
          optionCode: 'CCPRO-YEB'
        },
        {
          optionName: '尊享权益',
          optionCode: 'CCPRO-YEB'
        }
      ]
      const optionName = this.$route.query?.optionName
      const seriesName = this.$route.query?.seriesName
      const form = this.$route.query?.form
      if (seriesName == 'G4') return
      console.log(this.$route.query?.caseCode)
      const titBox = arr.find((e) => e.optionCode == this.$route.query?.caseCode) || {}
      console.log('titBox', titBox)
      setTimeout(() => {
        if (form == 'carVersion') {
          this.$store.commit('setTitleFn', optionName)
          return
        }
        let str = ''
        if (titBox?.optionName) {
          str = titBox?.optionName
        }
        if (!titBox?.optionName) {
          // if (seriesName == 'G6') {
          //   str = "重装权益包"
          // }
          // if (seriesName == '49') {
          //   str = "尊享权益"
          // }
        }
        str && this.$store.commit('setTitle', str)
      }, 1000)
    },
    async getEquity(id) {
      // "YEA"
      // optionId
      // "f7329319-befc-454c-a1c3-1eaed09f4bf0"
      // "轻装权益包"
      const modelLineCode = this.$route.query?.modelLineCode
      const caseCode = this.$route.query?.caseCode
      const p = {
        carModelId: modelLineCode,
        caseCode: caseCode && 'YEA,YEG'.includes(caseCode) ? 'A' : '',
        type: 1,
        modelYear: this.$route.query?.modelYear || '',
        modelVersion: this.$route.query?.modelVersion || ''
      }
      if (!p.caseCode) {
        delete p.caseCode
      }

      const { orderId } = this.$route.query
      if (orderId) {
        p.orderId = orderId
      }

      const res = await getUserRightsByCarModelId(p)
      const { data } = res
      console.log('权益 res：', res)
      if (data.code == '01') return
      if (data?.data?.rights?.startsWith('http')) {
        this.dataImage = decodeURIComponent(data.data.rights)
      } else {
        const d = JSON.parse(data.data.rights)
        d.forEach((f) => {
          if (Array.isArray(f.children)) {
            f.show = f.children.length > 0
          }
        })
        this.dataList = d
      }
      console.log('000', this.dataList)
      this.quanyititle = data.data.title
      if (this.$route.path !== '/configration') {
        this.$store.commit('setTitle', data.data.title)
      }
    },
    // 显示详情
    showDetail(item, index = 0) {
      console.log(index, '显示详情', item)
      if (item.children == false) return
      this.$refs.swipe3dRef.init(item, index)
      // this.currentTitle = item.detailTitle
      // this.currentDetail = item.detail
      // this.detailVisible = true
    },
    close() {
      this.detailVisible = false
    },
    sectionToChinese(section) {
      const chnNumChar = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      const chnUnitChar = ['', '十', '百', '千', '万', '亿', '万亿', '亿亿']
      let strIns = ''; let
        chnStr = ''
      let unitPos = 0
      let zero = true
      while (section > 0) {
        const v = section % 10
        if (v === 0) {
          if (!zero) {
            zero = true
            chnStr = chnNumChar[v] + chnStr
          }
        } else {
          zero = false
          strIns = chnNumChar[v]
          strIns += chnUnitChar[unitPos]
          chnStr = strIns + chnStr
        }
        unitPos++
        section = Math.floor(section / 10)
      }
      return chnStr
    },
    toquanyidetail() {
      this.$router.push({
        path: '/equity-detail-moreinfo',
        query: { title: `${this.quanyititle}详情`, ...this.$route.query }
      })
    }
  }
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  padding: 16px;
  font-size: 14px;

  &.container1 {
    // height: calc(100vh - 190px);
    min-height: 100%;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
  }
}

.layout-wrapper {
  .layout-item-wrapper {
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
    margin-bottom: 20px;
    padding: 10px;
    box-sizing: border-box;

    >.name-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .item-wrapper {
      margin-top: 8px;

      .item {
        display: flex;
        justify-content: space-between;
        line-height: 26px;

        >.name {
          max-width: 90%;
        }
      }
    }
  }
}

.detail-wrapper {
  padding: 20px;
  padding-bottom: 50px;

  >.title {
    font-weight: bold;
    font-size: 16px;
    padding-right: 50px;
  }

  >.detail {
    margin-top: 15px;
    font-size: 14px;
  }
}

.loan-item {
  font-size: 12px;
  color: #999999;
}

.quanyiimage {
  display: block;
  width: 100%;
}

.toquanyidetail {
  text-align: right;
  margin: 16px 0;
}
</style>
