<template>
  <div class="swiper-container" >
    <div class="swiper-wrapper" v-if="activeView !== 'hub'">
      <!-- <div class="swiper-slide" v-for="item in imgList" :key="item" > -->
      <div class="swiper-slide" v-for="item in carImageUrls" :key="item" >
        <div>
          <img :src="item | imgFix(640)" alt="" @load="endLoad">
        </div>
      </div>
    </div>
    <div class="hubwrapper default-car"  v-else>
      <figure>
        <img :src="hubImages.carimg" alt="" @load="endLoad">
        <div class="car-wheel">
          <!-- 修改点1：添加动态类绑定 -->
          <img :src="hubImages.hubimg" 
               :class="{ 
                 'default-animation': isFirstHubEntry, 
                 'rotate-animation': wheelRotating 
               }" 
               @animationend="onAnimationEnd" 
               alt="" 
               @load="endLoad">
        </div>
      </figure>
      </div>
    <!-- <div class="navigation">
      <div class="swiper-button-prev"></div>
      <div class="swiper-button-next"></div>
    </div> -->
    <!-- <div class="swiper-pagination" /> -->
  </div>
</template>
<script>
import Swiper from 'swiper'

// import Vue from 'vue';
// import { Swipe, SwipeItem } from 'vant';
// Vue.use(Swipe);
// Vue.use(SwipeItem);

export default {
  components: {},
  props: {
    imgList: {
      type: Array,
      default: () => []
    },
    activeView: {
      type: String,
      default: 'color' // 'color' 或 'hub'
    },
    // 新增 prop 接收轮毂图片数据
    hubImages: {
      type: Object,
      default: null 
    },
  },
  data() {
    return {
      mySwiper: null,
      // 初始化为空数组，稍后生成带时间戳的URL
      carImageUrls: [],
      wheelRotating: false,
      // 修改点2：添加首次进入标志
      isFirstHubEntry: true
    }
  },
  watch: {
    imgList() {
      this.$nextTick(() => {
        if (this.mySwiper) {
          this.mySwiper.destroy()
        }
        this.initSwiper()
      })
    },

    hubImages: {
      handler(newVal) {
        if (this.activeView === 'hub') {
          // 修改点3：仅当不是首次进入时才触发旋转动画
          if (!this.isFirstHubEntry) {
            console.log('%c 不是首次进入！！', 'font-size:16px;color:green;');
            this.wheelRotating = true;
          }
        }
      },
      deep: true
    },

     activeView(newVal) {
      console.log(newVal);
      // 修改点4：优化首次进入判断逻辑
      if (newVal === 'hub') {
        // 标记为首次进入
        this.isFirstHubEntry = true;
      }else{
        this.wheelRotating = false;
        this.isFirstHubEntry = true;
      }
    }
  },
  activated() {
    // 如果是 keep-alive 缓存的组件，激活时重新生成URL
    this.generateImageUrls();
    this.$nextTick(() => {
      if (this.mySwiper) {
        this.mySwiper.destroy();
      }
      this.initSwiper();
    });
  },
  mounted() {
    // this.$store.commit('showLoading')
    this.generateImageUrls(); // 生成带时间戳的URL
    this.$nextTick(() => {
      this.initSwiper()
    })
  },

  methods: {
    generateImageUrls() {
      // 为 GIF 添加时间戳参数强制重新加载
      const timestamp = new Date().getTime();
      this.carImageUrls = [
        require("../../assets/Front45.gif") + `?t=${timestamp}`,
        "https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a5l/carImages/F08BZY002/F8A1/V30/Side.png",
        "https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a5l/carImages/F08BZY002/F8A1/V30/Rear45.png",
        "https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a5l/carImages/F08BZY002/F8A1/V30/Rear.png",
        "https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a5l/carImages/F08BZY002/F8A1/V30/Front.png",
        "https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a5l/carImages/F08BZY002/F8A1/V30/Top.png",
        // ... 其他图片URL保持不变 ...
      ];
    },
    // 修改点5：处理两种动画结束状态
    onAnimationEnd(event) {
      if (this.isFirstHubEntry) {
        // 首次进入的默认动画结束后重置标志
        this.isFirstHubEntry = false;
      } else {
        // 轮毂切换的旋转动画结束后重置状态
        this.wheelRotating = false;
      }
    },
    initSwiper() {
      this.mySwiper = new Swiper('.swiper-container', {
        loop: true,
        cache: false,
        speed: 1000,
        autoplay: true,
        direction: 'horizontal',
        pagination: {
          el: '.swiper-pagination',
          type: 'fraction'
        },
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      })
    },
    endLoad() {
      // this.$store.commit('hideLoading')
    }
  },
  beforeDestroy() {
    this.mySwiper.destroy()
    this.mySwiper = null
  }
}
</script>
<style lang="less" scoped>
.swiper-container {
  width: 100%;
  //   height: 220px;
  --swiper-pagination-color: black;
  --swiper-navigation-size: 14px;
  // overflow: visible;

  .swiper-wrapper {
    width: 100%;

    .slide img {
      width: 100%;
      height: 100%;
    }

    .swiper-slide {
      >div {
        position: relative;
      }

      img {
        height: 220px;
        object-fit: contain;
      }

      .ex-img,
      .ex-img {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 2;
      }

      video {
        height: 220px;
        object-fit: contain;
      }
    }
  }
  .hubwrapper{
    position: relative;
    width: 100%;
    height: 220px;
    overflow: hidden;
    background-color: #000000;
    figure {
        width: 100%;
        height: 220px;
        position: relative;
        display: inline-block;
        img {
          height: auto;
          width: 100%;
          -webkit-transform: scale(1) rotate(0) translate3d(0, 0, 0);
          transform: scale(1) rotate(0) translate3d(0, 0, 0);
          -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
        }
        .car-wheel {
          position: absolute;
          height: 136px;
          top: 35%;
          left: 42.3%;
          text-align: left;
          img {
            height: 100%;
            width: auto;
            -webkit-transform: scale(1) rotate(0) translate3d(0, 0, 0);
            transform: scale(1) rotate(0) translate3d(0, 0, 0);
            /* 修改点5：移除默认动画，改为类控制 */
          }
        }
    }
  }
  .hubwrapper.default-car figure {
    margin: 0;
    width: 100%;
    height: 220px;
    -webkit-animation: defaultCar .9s 0s linear;
    animation: defaultCar .9s 0s linear;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
  }
  @keyframes defaultCar {
        0% {
            right: -100%;
        }

        100% {
            right: 0;
        }
    }

  /* 修改点6：将默认轮子动画改为类控制 */
  .default-animation {
    -webkit-animation: defaultWheel .9s 0s linear;
    animation: defaultWheel .9s 0s linear;
  }
  @keyframes defaultWheel {
        0% {
            -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
        }

        100% {
            webkit-transform: rotate(0);
            transform: rotate(0);
        }
  }
  .rotate-animation {
      animation: rotateWheel 1.5s cubic-bezier(0.5, 0, 0.3, 1) both;
      /* 添加硬件加速 */
      transform: translateZ(0);
      will-change: transform;
  }
  @keyframes rotateWheel {
    0% { 
      transform: rotate(0deg); 
      /* 确保初始状态与默认状态一致 */
      animation-timing-function: cubic-bezier(0.5, 0, 0.3, 1);
    }
    100% { 
      transform: rotate(-720deg); 
      /* 确保结束时速度趋近于零 */
      animation-timing-function: cubic-bezier(0.66, 0, 0.33, 1);
    }
  }

  .navigation {
    margin: 0 auto;
    position: relative;
    width: 40px;
    height: 18px;
  }

  .swiper-pagination {
    position: absolute;
    top: 236px;
  }
}
</style>