module.exports = {
  presets: ['@vue/app'],
  plugins: [
    '@babel/plugin-proposal-nullish-coalescing-operator',
    '@babel/plugin-proposal-optional-chaining',
    [
      'import',
      { libraryName: 'vant', libraryDirectory: 'es', style: true },
      'vant'
    ],
    [
      'component',
      {
        libraryName: 'element-ui',
        styleLibraryName: 'theme-chalk'
      }
    ]
  ]
  // 加上项目启不了
  // resolve: {
  //   alias: {
  //     '@': resolve('src')
  //   }
  // }
}
