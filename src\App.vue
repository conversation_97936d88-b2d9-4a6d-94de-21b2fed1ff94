<template>
  <div id="app">
    <component
      :is="currentHeader"
      v-show="isShowHeader"
      :style="{
        height: navigationBarHeight + 'px',
        'padding-top': statusBarHeight + 'px',
        'line-height': navigationBarHeight + 'px'
      }"
    />

    <div
      v-if="flag"
      class="view-wrapper"
      id="common-view-wrapper"
      :class="{ 'height-full': !isShowHeader }"
      :style="{ height: `calc(100% - ${headerHeight})` }"  >
      <keep-alive include="Configration,SelectModel,SeekStore,TestdriverCreate,ConfigrationContainer,detail" >
        <router-view
          class="children"
          @set-bottom="setBottom"
          @isBg="isBG"
          v-if="isReload"
        />
      </keep-alive>
    </div>
    <Loading />
    <LoadingNospinner />
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import wx from 'weixin-js-sdk'
import Header from '@/components/header.vue'
import NewHeader from '@/components/newHeader.vue'
import {
  setToken,
  setRefreshToken,
  removeToken,
  removeRefreshToken
} from '@/utils/auth'
import { getSourceId } from '@/api/api'
import {
  callNative,
  getUrlParamObj,
  getUserInfo,
  dispatchCallback
} from '@/utils/index'
import { getUserInfoFn } from '@/utils/bridgeApi'
import Loading from './components/loading.vue'
import LoadingNospinner from './components/loading-nospinner.vue'
import storage from './utils/storage'

const { env } = getUrlParamObj()

console.log('env', env)

/**
 * 为了不影响之前的 header ui 和业务
 * 创建一个路由表来兼容新版ui的header
 */
const showNewHeaderComponentRoute = [
  '/configration',
  '/configrationContainer',
  '/configrationInterior',
  '/configrationOption',
  '/configrationEquity'
]

export default {
  provide() {
    return {
      reload: this.reload,
      getUserInfoToken: this.getUserInfoToken,
      checkLoginFn: this.checkLoginFn
    }
  },
  data() {
    return {
      fromPath: '',
      flag: false,
      status: false,
      isReload: true,
      bottom: false,
      statusBarHeight: 0,
      navigationBarHeight: 50,
      headerHeight: '',
      isBg: false,
      isKeyboard: false,
      currentHeader: 'Header',

      docmHeight:
        document.documentElement.clientHeight || document.body.clientHeight,
      showHeight:
        document.documentElement.clientHeight || document.body.clientHeight
    }
  },
  components: {
    Header,
    NewHeader,
    Loading,
    LoadingNospinner,
  },
  created() {
    localStorage.removeItem('token')
    window.audiH5IsLogin = async () => {
      this.loginModeChange()
    }
    window.audiH5Refresh = async () => {
      this.loginModeChange()
    }
    // window.audiH5IsLogin()
  },
  async mounted() {
    // web使用
    // this.viewHeight = "50px";
    // this.childrenMinHeight = document.documentElement.clientHeight - 50 + "px";
    // localStorage.setItem("token", "Uj82ozQDTnwACwNu_t0y54yn5evmZi4s");

    // 调用的方法名与原生APP开发人员沟通好，
    // 注意：
    // 1.不管是APP调用js还是js调用APP方法，方法名需要沟通好否则接受不到
    // 2.原生APP调用js方法时，H5页面初始化的时候就需要调用后续APP调用js的方法。
    // 例如在mounted生命周期内调用getTimes这个方法，初始化不调用后期APP调用后H5接收不到

    this._getToken()
    this.getNativeDevice()
    this.nativeHeideLoading()
    this.$store.commit('showLoading')
    this.$store.commit('setEnv', env)
    if (env === 'minip') {
      this.$store.commit('setHeaderVisible', false)
    }
    window.onresize = () =>
      (() => {
        this.showHeight =
          document.documentElement.clientHeight || document.body.clientHeight
      })()
    let param = {}
    if (env === 'minip') {
      console.log('minip -----mounted--------')
      param = {
        app: 'MiniAPP',
        dealerCode: 'MiniAPP',
        dealerName: 'MiniAPP',
        name: 'MiniAPP'
      }

      if (!this.hisRoutes) {
        this.hisRoutes = []
      }
      const fullPath = this.$route.fullPath
      this.hisRoutes.push(fullPath)
    } else {
      param = {
        app: 'OneAPP',
        dealerCode: 'OneAPP',
        dealerName: 'OneAPP',
        name: 'OneAPP'
      }
    }
    const { data } = await getSourceId(param)
    this.$store.commit('setSourceId', data.data.sourceId)
  },
  computed: {
    ...mapState({
      isLogin: (state) => state.isLogin,
      isShowHeader: (state) => state.isShowHeader,
      loading: (state) => state.loading
    })
  },
  beforeRouteEnter(to, from, next) {
    console.log(from, 'fromfromfrom')
    next((vm) => {
      vm.fromPath = from.path
    })
  },
  methods: {
    ...mapMutations(['setDevice']),
    reload() {
      this.isReload = false
      this.$nextTick(() => {
        this.isReload = true
      })
    },
    // 跳转到登录页
    async getUserInfoToken() {
      await getUserInfoFn({ sync: 'authenticate' })
        .then((res) => {
          console.log('get user info', res)
          this.$storage.set('token', res.token)
          this.$storage.set('userId', res.userId)
          this.$EventBus.$emit('loginStatusChange', {}) // 刷新页面
          return true
        })
        .catch((err) => false)
    },
    // 检查是否需要跳转到登录页
    checkLoginFn(str = '') {
      const isToken = this.$storage.get('token')
      console.log('检查storage是否有Token:', isToken)
      if (!isToken) {
        console.log('没有token')
        if (str) return false
        const { env, from } = getUrlParamObj()
        if (env === 'minip') {
          if (from === 'srmip') {
            return wx.miniProgram.navigateTo({
              url: `/pages/single/relation-phone/index?from=h5&todo=login&href=${encodeURIComponent(
                window.location.href
              )}`
            })
          }
          wx.miniProgram.navigateTo({
            url: '/pages/binding-mobile/binding-mobile?from=web'
          })
        } else {
          this.getUserInfoToken() // 跳转到登录页
        }
        return false
      }
      return true
    },

    async loginModeChange() {
      console.log('===loginModeChange===')
      const { env, token } = getUrlParamObj()
      console.log('===loginModeChange==token=')
      if (env === 'minip') {
        console.log('===loginModeChange==minip=')
        const isLogin = +!!token
        if (isLogin != this.isLogin) {
          if (token) {
            console.log('===1==token=')
            this.$store.commit('setIsLogin', 1)
            storage.set('token', token)
          } else {
            this.$store.commit('setIsLogin', 0)
            storage.remove('token', token)
          }
          this.$EventBus.$emit('loginStatusChange', {})
        }
      } else {
        const userInfo = await getUserInfoFn({ sync: 'login' })
        console.log('查看登陆状态 ', userInfo)
        this.$store.commit('setIsLogin', +userInfo.isLogin)
        console.log('登录！！', +userInfo.isLogin !== this.isLogin)
        if (+userInfo.isLogin !== this.isLogin) {
          if (+userInfo.isLogin) {
            storage.set('token', userInfo.token)
          } else {
            console.log('未登录！！')
            storage.remove('token', userInfo.token)
            storage.remove('userId', userInfo.userId)
          }
          this.$EventBus.$emit('loginStatusChange', {})
        }
      }
    },

    // js调用native ==================================
    async _getToken() {
      const res = await callNative('getAudiUserInfo', {})
      console.log('getAudiUserInfo响应数据   :', JSON.stringify(res))
      // eslint-disable-next-line no-undef
      if (res?.token) {
        setToken(res.token)
        localStorage.setItem('token', res.token)
      }
      this.flag = true
      this.$store.commit('hideLoading')
    },
    async nativeHeideLoading() {
      // if (env === 'test') return
      await callNative('toggleLoading', { show: '0' })
    },

    // native调js =====================================
    // native调用js的方法 appCallJS, 需要注册
    appCall() {
      this.$bridge.dispatchMessageFromNative(
        'appCallJS',
        (datas, responseCallback) => {
          console.log(`获取app响应数据:${datas}`)
        }
      )
    },
    // 获取导航栏高度??
    // async getHeight() {
    //   // if (env === 'test') return
    //   const data = await callNative('navigationBarHeight', {})
    //   console.log('navigationBarHeight', data)
    //   this.statusBarHeight = data.statusBarHeight
    //   // 导航栏高度
    //   this.navigationBarHeight = data.navigationBarHeight

    //   this.headerHeight = `${data.statusBarHeight + data.navigationBarHeight}px`
    //   // this.childrenMinHeight = `${document.documentElement.clientHeight
    //   //       - data.navigationBarHeight
    //   // }px`
    // },

    // 获取（APP）设备信息
    async getNativeDevice() {
      try {
        const data = (await callNative('getDeviceInfo', {})) || {}
        /*
        nativeApp 原生app(Boolean), false 返回空
        system 操作系统及版本
        platform 客户端平台
        brand 设备品牌
        pixelRatio 设备像素比
        statusBarHeight 状态栏的高度，单位px
        navigationBarHeight 导航栏高度，单位px
        */
        if (data && Object.keys(data)?.length) {
          const { statusBarHeight, navigationBarHeight } = data
          // 导航栏高度
          if (statusBarHeight) {
            this.statusBarHeight = statusBarHeight
          }
          // 导航栏高度
          if (navigationBarHeight) {
            const navigationBarHeightValue =
              navigationBarHeight > 44 || navigationBarHeight < 40
                ? 44
                : navigationBarHeight
            data.navigationBarHeight = navigationBarHeightValue
            this.navigationBarHeight = navigationBarHeightValue
          }

          this.headerHeight = `${
            this.statusBarHeight + this.navigationBarHeight
          }px`
          this.setDevice(data)
          console.log('device', data)
        }
      } catch (error) {
        console.log('device 获取失败或者不支持', error)
      }
    },

    setBottom(bool) {
      this.bottom = bool
    },
    // 控制背景
    isBG(bool) {
      this.isBg = bool
    }
  },
  watch: {
    showHeight: function () {
      if (this.docmHeight > this.showHeight) {
        // 隐藏
        this.$store.commit('setShowBottomButton', false)
      } else {
        // 显示
        this.$store.commit('setShowBottomButton', true)
      }
    },
    $route: {
      handler: function (val, oldVal) {
        window.console.log('路由监听==========', val)
        console.log('app', val.path)

        // 根据路由更新header
        const isIncluded = showNewHeaderComponentRoute.includes(val.path)
        if (isIncluded) {
          this.currentHeader = 'NewHeader'
          console.log('NewHeader')
        } else {
          this.currentHeader = 'Header'
          console.log('header')
        }

        //purple项目加主题色
        if(val.query?.fromType === 'fromPurple'){
          this.$styleManager.addStyles(val.query.fromType);
          document.getElementById("app").style.background = '#191919'
        }else{
          this.$styleManager.removeStyles();
          document.getElementById("app").style.background = '#f9fafb'
        }

        const { env, token } = getUrlParamObj()
        if (env === 'minip') {
          const fullPath = this.$route.fullPath.split('?')[0]
          const hisIndex = this.hisRoutes.indexOf(fullPath)
          if (hisIndex === -1) {
            this.hisRoutes.push(fullPath)
          }
          this.loginModeChange()
          if (this.hisRoutes.length > 1 && hisIndex == -1) {
            const croute =
              fullPath.indexOf('?') != -1
                ? fullPath.substring(0, fullPath.indexOf('?'))
                : fullPath
            const lastIdx =
              hisIndex == -1 ? this.hisRoutes.length - 2 : hisIndex - 1
            const lastPath = this.hisRoutes[lastIdx]
            const lroute =
              lastPath.indexOf('?') != -1
                ? lastPath.substring(0, lastPath.indexOf('?'))
                : lastPath
            console.log('路由匹配 ', croute, lroute)
            if (croute == lroute) {
              window.history.go(-1)
            }
          }
        }
      },
      // 深度观察监听
      deep: true
    }
  }
}
</script>

<style lang="less">
html,
body {
  height: 100%;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
}

html,
body,
button,
input,
textarea {
  font-family: 'Audi-Normal';
}

#app {
  height: 100%;
  overflow: hidden;
}

.view-wrapper {
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
}

.height-full {
  height: 100% !important;
}

img {
  width: 100%;
  vertical-align: middle;
}

.text-hide {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
