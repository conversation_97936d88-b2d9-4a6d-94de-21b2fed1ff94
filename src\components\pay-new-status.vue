<template>
  <div class="isPayment">
    <!-- 订单的状态： //待付款  00，已取消 98，已付款 30，退款中 80，已退款 81  -->
    <div class="order-status">
      <div
        class="align-center"
      >
        <h3>{{ orderStatusMap.title }}</h3>
      </div>
      <div
        class="desc"
        v-if="orderStatusMap.desc"
      >
        {{ orderStatusMap.desc }}
      </div>
    </div>
  </div>
</template>

<script>
// import { mapState } from 'vuex'
import {
  ORDER_STATUS_DISTRICT, ENGINE_TYPE
} from '@/config/conf.data'

const PAY_STATUS_DESC = {
  98: {
    status: '已取消'
  },
  99: {
    status: '已关闭'
  },
  90: {
    status: '已完成'
  },
  80: {
    status: '退款中'
  },
  81: {
    status: '已退款'
  },
  '00': {
    status: '待支付意向金'
  },
  30: {
    status: '已支付意向金，待支付定金'
  },
  31: {
    status: '已支付定金'
  },
  301: {
    status: '已支付意向金，待支付定金'
  },
  32: {
    status: '待交车'
  },
  84: {
    status: '大定退款中'
  }
}

export default {
  props: {
    // 订单（分支）状态
    maxStatus: {
      type: Object,
      default: () => ({
        message: '',
        orderStatus: ''
      })
    },
    countDown: {
      type: Array,
      default: () => ([])
    },
    knapStatus: {
      type: String,
      default: ''
    },
    isOneStep: {
      type: Boolean,
      default: false
    },
    confirmStatus: {
      type: String ,
      default: ''
    },
    allowRefund:{
      type: Boolean,
      default: false
    },
    boolReceivePurchaseInvoice:{
      type: Boolean,
      default: false
    },
    orderStatus:{
      type: String,
      default: ''
    },
    boolConfigModifiable:{
      type: Boolean,
      default: false
    },
    boolPurchaseSignable:{
      type: Boolean,
      default: false
    },
    isAllSignedContract: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      payStatusDesc: PAY_STATUS_DESC,
      orderStatusMap: {},
      timeDown: []
    }
  },
  created() {

  },
  watch: {
    maxStatus: {
      handler(value) {
        this.handleInitStatus(value)
      },
      deep: true,
      immediate: true
    },
    countDown: {
      handler([hour, minute, second]) {
        const { orderStatusMap: { title }, maxStatus: { orderStatus } } = this
        const { LIFE_MAX_CYCLE } = ORDER_STATUS_DISTRICT
        if (title && (orderStatus === '00' || (this.isOneStep && orderStatus === '301'))) {
          this.orderStatusMap.title = `${LIFE_MAX_CYCLE[this.isOneStep && orderStatus === '301' ? 10 : 0].text}，还剩 ${hour}:${minute}:${second}`
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleInitStatus(statusMap) {
      const { LIFE_MAX_CYCLE } = ORDER_STATUS_DISTRICT
      const { BEVS } = ENGINE_TYPE
      const {
        message, seriesCode, paymentMethod, invalid
      } = statusMap
      let {
        status, orderStatus
      } = statusMap

      if (this.isOneStep && orderStatus === '301') {
        status = '30O'
      }
      status = status === '301' ? '30' : status
      orderStatus = orderStatus === '301' ? '30' : orderStatus
      const { knapStatus } = this
      // paymentMethod 10 => 全款，20=> 贷款

      console.log('%c [ orderStatus ]-141', 'font-size:14px; background:#cf222e; color:#fff;', orderStatus)
      if (orderStatus) {
        const STATUS = knapStatus || status?.toString() || orderStatus
        const [{ text, desc }] = LIFE_MAX_CYCLE.filter((i) => (i.status === (STATUS === '107' ? orderStatus : STATUS)))
        if(this.confirmStatus == '00') {
          this.orderStatusMap = {
            title: '退款审批中',
            desc: '申请退款流程进行中，审批完成前您也可以随时撤回申请'
          }
        }else if(this.allowRefund) {
          this.orderStatusMap = {
            title: '已开启退款入口',
            desc: '申请退款后开启审批流程，审批完成前您也可以随时撤回申请'
          }
        } else if((this.boolReceivePurchaseInvoice && this.orderStatus == '32') || (this.isAllSignedContract && (this.orderStatus == '31' || this.orderStatus == '32'))) {
          this.orderStatusMap = {
            title: '已签署购车协议',
            desc: '您已完成汽车购买协议签署，完成交车确认书签署及绑车后，可享受车主专属权益'
          }
        } else if(!this.boolConfigModifiable && (this.boolPurchaseSignable || !this.boolPurchaseSignable) && this.orderStatus == '31') {
          this.orderStatusMap = {
            title: '已支付定金',
            desc: '您的订单已锁定，完成合同签署后将为您匹配车辆资源'
          }
        } else if(this.orderStatus == '31') {
          this.orderStatusMap = {
            title: '已支付定金',
            desc: '您已成功支付定金'
          }
        } else {
          this.orderStatusMap = {
            title: message || text || '',
            desc: desc
          }
        }
        
        if (STATUS === '00') {
          this.orderStatusMap.desc = desc.replace(/COUNTDOWN_TIME/g, '1小时')
        } else if (['30'].includes(STATUS)) {
          this.orderStatusMap.desc = desc[BEVS.includes(seriesCode) ? 1 : 0]
        } else if ([ '101', '97IN'].includes(STATUS)) {
          this.orderStatusMap.desc = desc[paymentMethod !== '10' ? 1 : 0]
          // eslint-disable-next-line no-nested-ternary
          // this.orderStatusMap.desc = desc[['41', '101'].includes(STATUS) ? 0 : (paymentMethod !== '10' ? 1 : 0)]
        } else if (['97UP'].includes(STATUS)) {
          console.log('%c [ invalid ]-135', 'font-size:14px; background:#cf222e; color:#fff;', invalid, paymentMethod)
          if (invalid === 0) {
            this.orderStatusMap.desc = desc[invalid]
          } else if (invalid === 2) {
            this.orderStatusMap.desc = desc[paymentMethod !== '10' ? 2 : 3]
          } else if (invalid === 3) {
            this.orderStatusMap.desc = desc[1]
          }
        }
      }
    }

  }

}
</script>

<style scoped lang="less">
  .align-center {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .isPayment {
    // border-bottom: 1px solid #e5e5e5;
    padding: 27px 16px 10px;
    background: linear-gradient(180deg, #F0F0F0 0%, #FFFFFF 100%);
    position: relative;
    overflow: hidden;
    &::after {
      content: '';
      position: absolute;
      z-index: 0;
      bottom: 0;
      right: 32px;
      width: 138px;
      height: 32px;
      background: url("~@/assets/img/audi-logo-gray-gradient.png") no-repeat center/contain;
    }
    .order-status {
      position: relative;
      z-index: 2;
    }

    h3 {
      font-size: 14px;
      font-family: "Regular";
      color:#000000 ;
      line-height: 24px;
      font-weight: bold;
      margin: 0 0 8px 0;
    }

    .desc {
      font-family: "Regular";
      font-size: 10px;
      color: #000000;
      line-height: 18px;
      margin-bottom: 4px;
    }

    .timer {
      font-size: 10px;
      line-height: 18px;
    }
  }
</style>
