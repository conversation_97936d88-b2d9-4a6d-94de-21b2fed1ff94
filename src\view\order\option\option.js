/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-10-27 16:02:56
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-12-07 10:14:24
 * @FilePath     : \src\view\order\option\option.js
 * @Descripttion :
 */
import { CCPRO_OPTIONS } from '@/config/conf.data'

export default {
  name: 'FormsOption',
  props: {
    title: {
      type: String,
      default: '车辆信息'
    },
    collapse: {
      type: Boolean,
      default: true
    },
    spread: {
      type: Boolean,
      default: true
    },
    goods: {
      type: Object,
      default: () => ({})
    },
    optionList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      options: []
    }
  },
  watch: {
    optionList: {
      handler([insideColor, outsideColor, optionList]) {
        const { INSIDE, OUTSIDE } = CCPRO_OPTIONS
        const option = []
        const inside = []
        const outside = []
        if (optionList?.length) {
          optionList.forEach((i) => {
            if ([...INSIDE, ...OUTSIDE].includes(i.optionClassification)) {
              if ([...INSIDE].includes(i.optionClassification)) {
                i.typeName = '内饰'; inside.push(i)
              } else {
                i.typeName = '外饰'; outside.push(i)
              }
            } else {
              i.typeName = '选配'; option.push(i)
            }
            i.price = +i.optionPrice
          })
        }

        outside.push({ ...outsideColor, ...{ typeName: '外饰', price: +outsideColor?.price || 0 } })
        inside.push({ ...insideColor, ...{ typeName: '内饰', price: +insideColor?.price || 0 } })
        this.options = [...inside, ...outside, ...option].sort((a, b) => Math.abs(b.price) - Math.abs(a.price))

        console.log('%c [ this.options  ]-62', 'font-size:14px; background:#cf222e; color:#fff;', this.options)
      },
      deep: true,
      immediate: true
    }
  },
  created() {},
  methods: {
    handleSpreadEachOther() {
      this.spread = !this.spread
    }
  }
}
