import {
  getCarList,
  getModellineList,
  getSwitchModellineList,
  getExterior,
  getModelHub,
  getInterior,
  getInteriorChair,
  getInteriorEih,
  getInteriorSib,
  getPrivateOrder,
  getPacketItem,
  getPriceCompute,
  getMeasurePriceCompute,
  getDeliveryTimeCompute,
  getCcInfo,
  getSibColorInterieur,
  getOptionsInfoList,
  getCcConfigration,
  bestRecommendCar,
  getModelLineQuery,
  measureQuery, getStep2CC, getPacketEquity
} from '@/configratorApi/index'
import { getMonthWeek, getUrlParamObj, checkV2 } from '@/utils'
import V2Car from '@/utils/carBox'
import storage from '@/utils/storage'


export default {
  /**
   *  q5e 半定使用此接口
   *  idx / style 字段都可以砍掉
   */
  async initCarModelList({ commit, state }, {
    idx, urlModelLineId, style, $storage
  }) {
    commit('showLoading')
    await getCarList().then(async (res) => {
      if (res.data.code === '00') {
        const obj = res.data.data.find((e) => e.seriesName == ['A7L', 'Q5E', 'Q6'][idx])
        commit('setSelectCarIfo', {
          customSeriesId: obj.customSeriesId,
          seriesName: obj.seriesName,
          seriesCode: obj.seriesCode,
          deliveryTime: obj.deliveryTime
        })
        commit('hideLoading')
      }
    }).catch((error) => {
      console.log(error)
    })

    let responesBox = {}
    const type = $storage.getPlus('semi-definite') == '个性定制' ? 2 : 1
    // let urlPara = getUrlParamObj()
    // if(urlPara && urlPara.idx == 1 ) {
    //   type = 1
    // }

    // type 1 高  2半  3混
    // 获取配置线列表
    responesBox = await getModellineList(state.selectCarInfo.customSeriesId, type)
    console.log('%c 获取配置线列表', 'font-size:25px;color:red;', $storage.getPlus('semi-definite'))
    console.log(responesBox)
    const res = responesBox
    if (res.data.code === '00') {
      commit('setLineList', res.data.data)
      console.log('setLineListend')
      // 若存在配置单,则设置配置单中的车型为默认车型
      if (state.ccConfigration && state.ccConfigration.modelLineId) {
        const defaultModelLine = res.data.data.find(
          (item) => item.modelLineId === state.ccConfigration.modelLineId
        )
        defaultModelLine
          ? commit('setSelectCarIfo', {
            modelLineCode: defaultModelLine.modelLineCode,
            modelLineId: defaultModelLine.modelLineId,
            modelLineName: defaultModelLine.modelLineName,
            deliveryTime: defaultModelLine.deliveryTime,
            price: defaultModelLine.price
          })
          : commit('setSelectCarIfo', {
            modelLineCode: res.data.data[0]?.modelLineCode,
            modelLineId: res.data.data[0]?.modelLineId,
            modelLineName: res.data.data[0]?.modelLineName,
            deliveryTime: res.data.data[0]?.deliveryTime,
            price: res.data.data[0]?.price
          })
      } else if (urlModelLineId) {
        console.log('%c urlModelLineIdurlModelLineId', 'font-size:25px;color:green;', urlModelLineId)
        const queryRes = await getModelLineQuery(urlModelLineId)
        console.log('%c queryResqueryResqueryResqueryRes', 'font-size:25px;color:green;', queryRes)
        if (queryRes.data.code === '00') {
          commit('setSelectCarIfo', {
            modelLineCode: queryRes.data.data[0].modelLineCode,
            modelLineId: queryRes.data.data[0].modelLineId,
            modelLineName: queryRes.data.data[0].modelLineName,
            deliveryTime: queryRes.data.data[0].deliveryTime,
            price: queryRes.data.data[0].price
          })
        }
      } else {
        commit('setSelectCarIfo', {
          modelLineCode: res.data.data[0]?.modelLineCode,
          modelLineId: res.data.data[0]?.modelLineId,
          modelLineName: res.data.data[0]?.modelLineName,
          deliveryTime: res.data.data[0]?.deliveryTime,
          price: res.data.data[0]?.price
        })
      }

      commit('hideLoading')
    }
  },
  // 获取外饰列表
  async getExteriorList({ commit, state }) {
    await getExterior(state.selectCarInfo.modelLineId).then((res) => {
      console.log('%c外饰', 'font-size:40px;color:blue;', res)
      if (res.data.code === '00') {
        const colorList = res.data.data.filter((item) => item.status === 2 || item.status === 1)
        let defaultExColor
        commit('setExteriorList', colorList)
        // 如果存在配置单,则从配置单中获取外饰
        if (state.ccConfigration) {
          state.ccConfigration.customOptionVoList.forEach((item) => {
            const temp = colorList.find(
              (color) => color.optionId === item.optionId
            )
            temp && (defaultExColor = temp)
          })
          commit('setSelectExColor', defaultExColor || colorList[0])
        } else {
          defaultExColor = res.data.data.find((item) => item.defaultConfig === 1)
          commit('setSelectExColor', defaultExColor || colorList[0])
        }
      }
    })
  },
  // 获取轮毂列表
  async getModelHubList({ commit, state }) {
    await getModelHub(state.selectCarInfo.modelLineId).then((res) => {
      console.log('%c轮毂', 'font-size:40px;color:blue;', res)
      if (res.data.code === '00') {
        const hubList = res.data.data
        commit('setModelHubList', hubList)
        let defaultHub = hubList.find((item) => item.status === 1)

        if (state.ccConfigration) {
          state.ccConfigration.customOptionVoList.forEach((item) => {
            const temp = hubList.find(
              (color) => color.optionId === item.optionId
            )
            temp && (defaultHub = temp)
          })
        }
        defaultHub
          ? commit('setCurrentModelHub', defaultHub)
          : commit('setCurrentModelHub', hubList[0])
      }
    })
  },

  // 获取所有内饰(饰条,面料,座椅)
  async getAllInterior({ commit, state }, type) {
    await Promise.all([
      getInteriorChair(state.selectCarInfo.modelLineId),
      getSibColorInterieur(state.selectCarInfo.modelLineId),
      getInteriorEih(state.selectCarInfo.modelLineId)
      // getInteriorSib(state.selectCarInfo.modelLineId)
    ]).then((res) => {
      console.log('获取所有内饰(饰条,面料,座椅) 的接口出参 ：', res)
      res.forEach((item, index) => {
        if (item.data.code === '00') {
          switch (index) {
            case 0:
              const chairList = res[index].data.data
              let defaultChair = chairList.find((item) => item.status === 1)
              if (state.ccConfigration) {
                state.ccConfigration.customOptionVoList.forEach((item) => {
                  const temp = chairList.find(
                    (color) => color.optionCode === item.optionCode
                  )
                  temp && (defaultChair = temp)
                })
              }

              commit('setCurrentInteriorChair', defaultChair || chairList[0])
              commit('setInteriorChairList', chairList.filter((val) => val.status === 1 || val.status === 2))
              commit('setAllInteriorChairList', chairList)
              break
            case 1:
              const sibColorInterieurList = res[index].data.data
              commit('setSibColorInterieurList', sibColorInterieurList.filter((val) => val.status === 1 || val.status === 2))
              commit('setAllSibColorInterieurList', sibColorInterieurList)
              let defaultSibColorInterieur = sibColorInterieurList.find(
                (item) => item.status === 1
              )
              if (state.ccConfigration) {
                const temp = sibColorInterieurList.find(
                  (color) => color.sibInterieurId === state.ccConfigration.sibInterieurId
                )
                temp && (defaultSibColorInterieur = temp)
              }
              commit('setCurrentSibColorInterieur', defaultSibColorInterieur || sibColorInterieurList[0])
              break
            case 2:
              const eihList = res[index].data.data
              commit('setAllInteriorEihList', eihList)
              commit('setInteriorEihList', eihList)
              if (type === 'noeih') {
                return
              }
              let defaultEih = eihList.find((item) => item.status === 1)
              if (state.ccConfigration) {
                state.ccConfigration.customOptionVoList.forEach((item) => {
                  const temp = eihList.find(
                    (color) => color.optionId === item.optionId
                  )
                  temp && (defaultEih = temp)
                })
              }

              // console.log('setCurrentInteriorEih defaultEih', defaultEih)
              // console.log('setCurrentInteriorEih eihList',  eihList[0])
              // debugger
              defaultEih
                ? commit('setCurrentInteriorEih', defaultEih)
                : commit('setCurrentInteriorEih', eihList[0])
              break
            default:
              console.warn('getAllInterior default 获取座椅,面料,饰条异常!')
              break
          }
        }
      })
      commit('setFinishRequestInterior', true)
    })
  },

  async getPrivateOrderList({ commit, state }, { idx }) {
    await getPrivateOrder(state.selectCarInfo.modelLineId).then((res) => {
      console.log('%c私人订制选装包', 'font-size:40px;color:blue;', res)
      if (res.data.code === '00') {
        let optionalList; let allPrivateOrderList
        if (idx === '0') {
          // status === 2的为当前可选的
          // 青春：d9150277-7dbe-4840-aac3-2b7e010ead7f
          // 未来：5cd07835-f1c3-4be0-902a-13ba1d14360c
          const arr = ['5cd07835-f1c3-4be0-902a-13ba1d14360c', 'd9150277-7dbe-4840-aac3-2b7e010ead7f']
          if (arr.includes(state.selectCarInfo.modelLineId)) {
            optionalList = res.data.data
          } else {
            optionalList = res.data.data.filter((item) => item.status === 2)
          }

          if (state.ccConfigration) {
            state.ccConfigration.customOptionVoList.forEach((item) => {
              const temp = optionalList.find(
                (val) => val.optionId === item.optionId
              )
              temp
                && (state.currentOptionsList = [...state.currentOptionsList, temp])
            })
          }
          allPrivateOrderList = res.data.data.filter((item) => (item.status === 2 || item.status === 1))
          console.log('%c allPrivateOrderListallPrivateOrderList', 'font-size:25px;color:green;', allPrivateOrderList)
        } else if (idx === '1') {
          // status === 2的为当前可选的
          optionalList = res.data.data.filter((item) => (item.status === 2 || item.status === 1))

          if (state.ccConfigration) {
            state.ccConfigration.customOptionVoList.forEach((item) => {
              const temp = optionalList.find(
                (val) => val.optionId === item.optionId
              )
              temp
                && (state.currentOptionsList = [...state.currentOptionsList, temp])
            })
          }
          allPrivateOrderList = res.data.data.filter((item) => (item.status === 2 || item.status === 1))
          console.log('%c 私人定制列表数据：', 'font-size:25px;color:green;', allPrivateOrderList)
        } else if (idx === '2') {
          // status === 2的为当前可选的
          optionalList = res.data.data.filter((item) => (item.status === 2 || item.status === 1))

          if (state.ccConfigration) {
            state.ccConfigration.customOptionVoList.forEach((item) => {
              const temp = optionalList.find(
                (val) => val.optionId === item.optionId
              )
              temp
                && (state.currentOptionsList = [...state.currentOptionsList, temp])
            })
          }
          allPrivateOrderList = res.data.data.filter((item) => (item.status === 2 || item.status === 1))
          console.log('%c 私人定制列表数据：', 'font-size:25px;color:green;', optionalList)

          // YEB 轻装权益包，默认选中，用户不可直接取消选中
          // let YEA = optionalList.find(e => e.optionCode == "YEA");
          // if(YEA) commit('setCurrentOptionsList', [...state.currentOptionsList, YEA])
        }
        commit('setPrivateOrderList', optionalList)
        commit('setAllPrivateOrderList', allPrivateOrderList)
      }
    })
  },
  async getPacketItemInfo({ commit, state }) {
    await getPacketItem(
      state.selectCarInfo.modelLineId,
      state.clickOption.optionId
    ).then((res) => {
      console.log('%c装备包详情', 'font-size:40px;color:blue;', res)
      if (res.data.code === '00') {
        commit('setPacketItem', res.data.data)
      }
    })
  },

  // 半定 getMeasurePriceCompute
  // 高定 getPriceComputeAction
  async getMeasurePriceComputeAction({ commit, state }, optionIds) {
    let res
    const selOptionIds = Array.from(new Set(optionIds))
    if (checkV2()) {
      res = await getMeasurePriceCompute(state.selectCarInfo.modelLineId, selOptionIds)
    } else {
      res = await getPriceCompute(state.selectCarInfo.modelLineId, selOptionIds)
    }

    if (res.data.code === '00') {
      commit('setCurrentPrice', res.data.data)
    }
  },

  async getDeliveryTimeComputeAction({ commit, state }, optionIds) {
    await getDeliveryTimeCompute(state.selectCarInfo.modelLineId, optionIds).then(
      (res) => {
        if (res && res.data.code === '00' && res.data?.data?.estimateDate) {
          const strarr = res.data?.data?.estimateDate.split('-')
          const week = getMonthWeek(Number(strarr[0]), Number(strarr[1]), Number(strarr[2]))
          commit('setSelectCarIfo', {
            deliveryTime: `${strarr[0]}年${Number(strarr[1])}月第${week}周左右交付`
          })
        }
      }
    )
  },


  async getCcConfigrationAction({ commit, state }, ccid) {
    await getCcConfigration(ccid).then((res) => {
      console.log('%c根据ccid获取车型信息', 'font-size:40px;color:blue;', res)
      if (res.data.code === '00') {
        commit('setCcConfigration', res.data.data)
        // commit('setCcInfo', res.data.data)
      }
    })
  },

  // 设置推荐页当季热销的车型列表(未上线)
  async getBestRecommendCar({ commit, state }, { customSeriesId }) {
    const res = await bestRecommendCar(customSeriesId)
    console.log('虎年限定', res)
    if (res.data.code === '00') {
      const requestarr = []

      res.data.data.forEach((item) => {
        const colors = item.options.filter((option) => option.category === 'COLOR_EXTERIEUR')
        const hubs = item.options.filter((option) => option.category === 'RAD')

        item.outsideColor = colors[0]
        item.hub = hubs[0] || item.standardRad

        // item.imageUrl = `/ccpro-backend/a7lbest/${item.outsideColor.optionName}_${item.hub.optionName}.png`
        item.imageUrl = `/ccpro-backend/storebest/${item.modelLine.modelLineCode}_${item.hub.optionCode}_${item.outsideColor.optionCode}.png`

        const optionIds = []
        optionIds.push(item.modelLineSibInterieurVo.sibOptionId)
        optionIds.push(item.modelLineSibInterieurVo.interieurOptionId)
        const options = item.options
        if (options && options.length > 0) {
          options.forEach((e) => {
            optionIds.push(e.optionId)
          })
        }
        requestarr.push(getDeliveryTimeCompute(item.modelLine.modelLineId, optionIds))
      })

      const res1 = await Promise.all(requestarr)
      res1.forEach((e1, i1) => {
        if (e1.data.code === '00' && e1.data.data) {
          const strarr = e1.data.data.estimateDate.split('-')
          const week = getMonthWeek(Number(strarr[0]), Number(strarr[1]), Number(strarr[2]))
          res.data.data[i1].deliveryTime = `${strarr[0]}年${Number(strarr[1])}月第${week}周左右交付`
        } else {
          res.data.data[i1].deliveryTime = ''
        }
      })
      commit('setBestRecommendCarList', res.data.data)
    }
  },

  /**
   * 查询半定是否有库存
   * 200001 无库存
   */
  async measureQueryBefore({ commit, state }) {
    const res = await measureQuery(state.selectCarInfo.modelLineId, [])
    commit('setHasV2Car', res.data.code !== '200001')
  },
  // async packetEquity(commit, state) {
  //   const res = await getPacketEquity({ modelLineId: state.selectCarInfo.modelLineId })
  //   const packageData = res?.data?.data || []
  //   if (!packageData.length) return
  //   const { optionId } = getUrlParamObj()
  //   if (!optionId) return
  //   const optionObj = packageData.find((e) => e.optionId == optionId)
  //   commit('setCurrentOptionsList', [...state.currentOptionsList, optionObj])
  // },
  async measureQuery({ commit, state }, { step, noQuery }) {
    // 每一步都需要更新数据，但是只更新后面步骤的数据
    // step 1234567 对应 车型.外饰颜色.轮毂.座椅.面料.饰条.私人订制
    let optionIds = []
    if (step > 1) {
      optionIds.push(state.currentExColor.optionId)
    }
    if (step > 2) {
      optionIds.push(state.currentModelHub.optionId)
    }
    if (step > 3) {
      optionIds.push(state.currentInteriorChair.optionId)
    }
    if (step > 4) {
      optionIds.push(state.currentSibColorInterieur.sibOptionId)
      optionIds.push(state.currentSibColorInterieur.interieurOptionId)
    }
    if (step > 5) {
      optionIds.push(state.currentInteriorEih.optionId)
    }
    if (step > 6) {
      const tmpoptionIds = state.currentOptionsList.map((item) => item.optionId)
      optionIds = optionIds.concat(tmpoptionIds)
    }
    // let { optionId } = getUrlParamObj()
    // if(optionId) optionIds.push(optionId)
    optionIds = optionIds.filter((e) => e)
    console.log('step>>>>', `${step}${step}${step}${step}${step}${step}${step}${step}${step}${step}`)
    const res = await measureQuery(state.selectCarInfo.modelLineId, optionIds)
    console.log('measureQuery>>>', res)
    if (res.data.code === '00') {
      if (step === '1') {
        const colorList = res.data.data.colorExterieur
        commit('setExteriorList', colorList)
        commit('setSelectExColor', colorList[0])
      } else if (step === '2') {
        const hubList = res.data.data.rad
        commit('setModelHubList', hubList)
        commit('setCurrentModelHub', hubList[0])
      } else if (step === '3') {
        const chairList = res.data.data.vos

        const semiDefinite = storage.getPlus('semi-definite')
        if (semiDefinite === '个性定制') {
          chairList.forEach((e) => {
            e.status = 1
          })
        }

        commit('setInteriorChairList', chairList)
        commit('setCurrentInteriorChair', chairList[0] || {})
      } else if (step === '4') {
        const sibColorInterieurList = res.data.data.sibInterieur
        commit('setSibColorInterieurList', sibColorInterieurList)
        commit('setCurrentSibColorInterieur', sibColorInterieurList[0])
      } else if (step === '5') {
        const eihList = res.data.data.eih
        commit('setInteriorEihList', eihList)
        commit('setCurrentInteriorEih', eihList[0])
      } else if (step === '6') {
        const optionalList = res.data.data.personals
        const measureConfigCodeList = res.data.data.measureConfigCodeList
        console.log('#########setPrivateOrderList##########', optionalList)
        commit('setPrivateOrderList', optionalList)
        console.log(state.currentOptionsList)
        if (state.idx != 2) commit('setCurrentOptionsList', [])
        commit('setMeasureConfigCodeList', measureConfigCodeList)
        console.log('%c setMeasureConfigCodeList2', 'font-size:25px;color:green;', measureConfigCodeList)
      } else if (step === '7') {
        const optionalList = res.data.data.personals
        const measureConfigCodeList = res.data.data.measureConfigCodeList
        console.log('@@@@@@setPrivateOrderList>>>>>>>>>', optionalList)
        commit('setPrivateOrderList', optionalList)
        commit('setMeasureConfigCodeList', measureConfigCodeList)
        console.log('%c setMeasureConfigCodeList1', 'font-size:25px;color:green;', measureConfigCodeList)
      }
    }
  }
}
