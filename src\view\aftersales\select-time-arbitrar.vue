<template>
  <div class="_container">
    <selectTime :timelist="timeData" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import selectTime from './select-time.vue'

export default {
  components: { selectTime },
  data() {
    return {
      timeData: []
    }
  },
  mounted() {
    console.log(this.$route.query)
    const tempDate = new Date() // 获取今天的日期
    tempDate.setDate(tempDate.getDate()+1) // 今天的前N天的日期，N自定义
    const beginDate = `${tempDate.getFullYear()}-${tempDate.getMonth() + 1}-${tempDate.getDate()}`

    const tempDate2 = new Date() // 获取今天的日期
    tempDate2.setDate(tempDate2.getDate() + 31) // 今天的前N天的日期，N自定义
    const endDate = `${tempDate2.getFullYear()}-${tempDate2.getMonth() + 1}-${tempDate2.getDate()}`
    const timeDemo = [] // 获取数据
    for (let i = 0; i < 30; i++) {
      const day = dayjs(tempDate).add(i, 'day').format('YYYY-MM-DD')
      timeDemo.push({
        date: day,
        month: dayjs(tempDate).add(i, 'day').format('MM'),
        resources: []
      })
    }
    //type = TakeDatetime 取车时间   开始时间  takeDate
    //type = SendDatetime 送车时间   结束时间  sendDate
    
    let minute = null
    timeDemo.forEach((res) => {
      minute = '07:55'
      for (let i = 0; i < 109; i++) {
        minute = dayjs(`${res.date} ${minute}`).add(5, 'minute').format('HH:mm')
        let param = { startAt: minute, status: 1 }
        
        if(this.$route.query?.type === 'SendDatetime' && this.$route.query?.takeDate){  //选择送车时间  判断取车时间
          const startTime = dayjs(this.$route.query.takeDate).add(3, 'hour').format('YYYY-MM-DD HH:mm')
          const endTime = dayjs(`${res.date} ${minute}`).format('YYYY-MM-DD HH:mm')
          if(startTime > endTime) param.status = 2
        }
        if(this.$route.query?.type === 'TakeDatetime' && this.$route.query?.sendDate){  //选择取车时间 判断送车时间
          const startTime = dayjs(this.$route.query.sendDate).subtract(3, 'hour').format('YYYY-MM-DD HH:mm')
          const endTime = dayjs(`${res.date} ${minute}`).format('YYYY-MM-DD HH:mm')
          if(startTime < endTime) param.status = 2
        }
        res.resources.push(param)
      }
    })
    
    this.timeData = timeDemo
    console.log(timeDemo)
  },
  methods: {

  }
}
</script>

<style lang="less" scoped>

</style>
