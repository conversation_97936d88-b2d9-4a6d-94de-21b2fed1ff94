<template>
  <div class="container">
    <van-radio-group v-model="radioType">
      <van-radio
        name="1"
        style="font-size: 14px; height: 40px; align-items: center"
      >
        保养
        <template #icon="props">
          <img
            class="img-icon"
            :src="props.checked ? activeIcon : inactiveIcon"
          >
        </template>
      </van-radio>
      <van-radio
        name="2"
        style="font-size: 14px; height: 40px"
      >
        维修
        <template #icon="props">
          <img
            class="img-icon"
            :src="props.checked ? activeIcon : inactiveIcon"
          >
        </template>
      </van-radio>
      <van-radio
        name="3"
        style="font-size: 14px; height: 40px"
      >
        检查
        <template #icon="props">
          <img
            class="img-icon"
            :src="props.checked ? activeIcon : inactiveIcon"
          >
        </template>
      </van-radio>
      <van-radio
        name="4"
        style="font-size: 14px; height: 40px"
      >
        其他
        <template #icon="props">
          <img
            class="img-icon"
            :src="props.checked ? activeIcon : inactiveIcon"
          >
        </template>
      </van-radio>
    </van-radio-group>

    <div class="chat-input">
      <van-field class="textarea"
        type="textarea"
        rows="4"
        placeholder="选填，请输入补充说明"
        v-model="inputRemark"
        maxlength="70"
        show-word-limit
      />
    </div>

    <div class="bottom_style">
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onSubmit"
          :text="'提交'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { RadioGroup, Radio ,Field} from 'vant'
import AudiButton from '@/components/audi-button'

Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Field)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      inputRemark: '',
      radioType: 1,
      activeIcon: require('../../assets/img/radio_checked.png'),
      inactiveIcon: require('../../assets/img/radio_normal.png')
    }
  },

  async mounted() {
    const { serviceType, inputRemark } = this.$route.query
    this.radioType = serviceType
    this.inputRemark = inputRemark
  },

  methods: {
    // 确认
    onSubmit() {
      console.log('radio', this.radioType)
      let serviceName = ''
      if (this.radioType === '1') {
        serviceName = '保养'
      } else if (this.radioType === '2') {
        serviceName = '维修'
      } else if (this.radioType === '3') {
        serviceName = '检查'
      } else {
        serviceName = '其他'
      }

      this.$store.commit('saveSelectServiceType', {
        inputServiceRemark: this.inputRemark,
        serviceType: this.radioType,
        serviceName: serviceName
      })
      sessionStorage.setItem('typeChange', JSON.stringify(true))
      this.$router.back(-1)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.container {
  padding: 16px;
}
.img-icon {
  height: 20px;
  width: 20px;
  padding-bottom: 8px;
}
.chat-input {
  width: 100%;
  display: flex;
  padding-top: 10px;
  .textarea {
    width: 100%;
    height: 140px;
    background: #ffffff;
    border: 1px solid #e5e5e5;
    padding: 8px;
    font-size: 14px;
    color: #000000;
    border-radius: 0;
    -webkit-appearance: none;
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
}
.btn-delete-wrapper {
  margin: 0 16px;
}
</style>
