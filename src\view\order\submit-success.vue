<template>
  <div class="unsubscribeSucceed">
    <div class="circle">
      <van-icon
        name="success"
        size="20"
      />
    </div>
    <p>您的支付已提交</p>
    <p>审核通过后，服务人员将会同您取得联系</p>
    <div class="box">
      <div class="btnWarp">
        <div
          class="buttons"
          @click="confim"
        >
          确认完成
        </div>
        <div class="bt" />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Icon } from 'vant'

Vue.use(Icon)
export default {
  methods: {
    confim() {
      this.$bridge.callHandler('goHome', {}, (err, data) => {
        if (err) {
          // 发生错误
          // alert(err);
          console.log('goHomeError=', err)
          return
        } // 成功返回结果
        console.log('goHome=', data)
      })
    }
  }
}
</script>

<style scoped lang="less">
@import url("../../assets/style/scroll.less");
@import url("../../assets/style/buttons.less");
.unsubscribeSucceed {
  .circle {
    position: relative;
    width: 72px;
    height: 72px;
    border: 1px solid #000000;
    border-radius: 50%;
    margin: 166px auto auto auto;
    /deep/.van-icon-success {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      &::before {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        width: 20px;
        height: 18px;
        bottom: 0;
        margin: auto;
      }
    }
  }
  p {
    padding: 0 27px 0 29px;
    text-align: center;
    font-size: 16px;
    line-height: 28px;
    font-weight: 400;
  }

}
</style>
