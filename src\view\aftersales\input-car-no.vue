<template>
  <div>
    <div class="carno">
      <div class="carno-input">
        <div class="input-box">
          <li>{{ first }}</li>
          <li>{{ numArr[0] }}</li>
          <li>{{ numArr[1] }}</li>
          <li>{{ numArr[2] }}</li>
          <li>{{ numArr[3] }}</li>
          <li>{{ numArr[4] }}</li>
          <li>{{ numArr[5] }}</li>
          <li v-if="isNewEnergyCar">{{ numArr[6] }}</li>

          <li v-if="!isNewEnergyCar">{{ '新' }}</li>
        </div>
      </div>

      <div class="car-no-warp">
        <div
          class="car-no-list"
          v-for="(item, index) in carNoList"
          :key="index">
          <div class='car-no-text' @click.stop="onCarNo(item.text)">{{ item.text }}</div>
          <div class="a-right-close" @click.stop='deleteCareNo(item.text)'></div>
        </div>
      </div>

      <!-- 英文 数字 键盘 -->
      <div class="carno-keyboard">
        <div class="header" v-if="isShowConfirm">
          <span class="confirm" @click="confirmCarNo">确认</span>
        </div>
        <div class="plate_chinese_box" v-if="show_chinese">
          <div
            v-for="(item, index) in ChineseList"
            :key="item.id"
            @click="checkChinese(index)"
          >
            {{ item.name }}
          </div>
        </div>

        <div class="plate_number_box" v-if="show_allBoard">
          <div
            v-for="(item, index) in English_Number"
            :key="item.id"
            @click="checkEnglish_num(index)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { callNative } from '@/utils'
import storage from '../../utils/storage'
import { postBindCarNumber } from '@/api/api'
import Vue from 'vue'
import { Form, Field, Icon, Button } from 'vant'
Vue.use(Form).use(Button)
export default {
  data() {
    return {
      carNo: '',
      isNewEnergyCar: false,
      isShowConfirm: false, //是否显示确认按钮
      show_chinese: true, // 是否显示汉字键盘
      show_allBoard: false, // 是否显示英文数字键盘
      ChineseList: [
        { name: '京', id: 1 },
        { name: '津', id: 2 },
        { name: '渝', id: 22 },
        { name: '沪', id: 9 },
        { name: '冀', id: 3 },
        { name: '晋', id: 4 },
        { name: '辽', id: 6 },
        { name: '吉', id: 7 },
        { name: '黑', id: 8 },
        { name: '苏', id: 10 },
        { name: '浙', id: 11 },
        { name: '皖', id: 12 },
        { name: '闽', id: 13 },
        { name: '赣', id: 14 },
        { name: '鲁', id: 15 },
        { name: '豫', id: 16 },
        { name: '鄂', id: 17 },
        { name: '湘', id: 18 },
        { name: '粤', id: 19 },
        { name: '琼', id: 21 },
        { name: '川', id: 23 },
        { name: '贵', id: 24 },
        { name: '云', id: 25 },
        { name: '陕', id: 27 },
        { name: '甘', id: 28 },
        { name: '蒙', id: 5 },
        { name: '桂', id: 20 },
        { name: '青', id: 29 },
        { name: '宁', id: 30 },
        { name: '新', id: 31 },
        { name: '藏', id: 26 },
        { name: '使', id: 32 },
        { name: '领', id: 33 },
        { name: '警', id: 34 },
        { name: '港', id: 37 },
        { name: '澳', id: 38 },
        { name: '.', id: 99 }
      ],
      English_Number: [
        { name: '1', id: 28 },
        { name: '2', id: 29 },
        { name: '3', id: 30 },
        { name: '4', id: 31 },
        { name: '5', id: 32 },
        { name: '6', id: 33 },
        { name: '7', id: 34 },
        { name: '8', id: 35 },
        { name: '9', id: 36 },
        { name: '0', id: 37 },
        { name: 'Q', id: 38 },
        { name: 'W', id: 39 },
        { name: 'E', id: 40 },
        { name: 'R', id: 41 },
        { name: 'T', id: 42 },
        { name: 'Y', id: 43 },
        { name: 'U', id: 44 },
        { name: 'I', id: 45 },
        { name: 'O', id: 46 },
        { name: 'P', id: 47 },
        { name: 'A', id: 48 },
        { name: 'S', id: 49 },
        { name: 'D', id: 50 },
        { name: 'F', id: 51 },
        { name: 'G', id: 52 },
        { name: 'H', id: 53 },
        { name: 'J', id: 54 },
        { name: 'K', id: 55 },
        { name: 'L', id: 56 },
        { name: 'Z', id: 57 },
        { name: 'X', id: 58 },
        { name: 'C', id: 59 },
        { name: 'V', id: 60 },
        { name: 'B', id: 61 },
        { name: 'N', id: 62 },
        { name: 'M', id: 63 },
        { name: '.', id: 99 }
      ],
      first: '',
      numArr: [],
      carNoList: [],
      type: '',
      vin: ''
    }
  },
  mounted() {
    let carNo = storage.get('serviceCarNoList') || ''
    if (carNo) {
      let carNoList2 = carNo.split(',')
      for (let i = 0; i < carNoList2.length; i++) {
        if (carNoList2[i] && i < 3) {
          this.carNoList.push({ text: carNoList2[i] })
        }
      }
    }
    this.type = this.$route.query.type
    this.vin = this.$route.query.vin
  },
  methods: {
    // 删除历史车牌
    deleteCareNo (text) {
      let carNo = storage.get('serviceCarNoList') || ''
      if (carNo) {
        let arr = []
        let carNoList2 = carNo.split(',')
        let newCarNoList = carNoList2.filter(e => e !== text)
        newCarNoList.slice(0, 3).map(req => {
          arr.push({ text: req })
        })
        storage.set('serviceCarNoList', newCarNoList.join(','))
        this.carNoList = arr
      }
    },
    // 唤起键盘
    clickShowKeyboard() {
      if (!this.first) {
        this.show_chinese = true
      } else {
        this.show_chinese = false
        this.show_allBoard = true
      }
    },

    // 选择车牌号前面的汉字
    checkChinese(index) {
      // 如果点击删除键，删除第一个格的内容
      if (this.ChineseList[index].id === 99) {
        this.first = ''
      } else {
        // 把选中的字赋值给第一个格，并且切换键盘
        this.first = this.ChineseList[index].name
        this.show_chinese = false
        this.show_allBoard = true
      }
    },
    // 选择车牌号后面的数字和字母
    checkEnglish_num(index) {
      // 如果点击删除键，删除 numArr 的最后一个值
      if (this.English_Number[index].id === 99) {
        this.numArr.pop()
        // 如果 numArr 里面被删的没有值了，切换键盘
        if (this.numArr.length === 0) {
          this.show_chinese = true
          this.show_allBoard = false
        }
      } else {
        // 把选中的值 push 到 numArr 内
        this.numArr.push(this.English_Number[index].name)
        // 如果 numArr 中的值超过 7 个（车牌号的最大位数），删除最后一个
        if (this.numArr.length > 7) {
          this.numArr.pop()
        }
      }
      if (this.numArr.length === 8 || this.numArr.length === 7) {
        this.isShowConfirm = true
        this.isNewEnergyCar = true
      } else if (this.numArr.length === 6) {
        this.isShowConfirm = true
        this.isNewEnergyCar = false
      } else {
        this.isShowConfirm = false
        this.isNewEnergyCar = false
      }
    },
    // onNewEnergyCar(){
    //   this.isNewEnergyCar = !this.isNewEnergyCar
    // },
    onCarNo(carNo) {
      if (this.type === 'addCarNo') {
        //添加车牌号
        this.addCarNo(carNo)
      } else {
        storage.set('carNo', carNo)
        this.$router.back(-1)
      }
    },
    confirmCarNo() {
      if (
        this.first &&
        (this.numArr.length === 7 || this.numArr.length === 6)
      ) {
        let numStr = this.numArr.join('')
        this.carNo = this.first + numStr
        storage.set('carNo', this.carNo)

        var carNoList = storage.get('serviceCarNoList') || ''
        if (!carNoList) {
          storage.set('serviceCarNoList', this.carNo)
        } else {
          var no = this.carNo
          if (carNoList.indexOf(no) === -1) {
            storage.set('serviceCarNoList', no + ',' + carNoList)
          }
        }
        if (this.type === 'addCarNo') {
          //添加车牌号
          this.addCarNo(this.carNo)
        } else {
          this.$router.back(-1)
        }
      } else {
        callNative('toast', { type: 'fail', message: '请输入正确车牌号' })
      }
    },
    addCarNo(carNo) {
      // this.postBindCarNumber(carNo)
      // return
      //调用APP Dialog
      callNative('popup', {
        type: 'alert',
        alertparams: {
          title: '',
          desc: '车牌号绑定后不可修改，请确认绑定该车牌号。',
          actions: [
            {
              type: 'fill',
              title: '确定'
            },
            {
              type: 'stroke',
              title: '取消'
            }
          ]
        }
      }).then((data) => {
        if (data.type === 'fill') {
          // 点击确定
          this.postBindCarNumber(carNo)
        }
      })
    },

    async postBindCarNumber(carNo) {
      const param = {
        carNumber: carNo,
        vin: this.vin
      }

      const { data } = await postBindCarNumber(param)
      if (data.code === '200') {
        const carInfo = JSON.parse(sessionStorage.getItem('currentCar'))
        carInfo.plateNumber = carNo
        sessionStorage.setItem('currentCar', JSON.stringify(carInfo))

        this.$router.back(-1)
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.carno {
  height: auto;
  background-color: #ffffff;

  .header {
    width: 100%;
    height: auto;
    padding: 2vw 3vw;
    padding-bottom: 16px;

    font-size: 16px;
    color: #666666;
    display: flex;
    justify-content: right;

    .confirm {
      margin-right: 16px;
      align-items: center;
      justify-content: center;
      display: flex;
      width: 80px;
      height: 35px;
      color: #000000;
      background-color: #fff;
    }
  }

  &-input {
    width: 100%;
    height: 70px;
    display: flex;
    padding-top: 16px;
    justify-content: center;
    align-items: center;
    .input-box {
      width: 95%;
      // align-items: center;
      display: flex;
      justify-content: space-around;
      li {
        border: 1px solid rgba(206, 208, 210, 1);
        width: 10vw;
        height: 12vw;
        box-sizing: border-box;
        font-size: 4.33vw;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #323233;
      }
    }
  }

  &-keyboard {
    width: 100%;
    height: auto;
    padding-top: 16px;
    padding-bottom: 36px;
    background-color: #c5c5c5;
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: fixed;
    bottom: 0px;
    left: 0px;

    .plate_chinese_box {
      padding: 0 2vw;
      display: flex;
      justify-content: space-between;
      // align-items: center;
      flex-wrap: wrap;
      div {
        width: 7.8%;
        height: 40px;
        font-size: 4.6vw;
        box-sizing: border-box;
        color: #333333;
        margin: 1vw;
        background-color: #ffffff;
        font-family: 'Audi-Normal';
        display: flex;
        justify-content: center;
        align-items: center;
      }
      // div:hover {
      // background-color: #b6b6b6
      // }
      div:last-child {
        width: 38%;
        color: #ffffff;
        background: #ffffff url(../../assets/img/cancel-small.png) no-repeat
          center;
      }
    }

    .plate_number_box {
      background-color: #c5c5c5;
      padding: 0 2vw;
      display: flex;
      justify-content: space-between;
      // align-items: center;
      flex-wrap: wrap;
      div {
        width: 7.8%;
        height: 40px;
        font-size: 4.6vw;
        color: #333333;
        margin: 1vw;
        background-color: #ffffff;
        font-family: 'Audi-Normal';
        display: flex;
        justify-content: center;
        align-items: center;
      }
      // div:hover {
      // background-color: #b6b6b6
      // }
      div:last-child {
        width: 38%;
        color: #ffffff;
        background: #ffffff url(../../assets/img/cancel-small.png) no-repeat
          center;
      }
    }
  }
}
.car-no-warp{
  padding: 0 4px;box-sizing: border-box;
  .car-no-list{
    display: flex;align-items: center;justify-content: space-between;
    line-height: 42px;box-sizing: border-box;
    border-bottom: 1px solid #ddd;
    padding:8px 16px 0 16px;
    &:last-child{
      border-bottom: none;
    }
    .car-no-text{
      width: calc(100% - 62px);
    }
    .a-right-close{
      width: 42px;height: 42px;
      background: url(../../assets/img/cancel-small.png) no-repeat center;
    }
  }
}
</style>
