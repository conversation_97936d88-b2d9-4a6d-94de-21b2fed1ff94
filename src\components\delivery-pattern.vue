<template>
  <div class="delivery_mode">
    <div class="_box">
      <van-radio-group v-model="seletIndex">
        <van-cell-group>
          <van-cell
            :border="false"
            title="到店提车"
            clickable
            @click="getSeletIndex(1)"
          >
            <template #right-icon>
              <van-radio name="20">
                <template #icon="props">
                  <div :class="seletIndex === 1 ? 'radio-checked' : 'radio-unchecked' " />
                </template>
              </van-radio>
            </template>
          </van-cell>
          <van-cell
            :border="false"
            title="送车上门"
            clickable
            @click="getSeletIndex(2)"
          >
            <template #right-icon>
              <van-radio name="10">
                <template #icon="props">
                  <div :class="seletIndex === 2 ? 'radio-checked' : 'radio-unchecked' " />
                </template>
              </van-radio>
            </template>
          </van-cell>
          <div v-if="seletIndex === 2">
            <div
              class="_box-card-list border-bottom border-top"
              @click="cityShow = true"
            >
              <div style="font-size: 14px;">
                交车城市
              </div>
              <div class="_right">
                <div class="c-font14">
                  {{ cityValue ? cityValue.name:'请选择交车城市' }}
                </div>
                <img src="../assets/img/icon_20.png">
              </div>
            </div>
            <div
              class="_box-card-list border-bottom"
              @click="typeShow = true"
            >
              <div style="font-size: 14px;">
                运输服务类型
              </div>
              <div class="_right">
                <div class="c-font14">
                  {{ typeValue?typeValue.label:'请选择运输服务类型' }}
                </div>
                <img src="../assets/img/icon_20.png">
              </div>
            </div>
            <div
              class="_tips"
              style="font-size: 12px;color: #999999;line-height: 15px;letter-spacing: 1px;padding: 15px 16px;"
            >
              {{ typeValue.tips }}
            </div>
          </div>
        </van-cell-group>
      </van-radio-group>
    </div>

    <van-popup
      v-model="cityShow"
      position="bottom"
      :style="{ height: '48%' }"
    >
      <van-area
        :area-list="areaList"
        :value="dealerInfo && dealerInfo.cityCode"
        :columns-num="2"
        :visible-item-count="5"
        @confirm="onCityConfirm"
        @cancel="cityShow = false"
      />
    </van-popup>

    <van-popup
      position="bottom"
      :style="{ height: '45%' }"
    >
      <van-picker
        :visible-item-count="5"
        show-toolbar
        value-key="label"
        :columns="columns"
        @confirm="onTypeConfirm"
        @cancel="typeShow = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { updateAmsDeliverType, getNearestDealerList, afterServiceScGeocodeGeo } from '@/api/api'
import model from '@/components/model.vue'


import AudiButton from '@/components/audi-button'
import { areaList } from '@vant/area-data'
import { getdiscount2 } from '@/utils'
import { mapState } from 'vuex'

import Vue from 'vue'
import {
  Popup, Area, Picker, Toast, RadioGroup, Radio, Cell, CellGroup
} from 'vant'

Vue.use(Area)
Vue.use(Picker)
Vue.use(Popup)
Vue.use(Cell)
Vue.use(CellGroup)
Vue.use(Radio)
Vue.use(RadioGroup)

const codeType = ['00', '200']
export default {
  components: { AudiButton },
  data() {
    return {
      cityShow: false, // 省市区选择器
      areaList,
      cityValue: '', // 选中的城市
      seletIndex: 1, // 1到店提车 1上车上门

      typeShow: false, // 运输类型选择器
      columns: [{
        label: '同城送车上门',
        value: '2',
        tips: '代理商将会为您提供同城送车上门服务，相关运输费用将由您线下向代理商支付。'
      },
      {
        label: '常规异地交车',
        value: '3',
        tips: '相关运输费用将由上汽奥迪进行补贴，您无需支付任何费用。'
      },
      {
        label: '非常规异地交车',
        value: '4',
        tips: '由于您选择的代理商与您的交车城市属于非常规线路，相关运输费用将由您自行承担'
      }
      ],
      typeValue: '', // 选中的运输类型
      dealerInfo: null
    }
  },
  computed: mapState({
    dealerDetail: (state) => state.dealerInfo
  }),
  // 交车城市 是按照当前城市市政府的坐标
  // 代理商坐标是按照实际的
  watch: {
    cityValue(newValue, oldValue) {
      if (newValue && newValue.name === this.dealerInfo.cityName) {
        this.typeValue = {
          label: '同城送车上门',
          value: '2',
          tips: '代理商将会为您提供同城送车上门服务，相关运输费用将由您线下向代理商支付。'
        }
        this.setdeliveryPatten()
      } else if (newValue && newValue.name !== this.dealerInfo.cityName) {
        this.getLocationAddress()
      }
    }
  },
  mounted() {
    this.getDealerDetail()
  },
  methods: {
    async getDealerDetail() {
      const { dealerCode } = this.$route.query
      const res = await this.$store.dispatch('getDealerByCode', { dealerCode })
      this.dealerInfo = this.dealerDetail
      this.cityValue = { name: this.dealerInfo.cityName, code: this.dealerInfo.cityCode }
      this.getDealerLocation()
    },

    getDealerLocation() { // 获取订单代理商的地址坐标
      afterServiceScGeocodeGeo({
        address: this.dealerInfo.address
      }).then(res => {
        console.info('🚀 ~ file:select-user-address method: line:223 -----', res.data.data.data)
        let result = res.data.data.data
        this.dealerInfo.lat = result.geocodes[0].location.split(',')[1]
        this.dealerInfo.lng = result.geocodes[0].location.split(',')[0]
      })
    },

    getLocationAddress() { // 获取交车城市市政府的坐标  并拉取附近代理商列表
      afterServiceScGeocodeGeo({
        address: `${this.cityValue.name}人民政府`
      }).then(async res => {
        console.info('🚀 ~ file:select-user-address method: line:223 -----', res.data.data.data)
        let result = res.data.data.data
        this.cityValue.lat = result.geocodes[0].location.split(',')[1]
        this.cityValue.lng = result.geocodes[0].location.split(',')[0]

        const data = await getNearestDealerList({ latitude: this.cityValue.lat, longitude: this.cityValue.lng })
        const dealerlist = data.data.data
        const dealerlist1 = await Promise.all(dealerlist.map(async (i) => {
          const discount = await getdiscount2(this.cityValue.lng || 0, this.cityValue.lat || 0, i.longitude || 0, i.latitude || 0)
          return { ...i, discount: discount.toFixed(2) }
        }))
        const dealerlist2 = dealerlist1.sort((a, b) => parseFloat(a.discount) - parseFloat(b.discount))
        if (dealerlist2.length > 0 && dealerlist2[0].dealerName === this.dealerInfo.dealerName) {
          this.typeValue = {
            label: '常规异地交车',
            value: '3',
            tips: '相关运输费用将由上汽奥迪进行补贴，您无需支付任何费用。'
          }
        } else {
          this.typeValue = {
            label: '非常规异地交车',
            value: '4',
            tips: '由于您选择的代理商与您的交车城市属于非常规线路，相关运输费用将由您自行承担'
          }
        }
        this.setdeliveryPatten()
      })
    },

    onCityConfirm(e) {
      this.cityValue = e[1]
      this.cityShow = false
      this.$store.commit('updateDeliveryPattern', e)
    },
    onTypeConfirm(item, index) {
      this.typeShow = false
      this.typeValue = item
    },
    getSeletIndex(e) {
      this.seletIndex = e
    },

    setdeliveryPatten() {
      const param = {}
      if (this.seletIndex === 1) {
        param.deliverAddr = this.dealerInfo.cityCode
        param.deliverType = '1'
      }
      if (this.seletIndex === 2) {
        param.deliverAddr = this.cityValue.code
        param.deliverType = this.typeValue.value
      }
      this.$store.commit('updateDeliveryPattern', param)
    }
  }
}
</script>

<style lang='less' scoped>
  @import "../assets/style/common.less";

  .delivery_mode {
    width: 100%;
    font-family: "Audi-Normal";

    ._box {
      width: 100%;
      box-sizing: border-box;

      ._box-card {
        width: 100%;
        background: #FFFFFF;
        box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
        margin-bottom: 20px;

        img {
          width: 100%;
        }

        &-list {
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          height: 51px;
          margin: 0 16px;

          img {
            width: 24px;
          }

          ._right {
            display: flex;
            align-items: center;
            color: #666666;
          }
        }

        ._tips {
          font-size: 12px;
          color: #666666;
          line-height: 15px;
          letter-spacing: 1px;
          padding: 15px 16px;
        }
      }
    }
  }

  .border-bottom {
    border-bottom: 1px solid #E5E5E5;
  }
  .border-top {
    border-top: 1px solid #E5E5E5;
  }

  .radio-checked {
    height: 20px;
    width: 20px;
    border: 1px solid #ccc;
    border-radius: 50%;
    background-color: #000;
    background-clip: content-box;
    padding: 4px;
    box-sizing: border-box;
  }

  .radio-unchecked {
    height: 20px;
    width: 20px;
    border: 1px solid #ccc;
    border-radius: 50%;
    box-sizing: border-box;
  }
</style>
