// 打开弹窗设置默认值 start
let validCouponList = []
this.eticketList.forEach((element) => {
  if (element.freeze !== 1) {
    validCouponList.push(element)
  }
})
if (validCouponList.length > 0) {
  // 取车或者送车
  if (!vouchers) {
    if (!this.couponList.length) {
      this.eticketList[0].checked = true
      this.couponList = [validCouponList[0]]
      // 分开订单
      if (this.orderType === '20131010') {
        // 取车券
        this.takeIndexNo = validCouponList[0].couponEquityId
        this.takeCarMileage = validCouponList[0].mileage
        this.handleCouponName = validCouponList[0].mainTitle
      } else {
        // 送车券
        this.sendIndexNo = validCouponList[0].couponEquityId
        this.sendCarMileage = validCouponList[0].mileage
        this.handleCouponName = validCouponList[0].mainTitle
      }
    }
  }
  // 取送车 - 取车
  if (vouchers === 'takeCar') {
    let nData = {}
    if (!obj_take?.take) {
      if (!obj_send?.send) {
        nData = validCouponList[0]
      } else {
        let newList = validCouponList.filter(e => e.couponEquityId !== obj_send.couponEquityId)
        if (newList?.length) {
          nData = newList[0]
        }
      }
      obj_take = {
        ...nData,
        take: '1'
      }
      this.couponList.push(obj_take)
      this.takeIndexNo = obj_take.couponEquityId
      this.takeCouponName = obj_take.mainTitle
      this.takeCarMileage = obj_take.mileage
    }
  }
  if (vouchers === 'sendCar') {
    let nData = {}
    if (!obj_send?.send) {
      if (!obj_take?.take) {
        nData = validCouponList[0]
      } else {
        let newList = validCouponList.filter(e => e.couponEquityId !== obj_take.couponEquityId)
        if (newList?.length) {
          nData = newList[0]
        }
      }
      obj_send = {
        ...nData,
        send: '1'
      }
      this.couponList.push(obj_send)
      this.sendIndexNo = obj_send.couponEquityId
      this.sendCouponName = obj_send.mainTitle
      this.sendCarMileage = obj_send.mileage
    }
  }
}
// 打开弹窗设置默认值 end
