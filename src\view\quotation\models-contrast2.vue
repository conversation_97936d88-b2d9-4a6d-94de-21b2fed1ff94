<template>
  <div style="position: relative;height: 100%;width: 100vw;overflow-x: hidden;">
    <div
      class="_container"
      v-show="carlist.length>0"
      @touchstart="moveStart"
      @touchmove="onMove"
      @touchend="moveEnd"
      ref="initBoxIndexWith"
    >
      <div class="item-box">
        <div class="item-content">
          <div class="val-box">
            <div
              ref="initTitleIndex"
              class="val-title border-bottom border-right border-top"
              @click="onHide()"
            >
              <img
                v-if="isHide"
                src="../../assets/img/icon-checkbox-true.png"
                width="58"
                style="margin-bottom: 5px;"
              >
              <img
                v-else
                src="../../assets/img/icon-checkbox-false.png"
                width="58"
                style="margin-bottom: 5px;"
              >
              <div>隐藏相同</div>
            </div>
            <div
              ref="initValIndex"
              :style="{'left':scrollx + 'px'}"
              class="li-box border-bottom border-right border-top"
              v-for="(item,index) in carlist"
              :key="index"
            >
              <div class="carText1">
                {{ item.modelLineVo.modelLineName }}
              </div>
              <div class="carText2">
                {{ item.modelLineVo.price | prefixFormatPrice }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="item-box"
        v-for="(item,index) in parameter"
        :key="item.parameterType"
      >
        <div class="item-header-box">
          <div class="_title">
            {{ item.parameterTypeName }}
          </div>
        </div>
        <div class="item-content">
          <div
            class="val-box"
            v-for="(val,index2) in item.value"
            :key="index2"
            v-show="val.isIdent || isHide === false"
          >
            <div
              class="val-title border-bottom border-right"
              :class="[index2 === 0 || val.isIdent ? 'border-top':'']"
            >
              {{ val.parameterName }}
            </div>
            <div
              :style="{'left':scrollx + 'px'}"
              class="li-box border-bottom border-right"
              :class="[index2 === 0 || val.isIdent ? 'border-top':'']"
              v-for="(li,index3) in val.value"
              :key="index3"
            >
              {{ li.parameterValue ? li.parameterValue:'--' }}
            </div>
          </div>
        </div>
      </div>

      <div
        class="item-box"
        v-for="(item,index) in modelLineOption"
        :key="index"
      >
        <div class="item-header-box">
          <div class="_title">
            {{ item.optionTypeName }}
          </div>
          <div class="_label">
            <div class="_label-item">
              <img src="../../assets/img/icon-circular-sx.png">
              <div>标配</div>
            </div>
            <div class="_label-item">
              <img src="../../assets/img/icon-circular-kx.png">
              <div>选装</div>
            </div>
            <div class="_label-item">
              <img src="../../assets/img/icon-circular-red-kx.png">
              <div>已选配</div>
            </div>
            <div class="_label-item">
              <img src="../../assets/img/icon-line-black.png">
              <div>不适用</div>
            </div>
          </div>
        </div>
        <div class="item-content">
          <div
            class="val-box"
            v-for="(val,index2) in item.value"
            :key="index2"
            v-show="val.isIdent || isHide === false"
          >
            <div
              class="val-title border-bottom border-right"
              :class="[index2 === 0 || val.isIdent ? 'border-top':'']"
            >
              {{ val.optionName }}
            </div>
            <div
              :style="{'left':scrollx + 'px'}"
              class="li-box border-bottom border-right"
              :class="[index2 === 0 || val.isIdent ? 'border-top':'']"
              v-for="(li,index3) in val.value"
              :key="index3"
            >
              <img
                v-if="li.status === 0 && !li.selected"
                src="../../assets/img/icon-line-black.png"
              >
              <img
                v-if="li.status === 1 && !li.selected"
                src="../../assets/img/icon-circular-sx.png"
              >
              <img
                v-if="li.status === 2 && !li.selected"
                src="../../assets/img/icon-circular-kx.png"
              >
              <img
                v-if="li.selected"
                src="../../assets/img/icon-circular-red-kx.png"
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCodelLineConfigsCompare, getCarConfig } from '../../api/api.js'

export default {
  data() {
    return {
      parameter: [],
      modelLineOption: [],
      carlist: [],
      isHide: false, // 是否隐藏先沟通
      initLeftX: 0, // 小标题盒子宽度
      initRightX: 0, // 页面初始宽度
      scrollx: 0, // 位移的距离
      startX: 0, // 触摸起始x
      startY: 0, // 触摸起始y
      endX: 0, // 触摸结束x
      endY: 0, // 触摸结束y
      isMove: false // 是否可以滑动
    }
  },
  async mounted() {
    const { ccid } = this.$route.query
    const ccidlist = ccid.split(',')
    const res = await Promise.all( // 获取车的全部
      ccidlist.map((i, index) => getCodelLineConfigsCompare({ ccid: i }))
    )
    this.carlist = res.map((i, index) => i.data.data)
    console.log(this.carlist)
    const parameterlist = res.map((i) => i.data.data.parameter)
    const modelLineOption = res.map((i) => i.data.data.modelLineOption)
    this.getParameterlist(parameterlist)
    this.getModelLineOptionlist(modelLineOption)

    this.$nextTick((i) => {
      const boxright = this.$refs.initValIndex[this.carlist.length - 1].getBoundingClientRect().right
      if (boxright > document.body.clientWidth) {
        this.isMove = true
      }
    })
    console.log('mounted query:: ', this.$route.query)
  },
  methods: {
    moveStart(e) {
      if (this.isMove === true) {
        this.startX = e.targetTouches[0].pageX
        this.startY = e.targetTouches[0].pageY
      }
    },
    onMove(e) {
      if (this.isMove === true) {
        this.endX = e.targetTouches[0].pageX
        this.endY = e.targetTouches[0].pageY
        const dValueX = Math.abs(this.startX - this.endX)
        const dValueY = Math.abs(this.startY - this.endY)
        if (dValueX > dValueY) {} else { return }

        if (this.endX > this.startX) { // div向右边移动
          if (this.scrollx > 0 || this.scrollx === 0) {
            this.scrollx = 0
            return false
          }
          this.scrollx = this.endX - this.startX + this.scrollx
          this.startX = this.endX
        } else { // div向左边移动
          const boxright = this.$refs.initValIndex[this.carlist.length - 1].getBoundingClientRect().right
          if (boxright < document.body.clientWidth) {
            return false
          }
          this.scrollx = this.endX - this.startX + this.scrollx
          this.startX = this.endX
        }
      }
    },
    moveEnd(e) {
      if (this.isMove === true) {
        this.startX = this.endX = 0
        this.startY = this.endY = 0
        const boxright = this.$refs.initValIndex[this.carlist.length - 1].getBoundingClientRect().right
        // const boxleft = this.$refs.initValIndex[0].getBoundingClientRect().left
        if (boxright < document.body.clientWidth) {
          // this.scrollx = -(130 * (this.carlist.length - 3 ) + 90)
          return false
        }
      }
    },

    onHide() { // 隐藏相同事件
      this.isHide = !this.isHide
    },

    /*
       * 处理接口请求的数据 转成界面渲染格式
       */

    // 整车性能参数
    getParameterlist(list) {
      const parameter = []
      // 提取一级数组
      list.forEach((i, index) => {
        let parameterType = ''
        i.forEach((j) => {
          if (index === 0) {
            if (parameterType !== j.parameterType) {
              parameter.push({
                parameterTypeName: j.parameterTypeName,
                parameterType: j.parameterType,
                value: []
              })
              parameterType = j.parameterType
            }
          }
        })
      })
      // 二级数组
      list.forEach((i, index) => {
        i.forEach((j) => {
          if (index === 0) {
            parameter.forEach((m) => {
              if (m.parameterType === j.parameterType) {
                m.value.push({ parameterName: j.parameterName, value: [] })
              }
            })
          }
        })
      })
      // 三级数组
      parameter.forEach((m) => {
        m.value.forEach((z) => {
          list.forEach((i, index) => {
            i.forEach((j) => {
              if (m.parameterType === j.parameterType && z.parameterName === j.parameterName) { z.value.push(j) }
            })
          })
        })
      })
      // 隐藏相同
      parameter.forEach((i) => {
        i.value.forEach((j) => {
          const value = j.value[0].parameterValue
          j.isIdent = j.value.find((z) => z.parameterValue !== value)
        })
      })
      this.parameter = parameter
      this.$nextTick((i) => {
        this.initLeftX = this.$refs.initTitleIndex.clientWidth
        this.initRightX = this.$refs.initBoxIndexWith.clientWidth
      })
      // console.log(list)
      console.log(parameter)
    },
    // 所有配置
    getModelLineOptionlist(list) {
      const modelLineOption = []
      // 提取一级数组
      list.forEach((i, index) => {
        let optionType = ''
        i.forEach((j) => {
          if (index === 0) {
            if (optionType !== j.optionType) {
              modelLineOption.push({
                optionTypeName: j.optionTypeName,
                optionType: j
                  .optionType,
                value: []
              })
              optionType = j.optionType
            }
          }
        })
      })
      // 二级数组
      list.forEach((i, index) => {
        i.forEach((j) => {
          if (index === 0) {
            modelLineOption.forEach((m) => {
              if (m.optionType === j.optionType) {
                m.value
                  .push({ optionName: j.optionName, optionId: j.optionId, value: [] })
              }
            })
          }
        })
      })
      // 三级数组
      modelLineOption.forEach((m) => {
        m.value.forEach((z) => {
          list.forEach((i, index) => {
            i.forEach((j) => {
              if (m.optionType === j.optionType && z.optionId
                  === j.optionId) { z.value.push(j) }
            })
          })
        })
      })
      // 隐藏相同
      modelLineOption.forEach((i) => {
        i.value.forEach((j) => {
          const value = j.value[0].status
          j.isIdent = j.value.find((z) => z.status !== value)
        })
      })
      console.log('modelLineOption:: ', modelLineOption)
      this.modelLineOption = uniqueFunc(modelLineOption, 'optionTypeName')
      uniqueFunc(modelLineOption, 'optionTypeName').forEach((e) => {
        if (e.optionTypeName == '座椅/真皮') {
          console.log('=======座椅==============', e)
        }
      })
      function uniqueFunc(arr, uniId) {
        const res = new Map()
        return arr.filter((item) => !res.has(item[uniId]) && res.set(item[uniId], 1))
      }
      // console.log(list)
      console.log(modelLineOption)
    }
  }
}
</script>

<style lang='less' scoped>
  .border-bottom {
    border-bottom: 1px solid #e5e5e5;
  }

  .border-top {
    border-top: 1px solid #e5e5e5;
  }

  .border-right {
    border-right: 1px solid #e5e5e5;
  }

  div {
    box-sizing: border-box;
  }

  ._container {
    position: absolute;
    top: 0;
    left: 0;

    .item-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-flow: column;

      .item-header-box {
        width: 100vw;
        height: 56px;
        display: flex;
        align-items: center;
        padding-left: 16px;
        width: 100vw;

        ._title {
          width: 150px;
          font-size: 12px;
          color: #000000;
          font-family: 'Audi-WideBold';
        }

        ._label {
          display: flex;
          align-items: center;

          ._label-item {
            display: flex;
            align-items: center;
            margin-right: 8px;

            img {
              width: 10px;
            }

            div {
              font-size: 10px;
              color: #666666;
              margin-left: 8px;
              white-space: nowrap;
            }
          }
        }

      }

      .item-content {
        width: 100%;
        flex-flow: column;

        .val-box {
          width: 100%;
          display: flex;

          .val-title {
            width: 90px;
            max-width: 90px;
            font-size: 10px;
            line-height: 16px;
            padding: 12px 16px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-flow: column;
            flex: 1;
            color: #999999;
            background-color: white;
            z-index: 99;
          }

          .li-box {
            padding: 12px 10px;
            width: 130px;
            max-width: 130px;
            line-height: 16px;
            font-size: 12px;
            position: relative;
            font-family: 'Normal';
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-flow: column;

            .carText1 {
              line-height: 15px;
            }

            .carText2 {
              font-family: 'Audi-WideBold';
              line-height: 16px;
              margin-top: 12px;
            }

            .carText3 {
              font-size: 10px;
              color: #333;
              margin-top: 3px;
              line-height: 15px;
            }

            img {
              width: 10px;
            }
          }
        }
      }
    }
  }
</style>
