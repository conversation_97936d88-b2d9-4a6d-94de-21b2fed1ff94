<template>
  <div class="bg">
    <div class="wrapper-scroll">
      <div v-for="item in equityList" :key="item.name" class="card" v-show="item.level === 1">
        <div class="name-wrapper c-flex-between">
          <div class="name c-bold c-font14">· {{ item.desc }}</div>
          <div class="btn-detail c-font12" v-if="item.children?.length > 0" @click="showDetail(item)">查看详情</div>
        </div>

        <div class="list-wrpper c-font12">
          <div v-for="j in item.children" :key="j.name" class="list">
            <div class="list-name">· {{ j.detailTitle }}</div>
          </div>
        </div>
      </div>

      <div v-for="item in equityList" :key="item.name" class="card c-font12 law-text" v-show="item.level === 6">
        {{ item.desc }}
      </div>
    </div>
    <CommonFooter @click="nextPage"></CommonFooter>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import wx from 'weixin-js-sdk'
import { getUserRightsByCarModelId, getOtds } from '@/api/api'
import { getUrlParamObj } from '@/utils'
import CommonFooter from './components/commonFooter.vue'

const { env } = getUrlParamObj()

export default {
  name: 'EquIty',
  components: { CommonFooter },

  data() {
    return {
      equityList: [], // 权益列表
      pageStartTime: 0
    }
  },
  computed: {
    ...mapGetters(['currentCarType']),
    ...mapState({
      // ccid 和 skuid 使用全局的store
      ccid: (state) => state.ccid,
      skuid: (state) => state.skuid,
      carIdx: (state) => state.configration.carIdx,
      queryParams: (state) => state.configration.queryParams,
      currentVersion: (state) => state.configration.currentVersion,
      referConfigrationActiveTab: (state) => state.configration.referConfigrationActiveTab,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      configrationActiveTab: (state) => state.configration.configrationActiveTab
    })
  },
  watch: {
    configrationActiveTab(val) {
      if (val === 'equity') {
        this.pageStartTime = Date.now()
      }
    }
  },

  mounted() {
    this.getData()
    this.pageStartTime = Date.now()
  },
  methods: {

    // 获取权益数据
    async getData() {
      const { modelLineCode, modelYear, version } = this.currentModelLineData
      const { caseCode, orderId } = this.queryParams

      const code = ['YEA', 'YEG'].includes(caseCode) ? 'A' : ''
      const params = {
        carModelId: modelLineCode, //
        type: 1, // 1 || 2 || 4
        modelYear: modelYear,
        modelVersion: version,
        ...orderId ? { orderId } : {},
        ...code ? { caseCode: code } : {}
      }

      // 参数有更新
      const res = await getUserRightsByCarModelId(params)
      const data = JSON.parse(res.data.data.rights)
      this.equityList = data
    },

    showDetail(item) {
      // console.log(item)
      this.$store.commit('updateEquityDetail', item)
      this.clickEquitySensors('查看详情', item.desc) // 埋点

      this.$router.push({
        path: '/configrationEquityDetail'
      })
    },

    async nextPage() {
      this.clickEquitySensors('下一步') // 埋点

      const { orderStatus } = this.$route.query

      this.$store.commit('showLoading')
      await Promise.all([
        this.$store.dispatch('getSkuId'), // this.skuid
        this.$store.dispatch('getCCid', {
          orderStatus
        })
      ])
      this.$store.commit('hideLoading')

      if (['30', '00'].includes(orderStatus)) {
        this.toMoneyDetailPage()
        return
      }

      if (env === 'minip' && !this.$storage.get('token')) {
        this.openWebViewMinip()
      } else {
        this.toQuotationPage()
      }
    },

    // 跳转到报价单页面
    async toQuotationPage() {
      const {
        orderStatus, orderId, dealerCode, shoppingCartId
      } = this.$route.query

      this.$router.push({
        path: '/quotation',
        query: {
          ccid: this.ccid,
          skuid: this.skuid,
          orderStatus,
          orderId,
          dealerCode,
          shoppingCartId,
          idx: this.carIdx,
          customBack: 'newConfigration'
        }
      })
    },

    // 跳转小程序页面
    async openWebViewMinip() {
      const {
        orderStatus, orderId, dealerCode, shoppingCartId
      } = this.$route.query
      const ccid = this.ccid
      const skuid = this.skuid
      const idx = this.carIdx
      const query = {
        ccid,
        skuid,
        orderStatus,
        orderId,
        dealerCode,
        shoppingCartId,
        idx,
        customBack: 'newConfigration'
      }
      const { origin, pathname } = window.location
      const string = Object.keys(query).reduce((i, n) => i + (query[n] ? (`&${n}=${query[n]}`) : ''), '')
      const strp = string.substring(1, string.length)
      const url = encodeURIComponent(`${origin}${pathname}#/quotation?${strp}`)
      wx.miniProgram.navigateTo({ url: `/pages/web/index?idx=${idx}&url=${url}&skuid=${skuid}&dealerCode=${dealerCode}` })
    },

    // 进入详情页
    toMoneyDetailPage() {
      const { orderId } = this.$route.query
      this.$router.push({
        path: '/order/money-detail',
        query: { orderId }
      })
    },

    // 埋点
    clickEquitySensors(buttonName, desc = '') {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const tabMap = {
        exterior: '外观',
        interior: '内饰',
        option: '选装',
        equity: '权益'
      }
      const { engine, customSeriesName } = this.currentModelLineData
      let param = {
        source_module: 'H5',
        refer_tab_name: tabMap[this.referConfigrationActiveTab],
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        select_right: desc,
        button_name: buttonName
      }
      if (buttonName === '下一步') {
        param = {
          ...param,
          $event_duration: Math.floor((Date.now() - this.pageStartTime) / 1000)
        }
      }

      // console.log(param)
      this.$sensors.track('CC_CarConfiguration_Right_BtnClick', param)
    }
  }
}
</script>

<style lang="less" scoped>
// commonless
@import "../../assets/style/common.less";

.bg {
  background-color: #F2F2F2;
  padding-bottom: 140px;
  min-height: 70%;
}
.wrapper-scroll {
  overflow-y: auto;
  height: calc(100vh - @HeaderHeight - @TabHeight - @FooterHeight);
}

.card {
  background-color: #fff;
  margin-bottom: 8px;
  padding: 12px 16px;

  .name-wrapper {
    line-height: 26px;

    .btn-detail {
      color: #333333;
    }
  }

  .list-wrpper {
    // color:
    margin-top: 6px;
    >.list {
      margin-top: 8px;
      .list-name {
        line-height: 20px;
        opacity: 0.5;
      }
    }
  }

  &.law-text {
    padding-bottom: 30px;
  }
}
</style>
