<template>
  <div class="container">
    <div class="agent-list">
      <div class="_content">
        <div class="_logo">
          <img src="../../assets/img/car.png">
        </div>
        <div class="_info">
          <div class="_title text-hide">
            上海安亭汽车城体验中心
          </div>
          <div class="_rate">
            <div>评分：</div>
            <div>
              <van-rate v-model="value" disabled void-color="#E5E5E5" disabled-color="#000000" />
            </div>
            <div>4.0</div>
          </div>
          <div class="_address">
            上海市嘉定区安亭镇安驰路888号
          </div>
        </div>
        <div class="distance">
          <img src="../../assets/img/0.png">
          <div>150km</div>
        </div>
      </div>
      <div class="_tel">
        <img src="../../assets/img/phone.png">400-2222
      </div>
      <div class="_label">
        <div>代上牌</div>
        <div>试乘试驾</div>
      </div>
    </div>
    <div class="agent-list">
      <div class="_content">
        <div class="_logo">
          <img src="../../assets/img/car.png">
        </div>
        <div class="_info">
          <div class="_title text-hide">
            上海安亭汽车城体验中心
          </div>
          <div class="_rate">
            <div>评分：</div>
            <div>
              <van-rate v-model="value" disabled void-color="#E5E5E5" disabled-color="#000000" />
            </div>
            <div>4.0</div>
          </div>
          <div class="_address">
            上海市嘉定区安亭镇安驰路888号
          </div>
        </div>
        <div class="distance">
          <img src="../../assets/img/0.png">
          <div>150km</div>
        </div>
      </div>
      <div class="_tel">
        <img src="../../assets/img/phone.png">400-2222
      </div>
      <div class="_label">
        <!-- <div>代上牌</div><div>试乘试驾</div> -->
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Rate } from 'vant'

Vue.use(Rate)

export default {
  name: 'AgentList',
  data() {
    return {
      value: 3
    }
  },
 
  mounted() {

  },
  methods: {

  }
}
</script>

<style lang='less' scoped>
div {
  box-sizing: border-box;
}

.text-hide {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.container {
  display: flex;
  flex-flow: column;
  width: 100%;
  margin: 0;
  padding: 0 16px;
  border-top: 1px #F2F2F2 solid;
  color: #000000;
  padding-bottom: 50px;

  .agent-list {
    border-bottom: 1px #F2F2F2 solid;
    width: 100%;
    padding: 16px 0;

    ._content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      ._logo {
        width: 80px;

        img {
          width: 80px;
          height: 80px;
        }
      }

      ._info {
        width: calc(100% - 80px - 30px);
        padding: 7px 15px;

        ._title {
          font-size: 16px;
          font-weight: 500;
          color: #000000;
          line-height: 21px;
          width: 100%;
        }

        ._rate {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #333333;
          line-height: 14px;
          margin-top: 5px;

          /deep/.van-rate {
            margin: 0 5px;
          }
        }

        ._address {
          width: 100%;
          font-size: 12px;
          color: #000000;
          line-height: 12px;
          margin-top: 16px;
        }
      }

      .distance {
        min-width: 30px;
        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;

        img {
          width: 30px;
          height: 30px;
        }

        div {
          font-size: 12px;
          color: #B2B2B2;
          line-height: 19px;
          margin-top: 3px;
        }

      }
    }

    ._tel {
      font-size: 12px;
      color: #000000;
      display: flex;
      align-items: center;
      padding-left: 93px;
      width: 100%;
      margin-top: 10px;

      img {
        width: 11px;
        height: 11px;
        margin-right: 5px;
      }
    }

    ._label {
      padding-left: 93px;
      display: flex;
      align-items: center;
      width: 100%;
      margin-top: 16px;

      div {
        height: 24px;
        border: 1px solid #999999;
        padding: 0 6px;
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #999999;
        margin-right: 8px;
      }
    }
  }
}
</style>
