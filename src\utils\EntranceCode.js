/*
 * @Author: lizhe <EMAIL>
 * @Date: 2022-07-11 20:50:30
 * @LastEditors: lizhe <EMAIL>
 * @LastEditTime: 2022-07-11 22:05:44
 * @FilePath: \audi-order-h5\src\view\configration\enter_code\EntranceCode.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
  // 入口常量
Entrance.Code = {
    /** 半订制入口 */
    'ONEAPP_MEASURE': 'ONEAPP_MEASURE',
    /** 私人高定入口 */
    'ONEAPP_PERSONAL': 'ONEAPP_PERSONAL',
    /** 爆款推荐入口 */
    'ONEAPP_RECOMMEND': 'ONEAPP_RECOMMEND',
    /** 虎头车入口 */
    'ONEAPP_YEARLY_RECOMMEND': 'ONEAPP_HQ_RECOMMEND',
    /** 经销商特选车入口 */
    'ONEAPP_AGENT_RECOMMEND': 'ONEAPP_AGENT_RECOMMEND',
    /** 相似车 */
    'ONEAPP_ESTIMATE': 'ONEAPP_ESTIMATE',
};

export function Entrance(code) {
    return new Entrance(code)
}



// 没有正确入口生成不了配置单。
// 配置单入口统一：
// 半订制入口: ONEAPP_MEASURE
// 私人搞定入口: ONEAPP_PERSONAL
// 爆款推荐入口: ONEAPP_RECOMMEND
// 当季限定入口: ONEAPP_HQ_RECOMMEND
// 经销商特选车入口: ONEAPP_AGENT_RECOMMEND


// minip：
// 半订制入口: MINIP_MEASURE
// 私人搞定入口: MINIP_PERSONAL
// 爆款推荐入口: MINIP_RECOMMEND
// 当季限定入口: MINIP_HQ_RECOMMEND
// 经销商特选车入口: MINIP_AGENT_RECOMMEND
