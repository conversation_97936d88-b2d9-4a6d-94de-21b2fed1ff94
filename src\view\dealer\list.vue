<template>
  <div class="container">
    <div
      class="item-wrapper"
      v-for="item,idx in dealerList"
      :key="idx"
      @click="chooseDealer(item)"
    >
      <div class="img-wrapper">
        <img
          :src="item.thumbnailUrl.includes('http') ?item.thumbnailUrl : ossUrl + item.thumbnailUrl"
          alt=""
        >
      </div>

      <div class="content-wrapper flex1">
        <div class="c-font16 c-bold">
          {{ item.dealerName }}
        </div>
        <div style="margin-top: 4px;" />
        <div class="c-font12">
          {{ item.dealerAdrress }}
        </div>

        <div style="margin-top: 15px;" />
        <div class="c-font12">
          <img
            class="phone-icon"
            :src="require('@/assets/img/dealer-phone.png')"
            alt=""
          >
          {{ item.dealerPhone }}
        </div>
      </div>

      <div class="navgation-wrapper">
        <div>
          <div
            class="nav-icon"
            v-show="item.distance !== 0"
            @click="toNavigation(item.longitude, item.latitude, item.dealerName)"
          >
            <img
              :src="require('@/assets/img/nav.png')"
              alt=""
            >
          </div>
          <span
            class="c-font12"
          >
            {{ item.distance | formatDistance }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import { Promise } from 'q'
import pinyin from 'js-pinyin'
import { getDealerList, getNearestDealerList } from '@/api/api'
import baseUrl from '@/config/url'
import { callNative, getdiscount2 } from '@/utils'


const latitude = '31.2304'
const longitude = '121.4737'

export default {
  data() {
    return {
      ossUrl: baseUrl.BaseOssHost,
      dealerList: [],
      location: '' // 模拟本地的经纬度信息.
    }
  },

  async mounted() {
    const data = await callNative('getLocationCity', {})
    this.location = data.location || `${latitude},${longitude}`
    console.log('getLocationCity', location)
    this.getDealerList(this.location)
  },

  methods: {
    async getDealerList(location) {
      //! v5.0 购车城市与门店 【是否保留原有定位】，扩展根据当前选择城市为主
      const { cid } = this.$route?.query || ''
      console.log('route.query', location)
      let params = {}
      // if (location !== '') {
      //   params = { ...{ latitude: location.split(',')[0], longitude: location.split(',')[1] }, ...params }
      // }
      if (cid) {
        params = {
          ...{ cityCode: cid }, ...params, ifRegionCodeByCity: 1, defaultHeadquarters: 1
        }
      }

      const { data } = await getNearestDealerList(params)
      if (data.code === '00') {
        const headquarters = data.data.filter((i) => i.dealerCode === '76600019')
        this.dealerList = data.data.filter((i) => i.dealerCode !== '76600019')
        // 高德api计算两点之间的距离
        // const location = this.location.split(',')
        // const results = await Promise.all(this.dealerList.map(async (dealer) => {
        //   if (dealer.latitude && dealer.longitude) {
        //     console.log('距离计算 ', location[1], location[0], dealer.longitude, dealer.latitude)
        //     return getdiscount2(location[1], location[0], dealer.longitude, dealer.latitude)
        //   }

        //   return new Promise((resolve) => {
        //     resolve(0)
        //   })
        // }))

        // this.dealerList.forEach((dealer, i) => {
        //   dealer.distance = results[i]
        // })
        // console.log(this.dealerList)
        this.dealerList = this.dealerList.map((i) => {
          i.pinyin = pinyin.getFullChars(i.dealerName)
          return i
        })
        this.dealerList.sort(this.compare('pinyin'))
        this.dealerList = [...this.resort(this.dealerList), ...headquarters]
        console.log(this.dealerList)
      }
    },

    getOtherPoint({ latitude, longitude }) {
      if (latitude && longitude) {
        return `${latitude},${longitude}`
      }
      return `${latitude},${longitude}`
    },


    chooseDealer(item) {
      const { isluckybag } = this.$route.query
      this.$store.commit('updateDealerInfo', item)
      // 福袋车确认单页面更改代理商重置一下交车方式
      if (isluckybag) {
        this.$store.commit('updateDeliveryPattern', {})
      }
      this.$router.back(-1)
    },

    compare(prop) {
      return function (obj1, obj2) {
        const val1 = obj1[prop]
        const val2 = obj2[prop]
        if (val1 === null || val2 === null) {
          return val1 === null ? 1 : -1
        } if (val1 < val2) {
          return -1
        } if (val1 > val2) {
          return 1
        }
        return 0
      }
    },

    resort(dealerList) {
      const idx = dealerList.findIndex((item) => item.distance !== 0)
      const truncate = dealerList.slice(0, idx)
      dealerList.splice(0, idx)
      return dealerList.concat(truncate)
    },

    toNavigation(longitude, latitude, dealerName) {
      callNative('navigationMap', {
        lat: latitude.toString(),
        long: longitude.toString(),
        des: dealerName
      })
    }

  }


}
</script>

<style lang="less" scoped>
  @import "../../assets/style/common.less";

  .container {
    padding: 0 16px;
  }

  .flex1 {
    flex: 1;
  }

  .item-wrapper {
    .c-flex-between;
    border-bottom: 1px solid #e5e5e5;
    padding: 18px 0;
  }

  .phone-icon {
    width: 12px;
    vertical-align: baseline;
  }

  .img-wrapper {
    width: 24%;
  }
  .content-wrapper {
    .c-flex-between;
    flex-direction: column;
    margin-left: 15px;
    padding: 5px 0;
  }
  .navgation-wrapper {
    .c-font14;
    .c-flex-center;
    color: #b2b2b2;
    text-align: center;
    .nav-icon {
      width: 24px;
      margin: 0 auto 5px auto;
    }
  }
</style>
