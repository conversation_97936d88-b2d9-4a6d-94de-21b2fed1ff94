@font-face {
    font-family: "Audi-ExtendedBold";
    src: url('https://audi-oss.saic-audi.mobi/audicc/app/font/AudiTypeGB-WideBold.ttf')
}

@font-face {
    font-family: "Audi-Normal";
    src: url('https://audi-oss.saic-audi.mobi/audicc/app/font/AudiTypeGB-Normal.ttf')
}

@font-face {
    font-family: "Audi-WideBold";
    src: url('https://audi-oss.saic-audi.mobi/audicc/app/font/AudiTypeGB-WideBold.ttf')
}

@font-face {
    font-family: "medium";
    src: url('https://audi-oss.saic-audi.mobi/audicc/app/font2/DFPKingGothicGB-Medium.ttf')
}
@font-face {
    font-family: "Light";
    src: url('https://audi-oss.saic-audi.mobi/audicc/app/font2/DFPKingGothicGB-Light.ttf')
}
@font-face {
    font-family: "Regular";
    src: url('https://audi-oss.saic-audi.mobi/audicc/app/font2/DFPKingGothicGB-Regular.ttf')
}
@font-face {
    font-family: "Semibold";
    src: url('https://audi-oss.saic-audi.mobi/audicc/app/font2/DFPKingGothicGB-Semibold.ttf')
}


*:not(input, textarea) {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -o-user-select: none;
}

/* flex box */
[data-flex] {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

[data-flex]>* {
    display: block
}

[data-block] {
    flex: 1
}

[data-flex]>[data-flex] {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

[data-flex~="box:wrap"] {
    flex-wrap: wrap
}

[data-flex~="box:reverse"] {
    flex-wrap: wrap-reverse
}

[data-flex~="dir:left"] {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row
}

[data-flex~="dir:right"] {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -webkit-flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse;
    -webkit-box-pack: end
}

[data-flex~="dir:top"] {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column
}

[data-flex~="dir:bottom"] {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
    -webkit-box-pack: end
}

[data-flex~="main:left"] {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

[data-flex~="main:right"] {
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

[data-flex~="main:justify"] {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between
}

[data-flex~="main:center"] {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center
}

[data-flex~="cross:top"] {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start
}

[data-flex~="cross:bottom"] {
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    -ms-flex-align: end;
    align-items: flex-end
}

[data-flex~="cross:center"] {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center
}

[data-flex~="cross:baseline"] {
    -webkit-box-align: baseline;
    -webkit-align-items: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
}

[data-flex~="cross:stretch"] {
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
}
[data-flex~="box:top"] {
    -webkit-align-self: flex-start;
    -ms-align-self: flex-start;
    align-self: flex-start
}
[data-flex~="box:bottom"] {
    -webkit-align-self: flex-end;
    -ms-align-self: flex-end;
    align-self: flex-end
}

[data-flex~="box:mean"]>*,
[data-flex~="box:first"]>*,
[data-flex~="box:last"]>*,
[data-flex~="box:justify"]>* {
    width: 0;
    height: auto;
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-flex-shrink: 1;
    -ms-flex-negative: 1;
    flex-shrink: 1
}

[data-flex~="box:first"]>:first-child,
[data-flex~="box:last"]>:last-child,
[data-flex~="box:justify"]>:first-child,
[data-flex~="box:justify"]>:last-child {
    width: auto;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0
}

[data-flex~="dir:top"][data-flex~="box:mean"]>*,
[data-flex~="dir:top"][data-flex~="box:first"]>*,
[data-flex~="dir:top"][data-flex~="box:last"]>*,
[data-flex~="dir:top"][data-flex~="box:justify"]>*,
[data-flex~="dir:bottom"][data-flex~="box:mean"]>*,
[data-flex~="dir:bottom"][data-flex~="box:first"]>*,
[data-flex~="dir:bottom"][data-flex~="box:last"]>*,
[data-flex~="dir:bottom"][data-flex~="box:justify"]>* {
    width: auto;
    height: 0;
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-flex-shrink: 1;
    -ms-flex-negative: 1;
    flex-shrink: 1
}

[data-flex~="dir:top"][data-flex~="box:first"]>:first-child,
[data-flex~="dir:top"][data-flex~="box:last"]>:last-child,
[data-flex~="dir:top"][data-flex~="box:justify"]>:first-child,
[data-flex~="dir:top"][data-flex~="box:justify"]>:last-child,
[data-flex~="dir:bottom"][data-flex~="box:first"]>:first-child,
[data-flex~="dir:bottom"][data-flex~="box:last"]>:last-child,
[data-flex~="dir:bottom"][data-flex~="box:justify"]>:first-child [data-flex~="dir:bottom"][data-flex~="box:justify"]>:last-child {
    height: auto;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0
}

[data-flex-box="0"] {
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0
}

[data-flex-box="1"] {
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-flex-shrink: 1;
    -ms-flex-negative: 1;
    flex-shrink: 1
}

[data-flex-box="2"] {
    -webkit-box-flex: 2;
    -webkit-flex-grow: 2;
    -ms-flex-positive: 2;
    flex-grow: 2;
    -webkit-flex-shrink: 2;
    -ms-flex-negative: 2;
    flex-shrink: 2
}

[data-flex-box="3"] {
    -webkit-box-flex: 3;
    -webkit-flex-grow: 3;
    -ms-flex-positive: 3;
    flex-grow: 3;
    -webkit-flex-shrink: 3;
    -ms-flex-negative: 3;
    flex-shrink: 3
}

[data-flex-box="4"] {
    -webkit-box-flex: 4;
    -webkit-flex-grow: 4;
    -ms-flex-positive: 4;
    flex-grow: 4;
    -webkit-flex-shrink: 4;
    -ms-flex-negative: 4;
    flex-shrink: 4
}

[data-flex-box="5"] {
    -webkit-box-flex: 5;
    -webkit-flex-grow: 5;
    -ms-flex-positive: 5;
    flex-grow: 5;
    -webkit-flex-shrink: 5;
    -ms-flex-negative: 5;
    flex-shrink: 5
}

[data-flex-box="6"] {
    -webkit-box-flex: 6;
    -webkit-flex-grow: 6;
    -ms-flex-positive: 6;
    flex-grow: 6;
    -webkit-flex-shrink: 6;
    -ms-flex-negative: 6;
    flex-shrink: 6
}

[data-flex-box="7"] {
    -webkit-box-flex: 7;
    -webkit-flex-grow: 7;
    -ms-flex-positive: 7;
    flex-grow: 7;
    -webkit-flex-shrink: 7;
    -ms-flex-negative: 7;
    flex-shrink: 7
}

[data-flex-box="8"] {
    -webkit-box-flex: 8;
    -webkit-flex-grow: 8;
    -ms-flex-positive: 8;
    flex-grow: 8;
    -webkit-flex-shrink: 8;
    -ms-flex-negative: 8;
    flex-shrink: 8
}

[data-flex-box="9"] {
    -webkit-box-flex: 9;
    -webkit-flex-grow: 9;
    -ms-flex-positive: 9;
    flex-grow: 9;
    -webkit-flex-shrink: 9;
    -ms-flex-negative: 9;
    flex-shrink: 9
}

[data-flex-box="10"] {
    -webkit-box-flex: 10;
    -webkit-flex-grow: 10;
    -ms-flex-positive: 10;
    flex-grow: 10;
    -webkit-flex-shrink: 10;
    -ms-flex-negative: 10;
    flex-shrink: 10
}

.van-hairline,
.van-hairline--bottom,
.van-hairline--left,
.van-hairline--right,
.van-hairline--surround,
.van-hairline--top,
.van-hairline--top-bottom {
    position: relative
}

.van-hairline--bottom:after,
.van-hairline--left:after,
.van-hairline--right:after,
.van-hairline--surround:after,
.van-hairline--top-bottom:after,
.van-hairline--top:after,
.van-hairline:after {
    position: absolute;
    box-sizing: border-box;
    -webkit-transform-origin: center;
    transform-origin: center;
    content: " ";
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #eee;
}

.van-hairline--top:after {
    border-top-width: 1px
}

.van-hairline--left:after {
    border-left-width: 1px
}

.van-hairline--right:after {
    border-right-width: 1px
}

.van-hairline--bottom:after {
    border-bottom-width: 1px
}

.van-hairline--top-bottom:after {
    border-width: 1px 0
}

.van-hairline--surround:after {
    border-width: 1px
}
/* @media screen and (-webkit-min-device-pixel-ratio: 1.5) {
    .van-hairline:after {
        -webkit-transform: scale(.66666667);
        transform: scale(.66666667)
    }
}
@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .van-hairline:after {
        -webkit-transform: scale(.5);
        transform: scale(.5)
    }
}
@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .van-hairline:after {
        -webkit-transform: scale(.33333333);
        transform: scale(.33333333)
    }
} */
.text-one-hidd {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.text-two-hidd {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    word-break: break-all;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}


.van-toast.toast-dark-mini {
    padding: 16px;
    /* max-width: 190px; */
    width: 228px !important;
    border-radius: 0 !important;
    color: #fff !important;
    background-color: rgba(0,0,0,.9) !important;
    box-sizing: border-box;
}
.van-toast.toast-pos-middle{
    top: 50% !important;
}
.toast-dark-mini .van-toast__text {
    text-align: center;
    font-size: 14px;
    line-height: 22px;
}

.custom-ux-btn .button {
    transition: all .35s;
}
.custom-ux-btn .black-btn.btn-un-enabled {
    background-color: #e2e2e2 !important;
    border-color: #e2e2e2 !important;
    color: #aaa !important;
}
.custom-ux-btn .white-btn.btn-un-enabled {
    background-color: #fefefe!important;
    border-color: #f2f2f2 !important;
    color: #c2c2c2 !important;
}
.lan-custom-popover-box.square .van-popover__content {
    border-radius: 0;
}
.lan-custom-popover-box.square .van-popover__content .tips {
    width: 50vw;
    line-height: 160%;
    font-size: 14px;
    padding: 0 14px;
    border-radius: 0;
}

.el-popover {
  min-width: initial
}