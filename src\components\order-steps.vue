<template>
  <div class="steps-box">
    <div
      class="steps-list"
      v-for="(item,index) in statuslist"
      :key="index"
    >
      <div class="steps">
        <div :class="['point-in-times', nodeStatusObj[item.note || 2]]" />
        <dl class="dl">
          <dt
            class="dt"
            data-flex="main:justify"
          >
            <p class="tit">
              {{ item.nodeStatusRemark || '-' }}
            </p>
            <span>{{ item.nodeTime | dayjsFilter('MM-DD HH:mm') }}</span>
          </dt>
          <dd class="dd">
            {{ item.message || '' }}
          </dd>
        </dl>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Icon } from 'vant'

const nodeStatusObj = Object.freeze({
  2: 'van-icon van-icon-success',
  4: 'lan-icon-warning'
})
Vue.use(Icon)
export default {
  data() {
    return {
      stepsList: [],
      nodeStatusObj
    }
  },
  props: {
    statuslist: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
  },
  methods: {

  }
}
</script>

<style lang='less' scoped>
.steps-box {
  padding-bottom: 40px;
}
.steps-list {
  .steps {
    position: relative;
    z-index: 2;
    padding: 16px 0 0 28px;
    &::before {
      content: '';
      position: absolute;
      z-index: 1;
      left: 9px;
      bottom: 0;
      top: 0;
      border-left: 2px solid #F2F2F2;

    }

  }
  &:first-child {
    .steps::before {
      top: 16px
    }
  }
  &:last-child {
    .steps::before {
      bottom: 40px;
    }
  }
  .point-in-times, .lan-icon-warning {
    position: absolute;
    z-index: 2;
    left: 0;
  }
  .point-in-times, .lan-icon-warning::before {
    text-align: center;
    border-radius: 50%;
    overflow: hidden;
  }
  .point-in-times {
    width: 20px;
    height: 20px;
    line-height: 20px;
    color: #fff;
    font-size: 13px;
    background-color: #000;
    &.van-icon-success {
      &::before {
        content: '';
        position: relative;
        top: 0;
        background: url('~@/assets/img/check.svg?fill=#ffffff') 50% no-repeat;
        width: 100%;
        height: 100%;
        background-size: 70%;
      }
    }
  }
  .lan-icon-warning {
    background-color: #f50537;
    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      width: 8px;
      height: 8px;
      background-color: #fff;
      transform: translate(-50%,-50%);
    }
  }
  .dl, .dd, .dt {
    margin: 0;
  }
  .dd, .dt .tit {
    width: calc(100% - 120px);
  }
  .dl, .dd {
    .dt {
      min-height: 20px;
      line-height: 20px;
      span {
        color: #999;
      }
      .tit {
        margin: 0;
        width: calc(100% - 120px);
      }
    }
    .dd {
      min-height: 30px;
      margin-top: 6px;
      line-height: 20px;
      font-size: 12px;
      color: rgba(#000, .5);
    }
  }
}
</style>
