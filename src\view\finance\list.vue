<template>
  <div class="_container">
    <div
      class="_banner"
      @click="toCalculator"
    >
      计算器：先试算一下适合自己的金融方案吧
      <van-icon
        name="arrow"
        color="#000000"
      />
    </div>
    <div
      class="_box"
      v-if="serverList.length>0"
    >
      <div class="layout-title">
        标准信贷：
      </div>
      <div class="layout-img">
        <div
          v-for="(item,index) in serverList"
          :key="index"
          class="img-wrapper"
          @click="slectServe(item)"
        >
          <img
            :src="$loadWebpImage(item.img)"
            alt=""
          >
        </div>
      </div>
    </div>
    <div
      class="_box"
      v-if="leaseList.length>0"
      style="margin-bottom: 80px;"
    >
      <div class="layout-title">
        融资租赁：
      </div>
      <div class="layout-img">
        <div
          v-for="(item,index) in leaseList"
          :key="index"
          class="img-wrapper"
          @click="slectServe(item)"
        >
          <img
            :src="$loadWebpImage(item.img)"
            alt=""
          >
        </div>
      </div>
    </div>
    <div
      class="btn-pay-wrapper"
      v-if="paymentMethod === '20'"
    >
      <div
        :class="`btn-box ${replacementEnabled ? 'line-two-columns' : ''}`"
        data-flex="main:justify"
      >
        <AudiButton
          @click="modalshow = true"
          text="全款支付"
          color="black"
          font-size="16px"
          height="56px"
        />
        <AudiButton
          v-if="replacementEnabled"
          @click="handleBtnClickReplacementShow"
          text="重选金融方案"
          font-size="16px"
          height="56px"
        />
      </div>
    </div>
    <model
      class="model-custom-box"
      :modalshow.sync="modalshow"
      @update:modalshow="updateToFullPayment"
      confirm-text="确认"
      cancel-text="取消"
      bold-content="您的付款方式将由贷款更改为全款，是否确认。"
    />
    <popup-custom-action-btn
      v-if="replacement.conf && replacement.conf.enabled || false"
      :btn-conf.sync="replacement.conf"
      :btn-items="replacement.items"
      @emitGetActionBtn="handleSetFinanceReplacement"
    >
      <template #popup-custom-main>
        <div class="popup-content-text custom-align-center">
          {{ replacement.desc[0] }}
        </div>
      </template>
    </popup-custom-action-btn>
  </div>
</template>

<script>
import {
  Toast
} from 'vant'
import Vue from 'vue'
import {
  getMyOrders,
  getFinancePlatformList,
  getLoanStatus,
  getOrgBankList,
  updateToFullPayment,
  setFinanceReplacement
} from '@/api/api'
import baseUrl from '@/config/url'
import AudiButton from '@/components/audi-button'
import model from '@/components/model.vue'
import {
  callNative, getUrlParamObj, Obj2UrlParam, delay
} from '@/utils/'
import popupCustomActionBtn from '@/components/popup-custom-action-btn.vue'
import POPUP_CUSTOM from '@/config/popup-custom.data'

Vue.use(Toast)

export default {
  components: {
    AudiButton,
    model,
    'popup-custom-action-btn': popupCustomActionBtn
  },
  data() {
    return {
      imgMapList: [{
        img: require('@/assets/mock/shangqicai.png'),
        type: 'shangqicai',
        platformCd: 'saicfs',
        sensors: 'saicFinance',
        name: '上汽财务'
      },
      {
        img: require('@/assets/mock/jinrong.png'),
        type: 'jinrong',
        platformCd: 'vwfs',
        sensors: 'volkswagenFinance',
        name: '大众金融'
      },
      {
        img: require('@/assets/mock/zhongxin.png'),
        type: 'zhongxin',
        platformCd: 'citic',
        sensors: 'chinaCiticBank',
        name: '中信银行'
      }
        // {
        //   img: require('@/assets/mock/cmbBank.png'),
        //   type: 'zhaoshan',
        //   platformCd: 'cmb',
        //   sensors: 'cmbBank',
        //   name: '招商银行'
        // }
      ],
      serverList: [],
      leaseList: [],
      paymentMethod: '',
      platformCd: '',
      modalshow: false,
      orderId: '',
      ficateType: '1',
      replacement: {},
      replacementEnabled: false
    }
  },
  mounted() {
    // orderId=***************&skuid=1407545686728208386
    this.orderId = this.$route.query?.orderId || localStorage.getItem('orderId')
    // this.orderId = '***************'
    console.log('订单id:', this.orderId)
    this.getOrgBankList(this.orderId)
  },
  methods: {
    handleBtnClickReplacementShow() {
      const { replacement: { conf } } = this
      this.replacement.conf = { ...conf, ...{ show: true } }
    },
    async handleSetFinanceReplacement([emit, index, name]) {
      console.log('%c [ emit, index, name ]-182', 'font-size:14px; background:#cf222e; color:#fff;', emit, index, name)
      const { replacement: { items }, handleBtnPowerLoading } = this
      const list = handleBtnPowerLoading(index, items, ['add', '重置中 ...'])
      this.replacement.items = list
      this.replacement.loading = true
      // eslint-disable-next-line no-return-assign
      delay(() => {
        if (this.replacement.loading) {
          this.replacement.items = handleBtnPowerLoading(index, list, [])
          this.replacement.loading = false
          return Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '网络异常，请稍后再试',
            forbidClick: true
          })
        }
      }, 6000)
      const { orderId } = this
      const { data: { code, data, message } } = await setFinanceReplacement({ orderId })
      this.replacement.items = handleBtnPowerLoading(index, list, [])
      if (code !== '00' || !data) {
        this.replacement.loading = false
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: message || '网络异常，请稍后再试',
          forbidClick: true
        })
      }
      // this.$router.go(0)
      window.location.reload()
    },
    handleTimeoutError(message, index, list) {
      this.replacement.items = this.handleBtnPowerLoading(index, list, [])
      this.replacement.loading = false
      if (message) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: message || '网络异常，请稍后再试',
          duration: 1000,
          forbidClick: true
        })
      }
    },
    handleBtnPowerLoading(index, list, [action = '', strkey = '']) {
      return list.map((l, i) => {
        if (action !== 'add' && l.disabled) l.disabled = false
        if (i === index) l.loadingText = (action && action === 'add' ? strkey : '')
        return l
      })
    },
    toCalculator() {
      const {
        orderId, ccid, orderStatus, env
      } = this.$route.query
      if (env === 'minip') {
        return this.handleGoToPageDownloadApp(orderId, ccid)
      }
      console.log('carConfigH5BaseUrl: toCalculator', this.$carConfigH5BaseUrl)
      this.$router.push({
        path: '/configration/financial-calculator'
      })
      // this.locationPath({
      //   url: `${this.$carConfigH5BaseUrl}configration/financial-calculator?`,
      //   query: {
      //     from: 'money-detail',
      //   }
      // })
    },
    locationPath(e) {
      if (!e.url) return
      const {
        env, refreshToken, userId, token
      } = getUrlParamObj()
      const info = {
        env, refreshToken, userId, token
      }
      const minip = this.$store.state.env === 'minip' ? `&${Obj2UrlParam(info)}` : ''
      window.location.href = `${e.url}${e.query ? Obj2UrlParam(e.query) : ''}${minip}`
      const path = `${e.url}${e.query ? Obj2UrlParam(e.query) : ''}${minip}`
      callNative('audiOpen', { path: path })
    },

    async updateToFullPayment(status) {
      if (status) {
        this.modalshow = false
        const { orderId } = this.$route.query
        const { data } = await updateToFullPayment({ orderId })
        console.log(data, '更改状态')
        if (data.code === '00') {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: data?.message || '修改成功',
            duration: 1000,
            forbidClick: true
          })
          setTimeout((i) => {
            this.$router.go(-1)
          }, 1500)
        } else {
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: data?.message || '网络异常，请稍后再试',
            duration: 1000,
            forbidClick: true
          })
        }
      }
    },

    async getOrgBankList(orderId) {
      callNative('toggleLoading', { show: '1' })
      const orderDetail = await this.getOrderDetail(orderId)
      this.paymentMethod = orderDetail.extInfo.paymentMethod
      this.ficateType = orderDetail?.carBuyerInfo?.carOwnerCertificateType

      const { data } = await getOrgBankList({ dealerCode: orderDetail.dealerInfo.dealerCode })
      console.log('BankList：data', data, orderDetail.carBuyerInfo.buyType)
      // const { data } = await getFinancePlatformList({ orderId })
      console.log('baseUrl:', baseUrl)
      data.data.forEach((item) => {
        item.platformCd = item.bankCode
        item.img = baseUrl.BaseOssHost + item.imageUrl
        item.platformName = item.bankName
      })

      let financialList = data.data
      const leaseCodeMap = ['pingan', 'vwfsfl', 'anji', 'vwfsl']
      let serverList = []
      let leaseList = []

      if (orderDetail.carBuyerInfo.buyType === '02' || !['1'].includes(this.ficateType)) { // 企业用户只是用上汽财务的金融服务
        // 新增 平安(pingan)
        const seller = ['pingan', 'saicfs']
        financialList = financialList.filter((i) => seller.includes(i.bankCode))
      }
      financialList.forEach((i) => {
        (!leaseCodeMap.includes(i.bankCode) ? serverList : leaseList).push(i)
      })
      console.log('leaseCodeMap', leaseCodeMap)
      console.log('标准信贷 serverList', serverList)
      console.log(' leaseList', leaseList)

      const res = await getLoanStatus({ orderId })
      callNative('toggleLoading', { show: '0' })
      console.log(res)
      /**
         * res.data.loanStatus
         * 贷款状态 1-已操作贷款申请，尚未提交审批 2-已提交贷款申请，尚未出结果
         * 3-审批通过，尚未签署贷款合同 4-审批不通过 5-已* 签署贷款合同，尚未收到放款指令
         * 6-已收到放款指令，尚未完成放款 7-已完成放款
         */
      for (const item of res.data.data) {
        if (item.loanStatus !== 4) {
          serverList = serverList.filter((i) => i.platformCd === item.platformCode)
          leaseList = leaseList.filter((i) => i.platformCd === item.platformCode)
          break
        }
      }

      // 可重置
      if (res.data.data.some((i) => [0, 1].includes(i.loanStatus))) {
        this.replacement = POPUP_CUSTOM.REPLACEMENT
        this.replacementEnabled = true
      }

      this.serverList = serverList
      this.leaseList = leaseList
    },

    async getOrderDetail(orderId) {
      const { data } = await getMyOrders({ orderId })
      return data.data
    },

    slectServe(item) {
      const {
        orderId, ccid, orderStatus, env
      } = this.$route.query
      if (env === 'minip') {
        return this.handleGoToPageDownloadApp(orderId, ccid)
      }
      const query = {
        imgMapItem: item,
        serverListLength: this.serverList.length || this.leaseList.length,
        orderId
      }
      this.$storage.setPlus('bankInfo', query)

      console.log('query: ', query)

      this.$router.push({
        path: '/finance-programme',
        query: query
      })
    },
    handleGoToPageDownloadApp(orderId, ccid) {
      this.$router.push({
        name: 'order-guide-page-download-app',
        query: { orderId, ccid }
      })
    }
  }

}
</script>

<style lang="less" scoped>
  div {
    box-sizing: border-box;
  }

  ._box {
    padding: 0 16px;
  }

  .layout-title {
    line-height: 30px;
    margin-top: 10px;
    font-size: 14px;
    font-family: "Audi-WideBold";
  }

  .layout-img {
    margin-top: 0px;
  }

  .btn-pay-wrapper {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 10px 15px;
    box-sizing: border-box;
  }

  ._banner {
    width: 100%;
    height: 44px;
    background: #F2F2F2;
    display: flex;
    align-items: center;
    padding: 0 16px;
    justify-content: space-between;
    font-size: 14px;
    font-family: "Audi-Normal";
    color: #000000;
    box-sizing: border-box;
  }
.line-two-columns {
  .button {
    width: calc(50% - 3px) !important;
  }
}
.popup-content-text {
  line-height: 24px;
}

</style>
<style lang="less">
.model-custom-box {
  ._modal {
    .modal-bold-content {
      text-align: center;
      line-height: 24px;
      font-size: 16px;
      font-family: "Audi-Normal";
      padding-bottom: 20px;
    }
  }
}
</style>
