import request from '../router/axios'
import api from '../config/url'

const baseUrl = api.BaseApiUrl

// 排队人数
export const getLimitedNumbersLine = (params) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/limited_numbers_line/getLimitedNumberLineCount`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})
// 背景图

export const getBackgroundimage = () => request({
  url: `${baseUrl}/api-wap/audi-page/api/floors?pageAccessType=2&pageFrontCode=limitnumber-ranking-backgroundimage`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})
