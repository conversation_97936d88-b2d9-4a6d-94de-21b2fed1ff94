<template>
  <div
    class="modelline-wrapper"
    v-if="seriesLineList && seriesLineList.length"
    ref="lineModel"
  >
    <div
      :class='["series-tag", currentSeries == index ? "active": ""]'
      v-for="(series, index) in seriesLineList"
      :key="series[0].customSeriesCode + index"
      @click="currentSeries = index"
    >
      <div class="series">
        <span class="series-name">{{series[index].customSeriesName.replace("Audi ", "")}}</span>
      </div>
    </div>

    <div
      class="line"
      v-if="seriesLineList[currentSeries]"
      v-for="(line, index) in seriesLineList[currentSeries]"
      :key="line.modelLineCode + index"
      @click.prevent="selectModel(line)"
    >
      <div class="line-left">
        <div class="name zindex10" v-if="a7Mr.map(i=>i.code).includes(line.modelLineCode)">
          {{ line.modelLineName | a7MrTextWrap }}
        </div>
         <div class="name zindex10" v-else>
          {{ line.modelLineName | textWrap }}
        </div>
        <div class="price zindex10">
          ￥{{ (line.price || '价格待定') | formatPrice }}起
        </div>
      </div>

      <div class="line-right">
         >
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import {
  bestRecommendCar, getBestRecommendConfig, bestRecommendCarAgent
} from '@/configratorApi'
import {
  getOtds
} from '@/api/api'
import url from '@/config/url'
import { A7MR } from '@/view/newConfigration/car/a7mr'

export default {
  name: 'SelectModel',
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      modelLineList: [],
      seriesLineList: [],
      dealerCode: '',
      from: 'false',
      currentSeries: 0,
      a7Mr: A7MR
    }
  },
  created() {
    const query = this.$route.query
    if (query && query.dealerCode) {
      this.dealerCode = query.dealerCode
    }
    if (query && query.modelLineList) {
      this.modelLineList = query.modelLineList
      this.convert2Series()
    } else {
      this.$store.commit('showLoading')
      this.getModelLine()
      this.$store.commit('hideLoading')
    }
    if (query && query.from) {
      this.from = query.from
    }
    console.log('%c11', 'font-size:40px;color:blue;', this.modelLineList)
  },
  mounted() {

  },
  watch: {
    modelLineList(val) {
      setTimeout(() => {
        if (document.getElementById('pane-0')) {
          document.getElementById('pane-0').scrollTo({ top: val || 0 })
        }
      })
    }
  },
  activated() {
    console.log(
      '%cactivated',
      'font-size:40px;color:blue;',
      this.modelScrollTop,
      document.getElementById('pane-0')
    )
    const ele = document.getElementById('common-view-wrapper')
    ele.style.backgroundColor = '#F0f2f5'

    const query = this.$route.query
    if (query && query.dealerCode) {
      this.dealerCode = query.dealerCode
    }
    if (query && query.modelLineList) {
      this.modelLineList = query.modelLineList
      this.convert2Series()
    }
    document
      .getElementById('pane-0')
      ?.scrollTo({ top: this.modelScrollTop || 0 })
  },
  methods: {
    selectModel(line) {
      const _this = this
      if ('DealerDetail'.includes(this.from)) {
        getBestRecommendConfig(line.bestRecommendId).then((res) => {
          getOtds({ useType: 1, seriesId: 'ADA7' }).then((res1) => {
            const ccid = res.data.data.ccId
            const skuid = res1.data.data.prodSkuId
            if (ccid && skuid) {
              _this.$router.push({
                path: '/quotation',
                query: { ccid: ccid, skuid: skuid, dealerCode: _this.dealerCode }
              })
            }
          })
        })
      } else if (this.dealerCode) {
        this.$router.push({
          path: '/configration/financial-calculator',
          query: {
            showModel: line,
            fromselectmodel: true,
            dealerCode: this.dealerCode
          }
        })
      } else {
        this.$router.push({
          path: '/configration/financial-calculator',
          query: {
            showModel: line,
            fromselectmodel: true
          }
        })
      }
    },
    showModelDetail(line) {
      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: line.modelLineId,
          carModelName: line.modelLineName,
          seriesName: line.customSeriesCode
        }
      })
    },
    async getModelLine() {
      if (this.dealerCode) {
        await bestRecommendCarAgent(this.dealerCode).then((res) => {
          this.modelLineList = res.data.data.map((e) => {
            const bestRecommendId = e.bestRecommendId
            const totalPrice = e.totalPrice
            const modelLine = e.modelLine
            modelLine.bestRecommendId = bestRecommendId
            modelLine.price = totalPrice
            const colors = e.options.filter((option) => option.category === 'COLOR_EXTERIEUR')
            const currentColor = colors[0]
            const hubs = e.options.filter((option) => option.category === 'RAD')
            let currentHub = {}
            if (hubs.length > 0) {
              currentHub = hubs[0]
            } else {
              currentHub = e.standardRad
            }
            modelLine.imageUrl = `/ccpro-backend/storebest/${modelLine.modelLineCode}_${currentHub.optionCode}_${currentColor.optionCode}.png`
            return modelLine
          })
          this.convert2Series()
        })
      }
    },

    convert2Series() {
      console.log(this.modelLineList)
      const seriesLineList = {}
      this.seriesLineList = []
      this.modelLineList.forEach((item) => {
        const customSeriesCode = item.customSeriesCode
        if (!seriesLineList[customSeriesCode]) {
          seriesLineList[customSeriesCode] = []
        }
        seriesLineList[customSeriesCode].push(item)
      })
      for (const key in seriesLineList) {
        this.seriesLineList.push(seriesLineList[key])
      }
    }
  }
}
</script>

<style lang="less" scoped>
.zindex10 {
  position: relative;
  z-index: 10;
}

.modelline-wrapper {
  padding: 16px;
  overflow-x: hidden;

  .series-tag {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
    font-family: 'Audi-WideBold';

    .series {
      display: flex;
      justify-content: space-between;
      align-content: center;
    }
  }

  .series-tag.active .series-name {
    border-bottom: solid #000 1px;
  }
  .line {
    position: relative;
    /* box-shadow: 0px 0px 6px 0px rgb(0 0 0 / 15%); */
    margin-bottom: 16px;
    overflow: hidden;
    padding: 14px;
    display: flex;
    justify-content: space-between;
    background: #fff;

    &.active {
      border: 1px solid #000000;
    }
    .name {
      font-size: 16px;
      color: #333333;
      font-family: 'Audi-WideBold';
      white-space: pre-line;
    }
    .detail {
      padding-top: 18px;
      font-size: 14px;
      display: inline-block;
    }
    .price {
      color: #999999;
      margin-top: 16px;
      font-family: 'Audi-WideBold';
    }
    .img {
      position: absolute;
      right: -15px;
      bottom: 10px;
      width: 230px;
      height: 130px;
      img {
        width: 100%;
        height: 100%;
        display: block;
      }
    }

    .line-left {
      width: calc(100% - 40px);
    }

    .line-right {
      justify-content: center;
      align-items: center;
      display: flex;
    }
  }
}
</style>
