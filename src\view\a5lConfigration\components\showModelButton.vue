<template>
  <div v-if="!isMinip" class="btn-showmodel">
    <img src="../../../assets/img/icon02.png" @click="showModel3d" alt="">
  </div>
</template>

<script setup>
import { defineProps, getCurrentInstance, ref } from "vue";
import { useStore } from "@/view/newConfigration/util/vueApi";
import { callNative, getUrlParamObj, isEmptyObj } from "@/utils";
import { getA5Lcc } from "@/api/api";

const { env } = getUrlParamObj();
const instance = getCurrentInstance();
const sensors = instance.proxy.$sensors;
const store = useStore();

const isMinip = ref(env === "minip");

// 获取 props
const props = defineProps({
  type: {
    type: String,
    required: true,
    default: "out" // out | in
  }
});

// 获取车型的模型展示url
const get3dModelUrl = async (name) => {
  let res = await getA5Lcc();
  const { data } = res;
  if (data.code !== "00") return console.error("获取url出错");
  return data.data.configValue;
};

/**
 * 外观的内饰的区别就是 in | out 的区别
 */
const showModel3d = async () => {
  clickPopupSensors("切换为3D"); // 埋点

  store.commit("showLoading");
  const { currentModelLineData, currentExterior, selectedOptions, currentHub, currentEih, currentII } = store.state.configration;

  const currentModelCode = currentModelLineData.modelUnicode;
  const currentExCode = currentExterior.featureCode;

  // 内饰组合
  let currentInCode = "";
  if (!isEmptyObj(currentII.packet) && Array.isArray(currentII.packet.labelChildren) && currentII.packet.labelChildren.length > 0) {
    const features = {};
    for (const item of currentII.packet.labelChildren) {
      if (item.familyCode === "SIB" || item.familyCode === "II") {
        features[item.familyCode] = item.featureCode;
      }
    }
    let arr = [
      features.SIB,
      features.II
    ];
    currentInCode = arr.join("+");
  }

  // 轮毂组合
  // "RAD":"轮毂", "BAH":"后卡钳","BAV": "前卡钳",
  let currentHubCode = "";
  if (!isEmptyObj(currentHub.packet) && Array.isArray(currentHub.packet.labelChildren) && currentHub.packet.labelChildren.length > 0) {
    const features = {};
    for (const item of currentHub.packet.labelChildren) {
      if (item.familyCode === "BAH" || item.familyCode === "BAV" || item.familyCode === "RAD") {
        features[item.familyCode] = item.featureCode;
      }
    }
    let arr = [
      features.RAD,
      features.BAH,
      features.BAV
    ];
    currentHubCode = arr.join("_");
  }

  const currentOptions = selectedOptions.map((item) => item.featureCode) || [];
  if (currentEih.featureCode) {
    currentOptions.push(currentEih.featureCode);
  }

  currentOptions.push("HSP"); // iframe隐藏热点

  const currentCar = {
    currentModelCode,
    currentExCode,
    currentInCode,
    currentHub: currentHubCode,
    currentOptions: currentOptions,
    platform: "app",
    showCloseBtn: true,
    showInnerHotspot: "0",
    currentView: props.type // out | in
    // currentScene: 'chengshi' // 当前场景，值为“shanghai”和“chengshi”和“guangzhou”
  };

  let url = await get3dModelUrl("a5l");
  const currentCarBase64 = window.btoa(JSON.stringify(currentCar));
  const str = url.includes("?") ? "&" : "?";
  const iframeUrl = `${url + str}hidepanel=1&currentCar=${currentCarBase64}`;

  console.log("%c A5L iframeUrl:", "font-size:16px;color:green;", iframeUrl, currentCar);
  store.commit("hideLoading");

  await callNative("audiOpen", {
    path: iframeUrl,
    params: "app",
    pageOrientation: "landscape"
  });
};

// 埋点
const clickPopupSensors = (operationType) => {
  const { currentModelLineData, carIdx, currentVersion } = store.state.configration;
  const { engine, customSeriesName } = currentModelLineData;
  const param = {
    source_module: "H5",
    car_series: "A5L",
    car_type: store.getters.currentCarType,
    power_type: `${customSeriesName} ${engine}`,
    car_version: currentVersion.styleName,
    delivery_type: "定制交付", // 快速交付|定制交付
    operation_type: operationType
  };
  sensors.track("CC_CarConfiguration_Operate", param);
};

</script>

<style lang="less" scoped>

.btn-showmodel {
  position: absolute;
  top: 18px;
  left: 18px;
  width: 52px;
  height: 52px;
  z-index: 2;
}

</style>
