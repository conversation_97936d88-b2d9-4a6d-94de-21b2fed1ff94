<template>
  <div class="_container">
    <div class="text-content">
      <!-- <span>为用于贷款/融资租赁合同的签署中个人信息真实性校验，确保签署方身份的真实，需要传输给金融机构个人信息的必要字段，包括：车主姓名，身份证号，手机号传输给如下机构：</span>
      <span>平安国际融资租赁有限公司（住所：中国（上海）自由贸易试验区世纪大道8号上海国金中心办公楼二期37层）；平安国际融资租赁（天津）有限公司（住所：天津自贸试验区（东疆保税港区）乐山道200号铭海中心2号楼-5、6-802）</span>
      <span>接收方联系方式：400-611-7700</span> -->
      <span v-html="chemetext" />
    </div>
    <div class="btn-pay-wrapper">
      <AudiButton
        @click="updateModalStatus"
        text="前往金融服务"
        color="black"
        font-size="16px"
        height="56px"
      />
    </div>
    <model
      :modalshow.sync="modalshow"
      @update:modalshow="submit"
      confirm-text="确认选择"
      cancel-text="我再想想"
      :content="content"
      :title="title"
    />
    <van-popup
      v-model="deliveryDialog"
      class="popup-custom"
      :close-on-click-overlay="false"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="text align-center">
            {{ errorsMessage || '网络请求错误' }}
          </div>
        </div>
        <div
          class="popup-custom-btn"
        >
          <audi-button
            text="我知道了"
            color="black"
            height="56px"
            @click="deliveryDialog = false"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { applyFinanceServer, getMyOrders } from '@/api/api'
import AudiButton from '@/components/audi-button'
import { callNative, isIos } from '@/utils'
import model from '@/components/model.vue'

export default {
  components: { AudiButton, model },
  data() {
    return {
      modalshow: false,
      title: '',
      content: '',
      chemetext: '',
      errorsMessage: '',
      deliveryDialog: false
    }
  },
  mounted() {
    console.log('2222', this.$storage.getPlus('bankInfo')?.imgMapItem.confirmText)

    this.chemetext = this.$route.query?.imgMapItem.confirmText || this.$storage.getPlus('bankInfo')?.imgMapItem.confirmText
  },
  methods: {
    updateModalStatus() {
      const query = this.$route.query
      const bankInfo = this.$storage.getPlus('bankInfo')
      const serverListLength = query.serverListLength || bankInfo.serverListLength
      const imgMapItem = query.imgMapItem || bankInfo.imgMapItem
      if (serverListLength === '1') { // 在当前页选中过一次 或者之前选择过就不会再次弹窗提示了
        this.submit(true)
      } else { // 第一次选择需要弹窗提示
        this.title = `<div style='font-size:15px;width:100%;text-align:left;padding:0 18px'>您将前往第三方“${imgMapItem.platformName}”提供的页面</div>`
        this.content = '1.进入第三方页面后，在该机构流程结束前不可更换其他金融机构。\n 2.第三方在提供服务的过程中向您做出的任何承诺、声明或行为仅适用于第三方与您之间的服务，不视为上汽奥迪的承诺、声明或行为。\n 3.若您因未遵守第三方相关授权文件的规定或要求，造成您的任何损失，上汽奥迪不承担任何责任。'
        this.modalshow = true
      }
    },
    async submit(isSubmit) {
      this.modalshow = false
      const { imgMapItem } = this.$storage.getPlus('bankInfo') || this.$route.query || {}
      if (isSubmit) {
        // 埋点
        this.$sensors.track(imgMapItem.sensors)

        const orderId = this.$route.query?.orderId || this.$storage.getPlus('bankInfo')?.orderId
        const { carBuyerInfo } = await this.getOrderDetail(orderId)
        const param = {
          orderNo: orderId,
          ownerName: carBuyerInfo.fullName,
          ownerPhone: carBuyerInfo.mobile,
          payerName: carBuyerInfo.fullName,
          platformCode: imgMapItem.platformCd
        }
        const { data } = await applyFinanceServer(param)

        console.log('%c [ data ]-82', 'font-size:14px; background:#cf222e; color:#fff;', data)
        if (data.code === '00') {
          // 在当前页选择了之后展示选择过的   去掉多余的
          this.serverList = [imgMapItem]
          this.platformCd = imgMapItem.platformCd

          if (isIos) {
            if (imgMapItem.platformCd === 'citic') {
              // 这里先注视，3.0.1发版了该版本才能上
              callNative('toggleLoading', { show: '1' })
              setTimeout(() => {
                callNative('toggleLoading', { show: '0' })
              }, 5000)

              const backData = await callNative('bankOpen', { url: data.data })
              if (Object.getOwnPropertyNames(backData).length === 0) {
                callNative('openNativeBrowser', { url: data.data })
              }
            } else {
              if (imgMapItem.platformCd === 'pingan') {
                callNative('audiOpen', { path: data.data, showHeader: false })
              } else {
                window.location.href = data.data
                // 调用showheader
                callNative('business', {
                  callFunc: {
                    functionName: 'toggleNavigation',
                    functionParams: {
                      show: true
                    }
                  },
                  bizCode: '000003'
                })
              }
            }
          } else {
            if (imgMapItem.platformCd === 'pingan') {
              callNative('audiOpen', { path: data.data, showHeader: false })
            } else {
              window.location.href = data.data
              // 调用showheader
              callNative('business', {
                callFunc: {
                  functionName: 'toggleNavigation',
                  functionParams: {
                    show: true
                  }
                },
                bizCode: '000003'
              })
            }
          }
        } else {
          if (data.code === '01') {
            const { message } = data
            this.errorsMessage = ['不能修改', '网络请求'].some((i) => message.includes(i)) ? message : '该卡券不适用该订单，请您联系奥迪专属管家'
            this.deliveryDialog = true
            // callNative('toast', { type: 'fail', message: data.message })
            return
          }
          console.error(`applyFinanceServer url: ${data.data}`)
        }
      }
    },
    async getOrderDetail(orderId) {
      const { data } = await getMyOrders({ orderId })
      return data.data
    }
  }
}
</script>

<style lang='less' scoped>
  div {
    box-sizing: border-box;
  }

  .btn-pay-wrapper {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 10px 15px;
    box-sizing: border-box;
  }
  .text-content{
    padding: 20px 16px;

    span{
      display: inline-block;
      width: 100%;
      margin-top: 5px;
      font-size: 18px;
      font-family: "Audi-Normal";
      line-height: 30px;
      color: #000000;
    }
  }
</style>
