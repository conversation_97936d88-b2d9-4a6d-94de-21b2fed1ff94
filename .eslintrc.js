module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'eslint:recommended',
    'plugin:vue/essential',
    '@vue/airbnb'
  ],
  plugins: [
    'vue'
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'comma-dangle': [2, 'never'],
    semi: [2, 'never'],
    radix: 0,
    eqeqeq: [1],
    'no-underscore-dangle': [0, { allow: ['foo_', '_bar'] }],
    'max-len': [0, { code: 100 }],
    'import/extensions': [0, 'always'],
    'consistent-return': 0,
    'no-restricted-globals': [0],
    'import/no-cycle': [0, { ignoreExternal: true }],
    'global-require': 0,
    'import/no-dynamic-require': 0,
    'no-restricted-syntax': [0],
    'guard-for-in': 0,
    'import/no-extraneous-dependencies': [0],
    'prefer-destructuring': [0],
    'no-plusplus': 0,
    'func-names': 0,
    'no-param-reassign': 0,
    'no-lonely-if': 0,
    'no-unused-expressions': 0,
    'array-callback-return': 0,
    'no-prototype-builtins': 1,
    'no-continue': 0,
    'import/no-named-as-default': 0,
    'no-shadow': 0,
    'no-case-declarations': 0,
    'no-use-before-define': 0,
    'import/prefer-default-export': 0,
    'linebreak-style': 0, // window/unix 换行相关?
    'object-shorthand': [0, 'always'],
    'no-unused-vars': 0, // 后期会改为 2,禁止出现未使用的参数
    'no-empty': 0, // 后期改为2..
    'no-new': 0,
    'no-multiple-empty-lines': [2, { max: 2, maxEOF: 0 }],
    'space-before-function-paren': [2, {
      anonymous: 'always',
      named: 'never',
      asyncArrow: 'always'
    }],
    'vue/multi-word-component-names': 0


  },
  globals: {
    AMap: true,
    AMapUI: true
  },
  parserOptions: {
    ecmaVersion: 2020
    // parser: '@babel/eslint-parser'
  }
}
