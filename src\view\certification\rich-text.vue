<template>
  <div class="certification" v-if='iframeSrc'>
    <iframe
      ref="frame"
      class="myIframe"
      :src="iframeSrc"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script>
import Vue from "vue";
import { RadioGroup, Radio, Field, Checkbox, CheckboxGroup } from "vant";
import AudiButton from "@/components/audi-button";
import { callNative } from "@/utils";
import { getprivacy, getpolicy, getNewConsentByType } from '@/api/api'
import storage from "../../utils/storage";

Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(Field).use(CheckboxGroup).use(Checkbox);

export default {
  components: {},
  data() {
    return {
      iframeSrc: ""
        // "https://mos-public-prod.mos.csvw.com/prod/richText/richText/1702973838466.html",
    };
  },

  async mounted() {
    const { type } = this.$route.query;

    if (type == 2) {
      this.$store.state.title = "敏感个人信息处理规则";
      this.iframeSrc =
        "https://mos-public-prod.mos.csvw.com/prod/richText/richText/1651218605085.html";
    } else if (type == 3) {
      this.$store.state.title = "车辆识别码(汽车VIN号)出境规则";
      this.iframeSrc =
        "https://mos-public-prod.mos.csvw.com/prod/richText/richText/1676967359488.html";
    } else {
      // 隐私政策
      // const {Did} = await callNative('getUserInfo', {})
      getNewConsentByType({}, {
        'Content-Type': 'application/json',
        'X-Channel': 1000,
        'Did': parseInt(10000*Math.random())
      }).then(res => {
        console.info('🚀 ~ file:rich-text method: line:50 -----', res)
        this.iframeSrc = res.data?.data?.content;
      })
    }
  },

  methods: {},
};
</script>
<style lang="less" scoped>
@import "../../assets/style/common.less";

.certification {
  padding: 0 16px;
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .myIframe {
    width: 100vw;
    height: 100vh;
    z-index: 3;
    position: absolute;
    top: 80px;
    left: 0;
    background-color: #fff;
  }
}
::v-deep .van-checkbox__label {
  margin-top: 5px;
}

::v-deep .van-checkbox__label {
  margin-left: 4px;
}
</style>
