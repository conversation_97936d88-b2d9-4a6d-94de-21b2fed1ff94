<template>
  <div
    class="userAgreement"
    @touchstart="start($event)"
    @touchmove="move($event)"
    @touchend="end($event)"
  >
    <!-- <div class="title">
      为保证您的账号、车辆及服务的安全，请确保您的账号符合以下条件:
    </div> -->
    <ul>
      <li>
        <div>法律条款：</div>
        <div v-if="policyArr[0]" class="list">
          {{policyArr[0]}}
        </div>
        <div v-if="policyArr[1]" class="list">
          {{policyArr[1]}}
        </div>
      </li>
      <!-- <li>
        <div>二、购买流程</div>
        <div class="list">
          2.1上汽奥迪将尽快安排您完成后续的车辆购买事宜，包括但不限于签署《汽车购买协议》以及其他可能适用的协议。车辆配置选择仅为意向，交付车辆的配置详情及车辆总价等信息将在《汽车购买协议》中予以约定。虽有前述规定，您及上汽奥迪中的任何一方均可在《汽车购买协议》签署之前，终止车辆意向购买流程。在车辆意向购买流程根据本条款终止后，上汽奥迪将向您无息退还全部已收取的意向金。
        </div>
        <div class="list">
          2.2若您希望锁定意向金阶段获得的限量号，您需要在上市销售（公布售价日期为准）起3天内完成定金的支付，否则将无法保留该号码，其它意向金权益将继续保留。
        </div>
        <div class="list">
          2.3若您申请退还意向金，或在支付定金前切换车型，将无法享受意向购买专属权益。
        </div>
      </li>
      <li>
        <div>三、信息使用</div>
        <div class="list">
          3.1鉴于国家法律法规及政策要求，为保障您的合法权益及上汽奥迪为您提供良好的服务，您同意，上汽奥迪或其委托的第三方收集并合法使用您的个人信息。
        </div>
        <div class="list">
          3.2信息收集及保护需符合上汽奥迪品牌《隐私政策》（详见<span>https://audi-embedded-wap.saic-audi.mobi/download/yinsi.html</span>）及上汽奥迪品牌《用户协议》（详见<span>https://audi-embedded-wap.saic-audi.mobi/download/xieyi.html</span>）的规定。
        </div>
      </li>

      <li>
        <div>四、适用法律</div>
        <div class="list">
          4.1本协议受中华人民共和国法律管辖并据其进行解释。
        </div>
        <div class="list">
          4.2因本协议引起的以及与本协议有关的一切争议，首先应由双方通过友好协商解决。如果双方未能通过协商解决该等争议，则任何一方有权向上汽奥迪所在地的有管辖权的人民法院提起诉讼解决。
        </div>
      </li>
    </ul>
    <ul v-else>
      <li>
        <div>一、意向购买</div>
        <p class="list">
          1.1鉴于拟代办购车用户（下称“您”）与上海上汽大众汽车销售有限公司或其关联公司（下称“上汽奥迪”），<span
            class="xhx"
          >就您为购车用户（下称“车主“）</span>订购上汽奥迪品牌汽车（下称“上汽奥迪汽车”或“车辆”）已达成一致意见。
        </p>
        <div class="list">
          1.2鉴于您为完成代办车主订购上汽奥迪汽车意向购买事宜，您自愿代车主本人向上汽奥迪支付人民币【7777元】的意向金（下称“意向金”）。该意向金是您目前用来预订上汽奥迪汽车的金额，不是购买上汽奥迪汽车的定金。
        </div>
        <div class="list">
          1.3您代付意向金后，若车主本人与上汽奥迪后续就购买上汽奥迪汽车签署了《汽车购买协议》以及其他可能适用的协议的，则意向金将自动抵扣车辆定金的相应部分。
        </div>
        <div class="list">
          <div :style="{ display: 'flex' }">
            <span :style="{ marginRight: '8px' }">1.4</span>
            <van-checkbox
              v-model="checkbox"
              shape="square"
              icon-size="16px"
            >
              代付意向金承诺
            </van-checkbox>
          </div>
          <span
            :style="{ textIndent: '1.5rem' }"
          >“本人自愿代车主支付购车意向金【7777元】，若因代付该意向金引起的纠纷由我与车主本人协商处理，与上汽奥迪无关，特此声明。”</span>
        </div>
      </li>
      <li>
        <div>二、购买流程</div>
        <div class="list">
          2.1您将即时告知车主已代办订购上汽奥迪汽车事宜，上汽奥迪将尽快安排车主完成后续的车辆购买事宜，包括但不限于签署《汽车购买协议》以及其他可能适用的协议。车辆配置选择仅为意向，交付车辆的配置详情及车辆总价等信息将在《汽车购买协议》中予以约定。虽有前述规定，您及上汽奥迪中的任何一方均可在《汽车购买协议》签署之前，终止车辆意向购买流程。在车辆意向购买流程根据本条款终止后，上汽奥迪将向您无息退还全部已收取的意向金。
        </div>
        <div class="list">
          2.2若您希望锁定意向金阶段获得的限量号，您需要在上市销售（公布售价日期为准）起3天内完成定金的支付，否则将无法保留该号码，其它意向金权益将继续保留。
        </div>
        <div class="list">
          2.3若您申请退还意向金，或在支付定金前切换车型，将无法享受意向购买专属权益。
        </div>
      </li>
      <li>
        <div>三、信息使用</div>
        <div class="list">
          3.1鉴于国家法律法规及政策要求，为保障您的合法权益及上汽奥迪为您提供良好的服务，您同意，上汽奥迪或其委托的第三方收集并合法使用您的个人信息。
        </div>
        <div class="list">
          3.2信息收集及保护需符合上汽奥迪品牌《隐私政策》（详见<span>https://audi-embedded-wap.saic-audi.mobi/download/yinsi.html</span>）及上汽奥迪品牌《用户协议》（详见<span>https://audi-embedded-wap.saic-audi.mobi/download/xieyi.html</span>）的规定。
        </div>
      </li>

      <li>
        <div>四、适用法律</div>
        <div class="list">
          4.1本协议受中华人民共和国法律管辖并据其进行解释。
        </div>
        <div class="list">
          4.2因本协议引起的以及与本协议有关的一切争议，首先应由双方通过友好协商解决。如果双方未能通过协商解决该等争议，则任何一方有权向上汽奥迪所在地的有管辖权的人民法院提起诉讼解决。
        </div>
      </li> -->
    </ul>
    <div class="btn-warp">
      <div
        class="buttons tsbtn"
        v-if="time > 0"
        @click="confim"
      >
        还需阅读{{ time }}秒
      </div>
      <div
        class="buttons"
        v-else
        @click="confim"
      >
        我已确认
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Toast, Checkbox } from 'vant'
import { policyGet } from '@/configratorApi'


Vue.use(Toast).use(Checkbox)
export default {
  data() {
    return {
      time: 5,
      timer: null,
      checkbox: true,
      isbuyType: true,
      policyArr: []
    }
  },
  methods: {
    async policyGetFn() {
      const res = await policyGet()
      this.policyArr = res.data?.data?.configValue?.split('<br/>')
    },
    confim() {
      if (this.time > 0) {
        Toast({
          type: 'fail',
          message: '需仔细阅读5秒',
          icon: require('../../assets/img/error.png')
        })
      } else {
        this.$router.replace({
          path: '/configration/financial-calculator',
          query: this.$route.query
        })
        // if (this.$route.query.isIdentical == 'true') {
        //   if (this.$route.query.status) {
        //     this.$router.push({
        //       path: '/order/money-detail',
        //       query: {
        //         orderId: this.$route.query.orderId || ''
        //       }
        //     })
        //   } else {
        //     this.$router.push({
        //       path: '/order/detail',
        //       query: {
        //         formDetail: true,
        //         orderId: this.$route.query.orderId || ''
        //       }
        //     })
        //   }
        // } else {
        //   if (this.checkbox) {
        //     if (this.$route.query.status) {
        //       this.$router.push({
        //         path: '/order/money-detail',
        //         query: {
        //           orderId: this.$route.query.orderId || ''
        //         }
        //       })
        //     } else {
        //       this.$router.push({
        //         path: '/order/detail',
        //         query: {
        //           formDetail: true,
        //           orderId: this.$route.query.orderId || ''
        //         }
        //       })
        //     }
        //   } else {
        //     Toast({
        //       type: 'fail',
        //       message: '请阅读并确认代付意向金承诺',
        //       icon: require('../../assets/img/error.png')
        //     })
        //   }
        // }
      }
    },
    getAngle(angx, angy) {
      return (Math.atan2(angy, angx) * 180) / Math.PI
    },
    getDirection(startx, starty, endx, endy) {
      const angx = endx - startx
      const angy = endy - starty
      let result = 0 // 默认标记没有滑动
      // 如果滑动距离太短
      if (Math.abs(angx) < 2 && Math.abs(angy) < 2) {
        return result
      }
      const angle = this.getAngle(angx, angy)
      console.log(angle)
      if (angle >= -135 && angle <= -45) {
        result = 1 // 向上
      } else if (angle > 45 && angle < 135) {
        result = 2 // 向下
      } else if (
        (angle >= 170 && angle <= 180)
      ) {
        result = 3 // 向左
      } else if (angle >= -45 && angle <= 45) {
        result = 4 // 向右
      }
      return result
    },
    start(event) {
      const tabbarRef = this.$refs.tabbarRef
      const touchS = event.targetTouches[0] // touches数组对象获得屏幕上所有的touch，取第一个touch
      this.startPos = {
        x: touchS.pageX,
        y: touchS.pageY,
        time: new Date()
      } // 取第一个touch的坐标值
    },
    move(event) {
      // 当屏幕有多个touch或者页面被缩放过，就不执行move操作
    },
    end(event) {
      const touchE = event.changedTouches[0]
      this.endPos = {
        x: touchE.pageX,
        y: touchE.pageY,
        timeStemp: new Date()
      }
      const direction = this.getDirection(
        this.startPos.x,
        this.startPos.y,
        this.endPos.x,
        this.endPos.y
      )
      console.log('direction', direction)
      if (direction === 3) {
        // 左滑
        const param = {
          isGoBack: false
        }
        if (this.time > 0) {
          Toast({
            type: 'fail',
            message: '需仔细阅读5秒',
            icon: require('../../assets/img/error.png')
          })
          return false
        }
        param.isGoBack = true
        // if (this.$route.query.status) {
        //   this.$router.push({
        //     path: '/order/money-detail',
        //     query: {
        //       orderId: this.$route.query.orderId || ''
        //     }
        //   })
        // } else {
        //   this.$router.push({
        //     path: '/order/detail',
        //     query: {
        //       formDetail: true,
        //       orderId: this.$route.query.orderId || ''
        //     }
        //   })
        // }

        // this.bridge.callHandler("isOnKeyDown", param, (err, res) => {
        //   if (err) {
        //     console.log("isOnKeyDownError=", err);
        //   }
        //   console.log("isOnKeyDownSuccess=", res);
        // });
      } else {
        return false
      }
    }
  },
  mounted() {
    this.policyGetFn()
    if (this.$route.query.buyType === '1') {
      this.isbuyType = true
    } else {
      this.isbuyType = false
    }

    window.document.documentElement.scrollTop = 0
    this.$store.commit('allowBack', '1')
    console.log(this.$store.state.allowBackVal)
    this.timer = setInterval(() => {
      if (this.time > 0) {
        this.time--
      } else {
        this.$store.commit('allowBack', '0')
      }
    }, 1000)
  },

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="less">
// @import url("../../assets/style/scroll.less");
@import url("../../assets/style/buttons.less");


.btn-warp {
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 100%;
  background:#fff;
  padding: 10px 17px 30px 17px;
  box-sizing: border-box;
  z-index: 100;
}

/deep/.van-checkbox__icon--checked {
  .van-icon {
    background: #fff;
    color: #666;
    border-color: #666;
  }
}
.userAgreement {
  padding: 0 16px 100px 16px;
  .title {
    line-height: 24px;
    font-size: 16px;
    font-family: "Audi-ExtendedBold";
    margin-top: 10px;
  }
  ul {
    font-size: 14px;
    li {
      div {
        &:first-child {
          line-height: 48px;
        }
      }
      .list {
        line-height: 20px;
        margin-bottom: 30px;
        // .xhx {
        //   border-bottom: 2px solid #000;
        // }
      }
    }
  }
  .tsbtn {
    background: #999 !important;
    color: #fff;
    border: 0;
  }
  .buttons {
    background: #1a1a1a;
  }
}
.box {
  padding-bottom: 0;
}
/deep/.van-checkbox__label {
  margin: 0;
}
</style>
