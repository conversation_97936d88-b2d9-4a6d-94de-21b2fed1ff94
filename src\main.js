import Vue from 'vue'
import {
  Overlay, Toast, Tab as vTab, Tabs as vTabs
} from 'vant'
import VConsole from 'vconsole'
import Axios from 'axios'
import { Tabs, TabPane, Popover } from 'element-ui'
import AMapLoader from '@amap/amap-jsapi-loader'
import AMap from 'vue-amap'
import { router } from '@/router/router'
// eslint-disable-next-line import/no-unresolved
import '@/index.less'
import '@/index.css'
// import 'vant/lib/index.css' // 此css影响全局字体,暂且注释,是否需要?
import store from '@/store/index'
import Bridge from '@/utils/JSbridge.js'
import config from '@/config/url'
import { getCarConfigH5BaseUrl } from '@/configratorApi'
import storage from '@/utils/storage'
import { Entrance } from '@/utils/EntranceCode.js'
import App from './App'
import './assets/style/toast.less'
import './utils/filter' // global filter
import { getUrlParamObj } from '@/utils/index'

import EventBus from './utils/bus'
import '@/view/newConfigration/util/vueFilter'


import { loadImage, supportWebp } from './utils/webpImageLoader'
import { initTimeout } from './utils/timeout'
import StyleManager from '@/utils/purpleDriveStyle';


initTimeout()

Vue.use(Overlay)
Vue.use(AMap) // 引入模块

AMap.initAMapApiLoader({
  key: '14e5933fd8cfbc2be9b2ab98e74feefc',
  plugin: [
    'AMap.Autocomplete',
    'AMap.PlaceSearch',
    'AMap.Scale',
    'AMap.OverView',
    'AMap.ToolBar',
    'AMap.MapType',
    'AMap.PolyEditor',
    'AMap.CircleEditor',
    'AMap.Geolocation',
    'AMap.Driving'
  ],
  v: '1.4.4'
})

Vue.prototype.$entrance = Entrance
Vue.prototype.$styleManager = StyleManager

Vue.prototype.$getCarConfigH5BaseUrlFn = async () => {
  const res = await getCarConfigH5BaseUrl()
  Vue.prototype.$carConfigH5BaseUrl = res
}
Vue.prototype.$getCarConfigH5BaseUrlFn()
Vue.prototype.$storage = storage

Vue.use(Tabs)
Vue.use(TabPane)
Vue.use(Popover)
Vue.use(Toast)
Vue.use(vTab)
Vue.use(vTabs)

Vue.prototype.$axios = Axios
Vue.prototype.$bus = new Vue()
Vue.prototype.$bridge = Bridge.webViewJavascriptBridge

Vue.prototype.$webp = ''
supportWebp(() => {
  Vue.prototype.$webp = window.isSupportsWebp ? '?x-oss-process=image/format,webp' : ''
})

Vue.prototype.$loadWebpImage = (src) => loadImage(src)

const { VUE_APP_ENV, NODE_ENV, VUE_APP_MOCK } = process.env
if (VUE_APP_ENV === 'dev' || VUE_APP_ENV === 'qa' || VUE_APP_ENV === 'pre') {
  new VConsole() // 初始化
}

// 开发(MOCK)环境
if (NODE_ENV === 'development' && VUE_APP_MOCK === 'true') {
  // require('./mock')
  new VConsole() // 初始化
}

// 数据埋点
const sensors = require('sa-sdk-javascript')

sensors.init({
  // sensorsdata.min.js 文件的地址
  // sdk_url: 'https://cdn.jsdelivr.net/npm/sa-sdk-javascript@1.14.8/sensorsdata.min.js',
  // SDK 使用的一个默认的全局变量
  name: 'sensors',
  cross_subdomain: true,
  // 控制台取消打印
  show_log: VUE_APP_ENV === 'pre',
  server_url: config.serveUrl,
  // 数据接收地址 dev环境
  // server_url: 'https://appdc-dev.mos.csvw.com/sa?project=saoneapp',
  // 数据接收地址 uat
  // server_url: 'https://appdc-uat.mos.csvw.com/sa?project=saoneapp',
  // heatmap_url神策分析中点击分析及触达分析功能代码，代码生成工具会自动生成。如果神策代码中 `sensorsdata.min.js` 版本是 1.13.1 及以前版本，这个参数须配置，高于此版本不需要配置。
  heatmap_url: 'https://cdn.jsdelivr.net/npm/sa-sdk-javascript@1.14.8/heatmap.min.js',
  heatmap: {
    // 是否开启点击图，默认 default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭
    // 需要 JSSDK 版本号大于 1.7
    clickmap: 'default'
    // 是否开启触达注意力图，默认 default 表示开启，自动采集 $WebStay 事件，可以设置 'not_collect' 表示关闭
    // 需要 JSSDK 版本号大于 1.9.1
    // scroll_notice_map: "not_collect"
  },
  use_app_track: window.isApp, // 配置打通 App 与 H5 的参数
  is_track_single_page: true, // 单页面
  app_js_bridge: true
})
if (getUrlParamObj().env === 'minip') {
  // 修改 H5 上报数据的 distinctID
  sensors.identify(getUrlParamObj().distinctID, true)
  // 再执行全埋点 autoTrack 其他操作
}
const { materialType } = getUrlParamObj()
// 以异步加载 SDK 为例，神策 SDK 初始化完成，此时调用设置公共属性的方法，来保证之后的事件都有这两个属性。
sensors.registerPage({
  pub_platform_type: 'H5',
  pub_app_name: '上汽奥迪',
  app_name: '4ring',
  current_url: location.href,
  referrer: document.referrer,
  pub_is_login: !!store.state.isLogin,
  $latest_scene: getUrlParamObj().scene || 'H5',
  utm_material: materialType || ''
})
sensors.quick('autoTrack')

Vue.prototype.$sensors = sensors
Vue.prototype.$EventBus = EventBus


// // 挂载高德地图
// AMapLoader.load({
//   key: '14e5933fd8cfbc2be9b2ab98e74feefc',
//   plugins: [], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
//   Loca: {} // 是否加载 Loca， 缺省不加载
// })

new Vue({
  store,
  router,
  el: '#app',
  render: (h) => h(App)
})
