<template>
    <div class="container">
      <div class="connter">
        <div class="connter-list-item">
            <span>姓名</span>
            <p>{{ infoData.userFullName }}</p>
        </div>
        <div class="connter-list-item">
            <span>身份证号</span>
            <p>{{ infoData.idCard }}</p>
        </div>
        <div class="connter-list-item">
            <span>手机号</span>
            <p>{{ infoData.phoneNumber }}</p>
        </div>
        <div class="connter-list-item">
            <span>工作单位</span>
            <p>{{ infoData.employerName }}</p>
        </div>
        <div class="connter-list-item">
            <span>其他信息</span>
            <p>{{ infoData.moreInfo }}</p>
        </div>
        <div class="connter-list-item-y">
            <span>是否直系亲属购车</span>
            <img
            v-if="infoData.isFamily!=1"
            style="width: 58px;
            height: 32px;"
            src="../../assets/img/Switch1.png"
            >
            <img
            v-else
            style="width: 58px;
            height: 32px;"
            src="../../assets/img/Switch2.png"
            >
        </div>
        <div class="connter-list-item" v-if="infoData.isFamily==1">
            <span>姓名</span>
            <p>{{ infoData.familyBuyersName }}</p>
        </div>
        <div class="connter-list-item" v-if="infoData.isFamily==1">
            <span>份证号</span>
            <p>{{ infoData.familyBuyersIdCard }}</p>
        </div>
        <div class="connter-list-item" v-if="infoData.isFamily==1">
            <span>手机号</span>
            <p>{{ infoData.familyBuyersPhoneNumber }}</p>
        </div>
        <div class="connter-img-icar">
            <p>身份证</p>
            <div class="connter-img-icar-list">
                <div class="connter-img-icar-list-item" v-for="(item,index) in infoData.materialIdCards" :key="index">
                    <img :src="item.materialUrl" alt="">
                </div>
            </div>
        </div>
        <div class="connter-img">
            <p>其他证明材料</p>
            <div class="qt">
                * {{ dataCopy }}
            </div>
            <div class="connter-img-list">
                <div class="connter-img-list-item" v-for="(item,index) in infoData.materialAddInfos" :key="index">
                    <img :src="item.materialUrl" alt="">
                </div>
            </div>
        </div>
        <div class="connter-img-icar" v-if="infoData.materialFamilyIdCards?.length>0&&infoData.isFamily==1">
            <p>直系亲属身份证</p>
            <div class="connter-img-icar-list">
                <div class="connter-img-icar-list-item" v-for="(item,index) in infoData.materialFamilyIdCards" :key="index">
                    <img :src="item.materialUrl" alt="">
                </div>
            </div>
        </div>
        <div class="connter-img" v-if="infoData.materialFamily?.length>0&&infoData.isFamily==1">
            <p>直系亲属证明材料</p>
            <div class="connter-img-list">
                <div class="connter-img-list-item" v-for="(item,index) in infoData.materialFamily" :key="index">
                    <img :src="item.materialUrl" alt="">
                </div>
            </div>
        </div>
        <div style="height: 90px;">

        </div>
      </div>
    </div>
  </template>
  
  <script>
  import Vue from 'vue'
  import navigation from '../../components/navigation'
  import { callNative } from '@/utils'
  import {
  queryCheckCustomizeTagInformation,
} from '@/api/api'

  export default {
    components: { navigation },
    data() {
      return {
        backType: 'app',
        infoData:{},
        dataCopy:'需上传的材料为：在职证明、近三个月社保参保证明、车补证明'
      }
    },
    mounted() {
      // 判断跳转来源
      const { tag,id } = this.$route.query
      switch (this.tag) {
      case 'BF':
        this.dataCopy='需上传的材料为：在职证明、近三个月社保参保证明、车补证明'
        break;
      case 'B4':
        this.dataCopy='需上传的材料为：在职证明、近三个月社保参保证明、区域签字报告、AMS在册截图（如为门店管理层）'
        break;
      case 'BG':
        this.dataCopy='需上传的材料为：国外学历学位认证书、护照、出入境证明'
        break;
      case 'B1':
        this.dataCopy='需上传的材料为：外交证件、使馆敲章证明文件'
        break;
      case 'BC':
        this.dataCopy='需上传的材料为：身份证、在职证明、近三个月社保参保证明'
        break;
      case 'B6':
        this.dataCopy='需上传的材料为：购车申请表'
        break;
      case 'B7':
        this.dataCopy='需上传的材料为：购车申请表'
        break;
      case 'BI':
        this.dataCopy='需上传的材料：身份证、工作证、在职证明（或三个月以内社保参保证明）'
        break;
      default:
        break;
    }
      this.queryCheckCustomizeTagInformation(tag,id)
    },
    methods: {
    queryCheckCustomizeTagInformation(tag,id) {
      let params={
        tag:tag,
        informationId:id
      }
      queryCheckCustomizeTagInformation(params).then((res) => {
        if (res.data.data) {
              // this.readonly = true
              this.infoData = {
                ...res.data.data
              }
              if (res.data.data.isFamily == 1) {
                this.infoData.isFamily = true
              } else {
                this.infoData.isFamily = false
              }
            }
  
      })
    },
    }
  }
  </script>
  
  <style lang="less" scoped>
  .container{
    padding: 0 16px;
    height: 100vh;
    .connter{
        margin-top: 8px;
    }
    .connter-list-item{
        height: 56px;
        border-bottom: 0.5px solid #E5E5E5;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        span{
            font-size: 16px;
            line-height: 24px;
            color: #000000;
            width: 84px;
            font-family: 'Audi-Normal';

        }
        p{
            color: #333333;
            font-size: 16px;
            font-family: 'Audi-Normal';
        }
    }

    .connter-list-item-y{
        height: 56px;
        border-bottom: 0.5px solid #E5E5E5;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        span{
            font-size: 16px;
            line-height: 24px;
            color: #000000;
            width: 130px;
            font-family: 'Audi-Normal';

        }
    }
    .connter-img-icar{
        margin-top: 36px;
        p{
            font-size: 16px;
            line-height: 24px;
            color: #000000;
            font-family: 'Audi-Normal';

        }
        .connter-img-icar-list{
            display: flex;
            justify-content: space-between;
            .connter-img-icar-list-item{
                width: 45vw;
                img{
                    width: 100%;
                }
            }
        }
    }
    .connter-img{
        margin-top: 36px;
        .qt{
            padding: 8px 16px;
            margin-bottom: 10px;
            font-size: 12px;
            color: #333333;
            line-height: 22px;
            background-color: #F4F4F4;
            position: relative;
            top: -11px;
            left: -16px;
            width: calc(100vw - 28px) ;
        }  
        p{
            font-size: 16px;
            line-height: 24px;
            color: #000000;
            font-family: 'Audi-Normal';

        }
        .connter-img-list{
            display: flex;
            flex-wrap: wrap;
            // justify-content: flex-start;
            .connter-img-list-item{
                width: 29vw;
                height: 109px;
                margin-bottom: 8px;
                margin-right:8px;
                img{
                    width: 100%;
                    height:100%;
                }
            }
        }
    }
  }
  
  </style>
  