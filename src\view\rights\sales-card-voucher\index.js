/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-09-05 17:16:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-09-07 15:52:15
 * @FilePath     : \src\view\rights\sales-card-voucher\index.js
 * @Descripttion :
 */
import Vue from 'vue'
import {
  Icon, Cell
} from 'vant'
import HeaderCustom from '@/components/header-custom.vue'
import {
  getOrderEquities
} from '@/api/api'
import couponMap from '@/config/coupon.map'
import './index.less'

Vue.use(Icon).use(Cell)
export default {
  components: {
    'header-custom': HeaderCustom
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      couponStatus: couponMap.CARD_VOUCHER_STATUS,
      equityList: [],
      exceptionTips: 0
    }
  },
  created() {
    this.handleGetCarPaymentInfo()
  },
  methods: {
    async handleGetCarPaymentInfo() {
      const { orderId } = this.$route.query
      const { data: { data: allEquityList } } = await getOrderEquities({ orderId })
      allEquityList.some((i) => !['10', '90', '96'].includes(i.status)) && (this.exceptionTips = 1)
      allEquityList.map((i) => {
        i.deductOff /= 100
        return i
      })
      this.equityList = allEquityList
    },
    handleLeftBack() {}
  }
}
