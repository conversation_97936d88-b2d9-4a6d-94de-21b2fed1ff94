<template>
  <div class='_detail' v-if='detail'>
    <navigation title='商品详情' :back-type="backType">
      <div class="nav_right">
        <img @click="updateFavorite" v-if="isFavori" src="../../assets/img/icon-collec-cur.png">
        <img @click="updateFavorite" v-else src="../../assets/img/icon-collec.png">
        <img @click="onProductShare" src="../../assets/img/icon-share.png">
        <!-- <img src="../../assets/img/icon-shopping.png"> -->
      </div>
    </navigation>
    <div style="padding:10px 16px;">
      <swipe :swipe-list="swipeList" height="180"></swipe>
      <div class="_content">
        <div class="_title">
          {{detail.prodName}}
          <img @click="goAppInto" src="../../assets/img/icon-kefu.png">
        </div>
        <div class="_label" v-show="detail.prodLabel.length > 0">
          <div v-for="(item,index) in detail.prodLabel" :key='index'>{{item.label}}</div>
        </div>
        <div class="_desc" v-show="detail.prodSalePoints">
          {{detail.prodSalePoints}}
        </div>
        <div class="_preice">
          {{ getIntegralPay(detail) }}
        </div>
        <div class="_sku border-top border-bottom" @click="setSku" v-if="skuName">
          <p>规格</p>
          <div class="_item-sku">{{skuName}}</div>
          <van-icon name="arrow" color='#999' />
        </div>
        <div style="padding-bottom: 200px;padding-top: 16px;" v-html="detail.prodContentMobile"></div>
      </div>
    </div>
    <div class="foot-bottom">
      <AudiButton text="立即购买" @click='skuModel = true' color="black" height="56px" font-size="16px" />
    </div>

    <van-action-sheet v-model="skuModel" title="">
      <div class="_content">
        <van-icon class="_cross" name="cross" @click='skuModel = false' />
        <div class="selet-sku">
          <img :src="detail.smallImgUrl[0].imageUrl">
          <div class="_right">
            <div class="text1">
              {{ getIntegralPay(detail) }}
            </div>
            <div class="text2" v-show="detail.defaultProdSku.stockActual>0">库存 有货</div>
            <div class="text2">{{skuName}}</div>
          </div>
        </div>
        <div class="_list border-bottom" v-if="this.orderType === 9 || this.orderType === 2">
          <div class="_title">适用门店</div>
          <div class="_sku3">
            <div class="_left">服务商</div>
            <div class="_right" @click="toDealer">
              <div class="text-hide">
                {{serviceProvider ? serviceProvider.dealerName : '选择服务商'}}
              </div>
              <van-icon style="margin-left: 3px;" name="arrow" color='#333' />
            </div>
          </div>
        </div>
        <div class="_scroll">
          <div class="_list" v-show="item.propName !== '配送方式'" 
              :class="[index !== skulist.length-1&&'border-bottom']" v-for="(item,index) in skulist" :key="index">
            <div class="_title">{{item.propName}}</div>
            <div class="_sku2">
              <div @click="getSkuIndex(index,val.propValueId)" :class="[item.propValueId === val.propValueId && 'selet']" v-for="(val,index2) in item.propValue"
                :key="index2">
                {{val.propValue}}
              </div>
            </div>
          </div>
          <!-- <div class="_title">购买数量
            <div class="_stepper">
              <van-icon name="minus" @click="reduce" :color="num < 2  ? '#999':'#333'" />
              <div class="num">{{num}}</div>
              <van-icon name="plus" @click="add" :color="num < stockActual  ? '#333':'#999'" />
            </div>
          </div> -->
        </div>
        <div class="_sku-button">
          <AudiButton text="立即购买" @click="confirmOrder" color="black" height="56px" font-size="16px" />
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
  import swipe from "./swipe.vue"
  import { mapState } from "vuex"
  import { callNative } from '@/utils/'
  import AudiButton from '@/components/audi-button'
  import navigation from '../../components/navigation.vue';
  import { getProductDetail, isFavorited, updateFavorite, getAfterSalesByOrderId } from "../../api/api.js"
  import Vue from 'vue';
  import { ActionSheet, Toast } from 'vant';
  Vue.use(ActionSheet);
  Vue.use(Toast);
  export default {
    components: { swipe, navigation, AudiButton },
    data() {
      return {
        swipeList: [],
        detail: null,
        skuModel: false,
        num: 1,
        skulist: [],
        skuName: '',
        stockActual: 1,
        isFavori: false, //是否收藏商品
        backType: 'app',
        orderType:null,//商品类型
        orderId:null,
        dealerIdList:null,//服务商code列表
      }
    },
    computed: {
      ...mapState({
        serviceProvider: (state) => state.serviceProvider || null, //服务商
        isInit: (state) => state.isInit, //是否是第一次进来
      })
    },
    watch: {
      isInit: {
        immediate: true,
        handler(newValue, oldValue) {
          if(!newValue) this.skuModel = true
        },
      }
    },
    mounted() {
      this.getProductDetail()
      this.isFavorited()
      
      this.orderId = this.$route.query?.orderId
      //判断跳转来源
      const { backType } = this.$route.query
      if (backType) {
        this.backType = backType
      } else {
        this.backType = 'app'
      }
    },
    methods: {
      setSku() {
        this.skuModel = true
      },
      async getProductDetail() { //获取商品详情
        const { skuId } = this.$route.query
        const res = await getProductDetail({ id: skuId })
        this.swipeList = res.data.data.prodImages.filter(i => i.imageType === 2 && i.showType === 1)
        var dealerIdList = res.data.data.prodAttrs.find(i=>i.propName === '经销商')
        if(dealerIdList){
          dealerIdList = JSON.parse(dealerIdList?.propValue).dealerIdList
        }
        this.dealerIdList = dealerIdList 
        res.data.data.prodSkus.map(i => {
          i.skuPropsJson = JSON.parse(i.skuPropsJson)
        })
        this.detail = res.data.data
        this.orderType = parseInt(res.data.data.defaultProdSku.skuType)
        this.detail.smallImgUrl = res.data.data.prodImages.filter(i=> i.imageType === 1 && i.showType === 1)
        this.skulist = this.detail.saleAttrs
        if (this.skulist.length < 1) {
          this.skulist = JSON.parse(this.detail.defaultProdSku.skuPropsJson)
        }
        const skuProps = this.detail.defaultProdSku.skuProps
        //实际库存
        this.stockActual = this.detail.defaultProdSku.stockActual
        //显示的sku名称
        const skuName = []
        console.log(this.skulist)
        this.skulist.forEach(i => {
          if (i.prodSkuPropValues) i.propValue = i.prodSkuPropValues;
          i.propValue.forEach(j => {
            if (skuProps.indexOf(j.propValueId) !== -1 ) {
              i.propValueId = j.propValueId
              
              if(j.propValue !== '无需配送')skuName.push(j.propValue)
            }
          })
        })
        if (this.orderType === 2 || (this.orderType === 9 && this.orderId)) {
          this.getAfterSalesByOrderId() //整车订单商品不能修改服务商  使用默认服务商地址
        }
        console.log(this.skulist)
        this.skuName = skuName.join(',')
        this.getIntegralPay(res.data.data)
      },

      reduce() {
        if (this.num > 1) {
          this.num -= 1
        }
      },
      add() {
        if (this.num < this.stockActual) {
          this.num += 1
        }
      },

      getSkuIndex(index, propValueId) { //获取选中的sku
        this.skulist[index].propValueId = propValueId
        const skuName = []
        this.skulist.forEach(i => i.propValue.forEach((j, index) => {
          if (i.propValueId === j.propValueId) skuName.push(j.propValue)
        }))
        this.skuName = skuName.join(',')
        this.getStockActual()
        this.$forceUpdate()
      },

      getStockActual() { //获取实际的库存
        let skuProps = this.skulist.map(i => {
          let ids = ''
          i.propValue.forEach(j => {
            if (i.propValueId === j.propValueId) ids = i.propId + ":" + j.propValueId
          })
          return ids
        })
        this.detail.prodSkus.forEach(i => {
          if (i.skuProps === skuProps.join(';')) {
            this.stockActual = i.stockActual
          }
        })
        console.log(this.stockActual)
      },
      isServiceShow(){
        if(this.orderType === 9 && !this.orderId){ //协助安装
          return true
        }
        return false
      },
      confirmOrder() {
        if (this.num > this.stockActual) {
          Toast({ type: 'fail', message: '库存不足', icon: require('../../assets/img/contract-fail.png') })
          return
        }
        if (this.isServiceShow() && !this.serviceProvider) {
          Toast({ type: 'fail', message: '请选择服务商', icon: require('../../assets/img/contract-fail.png') })
          return
        }
        //判断默认服务商是否 在 运营侧创建商品时选中的服务商列表中
        if (this.orderType === 2 || (this.orderType === 9 && this.orderId)) {
          if(this.serviceProvider && this.dealerIdList){
            if(!this.dealerIdList.includes(this.serviceProvider.dealerCode)){
              Toast({ type: 'fail', message: '等待业务输入', icon: require('../../assets/img/contract-fail.png') })
              return
            }
          }
        }

        let skuProps = this.skulist.map(i => {
          let ids = ''
          i.propValue.forEach(j => {
            if (i.propValueId === j.propValueId) ids = i.propId + ":" + j.propValueId
          })
          return ids
        })
        let skuId = ''
        this.detail.prodSkus.forEach(i => {
          if (i.skuProps === skuProps.join(';')) {
            skuId = i.prodSkuId
          }
        })
        const { orderId } = this.$route.query
        this.$store.commit('saveisInit', true)
        this.$router.push({
          path: '/confirm-order',
          query: {
            skuId,
            orderId,
            quantity: this.num,
          }
        })
        console.log(skuProps)
        console.log(skuId)
      },
      
      async getAfterSalesByOrderId() { //获取订单绑定的服务商
        const { orderId } = this.$route.query
        const { data } = await getAfterSalesByOrderId({ orderId })
        if (data.data) {
          const userAddress = {
            receiverName: data.data.dealerName,
            mobile: data.data.dealerPhone,
            province: data.data.provinceName,
            city: data.data.cityName,
            district: data.data.areaName,
            details: data.data.dealerAdrress,
            addreType: '服务商'
          }
          this.$store.commit('saveappUserAddress', userAddress)
          this.$store.commit('updateServiceProvider', data.data)
        } else {
          this.buttonStatus = false
          Toast({ type: 'fail', message: '该商品暂无服务商', icon: require('../../assets/img/contract-fail.png') })
        }
      },
      
      goAppInto() { //打开客服
        callNative('openRoutePath', {
          path: 'scaudi://customServe/into/mall'
        })
      },
      async isFavorited() { //查询是否收藏商品
        const { skuId } = this.$route.query
        const res = await isFavorited({ targetId: skuId, type: 3 })
        this.isFavori = res.data.data
      },
      async updateFavorite() { //收藏获取取消收藏商品
        const { skuId } = this.$route.query
        const { data } = await updateFavorite({ targetId: skuId, type: 3 })
        if (data.code === '00') {
          this.isFavori = !this.isFavori
        }
      },
      onProductShare() { //分享
        let params = {
          type: "productDetail",
          id: this.detail.defaultProdSku.prodSkuId,
          title: this.detail.prodName,
          summary: this.detail.prodSalePoints,
          thumbnailImg: this.detail.prodImages[0].imageUrl,
          communityArticleId: this.detail.defaultProdSku.prodSkuId
        };
        callNative("audiShare", params);
      },
      toDealer() {
        if (this.orderType === 2 || (this.orderType === 9 && this.orderId)) {
          return
        }
        this.$store.commit('saveisInit', false)
        this.$router.push({
          path: '/aftersales/select-service-providers-list',
          query: {
            isBack: true,
            dealerIdList: this.dealerIdList,
          }
        })
      },
      getIntegralPay(product) {
        //显示价格
        if (product.defaultProdSku.skuPrice === 999990) {
          //特殊商品 不显示价格
          return ''
        } else {
          if (product.creditPercent === product.creditPercentFloor && product.creditPercent === 100) {
            // 表示全奥金支付
            return product.defaultProdSku.skuPrice * 10 + "奥金"
          } else if (product.creditPercent == product.creditPercentFloor && product.creditPercent == 0) {
            // 表示不可以使用奥金支付 全部使用金钱支付
            return "¥ " + (product.defaultProdSku.skuPrice / 100).toString()
          } else if (product.creditPercent === product.creditPercentFloor && product.creditPercent < 100 && product.creditPercent > 0) {
            // 固定价格和固定奥金以支付
            return "¥ " + (product.defaultProdSku.skuPrice * (100 - product.creditPercent) / 10000).toString() +
              " | " + (product.defaultProdSku.skuPrice * 10 * product.creditPercent / 10000) + "奥金"
          } else if (product.creditPercent > 0 && product.defaultProdSku.skuPrice > 0) {
            //表示可以混合支付
            return "¥ " + (product.defaultProdSku.skuPrice).toString() + product.defaultProdSku.skuPrice * 10 + "奥金"
          } else {
            return "¥ " + (product.defaultProdSku.skuPrice / 100).toString()
          }
        }
      },
    }
  }
</script>

<style lang='less' scoped>
  .border-top {
    border-top: 1px solid #E5E5E5;
  }

  .border-bottom {
    border-bottom: 1px solid #E5E5E5;
  }

  div p {
    box-sizing: border-box;
    font-family: "Audi-Normal";
  }

  p {
    margin: 0;
  }

  .nav_right {
    display: flex;
    align-items: center;

    img {
      width: 24px;
      height: 24px;
      margin-left: 16px;
    }
  }

  ._content {
    display: flex;
    flex-flow: column;
    width: 100%;

    ._title {
      padding: 13px 0;
      margin-top: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #000000;
      font-size: 16px;

      img {
        width: 25px;
        height: 25px;
      }
    }

    ._desc {
      font-size: 12px;
      color: #000000;
      line-height: 20px;
    }

    ._preice {
      margin-top: 16px;
      margin-bottom: 22px;
      font-size: 18px;
      line-height: 22px;
    }

    ._label {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      div {
        height: 28px;
        background: #F2F2F2;
        padding: 0 16px;
        line-height: 28px;
        margin-right: 10px;
        font-size: 12px;
        color: #000000;
      }
    }

    ._sku {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 16px 0;

      > p {
        white-space: nowrap;
      }

      ._item-sku {
        width: calc(100% - 55px);
        padding: 0 20px;
        font-size: 14px;
        line-height: 16px;
        text-align: right;
      }
    }
  }

  .foot-bottom {
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
    background: #FFFFFF;
    width: 100vw;
    position: fixed;
    box-sizing: border-box;
    bottom: 0;
    left: 0;
    padding: 16px;
    padding-bottom: calc(env(safe-area-inset-bottom));

    .button {
      margin-bottom: 8px;
    }
  }

  ::v-deep .van-popup--round {
    border-radius: 0;
  }

  .van-action-sheet__content {

    ._content {
      padding: 19px 16px;
      position: relative;
      background-color: #fff;
      box-sizing: border-box;
      font-family: "Audi-Normal";

      ._cross {
        position: absolute;
        top: 21px;
        right: 16px;
      }

      .selet-sku {
        width: 100%;
        display: flex;
        align-items: center;
        background-color: #fff;
        justify-content: space-between;
        padding-bottom: 15px;

        img {
          width: 88px;
          height: 88px;
          object-fit: cover;
        }

        ._right {
          width: calc(100% - 88px);
          padding: 7px 0;
          padding-left: 16px;
          display: flex;
          flex-flow: column;

          .text1 {
            font-size: 18px;
            line-height: 22px;
          }

          .text2 {
            font-size: 12px;
            line-height: 15px;
            margin-top: 10px;
          }
        }
      }

      ._scroll {
        min-height: 100px;
        height: 150px;
        max-height: 200px;
        overflow: scroll;
      }

      ._list {
        display: flex;
        flex-flow: column;
        width: 100%;

        ._sku2 {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          padding-bottom: 14px;

          div {
            height: 24px;
            background: #F2F2F2;
            padding: 0 16px;
            line-height: 24px;
            font-size: 12px;
            color: #333333;
            margin-right: 16px;
            margin-bottom: 10px;

            &.selet {
              background: #000000;
              color: #FFFFFF;
            }
          }
        }
        ._sku3{
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          padding-bottom: 10px;
          
          ._left{
            color: #000 !important;
          }
          
          ._right{
            display: flex;
            align-items: center;
            color: #000;
            width: 75%;
            justify-content: flex-end;
          }
        }
      }

      ._title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #333333;
        padding: 15px 0;
        line-height: 24px;

        ._stepper {
          display: flex;
          widtwidth: 100%;
          align-items: center;
          border: 1px solid #979797;
          box-sizing: border-box;

          .num {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-left: 1px solid #979797;
            border-right: 1px solid #979797;
          }

          i {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }

      ._sku-button {
        padding-top: 16px;
        width: 100%;
        background-color: #fff;
      }
    }

  }
</style>
