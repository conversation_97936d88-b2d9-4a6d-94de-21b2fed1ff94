<template>
  <div
    class="loading"
    v-if="loading"
  >
    <van-loading
      color="#000"
      class="loadingVant"
      vertical
      type="spinner"
    >
      加载中...
    </van-loading>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  data() {
    return {
      loading:true
    }
  },
  methods:{
    show(status) {
      this.loading = status
    }
  }
}
</script>
<style scoped lang="less">
.loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  

}
/deep/.van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%);
  z-index: 999999;
}
</style>
