<template>
  <div class="option-popup">
    <div
      class="option-cover"
    />
    <div class="option-main">
      <header>
        <div>请做出一个选择</div>
      </header>
      <main>
        <option-popup-card
          title="您想增加："
          type="text"
          :list="[clickOption]"
        />
        
        <option-popup-card-plus ref="optPopuRef"
          v-if="dependSortArray && dependSortArray.length > 0 && currentSibColorInterieur.sibOptionCode == 'N4X' && selectCarInfo.modelLineName.includes('星耀')"
          :title="clickOption.interieurName?'您需要在下方装备中选择一个加装：':'您必须选择以下选装：'"
          type="checkbox"
          :list="dependSortArray"
        />
        <div v-else>
          <option-popup-card
            v-if="dependSortArray && dependSortArray.length > 0"
            :title="clickOption.interieurName?'您需要在下方装备中选择一个加装：':'您必须选择以下选装：'"
            type="checkbox"
            :list="dependSortArray"
          />
        </div>
        
        <div  v-if="conflictList && conflictList.length > 0">
          <option-popup-card
            v-if="conflictList && conflictList.length > 0"
            title="以下选装配置将会被移除："
            type="imgbox"
            :list="conflictList"
          />
          <div style="font-size: 12px;color: #999;" v-if="clickOption.optionCode == 'WTY' && conflictList.find(e => e.optionCode == 'PS1')">
            尊享六座套装（七座->六座）中包含隐私静音玻璃
          </div>
        </div>
      
      </main>
      <footer>
        <div class="footer-desc" v-if="selectCarInfo.seriesName != 'Q6'">
          <span>当前选项的价格已更新：</span>
          <span>¥{{ price | formatPrice }}</span>
        </div>
        <div
          class="button confirm"
          @click="handleConfirm"
        >
          确认
        </div>
        <div
          class="button cancel"
          @click="handleCancel"
          v-if="!clickOption.interieurName"
        >
          取消
        </div>
      </footer>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { Toast } from 'vant'
import {
  resolveConflict, outputChangeList, outputCurrentOptionsList, outputData
} from '@/utils/sp'
import url from '@/config/url'
import OptionPopupCard from './option-popup-card.vue'
import OptionPopupCardPlus from './option-popup-card-plus.vue'
import { arraySortByKeys } from '@/utils'

import {
  defPatchesFn04, patchesPS138,defPatchesWA3 
} from '@/view/configration/fix_configration/ccFn.js'
import { checkV2 } from '@/utils'



export default {
  components: { OptionPopupCard, OptionPopupCardPlus },
  data() {
    return {
      dependList: [],
      dependSortArray: [],
      conflictList: [],
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      price: 0
    }
  },
  computed: {
    ...mapState([
      'currentTabIndex',
      'idx',
      'clickOption',
      'otherClickOption',
      'selectCarInfo',
      'currentOptionsList',
      'interiorChairList',
      'sibColorInterieurList',
      'interiorEihList',
      'modelHubList',
      'changeList',
      'currentModelHub',
      'currentInteriorChair',
      'currentInteriorEih',
      'currentSibColorInterieur',
      'privateOrderList',
      'allSibColorInterieurList',
      'allInteriorChairList'
    ]),
    ...mapGetters(['packetItem', 'allOptionsItems'])
  },
  mounted() {
    if ('G4IBC3001,G4ICC3001'.includes(this.selectCarInfo.modelLineCode) && 'N4X'.includes(this.clickOption.sibOptionCode)) {
      const tmp = this.privateOrderList.find((e) => e.optionCode === '4A4')
      this.$store.commit('setOtherClickOption', tmp)
    }
  },
  watch: {
    clickOption: {
      handler(next) {
        if (next) {
          // clickOption  当前点击的选装包
          // allOptionsItems  当前选装包中的选装件
          // selectedCarInfo  当前选中的配置线
          // currentOptionslList  当前的选中的选装包（件）列表
          // chair： 当前选中的座椅
          // sibcolor ： 当前选中的面料````
          const _obj = {
            clickOption: this.clickOption,
            allOptionsItems: this.allOptionsItems,
            selectCarInfo: this.selectCarInfo,
            currentOptionsList: [...this.currentOptionsList, next],
            currentInteriorChair: this.currentInteriorChair || {},
            currentSibColorInterieur: this.currentSibColorInterieur || {},
            currentInteriorEih: this.currentInteriorEih || {},
            price: next.price,
            q6: this.idx == 2
          }
          const _result = resolveConflict(_obj)
          console.log("_obj======", _obj);
          console.log("_result======", _result);
          // arraySortByKeys
          let dependSortArray
          if (_result.tempdependList && _result.tempdependList.length > 0) {
            dependSortArray = arraySortByKeys(_result.tempdependList, ['category', 'sibInterieurCategory'])
            if (this.idx == 2 && dependSortArray && dependSortArray[0]?.children) {
              let sib =  this.currentSibColorInterieur.sibInterieurCode
              let obj = dependSortArray[0]?.children?.find(e => e.sibInterieurCode == sib)
              if (obj && sib) {
                dependSortArray = []
              }
            }
          }

// 齐云飞骑、齐云羽林
          let modelLineCode = this.selectCarInfo.modelLineCode
          let sib = this.$store.state.currentSibColorInterieur?.sibInterieurCode
          let WTY = this.clickOption.optionCode == "WTY"
          let G6IBAY001 = 'G6IBAY001'.includes(modelLineCode) && '"N5D-TX,N5D-TT"'.includes(sib)
          let G6ICAY001 = 'G6ICAY001'.includes(modelLineCode) && "N5D-AW".includes(sib)
          let ps1 = this.currentOptionsList.find(e => e.optionCode == 'PS1')
          if (WTY && (G6IBAY001 || G6ICAY001) && ps1) {
            let children =  this.$store.state.allPrivateOrderList
            let arr = JSON.parse(JSON.stringify(children))
            let temp =  arr.filter(e=> "PS8,PS3".includes(e.optionCode))
            temp.forEach(e => {
              e.defVis = 1
            })
            dependSortArray = [
              {
                children: temp
              }
            ]
            this.$store.commit('setOtherClickOption', temp[0])
          }

          let confCodeArr = []
          let confOpion = []
          if (this.idx == 2 && this.clickOption?.optionRelates && this.clickOption?.optionRelates?.length > 0) {
            this.clickOption?.optionRelates.forEach(e => {
              if (e.relateType == "conflict" && e.optionRelateCategory == "PACKET") {
                confCodeArr.push(e.optionRelateCode)
              }
            });
            confCodeArr.forEach(e => {
              let obj = this.currentOptionsList.find(o => o.optionCode == e)
              if (obj) {
                confOpion.push(obj)
              }
            });

            if (confOpion.length > 0) {
               this.conflictList = confOpion
            }
          }

          this.price = _result.price
          this.dependSortArray = dependSortArray
          this.dependList = _result.tempdependList
          if (this.idx != 2) this.conflictList = _result.tempconflictList
          console.log("tempconflictList=====", _result.tempconflictList);
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleClickCover: function () {
      // this.$store.commit('setShowOptionPopup', false)
    },
    handleCancel: function () {
      this.$store.commit('setShowOptionPopup', false)
    },
    handleConfirm: function () {
      // 埋点
      this.$sensors.track('clickOptionalPackage', {
        pop_up_name: this.clickOption.optionName,
        amount_of_money: this.clickOption.price
      })
      // changeList中包含了用户已经选中的依赖项
      const { changeList, dependList } = this
      let selectedPackageEquipment = []
      if (this.currentTabIndex == 3 && changeList.PACKET && this.idx == 1 && !checkV2()) {
        selectedPackageEquipment = [changeList.PACKET, ...this.currentOptionsList]
      }else {
        selectedPackageEquipment = this.currentOptionsList
      }

      const parma = {
        equipment: this.clickOption,
        conflicts: this.conflictList,
        depends: this.dependList,
        sibSelected: changeList.sib,
        eihSelected: changeList.eih,
        himSelected: changeList.him,
        selectedPackageEquipment: selectedPackageEquipment,
        packagesList: this.privateOrderList,
        composeVosList: this.allInteriorChairList.filter((val) => val.status === 1 || val.status === 2),
        composeSibColorList: this.allSibColorInterieurList.filter((val) => val.status === 1 || val.status === 2)
      }
      const obj = outputData(parma)
      console.log("obj===============", obj);
      console.log("changeList===============", changeList);
      console.log("parma===============", parma);
      if (obj.PackagesListEvent) {
        this.$store.commit('setPrivateOrderList', obj.PackagesListEvent)
      }
      if (obj.VosListEvent) {
        this.$store.commit('setInteriorChairList', obj.VosListEvent)
      }

      if (obj.SelectedVosEvent) {
        this.$store.commit('setCurrentInteriorChair', obj.SelectedVosEvent)
      }
      if (obj.EihListEvent) {
        this.$store.commit('setInteriorEihList', obj.EihListEvent)
      }
      if (obj.SelectedEihEvent) {
        this.$store.commit('setCurrentInteriorEih', obj.SelectedEihEvent)
      }
      if (obj.SibColorListEvent) {
        if (this.idx != 2) {
          this.$store.commit('setSibColorInterieurList', obj.SibColorListEvent)
        }
      }

      if (obj.SelectedSibColorEvent) {
        if (this.idx != 2) {
          this.$store.commit('setCurrentSibColorInterieur', obj.SelectedSibColorEvent)
        }
      }
      
      if (obj.SelectedPackageEquipmentEvent) {
        if ('G4IBC3001,G4ICC3001'.includes(this.selectCarInfo.modelLineCode) && ('4A4,WE8'.includes(this.clickOption.optionCode) || 'N4X'.includes(this.clickOption.sibOptionCode))) {
          if (this.clickOption.sibOptionCode === 'N4X') {
            const flagindex = this.currentOptionsList.findIndex((e) => this.otherClickOption.optionCode === e.optionCode)
            if (this.otherClickOption.optionCode === '4A4') {
              const flagindex1 = this.currentOptionsList.findIndex((e) => e.optionCode === 'WE8')
              if (flagindex1 !== -1) {
                this.$store.commit('setDelOption', flagindex1)
              }
            }
            if (this.otherClickOption.optionCode === 'WE8') {
              const flagindex1 = this.currentOptionsList.findIndex((e) => e.optionCode === '4A4')
              if (flagindex1 !== -1) {
                this.$store.commit('setDelOption', flagindex1)
              }
            }
            if (flagindex === -1) {
              this.$store.commit('setCurrentOptionsList', [
                ...this.currentOptionsList,
                this.otherClickOption
              ])
            }
          }
          if (this.clickOption.optionCode === 'WE8') {
            obj.PackagesListEvent = obj.PackagesListEvent.filter((e) => e.optionCode !== '4A4')
          }
          if (this.clickOption.optionCode === '4A4') {
            obj.PackagesListEvent = obj.PackagesListEvent.filter((e) => e.optionCode !== 'WE8')
          }
        } else {
          if (this.idx == 2) {
            // this.otherClickOption 
            let arr = []
            obj.SelectedPackageEquipmentEvent.forEach(e => {
              if (e.optionCode) {
                arr.push(e)
              }
            });
            if (arr.length == 0) {
              this.$store.commit('setCurrentOptionsList', [...this.currentOptionsList, this.otherClickOption])
            } else {
              let title = this.$store.state.title.includes('私人定制')
              if (this.otherClickOption.optionCode && !title) {
                this.$store.commit('setCurrentOptionsList', [...arr, this.otherClickOption])
              } else {
                this.$store.commit('setCurrentOptionsList', arr)
              }
            }
          } else {
            this.$store.commit('setCurrentOptionsList', obj.SelectedPackageEquipmentEvent)
          }
          
        }
      }
      // 某些装备包中带必选装备 遍历当前装备包中的装备 与依赖装备进行对比 如果依赖项中已有该配置项,则无视,若没有,则选中
      // const changeListTemp = outputChangeList(changeList, dependList, this.clickOption)
      // 从选装列表中吧互斥的选装包删掉
      // const currentOptionsList = outputCurrentOptionsList(this.conflictList, this.currentOptionsList, this.clickOption)
      // 放入vuex
      // this.$store.commit('setChangeList', changeListTemp)
      // this.$store.commit('setCurrentOptionsList', currentOptionsList)
      // 根据changeList选中选装包中的各个配置放入vuex，影响到其他tab
      // for (const key in changeListTemp) {
      //   if (Object.hasOwnProperty.call(changeListTemp, key)) {
      //     switch (key) {
      //       case 'sib':
      //         if (changeListTemp[key] && Object.keys(changeListTemp[key]).length > 0) {
      //           this.$store.commit('setCurrentSibColorInterieur', changeListTemp[key])
      //         }
      //         break
      //       case 'vos':
      //         if (changeListTemp[key] && Object.keys(changeListTemp[key]).length > 0) {
      //           this.$store.commit('setCurrentInteriorChair', changeListTemp[key])
      //         }
      //         break
      //         // case 'rad':
      //         //   if (Object.keys(changeListTemp[key]).length > 0) {
      //         //     this.$store.commit('setCurrentModelHub', changeListTemp[key])
      //         //   }
      //         //   break
      //         // case 'eih':
      //         //   if (Object.keys(changeListTemp[key]).length > 0) {
      //         //     this.$store.commit('setCurrentInteriorEih', changeListTemp[key])
      //         //   }
      //         //   break
      //       default:
      //         break
      //     }
      //   }
      // }


      /**
       * @story  选中按摩/通风 时，提示用户另外一个标装将被移除, 用户点击确认后，再将另外一个标装自动隐藏掉
       * 
       * <AUTHOR>
       */
      /* @optionCode 前排座椅按摩 8I6 */
      /* @optionCode 前排座椅通风 4D3 */
      let _this = this
      let tempArrPackagesList = obj.PackagesListEvent || this.$store.state.allPrivateOrderList // 获取私人定制列表数据
      let chooseObj = obj.SelectedPackageEquipmentEvent[0] || {}
      if (chooseObj.optionCode === "8I6" || chooseObj.optionCode === "4D3" ) {
        if (chooseObj.optionCode === "8I6") filterFn('4D3')
        if (chooseObj.optionCode === "4D3") filterFn('8I6')
      }
      function filterFn(t) {
        let arr = []
        arr = tempArrPackagesList.filter(e => e.optionCode !== t)
        // 更新私人定制列表数据
        _this.$store.commit('setPrivateOrderList', arr) 
        // 更新价格
        _this.$store.commit('setCurrentOptionsList', [
          ..._this.currentOptionsList,
        ])
      }
      
      defPatchesFn04(this) //补丁： 私人定制六选一的时候选中加热座椅失败的bug 
      // patchesPS138(this)

      // defPatchesWA3(this)

      // 齐云飞骑、齐云羽林
      let modelLineCode = this.selectCarInfo.modelLineCode
      let sib = this.$store.state.currentSibColorInterieur?.sibInterieurCode
      let WTY = this.clickOption.optionCode == "WTY"
      let G6IBAY001 = 'G6IBAY001'.includes(modelLineCode) && '"N5D-TX,N5D-TT"'.includes(sib)
      let G6ICAY001 = 'G6ICAY001'.includes(modelLineCode) && "N5D-AW".includes(sib)
   
      if (WTY && (G6IBAY001 || G6ICAY001) ) {
          let arrBox = JSON.parse(JSON.stringify(this.currentOptionsList))
        // this.$store.commit('setPrivateOrderList', tempArrPackagesList) 
        // let ps1 = arrBox.find(e => e.optionCode == 'PS1')
        // if (!ps1 ) {
        //   return
        // }
        let temp = [
          ...arrBox.filter(e => e.optionCode != 'PS1'),
          this.otherClickOption
        ]
        this.$store.commit('setCurrentOptionsList', temp)
      }

      // 关掉弹出窗
      this.$store.commit('setShowOptionPopup', false)
      // this.$store.commit('setOptionPopupInit')
    }

  }
}
</script>

<style lang="less" scoped>
.option-popup {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    .option-cover {
        width: 100%;
        height: 100%;
        position:absolute;
        top: 0;
        left: 0;
        background: rgba(0,0,0,.5);
        z-index: -1;
    }
    .option-main{
        width: calc(100% - 32px);
        height: 80%;
        background-color: #fff;
        margin: auto 16px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        padding: 16px;
        box-sizing: border-box;
        header {
            font-size: 14px;
            font-family: Audi-WideBold;
            color: #000000;
            line-height: 20px;
            margin-bottom: 13px;
        }
        main {
          height: calc(100% - 167px - 32px);
          overflow-y: auto;
          -ms-overflow-style: none;
          overflow: -moz-scrollbars-none;
          &::-webkit-scrollbar { width: 0 !important }
        }
        footer{
          width: calc(100% - 32px);
          position: absolute;
          bottom: 16px;
          display: flex;
          flex-direction: column;
          border-top: 1px solid #e5e5e5;
          .footer-desc {
            font-size: 12px;
            font-family: Audi-WideBold;
            color: #000000;
            line-height: 50px;
            display: flex;
            justify-content:space-between;
          }
          .button {
            width: 100%;
            height: 56px;
            background: #fff;
            border: 1px solid #1A1A1A;
            box-sizing: border-box;
            display: flex;
            justify-content:center;
            align-items: center;
            &.confirm{
              background: #1A1A1A;
              color: #fff;
              margin-bottom: 4px;
            }
          }
        }
    }
}
</style>
