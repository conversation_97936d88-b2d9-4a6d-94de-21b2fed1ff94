---
kind: pipeline
name: fondev
platform:
  os: linux
  arch: amd64
steps:
  - name: build-front
    pull: if-not-exists
    image: node
    settings:
      mirror: https://docker.mirrors.ustc.edu.cn
    commands:
      - npm install
      - npm run build-dev
  - name: code-analysis
    image: mailbyms/drone-sonar-plugin:v1
    settings:
      sonar_host:
        from_secret: sonar_host
      sonar_token:
        from_secret: sonar_token
  - name: notify
    image: kmsp/drone-plugin-dingtalk:1.0.0
    settings:
      url: http://**************/robot/send
      token:
        from_secret: dingtalk_token
    when:
      status:
        - failure
        - success
volumes:
  - name: drone_home
    host:
      path: /data/drone/home
trigger:
  branch:
    - dev
  event:
    - push
...