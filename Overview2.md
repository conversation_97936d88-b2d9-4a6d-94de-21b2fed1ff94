# 奥迪汽车订购H5项目 - 小功能梳理

## 1. 车辆配置模块 (Vehicle Configuration)

### 1.1 车型选择功能
**功能描述**: 用户选择不同车系和车型配置线

**核心代码示例**:
```javascript
// 选择车型配置线
async toSelectModel(item) {
  if (this.currentModelLineData.suit === item.suit) {
    return
  }
  
  // A7MR车型特殊处理
  if (this.isA7MrCar) {
    if (DONGGAN_CODE.includes(item.optionCode)) {
      // 动感套装，选中选装包
      for (const option of this.currentOptions) {
        if (option.optionCode === item.optionCode) {
          option.selected = true
          this.$store.commit('updateSelectedOptions', [option])
          break
        }
      }
    } else {
      // 新潮套装,清空选装包
      this.$store.commit('updateSelectedOptions', [])
    }
  }
  
  this.$store.commit('updateCurrentModelLineData', item)
}
```

### 1.2 外观配置功能
**功能描述**: 车辆外观颜色和轮毂选择

**核心代码示例**:
```javascript
// 外观配置修改
async resizeCar(item, key) {
  this.paramDto = await this.$store.dispatch("getOmdModelConfigParams", { item, key });
  const carConfig = await postOmdModelConfig(this.paramDto);
  
  if (carConfig.data.data.code !== "20000") {
    return this.$toast.fail(carConfig.data.data.message);
  }
  
  this.prompt = carConfig.data.data.result[0]?.prompt;
  await this.$store.commit("updateCurrentVersion", carConfig.data.data.result[0]);
  await this.$store.dispatch("setRadEquipmentGroup");
  await this.$store.dispatch("setIIEquipmentGroup");
}
```

### 1.3 价格计算功能
**功能描述**: 实时计算配置价格和交付时间

**核心代码示例**:
```javascript
// 价格计算
async getPriceCompute(params) {
  const { data } = await getPriceCompute(params)
  if (data.code === '00') {
    this.currentPrice = data.data.price
    this.deliveryTime = data.data.deliveryTime
    this.$store.commit('setCurrentPrice', data.data.price)
  }
}
```

## 2. 订单管理模块 (Order Management)

### 2.1 订单创建功能
**功能描述**: 创建车辆订单并处理支付流程

**核心代码示例**:
```javascript
// 创建订单
export const createOrder = async (params, goods, spare = {}, mobile) => {
  const { data } = (await carPlaceOrder(params)) || ''
  
  // 缓存存oldccid 下次进入更新
  localStorage.setItem('oldCcid', 'oldCcid')
  
  if (data?.code && RES_SUCCEED_CODE.includes(data.code)) {
    const { orderId } = data.data || ''
    const orderStatus = '00'
    
    if (orderId && ORDER_PASSING_CODE.includes(orderStatus)) {
      await saveKfLog({
        key:"原生支付的接口：callNative('callPayCenter')", 
        value:data.data && JSON.stringify(data.data),  
        source:"audi-order-h5"
      })
      payOrder(orderId, { ...goods, orderStatus }, spare, mobile)
      return true
    }
  }
}
```

### 2.2 购物车功能
**功能描述**: 添加配置到购物车

**核心代码示例**:
```javascript
// 添加配置单到购物车
async addConfigFn() {
  if (!this.checkLoginFn()) return // 检查登录状态
  
  this.$store.commit('showLoading')
  const { ccid, skuid, inviteBuyCarCode } = this.$store.state
  const shoppingCartId = this.$route.query?.shoppingCartId || ''
  const entryPoint = this.$storage.getPlus('entryPoint')
  
  const { data } = await addCarShoppingCart({
    ccid, skuid, invitationCode: inviteBuyCarCode, shoppingCartId, entryPoint
  })
  
  if (data.code === '00') {
    this.$store.commit('hideLoading')
    Toast({
      message: data.message,
      className: 'toast-dark-mini toast-pos-middle',
      forbidClick: true,
      duration: 800
    })
  }
}
```

### 2.3 订单提交功能
**功能描述**: 表单验证和订单提交

**核心代码示例**:
```javascript
// 表单提交
async submit() {
  const { orderLuckyBags, isModelStock } = this || 0
  
  // 检测福包关联车型库存
  if (orderLuckyBags && isModelStock !== 1) {
    this.emptyBookPopShow = true
    return
  }
  
  // 判断代理商一致性
  if (this.orgCode && this.dealerCode !== this.orgCode) {
    this.orgShow = true
    return
  }
  
  const timer = new Date().getTime()
  if (this.isIdentical) {
    this.updateForm()
  }
  
  if (this.checked) {
    this.$refs.form.submit()
  } else {
    Toast({
      className: 'toast-dark-mini toast-pos-middle',
      message: '请阅读并勾选定金协议',
      forbidClick: true,
      duration: 800
    })
  }
  
  // 埋点
  this.dataCollection(timer)
}
```

## 3. 支付流程模块 (Payment Process)

### 3.1 支付令牌获取
**功能描述**: 获取安全支付令牌

**核心代码示例**:
```javascript
// 获取submit-token
export const getSubmitToken = () => request({
  url: `${baseUrl}/api-wap/cop-auth/api/v1/submit-tokens`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 提交支付
export const postPayments = async (data) => {
  const res = await getSubmitToken()
  if (['200', '00'].includes(res.data.code)) {
    return request({
      url: `${baseUrl}/api-wap/cop-order/api/v1/payments`,
      method: 'POST',
      headers: {
        'x-access-token': localStorage.getItem('token'),
        'x-submit-token': res.data.data
      },
      data: { ...data }
    })
  }
}
```

### 3.2 支付状态查询
**功能描述**: 轮询查询支付结果

**核心代码示例**:
```javascript
// 查询支付结果
function loopQuery(i) {
  setTimeout(async () => {
    // 小订支付意向金后的逻辑
    if (action === 'xiaoding') {
      const res = await getOrderLimitedNumberStatus({ 
        orderId: orderId || localStorage.getItem('orderId') 
      })
      
      if (codeType.includes(res.data.code)) {
        if (res.data.data.status === 1) { // 支付成功
          const resss = await getLimitedNumber({ 
            orderId: orderId || localStorage.getItem('orderId') 
          })
          that.$store.commit('hideLoading')
          
          let path
          if (codeType.includes(resss.data.code)) {
            if (!resss.data.data) {
              path = '/order/payment-error' // 无限量号
            } else {
              path = '/order/payment-success' // 有限量号
            }
          }
        }
      }
    }
  }, 2000)
}
```

## 4. 试驾预约模块 (Test Drive Booking)

### 4.1 试驾预约创建
**功能描述**: 创建试驾预约订单

**核心代码示例**:
```javascript
// 提交试驾预约订单
async submitTestDriverOrder() {
  const data = {
    inclinedModel: this.formData.testdriveSeries.customSeriesName == 'A7L' 
      ? this.formData.inclinedModel : '',
    seriesCode: this.formData.testdriveSeries.seriesCode,
    appoPhone: this.formData.mobile,
    code: this.formData?.code || '',
    appoName: this.formData.fullName,
    appoType: '90030',
    orgCode: this.formData?.agent?.dealerCode,
    activityCode: '0001',
    channel: 2,
    inviteCode: this.inviteCode
  }
  
  let subFun = createInviteTestDriver
  if (this.youkeVis) {
    subFun = createTouristYouke
    data.mobile = data.appoPhone
    delete data.appoPhone
  }
  
  const result = await subFun(data)
  // 处理预约结果...
}
```

### 4.2 试驾预约列表
**功能描述**: 获取用户试驾预约列表

**核心代码示例**:
```javascript
// 获取试驾预约列表
async testDriverList() {
  const data = {
    appoType: '90030',
    page: 1,
    size: 999
  }
  
  const { data: { data: list } } = await testDriverList(data)
  
  if (list?.data?.length) {
    list.data.forEach((l) => {
      const [date, t1] = l.appoBeginTime.split(' ')
      const [d, t2] = l.appoEndTime.split(' ')
      const [seriesCode] = AUTOMOBILE.filter((i) => l.seriesId === i.seriesCode)
      
      l.date = date
      l.time = `${t1} - ${t2}`
      l.path = seriesCode.seriesPathName
      l.state = TESTDRIVE_STATUS.find((i) => i.status.includes(l.status))
    })
    this.list = list.data
  }
}
```

### 4.3 试驾跳转功能
**功能描述**: 从报价页跳转到试驾预约

**核心代码示例**:
```javascript
// 预约试驾
tryCar() {
  this.clickQuotationSensors('预约试驾') // 埋点
  
  const seriesCode = this.carSeries.seriesCode
  this.$router.push({
    path: '/testdrive/create',
    query: {
      idx: ['49', 'G4', 'G6', 'F0'].findIndex((e) => e == seriesCode)
    }
  })
}
```

## 5. 用户认证模块 (User Authentication)

### 5.1 登录状态检查
**功能描述**: 检查用户登录状态

**核心代码示例**:
```javascript
// 检查登录状态
export const checkLoginStatus = async () => {
  const { env, token } = getUrlParamObj();
  
  if (env === "minip") {
    // 小程序环境
    const isLogin = +!!token;
    if(token){
      store.commit("setIsLogin", 1)
      storage.set("token", token);
    } else {
      store.commit("setIsLogin", 0)
      storage.remove("token", token)
    }
  } else {
    // APP环境
    const userInfo = await getUserInfo({'sync': 'login'})
    store.commit("setIsLogin", +userInfo.isLogin)
    
    if(+userInfo.isLogin){
      storage.set("token", userInfo.token);
    } else {
      storage.remove("token", userInfo.token);
      storage.remove("userId", userInfo.userId);
    }
  }
}
```

### 5.2 原生APP用户信息获取
**功能描述**: 从原生APP获取用户信息

**核心代码示例**:
```javascript
// 获取原生APP用户信息
async getNativeAppUserInfo({ commit, state }, p = {}) {
  const { isLogin, isAuth, isGuest } = p
  const params = {}
  params.sync = 'login'
  
  if (isAuth && !isGuest) {
    // 是否鉴权
    params.sync = 'authenticate'
  }
  
  let res = ''
  const { env } = getUrlParamObj()
  
  if (env != 'test') {
    res = await callNative('getAudiUserInfo', params)
  }
  
  res = res || {
    token: '', userId: 0, refreshToken: '', isLogin: 0
  }
  
  commit('setLogin', res)
}
```

## 6. 状态管理模块 (State Management)

### 6.1 配置状态管理
**功能描述**: 管理车辆配置相关状态

**核心代码示例**:
```javascript
// 配置状态定义
state: {
  currentCarInfo: [],
  projectStartTime: 0,
  carIdx: "", // 0: a7l, 1: q5e, 2: q6
  currentCarSeriesData: {},
  carModelActiveTab: 'power',
  configrationActiveTab: 'exterior',
  currentVersionData: [],
  outColorArray: [], // 外观颜色列表
  hubArray: [], // 轮毂列表
  sibArray: [], // 内饰面料列表
  eihArray: [], // 饰板列表
  vosArray: [], // 座椅列表
  deliveryTimeData: {} // 交付时间数据
}
```

### 6.2 车辆配置Actions
**功能描述**: 处理车辆配置相关的异步操作

**核心代码示例**:
```javascript
// 获取车辆配置数据
import {
  getCarList,
  getModellineList,
  getExterior,
  getModelHub,
  getInterior,
  getPrivateOrder,
  getPriceCompute,
  getDeliveryTimeCompute
} from '@/configratorApi/index'

// Actions示例
const actions = {
  async getCarListAction({ commit }, params) {
    const { data } = await getCarList(params)
    if (data.code === '00') {
      commit('setCarList', data.data)
    }
  },
  
  async getPriceComputeAction({ commit }, params) {
    const { data } = await getPriceCompute(params)
    if (data.code === '00') {
      commit('setCurrentPrice', data.data.price)
      commit('setDeliveryTime', data.data.deliveryTime)
    }
  }
}
```

## 总结

该项目通过模块化的方式实现了完整的汽车销售业务流程，每个模块都有明确的职责分工：

- **车辆配置模块**: 提供丰富的车辆定制选项
- **订单管理模块**: 处理完整的订单生命周期
- **支付流程模块**: 确保安全可靠的支付体验
- **试驾预约模块**: 提供便捷的试驾服务
- **用户认证模块**: 保障用户身份安全
- **状态管理模块**: 统一管理应用状态

每个模块都采用了Vue.js的最佳实践，结合Vuex进行状态管理，使用组件化开发提高代码复用性。
