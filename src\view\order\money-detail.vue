<template>
  <div
    class="moneyDetail"
    :style="{
      paddingBottom: navigationHeight,
    }"
  >
    <pay-status
      :knap-status="orderKnapStatus"
      :max-status="orderMaxStatus"
      :count-down="countDown"
      :is-one-step="isOneStep"
    />
    <div
      v-if="[...ORDER_STATUS_DISTRICT.DA_DING_BEFORE, ...ORDER_STATUS_DISTRICT.REFUND, ...ORDER_STATUS_DISTRICT.REFUNDED, ...ORDER_STATUS_DISTRICT.DIES].includes(orderStatus)"
      class="pgc-page"
      data-flex="cross:center"
      @click="handleGoToPGCPage"
    >
      <span>◆</span> 购车流程全了解，看看你离提车还差几步 <span>>>></span>
    </div>
    <div
      class="order-lifecycle"
      v-if="[...ORDER_STATUS_DISTRICT.DA_DING_AFTER, ...ORDER_STATUS_DISTRICT.EBSCO].includes(lifecycleStatus) && modelNameCn"
    >
      <van-tabs
        v-model="lifecycleCurrent"
      >
        <van-tab
          v-for="(list,index) in ORDER_STATUS_DISTRICT_LIFE_MIN_CYCLE"
          :key="index"
          :title="list.text"
          :name="list.name"
          :title-class="[!(ORDER_STATUS_DISTRICT_LIFE_MIN_CYCLE.findIndex((i) => i.status === lifecycleStatus) >= index) || 'lan-tab--active', `status-${list.name}`]"
        >
          {{ list.name }}
        </van-tab>
      </van-tabs>
    </div>
    <div
      class="order-life-in"
      v-if="isSignedOnlineContract && ![...ORDER_STATUS_DISTRICT.REFUND,...ORDER_STATUS_DISTRICT.REFUNDED,...ORDER_STATUS_DISTRICT.DIES].includes(orderStatus) && isTransparency"
      @click="toOrderSteps"
      data-flex="main:justify cross:center"
    >
      <span>您的车辆已按照顺序进入资源准备流程</span>
      <div
        class="right"
        data-flex="cross:center"
      >
        <span :class="['has-warning', !warning.lifecycle || 'is-warning']">
          查看进度
        </span><van-icon name="arrow" />
      </div>
    </div>
    <div
      :class="['goods-box', !(!isSignedOnlineContract && ![...ORDER_STATUS_DISTRICT.DA_DING_AFTER, ...ORDER_STATUS_DISTRICT.EBSCO].includes(lifecycleStatus) && !ORDER_STATUS_DISTRICT.DA_DING_BEFORE.includes(orderStatus)) || 'has-border-top']"
    >
      <div
        class="goods-info"
        data-flex="main:justify"
      >
        <div
          class="goods-left"
          data-block
          data-flex="box:wrap"
        >
          <h2 class="h2 text-two-hidd">
            {{ modelNameCn }}
          </h2>
          <div
            class="price"
            data-flex="main:left box:bottom cross:center"
          >
            <img
              v-if="priceTagShow"
              class="tags"
              src="../../assets/img/carPriceTag.png"
            >
            <span v-else>总价：</span>
            <span><font>{{ totalPrice | prefixFormatPrice }}</font></span>
          </div>
        </div>
        <div
          class="goods-right"
          data-flex="main:center cross:center"
        >
          <img :src="$loadWebpImage(sideSeriesImgUrl)">
        </div>
      </div>
      <div
        v-if="!['98', ...ORDER_STATUS_DISTRICT.DA_DING_BEFORE].includes(orderStatus)"
        :class="['goods-handle', !goodsInfoHandle || 'goods-unfold']"
        @click="handleGoodsInfoAction"
        data-flex="cross:center main:center"
      >
        <span class="handle">{{ goodsInfoHandle ? '收起': '展开' }}订单</span>
      </div>
    </div>
    <div
      class="group-block-box fill-block-gray"
      v-show="goodsInfoHandle"
    >
      <div
        class="finance-wrapper"
      >
        <h3>车辆配置</h3>
        <div
          class="status"
          @click="lookCar"
        >
          <van-icon
            name="arrow"
            size="16px"
          />
        </div>
      </div>
      <div
        class="finance-wrapper"
      >
        <h3>购买信息</h3>
        <div
          class="status"
          @click="toModifyInfoPage"
        >
          <van-icon
            name="arrow"
            size="16px"
          />
        </div>
      </div>
      <div
        class="finance-wrapper"
      >
        <!-- 配置器 客户权益 -->
        <h3>客户权益</h3>
        <div
          class="status"
          @click="handleGoToRightsPage"
        >
          <van-icon
            name="arrow"
            size="16px"
          />
        </div>
      </div>
    </div>
    <div
      class="group-block-box fill-block-gray"
      v-if="limitNumber || !limitNumber && LIMIT_NUMBERSS_IN.includes(currentModelLineCode) && [...[(carSeries.seriesCode !== LIMIT_NUMBERS_A7L.seriesCode) || '30'], ...ORDER_STATUS_DISTRICT.DA_DING].includes(orderStatus) && (isOneStep ? carSeries.seriesCode !== '49' && isSignedOnlineContract : true)"
    >
      <div
        class="finance-wrapper"
        v-if="!(showLineUpNumber && total > 0)"
      >
        <h3>限量号</h3>
        <div
          class="status"
          @click="handleCheckLimitedNumber"
        >
          <span :class="['right-tips-text','has-warning', limitNumber || 'is-warning']">{{ limitNumber || '待选号' }}</span>
          <van-icon
            name="arrow"
            size="16px"
          />
        </div>
      </div>
      <!--  排队号 1-->
      <div
        class="finance-wrapper ish3-long"
        v-else
      >
        <h3>限量号前方等待人数</h3>
        <div
          class="status"
        >
          <span>{{ total }}人</span>
        </div>
      </div>
    </div>
    <div
      class="group-block-box fill-block-gray"
      v-if="isShowChargingPile"
      v-show="['G4'].includes(carSeries.seriesCode)"
    >
      <!-- 00 待支付 30 小订 301 大定待支付 31 大定 32 尾款 90 交车 98订单超时（小订） 99 退订 -->
      <div
        class="finance-wrapper"
      >
        <h3>充电权益</h3>
        <div
          class="status"
          @click="toChargingPilePage"
        >
          <span :class="['right-tips-text','has-warning', ((['200'].includes(chargingStatus) || ['1102', '1203', '0002'].includes(chargingStatus) && equityOrderStatus === 1) || equityOrderStatus === 2) ? 'is-warning' : '']">{{ chargingPileButtonText }}</span>
          <van-icon
            name="arrow"
            size="16px"
          />
        </div>
      </div>
    </div>

    <div
      class="group-block-box fill-block-gray"
      v-if="getdeliveyShow() && ![...ORDER_STATUS_DISTRICT.REFUNDED,...ORDER_STATUS_DISTRICT.DIES].includes(orderStatus) && !isOneStep"
    >
      <div
        class="finance-wrapper"
        @click="toDeliveryPattern"
      >
        <h3 style="_title">
          交车方式
        </h3>
        <div class="status">
          <span v-if="[...ORDER_STATUS_DISTRICT.DIES, ...ORDER_STATUS_DISTRICT.REFUND].includes(this.orderStatus)">{{ deliveryTypeObject[deliveryType] || '' }}</span>
          <span
            v-else
            :class="['right-tips-text','has-warning', !!deliveryTypeObject[deliveryType] || 'is-warning']"
          >{{ deliveryTypeObject[deliveryType] || '' }}</span>
          <van-icon
            name="arrow"
            size="16px"
          />
        </div>
      </div>
    </div>
    <div class="group-block-box fill-block-gray">
      <div
        class="finance-wrapper"
        v-if="isSignedOnlineContract && parseInt(orderStatus) > 0 && ![...ORDER_STATUS_DISTRICT.REFUNDED,...ORDER_STATUS_DISTRICT.DIES].includes(orderStatus)"
      >
        <h3>信贷服务</h3>
        <div
          class="status"
          @click="toFinancePage(1)"
        >
          <span :class="['right-tips-text','has-warning', !isLoansWarning || 'is-warning']">{{ financeButtonText }}</span>
          <van-icon
            name="arrow"
            :color="(this.isConfirmed === true || ['90', '98', '99', '32', '80', '81', '84'].includes(this.orderStatus) || this.equityAmount === -1) ? '#e5e5e5' : 'inherit'"
            size="16px"
          />
        </div>
      </div>
      <div
        class="finance-wrapper"
        v-if="orderStatus && orderDetail.orderStatus !== '99'"
      >
        <h3>购车协议</h3>
        <div
          class="status"
          @click="toSignContract"
        >
          <!-- {{ isSignedOnlineContract? '已签署':'未签署' }} -->
          <span :class="['has-warning', (orderStatus === '00' || (orderStatus === '31' && !isSignedOnlineContract) || orderStatus === '32' && +vehicleStatus === 3) ? 'is-warning' : '']" />
          <van-icon
            name="arrow"
            size="16px"
          />
        </div>
      </div>
      <div
        class="finance-wrapper"
        v-if="isProgres()"
        v-show="goodsInfoHandle && ['G4'].includes(carSeries.seriesCode)"
      >
        <h3>牌照进度</h3>
        <div
          class="status"
        >
          <span class="right-tips-text">{{ queryLicenseStatus[licenseStatus] }}</span>
        </div>
      </div>
      <div
        class="finance-wrapper"
        v-if="orderStatus === '90' && orderDetail.contractInfo.deliveryContractBoolOnline"
        @click="toGuaranteesVoucher"
      >
        <h3 style="_title">
          三包凭证
        </h3>
        <div class="status">
          <van-icon
            name="arrow"
            size="16px"
          />
        </div>
      </div>
    </div>
    <div
      class="order-info-box fill-block-gray"
      v-if="!['81'].includes(orderStatus)"
    >
      <div
        class="title van-hairline--bottom"
        data-flex="main:justify cross:center"
      >
        <h2 class="h2">
          付款明细
        </h2>
      </div>
      <div class="order-config-list van-hairline--bottom">
        <div
          class="list"
          data-flex="main:justify"
          v-if="(+orderStatus > 30 && orderStatus !== '301')"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span
              class="name"
            >车辆总价</span>
          </p>
          <p class="right">
            <span>{{ totalPrice | prefixFormatPrice }}</span>
          </p>
        </div>
        <div
          :class="['list', !(orderStatus === '00') || 'selected']"
          data-flex="main:justify"
          v-if="(['00','30', '301'].includes(orderStatus) || +orderStatus >= 30 && !ORDER_STATUS_DISTRICT.DIES.includes(orderStatus) && (formData.carBuyerInfo.buyType === '01' && !zeroPayment)) && !isOneStep"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span
              :class="['name', orderStatus !== '00' || 'left']"
            >{{ orderStatus === '00' ? '待' : '已' }}支付意向金</span>
          </p>
          <p class="right price">
            <span>{{ ORDER_STATUS_DISTRICT.DA_DING.includes(orderStatus) ? '-' : '' }} {{ ((orderStatus !== '00' ? firstPaidAmount : depositPrice.prodNgaDepositPrice) / 100) | prefixFormatPrice }}</span>
          </p>
        </div>
        <div
          :class="['list', !['30','301'].includes(orderStatus) || 'selected']"
          data-flex="main:justify"
          v-if="['30', '301'].includes(orderStatus) || +orderStatus >= 30 && !ORDER_STATUS_DISTRICT.DIES.includes(orderStatus) && (formData.carBuyerInfo.buyType === '01' && !zeroPayment)"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span
              class="name"
            >{{ ORDER_STATUS_DISTRICT.DA_DING_BEFORE.includes(orderStatus) ? '待' : '已' }}支付定金</span>
          </p>
          <p class="right price">
            <span>{{ ORDER_STATUS_DISTRICT.DA_DING.includes(orderStatus) ? '-' : '' }} {{ ((ORDER_STATUS_DISTRICT.DA_DING.includes(orderStatus) ? lastPaidAmount : (depositPrice.prodNgaDownPayPrice - (isOneStep ? 0 : depositPrice.prodNgaDepositPrice))) / 100) | prefixFormatPrice }}</span>
          </p>
        </div>
        <div
          class="list"
          data-flex="main:justify"
          v-if="(paymentMethod === '20' && +loanAmount)"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span
              class="name"
            >贷款金额</span>
          </p>
          <p class="right">
            <span>{{ loanAmount ? '-' : '' }} {{ loanAmount | prefixFormatPrice }}</span>
          </p>
        </div>
        <div
          class="list"
          data-flex="main:justify"
          v-if="(equityAmount && equityAmount !== -2)"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span
              class="name"
            >专属权益</span>
          </p>
          <p
            class="right"
            @click="$router.push({ name: 'rights-sales-card-voucher', query: { orderId : $route.query.orderId } })"
            data-flex="main:right cross:center"
          >
            <span
              v-if="equityAmount > 0"
            >- {{ equityAmount | prefixFormatPrice }}</span>
            <span
              v-else
              class="c-bold has-warning is-warning"
            >卡券不可用</span>
            <van-icon
              name="arrow"
              size="14px"
            />
          </p>
        </div>
        <div
          class="list selected"
          data-flex="main:justify"
          v-if="+orderStatus >= 31 && !['301', ...ORDER_STATUS_DISTRICT.DIES].includes(orderStatus)"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span class="type">{{ [...ORDER_STATUS_DISTRICT.DA_DING].splice(1).includes(orderStatus) ? '已' : '待' }}支付尾款</span>
          </p>
          <p class="price">
            <span>{{ balancePayment | prefixFormatPrice }}</span>
          </p>
        </div>
        <div
          class="buy-t02 buy-t03"
          v-if="equityAmount === -1"
        >
          <div
            class="tips-banner"
          >
            *专属权益卡券暂只支持叠加使用，由于存在失效卡券导致卡券不可用，请联系专属奥迪管家咨询卡券资格。
          </div>
        </div>
        <div
          class="buy-t02"
          v-if="(formData.carBuyerInfo.buyType === '02' || zeroPayment) && +orderStatus >= 31"
        >
          <div
            class="list"
            data-flex="main:justify"
            v-if="!isOneStep"
          >
            <p
              class="p"
              data-flex="main:left"
            >
              <span
                class="name"
              >已支付意向金</span>
            </p>
            <p class="right">
              <span>{{ (firstPaidAmount / 100) | prefixFormatPrice }}</span>
            </p>
          </div>
          <div
            class="list"
            data-flex="main:justify"
          >
            <p
              class="p"
              data-flex="main:left"
            >
              <span
                class="name"
              >已支付定金</span>
            </p>
            <p class="right">
              <span>{{ (lastPaidAmount / 100) | prefixFormatPrice }}</span>
            </p>
          </div>
          <div
            class="tips-banner"
          >
            *上汽奥迪确认收到尾款后，将退回意向金及定金至您的原付款账户
          </div>
        </div>
      </div>
    </div>
    <div class="order-info-box">
      <div
        class="title van-hairline--bottom"
        data-flex="main:justify cross:center"
      >
        <h2 class="h2">
          订单信息
        </h2>
      </div>
      <div class="order-config-list">
        <div
          class="list"
          data-flex="main:justify"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span
              class="name"
            >订单编号</span>
          </p>
          <p
            class="right"
            id="orderId"
            :data-clipboard-text="formData.orderId"
          >
            <span>{{ formData.orderId }}</span>
            <font class="separator">
              |
            </font><span
              class="copy-text-btn"
              @click="copy"
            >复制</span>
          </p>
        </div>
        <div
          class="list"
          data-flex="main:justify"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span
              class="name"
            >下单时间</span>
          </p>
          <p class="right">
            <span>{{ formData.orderTime }}</span>
          </p>
        </div>
        <div
          class="list"
          data-flex="main:justify"
          v-if="buyerRemark"
        >
          <p
            class="p"
            data-flex="main:left"
          >
            <span
              class="name"
            >订单备注</span>
          </p>
          <p class="right">
            <span>{{ buyerRemark }}</span>
          </p>
        </div>
        <div
          class="list"
          data-flex="main:justify"
        >
          <!-- 订单的状态： //订单小订待付款  00，订单小订已下，但未支付 98，订单小订已付款 30，订单小订退款中 80，订单小订已退款 81 大定或小定已退款\n -->
          <p
            class="p min-text"
            data-flex="main:left cross:center"
            @click="goOrDialog"
            v-if=" !['81', '80', '99', '98', '31'].includes(orderStatus) && canRefund === 1"
          >
            {{
              orderStatus == "30"
                ? "申请退订"
                : "意向金订单协议"
            }}<van-icon
              name="arrow"
              color="#b7b7b7"
            />
          </p>
          <p class="right" />
        </div>
      </div>
    </div>
    <van-dialog
      v-model="voucherConfirmVisible"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <div class="text">
        尊敬的上汽奥迪车主，您的“三包凭证”正在生成中，请稍后再试。如不能显示请联系代理商协助，谢谢！
      </div>
      <div
        class="D-buttons"
        @click="voucherConfirmVisible = false"
      >
        好的
      </div>
    </van-dialog>

    <div
      class="orderInformation paading"
      v-if="MANAGER_ON_OFF.LONG_TEST_DRIVE"
    >
      <!-- 试驾入口 -->
      <div
        class="test-drive-box"
        v-if="testDrive.q5"
      >
        <div
          class="test-drive-media"
          @click="$router.push({ name:'long-test-drive-state' })"
        >
          <img :src="require('../../assets/img/Q5e-tron-02.jpg')">
          <div
            class="tips"
            data-flex="main:justify cross:center"
          >
            <span>预约超长试驾</span>
            <span class="btn">立即了解</span>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="orderInformation box" style="margin-bottom: 30px;" v-if="orderStatus === '31'||orderStatus === '32'">
      <div class="order-info-list border-top">
        <h3>原装附件推荐</h3>
      </div>
      <div>
        <swipe :swipe-list="floorslist" height="420" @change="toProductDetail"></swipe>
      </div>
    </div> -->
    <div
      class="orderInformation box"
      style="margin-bottom: 30px;"
      v-if="hasSpecCarRights()"
    >
      <div class="order-info-list border-top">
        <h3>增值权益</h3>
      </div>
      <div
        class="order-info-rights"
        @click="toSpecCarRights()"
      >
        <img :src="specCarRight" />
      </div>
    </div>

    <div
      class="btn-pay-wrapper"
      v-if=" orderStatus == '00' && payType !== '1' && countDownNum > 0 "
    >
      <div
        class="buttons"
        @click="toShowBuyCarAgreeMent"
      >
        去支付意向金
      </div>
    </div>


    <div
      class="btn-pay-wrapper"
      v-if="orderStatus === '00' && payType === '1' && !isUpload"
    >
      <div
        class="buttons2"
        @click="
          $router.push({
            path: '/order/upload-documents',
            query: {
              orderId: $route.query.orderId || '',
            },
          })
        "
      >
        数字人民币支付凭证上传
      </div>
    </div>

    <!-- // 小订转大定 去支付定金-->
    <div
      class="btn-pay-wrapper"
      v-if="(showPayPriceButton && canDdPay && !visBtn && equityAmount !== -2)"
    >
      <AudiButton
        borderColor="#B3B3B3"
        :text="isOneStep ? `去支付定金 ¥${depositAmount}` : '去支付定金 ¥'+ depositAmount + '（不可退还）'"
        color="#b3b3b3"
        font-size="16px"
        height="56px"
        :class="['black-btn', equityAmount !== -1 || 'freeze-btn']"
      />
    </div>

    <!-- 去支付尾款  allowRefund 是否退款退款  isSignedOnlineContract合同是否签署   vehicleStatus 验车状态 1未验车 2已验车 -->
    <div
      class="btn-pay-wrapper custom-ux-btn"
      v-if="(orderStatus === '31' && isSignedOnlineContract && !allowRefund && ['1', '2'].includes(vehicleStatus) && equityAmount !== -2)"
    >
      <AudiButton
        borderColor="#B3B3B3"
        :text="isBalancePayment ? '跳转中...' : '去支付尾款'"
        :class="[`black-btn`, isBalancePayment ? 'btn-un-enabled' : 'btn-enabled', equityAmount !== -1 || 'freeze-btn']"
        color="#b3b3b3"
        v-if="vehicleStatus === '2' && $route.query.env !== 'minip'"
        font-size="16px"
        height="56px"
      />
      <AudiButton
        borderColor="#B3B3B3"
        text="确认验车"
        color="#b3b3b3"
        v-if="vehicleStatus === '1' && !inspectionNum"
        font-size="16px"
        height="56px"
      />
    </div>
    <!-- 多次验车  inspectionNum验车的次数-->
    <div
      class="btn-pay-wrapper"
      v-if="(orderStatus === '31' || orderStatus === '32') && inspectionNum && vehicleStatus ==='1'"
    >
      <AudiButton
        borderColor="#B3B3B3"
        text="确认验车"
        color="#b3b3b3"
        font-size="16px"
        height="56px"
      />
    </div>
    <van-dialog
      v-model="payRestConfirmVisible"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <div class="text">
        确定验车吗？
      </div>
      <div
        class="dialog-box line-two-cols"
        data-flex="main:justify"
      >
        <div
          class="D-buttons2 btn"
          @click="payRestConfirmVisible = false"
        >
          取消
        </div>
        <div
          class="D-buttons btn"
          @click="amsConfCheckCar"
        >
          确定
        </div>
      </div>
    </van-dialog>

    <!-- 签署交车确认书-->
    <div
      class="btn-pay-wrapper"
      v-if="orderStatus === '32' && vehicleStatus === '3' && $route.query.env !== 'minip'"
    >
      <AudiButton
        text="签署交车确认书"
        borderColor="#B3B3B3"
        color="#b3b3b3"
        font-size="16px"
        height="56px"
      />
    </div>
    <rights-q5e-lite-prevent
      v-if="orderStatus === '32' && vehicleStatus === '3' && Q5E_LITE_MODEL_CODE.includes(this.modelCode) && ['200', '0002', 2000].includes(chargingStatus)"
      :rights-prevent.sync="rightsPreventShow"
      :order-id="$route.query.orderId"
    />

    <!-- 去签署合同-->
    <div
      class="btn-pay-wrapper"
      v-if="orderStatus === '31' && !isSignedOnlineContract"
    >
      <AudiButton
        text="去签署合同"
        borderColor="#B3B3B3"
        color="#b3b3b3"
        font-size="16px"
        height="56px"
      />
    </div>


    <!-- 确认交车 -->
    <div
      class="btn-pay-wrapper"
      v-if="orderStatus === '90' && vehicleStatus === '3' && $route.query.env !== 'minip'"
    >
      <AudiButton
        text="确认交车"
        borderColor="#B3B3B3"
        color="#b3b3b3"
        font-size="16px"
        height="56px"
      />
    </div>
    <van-dialog
      v-model="deliveryConfirmVisible"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <div class="text">
        确认交车吗？
      </div>
      <div
        class="dialog-box line-two-cols"
        data-flex="main:justify"
      >
        <div
          class="D-buttons2 btn"
          @click="deliveryConfirmVisible = false"
        >
          取消
        </div>
        <div
          class="D-buttons btn"
          @click="confimDelivery"
        >
          确定
        </div>
      </div>
    </van-dialog>

    <!-- 全车系保险 -->
    <popup-custom-action-btn
      v-if="popInsurance.conf && popInsurance.conf.enabled || false"
      :btn-conf="popInsurance.conf"
      :btn-items="popInsurance.items"
      @emitGetActionBtn="handleGetActionBtn"
    >
      <template #popup-custom-main>
        <template v-if="popInsurance.mineInsuranceVoucher === 2">
          <div class="head align-center">
            <h2>{{ popInsurance.desc[0] }}</h2>
          </div>
          <div class="text">
            <h3>{{ popInsurance.desc[1] }}</h3>
            <p>{{ popInsurance.desc[2] }}</p>
            <p>{{ popInsurance.desc[3] }}</p>
            <p>{{ popInsurance.desc[4] }}</p>
          </div>
        </template>
        <template v-else>
          <div class="align-center">
            {{ popInsurance.desc[5] }}
          </div>
        </template>
      </template>
    </popup-custom-action-btn>

    <!-- // 确认退款 -->
    <div
      class="btn-pay-wrapper"
      v-if="allowRefund"
    >
      <AudiButton
        borderColor="#B3B3B3"
        text="确认退款"
        color="#b3b3b3"
        font-size="16px"
        height="56px"
      />
    </div>

    <!-- //失效  确认退款 -->
    <div
      class="btn-pay-wrapper"
      v-if="visBtn"
    >
      <AudiButton
        borderColor="#B3B3B3"
        text="确认退款"
        color="#b3b3b3"
        font-size="16px"
        height="56px"
      />
    </div>


    <!-- // 退款的二次确认弹窗 -->
    <van-dialog
      v-model="refundConfirmVisible"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <div class="text">
        {{ getDDRefundText }}
      </div>
      <div
        class="dialog-box line-two-cols"
        data-flex="main:justify"
      >
        <div
          class="D-buttons2 btn"
          @click="confirmRefund"
        >
          坚持退款
        </div>
        <div
          class="D-buttons btn"
          @click="refundConfirmVisible = false"
        >
          我再想想
        </div>
      </div>
    </van-dialog>

    <!-- 小订退款确认弹窗 -->
    <van-dialog
      v-model="show"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <div class="text">
        {{ getXDRefundText }}
      </div>
      <div
        class="dialog-box line-two-cols"
        data-flex="main:justify"
      >
        <div
          class="D-buttons2 btn"
          @click="confim"
        >
          坚持退订
        </div>
        <div
          class="D-buttons btn"
          @click="think"
        >
          我再想想
        </div>
      </div>
    </van-dialog>


    <!-- 大定确认的弹窗  -->
    <van-dialog
      v-model="confirmOrderDialogVisible"
      :close-on-click-overlay="true"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <div class="dialog-wrapper">
        <div class="c-extendedBold c-font16">
          请您确认订单信息,如有修改请点击返回修改。
        </div>

        <div class="info-wrapper">
          <div class="c-margin-top">
            车辆信息
          </div>
          <div>
            车型: {{ modelNameCn }}
          </div>
          <div>
            颜色: {{ colorNameCn }}
          </div>
        </div>
        <div style="margin-top: 20px;" />

        <div class="info-wrapper">
          <div class="c-margin-top">
            购车人
          </div>
          <div>
            姓名: {{ carBuyerInfo.fullName }}
          </div>
          <div>
            手机号: {{ carBuyerInfo.mobile }}
          </div>
          <div>
            证件号: {{ carBuyerInfo.moreContact | filterIdentCode }}
          </div>
        </div>
        <div style="margin-top: 20px;" />

        <div
          class="info-wrapper"
          v-if="carBuyerInfo.buyType === '01'"
        >
          <div class="c-margin-top">
            车主
          </div>
          <div>
            姓名: {{ carBuyerInfo.carOwnerName }}
          </div>
          <div>
            手机号: {{ carBuyerInfo.carOwnerMobile }}
          </div>
          <div>
            证件号: {{ carBuyerInfo.carOwnerCertificateNumber }}
          </div>
        </div>

        <div style="margin-top: 20px;" />

        <div
          class="info-wrapper"
          v-if="carBuyerInfo.buyType === '02'"
        >
          <div>企业</div>
          <div>企业名称: {{ carBuyerInfo.enterpriseName }}</div>
          <div>组织架构代码: {{ carBuyerInfo.enterpriseCode }}</div>
        </div>

        <div style="margin-top: 20px;" />

        <div style="margin-top: 15px;" />

        <div class="btn-wrapper">
          <AudiButton
            @click="goPayMoney('dading')"
            text="我已确认"
            color="black"
            font-size="16px"
            height="54px"
          />
          <div style="margin-top: 10px;" />
          <AudiButton
            @click="toDetail"
            text="返回修改"
            color="white"
            font-size="16px"
            height="54px"
          />
        </div>
      </div>
    </van-dialog>

    <!-- 交车方式提醒选择弹窗 -->
    <van-dialog
      class="dialog lan-dialog-custom"
      v-model="deliveryDialog"
      :close-on-click-overlay="true"
      message="请选择交车方式"
      confirm-button-text="我知道了"
      @confirm="deliveryDialog = false"
    />
    <van-dialog
      class="dialog lan-dialog-custom lan-tips"
      v-model="tipsStatus"
      :close-on-click-overlay="true"
      message="该订单已关闭，请联系您的门店管家"
      confirm-button-text="我已知晓"
      @confirm="tipsStatus = false"
    />

    <!-- 金融信贷提醒选择弹窗 -->
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols"
      v-model="financeDialog"
      :close-on-click-overlay="true"
      cancel-button-text="我再想想"
      confirm-button-text="确认"
      show-cancel-button
      message="是否确认进行金融申请"
      @confirm="toFinancePage()"
      @cancel="financeDialog = false"
    />

    <!-- //充电权益确认弹窗 -->
    <van-dialog
      style="text-align: left;"
      v-model="showChargingPileDialog"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <p
        style="margin-left: 16px;"
        class="c-font18"
        v-html="`1、申领上海地区新能源汽车专用牌照用户仅可选择私人充电桩或${HUI_XING.WW}奥金+公共桩挂靠。<br/>2、申领其余地区新能源汽车专用牌照用户仅可选择私人充电桩或${HUI_XING.WW}奥金。`"
      />
      <van-checkbox
        style=" margin-top: 10px;margin-left: 16px;      margin-bottom: 10px;"
        disabled="disabled"
        @click="isChargingPileChecked = !isChargingPileChecked"
      >
        <img
          style="height: 16px;width: 16px;"
          slot="icon"
          :src="isChargingPileChecked ? activeIcon : inactiveIcon"
        >
        <span style="color: #666666; font-size: 12px"> 我已知晓上述说明</span>
      </van-checkbox>

      <div
        class="D-buttons"
        @click="toChargingPileNext"
      >
        下一步
      </div>
    </van-dialog>
    <!-- 半定制匹配到库存车(可转配置) -->
    <van-popup
      v-model="hasStockResources"
      class="popup-custom"
      :close-on-click-overlay="false"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="text">
            <p class="p">
              已为您匹配到可快速交付的专业推荐资源且定金将调整为{{ changeConfigurationMoney }}元，你可以选择：
            </p>
          </div>
        </div>
        <div
          class="popup-custom-btn"
          data-flex="main:justify"
        >
          <div class="line-two-cols lan-button-box">
            <audi-button
              height="54px"
              text="返回"
              @click="handleCancelChangeConfiguration"
            />
          </div>
          <div class="line-two-cols lan-button-box">
            <audi-button
              height="54px"
              :text="popupCustomBtnWorkingTips || `去支付定金${changeConfigurationMoney}元`"
              color="black"
              :class="[`black-btn`, popupCustomBtnWorkingTips ? 'btn-un-enabled' : 'btn-enabled']"
              @click="handleConfirmChangeConfiguration"
            />
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 限量号不足提示弹窗 -->
    <van-popup
      v-model="popLimitNumber"
      class="popup-custom"
      :close-on-click-overlay="false"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="text align-center">
            限量号全部被绑定，请您联系奥迪管家
          </div>
        </div>
        <div
          class="popup-custom-btn"
        >
          <audi-button
            height="56px"
            text="我知道了"
            color="black"
            @click="() => popLimitNumber = false"
          />
        </div>
      </div>
    </van-popup>
    <model
      :modalshow.sync="modalshow"
      @update:modalshow="submit"
      title="在App内打开"
      confirm-text="复制链接"
      content="点击下方复制链接，前往浏览器打开"
    />

    <van-dialog
      v-model="visibleDialog"
      :close-on-click-overlay="true"
      :title="invalidReason"
    >
      <!-- // 0是普通失效只能去退订，1有效不变，2选装下架失效，3车型下架失效 -->
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div
          v-if="!loanStatusBool || invalid == 3"
          class="boderBox"
          @click="handleBack"
        >
          返回
        </div>
        <div
          v-if="loanStatusBool && invalid != 3"
          class="boderBox"
          @click="goCC(0)"
        >
          重新配置
        </div>
        <div
          class="bckBox"
          @click="orderCancel"
        >
          去退订
        </div>
      </div>
    </van-dialog>

    <van-dialog
      v-model="updateVis"
      :close-on-click-overlay="true"
      :title="packageItem"
    >
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div
          class="boderBox"
          @click="handleBack"
        >
          返回
        </div>
        <div
          v-if="loanStatusBool"
          class="bckBox"
          @click="goCC(1)"
        >
          确认更新
        </div>
        <div
          v-if="!loanStatusBool"
          class="bckBox"
          @click="orderCancel"
        >
          去退订
        </div>
      </div>
    </van-dialog>

    <van-dialog
      class="dialog lan-dialog-custom"
      v-model="refundFailDialog"
      :close-on-click-overlay="true"
      message="抱歉，由于您已完成验车流程，暂无法在线办理退款，烦请联系您的奥迪管家处理。"
      confirm-button-text="我知道了"
      @confirm="refundFailDialog = false"
    />
    <network @reload="networkReload()" />
  </div>
</template>

<script>
import Clipboard from 'clipboard'
import Vue from 'vue'
import {
  Icon, Toast, Dialog, Swipe, SwipeItem, Field, Popup, Popover, Tab, Tabs
} from 'vant'
import { mapState, mapGetters } from 'vuex'
import wx from 'weixin-js-sdk'
import dayjs from 'dayjs'
import POPUP_CUSTOM from '@/config/popup-custom.data'
import {
  callNative, getUrlParamObj, paramsStrict, checkType, delay, combiningStrings, getCalcCMTImages
} from '@/utils/'
import AudiButton from '@/components/audi-button'
import PayStatus from '@/components/pay-status'

import {
  columns1, XIAN_XING_VERSION, XIAN_JIAN_VERSION, XIAN_XING_VERSION_Q5, ZHU_MENG_QING_CHUN_VERSION, ZHU_MENG_WEI_LAI_VERSION, LOAN_STATUS
} from '@/config/constant'
import {
  getMyOrders,
  getDigitalPayStatus,
  getDigitalPayInfo,
  getLimitedNumber,
  getHasLimitedNumber,
  daDingToRefund,
  getLoanStatus,
  getCanRefund,
  getLimitedNumberLineByOrderId,
  isConfirmed,
  confirmPayRest,
  getDetailsByOrderId,
  amsConfCheckCar,
  amsConfDeliverCarSign,
  amsConfDeliverCar,
  getFloorslist,
  addRemark,
  getGenerateMessage,
  getOrderDetails,
  getNgaDetailsOrder,
  getNgaConfirmCanDdPay,
  getEquityChargingPile,
  canXdUpdateInfo,
  getXdLoanAgentStatus,
  clickLoanAgent,
  getQueryLicenseStatus,
  getBlessedPackInfo,
  getCarSpecPrice,
  getUserInfo,
  getCarInsurance,
  getNgaPrice,
  getCheckChangeConfiguration,
  setConfirmChangeConfiguration,
  checkOrderHasSalesCoupons,
  getCarPaymentInfo,
  getOrderEquities,
  getMineInsuranceVoucher,
  getZMRightsProdId,
  getAudiMallH5Url,
  checkPGCData,
  checkOrderTraceInfoStatus,
  getOrderMniStatus,
  getTraceInfo,
  getAPITimeOutTesting,
  getAudiMinipUrl
} from '@/api/api'

import { getUpdateNew } from '@/configratorApi'

import {
  getMineLimitedNumberStep
} from '@/api/limited-number'
import model from '@/components/model.vue'
import Q5E_TRON_DATA from '@/config/Q5e-tron-map.data'
import ORDER_PAGE_DATA from '@/config/order-page.data'
import popupCustomActionBtn from '@/components/popup-custom-action-btn.vue'
import {
  LIMIT_NUMBERS, ORDER_STATUS_DISTRICT, AUTOMOBILE, RES_SUCCEED_CODE, CCPRO_RIGHTS_PACKAGE, LIMIT_NUMBERS_A7L, Q5E_LITE_MODEL_CODE, CHARGING_STATUS_TEXT, MANAGER_ON_OFF, HUI_XING
} from '@/config/conf.data'
import rightsQ5eLitePrevent from '@/components/rights.q5e.lite.prevent.vue'
import network from '@/components/network.vue'
import { networkToast } from '@/utils/timeout'
import api from '../../config/url'
import { getLimitedNumbersLine } from '../../api/payment-error'
import swipe from '../product/swipe.vue'
import { getOrderLimitedNumberStatus } from '../../api/detail'

import {
  getCancelReasons,
  unsubscribe
} from '../../api/information-cause.js'

const { INSURANCE, QUICK_LANGUAGE } = POPUP_CUSTOM
const baseOssHost = api.BaseOssHost

Vue.use(Icon).use(Toast).use(Dialog).use(Swipe)
  .use(SwipeItem)
  .use(Field)
  .use(Popup)
  .use(Popover)
  .use(Tab)
  .use(Tabs)
const codeType = ['00', '200']

//版本判断
const  isVersionGreaterOrEqual = (version, targetVersion) =>{
  const versionParts = version.split('.').map(Number)
  const targetParts = targetVersion.split('.').map(Number)
  const maxLength = Math.max(versionParts.length, targetParts.length)
  for (let i = 0; i < maxLength; i++) {
    const numVersion = versionParts[i] || 0
    const numTarget = targetParts[i] || 0

    if (numVersion < numTarget) {
      return true
    } else if (numVersion > numTarget) {
      return false
    }
  }
  return true
}

//
export default {
  inject: ['reload'],
  components: {
    AudiButton,
    model,
    swipe,
    'pay-status': PayStatus,
    'popup-custom-action-btn': popupCustomActionBtn,
    'rights-q5e-lite-prevent': rightsQ5eLitePrevent,
    network
  },
  data() {
    return {
      lifecycleStatus: '',
      lifecycleCurrent: '',
      isLoansWarning: 0,
      orderMaxStatus: {},
      orderTime: 0,
      goodsInfoHandle: false,
      ccProRPCode: '', // 轻重装权益包 code
      LIMIT_NUMBERS_A7L,
      LIMIT_NUMBERSS_IN: [LIMIT_NUMBERS_A7L.modelLineCode, LIMIT_NUMBERS[0].modelLineCode, LIMIT_NUMBERS[1].modelLineCode],
      ORDER_STATUS_DISTRICT,
      pgcURL: '',
      carInfo: {},
      orderInfo: {},
      visBtn: false,
      updateFlag: '',
      packageItem: '',
      invalidReason: '',
      invalid: {},
      updateVis: false,
      loanStatusBool: false,
      visibleDialog: false,
      ZHU_MENG_CODES: [ZHU_MENG_QING_CHUN_VERSION],
      orderStatus: '', // 订单状态
      confirmOrderDialogVisible: false,
      navigationHeight: '',
      labeiMinWidth: '142px',
      BaseOssHost: api.BaseOssHost,
      floorslist: [], // 运营位
      iconPhone: require('../../assets/img/phone.png'),
      show: false,
      allPayPrice: '', // 定金总金额
      isConfirmed: false, // 是否点击过支付尾款按钮 false未点击 可以点击金融服务
      canDdPay: false, // 是否可以大定
      formData: {
        orderId: '',
        carBuyerInfo: {
          buyType: '01',
          fullName: '',
          mobile: '',
          moreContact: [],
          carOwnerCertificateNumber: '',
          carOwnerCertificateType: '',
          carOwnerMobile: '',
          carOwnerName: '',
          enterpriseName: '',
          enterpriseCode: ''
        },
        contractInfo: {
          loanContractStatus: ''
        },
        confirmReturn: {
          applyType: ''
        },
        carCustomInfo: {
          carLicenseCityName: '',
          carLicenseCityCode: ''
        },
        orderItemList: [{
          reserveOrderAmount: 0,
          imageUrl: '',
          status: ''
        }],
        payList: [{
          payTime: ''
        }],
        orderStatus: '',
        orderStatusDesc: '',
        orderTime: ''
      },
      // carImgUrl: '',
      // colorNameCn: '',
      // modellineId: '',
      limitNumber: '',
      payType: '0',
      showLimit: true,
      total: 0,
      deliveryTypeObject: {
        1: '到店提车',
        2: '送车上门', // '同城送车上门',
        3: '送车上门', // '常规异地交车',
        4: '送车上门' // '非常规异地交车',
      },
      deliveryType: null, // 交车方式
      deliverAddr: null, // 交车城市code
      vehicleStatus: null, // 交车验车状态 0:无 1:待验车 2:已验车 3:待交车 4:已交车
      // 数字支付是否上传过凭证
      isUpload: false,
      countDown: [],
      countDownNum: 0,
      countDownTime: '',
      timer: null,
      isSignedContract: false,
      isAllSignedContract: false,
      isDeliveryContract: false,
      allowRefund: false,
      refundConfirmVisible: false,
      payRestConfirmVisible: false, // 验车确认弹窗状态
      voucherConfirmVisible: false, // 三包凭证
      ccid: '',
      skuid: '',
      carBuyerInfo: {},
      refundDDText: '',
      financeButtonText: '未审批',
      canXdStatus: null,
      modalshow: false,
      deliveryDialog: false, // 控制交车是否已经选择的弹窗提示
      financeDialog: false,
      financeStatus: true, // 点击过了
      paymentMethod: '10', // 贷款20  全款10
      canRefund: 0, // 是否显示申请退款按钮  0不显示 1 显示
      deliveryConfirmVisible: false, // 确认交车弹窗状态
      orderDetail: null, // 订单详情
      inspectionNum: 0, // 验车次数
      buyerRemark: null, // 大定备注

      chargingPileButtonText: '未审批', // 充电桩
      isShowChargingPile: false,
      chargingPileData: 0,
      showChargingPileDialog: false, // 跳转先弹窗
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      specCarRight: require('../../assets/img/spec_car_rights.jpg'),
      isChargingPileChecked: false,
      licenseStatus: null,
      carModel: null,
      luckyPackageId: null,
      carSpecPrice: 0,
      priceTagShow: false,
      queryLicenseStatus: {
        1: '未申请',
        2: '申请中',
        3: '已获得',
        4: '申请失败'
      },
      XIAN_XING_VERSION_Q5,
      popInsurance: {},
      popLimitNumber: false,
      totalPrice: 0,
      toPayPrice: 0,
      depositAmount: 0,
      hasStockResources: false,
      popupCustomBtnWorkingTips: '',
      changeConfigurationMoney: 0,
      Q5E_TRON_DATA,
      ORDER_PAGE_DATA,
      isSignedOnlineContract: false, // 是否签署线上合同
      isBalancePayment: false,
      equityAmount: -2, // 避免查询卡券的延迟故默认为-2
      balancePayment: 0,
      popoverActualPayment: false,
      INSURANCE,
      QUICK_LANGUAGE,
      testDrive: {
        q5: false
      },
      warning: {
        lifecycle: false
      },
      depositPrice: {
        prodNgaDepositPrice: 0, prodNgaDownPayPrice: 0
      },
      loanAmount: 0,
      sideSeriesImgUrl: '',
      isTransparency: false,
      zeroPayment: false,
      rightsPreventShow: false,
      Q5E_LITE_MODEL_CODE,
      appoId: '',
      chargingStatus: '',
      ngaDetailsOrder: {},
      equityOrderStatus: '',
      MANAGER_ON_OFF,
      orderKnapStatus: '',
      minipH5: '',
      isOneStep: false,
      isAffirmSignContract: false,
      ORDER_STATUS_DISTRICT_LIFE_MIN_CYCLE: [],
      firstPaidAmount: 0,
      lastPaidAmount: 0,
      refundFailDialog: false,
      HUI_XING,
      tipsStatus:false
    }
  },
  created() {
    const { orderId } = this.$route.query
    if (!orderId) {
      console.error(`money-detail page , orderid:${orderId}`)
    }
    this.$store.commit('setOrderId', orderId)
    this.$emit('set-bottom', false)
  },

  computed: {
    ...mapState({
      carDetail: (state) => state.carDetail,
      dealerMainImg: (state) => {
        const imgurl = state.dealerInfo?.imageUrl
        if (!imgurl) return ''

        if (imgurl.includes('http')) {
          return imgurl
        }
        return `${baseOssHost}${imgurl}`
      },
      latitude: (state) => state.dealerInfo.latitude,
      longitude: (state) => state.dealerInfo.longitude,
      dealerName: (state) => state.dealerInfo.dealerName,
      dealerPhone: (state) => state.dealerInfo.dealerPhone,
      dealerDistance: (state) => state.dealerInfo?.distance,
      dealerAdrress: (state) => state.dealerInfo?.dealerAdrress,
      carSeries: (state) => state.carSeries,
      currentModelLineId: (state) => state.carDetail.configDetail?.carModel?.modellineId,
      currentModelLineCode: (state) => state.carDetail.configDetail?.carModel?.modelLineCode,
      modelCode: (state) => state.carDetail.configDetail?.carModel?.modelCode,
      carImgUrl: (state) => baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl, // 主图
      colorNameCn: (state) => state.carDetail.configDetail?.outsideColor?.colorNameCn,
      inColorNameCn: (state) => state.carDetail.configDetail?.insideColor?.colorNameCn,
      modelNameCn: (state) => state.carDetail.configDetail?.carModel?.modelNameCn,
      // totalPrice: (state) => state.carDetail.configDetail?.totalPrice,
      bestRecommendId: (state) => state.carDetail.bestRecommendId // 是否是推荐车型
    }),
    isXianXing() {
      return this.currentModelLineCode === XIAN_XING_VERSION
    },
    showPayDetail() {
      return this.orderStatus === '81' || this.orderStatus === '80'
    },
    getXDRefundText() {
      if (this.currentModelLineCode === XIAN_XING_VERSION) {
        return '退订后您将失去专属限量号和先行权益，且无法再次下单，是否确认退订？'
      }
      if (this.currentModelLineCode === XIAN_JIAN_VERSION) {
        return '退订后您将失去先见权益，是否确认退订？'
      }
      const { seriesNanoName } = AUTOMOBILE.filter((i) => i.seriesCode === this.carSeries.seriesCode)[0] || ''
      return `退订后您将失去${seriesNanoName}车主专属权益，是否确认退订？`
    },
    isA7L20T() {
      const ret = ['498BZG001', '498BZY001', '498BZY002', '498BZY003', '498BZY004'].includes(this.currentModelLineCode)
      return ret
    },
    getDDRefundText() {
      if (this.currentModelLineCode === XIAN_XING_VERSION) {
        return '退订后您将失去专属限量号和先行权益，且无法再次下单，是否确认退订？'
      }
      if (this.currentModelLineCode === XIAN_JIAN_VERSION) {
        return '退订后您将失去先见权益，是否确认退订？'
      }
      const { seriesNanoName } = AUTOMOBILE.filter((i) => i.seriesCode === this.carSeries.seriesCode)[0] || ''
      return `退订后您将失去${seriesNanoName}车主专属权益，是否确认退订？`
    },
    // 未支付意向金
    noPayXiaoding() {
      return this.orderStatus === '00'
    },

    // 是否显示小订转大定支付定金的按钮
    showPayPriceButton() {
      if (this.carDetail?.valid !== undefined) {
        return (this.orderStatus === '30' || this.orderStatus === '301') && this.carDetail?.valid == 1
      }
      return this.orderStatus === '30' || this.orderStatus === '301'
    },

    // 修改为 待支付意向金和支付意向金后未支付定金的时候 才允许修改订单
    allowModifyOrder() {
      return ['00', '30'].includes(this.orderStatus)
    },

    // 是否显示限量号按钮
    isDaDingStatus() {
      // 区分Q5e和A7
      const statusList = this.carSeries.seriesCode === 'G4' ? ['31', '32', '99'] : ['30', '301', '31', '32', '90']
      return statusList.includes(this.orderStatus) && this.showLimit
    },

    // 金融服务是否审批
    // financeButtonText() {
    //   if (this.formData?.contractInfo?.loanContractStatus) {
    //     return '已审批'
    //   }
    //   return '未审批'
    // },

    showLineUpNumber() {
      return this.orderStatus === '30' && !this.showLimit && this.isXianXing
    }
  },
  filters: {
    filterIdentCode(num) {
      if (Array.isArray(num)) {
        return num[1]
      }
      if (typeof num === 'string') {
        return num.split(',')[1]
      }
      return ''
    }
  },

  mounted() {
    this.initData()
    // this.handleGetCarPaymentInfo()
    // this.getXdLoanAgentStatus()
  },

  watch: {
    $route(val) {
      const { timestamp, env } = this.$route.query
      if (env === 'minip') {
        window.history.go(-1)
      }
    },
    bestRecommendId: {
      immediate: true,
      handler(newValue, oldValue) {
        console.log(newValue, oldValue, '是否是推荐车型')
      },
      deep: true
    },
    // 监听车系seriesCode是否已经获取
    carSeries: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue.seriesCode && this.orderDetail?.carCustomId) {
          this.getEquityChargingPile(newValue.seriesCode)
        }
      },
      deep: true
    },
    orderDetail: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue?.carCustomId && this.carSeries?.seriesCode) {
          this.getEquityChargingPile(this.carSeries.seriesCode)
        }
      },
      deep: true
    }
  },

  methods: {
    async orderCancel() {
      this.$store.commit('showLoading')
      const param = {
        orderId: this.$route.query?.orderId,
        remark: '',
        refundReasonType: '39',
        boolOfflineRefund: false
      }
      console.log('退订原因：param', param)
      const { data } = await unsubscribe(param)
      this.$store.commit('hideLoading')
      if (!['00', '200'].includes(data.code)) return
      const seriesCode = this.carInfo?.configDetail?.carSeries?.seriesCode || ''
      this.$router.push({
        path: '/order/unsubscribe-succeed',
        query: {
          valid: this.invalid == 1 ? '' : this.invalid,
          updateFlag: this.updateFlag,
          seriesCode: seriesCode
        }
      })
    },
    async goCC(e = '') {
      if (e) {
        const ccid = this.ccid
        const res = await getUpdateNew(ccid)
        this.updateVis = false
        this.reload()
        return
      }

      const seriesCode = this.carInfo?.configDetail?.carSeries?.seriesCode || ''
      console.log(seriesCode)
      if (!seriesCode) { return }
      this.$storage.setPlus('semi-definite', this.carInfo.measureId ? '个性定制' : '私人高定')
      const { orderId } = this.$route.query
      const obj = {
        idx: ['49', 'G4', 'G6'].findIndex((e) => e == seriesCode),
        tabIndex: 0,
        ccid: this?.ccid || '',
        orderStatus: this.orderStatus,
        orderId
      }
      this.$router.push({
        path: '/configration',
        query: obj
      })
      if (seriesCode == 49 || seriesCode == 'G6') {
        this.$router.push({
          path: '/configration',
          query: {
            idx: ['49', 'G4', 'G6'].findIndex((e) => e == seriesCode),
            ccid: this?.ccid || '',
            orderStatus: this.orderStatus,
            orderId
          }
        })
      }
      seriesCode == 'G4' && this.$router.push({
        path: '/configration',
        query: obj
      })
    },
    async handleGetCarPaymentInfo(totalPrice) {
      const { orderId } = this.$route.query
      const { data: { data: equities } } = await getOrderEquities({ orderId })
      const validStatus = ['10', '90', '96']

      if (equities.some((i) => !validStatus.includes(i.status))) {
        this.equityAmount = -1
        return 0
      }

      // 优惠金额 分
      const finalResult = equities.reduce((sum, list) => (sum + (!validStatus.includes(list.status) ? 0 : list.deductOff)), 0)
      console.log('%c [ finalResult totalPrice ]-1510', 'font-size:14px; background:#cf222e; color:#fff;', finalResult, totalPrice, totalPrice * 100 - finalResult)

      this.equityAmount = finalResult && totalPrice ? finalResult / 100 : 0
      return finalResult || 0
    },
    handleMoney() {
      this.visibleDialog = false
      this.confim()
      // refundConfirmVisible = true
    },
    handleBack() {
      this.visibleDialog = false
      this.visBtn = true
      this.updateVis = false
    },
    async getEquityChargingPile(seriesCode) {
      const { orderId } = this.$route.query
      const { data } = await getEquityChargingPile({ seriesCode, orderId, carCustomId: this.orderDetail.carCustomId })
      if (data?.data) {
        const {
          status, appoId, equityType, equityOrderStatus
        } = data.data || { status: 0, appoId: '', equityType: 0 }
        if (appoId) {
          this.appoId = `${appoId}`
        }
        status && (this.chargingStatus = status)
        equityOrderStatus && (this.equityOrderStatus = equityOrderStatus)
        if (equityType === 0 || ['BK', 'BJ'].includes(this.orderInfo.extInfo.tags)) {
          this.isShowChargingPile = false
        } else {
          this.isShowChargingPile = (this.isOneStep ? this.isSignedOnlineContract : true) && ORDER_STATUS_DISTRICT.DA_DING.includes(this.orderStatus)
        }
        this.chargingPileData = equityType
        // const CSTATUS = equityType === 20 ? 1 : 0
        // const DSTATUS = equityType === 21 ? 5 : 0


        this.chargingPileButtonText = (equityOrderStatus !== 2 && CHARGING_STATUS_TEXT[`${equityType}`]) || '未选择'
        // if (['1102', '1203'].includes(status)) {
        //   this.chargingPileButtonText = ''
        // }
        // 权益类型 1-充电桩 2-奥金 3-挂靠+奥金
        // if (data.data === 1) {
        //   this.chargingPileButtonText = '充电桩'
        // } else if (data.data === 2) {
        //   this.chargingPileButtonText = '奥金'
        // } else if (data.data === 3) {
        //   this.chargingPileButtonText = '挂靠+奥金'
        // } else {
        //   this.chargingPileButtonText = '未选择'
        // }
      }
    },

    toChargingPileNext() {
      if (!this.isChargingPileChecked) {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '请勾选知晓上述说明',
          forbidClick: true,
          duration: 800
        })
        return
      }
      this.showChargingPileDialog = false
      const { orderId } = this.$route.query
      const { appoId, modelCode, orderStatus } = this
      this.$router.push({
        path: '/charging/select-equities',
        query: { orderId, orderStatus, ...(Q5E_LITE_MODEL_CODE.includes(modelCode) ? { skukw: 'lite' } : {}) }
      })
    },

    async getXdLoanAgentStatus() {
      const { orderId } = this.$route.query
      const { data } = await getXdLoanAgentStatus({ orderId })
      this.financeStatus = data.data
    },

    toChargingPilePage() {
      const {
        appoId, modelCode, orderStatus, chargingPileData, chargingStatus, equityOrderStatus
      } = this
      const { orderId, env } = this.$route.query
      if (env === 'minip') {
        return this.handleGoToPageDownloadApp(orderId)
      }
      if (equityOrderStatus === 2) {
        if (!Q5E_LITE_MODEL_CODE.includes(modelCode)) {
          return this.$router.push({
            path: '/charging/select-equities',
            query: { orderId, orderStatus }
          })
        }
        if (orderStatus !== '90') {
          return this.$router.push({
            path: '/charging/select-equities',
            query: { orderId, skukw: 'lite', orderStatus }
          })
        }
        return this.$router.push({
          path: '/charging/install-info',
          query: {
            appoId, orderStatus, skukw: 'lite', orderId
          }
        })
      }


      if ([6].includes(chargingPileData)) {
        return
      }
      if ([1, 5].includes(chargingPileData)) {
        // 查看我的订单
        if (Q5E_LITE_MODEL_CODE.includes(modelCode)) {
          this.$router.push({
            path: '/charging/install-info',
            query: {
              appoId, orderStatus, ...(Q5E_LITE_MODEL_CODE.includes(modelCode) ? { skukw: 'lite' } : {}), orderId
            }
          })
          return
        }

        callNative('openRoutePath', { path: 'scaudi://mall/orderlist?index=3' })
      } else if ([2, 3, 7].includes(chargingPileData)) {
        // 奥金中心
        callNative('openRoutePath', { path: 'scaudi://mine/sign/home' })
      } else {
        const { orderId } = this.$route.query
        if (orderStatus !== '99') {
          this.$router.push({
            path: '/charging/select-equities',
            query: { orderId, orderStatus, ...(Q5E_LITE_MODEL_CODE.includes(modelCode) ? { skukw: 'lite' } : {}) }
          })
          // if (Q5E_LITE_MODEL_CODE.includes(modelCode)) {
          //   this.$router.push({
          //     path: '/charging/select-equities',
          //     query: { orderId, skukw: 'lite', orderStatus }
          //   })
          //   return
          // }
          // this.showChargingPileDialog = true
        }
      }
    },

    toNavigation() {
      callNative('navigationMap', {
        lat: this.latitude.toString(),
        long: this.longitude.toString(),
        des: this.dealerName
      })
    },
    //  是否点击过尾款支付按钮
    async getisConfirmed() {
      const param = { orderId: this.$route.query.orderId }
      const { data } = await isConfirmed(param)
      this.isConfirmed = data.data
    },
    // 确认尾款支付
    async confirmPayRest() {
      const { orderId } = this.$route.query
      const { ccid, isBalancePayment, equityAmount } = this
      if (equityAmount === -1) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '存在失效卡券，无法提交订单，请联系专属奥迪管家',
          forbidClick: true,
          duration: 800
        })
      }
      if (isBalancePayment) {
        return
      }
      const { data: { data, code, message } } = await confirmPayRest({ orderId })
      if (!codeType.includes(code)) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: message || '网络请求错误',
          forbidClick: true,
          duration: 800
        })
      }
      this.isBalancePayment = true
      delay(() => {
        this.isBalancePayment = false
      }, 6000)
      // 检查当前整车订单是发放有优惠券
      // const { data: { data: HasSalesCoupons } } = await checkOrderHasSalesCoupons({ orderId })
      this.isBalancePayment = false
      // console.log('%c [ HasSalesCoupons ]-1510', 'font-size:14px; background:#cf222e; color:#fff;', HasSalesCoupons, HasSalesCoupons ? 'verification-coupons' : 'balance')
      this.$router.push({
        // path: `/order/${HasSalesCoupons ? 'verification-coupons' : 'balance'}`,
        path: '/order/verification-coupons',
        query: {
          orderId,
          ccid
        }
      })
    },
    // 获取是否可以大定支付状态
    async getNgaConfirmCanDdPay() {
      const { orderId } = this.$route.query
      const { data } = await getNgaConfirmCanDdPay({ orderId })
      this.canDdPay = data.data
    },

    // 确认CCID是否是福袋车CCID
    async getBlessedPackInfo(ccid) {
      const { data } = await getBlessedPackInfo({ ccid })
      console.log('福包车信息', data.data)
      this.luckyPackageId = data.data.id
    },
    // 查询福袋车总价
    async getCarSpecPrice(ccid) {
      const { data } = await getCarSpecPrice({ ccid })
      if (data.data.isFdOrder) {
        this.priceTagShow = true
        if (data.data.contractPrice) {
          this.carSpecPrice = data.data.contractPrice
          this.priceTagShow = false
          return this.carSpecPrice || 0
        }
        this.getBlessedPackInfo(ccid)
      }
      return 0
    },

    // 获取整车订单详情信息
    async getNgaDetailsOrder() {
      const { orderId } = this.$route.query
      const { data } = await getNgaDetailsOrder({ orderId })
      const { data: { payList } } = data
      const { orderStatus } = this
      if (ORDER_STATUS_DISTRICT.DA_DING.includes(orderStatus)) {
        this.toPayPrice = payList.reduce((sum, index) => sum + (index.status === '3' && index.amount ? index.amount : 0), 0) / 100
      }
      console.log('%c [ orderStatus = 31, 32, 90 payList status = 3 this.toPayPrice ]-1638', 'font-size:14px; background:#cf222e; color:#fff;', this.toPayPrice)
      this.buyerRemark = data.data.buyerRemark
      this.ngaDetailsOrder = data.data
      if (data?.data?.isDepositBreakage) {
        this.orderKnapStatus = '999'
      }
    },
    // 获取运营位
    async getFloorslist() {
      const param = {
        pageFrontCode: 'audi-after-goods',
        pageAccessType: 2
      }
      const { data } = await getFloorslist(param)
      if (codeType.includes(data.code)) {
        this.floorslist = data.data[0]?.content.contentDetailList.map((i) => {
          const imglist = i.linkObject.prodImages.filter((i) => i.showType === 1 && i.imageType === 1)
          imglist.forEach((i) => {
            if (i.imageUrl.indexOf('http') === -1) {
              i.imageUrl = baseOssHost + i.imageUrl
            }
          })
          return {
            imageUrl: imglist[0].imageUrl,
            prodId: i.linkObject.prodId,
            prodSku: i.linkObject.prodSkus.find((i) => i.prodSkuId)
          }
        })
        console.log(this.floorslist)
      }
    },

    // 跳转商品详情
    toProductDetail(detail) {
      const { orderId } = this.$route.query
      const params = {
        skuId: detail.prodSku.prodSkuId,
        orderId,
        backType: 'h5'
      }
      console.log('携带参数:', params)
      this.$router.push({
        path: '/product-detail',
        query: {
          ...params
        }
      })

      // callNative('openRoutePath', {
      //   path: 'scaudi://mall/detail/' + detail.prodSku.prodSkuId + "?ngaOrderId=" + orderId,
      //   params
      // })
    },

    // 获取交车状态是否显示
    getdeliveyShow() {
      if (this.orderDetail) {
        if (+this.orderDetail.orderStatus === 30) return true
        // if (this.orderDetail.payList[0].payPhase === '11') { // 特定车
        //   return true
        // } if (this.orderDetail.dealerInfo.dealerCode === '76600019') { // 普通车 代理商是总部   大定完可以选择并查看交车方式
        //   if (+this.orderDetail.orderStatus >= 31) return true
        //   return false
        // } if (+this.orderDetail.orderStatus >= 30) { // 普通车 普通代理商  小订和大定之间可以选择并查看交车方式
        //   return true
        // }
        return false
      }
    },
    // 是否可以更改并跳转交车方式页面
    isTodelivey() {
      if (this.orderDetail.payList[0].payPhase === '11') { // 特定车
        return false
      } if (this.orderDetail.dealerInfo.dealerCode === '76600019') { // 普通车 代理商是总部   大定完可以选择并查看交车方式
        if (this.orderDetail.orderStatus === '31' && !this.deliveryType) return true
        return false
      } if (this.orderDetail.orderStatus === '30' || this.orderDetail.orderStatus === '301') {
        return true
      }
      return false
    },

    // 获取交车验车状态
    async getDetailsByOrderId() {
      const { orderId } = this.$route.query
      const { data } = await getDetailsByOrderId({ orderId })
      this.vehicleStatus = data.data.status
      if (this.vehicleStatus === '2') {
        this.lifecycleStatus = '42'
        this.lifecycleCurrent = '06'
      }
      this.inspectionNum = data.data.inspectionNum
      this.deliveryType = data.data.deliveryType
      this.deliverAddr = data.data.deliverAddr
    },
    // 确认验车
    async amsConfCheckCar() {
      const { orderId, env } = this.$route.query
      if (env === 'minip') {
        return this.handleGoToPageDownloadApp(orderId)
      }
      const { data } = await amsConfCheckCar({ orderId })
      if (codeType.includes(data.code)) {
        this.payRestConfirmVisible = false
        this.getDetailsByOrderId()
        this.$router.go(0)
      }
    },

    // 获取贷款状态（废弃）
    async getLoanStatus(orderId) {
      const { data: { data } } = await getLoanStatus({ orderId }) || []
      if (data?.length) {
        const list = data.filter((i) => i.loanStatus !== 4)
        if (list?.length) {
          const loanData = list[0]
          this.financeButtonText = LOAN_STATUS[loanData.loanStatus].text || ''
          this.isLoansWarning = +[0, 1].includes(loanData.loanStatus) || 0
          return loanData?.loanAmount || 0
        }
        this.financeButtonText = LOAN_STATUS[4].text || ''
      } else {
        this.financeButtonText = ''
      }
      return 0
    },

    // 获取退款按钮状态
    async getCanRefund() {
      const param = { orderId: this.$route.query.orderId }
      const res = await getCanRefund(param)
      this.canRefund = res.data.data
    },

    lookCar() {
      // 埋点
      this.$sensors.track('vehicleConfigurationSelection', {
        page_name: '购车订单详情页'
      })

      const { orderId } = this.$route.query
      let param = {}

      switch (this.orderStatus) {
        case '00': // 待支付意向金
        case '98': // 已取消
        case '30': // 已支付意向金，待支付定金
        case '301': // 大定待付款
        case '31': // 已支付定金
        case '32': // 已支付尾款
        case '90': // 已完成订单
          if (!this.ccid || !this.skuid) {
            console.error(`到报价单必须要有ccid: ${this.ccid}, skuid: ${this.skuid}`)
          }
          param = {
            path: '/quotation',
            query: {
              orderId,
              fromPage: 'orderDetail',
              ccid: this.ccid,
              skuid: this.skuid,
              orderStatus: this.orderStatus,
              measureId: this.carInfo.measureId || ''
            }
          }
          break

        case '80': // 退款中
        case '81': // 已退款
        case '84': // 大定退款中
        case '99': // 已关闭
          // 跳转到配置表
          param = {
            path: '/car-config-table',
            query: {
              orderId,
              carModelName: this.modelNameCn,
              modellineId: this.currentModelLineId
            }
          }
          break

        default:
          // 跳转到配置表
          param = {
            path: '/car-config-table',
            query: {
              orderId,
              carModelName: this.modelNameCn,
              modellineId: this.currentModelLineId
            }
          }
      }
      this.$router.push(param)
    },

    toEquity() {
      const { orderId } = this.$route.query
      this.$router.push({
        path: '/equity-detail',
        query: { orderId }
      })
    },

    async toDetail() {
      const dealerCode = this.orderDetail.dealerInfo.dealerCode
      if (this.isTodelivey() && dealerCode !== '76600019' && !this.deliveryType && !this.isOneStep) {
        this.deliveryDialog = true
        return
      }
      const { orderId } = this.$route.query
      this.$store.commit('updateReaded', 0)
      this.$store.commit('setDadingFormData', null)
      if (this.luckyPackageId) {
        const param = {
          deliverAddr: this.orderDetail?.dealerInfo?.cityCode,
          deliverType: this.deliveryType,
          deliveryName: this.deliveryTypeObject[this.deliveryType]
        }
        this.$store.commit('updateDeliveryPattern', param)
      }
      const { orderStatus } = this

      console.log('%c [ go to pay ====> orderStatus ]-1740', 'font-size:14px; background:#cf222e; color:#fff;', orderStatus)
      if (['30', '301'].includes(orderStatus)) {
        const { data: { depositPrice, downPayPrice, status } } = await this.handleCheckChangeConfiguration(orderId) || {}
        console.log('%c [ handleCheckChangeConfiguration ]-1741', 'font-size:14px; background:#cf222e; color:#fff;', depositPrice, downPayPrice, status)
        if (status) {
          this.changeConfigurationMoney = (downPayPrice - depositPrice) / 100
          this.hasStockResources = true
          return
        }
      }

      // this.$router.push({
      //   path: '/order/detail',
      //   query: {
      //     ccid: this.ccid,
      //     skuid: this.skuid,
      //     orderStatus,
      //     orderId,
      //     luckyPackageId: this.luckyPackageId
      //   }
      // })
      this.$router.push({
        name: 'order-affirm',
        query: {
          from: 'order',
          ccid: this.ccid,
          skuid: this.skuid,
          orderStatus,
          orderId,
          luckyPackageId: this.luckyPackageId
        }
      })
    },
    async handleConfirmChangeConfiguration() {
      const { orderId } = this.$route.query
      if (!orderId) {
        console.log('%c [ orderId ]-1764', 'font-size:14px; background:#cf222e; color:#fff;', orderId)
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '订单不存在',
          forbidClick: true,
          duration: 800
        })
      }
      this.popupCustomBtnWorkingTips = '资源调整中...'
      const { data: { code, message } } = await setConfirmChangeConfiguration({ orderId }) || {}
      if (code === '200') {
        this.reload()
      } else {
        this.popupCustomBtnWorkingTips = ''
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: message || '网络请求错误',
          forbidClick: true,
          duration: 800
        })
      }
    },
    handleCancelChangeConfiguration() {
      const { orderStatus, carSeries: { seriesCode } } = this
      const { orderId, shoppingCartId } = this.$route.query
      const minip = this.$route.query.env == 'minip'
      this.$storage.setPlus('entryPoint', minip ? 'MINIP_PERSONAL' : 'ONEAPP_PERSONAL')
      this.$router.push({
        path: '/configration',
        query: {
          orderStatus,
          orderId,
          idx: seriesCode === '49' ? 0 : 1,
          tabIndex: 0,
          from: 'money-detail',
          definedCar: 1,
          shoppingCartId: shoppingCartId || ''
        }
      })
    },
    async handleCheckChangeConfiguration(orderId) {
      const { data } = await getCheckChangeConfiguration({ orderId }) || {}
      return data
    },
    async toShowBuyCarAgreeMent() {
      if (this.equityAmount === -1) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '存在失效卡券，无法提交订单，请联系专属奥迪管家',
          forbidClick: true,
          duration: 800
        })
      }
      // const { env } = getUrlParamObj()
      // if (env === 'minip' && this.orderStatus !== '00') {
      //   const { orderId } = this.$route.query
      //   // this.modalshow = true
      //   this.handleGoToPageDownloadApp(orderId)
      //   return
      // }

      this.toDetail()
    },
    handleGoToPageDownloadApp(orderId) {
      const { ccid } = this
      this.$router.push({
        name: 'order-guide-page-download-app',
        query: { orderId, ccid }
      })
    },
    showDadingDialog() {
      // 埋点
      const { orderId } = this.$route.query
      this.$sensors.track('clickToPayTheDeposit', {
        order_no: orderId,
        order_time: this.formData.orderTime,
        enterprise_name: this.formData.carBuyerInfo.enterpriseName,
        organization_structure_code: this.formData.carBuyerInfo.enterpriseCode,
        car_buying_city: this.formData.carCustomInfo.carLicenseCityName
      })
    },

    // 跳转到签署合同的页面
    toSignContract(tsc = 0) {
      if (this.orderStatus === '98' || this.orderStatus === '99') return
      const { orderId, already, env } = this.$route.query
      const { ngaDetailsOrder: { contractInfo: { isSelectOnline, isClickDownloadContract, lastSelectOnline } }, ccid, isOneStep } = this
      if (tsc === 1) {
        const { contractInfo: { purchaseContractBoolOnline, purchaseContractId }, carBuyerInfo: { buyType, mobile } } = this.orderDetail

        console.log('%c [ isSelectOnline ]-2216', 'font-size:14px; background:#cf222e; color:#fff;', isSelectOnline)
        if (buyType === '02') {
          return this.$router.push({
            name: 'contract-info', // contract-enterprise-online
            query: {
              purchaseContractId,
              orderId,
              mobile,
              isSelectOnline,
              isClickDownloadContract,
              lastSelectOnline
            }
          })
        }
        if (env === 'minip' && !already) {
          const { origin, pathname } = location
          const url = `/pages/order/contract/index?orderId=${orderId}&ccid=${ccid}&prod=${process.env.VUE_APP_ENV === 'pre' ? 0 : 1}&url=${encodeURIComponent(`${origin}${pathname}#/order/money-detail`)}`
          return wx.miniProgram.navigateTo({ url })
        }
        const fadadaStorageVerified = localStorage.getItem(`__AOID__${orderId}`)
        return this.$router.push({
          name: already || +fadadaStorageVerified === 1 ? 'contract-guide' : 'contract-info',
          query: {
            orderId, already: already || 0, ccid,
            orderType:this.orderDetail.orderType,
          }
        })
      }

      const {
        isSignedOnlineContract, deliveryType, isDeliveryContract, vehicleStatus, chargingStatus, modelCode
      } = this

      this.$router.push({
        name: 'order-cabinet-agreement',
        query: {
          orderId,
          isSignedContract: isSignedOnlineContract ? 1 : 0,
          deliveryType,
          isDeliveryContract: isDeliveryContract ? 1 : 0,
          vehicleStatus,
          ...(Q5E_LITE_MODEL_CODE.includes(this.modelCode) && chargingStatus === '200' ? { skukw: 'lite' } : {}),
          isSelectOnline,
          isClickDownloadContract,
          isOneStep: isOneStep ? 1 : 0,
          ccid,
          orderType:this.orderDetail.orderType
        }
      })
    },

    // 跳转到签署交车合同页面
    toContract() {
      const { orderId } = this.$route.query
      const { orderInfo } = this

      if (!['BK', 'BJ'].includes(orderInfo.extInfo.tags) && Q5E_LITE_MODEL_CODE.includes(this.modelCode) && ['200', '0002', 2000].includes(this.chargingStatus) && ![6].includes(this.chargingPileData)) {
        this.rightsPreventShow = true
        return
      }
      this.$router.push({
        path: '/delivery-contract',
        query: { orderId }
      })
    },

    toModifyInfoPage() {
      const { allowModifyOrder, canXdStatus, ORDER_PAGE_DATA: { BOOK_ORDER_UPDATE_BUYER_INFO } } = this
      const { orderId } = this.$route.query

      const index = checkType(canXdStatus) !== 'Number' ? 0 : canXdStatus
      let fields = ''
      if (index && BOOK_ORDER_UPDATE_BUYER_INFO?.length) {
        const BUYER_INFO = BOOK_ORDER_UPDATE_BUYER_INFO[index] ?? ''
        if (BUYER_INFO?.length && checkType(BUYER_INFO) === 'Array') {
          fields = BUYER_INFO.join(',')
        } else if (BUYER_INFO && checkType(BUYER_INFO) === 'Boolean') {
          fields = 'all'
        }
      }
      this.$router.push({
        name: 'order-buyer-info',
        query: { orderId, ...{ fields }, update: allowModifyOrder && canXdStatus ? 1 : 0 }
      })
    },

    // 跳转到订单状态时间轴页面
    toOrderSteps() {
      if (!this.isTransparency) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '当前无透明化节点',
          forbidClick: true,
          duration: 800
        })
      }
      const { orderId } = this.$route.query
      this.$router.push({
        path: '/logistics-status',
        query: { orderId }
      })
    },

    // 跳转到金融服务页面
    async toFinancePage(type) {
      // 点击过支付尾款按钮就不能再跳金融服务了
      if (this.isConfirmed === true || ['90', '98', '99', '32', '80', '81', '84'].includes(this.orderStatus) || this.equityAmount === -1) return
      // 埋点
      this.$sensors.track('financialService', {
        page_name: '购车订单详情页',
        module_name: '金融服务',
        approval_status: this.formData.contractInfo?.loanContractStatus ? '审批' : '未审批' // 审批状态
      })

      // 只有合同状态为 2 (已签署) 才能进入金融页面
      // if (!this.isAllSignedContract) {
      //   Toast({
      //     type: 'fail',
      //     message: '请签署合同',
      //     icon: require('../../assets/img/contract-fail.png')
      //   })
      //   return
      // }

      const { orderId, env } = this.$route.query
      if (env === 'minip') {
        return this.handleGoToPageDownloadApp(orderId)
      }
      if (this.orderStatus === '30' && this.financeButtonText === '未审批') {
        if (type === 1) {
          this.financeDialog = true
          return
        }
        // const { data } = await clickLoanAgent({ orderId })
        // this.financeStatus = data.data
      }
      const { ccid, orderStatus } = this
      this.$router.push({
        path: '/finance-list',
        query: { orderId, ccid, orderStatus }
      })
    },

    toDeliveryPattern() { // 跳转交车方式
      if ((![...ORDER_STATUS_DISTRICT.DIES, ...ORDER_STATUS_DISTRICT.REFUND].includes(this.orderStatus) && +this.orderStatus >= 30) || !this.deliveryType) {
        const { orderId } = this.$route.query
        const deliverAddr = this.deliverAddr
        let deliveryType = this.deliveryType
        if (deliveryType > 1) deliveryType = 2
        this.$router.push({
          path: '/delivery-pattern',
          query: { orderId, deliveryType, deliverAddr }
        })
      }
    },

    async toGuaranteesVoucher() { // 跳转三包凭证
      const { orderId } = this.$route.query
      const res = await getGenerateMessage({ orderId })
      if (res.data.code === '00') {
        if (res.data?.data) {
          this.$router.push({
            path: '/guarantees-voucher',
            query: { orderId }
          })
        } else {
          this.voucherConfirmVisible = true
        }
      }
    },
    handleOrderRefund() {
      const [payStatus] = this.orderDetail.payList.filter((i) => i.payPhase === '20' && i.status !== '3')
      if (payStatus) {
        this.refundFailDialog = true
        return
      }
      this.refundConfirmVisible = true
    },
    // 大定确认退款
    async confirmRefund() {
      const { orderId } = this.$route.query

      // 埋点
      this.$sensors.track('confirmRefund', {
        page_name: '购车订单详情页',
        order_no: orderId,
        order_time: this.formData.orderTime,
        enterprise_name: this.carBuyerInfo.enterpriseName,
        organization_structure_code: this.carBuyerInfo.enterpriseCode,
        car_buying_city: this.formData.carCustomInfo.carLicenseCityName
      })

      const { data } = await daDingToRefund({ orderId })
      console.log('大定确认退款:', data)
      if (data.code === '00') {
        // 跳转到退款成功页面
        this.$router.push({
          path: '/order/refund-success'
        })
      }
    },

    // 确认交车
    async confimDelivery() {
      this.$store.commit('showLoading')
      const { orderId } = this.$route.query
      const { dealerCode } = this.orderDetail.dealerInfo
      const { data } = await amsConfDeliverCar({ orderId, dealerCode })
      this.$store.commit('hideLoading')
      if (codeType.includes(data.code)) {
        this.deliveryConfirmVisible = false
        this.getDetailsByOrderId()
        this.$router.go(0)
      }
    },

    // 支付
    // 大定和小订的逻辑是一样的
    async goPayMoney(action) {
      const { env } = getUrlParamObj()
      const that = this
      const { orderId } = this.$route.query
      this.$store.commit('showLoading')

      if (env === 'minip') {
        const { origin, pathname } = window.location
        const url = encodeURIComponent(`${origin}${pathname}#/order/money-detail?orderId=${orderId}`)
        wx.miniProgram
          .navigateTo({ url: `/pages/pay/pay?orderId=${orderId}&url=${url}&colorNameCn=${that.colorNameCn}&inColorNameCn=${that.inColorNameCn}&action=${action}&seriesCode=${that.carSeries.seriesCode}` })
      } else {
        const callPayCenter = await callNative('callPayCenter', { orderId },{orderInfo: this.$route.query && JSON.stringify(this.$route.query)})
        console.log('pay callPayCenter', callPayCenter)
        const data = await callNative('callPayResult', {})
        this.$store.commit('hideLoading')
        const payType = data.payType
        console.log(`current action: ${action}, callPayResultData: ${data} `)
        if (payType === 'CNY') {
          // 使用数字人民币支付
          this.$router.push({
            path: '/order/pay-flow',
            query: { orderId }
          })
        } else {
          loopQuery(0)
        }
        // 查询支付结果
        function loopQuery(i) {
          setTimeout(async () => {
            /**
               * 查询支付后的状态.
               * 小订是通过查询限量号的状态
               */
            // 小订支付意向金后的逻辑
            if (action === 'xiaoding') {
              const res = await getOrderLimitedNumberStatus({ orderId: orderId || localStorage.getItem('orderId') })
              if (codeType.includes(res.data.code)) {
                //= 30支付成功
                if (res.data.data.status === 1) {
                  const resss = await getLimitedNumber({ orderId: orderId || localStorage.getItem('orderId') })
                  that.$store.commit('hideLoading')
                  const { dealerCode, areaCode } = that.orderDetail.dealerInfo || {}
                  let path
                  if (codeType.includes(resss.data.code)) {
                    if (!resss.data.data) {
                      path = '/order/payment-error' // 无限量号的界面
                    } else {
                      path = '/order/payment-success' // 带限量号的界面
                    }
                    that.$router.push({
                      path,
                      query: {
                        dealerCode,
                        areaCode,
                        orderId: orderId || that.$store.state.orderId,
                        colorNameCn: that.colorNameCn,
                        inColorNameCn: that.inColorNameCn
                      }
                    })
                  }
                } else {
                  // 失败到详情
                  if (i >= 4) {
                    that.reload()
                    that.$store.commit('hideLoading')
                  } else {
                    loopQuery(++i)
                  }
                }
              }
            }
          }, 1000)
        }
      }
    },
    goOrDialog() {
      if (this.orderStatus === '30') {
        this.show = true
        // 埋点
        const params = {
          cars_appearance: this.colorNameCn || '',
          cars_limit: this.limitNumber || '',
          intention_money: this.formData.orderItemList[0].reserveOrderAmount,
          order_number: this.formData.orderId,
          order_time: this.formData.orderTime,
          types_name: this.formData.carBuyerInfo.fullName || '',
          types_telephone: this.formData.carBuyerInfo.mobile || 0,
          types_document: this.formData.carBuyerInfo.moreContact[0],
          document_id: this.formData.carBuyerInfo.moreContact[1] || 0,
          enterprise_name: this.formData.carBuyerInfo.enterpriseName || '',
          enterprise_id: this.formData.carBuyerInfo.enterpriseCode || '',
          buy_city: this.formData.carCustomInfo.carLicenseCityName || '',
          owner_name: this.formData.carBuyerInfo.carOwnerName || '',
          owner_number: this.formData.carBuyerInfo.carOwnerMobile || '',
          owner_types: this.formData.carBuyerInfo.carOwnerCertificateType || '',
          owner_id: this.formData.carBuyerInfo.carOwnerCertificateNumber || ''
        }
        this.$sensors.track('applyUnsubscribe', params)
      } else {
        // let query = {}
        if (this.formData.carBuyerInfo.buyType === '01') {
          // query = {
          //   buyType: this.formData.carBuyerInfo.buyType,
          //   fullName: this.formData.carBuyerInfo.fullName,
          //   mobile: this.formData.carBuyerInfo.mobile,
          //   type: this.formData.carBuyerInfo.moreContact[0],
          //   code: this.formData.carBuyerInfo.moreContact[1],
          //   carOwnerName: this.formData.carBuyerInfo.carOwnerName,
          //   carOwnerMobile: this.formData.carBuyerInfo.carOwnerMobile,
          //   carOwnerCertificateType:
          //     this.formData.carBuyerInfo.carOwnerCertificateType,
          //   carOwnerCertificateNumber:
          //     this.formData.carBuyerInfo.carOwnerCertificateNumber,
          //   carLicenseCityName: this.formData.carCustomInfo.carLicenseCityName,
          //   carLicenseCityCode: this.formData.carCustomInfo.carLicenseCityCode
          // }
        } else if (this.formData.carBuyerInfo.buyType === '02') {
          // query = {
          //   buyType: this.formData.carBuyerInfo.buyType,
          //   fullName: this.formData.carBuyerInfo.fullName,
          //   mobile: this.formData.carBuyerInfo.mobile,
          //   type: this.formData.carBuyerInfo.moreContact[0],
          //   code: this.formData.carBuyerInfo.moreContact[1],
          //   carLicenseCityName: this.formData.carBuyerInfo.carLicenseCityName,
          //   carLicenseCityCode: this.formData.carBuyerInfo.carLicenseCityCode,
          //   enterpriseName: this.formData.carBuyerInfo.enterpriseName,
          //   enterpriseCode: this.formData.carBuyerInfo.enterpriseCode
          // }
        }
        this.$router.push({
          path: '/order/user-agreement',
          query: {
            status: true,
            orderId: this.$route.query.orderId
          }
        })
      }
    },
    // 复制按钮
    copy() {
      const clipboard = new Clipboard('#orderId')
      clipboard.on('success', (e) => {
        window.console.log('复制成功', e)
        Toast({
          message: '复制成功',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', () => {
        // 不支持复制
        Toast({
          message: '手机权限不支持复制功能',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
        // 释放内存
        clipboard.destroy()
      })
    },
    // 坚持退订
    confim() {
      // 埋点
      this.haveConfirmed(1)
      this.$router.push({
        path: '/order/information-cause',
        query: {
          orderItemId: this.formData.orderItemList[0].orderItemId || '',
          orderId: this.$route.query.orderId || '',
          valid: this.invalid == 0 ? 0 : 1,
          updateFlag: this.updateFlag,
          seriesCode: this.carSeries.seriesCode
        }
      })
    },
    isProgres() {
      if (this.carSeries.seriesCode === 'G4'
          && this.licenseStatus
          && parseInt(this.orderStatus) > 30) {
        return true
      }
      return false
    },
    // 查询牌照额度申请状态
    async getQueryLicenseStatus() {
      const { orderId } = this.$route.query
      const { data } = await getQueryLicenseStatus({ orderId })
      this.carModel = data.data.carModel
      this.licenseStatus = data.data.licenseStatus
    },
    // 我再想想
    think() {
      this.show = false
      this.haveConfirmed(2)
    },
    haveConfirmed(num) {
      const params = {
        page_name: '意向金订单详情',
        cars_appearance: this.colorNameCn,
        cars_interior: this.inColorNameCn
      }
      if (num === 1) {
        this.$sensors.track('insistUnsubscribe', params)
      } else if (num === 2) {
        this.$sensors.track('thinkAbout', params)
      }
    },
    countDownFun(times, cd = false) {
      let day = 0
      let hour = 0
      let minute = 0
      let second = 0
      if (times > 0) {
        day = Math.floor(times / (60 * 60 * 24))
        hour = Math.floor(times / (60 * 60)) - (day * 24)
        minute = Math.floor(times / 60) - (day * 24 * 60) - (hour * 60)
        second = Math.floor(times) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60)
      }
      const string = ['天', '时', '分', '秒']
      const time = [day, hour, minute, second]
      const number = time.filter((i) => i > 0)[0] || ''

      if (day <= 9) day = `0${day}`
      if (hour <= 9) hour = `0${hour}`
      if (minute <= 9) minute = `0${minute}`
      if (second <= 9) second = `0${second}`
      return cd ? [hour, minute, second] : number + string[time.indexOf(number)]
      // this.countDown = [hour, minute, second, number + string[time.indexOf(number)] || '']
    },
    setDealerinfo(dealerInfo) {
      // dealerInfo.dealerId 是代理商的 dealerCode
      this.$store.dispatch('getDealerByCode', {
        dealerCode: dealerInfo.dealerId
      })
    },

    // 小订后是否可以更新订单的信息
    async canXdUpdateInfo(orderId) {
      const { data } = await canXdUpdateInfo({ orderId })
      this.canXdStatus = data.data
    },

    async getMyOrders(orderId) {
      networkToast()

      const { data } = await getMyOrders({ orderId }) // getMyOrders  //getOrderDetails

      console.log('订单信息', data)
      this.orderInfo = data.data

      if (codeType.includes(data.code)) {
        // 添加从小程序跳转过来的逻辑
        // url中携带  action 表示从 小程序跳转过来
        const { action } = getUrlParamObj()
        if (action === 'dading') {
          if (data.data.orderItemList[0].status === '31') {
            // 大定在小程序支付成功回到此页面需 跳转到支付成功的页面
            this.$router.push({
              path: '/dading-success',
              query: { orderId }
            })
          }
        }

        // if (action === 'xiaoding') {
        //   const res = await getOrderLimitedNumberStatus({ orderId: orderId })
        //   if (codeType.includes(res.data.code)) {
        //     //= 30支付成功
        //     if (res.data.data.status === 1) {
        //       const resss = await getLimitedNumber({ orderId: orderId })
        //       this.$store.commit('hideLoading')
        //       const { cityCode, dealerCode } = data.data.dealerInfo
        //       let path
        //       if (codeType.includes(resss.data.code)) {
        //         if (!resss.data.data) {
        //           path = '/order/payment-error' // 无限量号的界面
        //         } else {
        //           path = '/order/payment-success' // 带限量号的界面
        //         }
        //         this.$router.push({
        //           path,
        //           query: {
        //             cityCode,
        //             dealerCode,
        //             orderId: orderId,
        //             colorNameCn: this.colorNameCn,
        //             inColorNameCn: this.inColorNameCn
        //           }
        //         })
        //       }
        //     }
        //   }
        // }

        // 是否已经签订合同 purchaseContractStatus: 0:未签署, 1: 用户(线下)已签署 2:线上合同已签署, 10: 客户已签署
        // 目前只判断 1 或 2, 状态有问题 @lixuzhou
        this.isSignedContract = data.data.contractInfo?.purchaseContractStatus === '2' || data.data.contractInfo?.purchaseContractStatus === '1'

        // 签署线上合同完成才可查看订单
        this.isSignedOnlineContract = data.data.contractInfo?.purchaseContractStatus === '2'

        // 查询订单透明化 是否有新未读节点
        if (this.isSignedOnlineContract) {
          const { data: { data, code, message } } = await checkOrderTraceInfoStatus({ orderId })

          console.log('%c [ checkOrderTraceInfoStatus ]-2583', 'font-size:14px; background:#cf222e; color:#fff;', `isSignedOnlineContract:${this.isSignedOnlineContract}`, data)
          if (codeType.includes(code) && data?.status === 0) {
            this.warning.lifecycle = true
          }
        }

        // 双方已签署
        this.isAllSignedContract = data.data.contractInfo?.purchaseContractStatus === '2'

        // 是否允许退款
        this.allowRefund = data.data.extInfo?.boolConfirmReturn

        // 20 贷款  10全款
        this.paymentMethod = data.data.extInfo?.paymentMethod

        this.orderStatus = data.data.orderItemList[0].status
        // 查询是否使用过数字人民币
        this.getWhetherOrderDigitalCertificate(orderId, this.orderStatus)
        // 定金金额
        this.allPayPrice = data.data.orderItemList[0].confirmOrderAmount
        // skuid
        this.skuid = data.data.orderItemList[0].skuId

        // 下单信息
        this.carBuyerInfo = data.data.carBuyerInfo
        this.formData = data.data

        if (this.orderStatus) {
          this.lifecycleStatus = this.orderStatus
        }

        // 订单详情
        this.orderDetail = data.data
        if(this.orderDetail.orderType == '07' && !['90','98','99','84','81'].includes(this.orderStatus)) {
          this.tipsStatus = true
        }
        // 获取经销商信息
        this.setDealerinfo(data.data.dealerInfo)

        // 虎年限定 推荐车型等 不需要小订 直接大定  取消支付显示应该走大定流程
        if (this.orderStatus === '00' && this.orderDetail.payList && this.orderDetail.payList.length === 1 && this.orderDetail.payList[0].payPhase
            === '11') {
          this.orderStatus = '301'
        }


        this.ORDER_STATUS_DISTRICT_LIFE_MIN_CYCLE = [...ORDER_STATUS_DISTRICT.LIFE_MIN_CYCLE]
        if (this.orderDetail?.payList?.length) {
          this.orderDetail.payList.forEach((i) => {
            if (i.status === '3') {
              i.payPhase === '11' && (this.lastPaidAmount = i.amount)
              i.payPhase === '10' && (this.firstPaidAmount = i.amount)
            }
          })
          const [{ payPhase }] = this.orderDetail.payList || [{ payPhase: '' }]
          if (payPhase === '11') {
            this.isOneStep = true
            this.ORDER_STATUS_DISTRICT_LIFE_MIN_CYCLE[0].text = '待支付定金'
            console.log('%c [ this.isOneStep ]-2863', 'font-size:14px; background:#263238; color:#fff;', this.isOneStep)
          }
        }


        if (this.orderStatus === '30') {
          // 30: 已付款
          sessionStorage.setItem('orderType', '1')
        } else {
          sessionStorage.setItem('orderType', '0')
        }
        if (this.orderStatus !== '00') {
          this.$emit('set-bottom', true)
        }

        data.data.carBuyerInfo.moreContact = data.data.carBuyerInfo.moreContact
          .split(',')
          .map((item, index) => {
            if (index === 0) {
              const str = columns1.find((it) => it.value === item).text
              return str
            }
            return item
          })
        if (data.data.carBuyerInfo.buyType === '01') {
          data.data.carBuyerInfo.carOwnerCertificateType = columns1.find(
            (item) => (
              item.value === data.data.carBuyerInfo.carOwnerCertificateType
            )
          ).text
        }

        const bool = this.orderStatus !== '00' || !(this.payType === '1' && !this.isUpload)

        if (bool) {
          // getCurrentNavigationHeight 安卓特有的native方法
          // const resData = await callNative('getCurrentNavigationHeight', {})
          // if (resData?.navigationHeight) {
          //   this.navigationHeight = `${resData.navigationHeight}px`
          // }
        }
        // 未付款倒计时
        if (data?.data?.payRemainSeconds) {
          const { payRemainSeconds } = data.data
          this.countDownNum = payRemainSeconds
          // this.countDownTime = this.countDownFun(payRemainSeconds)
          this.timer = setInterval(() => {
            this.countDownNum--
            if (this.countDownNum >= 0) {
              this.countDown = this.countDownFun(this.countDownNum, true)
            } else {
              clearInterval(this.timer)
              this.reload()
            }
          }, 1000)
        }


        // 查询订单透明化
        if (this.isSignedOnlineContract && ![...ORDER_STATUS_DISTRICT.REFUND, ...ORDER_STATUS_DISTRICT.REFUNDED, ...ORDER_STATUS_DISTRICT.DIES].includes(this.orderStatus)) {
          const { data: { data } } = await getTraceInfo({ orderId }) || { data: { data: [] } }
          if (data?.length) {
            this.isTransparency = true
          }
        }


        const { data: { carCustomId, orderItemList: [item] } } = data || {}
        // 获取车配信息
        this.getCarDetailByCcid(data.data.carCustomId, item.productId, orderId, this.orderStatus, this.paymentMethod, data.data.payList)

        const { data: { data: voucher, code, message } } = await getMineInsuranceVoucher({ orderId })
        // hasCoupon 1 => 有券，2 => 无券
        if (code === '00' && voucher?.hasCoupon) {
          const { hasCoupon, jumpAddress } = voucher
          const {
            INSURANCE: {
              items, prepare: { cardBags, buyInsurance }, desc, conf
            }
          } = this
          this.popInsurance = {
            desc,
            hasCoupon,
            jumpAddress,
            message,
            mineInsuranceVoucher: hasCoupon,
            conf: { ...conf, ...{ show: true } },
            items: [...items, ...(hasCoupon === 2 ? [buyInsurance] : [cardBags])]
          }
        } else {}

        // 根据orderId获取限量号
        this.getLimitedNumber(orderId)
      }
    },
    async getCarDetailByCcid(ccid, prodId, orderId, orderStatus, paymentMethod, payList) {
      console.log('%c [ getCarDetailByCcid ccid, prodId]-2326', 'font-size:14px; background:#cf222e; color:#fff;', ccid, prodId, this.orderStatus, this.orderDetail)
      this.ccid = ccid
      // this.goCC(1)
      this.$store.commit('updateCcid', ccid)
      const { data } = await this.$store.dispatch('getCarDetailByCcid', { ccid, page: 'money-detail' })
      const {
        depositType, configDetail, configDetail: {
          carModel: { modelCode }, carSeries: { seriesCode }, optionList, totalPrice
        }
      } = data || {}

      // 获取轻重装权益包
      const optionListOptionCode = optionList.map((i) => i.optionCode)
      const [optionData] = CCPRO_RIGHTS_PACKAGE.filter((i) => optionListOptionCode.includes(i.optionCode)) || ''
      if (optionData?.optionCode) {
        this.ccProRPCode = optionData?.optionCode
      }

      const ccImagesUrl = getCalcCMTImages(configDetail)
      this.sideSeriesImgUrl = ccImagesUrl ? (baseOssHost + ccImagesUrl) : this.carImgUrl
      console.log('getCarDetailByCcid', data)

      this.carInfo = data

      if (+data?.valid != 1 && data && data?.valid !== null) {
        const { orderStatus } = this.orderInfo
        this.invalid = +data.valid
        //  98-已取消， 99-已关闭   取消弹窗
        // 0是普通失效只能去退订，1有效不变，2选装下架失效，3车型下架失效
        this.visibleDialog = (this.invalid != 1 || !this.loanStatusBool) && !'99,98'.includes(orderStatus)
        console.log('this.orderStatus', orderStatus)
        this.showPayPriceButton = false
        this.invalidReason = data.invalidReason
      }


      if (data?.valid == 1) {
        this.invalid = +data.valid
      }

      if ([1, 2].includes(data.updateFlag) && data?.updateContent) {
        const { orderStatus } = this.orderInfo
        this.updateFlag = data.updateFlag
        this.updateVis = !'99,98'.includes(orderStatus)
        this.showPayPriceButton = false
      }

      const param = { orderId: this.$route.query.orderId }
      const respones = await getLoanStatus(param)
      this.$store.commit('hideLoading')
      // 订单贷款信息(修复多次查询贷款信息)
      // const { data: { data: loans } } = await getLoanStatus({ orderId }) || { data: { data: [] } }
      const { data: { data: loans } } = respones || { data: { data: [] } }
      let loanAmount = 0
      if (paymentMethod === '20' && loans?.length) {
        const list = loans.filter((i) => i.loanStatus !== 4)
        if (list?.length) {
          const loanData = list[0]
          this.financeButtonText = LOAN_STATUS[loanData.loanStatus].text || ''
          this.isLoansWarning = +[0, 1].includes(loanData.loanStatus) || 0
          loanAmount = loanData?.loanAmount || 0
        } else {
          this.financeButtonText = LOAN_STATUS[4].text || ''
        }
      } else {
        this.financeButtonText = ''
      }

      // if( respones.data.data == false) return
      this.loanStatusBool = respones.data.data == false ? true : '0,1,4'.includes(respones.data.data[0]?.loanStatus) // 是否申请贷款   0 1 4 代表未申请
      if (['30', '301', '31', '32', '90'].includes(orderStatus) && ['G4'].includes(seriesCode)) {
        this.testDrive.q5 = true
      }
      // updateFlag === 2 时需要退订
      this.loanStatusBool = data.updateFlag !== 2

      this.packageItem = this.loanStatusBool ? data.updateContent : '由于您所选择的配置价格有更新，当前订单已失效，您需要退订后重新下单'
      // this.invalidReason = this.loanStatusBool ? data.invalidReason :  '由于您所选择的配置价格有更新，当前订单已失效，您需要退订后重新下单'

      console.log('失效状态 0失效 、orderStatus ', this.invalid, this.orderStatus)
      console.log('更新状态， 1 有更新', data.updateFlag)

      console.log('%c [ modelCode, seriesCode, depositType, prodId, totalPrice ]-2331', 'font-size:14px; background:#cf222e; color:#fff;', modelCode, seriesCode, depositType, prodId, totalPrice)
      if ([modelCode, seriesCode, depositType, prodId].every((i) => i)) {
        this.handleGetNgaPrice(modelCode, seriesCode, depositType, totalPrice, prodId, ccid, loanAmount, payList)
      }

      // 订单状态 (数据匹配)
      let orderMaxStatus = {
        paymentMethod: this.paymentMethod,
        orderStatus,
        seriesCode
      }

      if (['31', '32', '90'].includes(orderStatus)) {
        const { data: { data, code } } = await getOrderMniStatus({ orderId }) || {}

        console.log('%c [ getOrderMniStatus ]-2785', 'font-size:14px; background:#cf222e; color:#fff;', data)
        if (data && codeType.includes(code)) {
          orderMaxStatus = { ...orderMaxStatus, ...data }
        }
        if (!data) {
          if (orderStatus === '31') {
            if (this.isSignedOnlineContract) {
              this.lifecycleStatus = '41'
              orderMaxStatus = { ...orderMaxStatus, ...{ orderStatus: this.lifecycleStatus } }
            }
          }
        } else {
          if (+data.status > 101) {
            this.isDeliveryContract = true
          }
          if ([102, 101].includes(data.status)) {
            this.lifecycleStatus = '41'
          } else if ([104, 105, 103].includes(data.status)) {
            this.lifecycleStatus = '42'
          } else if ([106].includes(data.status)) {
            this.lifecycleStatus = '32'
          }
        }
      }
      if (!ORDER_STATUS_DISTRICT.DIES.includes(this.orderStatus)) {
        if (this.updateVis) {
          orderMaxStatus = { ...orderMaxStatus, ...{ orderStatus: '97IN' } }
        }
        if (this.visibleDialog) {
          orderMaxStatus = { ...orderMaxStatus, ...{ orderStatus: '97UP', invalid: this.invalid } }
        }
      }


      console.log('%c [ orderMaxStatus lifecycleStatus updateFlag invalid ]-2796', 'font-size:14px; background:#cf222e; color:#fff;',
        orderMaxStatus, this.lifecycleStatus, this.updateFlag, this.invalid)

      this.lifecycleCurrent = ['42', '32', '90'].includes(this.lifecycleStatus) ? '06' : '01'

      this.orderMaxStatus = { ...orderMaxStatus }

      if (['98', ...ORDER_STATUS_DISTRICT.DA_DING_BEFORE].includes(orderStatus) && !this.goodsInfoHandle) {
        this.goodsInfoHandle = true
      }

      // PGC 文章
      if ([...ORDER_STATUS_DISTRICT.DA_DING_BEFORE, ...ORDER_STATUS_DISTRICT.REFUND, ...ORDER_STATUS_DISTRICT.REFUNDED, ...ORDER_STATUS_DISTRICT.DIES].includes(orderStatus)) {
        const { data } = await checkPGCData()
        if (data?.code && RES_SUCCEED_CODE.includes(data.code)) {
          const [{ content: { contentDetailList } }] = data?.data?.filter((i) => seriesCode === i.floorFrontCode?.split('-')?.pop())
          if (contentDetailList[0]?.linkObject?.target) {
            this.pgcURL = contentDetailList[0]?.linkObject?.target
          }
        }
      }
    },
    async handleGetNgaPrice(modelCode, seriesCode, depositType, totalPrice, prodId, ccid, loanAmount, payList) {
      const {
        orderStatus, orderDetail: { contractInfo: { purchaseContractTotalPrice }, contractInfo }, isSignedContract, carBuyerInfo: { buyType }
      } = this
      const { data: { data: { prodNgaDepositPrice, prodNgaDownPayPrice } } } = await getNgaPrice({
        modelCode, seriesCode, depositType, prodId
      })

      const [depositPrice] = payList.filter((i) => i.status === '3' && i.payPhase === '10')
      const [payPrice] = payList.filter((i) => i.status === '3' && i.payPhase === '11')
      // if (prodNgaDepositPrice && prodNgaDownPayPrice) {
      // 大小定金额
      this.depositPrice = { prodNgaDepositPrice, prodNgaDownPayPrice }
      // prodNgaDownPayPrice（意向金+ 定金）
      // }

      if (depositPrice?.amount) {
        this.depositPrice.prodNgaDepositPrice = depositPrice.amount
        if (payPrice?.amount) {
          this.depositPrice.prodNgaDownPayPrice = depositPrice.amount + payPrice.amount
        }
      }


      // 特费车
      const specialAmount = await this.getCarSpecPrice(ccid)
      // 贷款信息(元)
      loanAmount && (this.loanAmount = loanAmount)
      // 实际价格 (元) 完成购车合同，使用合同价格
      const realPrice = this.isSignedOnlineContract ? purchaseContractTotalPrice : (specialAmount || totalPrice)
      // 卡券 这里的totalPrice单位是元  contractInfo.purchaseContractTotalPrice,,,,,,, equityAmount（分） (this.equityAmount/元)
      const equityAmount = await this.handleGetCarPaymentInfo(realPrice)

      console.log('%c [ this.isSignedOnlineContract  ]-2986', 'font-size:14px; background:#cf222e; color:#fff;', this.isSignedOnlineContract)
      if (specialAmount >= 0 && loanAmount >= 0 && equityAmount >= 0) {
        // 实际总价格 （分）
        const totalAmount = realPrice * 100
        // 零首付 （分计算）
        if (((depositPrice?.amount || 0) + (payPrice?.amount || 0)) > (totalAmount - (equityAmount + loanAmount * 100))) {
          this.zeroPayment = true
          console.log('%c [ 零首付 ]-2979', 'font-size:14px; background:#f60; color:#fff;')
        }
        this.totalPrice = totalAmount / 100
        this.depositAmount = (this.depositPrice.prodNgaDownPayPrice - (this.isOneStep ? 0 : this.depositPrice.prodNgaDepositPrice)) / 100
        // 待支付尾款 （分计算）
        const balancePayment = totalAmount - ((buyType === '02' || this.zeroPayment) ? 0 : this.depositPrice.prodNgaDownPayPrice) - equityAmount - (loanAmount * 100)
        this.balancePayment = (balancePayment < 0 ? 0 : balancePayment) / 100
        // totalPrice 总价， balancePayment 尾款
        console.log('%c [ totalAmount, purchaseContractTotalPrice, prodNgaDepositPrice, prodNgaDownPayPrice, loanAmount, equityAmount ]-2489', 'font-size:14px; background:#000; color:#fff;',
          totalAmount, purchaseContractTotalPrice * 100, this.depositPrice.prodNgaDepositPrice, this.depositPrice.prodNgaDownPayPrice, loanAmount * 100, equityAmount, payList, depositPrice?.amount, payPrice?.amount)
      }

      // orderStatus 301大定前 (意向金)
      // if (['30', '301', '98'].includes(orderStatus)) {
      //   const payPrice = (prodNgaDownPayPrice - prodNgaDepositPrice) / 100
      //   this.toPayPrice = payPrice
      //   this.depositAmount = payPrice
      // } else if (orderStatus === '00') {
      //   this.toPayPrice = prodNgaDepositPrice / 100
      // } else if (ORDER_STATUS_DISTRICT.DA_DING.includes(orderStatus)) {
      //   // console.log('%c [ 状态31, 32, 90的时候 是 paylist status为3的全部金额 ]-2494', 'font-size:14px; background:#cf222e; color:#fff;')
      //   // this.toPayPrice = 0
      // } else {
      //   this.toPayPrice = prodNgaDownPayPrice / 100
      // }

      console.log('%c [ handleGetNgaPrice ]-2304', 'font-size:14px; background:#cf222e; color:#fff;', prodNgaDepositPrice, prodNgaDownPayPrice, orderStatus, contractInfo)
    },
    // 查询数字人民币支付相关信息
    async getWhetherOrderDigitalCertificate(orderId, orderStatus) {
      // tag
      /**
         * 产品对这里还没有清晰的定义
         * 这里先用 orderStatus 30/31的方式来判断大小定.其他状态先不考虑???
         */
      if (orderStatus !== '30' && orderStatus !== '31') return

      const queryParam = {
        30: 1,
        31: 2
      }
      const { data } = await getDigitalPayStatus({
        orderId: orderId,
        orderIdentification: queryParam[orderStatus] // 1小订 2大定
      })

      if (!codeType.includes(data.code)) return

      // 返回的参数：0：没有使用数字人名币  1：已经使用数字人名币 2：第一次进入支付页面，没有选择支付方式
      this.payType = data.data

      if (data.data === '1') {
        const res = await getDigitalPayInfo({
          orderId,
          orderIdentification: queryParam[orderStatus] // 1小订 2大定
        })
        if (codeType.includes(res.data.code)) {
          if (res.data.data && res.data.data.digitalCertificateAddress) {
            this.isUpload = true
            this.$emit('set-bottom', true)
          }
        }
      }

      // if (data.data === 0) {
      //   this.getLimitedNumber(orderId)
      // }
    },
    // 查询订单是否有限量号
    // async getLimitedNumber(orderId) {
    //   const { data } = await getLimitedNumber({
    //     orderId
    //   })
    //   if (codeType.includes(data.code)) {
    //     this.showLimit = !!data.data
    //     // 如果没有限量号  查排队人数
    //     if (!data.data) {
    //       const data2 = await getLimitedNumbersLine()
    //       if (codeType.includes(data2.data.code)) {
    //         this.total = data2.data.data
    //       }
    //     }
    //   }
    // },
    async handleCheckLimitedNumber() {
      const {
        $router, $route, limitNumber, carSeries: { seriesCode }, currentModelLineCode, orderStatus
      } = this
      const { orderId } = $route.query

      if (limitNumber) {
        this.orderStatus
        return $router.push({
          path: seriesCode !== '49' ? '/limited-number/credentials' : '/order/limit-number',
          query: {
            orderId: $route.query.orderId,
            modelLineCode: currentModelLineCode,
            limitNumber,
            seriesCode,
            orderStatus
          }
        })
      }

      const { nickCode } = LIMIT_NUMBERS.filter((i) => i.seriesCode !== 'G4' && i.seriesCode === seriesCode)[0] || { nickCode: '' }

      console.log('%c [ nickCode ]-2794', 'font-size:14px; background:#cf222e; color:#fff;', nickCode)
      const { data: { data: { schedule, surplusLimitNumCount }, code, message } } = await getMineLimitedNumberStep({ orderId }, combiningStrings(nickCode)) || {}
      if (code !== '00' && message) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message,
          forbidClick: true,
          duration: 800
        })
      }
      if (schedule === 1 && surplusLimitNumCount < 1) {
        this.popLimitNumber = true
        return
      }

      const { nickCode: seriesNickCode } = LIMIT_NUMBERS.filter((i) => i.seriesCode === seriesCode)[0] || { nickCode: '' }

      if (seriesCode !== '49') {
        $router.push({
          name: `limited-number-${seriesNickCode}`,
          query: {
            orderStatus,
            orderId,
            seriesCode,
            modelLineCode: currentModelLineCode,
            ...(limitNumber ? { limitNumber } : {})
          }
        })
      }
    },
    async getLimitedNumber(orderId) {
      if (!orderId) {
        console.error(`orderId is ${orderId}`)
        return
      }
      if (this.carSeries.seriesCode === '49') {
        const { data } = await getLimitedNumber({ orderId }) // 查询a7l限量号  false没有
        if (codeType.includes(data.code)) {
          this.showLimit = !!data.data
          this.limitNumber = data.data.numberDisplay
          if (!data.data) {
            const res = await getLimitedNumberLineByOrderId({ orderId }) // 查询是否在队列中  0未排队  1在排队
            if (codeType.includes(res.data.code) && res.data.data) {
              const data2 = await getLimitedNumbersLine() // 查询排队号
              if (codeType.includes(data2.data.code)) {
                this.total = data2.data.data
              }
            }
          }
        }
      } else {
        const { data } = await getHasLimitedNumber({ orderId }) // 查询已有限量号
        if (codeType.includes(data.code) && data.data) {
          // this.showLimit = !!data.data // 无典藏号需显示
          this.limitNumber = data.data
        }
      }
    },

    submit(isSubmit) {
      if (isSubmit) {
        // 复制到粘贴板
        const copyResult = this.InsertShearPlate()
        this.modalshow = false
        if (copyResult) {
          Toast({
            message: '已复制到剪贴板',
            className: 'toast-dark-mini toast-pos-middle',
            forbidClick: true,
            duration: 800
          })
        } else {
          Toast({
            message: '复制失败',
            className: 'toast-dark-mini toast-pos-middle',
            forbidClick: true,
            duration: 800
          })
        }
      }
    },
    InsertShearPlate() {
      // 复制到粘贴板
      const text = 'https://app.saic-audi.mobi/index.html#/?channel=miniapp_VehiclePurchase'
      const copyString = text
      // 创建input标签存放需要复制的文字
      const myInput = document.createElement('input')
      // 把文字放进input中，供复制
      myInput.value = copyString
      document.body.appendChild(myInput)
      // 选中创建的input
      myInput.select()
      // 执行复制方法， 该方法返回bool类型的结果，告诉我们是否复制成功
      const copyResult = document.execCommand('copy')
      // 操作中完成后 从Dom中删除创建的input
      document.body.removeChild(myInput)
      return copyResult
    },
    // 购买保险跳转
    async handleClickPopupInsuranceBtn() {
      const { popInsuranceBtn: { text, enabled } } = this
      this.popInsuranceBtn.enabled = 0
      this.popInsuranceBtn.text = '跳转中 ...'
      const { data: { data: { userInfo: { displayName } } } } = await getUserInfo() || {}
      if (displayName) {
        const { data: { data, code } } = await getCarInsurance({ name: displayName })
        if (code) {
          this.popInsuranceBtn.enabled = 1
          this.popInsuranceBtn.text = ''
        }
        console.log(displayName, data)
      }
    },

    async toSpecCarRights() {
      const zmData = await getZMRightsProdId()
      const mallH5Data = await getAudiMallH5Url()
      const prodSkuId = zmData.data.data.configValue
      const mallH5Index = mallH5Data.data.data.configValue
      const { orderId } = this.$route.query
      const path = `${mallH5Index}mall/goods-detail?goodsId=${prodSkuId}&orderId=${orderId}&needHidden=1`
      console.log(`scaudi://mall/order?url=${path}`)
      callNative('openRoutePath', { path: `scaudi://mall/order?url=${path}` })
    },

    hasSpecCarRights() {
      // 如果已经交车了，权益的入口只是开放30天
      return (['31', '301', '32'].includes(this.orderStatus) || (this.orderStatus == '90' && dayjs().unix() - dayjs(this.orderDetail.contractInfo.deliveryContractTime).unix() < 30 * 24 * 3600)) && this.ZHU_MENG_CODES.includes(this.currentModelLineCode)
    },
    async handleGetActionBtn([emit, index, name]) {
      if (name !== 'card-bag') {
        const {
          popInsurance: {
            hasCoupon, jumpAddress, message
          }
        } = this
        this.popInsurance.loading = true

        if (hasCoupon !== 2 && !jumpAddress) {
          return Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: message || '跳转地址不存在',
            forbidClick: true,
            duration: 800
          })
        }
        callNative('audiOpen', { path: jumpAddress })
        this.popInsurance.conf.show = false
        return
      }
      //兼容6.4.3，老版本
        let r =  await callNative('getVersionInfo', {});
        let src = ''
        let v = isVersionGreaterOrEqual(r.versionName,'6.4.3')
          
        if(v){
          src =  'scaudi://mine/coupon/home?tabIndex=1'
        }else{
          src =  'scaudi://mine/singlecoupon/list?classificationCode=APP-CAR-RIGHTS&title=权益类'
        }

        item.subpagepath = src

        callNative('openRoutePath', { path: src })
      //兼容6.4.3，老版本 end
      this.popInsurance.conf.show = false
    },
    handleGoToPGCPage() {
      const { pgcURL, getDevice, minipH5 } = this

      console.log('%c [ pgcURL ]-3404', 'font-size:14px; background:#cf222e; color:#fff;', pgcURL, minipH5)
      const { nativeApp } = getDevice() || ''
      if (pgcURL) {
        if (nativeApp) {
          return callNative('openRoutePath', {
            path: pgcURL
          })
        }
        // scaudi://information/detail/1107138839639814144/A7L购车全流程
        // url: '/pages/web/index?url=information/detail/' + item.id
        if (minipH5 && this.$route.query.env === 'minip') {
          let string = pgcURL.replace(/scaudi:\/\//, '').split('/')
          string.pop()
          string = string.join('/')
          const url = encodeURIComponent(`${minipH5 + string}`)
          wx.miniProgram.navigateTo({ url: `/pages/web/index?&url=${url}` })
          // window.location.href = minipH5
        }
      }
    },
    handleGoodsInfoAction() {
      this.goodsInfoHandle = !this.goodsInfoHandle
    },
    handleGoToRightsPage() {
      const {
        $route, currentModelLineCode, carSeries: { seriesCode }, ccProRPCode
      } = this
      const { orderId } = $route.query
      this.$router.push({
        path: '/theEquity',
        query: {
          modelLineCode: currentModelLineCode || '', // 车型code
          caseCode: ccProRPCode || '', // 权益 optionCode
          orderId: orderId || '',
          seriesName: seriesCode || '' // 车系code: 49/G4/G6
        }
      })
    },
    async initData() {
      getAPITimeOutTesting()
      const { orderId, env, isSignedContract } = this.$route.query
      localStorage.setItem('orderId', orderId)
      await this.getMyOrders(orderId)
      this.canXdUpdateInfo(orderId)
      // this.$store.dispatch('getDealerInfo')
      this.getCanRefund()
      this.getisConfirmed()
      this.getDetailsByOrderId()
      this.getFloorslist()
      this.getNgaDetailsOrder()
      this.getNgaConfirmCanDdPay()
      this.getQueryLicenseStatus()
      if (env === 'minip') {
        const { data } = await getAudiMinipUrl()
        if (data?.code === '00' && data?.data?.configValue) {
          this.minipH5 = data.data.configValue
        }
        if (+isSignedContract === 0) {
          delay(() => {
            Toast({
              className: 'toast-dark-mini toast-pos-middle',
              message: '合同签署信息获取失败，请尝试再次签署',
              forbidClick: true
            })
          }, 800)
        }
      }
      Toast.clear()
    },
    networkReload() {
      this.initData()
    },
    ...mapGetters(['getDevice'])
  },
  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="less">
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/dialog.less");
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/nor.less");
  @import url("../../assets/style/common.less");

.tipB {
  font-size: 16px;
  color: #000;
  border-top: 0.5px solid #e5e5e5;
  border-bottom: 0.5px solid #e5e5e5;
  padding: 10px 18px;
}

  .boderBox {
    border: 1px solid;
    padding: 15px 0;
    width: 50%
  }

  .bckBox {
    background: black;
    color: white;
    padding: 15px 0;
    width: 50%;
    margin-left: 2px;
  }

  .border-bottom-none {
    border-bottom: none !important;
  }

  .van-dialog {
    overflow-y: auto;
    max-height: 80%;
    padding: 25px 16px 16px;
    top: 52%;
    z-index: 900;

    .dialog-wrapper {
      .c-font14;
      text-align: left;

      .info-wrapper {
        line-height: 30px;
      }

      .btn-wrapper {
        padding: 0 20px;
      }
    }
  }

  .carimg-wrapper {
    min-height: 180px;
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: center;
  }

  .bottom-line {
    border-bottom: 0.5px solid #e5e5e5;
    margin-bottom: 30px;
  }

  .moneyDetail {
    padding-bottom: 120px;

    .timer {
      font-size: 14px;
      line-height: 36px;
    }

    .warp {
      padding-bottom: 10px;

      p {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        line-height: 15px;
        padding: 16px 0 16px;
        margin: 0;
        text-decoration: underline;
      }
    }

    .box {
      padding-bottom: 0 !important;
    }

    .paading {
      padding: 0 16px 56px 16px;
    }

    .imgCard {
      margin-top: 24px;
      margin: 24px 15px 0 17px;
      padding: 0;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);

      .imgCardText {
        display: flex;
        justify-content: space-between;
        height: 54px;
        line-height: 54px;
        font-size: 14px;
        margin: 0 10px 0 16px;
        border-bottom: 0.5px solid #e5e5e5;

        .imgCardTextRight {
          display: flex;
          align-items: center;

          img{
            width: 45px;
            margin-right: 6px;
          }
        }

      }

      .imgCardText:last-child {
        border: 0;
      }
    }

    .paymentDetail {
      h3 {
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        font-weight: 500;
        color: #1a1a1a;
        font-family: "Audi-ExtendedBold";
        border-bottom: 0.5px solid #e5e5e5;
        margin: 0;
      }

      .paymentDetailMoney {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 48px;
        font-size: 14px;
        box-sizing: border-box;
        font-weight: 400;
        color: #000000;

        // border-bottom: 0.5px solid #e5e5e5;
        &:last-child {
          border-bottom: 0.5px solid #e5e5e5;
        }

        span {
          color: #000000;

          &:first-child {
            font-weight: 400;
          }

          &:last-child {
            // font-family: "Audi-WideBold";
          }
        }
      }

      img {
        width: 100%;
        margin-top: 16px;
      }

      .total-wrapper {
        text-align: right;
        box-sizing: border-box;
        line-height: 48px;
        border-top: 0.5px solid #e5e5e5;
        border-bottom: 0.5px solid #e5e5e5;
      }
    }

    .orderInformation {
      .order-info-list {
        display: flex;
        align-items: center;
        justify-content: space-between;

        h3 {
          font-size: 16px;
          font-weight: 500;
          font-family: "Audi-ExtendedBold" !important;
          color: #1a1a1a;
          // margin: 0;
        }

        .status {
          display: flex;
          align-items: center;
          justify-content: space-between;

          i {
            margin-left: 8px;
          }
        }
      }

      .copy {
        width: 16px;
        height: 16px;
        margin-left: 10px;
      }

      .list {
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 20px;
        font-family: "Audi-Normal";
        // min-height: 46px;
        padding: 12px 0;
        box-sizing: border-box;

        // border-bottom: 1px solid#e5e5e5;
        span {
          font-family: "Audi-ExtendedBold";
          font-weight: normal;
        }

        .between {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          flex: 1;

          span {
            font-family: "Audi-Normal";
          }

          & > div {
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }

      .right {
        justify-content: space-between;
        align-items: flex-start;

        .lable {
          line-height: 20px;
        }

        .value {
          display: flex;
          justify-content: flex-end;
          text-align: left;
          word-break: break-all;
        }
      }
    }

    .text {
      font-size: 18px;
      font-weight: 500;
      color: #1a1a1a;
      line-height: 32px;
    }

    .buttons {
      margin: 0 auto;
    }
  }

  // 经销商
  .dealer-wrapper {
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
    margin: 16px 16px;

    > .main-img-wrapper {
      min-height: 120px;
      overflow: hidden;
    }

    > .layout-bottom {
      display: flex;
      position: relative;
      font-size: 12px;

      > .left {
        width: 76%;
        padding: 20px 10px;

        .title {
          font-size: 16px;
        }

        .address {
          margin-top: 15px;
          color: #a3a3a3;
        }
      }

      > .right {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .distance {
          color: #a3a3a3;
          text-align: center;
        }
      }
    }
  }

  //购车合同
  .contract-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 16px;
    margin-top: 15px;
    border-bottom: 0.5px solid #e5e5e5;

    > h3 {
      font-size: 16px;
    }

    > .btn-sign {
      display: flex;
      align-items: center;
    }
  }

  // 金融服务
  .finance-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 15px 0 17px;
    border-bottom: 0.5px solid #e5e5e5;

    h3 {
      font-size: 16px;
      font-weight: 400;
      height: 24px;
      line-height: 24px;
    }

    > .status {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: calc(100% - 120px);
    }
    .right-tips-text {
      color: #666;
      font-size: 14px;
    }
    &.ish3-long {
      .status {
        width: calc(100% - 220px);
      }
    }

    // i {
    //   margin-left: 8px;
    // }
  }

  .btn-pay-wrapper {
    position: fixed;
    z-index: 999;
    left: 0;
    bottom: 0;
    right:0;
    padding: 5px 16px 39px;
    background-color: #fff;
    box-shadow: 0px -1px 16px 0px rgba(0,0,0,0.08);
    /deep/ .black-btn {
      &.freeze-btn {
        background-color: #e5e5e5 !important;
        border-color: #e5e5e5 !important;
      }
    }
  }

  .btnWarpper {
    position: fixed;
    height: 56px;
    width: 100%;
    bottom: 0px;
    padding-bottom: 50px;
    background: #fff;
    z-index: 1024;

    .buttons,
    .buttons2 {
      width: calc(100% - 32px);
      position: absolute;
      top: 0;
      bottom: 0;
      margin: 0 auto;
    }

    .bt {
      content: "";
      display: block;
      background: #000;
      opacity: 0.2;
      width: 134px;
      height: 5px;
      position: absolute;
      bottom: 9px;
      left: -32px;
      right: 0;
      margin: 0 auto;
      border-radius: 100px;
    }
  }

  .border-top {
    border-top: 0.5px solid #e5e5e5;
  }

  ._product {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    font-family: "Audi-Normal";

    &-item {
      width: 48%;
      display: flex;
      flex-flow: column;
      background: #FFFFFF;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
      margin-bottom: 16px;

      ._logo {
        width: 100%;

        img {
          width: 100%;
          height: 160px;
          object-fit: cover;
        }
      }

      ._content {
        padding: 0 8px;
        width: 100%;
        font-size: 12px;
        box-sizing: border-box;

        ._productName {
          color: #333;
          line-height: 20px;
          margin-top: 5px;
        }

        ._price {
          font-size: 14px;
          color: #000;
          line-height: 26px;
        }

        ._prevPrice {
          color: #B2B2B2;
          text-decoration: line-through;
          margin-bottom: 10px;
        }
      }
    }
  }

  ::v-deep .van-cell {
    padding: 8px;
    border: 1px solid #333;
    box-sizing: border-box;

    .van-field__word-limit {
      position: absolute;
      bottom: 0;
      right: 0px;
    }
  }

  ._title {
    font-size: 16px;
    font-weight: 500;
    font-family: "Audi-ExtendedBold" !important;
    color: #1a1a1a;
  }

.popup-custom {
  width: calc(100vw - 32px);
  box-sizing: border-box;
  .popup-custom-main {
    padding-top: 15px;
    &.remove-padding {
      padding-top: 0;
    }
    .head {
      h2 {
        font-size: 18px;
        line-height: 26px;
        margin: 0;
        text-align: center;
      }
    }
    .align-center {
        text-align: center;
    }
    .text {
      h3 {
        margin: 12px 0 2px;
        font-size: 14px;
        line-height: 20px;
      }
      p {
        margin: 4px 0 0;
        &.p {
          line-height: 24px;
          font-size: 16px;
          color:#000
        }
      }
      font-size: 16px;
      line-height: 160%;
      color: #333;
    }
  }
  .popup-custom-btn {
    margin-top: 30px;
    &.mtop20 {
      margin-top: 20px;
    }
    &.vertical-layout {
      .button {
        margin-top: 4px;
        &:first-child{
          margin: 0;
        }
      }
    }
  }
  .button-box, .popup-custom-btn {
    .button {
      transition: all .35s;
    }
    .black-btn {
      &.btn-un-enabled {
        background-color: #e2e2e2 !important;
        border-color: #e2e2e2 !important;
        color: #aaa !important;
      }
    }
    .white-btn {
      &.btn-un-enabled {
        background-color: #fefefe!important;
        border-color: #f2f2f2 !important;
        color: #c2c2c2 !important;
      }
    }
  }
}

.test-drive-box {
  border-top: solid 1px #E5E5E5;
  padding-top: 18px;
  .test-drive-media {
    position: relative;
    box-shadow: 1px 1px 6px rgba(0, 0, 0, 0.05),-1px -1px 8px rgba(0, 0, 0, 0.07);
    img {
      width: 100%;
      vertical-align: top;
    }
    .tips {
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      height: 48px;
      line-height: 46px;
      padding: 0 24px 0 12px;
      color: #fff;
      font-size: 14px;
      background-color: rgba(#000, 0.3);
      box-shadow: 0px 0px 6px 0px rgba(51,51,51,0.16);
      .btn {
        width: 64px;
        height: 24px;
        line-height: 23px;
        text-align: center;
        font-size: 12px;
        border: solid .5px #fff;
      }
    }
  }
}
.pgc-page, .order-lifecycle {
  height: 44px;
  line-height: 44px;
  padding: 0 16px;
  font-size: 12px;
}
.pgc-page {
  color: #fff;
  background: #010101 url("~@/assets/img/pgc-bg.png") no-repeat center/cover;
  span {
    font-size: 18px;
    line-height: 18px;
    &:first-child {
      margin-right: 8px;
      font-size: 22px;
    }
    &:last-child {
      margin-left: 15px;
      line-height: 17px;
    }
  }
}

.order-lifecycle {
  margin-top: 2px;
  color: rgba(0, 0, 0, 0.5);
  height: 40px;
  line-height: 40px;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
  /deep/.van-tabs__wrap {
      height: 40px;
      overflow: hidden;
  }
  /deep/.van-tabs__nav {
    padding: 0 8px;
    height: 40px;
    background-color: transparent;
    .van-tabs__line {
      display: none;
    }
    .van-tab {
      padding: 0 18px 0 0;
      font-size: 10px;
      &::before, &::after {
        content: '';
        position: absolute;
        z-index: 1;
      }
      &::before {
        z-index: 5;
        left: 0;
        top: 50%;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #e5e5e5;
        transform: translate(-50%, -50%);
        overflow: hidden;
      }
      &::after {
        height: 1px;
        border-top: dashed 1px #979797;
        left: 0;
        right: 0;
        top: calc(50% + 1px);
        transform: translateY(-50%);
      }
      &:nth-last-child(2) {
        padding: 0;
        &::after {
          right: 20px;
        }
      }

      .van-tab__text {
        z-index: 2;
        display: block;
        position: relative;
        padding-left: 10px;
        background-color: #fff;

      }
      &.van-tab--disabled {
        color: rgba(#000,.5);
      }
      &.lan-tab--active {
        color:#000;
        &::before {
          width: 12px;
          height: 12px;
          background-color: #000;
        }
      }
      &.lan-tab--one-step {
        &:first-child {
          display: none;
        }
      }
    }
  }
  /deep/.van-tabs__content {
    display: none;
  }
}
.order-life-in {
  height: 34px;
  line-height: 34px;
  margin-top: 8px;
  padding: 0 16px;
  font-size: 12px;
  color: rgba(#000,.6);
  background-color: #F2F2F2;
}
.goods-box {
  padding: 18px 16px 10px;
  border-bottom: 0.5px solid #e5e5e5;;
  &.has-border-top {
    border-top: 0.5px solid #e5e5e5;;
  }
  .goods-right {
    width: 160px;
    height: 90px;
    object-fit: cover;
    // overflow: hidden;
    // 由于当前车型图留白过于大，故做放大处理
    img {
      width: 120%;
    }
  }
  .goods-left {
    width: calc(100% - 170px);
    .h2 {
      font-size: 14px;
      font-weight: 400;
      margin: 0;
      line-height: 140%;
      color: rgba(#000,.9);
      width: 100%;
      height: 60px;
      -webkit-line-clamp: 3;
      word-break: break-word;
    }
    .price {
      font-size: 12px;
      line-height: 20px;
      color: rgba(#000,.6);
      font {
        color: #000;
        font-family: Audi-ExtendedBold;
        margin-left: 4px;
      }
      .tags {
        height: 13px;
        width: auto
      }
    }
  }
  .goods-handle {
    margin-top: 18px;
    .handle {
      height: 20px;
      font-size: 12px;
      color: rgba(#000,.6);
      position: relative;
      padding-right: 12px;
      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        right:0;
        top: 50%;
        margin: -4px 0 0 0;
        border: 4px solid transparent;
        border-top-color: #000;
      }
    }
    &.goods-unfold {
      .handle {
        &::after {
          margin: -9px 0 0 0;
          transform: rotate(180deg);
        }
      }
    }
  }
}
.fill-block-gray {
  border-bottom: 8px solid #f2f2f2;
}

.order-info-box {
  .title {
    box-sizing: content-box;
    margin: 0 16px;
    padding: 16px 0;
    .h2 {
      margin: 0;
      font-size: 16px;
      line-height: 24px;
      font-family: AudiTypeGB-WideBold, AudiTypeGB;
    }
  }
  // &.affirm-box {
  //   border: none;
  // }
  .order-config-list {
    margin: 0 16px;
    padding-bottom: 12px;
    .list {
      margin: 12px 0 0 0;
      font-size: 14px;
      color: rgba(0, 0, 0, .5);
      &.selected {
        color: rgba(#000,.8);
        .price {
          color:rgba(#000,.88);
          font-weight: 600;
        }
      }
      &.total-prices {
        color: #000;
        font-weight: 700;
        .thin {
          font-weight: normal;
        }
      }
      p {
        margin: 0;
        line-height: 20px;
        .left, &.right {
          color: rgba(0, 0, 0, .88);
        }
        &.right {
          margin-left: 20px;
          width: 210px;
          text-align: right;

          .van-icon-arrow {
            margin-left: 4px;
          }
        }
        &.min-text {
          font-size: 12px;
        }
        /deep/.icon-box {
            position: relative;
            .van-popover__wrapper {
              display: flex;
            }
            .icon-explain {
              width: 14px;
              height: 14px;
              margin-left: 4px
            }
            .tips {
              padding: 6px 6px 6px 12px;
            }
        }
        i {
          margin-left: 8px;
        }
        .separator {
          position: relative;
          top: -1px;
          margin: 0 8px;
          color: #D8D8D8;
        }

        .type {
        }
        .name {
        }
      }
      .text-underline {
        text-decoration: underline;
      }
    }
    .tips-banner {
      padding: 7px 16px;
      margin: 12px -16px;
      font-size: 10px;
      line-height: 20px;
      color: rgba(#000,.6);
      background-color: #F2F2F2;
    }
    .buy-t02 {
      border-top: solid .5px #e5e5e5;
      margin-bottom: -20px;
      margin-top: 8px;
      .tips-banner {
        margin-bottom: 0;
      }
      &.buy-t03 {
        margin-top: 0;
        border-top: none;
      }
    }
  }
}
.lan-tips.dialog{
  padding:24px !important;
  top: 50%;
  /deep/.van-dialog__content{
    padding: 4px 0 28px;
  }
  /deep/.van-dialog__footer{
    height: 44px;
    .van-button{
      height: 44px;
      line-height: 44px;
      font-size: 14px;
    }
  }
}
</style>
