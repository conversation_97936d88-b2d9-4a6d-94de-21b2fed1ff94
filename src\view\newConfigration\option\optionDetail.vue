<template>
  <div>
    <div class="main-img-wrapper">
      <img :src="ossUrl + currentDetail.imageUrlDetail | imgFix(640, true)" alt="">
    </div>

    <div class="content">
      <!-- 夏日礼包 -->
      <div v-if="summerPackage.includes(currentDetail.optionCode)" class="c-bold c-font14 c-lh22 name">
        <div>{{currentDetail.optionName}}</div>
        <!-- <div class="tag-wrapper">限时优惠</div> -->
      </div>
      <div v-else class="c-bold c-font14 c-lh22">
        {{ currentDetail.optionName }}
      </div>

      <div class="price c-font12 c-lh20">
        {{ currentDetail.price | finalFormatPriceDesc }}
      </div>

      <div class="list-wrapper">
        <div v-for="item in currentDetail.packetItems" v-show="[1,2].includes(item.status)"  :key="item.optionCode" class="item c-flex-center">
          <div class="img-wrapper">
            <img :src="ossUrl + item.imageUrlList | imgFix(100, true)" alt="">
          </div>
          <div class="">
            <div class="c-font12"> {{ item.optionName }} </div>
            <div class="c-font10 mt8" v-if="a7lFigureKey.includes(item.optionCode)">点击查看 <span class="black" @click="toCompotiblePage">&lt;兼容性声明及手机机型></span></div>
          </div>
        </div>
      </div>
    </div>

    <div class="footer-wrapper" v-if="!footerVisible">
      <div class="footer c-footer-shadow c-flex-between c-font14">

        <div class="left">
          <div class="price-wrapper c-bold c-font16">
            ￥{{ currentDetail.price | formatPrice }}
          </div>

          <div class="deposit-wrapper  c-font14">
            <!-- 辅助文字 -->
          </div>
        </div>

        <div class="right" v-if="!fromComposePage && !summerPackage.includes(currentDetail.optionCode)">
          <div class="next-btn" :class='{
            "bg-white": currentDetail.selected
          }' @click="toAddRemoveOption">
            {{ currentDetail.selected ? '移除选装' : '添加选装' }}
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import { Icon } from 'vant'
import url from '@/config/url'
import { SUMMER_PACKAGE, A7L_FIGURE_KEY } from '../util/carModelSeatData'

Vue.use(Icon)

const OSS_URL = url.BaseConfigrationOssHost
export default {
  data() {
    return {
      ossUrl: OSS_URL,
      fromComposePage: false, // 如果从组合页面跳转来, 就不显示按钮
      summerPackage: SUMMER_PACKAGE,
      a7lFigureKey: A7L_FIGURE_KEY
    }
  },
  computed: {
    ...mapGetters(['pageAllOptionList']),
    ...mapState({
      currentDetail: (state) => state.configration.currentOptionDetail
    }),
    footerVisible: function () {
      return this.$route.query.disabled === 'true'
    }
  },

  mounted() {
    const { tag } = this.$route.query
    this.fromComposePage = tag === 'composePage'
  },

  methods: {
    toAddRemoveOption() {
      this.$router.push({
        path: '/configrationContainer',
        query: {
          optionCode: this.currentDetail.optionCode
        }
      })
    },

    toCompotiblePage() {
      this.$router.push({
        path: '/configrationMobileCompatible',
        query: {
          // optionCode: this.currentDetail.optionCode
        }
      })
    }

  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";

@HEIGHT: 100px;

.main-img-wrapper {
  min-height: 208px;
}
.mt8{
  margin-top: 8px;
}
.black {
  color: #000;
}

.content {
  padding: 9px 16px;
  margin-top: 8px;
  padding-bottom: 120px;
  >.name {
    // position: relative;
    display: flex;
    align-items: center;
    >.tag-wrapper {
      position: relative;
      margin-left: 8px;
      background:#EB0D3F;
      color: #fff;
      font-weight: normal;
      font-size: 10px;
      line-height: 16px;
      box-sizing: border-box;
      padding:0 13px 0 5px;
      &::before{
        content: '';
        display: block;
        position: absolute;
        right: 0;
        top: 0;
        background-color: #fff;
        border-top: 9px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 7px solid #EB0D3F;
        border-left: 5px solid #EB0D3F;
      }
    }
  }
}

.price {
  color: #333333;
  margin-top: 4px;
  font-weight: normal;
  font-family: "Audi-ExtendedBold";
  opacity: 0.6;
}

.list-wrapper {
  margin-top: 12px;

  >.item {
    color: #808080;
    margin-top: 8px;

    .img-wrapper {
      flex-shrink: 0;
      width: 50px;
      height: 50px;
      margin-right: 13px;

      >img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.van-icon {
  font-size: 14px;

  &::before {
    vertical-align: middle;
  }
}

.footer {
  background: #fff;
  position: fixed;
  z-index: 20;
  bottom: 0;
  left: 0;
  width: 100%;
  height: @HEIGHT;
  padding: 5px 13px 0 13px;
  box-sizing: border-box;

  .left {
    height: min-content;

    .price-wrapper {
      margin-top: 10px;
    }

    .deposit-wrapper {
      color: #999999;
      margin-top: 4px;
    }
  }

  .right {
    border: 1px solid;
    height: min-content;

    >.next-btn {
      .c-font16;
      width: 140px;
      line-height: 56px;
      background-color: #000;
      color: #fff;
      text-align: center;

      &.bg-white {
        background-color: #fff;
        color: #000;
      }
    }
  }
}</style>
