
/**
 * @param {
 * selectCarInfo 车信息
 * modelLineCode  车型
 * modelLineCode 荣耀型机甲: "G4ICF3001"   星耀型机甲: "G4ICC3001"
 * currentSibColorInterieur 当前选中内饰+面料
 * sibInterieurCode AJ-N4X 内饰红翎-冰痕黑(AJ-N4X)
 * allPrivateOrderList 私人定制页面列表数据
 * optionCode  4D3 前排座椅通风
 * setPrivateOrderList 更新私人定制页面所展示的列表数据
 * currentOptionsList //私人定制 已选中 的数据
/**
  * @name defPatchesFn01
  * * 修改如下：
  * * 星耀机甲和荣耀机甲
  * * 删除规则：选择内饰红翎-冰痕黑(AJ+N4X)时，隐藏单选件：前排座椅通风（4D3）
  */
export const defPatchesFn01 = (the) => {
   
  const sibInterieurCode = the.$store.state.currentSibColorInterieur.sibInterieurCode
  //  console.log("currentSibColorInterieur================", the.$store.state.currentSibColorInterieur);
  const modelLineCode = the.selectCarInfo.modelLineCode
  const arr = ['G4ICF3001', 'G4ICC3001'] // 荣耀型机甲 G4ICF3001 星耀型机甲 G4ICC3001
  const condition1 = arr.includes(modelLineCode) // 是否包含： 荣耀型机甲、星耀型机甲
  const condition2 = sibInterieurCode === 'AJ-N4X' // 内饰红翎-冰痕黑(AJ+N4X)
  if (condition1 && condition2) {
    //  console.log("the.allPrivateOrderList================", the.allPrivateOrderList);
    const arr = the.allPrivateOrderList.filter((e) => e.optionCode !== '4D3')
    the.$store.commit('setPrivateOrderList', arr)
  }
}

/**
  * @name defPatchesFn02
  * * 修改如下：
  * * checkBox选中事件：六选一私人定制
  */
export const defPatchesFn02 = (the) => {
   
  // 这六个只能六选一
  if ('G4IBC3001,G4ICC3001'.includes(the.selectCarInfo.modelLineCode) && 'WE8,4A4,8I6+PV3+4A4,4D3+PV3+4A4,8I6+WE8,4D3+WE8'.includes(the.option.optionCode)) {
    the.$store.commit('setCurrentOptionsList', [
      ...the.currentOptionsList,
      the.option
    ])
    // 这六个只能六选一
    const sour = ['WE8', '4A4', '8I6+PV3+4A4', '4D3+PV3+4A4', '8I6+WE8', '4D3+WE8']
    const flagindex1 = the.currentOptionsList.findIndex((e) => sour.includes(e.optionCode) && the.option.optionCode !== e.optionCode)
    if (flagindex1 !== -1) {
      the.$store.commit('setDelOption', flagindex1) // 六选一
      if (the.unCheckOptionFun) the.unCheckOptionFun(the.option) // 取消选中
    }
  }
}

/**
 * @补丁： defPatchesFn03
 * * 修改如下：
 * *星耀锦衣和星耀机甲在内饰中进行座椅六选一后更新私人定制列表数据
 */
export const defPatchesFn03 = ({
  selectCarInfo,
  allPrivateOrderList,
  currentOptionsList,
  $store
}) => {
  const chairArr = ['WE8', '4A4', '8I6+PV3+4A4', '4D3+PV3+4A4', '8I6+WE8', '4D3+WE8'] // 座椅六选
  const carArr = ['G4IBC3001', 'G4ICC3001'] // 星耀锦衣和星耀机甲
  const optionCode = $store.state.otherClickOption.optionCode // 内饰中进行座椅六选一后存储的选中值
  const condition1 = carArr.includes(selectCarInfo.modelLineCode)
  const condition2 = chairArr.includes(optionCode)
  if (condition1 && condition2) {
    // 这六个只能六选一
    const temp = []
    currentOptionsList.forEach((e) => {
      if (chairArr.includes(e.optionCode)) {
        temp.push(e)
      }
    })
    const flagindex1 = currentOptionsList.findIndex((e) => chairArr.includes(e.optionCode) && optionCode !== '4A4' && e.optionCode === '4A4')
    if (flagindex1 !== -1 && temp.length !== 1) {
      $store.commit('setDelOption', flagindex1) // 六选一
    }
  }
}

/**
 * @补丁： defPatchesFn04
 * *私人定制六选一的时候选中加热座椅失败的bug
 * @param {
 * otherClickOption //内饰中进行座椅六选一后存储的选中值
 * } the
 */

export const defPatchesFn04 = (the, storage = {}) => {
   
  if (the.$store.state.currentTabIndex == 4) {
    const chairArr = ['WE8', '4A4', '8I6+PV3+4A4', '4D3+PV3+4A4', '8I6+WE8', '4D3+WE8'] // 座椅六选
    const carArr = ['G4IBC3001', 'G4ICC3001'] // 星耀锦衣和星耀机甲
    const selectCarInfo = the.$store.state.selectCarInfo
    const condition = carArr.includes(selectCarInfo.modelLineCode)
    const condition1 = chairArr.includes(the.clickOption.optionCode)
    if (condition && condition1) {
      const flagindex = the.currentOptionsList.findIndex((e) => chairArr.includes(e.optionCode))
      if (flagindex !== -1) {
        the.$store.commit('setDelOption', flagindex)
      }
      the.$store.commit('setCurrentOptionsList', [
        ...the.currentOptionsList, the.clickOption
      ])
      the.$store.commit('setShowOptionPopup', false)
    }
    return false
  }
}


// better 齐云型
// design 羽林套装
// sport 飞骑套装
// goodline 观云型
// better VR6 行云型
// edition one  凌云


// 齐云型 飞骑套装 &&  行云型 飞骑套装
// 外色影响内饰规则说明—Better Sport& Better VR6 Sport线  modelLineCode: "G6ICAY001"  modelCode:  "G6ICBY001"
// 选择外色2DA2时隐藏面料色AW
// 若外色选择了2DA2，则在内饰页面，面料
// AW红-高级Valcona真皮带菱形缝线和打孔
// 置灰
// 点击后弹窗提示用户置灰原因

export const patchesHandleColor = (the, item = {}) => {
   
  const modelLineCode = the.selectCarInfo.modelLineCode
  const arr = ["G6ICAY001", "G6ICBY001"]
  const condition = arr.includes(modelLineCode) 
  const sibColorInterieurList = the.$store.state?.sibColorInterieurList
  const exColor =  the.$store.state.currentExColor?.optionCode
  if ('2DA2'.includes(exColor) && condition && sibColorInterieurList.length > 0) {
    let obj = sibColorInterieurList.find(e => {
      return e.sibInterieurCode == 'N5D-AW'
    }) 
    if (obj) {
      sibColorInterieurList.forEach(e => {
        if (e.sibInterieurCode == 'N5D-AW') {
          e.opacityBool = true
        }
      });
      the.$store.commit('setSibColorInterieurList', sibColorInterieurList)
    }
  } else {
    let  obj = sibColorInterieurList.find(e => e.sibInterieurCode == 'N5D-AW' && e.opacityBool)
    if (obj) {
      sibColorInterieurList.forEach(e => {
        if (e.sibInterieurCode == 'N5D-AW') {
          e.opacityBool = false
        }
      });
      the.$store.commit('setSibColorInterieurList', sibColorInterieurList)
    }
  }
}


// 内饰影响装备规则 Better Design线  modelLineCode: "G6IBAY001" 齐云型 羽林套装
// 若内饰选择了“TI黑-Twinleather Nappa打孔真皮组合”或
// “TP棕-Twinleather Nappa打孔真皮组合”
// 则，在进入私人定制页面后：
// PS1尊享六座套装（七座->六座）
// PS3七座舒享套装
// PS8七座尊享套装
// 三件装备置灰，不可选中
// 且在装备包下面展示置灰不可选的原因
export const patchesChangeFabric = (the, item = {}) => {
   
  const modelLineCode = the.selectCarInfo.modelLineCode 
  const condition = ["G6IBAY001"].includes(modelLineCode) 
  const sibInterieurCode = the.$store.state.currentSibColorInterieur?.sibInterieurCode
  const condInt = ["N5U-TI", "N5U-TP"].includes(sibInterieurCode)
  if (condition && condInt) {
    if (item.optionCode &&  ['PS1', 'PS3', 'PS8'].includes(item.optionCode)) {
      return true
    } else {
      let list = ['PS1', 'PS3', 'PS8']
      the.privateOrderList.forEach(e => {
        if (list.includes(e.optionCode)) {
          e.why = '此装备与当前所选内饰冲突，请选中其他内饰后尝试'
        }
      });
      return false
    }
  }else {
    the.privateOrderList.forEach(e => {
        e.why = ''
    });
    the.$store.commit('setPrivateOrderList', the.privateOrderList)
  }
}


// 内饰影响装备规则 Better Sport  modelLineCode: "G6ICAY001" 齐云型 飞骑套装
// 若内饰选择了“TO黑-Dinamica真皮组合面料印花”或
// “OQ灰-Dinamica真皮组合面料印花”
// 则，在进入私人定制页面后，
// 不可选
// PS1尊享六座套装（七座->六座）
// PS3七座舒享套装
// PS8七座尊享套装
export const patchesChangeSport = (the, item = {}) => {
  const modelLineCode = the.selectCarInfo.modelLineCode 
  const condition = ["G6ICAY001"].includes(modelLineCode) 
  const sibInterieurCode = the.currentSibColorInterieur?.sibInterieurCode
  const condInt = ["N7K-TO", "N7K-OQ"].includes(sibInterieurCode)
  console.log("condition && condInt", condition , condInt);
  if (condition && condInt) {
    if (item.optionCode &&  ['PS1', 'PS3', 'PS8'].includes(item.optionCode)) {
      return true
    } else {
      let list = ['PS1', 'PS3', 'PS8']
      the.privateOrderList.forEach(e => {
        if (list.includes(e.optionCode)) {
          e.why = '此装备与当前所选内饰冲突，请选中其他内饰后尝试'
        }
      });
      return false
    } 
  } else {
    the.privateOrderList.forEach(e => {
        e.why = ''
    });
    // the.$store.commit('setPrivateOrderList', the.privateOrderList)
  }
}



// 内饰影响装备规则 Better VR6 Sport线  modelLineCode: "G6ICBY001" 行云型 飞骑套装
// 若内饰选择了“TO黑-高级Valcona真皮带菱形缝线和打孔” 
// 则，在进入私人定制页面后隐藏PS2六座前排尊享套装
export const patchesChangeVR6Sport = (the, item = {}) => {
   
  const modelLineCode = the.selectCarInfo.modelLineCode 
  const condition = ["G6ICBY001"].includes(modelLineCode) 
  const interieurOptionCode = the.$store.state.currentSibColorInterieur?.interieurOptionCode
  const condInt = ["TO"].includes(interieurOptionCode)
  
  if (condition && condInt) {
    if (item.optionCode && item.optionCode == 'PS2') {
      return true
    } else {
      the.privateOrderList.forEach(e => {
        if (e.optionCode == 'PS2') {
          e.why = '此装备与当前所选内饰冲突，请选中其他内饰后尝试'
        }
      });
      return false
    }
  }else {
    the.privateOrderList.forEach(e => {
        e.why = ''
    });
    the.$store.commit('setPrivateOrderList', the.privateOrderList)
  }
 
}



// 凌云飞骑、行云羽林/飞骑 里，私人定制页面隐藏PS1
// modelLineCode: "G6ICBY002", 
// modelLineCode: "G6ICBY001"
// modelLineCode: "G6IBBY001"
export const patchesPS1 = (the, item = {}) => {
   
  let {
    selectCarInfo,
    privateOrderList
  } = the.$store.state
  const modelLineCode = selectCarInfo.modelLineCode 
  const condition = ["G6ICBY001", "G6IBBY001", "G6ICBY002",].includes(modelLineCode) 
  let obj = privateOrderList.find((e) => e.optionCode == 'PS1')
  if (condition && obj) {
    const arr = privateOrderList.filter((e) => e.optionCode !== 'PS1')
    the.$store.commit('setPrivateOrderList', arr)
  }
}



// 齐云羽林，选择TT 或 TX时
// 齐云飞骑，选择AW时  "G6ICAY001"
// ['PS1', 'PS3', 'PS8']
export const patchesPS138 = (the, item = {}) => {
   
  let sib = the.$store.state.clickOption?.sibInterieurCode ||  the.$store.state.currentSibColorInterieur?.sibInterieurCode
  let modelLineCode =the.selectCarInfo.modelLineCode
  const clickOption = the.$store.state.otherClickOption // 内饰中进行三选一后存储的选中值
  let G6IBAY001 = 'G6IBAY001'.includes(modelLineCode) && '"N5D-TX,N5D-TT"'.includes(sib)
  let G6ICAY001 = 'G6ICAY001'.includes(modelLineCode) && "N5D-AW".includes(sib)
  console.log(G6IBAY001, G6ICAY001);
  if ((G6IBAY001 || G6ICAY001) && clickOption.optionCode) {
    
    if (item.optionCode && ['PS1', 'PS3', 'PS8'].includes(item.optionCode)) {
      the.visible = true
      the.popupIntro = "当前所选配置若要取消选中，请先修改您选择的面料"
      return true
    } else {
      if (clickOption.optionCode == 'WTY') {
        let i = the.currentOptionsList.findIndex(e => "PS1".includes(e.optionCode))
        if (i == -1) {
          the.$store.commit('setCurrentOptionsList', [clickOption])
        } else {
          the.currentOptionsList[i] = clickOption
        }
      } else {
        let i = the.currentOptionsList.findIndex(e => "PS1,PS3,PS8".includes(e.optionCode))
        if (i == -1) {
          the.$store.commit('setCurrentOptionsList', [clickOption])
        } else {
          the.currentOptionsList[i] = clickOption
        }
      }
      the.$store.commit('setShowOptionPopup', false)
    }
  }
}





export const defPatchesWA3= (the, storage = {}) => {
   
  let {
    selectCarInfo,
    allPrivateOrderList,
    currentOptionsList,
    $store
  } = the
  const optionCode = $store.state.otherClickOption.optionCode == 'WA3' // 内饰中进行座椅六选一后存储的选中值
  const condition = ['G6IBCY001', 'G6ICCY001'].includes(selectCarInfo.modelLineCode)
  if (condition && optionCode) {
    const obj = currentOptionsList.find((e) => e.optionCode == optionCode)
    if (!obj) {
      $store.commit('setCurrentOptionsList', [
        ...currentOptionsList,
        $store.state.otherClickOption
      ])
    }
    the.$store.commit('setShowOptionPopup', false)
  }
}

// modelLineCode: "G6IBCY001"
// modelLineId: "f8f2838a-fb92-4c05-897a-edcbfe50c6dd"
// modelLineName: "Audi Q6 40 TFSI quattro Roadjet 观云型 羽林套装"
// sibInterieurCode: "N5W-MP"
export const handelWA3= (the, item = {}, indexBox = '') => {
   
  let {
    selectCarInfo, currentSibColorInterieur, privateOrderList, allPrivateOrderList
  } = the.$store.state
  // sibInterieurCode: "N7U-TO"
  const condition = ['G6IBCY001', 'G6ICCY001'].includes(selectCarInfo.modelLineCode)
  console.log("handelWA3handelWA3", currentSibColorInterieur, condition);
  let sibInterieurCode = currentSibColorInterieur.sibInterieurCode
  console.log(sibInterieurCode, condition);
  if ("N7U-TO,N5W-MP".includes(sibInterieurCode) && condition) {
    console.log("此装备与当前所选内饰冲突", item);
    
    if (item.optionCode && item.optionCode == 'WA3') {
      if (indexBox == 2) {
        the.visible = true
        the.popupIntro = "当前所选配置若要取消选中，请先修改您选择的面料"
        return true
      } else{
        return true
      }
      
    } else {
      let obj = allPrivateOrderList.find(e=> e.optionCode == 'WA3')
      obj.why = '此装备与当前所选内饰冲突，请选中其他内饰后尝试'
      the.$store.commit('setPrivateOrderList', [...privateOrderList, obj])
      return false
    }
  } else {
    privateOrderList.forEach(e => {
      if (e.optionCode == 'WA3') {
        e.why = ''
      }
    })
    the.$store.commit('setPrivateOrderList', privateOrderList)
  }
}
