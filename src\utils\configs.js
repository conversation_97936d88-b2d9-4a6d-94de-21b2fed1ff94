/*
 * <AUTHOR> <PERSON>
 * @Date         : 2023-04-17 13:36:11
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-04-23 17:20:51
 * @FilePath     : \src\utils\configs.js
 * @Descripttion : 
 */
import JSEncrypt from 'jsencrypt'

export const JSEncrypt_KEY = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCeCBTaROF+y4HRXLw2vVdnH+mym58fzj7iJm2+jDVBxgAC/y2xUtI8/XIh051f7MKGzzNbmxA4AxHi+0Ea2DZSgWAtxVeNERgRBUUREuLrLH7z8A22Nwqk+mYNJFy8zzD3+jws5ZlP1VG8yOtOfkH+5LCYDj9W5tUemGK2cuLyHQIDAQAB'

//解密方法
export const Encrypt = (string, key) => {
  const encrypt= new JSEncrypt()
  encrypt.setPrivateKey(key)
  return encrypt.encrypt(string)
}

// 解密
export function decrypt(string, key) {
  const decrypt = new JSEncrypt()
  decrypt.setPrivateKey(key)
  return decrypt.decrypt(string)
}

export const MANAGER_ON_OFF = {
  LONG_TEST_DRIVE: false
}