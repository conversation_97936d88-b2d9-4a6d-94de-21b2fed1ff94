<!-- 页面加载 -->
<template>
	<div>
        <div class="network" v-if="initTimeout">
            <div>
                <img :src="'https://audi-oss.saic-audi.mobi/audicc/app/favoritecar/v6/network_reload.png'" />
            </div>
            <div class="content">
                <div class="no">暂无网络</div>
                <div class="desp">请查看是否开启网络</div>
                <div class="primary" @click="reload()">刷新重试</div>
            </div>
        </div>
	</div>
</template>

<script>
    import { mapState } from "vuex"
    import { networkToast } from "../utils/timeout"
    
	export default {
		name: 'network',
		data() {
			return {
				
			}
		},
        computed: {
			...mapState({
				initTimeout: (state) => state.initTimeout
			}),
		},
		watch: {
			
		},
		mounted() {
			
		},
		methods: {
            reload(){
                networkToast()
                this.$store.commit("setInitTimeout", false);
                this.$emit("reload");
            }
		}
	}
</script>

<style lang="less" scoped>

    .network {
        padding-top: 20vh;
        background: #fff;
        position: absolute;
        top: 0px;
        bottom: 0px;
        right: 0px;
        z-index: 1999;
        width: 100%;

        img {
            width: 100%;
        }

        .content {
            text-align: center;
            line-height: 20px;

            .no {
                font-size: 16px;
                color: #000;
                font-family: "AudiTypeGB-WideBold";
                margin-top: 10px;
            }

            .desp {
                font-size: 14px;
                color: #333333;
                font-family: "AudiTypeGB-Normal";
                margin-top: 10px;
            }


        }
    }
    
    .van-button--primary {
        background-color: transparent;
        border: 1px solid #000;
        font-family: "AudiTypeGB-Normal";
        color: #000;
        margin-top: 30px;
        width: 167px;
        height: 56px;
    }

    .primary {
            position: relative;
            display: inline-block;
            box-sizing: border-box;
            height: 44px;
            margin: 0;
            padding: 0;
            font-size: 14px;
            line-height: 1.2;
            text-align: center;
            border-radius: 2px;
            cursor: pointer;
            -webkit-transition: opacity .2s;
            transition: opacity .2s;
            background-color: transparent;
            border: 1px solid #000;
            font-family: "AudiTypeGB-Normal";
            color: #000;
            margin-top: 30px;
            width: 167px;
            height: 56px;
            line-height: 56px;
        }



::v-deep .van-toast {
    background: #000 !important;
}
</style>
