<template>
  <div>
    <navigation :back-type="routeType" v-show="scopeShowHeader">
      <img @click="audiShare" style="width: 22px; height: 22px" src="../../assets/img/icon18.png">
    </navigation>
    <!-- <div
      class="hint-wrapper"
      v-if="!bestRecommendId && invalid != 0"
    >
      已保存至我的购物车，可在我的页面查看，更改将自动保存
    </div> -->
    <div class="hint-wrapper" v-if="invalid == 0">
      已失效
    </div>
    <div class="hint-wrapper" v-if="updateFlag == 1">
      此配置单价格有更新
    </div>


    <div class="container" style="height: 100%;">
      <div class="sc-shadow carimg-wrapper" :style="{
        backgroundImage: `url(${carImgUrl}?t=${time})`
      }" />



      <!-- 我的配置 -->
      <div class="sc-m-top">
        <div class="c-flex-between">
          <div class="bold18">
            我的配置
          </div>
          <div class="c-flex-center small-font" @click="toConfigration"
            v-if="allowModifyConfig && isConfig === 1 && !bestRecommendId">
            更改
            <van-icon name="arrow" />
          </div>
        </div>

        <CarConfigDetail />
      </div>

      <!-- 支付对象 -->
      <div class="pay-wrapper">
        <div class="c-flex-between">
          <span class="small-font"> 支付对象：{{ payObj }} </span>
          <div class="price">
            {{ totalPrice | prefixFormatPrice }}
          </div>
        </div>
        <div class="pay-time">
          <!-- <div v-if="carSeries.seriesCode == '49'" v-html="`预计交付时间：${deliveryTimeByCcid || ''}`" /> -->
        </div>
      </div>

      <!-- 交车方式 -->
      <!-- <div
        class="plan-wrapper"
        v-if="dealerCode"
      >
        <div class="title">
          交车方式
        </div>
        <deliveryPattern />
      </div> -->

      <!-- 付款方案 -->
      <!-- <div class="plan-wrapper">
        <div class="title">
          付款方案
        </div>

        <van-radio-group v-model="payment">
          <van-cell-group>
            <van-cell
              :border="false"
              title="贷款"
              clickable
              @click="payment = '20'"
            >
              <template #right-icon>
                <van-radio name="20">
                  <template #icon="props">
                    <div
                      :class="
                        props.checked ? 'radio-checked' : 'radio-unchecked'
                      "
                    />
                  </template>
                </van-radio>
              </template>
            </van-cell>
            <van-cell
              :border="false"
              title="全款"
              clickable
              @click="payment = '10'"
            >
              <template #right-icon>
                <van-radio name="10">
                  <template #icon="props">
                    <div
                      :class="
                        props.checked ? 'radio-checked' : 'radio-unchecked'
                      "
                    />
                  </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
 -->


      <div v-if="(!dealerCode) || (dealerCode && orgBankList.length > 0)">
        <AudiButton @click="toFinancePage" text="计算器" height="50px" />
        <div class="pay-wrapper">
          <div class="finance-calculator-tips">
            您可在客户权益中点击查看详情，查看相关信贷方案
          </div>
        </div>
      </div>


      <div class="layout-bottom">
        <div class="left">
          <div class="price-wrapper">
            <div v-if="orderStatus === '30' || orderStatus === '301'">
              <span>定金</span>
              <span class="bold">¥{{ toPayPrice | formatPrice }}</span>
            </div>
            <div v-else-if="orderStatus === '31'">
              <span>定金</span>
              <span class="bold">¥{{ payedPrice | formatPrice }}</span>
            </div>
            <div v-else>
              <!-- <template v-if="!bestRecommendId"> -->
              <template>
                <span>意向金</span>
                <span class="bold">¥{{ (earnestPrice / 100) | formatPrice }}</span>
              </template>
              <!-- <template v-if="bestRecommendId">
                <span>定金</span>
                <span class="bold">¥{{ (allPayPrice / 100) | formatPrice }}</span>
              </template> -->
            </div>
          </div>
          <div class="price-wrapper">
            <div>
              <span>总价</span>
              <span class="font14">{{ totalPrice | prefixFormatPrice }}</span>
            </div>
          </div>
        </div>

        <div class="right align-center" v-if="orderStatus !== '31'">
          <AudiButton @click="addConfig" v-if="showAddConfigBtn" style="margin-right: 10px;" text="加入配置单" color="white"
            height="45px" font-size="16px" />
          <!-- 置灰 -->
          <div v-if="showBox || invalid == 0 || (updateFlag == 1 && updatePrice === '1')" style="
            font-size: 16px;
            width: 100%;
            line-height: 45px;
            max-width: 70%;
            background: #E5E5E5;
            color: #FFFFFF;
            line-height: 45px;
            text-align: center;">
            去支付
          </div>
          <AudiButton v-else @click="toOrderDetail" style="max-width: 70%;" :text="mainBtnText" color="black"
            height="45px" font-size="16px" />
        </div>
      </div>
    </div>
    <canvasCard ref="canvasImg" @getCanvasImg="getCanvasImg" :imgurl="carImgUrl"
      :title="carModelName +&quot; 配置单 &quot;" />
    <model :modalshow.sync="modalshow" @update:modalshow="submit" title="在App内打开" confirm-text="复制链接"
      content="点击下方复制链接，前往浏览器打开" />
    <van-dialog v-model="orgShow" :show-cancel-button="false" :show-confirm-button="false">
      <div class="dialog-title">
        你已经在{{ orgName }}建卡，请前往{{ orgName }}选择车辆。
      </div>
      <div class="D-buttons" @click="toDealerDetail">
        确认前往
      </div>
    </van-dialog>
    <van-dialog v-model="visible" title="当前配置车型已下架,配置单失效">
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div class="goB" @click="handleBack">
          返回
        </div>
        <div class="okB" @click="handleReCC">
          重新配置
        </div>
      </div>
    </van-dialog>

    <van-dialog v-model="visible" title="当前配置车型已下架,配置单失效">
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div class="goB"  @click="handleBack">
          返回
        </div>
        <div class="okB" @click="handleReCC">
          重新配置
        </div>
      </div>
    </van-dialog>

    <van-dialog v-model="showPDE" title="未选择DCC自适应动态悬架，预计交付周期大于16周">
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div class="goB" @click="handlePDE(1)">
          继续下订
        </div>
        <div class="okB" @click="handlePDE(0)">
          修改配置
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  RadioGroup,
  Radio,
  Cell,
  CellGroup,
  Toast
} from 'vant'
import { mapState } from 'vuex'
import {
  getCarConfig,
  getProductDetail,
  addCarShoppingCart,
  getShareUrl,
  canUpdateCCConfig,
  getOrgBankList,
  judgeReservationClient,
  getUserInfo,
  getAudiCarUrl,
  getNgaPrice,
  checkStockCar
} from '@/api/api'
import {
  getDeliveryTimeByCcid
} from '@/configratorApi/index'
import CarConfigDetail from '@/components/car-config-detail.vue'
import model from '@/components/model.vue'
import confiUrl from '@/config/url'
import navigation from '@/components/navigation.vue'
import {
  callNative, getUrlParamObj, getMonthWeek,
} from '@/utils'
import canvasCard from '@/components/canvas-card.vue'
import deliveryPattern from '@/components/delivery-pattern.vue'
import AudiButton from '../../components/audi-button.vue'
import debounce from './debounce.js'

const { env } = getUrlParamObj()

const baseOssHost = confiUrl.BaseOssHost
console.log('baseOssHost', baseOssHost)
Vue.use(Cell)
Vue.use(CellGroup)
Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Toast)

/**
   * 从业务上来分, 报价单页面可能会在各个地方去独立使用,
   * 所以要求进入此页面必须在url中携带 ccid 字段
   */
export default {
  components: {
    AudiButton,
    CarConfigDetail,
    model,
    navigation,
    deliveryPattern,
    canvasCard
  },
  data() {
    return {
      measure: '',
      updatePrice: '1',
      invalid: {},
      updateFlag: {},
      packageItem: '',
      updateVis: false,
      showPDE: false,
      visible: false,
      showBox: false,
      payObj: '上汽奥迪',
      allPayPrice: 0,
      earnestPrice: 0, // 小订金额
      payTime: '4周',
      showCarConfigDetail: true, // 是否显示车辆配置项
      mainBtnText: '去支付',
      orderStatus: '',
      modalshow: false,
      routeType: 'h5',
      time: Date.now(),
      scopeShowHeader: true,
      isConfig: 0, // 是否可以继续更改配置 0可以1不能
      dealerCode: '',
      orgBankList: [],
      orgShow: false,
      orgName: '',
      orgCode: '',
      deliveryTimeByCcid: '',
      prodId: '',
      isStockCar: false,
      carinfo: {}
    }
  },
  computed: {
    ...mapState({
      dealerMainImg: (state) => {
        const imgurl = state.dealerInfo?.imageUrl
        return imgurl ? `${baseOssHost}${imgurl}` : ''
      },

      standardConfigData: 'standardConfigData',
      dealerName: (state) => state.dealerInfo.dealerName,
      dealerDistance: (state) => state.dealerInfo.distance,
      dealerAddress: (state) => state.dealerInfo.dealerAdrress,
      carSeries: (state) => state.carSeries,
      carImgUrl: (state) => (state.carDetail.configDetail?.carModel?.imageUrl ? `${baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl}`
        : ''), // 主图
      totalPrice: (state) => state.carDetail.configDetail?.totalPrice,
      carHeadImageUrl: (state) => (state.carDetail.configDetail?.carModel?.headImageUrl ? baseOssHost + state.carDetail.configDetail?.carModel
        ?.headImageUrl : ''),
      carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn, // 车辆名称
      deliveryTime: (state) => state.carDetail.configDetail?.carModel?.deliveryTime, // 车辆名称
      bestRecommendId: (state) => state.carDetail.bestRecommendId, // 是否是推荐车型
      isReserveCard: 'isReserveCard',
      bestRecommandDealerCode: 'bestRecommandDealerCode',
      env: 'env',
      ccid: 'ccid',
      skuid: 'skuid'
    }),
    payment: {
      get() {
        return this.$store.state.payment
      },
      set(value) {
        this.$store.commit('updatePayment', value)
      }
    },

    showAddConfigBtn() {
      const { orderStatus } = this.$route.query
      // 报价单页显示的按钮, orderStatus 代表从详情页跳转过来
      const boo = this.$route.path === '/quotation' && !orderStatus && !this.bestRecommendId && !this.isStockCar
      console.log('showAddConfigBtn>>', boo)
      return boo
    },

    allowModifyConfig() {
      const { orderStatus } = this.$route.query
      console.log('更改 allowModifyConfig', orderStatus)
      return this.env !== 'minip' && orderStatus !== '31' && orderStatus !== '301' && orderStatus !== '00'
    },

    // 定金要付的金额
    toPayPrice() {
      return (this.allPayPrice - this.earnestPrice) / 100
    },
    // 定金的总金额
    payedPrice() {
      return this.allPayPrice / 100
    }

  },

  created() {
    /**
       * 跳转到此页面如果携带orderStatus ,那么跳转到ccpro配置页要带上这个信息
       * 1. 从未支付的订单页面跳转过来
       * 2. 从ccpro 选车跳转过来
       */
    const query = this.$route.query
    if (query && query.dealerCode) {
      this.dealerCode = query.dealerCode
      this.getOrgBankList()
    }
    const {
      ccid, skuid, orderStatus, invalid
    } = this.$route.query
    if (!ccid || !skuid) {
      console.error(`此页面依赖 ccid : ${ccid} and skuid: ${skuid}`)
    }
    // 只有已支付意向金,未支付定金的状态下显示[去支付定金]
    if (orderStatus === '30' || orderStatus === '301') {
      this.mainBtnText = '去支付'
    }
    this.orderStatus = orderStatus
    this.$store.commit('saveSkuId', skuid)
    this.$store.commit('updateCcid', ccid)

    if (env === 'minip') {
      this.scopeShowHeader = false
    }
  },

  mounted() {
    this.$store.dispatch('getDealerInfo')
    const { ccid, skuid } = this.$route.query
    this.routeType = this.$route.query.routeType || 'h5'
    console.log(this.routeType)
    this.getCarDetailByCcid(ccid, skuid)
    // this.getProductDetail(skuid)
    this.getDeliveryTimeByCcid(ccid, this.dealerCode)
    this.canUpdateCCConfig()

    console.log('selectCarInfo >>>', this.$store.state.selectCarInfo)


    this.getCarConfigfn()
  },

  methods: {
    handlePDE(e = '') {
      this.showPDE = false
      if(this.carSeries.seriesCode != 'G6') return
      if (e) {
        this.toOrderDetail(1)
      } else {
        this.$storage.setPlus('semi-definite', this.measure ? '个性定制' : '私人高定')
        const { ccid } = this.$route.query
        this.$router.push({
          path: '/configration',
          query: {
            idx: 2,
            tabIndex: 0,
            from: 'quotation',
            ccid: ccid || ''
          }
        })
      }
    },
    handleReCC() {
      this.visible = false
      const seriesCode = this.carSeries.seriesCode
      console.log('车系：seriesCode 49 G4 G6 F0', seriesCode)
      // 49 G4 G6 F0
      let obj = {}
      if (seriesCode == 49) {
        obj = {
          path: '/configration',
          query: {
            idx: 0
          }
        }
      }
      if (seriesCode == 'G4') {
        obj = {
          path: '/configration',
          query: {
            idx: 1,
            tabIndex: 0,
            definedCar: 0
          }
        }
      }

      if (seriesCode == 'G6') {
        obj = {
          path: '/configration',
          query: {
            idx: 2,
            tabIndex: 0,
            definedCar: 1
          }
        }
      }
      if (seriesCode == 'F0') {
        obj = {
          path: '/configration',
          query: {
            idx: "e0172bf8-d547-4ad7-adf7-1292f53ea0df",
            tabIndex: 0,
            definedCar: 1
          }
        }
      }
      this.$router.push(obj)
    },
    handleBack() {
      this.visible = false
      this.updateVis = false
    },
    handleMoney() {
      this.updateVis = false
      // 点击确认，则自动将有更新的价格
      this.updatePrice = ''
    },
    async getCarConfigfn() {
      const { ccid, skuid } = this.$route.query
      const { data } = await getCarConfig({ ccid: ccid })
      console.log('car info >>>>>>>>>>>>>.', data.data)
      if (+data?.data?.valid != 1) {
        this.invalid = +data?.data?.valid
        this.visible = this.invalid == 0
      }

      if (+data?.data?.updateFlag == 1 && data && data?.data?.updateContent) {
        this.updateFlag = +data?.data?.updateFlag
        this.updateVis = this.updateFlag == 1
        this.packageItem = data?.data?.updateContent
      }

      const carDetail = data.data.configDetail
      this.carinfo =  data?.data?.configDetail?.carModel || ''
      console.log('carDetail:', carDetail)
      console.log(carDetail?.carModel?.modellineId)
      console.log(1111, carDetail?.insideColor?.colorCode)
      const heibaiche = [
        '9bb9a4e5-096b-4f3a-8660-96feb126b271',
        '3bcc54ad-28e9-4499-98f3-2a8abf8fdcda'
      ].includes(carDetail?.carModel?.modellineId) && this.$route.query?.shoppingCartId

      this.showBox = [
        '0d035558-7a87-4267-b0c4-fd7f11936956',
        '3d722843-7016-4a9c-b339-4aeb5b16e13a'
      ].includes(carDetail?.carModel?.modellineId) && [
        'AJ-N4X',
        'DS-N4X',
        'EO-N4X'
      ].includes(carDetail?.insideColor?.colorCode) || heibaiche
      console.log('this.showBox <<', this.showBox)
    },
    async getDeliveryTimeByCcid(ccid, dealerCode) {
      const { data } = await getDeliveryTimeByCcid(ccid, dealerCode)
      if (data.code === '00') {
        const strarr = data.data.estimateDate.split('-')
        const week = getMonthWeek(Number(strarr[0]), Number(strarr[1]), Number(strarr[2]))
        this.deliveryTimeByCcid = `${strarr[0]}年${Number(strarr[1])}月第${week}周左右交付`
      }
    },
    async getCarDetailByCcid(ccid, skuid) {
      const { data } = await this.$store.dispatch('getCarDetailByCcid', { ccid })
      const { totalPrice, depositType, configDetail: { carModel: { modelCode }, carSeries: { seriesCode } } } = data || {}
      if ([modelCode, seriesCode, depositType, skuid].every((i) => i)) {
        this.handleGetNgaPrice(modelCode, seriesCode, depositType, skuid, totalPrice)
      }
      console.log('$store.state.carDetail:', data)
      if (ccid) {
        const { data: { data } } = await checkStockCar({ ccid }) || false
        this.isStockCar = data
      }

      this.measure = data.measureId
    },
    async getOrgBankList() {
      const { data } = await getOrgBankList({ dealerCode: this.dealerCode })
      this.orgBankList = data.data
    },
    async handleGetNgaPrice(modelCode, seriesCode, depositType, skuid, totalPrice) {
      const { orderStatus } = this
      const { data: { data: { prodId } } } = await getProductDetail({ id: skuid }) || {}
      prodId && (this.prodId = prodId)
      const { data: { data: { prodNgaDepositPrice, prodNgaDownPayPrice } } } = await getNgaPrice({
        modelCode, seriesCode, depositType, prodId
      })
      // !!! 这里的逻辑 ===> 续接以前各类金额显示
      if (prodNgaDepositPrice) {
        this.earnestPrice = prodNgaDepositPrice
      }
      if (prodNgaDownPayPrice) {
        if (['30', '31', '301'].includes(orderStatus)) {
          this.allPayPrice = prodNgaDownPayPrice
        }
        // 小定 toPayPrice   payedPrice 大定  意向金 earnestPrice
      }
    },
    // async getProductDetail(skuid) {
    //   const { data } = await getProductDetail({ id: skuid })
    //   // this.allPayPrice = data.data.defaultProdSku.confirmOrderAmount
    //   // this.earnestPrice = data.data.defaultProdSku.reserveOrderAmount
    // },

    toEquity() {
      this.$router.push({
        path: '/order/rights-detail'
      })
    },
    toFinancePage() {
      const selectCarInfo = this.$storage.getPlus('selectCarInfo') || {}
      this.$router.push({
        path: '/configration/financial-calculator',
        query: {
          ccid: this.ccid,
          skuid: this.skuid,
          dealerCode: this.dealerCode,
          from: 'quotation',
          seriesCode: selectCarInfo.seriesCode
        }
      })
    },
    toConfigration() {
      // 埋点
      this.$sensors.track('changeMyConfiguration', {
        page_name: '报价单页面'
      })

      const {
        orderStatus, orderId, shoppingCartId, measureId
      } = this.$route.query
      // this.$store.commit('setSelectCarIfo',{customSeriesId:this.carSeries.customSeriesId})
      const b = this.carSeries.seriesCode == 'G6' || this.carSeries.seriesCode == 'g6'

      this.$storage.setPlus('semi-definite', this.measure ? '个性定制' : '私人高定')

      if (measureId || this.measure) {
        // 默认跳转
        this.$router.push({
          path: '/configration',
          query: {
            ccid: this.$route.query?.ccid || '',
            orderStatus,
            orderId,
            idx: this.carSeries.seriesCode === '49' ? 0 : b ? 2 : 1,
            tabIndex: 0,
            from: 'quotation',
            shoppingCartId: shoppingCartId || '',
            definedCar: 0
          }
        })
        return
      }
      // 默认跳转
      this.$router.push({
        path: '/configration',
        query: {
          ccid: this.$route.query?.ccid || '',
          orderStatus,
          orderId,
          idx: this.carSeries.seriesCode === '49' ? 0 : b ? 2 : 1,
          tabIndex: 0,
          from: 'quotation',
          shoppingCartId: shoppingCartId || '',
          definedCar: this.measure ? 0 : 1
        }
      })
    },

    // 查看某个ccid是否还能更新配置 0可以1不能
    async canUpdateCCConfig() {
      const param = { ccid: this.$route.query.ccid }
      const res = await canUpdateCCConfig(param)
      this.isConfig = res.data.data
      console.log('查看某个ccid是否还能更新配置 isConfig', this.isConfig)
    },

    async toOrderDetail(e = '') {
      console.log(this.carinfo);
      const carDetail = this.$store.state.carDetail.configDetail
      if(!this.carinfo) return
      let modelNameCn = this.carinfo?.modelNameCn?.includes('观云')
      if (modelNameCn && this.carSeries.seriesCode === 'G6' && !e && !carDetail.optionList.find(element => element.optionCode && element.optionCode == 'PDE')) {
        this.showPDE = true
        return
      }

      const condition = this.showBox
      // 4. 下架星耀锦衣逐日和星耀机甲逐日里面的N4X面料
      // 灰翎-冰痕棕、橙翎-冰痕灰、红玲-冰痕黑
      // 5. 606个逐日的配置单，进入报价单后，支付按钮置灰，不可点击
      if (condition) {
        console.log(this.$store.state.carDetail)
        return
      }

      if (this.env === 'minip') {
        this.modalshow = true
        return
      }

      if (this.bestRecommandDealerCode.length) {
        console.log('虎头车绑定代理商 ', this.bestRecommandDealerCode)
        const { data } = await getUserInfo()
        const mobile = data.data?.userInfo?.mobile
        if (!mobile) {
          return console.error('获取mobile失败')
        }
        const res = await judgeReservationClient({
          custMobile: mobile,
          seriesCode: this.carSeries.seriesCode
        })
        console.log('创始卡数据', res)
        if (res.data.data && res.data.data.orgCode !== this.bestRecommandDealerCode) {
          this.orgShow = true
          this.orgName = res.data.data.orgName
          this.orgCode = res.data.data.orgCode
          return
        }
      }
      const {
        ccid,
        skuid,
        orderStatus,
        orderId,
        dealerCode,
        shoppingCartId
      } = this.$route.query
      const { prodId } = this
      let param = {}
      // 有orderStatus 说明此页面是从订单详情页返回回来的
      // 30: 已支付意向金,未支付定金 ,00: 待支付意向金
      // 这串逻辑已经迁移到车型页
      if (orderStatus === '30' || orderStatus === '00') {
        param = {
          path: '/order/money-detail',
          query: { orderId }
        }
      } else {
        const isBigOrder = this.$route.query.isBigOrder || ''
        param = {
          path: '/order/detail',
          query: {
            ccid, skuid, shoppingCartId, dealerCode, isBigOrder, prodId
          }
        }
      }
      this.scopeShowHeader = false
      this.$router.push(param)
    },
    submit(isSubmit) {
      if (isSubmit) {
        // 复制到粘贴板
        const copyResult = this.InsertShearPlate()
        this.modalshow = false
        if (copyResult) {
          Toast({
            type: 'fail',
            message: '已复制到剪贴板',
            icon: require('../../assets/img/success.png')
          })
        } else {
          Toast({
            type: 'fail',
            message: '复制失败',
            icon: require('../../assets/img/error.png')
          })
        }
      }
    },
    InsertShearPlate() {
      // 复制到粘贴板
      const text = 'https://app.saic-audi.mobi/index.html#/?channel=miniapp_VehiclePurchase'
      const copyString = text
      // 创建input标签存放需要复制的文字
      const myInput = document.createElement('input')
      // 把文字放进input中，供复制
      myInput.value = copyString
      document.body.appendChild(myInput)
      // 选中创建的input
      myInput.select()
      // 执行复制方法， 该方法返回bool类型的结果，告诉我们是否复制成功
      const copyResult = document.execCommand('copy')
      // 操作中完成后 从Dom中删除创建的input
      document.body.removeChild(myInput)
      return copyResult
    },
    audiShare() {
      this.time = Date.now() + 10
      this.$refs.canvasImg.getCanvasImg()
    },

    async getCanvasImg(e) {
      console.log(`图片+${e.fileStorageId}`, baseOssHost + e.fileUrl)
      const subTitle = this.standardConfigData.map((i) => i.name)
      const res = await getShareUrl()
      const path = `${res.data.data.configValue}#/carConfig?ccid=${this.$route.query.ccid}`
      console.log(baseOssHost + e.fileUrl)
      callNative('audiShare', {
        type: 'carConfig',
        path: path, // `${window.location.origin}/order/index.html#/car-config-other?ccid=${this.$route.query.ccid}`,
        imageUrl: baseOssHost + e.fileUrl,
        ccid: this.$route.query.ccid,
        title: `欢迎围观我的${this.carModelName}`,
        subTitle: `我的配置${subTitle.join('|')}`
      }).then((data) => {
        console.log(data, '分享回调')
      })
    },
    addConfig() {
      debounce(() => {
        this.addConfigFn()
      }, 1000);
    },
    // 添加配置单到购物车列表
    async addConfigFn() {
      this.$store.commit("showLoading");
      const { ccid, skuid, inviteBuyCarCode } = this.$store.state
      const shoppingCartId = this.$route.query?.shoppingCartId || ''
      const entryPoint = this.$storage.getPlus('entryPoint')
      const { data } = await addCarShoppingCart({
        ccid, skuid, invitationCode: inviteBuyCarCode, shoppingCartId, entryPoint
      })
      if (data.code === '00') {
        this.$store.commit("hideLoading");
        Toast({
          type: 'success',
          message: data.message,
          icon: require('@/assets/img/success.png')
        })
      }
    },
    // 跳转到代理商详情
    async toDealerDetail() {
      const { data } = await getAudiCarUrl()
      const url = `${data.data.configValue}dealerDetail?dealerCode=${this.orgCode}`
      callNative('audiOpen', { path: url })
    }
  }
}
</script>

<style scoped lang="less">
@import "../../assets/style/common.less";
@import url("../../assets/style/dialog.less");
@import url("../../assets/style/buttons.less");
/** 公共样式 */

@leftWith: 18vw;
@rightMargin: 15px;
@topMargin: 20px;
@fontColor: #999;

.goB {
  border: 1px solid;
  padding: 15px 0;
  width: 50%
}

.okB {
  background: black;
  color: white;
  padding: 15px 0;
  width: 50%;
  margin-left: 2px;
}

//向上的边距
.sc-m-top {
  margin-top: @topMargin;
}

.sc-left {
  width: @leftWith;
  margin-right: @rightMargin;
}

.sc-nowrap {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
}

.sc-height {
  .c-flex-between;
  flex-direction: column;
  flex: 1;
  height: @leftWith;
  padding: 12px 0;
  box-sizing: border-box;
}

//box阴影
.sc-shadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
}

// 下划线
.sc-u-line {
  border-bottom: 1px solid #e5e5e5;
}

//
.bold18 {
  .c-font18;
  .c-bold;
}

.small-font {
  .c-font12;
  color: #999;
}

.container {
  .sc-m-top;
  position: relative;
  padding: 0 18px;
  padding-bottom: 100px;
}

.hint-wrapper {
  background-color: #f2f2f2;
  font-size: 14px;
  padding: 10px 18px;
}

.carimg-wrapper {
  min-height: 180px;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center;
}

/** 其他样式 */
//代理商
.proxy-wrapper {
  .sc-shadow;
  .c-flex-center;

  position: relative;
  margin-top: 15px;

  >.left {
    .c-flex-center;
    width: 30%;
    margin-right: 15px;
  }

  >.right {
    position: relative;
    height: 110px;
    flex: 1;

    .title {
      margin: 10px 0;
    }

    .bottom {
      .small-font;
      position: absolute;
      bottom: 4px;
    }
  }
}

// 支付对象
.pay-wrapper {
  justify-content: space-between;
  margin-top: 20px;

  .price {
    font-weight: bold;
  }

  .pay-time {
    .small-font;

    text-align: right;
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    justify-content: flex-end;

    // > div {
    // word-break: keep-all;
    // }
  }

  .finance-calculator-tips {
    .small-font;

    text-align: center;
    margin-top: 10px;
  }
}

.plan-wrapper {
  .sc-shadow;
  padding-top: 20px;
  margin: 10px 0;

  >.title {
    font-weight: bold;
    margin-bottom: 10px;
    margin-left: 16px;
  }

  .radio-checked {
    height: 20px;
    width: 20px;
    border: 1px solid #ccc;
    border-radius: 50%;
    background-color: #000;
    background-clip: content-box;
    padding: 4px;
    box-sizing: border-box;
  }

  .radio-unchecked {
    height: 20px;
    width: 20px;
    border: 1px solid #ccc;
    border-radius: 50%;
    box-sizing: border-box;
  }
}

.layout-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 10px 17px 20px 17px;
  box-sizing: border-box;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);

  .left {
    font-size: 12px;
    color: #999999;
    padding: 3px 0;
    display: flex;
    flex-flow: column;
    justify-content: space-between;

    .price-wrapper {
      >div {
        display: flex;

        >span:first-child {
          width: 55px;
        }
      }
    }

    .bold {
      font-weight: bold;
      font-size: 16px;
      color: #000;
    }

    .font14 {
      font-size: 14px;
      color: #000;
    }
  }

  .right {
    width: 60%;
  }
}

.align-center {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.dialog-title {
  .c-font16;
  .c-bold;
  text-align: left;
  margin-bottom: 20px;
}

/deep/.van-dialog {
  overflow-y: auto;
  max-height: 80%;
  padding: 15px 16px 16px;
  top: 52% !important;
  z-index: 33336;

  h3 {
    margin: 0;
  }

  .item {
    color: #000;
    font-size: 14px;
    text-align: left;
    margin-bottom: 24px;

    .title {
      line-height: 24px;
    }

    .itemCotent {
      display: flex;
      line-height: 17px;

      div {
        margin-top: 8px;
      }
    }
  }
}
</style>
