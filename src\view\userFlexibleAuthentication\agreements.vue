<template>
  <div class="createAgreement">
    <!-- <div
      class="nav-box"
      :style="{ 'padding-top': statusBarHeight + 'px', 'height':statusBarHeight + navigationBarHeight+'px'}"
    >
      <div
        class="nav-content"
        :style="{'height': + navigationBarHeight+'px'}"
      >
        <div
          @click="routeBack"
          class="nav-back-box"
        >
          <img
            class="nav-back"
            src="../../assets/img/back_white.png"
          >
        </div>
        <div class="nav-title">
          {{ $store.state.title }}
        </div>
      </div>
    </div> -->
    <div
      class="createAgreement-connent"
    >
      <div class="createAgreement-text">
        <p style="margin-top:22px">
          <span style="width:26px;display:inline-block" />经购车客户授权同意，上海上汽大众汽车销售有限公司及其授权代理商收集购车客户的
身份证扫描件及其他证明材料，仅用于客户是否享受关键用户专属购车权益资质的审核，相
关材料将在审核完成后即刻彻底删除，不作留存
        </p>
        <p style="margin-top: 16px;">
          其他证明材料上传明细：详见资料上传页面。
        </p>
      </div>
      <div>
        <div
          class="createAgreement-sureBtn"
          @click="submit"
        >
          我已知悉并同意
        </div>
        <!--  <div class="createAgreement-sureBtns" @click="submit" v-show="times > 0">
          还需阅读{{ times }}秒
        </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'

import {
  Toast,
  Checkbox
} from 'vant'
import { callNative } from '@/utils'

Vue.use(Checkbox)
  .use(Toast)
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Agreements',
  components: {},
  data() {
    return {
      times: 5,
      statusBarHeight: 0,
      navigationBarHeight: 50,
      timeInterval: ''
    }
  },
  mounted() {
    this.$nextTick(async () => {
      await this.getNavigation()
    })
    // this.startTiming();
  },
  watch: {},
  methods: {
    async getNavigation() {
      const data = await callNative('navigationBarHeight', {})
      this.statusBarHeight = data.statusBarHeight
      this.navigationBarHeight = data.navigationBarHeight
    },

    // 点击返回键
    async appGoBack() {
      localStorage.setItem('userFlexibleAuthenticationAgreement', 1)
      const data = await callNative('prepage', { times: 1 })
    },

    submit() {
      this.appGoBack()

      // if (this.times > 0) {
      //   Toast({
      //     type: '',
      //     message: `还需阅读${this.times}秒`
      //   })
      // } else {
      //   this.appGoBack()
      // }
    },

    startTiming() {
      this.timeInterval = setInterval(() => {
        // console.log(this.duration);

        if (this.times > 0) {
          this.times -= 1
        } else {
          clearInterval(this.timeInterval)
          this.duration = 0
        }
      }, 1000)
    }
  }
}
</script>

<style lang="less" scoped>
  .createAgreement {
    padding: 0 16px;
    height:100%;
  }
  .createAgreement-connent {
    display: flex;
    flex-direction: column;
    height:100% ;


    .createAgreement-text {
      flex: 1;
      > p {
        font-size: 14px;
        color: #333333;
        line-height: 24px;
        font-family: 'AudiTypeGB-Normal', 'AudiTypeGB';
        margin:0
      }
    }
   div{
      .createAgreement-sureBtn {
      width: 100%;
      height: 50px;
      color: #ffffff;
      margin-bottom: 50px;
      background-color: #000;
      text-align: center;
      line-height: 50px;
      margin-top: 16px;
      font-size:16px
    }
    .createAgreement-sureBtns {
      width: 100%;
      height: 50px;
      color: #ffffff;
      margin-bottom: 18px;
      background-color: #e5e5e5;
      border: 1px solid #ffffff;
      text-align: center;
      line-height: 50px;
      margin-top: 18px;

    }
   }

  }

  .createAgreement-title {
    color: #000000;
    font-size: 14px;
    font-family: Audi-ExtendedBold, WideBold;
  }

  .header {
    position: fixed;
    top: 0;
    display: flex;
    width: 100%;
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
    background: #fff;
    z-index: 12345;

    .content {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 0;

      h3 {
        font-size: 18px;
        font-family: 'Audi-ExtendedBold';
        flex: 1;
        margin: 0;
        width: 100%;
        color: #000000;
      }

      .back {
        width: 20px;
        z-index: 99;
        cursor: pointer;
        // filter: brightness(100);
        // box-shadow: 0 0 2px 0px #fff;
        // transform: rotate(180deg);
        // filter: drop-shadow(0px 0px yellow);
        // -webkit-filter: brightness(100);
        // filter: drop-shadow(0px 0px 0px rgb(219, 16, 16));
      }

      .share {
        width: 34px;
        z-index: 99;
        cursor: pointer;
        // box-shadow: 0 0 2px 0px #fff;
        // transform: rotate(180deg);
        filter: brightness(100);

        // -webkit-filter: drop-shadow(10px 10px 20px rgba(255, 255, 255, 1));
        // filter: drop-shadow(10px 10px 20px rgba(255, 255, 255, 1));
      }
    }
  }
  </style>
