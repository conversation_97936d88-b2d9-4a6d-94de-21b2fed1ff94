<template>
  <div class="_container">
    <div class="_heardImg">
      <!-- <img src="../../assets/img/charging-authentication-img1.png"> -->
      <div
        class="nav-box"
        :style="{ 'padding-top': statusBarHeight + 'px', 'height':statusBarHeight + navigationBarHeight+'px'}"
      >
        <div
          class="nav-content"
          :style="{'height': + navigationBarHeight+'px'}"
        >
          <div
            @click="routeBack"
            class="nav-back-box"
          >
            <img
              class="nav-back"
              src="../../assets/img/back_black.png"
            >
          </div>
          <!-- <div class="nav-title">
            {{ $store.state.title }}
          </div> -->
        </div>
      </div>
    </div>
    <van-form
      ref="form"
      @failed="failed"
      @submit="confirmVisible = true"
      error-message-align="right"
    >
      <van-field
        v-model="userFullName"
        input-align="right"
        label="姓名"
        placeholder="请输入"
        :readonly="readonly"
      />
      <!-- <van-field
      v-model="name"
      input-align="right"
      label="名"
      placeholder="请输入"
    /> -->
      <div class="line">
        <div class="title">
          性别
        </div>
        <div
          class="btn-change"
          @click="showGender = true"
        >
          <div
            class="change-name"
            v-if="!sex"
            style="color: #999; font-size: 15px"
          >
            {{ "请选择" }}
          </div>
          <div
            class="change-name"
            v-if="sex"
          >
            {{ sex }}
          </div>
          <img
            class="btn-icon"
            src="../../assets/img/icon11.png"
            size="16px"
          >
        </div>
      </div>
      <div v-if="!readonly&&showGender">
        <div
          class="certificate-type"
          @click="onGenderType(101)"
        >
          男
        </div>
        <div
          class="certificate-type"
          @click="onGenderType(102)"
        >
          女
        </div>
      </div>

      <van-field
        v-model="phoneNumber"
        input-align="right"
        maxlength="11"
        type="number"
        label="手机号"
        placeholder="请输入"
        :readonly="readonly"
        ref="phoneNumber"
        :rules="[
          {
            pattern: /^1\d{10}$/,
            message: '请输入正确的身份证号',
          },
        ]"
      />

      <van-field
        v-model="idCard"
        input-align="right"
        label="身份证号"
        placeholder="请输入身份证号"
        ref="idCard"
        :readonly="readonly"
        :rules="[
          {
            pattern: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/,
            message: '请输入正确的身份证号',
          },
        ]"
      />

    <!-- <van-field
      v-model="passportNumber"
      input-align="right"
      label="护照号"
      placeholder="请输入护照号"
    /> -->
    </van-form>
    <div class="line">
      <div class="title">
        准购单办理途径
      </div>
      <div
        class="btn-change"
        @click="showBuyingOrderApproachAddress = true"
      >
        <div
          class="change-name"
          v-if="!purchaseAddress"
          style="color: #999; font-size: 15px"
        >
          {{ "请选择" }}
        </div>
        <div
          class="change-name"
          v-if="purchaseAddress"
        >
          {{ purchaseAddress }}
        </div>
        <img
          class="btn-icon"
          src="../../assets/img/icon11.png"
          size="16px"
        >
      </div>
    </div>
    <div v-if="!readonly&&showBuyingOrderApproachAddress">
      <div
        class="item-wrapper"
        v-for="(item, idx) in purchaseAddressList"
        :key="idx"
        @click="onApproachAddress(item)"
      >
        <div class="certificate-type">
          {{ item.buyingOrderApproachName }}
        </div>
      </div>
    </div>


    <!-- <van-field
      v-model="buyingOrderApproachTime"
      input-align="right"
      label="准购单有效期"
      placeholder="例：2021.12.12"
    /> -->
    <div class="btn-delete-height" />
    <div>
      <div class="bottom_style">
        <div class="btn-delete-wrapper">
          <AudiButton
            @click="onNext"
            :text="'下一步'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
      </div>
    </div>
    <van-overlay :show="isShowMessageId">
      <div class="wrapper">
        <div class="block">
          您已重新提交过资料
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Button,
  ActionSheet,
  Cell,
  Popup,
  Area,
  Toast,
  Checkbox,
  CheckboxGroup,
  Overlay
} from 'vant'
import AudiButton from '@/components/audi-button'
import { getBuyingOrderApproach, getqueryOverseasStudentsInformation,getqueryOverseasStudentsInformationById } from '@/api/api'
import { callNative } from '@/utils'
import storage from '../../utils/storage'

Vue.use(Form)
  .use(Field)
  .use(Button)
  .use(ActionSheet)
  .use(Cell)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Area)
  .use(Overlay)
  .use(Toast)
export default {
  components: {
    AudiButton
  },
  data() {
    return {

      showGender: false,
      sex: '',
      gender: '', // 1男2女
      userFullName: '', // 姓
      name: '', // 名
      phoneNumber: '', // 手机号
      idCard: '', // 身份证号
      passportNumber: '', // 护照号
      purchaseAddress: '', // 准购单途径
      buyingOrderApproachTime: '', //       准购单有效期
      purchaseAddressCode: '', // 准购单途径编码

      purchaseAddressList: [],


      showBuyingOrderApproachAddress: false,
      statusBarHeight: 0,
      navigationBarHeight: 50,
      readonly: false,
      isShowMessageId: false
    }
  },
  created() {
    this.getHeight()
  },
  async mounted() {
    const ismessageId = this.$route.query.messageId
    const params = {
      messageId: ismessageId
    }
    if (ismessageId) {
      getqueryOverseasStudentsInformationById(params).then((res) => {
        if (res.data.data && res.data.data.informationStatus === 2) {
          this.isShowMessageId = false
          this.getqueryOverseasStudentsInformation()
          this.getBuyingOrderApproach()
        } else {
          this.isShowMessageId = true
        }
      })
    } else {
      this.getqueryOverseasStudentsInformation()
      this.getBuyingOrderApproach()
    }
    

  },
  methods: {
    // 获取导航栏高度??
    async getHeight() {
      const data = await callNative('navigationBarHeight', {})
      this.statusBarHeight = data.statusBarHeight
      this.navigationBarHeight = data.navigationBarHeight
    },

    failed(err) {
      console.error('failed', err)
    },


    getqueryOverseasStudentsInformation() {
      getqueryOverseasStudentsInformation().then((res) => {
        const model = storage.get('saveOverseasStudentMessage')
        if(model){
          this.gender = JSON.parse(model).gender
          this.sex = JSON.parse(model).sex
          this.userFullName = JSON.parse(model).userFullName
          // this.name = JSON.parse(model).name
          this.phoneNumber = JSON.parse(model).phoneNumber
          this.idCard = JSON.parse(model).idCard
          // this.passportNumber = JSON.parse(model).passportNumber
          this.purchaseAddress = JSON.parse(model).purchaseAddress
          // this.buyingOrderApproachTime = JSON.parse(model).buyingOrderApproachTime
          this.purchaseAddressCode = JSON.parse(model).purchaseAddressCode
        }else {
        if (res.data.data) {
          this.readonly = false
          this.gender = res.data.data.gender
          this.sex = res.data.data.sex
          this.userFullName = res.data.data.userFullName
          // this.name = res.data.data.name
          this.phoneNumber = res.data.data.phoneNumber
          this.idCard = res.data.data.idCard
          // this.passportNumber = res.data.data.passportNumber
          this.purchaseAddress = res.data.data.purchaseAddress
          // this.buyingOrderApproachTime = res.data.data.buyingOrderApproachTime
          this.purchaseAddressCode = res.data.data.purchaseAddressCode
        } 
         

        }
      })
    },

    async getBuyingOrderApproach() {
      const { data } = await getBuyingOrderApproach({})
      this.purchaseAddressList = data.data
    },
    onGenderType(type) {
      this.showGender = false
      if (type === 101) {
        this.sex = '男'
        this.gender = '1'
      } else if (type === 102) {
        this.sex = '女'
        this.gender = '2'
      }
    },
    onApproachAddress(item) {
      this.showBuyingOrderApproachAddress = false
      this.purchaseAddress = item.buyingOrderApproachName
      this.purchaseAddressCode = item.buyingOrderApproachCode
    },
    testIdcard(val) {
      const regs = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/
      if (val.length <= 0 || !regs.test(val)) {
        return false
      }
      return true
    },
    testMobile(val) {
      const re = /^1\d{10}$/
      if (re.test(val)) {
        return true
      }
      return false
    },
    async onNext() {
      if (!this.userFullName) {
        callNative('toast', { type: 'fail', message: '请填写姓名！' })
        return
      }
      if (!this.sex) {
        callNative('toast', { type: 'fail', message: '请选择性别！' })
        return
      }
      // if (!this.name) {
      //   callNative('toast', { type: 'fail', message: '请填写名！' })
      //   return
      // }
      if (!this.testMobile(this.phoneNumber)) {
        callNative('toast', { type: 'fail', message: '请填写手机号！' })
        return
      }
      if (!this.testIdcard(this.idCard)) {
        callNative('toast', { type: 'fail', message: '请填写身份证！' })
        return
      }
      // if (!this.passportNumber) {
      //   callNative('toast', { type: 'fail', message: '请填写护照号！' })
      //   return
      // }
      if (!this.purchaseAddress) {
        callNative('toast', { type: 'fail', message: '请选择准购单凭证！' })
        return
      }
      // if (!this.buyingOrderApproachTime) {
      //   callNative('toast', { type: 'fail', message: '请填写准购单有效期！' })
      //   return
      // }
      // 调用APP Dialog
      callNative('popup', {
        type: 'alert',
        alertparams: {
          title: '',
          desc: '请仔细核对信息是否填写正确？',
          actions: [
            {
              type: 'fill',
              title: '下一步'
            },
            {
              type: 'stroke',
              title: '取消'
            }
          ]
        }
      }).then((data) => {
        if (data.type === 'fill') {
          // 点击确定
          this.goNext()
        }
      })
    },
    async goNext() {
      const param = {
        gender: this.gender,
        sex: this.sex,
        userFullName: this.userFullName,
        // name: this.name,
        phoneNumber: this.phoneNumber,
        idCard: this.idCard,
        // passportNumber: this.passportNumber,
        purchaseAddress: this.purchaseAddress,
        purchaseAddressCode: this.purchaseAddressCode
        // buyingOrderApproachTime: this.buyingOrderApproachTime
      }
      storage.set('saveOverseasStudentMessage', JSON.stringify(param))

      this.$router.push({
        path: '/dutyfreecar/upload-certificate',
        query: {}
      })
    },
    async routeBack() {
      const data = await callNative('prepage', { times: 1 })
    }
  }
}
</script>

<style lang='less' scoped>
::v-deep .van-cell {
  padding: 16px;
}

::v-deep .van-cell::after {
  border-bottom: 1px #e5e5e5 solid;
}

::v-deep .van-button {
  border-radius: 0;
}

::v-deep .van-field__label {
  font-size: 16px;
  color: #000;
  width: fit-content;
}
::v-deep .van-field {
  .van-field__body {
    textarea {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }

    input {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }
  }
}

._heardImg {
  height:300px;
  background-image:url('../../assets/img/charging-authentication-img1.png');
  background-size:100% 100%;
  background-repeat: no-repeat;
  .nav-box {
    display: flex;
    width: 100%;
    text-align: center;
    margin-bottom:131px;
    .nav-content {
      display: flex;
      align-items: center;
      position: relative;
      width: 100vw;
    }

    .nav-back-box {
      width: 10vw;
      position: absolute;
      left: 0;
      bottom: 0;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1025;
    }
    .nav-back {
      width: 20px;
      height: 20px;
      margin-left: 10px;
    }
    .nav-title {
      font-size: 18px;
      font-weight: 500;
      line-height: 20px;
      width: 100vw;
      position: absolute;
      bottom: 0;
      overflow: hidden;
      text-align: center;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #000000;
    }
  }
}

._content {
  width: 100%;
  padding: 8px;
}


.line {
  display: flex;
  align-items: center;
  margin: 16px 16px 0;

  padding-bottom: 16px;
  border-bottom: 1px solid #f2f2f2;
  white-space: nowrap;

  .title {
    font-size: 16px;
    color: #000;
    // margin: 5px 5px;
    white-space: nowrap;
  }
  .btn-change {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .change-name {
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 100%;
      font-size: 14px;
      color: #333;
    }
    .btn-icon {
      width: 20px;
      height: 20px;
      padding-left: 8px;
    }
  }
}
.btn-delete-height {
  height: 80px;
}
.btn-delete-wrapper {
  margin: 16px;
}
.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: absolute;
  bottom: 0px;
  // padding: 16px;
  left: 0px;
}

.certificate-type {
  display: flex;
  align-items: center;
  background: #f2f2f2;
  padding: 14px;
  border-bottom: 1px solid #e5e5e5;
  font-size: 14px;
  color: #333;
}

.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .block {
    width: 90%;
    height: 120px;
    line-height:120px;
    text-align: center;
    background-color: #fff;
  }
</style>
