<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-05-16 09:42:12
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-10-26 11:00:17
 * @FilePath     : \src\view\limited-number\rule-note\index.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['limited-number-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
    />
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <div class="limited-number-wrapper">
        <div
          class="limited-number-text"
          style="padding-top:20px"
        >
          <h2 class="h2">
            限量号选号流程：
          </h2>
          <dl class="dl">
            <dt
              class="dt"
              data-flex="main:left"
            >
              选号分为两个环节，第一个环节为意向选号环节，第二个环节为随机选号环节。<br>
              若在意向选号环节中系统匹配成功，则立即确认限量号、结束选号。<br>
              若在意向选号环节系统匹配失败，则进入随机选号环节。<br>
              随机选号环节可分为多轮，每轮选号均有30分钟时间用于确认限量号，或者通过消耗奥金更换备选号，并进入下一轮选号。<br>
              在某轮随机选号中，选号超过30分钟，则系统自动在备选限量号中选择一个限量号完成匹配。
            </dt>
          </dl>
          <h2 class="h2">
            匹配规则：
          </h2>
          <dl class="dl">
            <dt
              class="dt"
              data-flex="main:left"
            >
              意向选号：每位客户可以按照喜好选择1个意向限量号。若系统判断该意向限量号存在于剩余限量号中，则匹配成功；反之，匹配失败。<br>
              随机选号第一轮：系统在剩余限量号中随机为客户生成3个备选限量号，客户可以选择任意一个备选限量号并确认成为自己的专属限量号。完成限量号确认后，选号结束。随机选号第一轮不消耗奥金。<br>
              随机选号第二轮：若客户对随机选号第一轮的所有3个备选限量号均不满意，可以放弃选号，并使用{{ integral }}奥金，更新20个备选限量号，进入随机选号第二轮。客户同样可以选择任意一个备选限量号并确认成为自己的专属限量号。完成限量号确认后，选号结束。<br>
              随机选号第三轮及以后：客户可以通过使用奥金的方式不限次数进行备选限量号的更新，直至在备选限量号中选出心仪的限量号。从进入随机选号第三轮选号开始，消耗奥金的规则依次为：{{ paralleling.join('、') }}……
            </dt>
            <dt
              class="dt"
              data-flex="main:left"
            >
              <p style="margin-top: 18px">
                限量号一旦匹配，无法修改。
              </p>
            </dt>
          </dl>
        </div>
        <audi-button
          text="我知道了"
          color="black"
          height="56px"
          @click="$router.back(-1)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import audiButton from '@/components/audi-button.vue'
import HeaderCustom from '@/components/header-custom.vue'
import { LIMIT_NUMBERS } from '@/config/conf.data'

export default {
  components: {
    'header-custom': HeaderCustom,
    'audi-button': audiButton
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      integral: 0,
      increase: 0,
      paralleling: []
    }
  },
  created() {
    this.handleProcessData()
  },
  methods: {
    handleProcessData() {
      const { seriesCode } = this.$route.query || ''
      const seriesConf = LIMIT_NUMBERS.filter((i) => i.seriesCode === seriesCode)[0] || {}
      if (seriesConf && Object.keys(seriesConf)?.length) {
        this.integral = seriesConf.integral || 0
        this.increase = seriesConf.increase || 0
        for (let index = 1; index < 10; index++) {
          this.paralleling.push(seriesConf.integral + seriesConf.increase * index)
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.limited-number-wrapper {
  padding: 0 16px 16px 16px;
  .limited-number-text {
    span{
      word-wrap: break-word;
      word-break: break-all;
    }
    .h1 {
      font-size: 16px;
      text-align: center;
      padding: 10px 0;
    }
    .h2 {
      font-size: 14px;
    }
    .num {
        width: 20px;
    }

    .dl {
      margin-top: 15px;
      padding-bottom: 15px;
      font-size: 13px;
      color: #555;
      line-height: 160%;
      p {
        margin: 0;
      }
      .bold {
        font-weight: bold;
        color: #000;
      }
      .dt {
        margin-bottom: 12px;
        line-height: 180%;
      }
      .dd {
        margin: 12px 0 0 20px;
        li {
          margin-bottom: 8px;
        }
      }
      .uld2 {
        padding-left: 20px;
      }
      .underline {
        text-decoration: underline;
      }
    }
  }
}
</style>
