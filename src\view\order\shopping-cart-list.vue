<template>
  <div name="pddingTop">
    <page-load
      @onRefresh="onRefresh"
      :pulldown.sync="pulldown"
      :pullupstate="false"
      :min-height="minHeight"
    >
      <ul>
        <li
          v-for="(item, i) in dataList"
          :key="i"
        >
          <div class="shopmain">
            <van-checkbox
              class="item_checkbox_button"
              v-if="!isMinip"
              v-model="item.checked"
              @change="signchecked(item)"
            >
              <img
                class="item_checkbox_img"
                slot="icon"
                :src="item.checked ? activeIcon : inactiveIcon"
              >
            </van-checkbox>

            <div class="shopsright">
              <span class="time_span"> {{ item.createTime }}</span>

              <div
                class="shops"
                @click="todetails(item)"
              >
                <div>
                  <img
                    :src="
                      BaseOssHost +
                        item.carModel.imageUrl +
                        '?x-oss-process=image/resize,m_lfit,h_200,w_200'
                    "
                  >
                </div>
                <div class="item-content">
                  <div class="name_div">
                    {{ item.carModel.modelNameCn }}
                  </div>
                  <div class="config_div">
                    {{  getConfigNnme(item) }}
                  </div>

                  <div class="item-p">
                    <span>定金:¥
                      {{
                        (item.totalPrice / 100)
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                      }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
      <div
        class="btn-delete-height"
        v-if="!showNotData"
      />
      <div
        v-show="showNotData"
        class="not_data"
      >
        <div>
          <img
            src="../../assets/img/not_data.png"
            alt=""
          >
        </div>
        <p>暂无内容</p>
      </div>
      <div>
        <div
          class="bottom_style"
          v-if="!isMinip"
        >
          <div class="checkbox_style">
            <van-checkbox
              v-model="ischecked"
              disabled="disabled"
              @click="checkAll"
            >
              <img
                class="checkbox_button"
                slot="icon"
                :src="ischecked ? activeIcon : inactiveIcon"
              >
              <span> 全选 </span>
            </van-checkbox>

            <div
              class="checkbox_styleRight"
              v-show="total > 0"
            >
              <span>{{ "共" + total + "项" }}</span>
            </div>
          </div>
          <div class="btn-delete-wrapper">
            <AudiButton
              v-if="isShowDelete"
              @click="onDelete"
              :text="'删除'"
              color="black"
              font-size="16px"
              height="56px"
            />
            <AudiButton
              v-if="!isShowDelete"
              @click="onContrast"
              :text="contrastText"
              color="black"
              font-size="16px"
              height="56px"
            />
          </div>
        </div>
      </div>
    </page-load>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import {
  Checkbox, Card, Field, Cell, Toast
} from 'vant'
import {
  getCarShoppingCartList,
  postDelCarShoppingCart,
  getCarOrderInfoH5,
  getOtds,
  getCheckNowBuyStatus
} from '@/api/api'
import AudiButton from '@/components/audi-button'
import { callNative } from '@/utils'
import api from '../../config/url'
import PageLoad from '../../components/page-load.vue'

export default {
  components: {
    [Checkbox.name]: Checkbox,
    [Card.name]: Card,
    [Field.name]: Field,
    [Cell.name]: Cell,
    AudiButton,
    PageLoad
  },
  data() {
    return {
      minHeight: 'min-height: 100vh',
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      ischecked: false,
      isShowDelete: false,
      dataList: [],
      total: 0,
      disabled: false,
      BaseOssHost: api.BaseOssHost,
      cartIdList: [],
      ccIdList: [],
      showNotData: false,

      pulldown: false, // 下拉
      contrastText: '车型对比',
      isMinip: false,

      seriesCodeList: []
    }
  },
  mounted() {
    this.readAT()
    if (this.env === 'minip') {
      this.minHeight = 'min-height: calc(100vh - 56px)'
      this.isMinip = true
    } else {
      this.appCallJs()
    }
    this.shoppingCartlist()
  },
  computed: {
    ...mapState(['env'])
  },
  methods: {
    readAT() {
      const query = this.$route.query
      console.log('queryyypppppp', query)
      const token = query.token
      if (token) {
        localStorage.setItem('token', token)
        console.log(token)
      }
      const refreshToken = query.refreshToken
      if (refreshToken) {
        localStorage.setItem('refreshToken', refreshToken)
        console.log(refreshToken)
      }
    },
    appCallJs() {
      // this.isShowDelete = args.data
      // this.$forceUpdate()
      // alert("app args:====" + rgs.isEdit);
      callNative('appCallJs', {}).then((data) => {
        this.isShowDelete = data.isShowDelete
        // callbackId只生效一次，接到响应需要再次调用
        this.appCallJs()
      })
    },
    // 下拉刷新
    async onRefresh() {
      await this.shoppingCartlist()
      this.pulldown = !this.pulldown
    },
    // 列表
    async shoppingCartlist() {
      const { data } = await getCarShoppingCartList()

      // 这里需要手动添加默认的checked 后台没有返
      if (data.data.length > 0) {
        data.data.forEach((element) => {
          element.checked = false
          element.num = null
        })
        this.showNotData = false
        this.contrastText = '车型对比'
      } else {
        this.showNotData = true
        this.contrastText = '配置我的车辆'
      }
      this.dataList = data.data

      if (!this.dataList.length) {
        this.disabled = true
      }
    },

    async todetails(item) {
      if (this.isShowDelete) {
        item.checked = !item.checked
        this.amount()
      } else {
        if (this.env === 'minip') {
          // const res1 = await getOtds({
          //   seriesId: 'ADA7'
          // })
          // const skuid = res1.data.data.prodSkuId
          this.$router.push({
            path: '/quotation',
            query: {
              ccid: item.ccid,
              skuid: item.skuid,
              shoppingCartId: item.shoppingCartId,
              routeType: 'app'
            }
          })
          return
        }
        // 先查询是否可以下单
        const checkdata = await getCheckNowBuyStatus()

        console.log(`checkData----------${checkdata.data.data.status}`)

        if (checkdata.data.data.status === 2) {
          // 正常进入
          // 跳转配置单页面
          const { data } = await getCarOrderInfoH5()
          // const res1 = await getOtds({
          //   seriesId: 'ADA7'
          // })
          // const skuid = res1.data.data.prodSkuId
          const url = `${data.data.configValue}quotation?skuid=${item.skuid}&ccid=${item.ccid}&shoppingCartId=${item.shoppingCartId}&routeType=app`
          // 跳转详情
          callNative('audiOpen', { path: url }).then((data) => {})
        } else if (
          checkdata.data.data.status === 8
          || checkdata.data.data.status === 9
          || checkdata.data.data.status === 12
        ) {
          // 调用APP Dialog
          callNative('popup', {
            type: 'alert',
            alertparams: {
              title: '',
              desc: checkdata.data.data.content,
              actions: [
                {
                  type: 'fill',
                  title: '确定'
                },
                {
                  type: 'stroke',
                  title: '取消'
                }
              ]
            }
          }).then((data) => {
            if (data.type === 'fill') {
              // 打开APP页面
              callNative('openRoutePath', {
                path: checkdata.data.data.route
              }).then((data) => {})
            }
          })
        } else {
          callNative('toast', {
            type: 'fail',
            message: checkdata.data.data.content
          })
        }
      }
    },
    // 车型对比
    async onContrast() {
      if (this.showNotData) {
        // 跳转到我的爱车
        callNative('openRoutePath', { path: 'scaudi://bindCar/home' }).then((data) => {})
        return
      }
      this.seriesCodeList = [],
      this.ccIdList = []
      this.dataList.forEach((element) => {
        if (element.checked) {
          this.ccIdList.push(element.ccid)
          this.seriesCodeList.push(element.carSeries.seriesCode)
        }
      })

      if (this.ccIdList.length < 2) {
        callNative('toast', {
          type: 'fail',
          message: '车型对比至少选择2个配置单'
        })
      } else if (this.ccIdList.length > 6) {
        callNative('toast', {
          type: 'fail',
          message: '车型对比一次最多选择6个配置单'
        })
      } else {
        // 判断车型是否一致
        const newArr = []
        for (let i = 0; i < this.seriesCodeList.length; i++) {
          if (newArr.indexOf(this.seriesCodeList[i]) == -1) {
            newArr.push(this.seriesCodeList[i])
          }
        }
        if (newArr.length > 1) {
          callNative('popup', {
            type: 'alert',
            alertparams: {
              title: '',
              desc: '请选择相同的车系进行对比',
              actions: [
                {
                  type: 'fill',
                  title: '确定'
                }
              ]
            }
          }).then((data) => {

          })
        } else {
          let carCcid = ''
          for (let i = 0; i < this.ccIdList.length; i++) {
            carCcid += `${this.ccIdList[i]},`
          }
          // this.$router.push({
          //   path: '/quotation/models-contrast',
          //   query: {
          //     ccid: carCcid.substr(0, carCcid.length - 1)
          //   }
          // })

          // const { data } = await getCarOrderInfoH5()
          // const url = `${data.data.configValue}quotation/models-contrast?ccid=${carCcid.substr(0,carCcid.length-1)}`

          const { origin, pathname } = window.location
          const url = `${origin}${pathname}#/quotation/models_contrast?ccid=${carCcid.substr(0, carCcid.length - 1)}`
          // 跳转车型对比
          callNative('audiOpen', { path: url }).then((data) => {})
        }
      }
    },
    // 删除订单
    async onDelete() {
      this.dataList.forEach((element) => {
        if (element.checked) {
          this.cartIdList.push(element.shoppingCartId)
        }
      })
      if (this.cartIdList.length < 1) {
        callNative('toast', { type: 'fail', message: '请选择配置单' })
      } else {
        // 调用APP Dialog
        callNative('popup', {
          type: 'alert',
          alertparams: {
            title: '',
            desc: '确定删除配置单吗？',
            actions: [
              {
                type: 'fill',
                title: '确定'
              },
              {
                type: 'stroke',
                title: '取消'
              }
            ]
          }
        }).then((data) => {
          if (data.type === 'fill') {
            this.delCarShoppingCart()
          }
        })
      }
    },
    // 删除
    async delCarShoppingCart() {
      for (let i = 0; i < this.cartIdList.length; i++) {
        this.sensorsBuriedPoint(this.cartIdList[i])
      }
      const params = { shoppingCartIds: this.cartIdList }

      const { data } = await postDelCarShoppingCart(params)
      this.cartIdList = []

      this.ischecked = false
      this.total = 0
      this.shoppingCartlist()
    },

    // 单选
    signchecked(val) {
      this.amount(val)
    },
    amount(val) {
      const arr = []
      const MoneyList = []
      this.dataList.forEach((item) => {
        if (item.checked === true) {
          MoneyList.push(item)
          arr.push(item.checked)
        }
      })
      // 这里就是判断时候为全选
      if (arr.length === this.dataList.length) {
        this.ischecked = true
      } else {
        this.ischecked = false
      }
      // 价格要置为0  不然一直会累加的 也会又很大的问题
      this.total = MoneyList.length
      // for (let i = 0; i < MoneyList.length; i++) {
      //   this.total += MoneyList[i].price * MoneyList[i].buyNum;
      // }
    },
    // 全选  这里的事件有两中 一个是click 一个是change 不同的事件用不同的方法
    checkAll() {
      this.dataList.forEach((item) => {
        if (this.ischecked === false) {
          item.checked = true
        } else {
          item.checked = false
        }
      })
    },
    getConfigNnme(item) {
      let optionName = (item.outsideColor.colorNameCn ? `${item.outsideColor.colorNameCn},` : '')
      for (let i = 0; i < item.optionList.length; i++) {
        optionName += `${item.optionList[i].optionNameCn},`
      }
      return optionName
    },

    // 神策埋点
    sensorsBuriedPoint(shoppingCartId) {
      const params = {
        page_name: '我的购物车',
        tab_name: '整车',
        order_id: shoppingCartId,
        order_quantity: 1
      }
      this.$sensors.track('deleteMyOrder', params)
    }
  }
}
</script>

<style lang="less" scoped>
.not_data {
  height: 100%;
  text-align: center;
  padding-top: 150px;
  img {
    width: 76px;
    height: 76px;
  }
}

li {
  background: #ffffff;
  position: relative;
  border-bottom: 1px #f2f2f2 solid;
  padding: 16px;

  .shopmain {
    display: flex;
    position: relative;

    .shopsright {
      width: 100%;
    }

    .item_checkbox_button {
      vertical-align: middle;
      padding-right: 20px;

      .item_checkbox_img {
        width: 18px;
        height: 18px;
      }
    }

    .time_span {
      font-size: 14px;
      color: #999;
      font-family: "Audi-Normal";
    }
    .shops {
      padding-top: 10px;
      display: flex;
      img {
        width: 88px;
        height: 88px;
        object-fit: cover;
      }

      .item-content {
        padding-left: 12px;
        width: calc(100% - 88px);
        flex-flow: column;
        justify-content: space-between;
        align-items: flex-start;

        .name_div {
          font-size: 16px;
          color: #000000;
          border: none;
          font-family: "Audi-WideBold";
          // overflow : hidden;
          // text-overflow: ellipsis;
          // display: -webkit-box;
          // -webkit-line-clamp: 1; /* 限制在一个块元素显示的文本的行数 */
          -webkit-box-orient: vertical; /* 垂直排列 */
          word-break: break-all;  /* 内容自动换行 */
        }
        .config_div {
          font-size: 12px;
          padding-top: 6px;
          color: #999;
          font-family: "Audi-WideBold";
          overflow : hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1; /* 限制在一个块元素显示的文本的行数 */
          -webkit-box-orient: vertical; /* 垂直排列 */
          word-break: break-all;  /* 内容自动换行 */
        }

        .item-p {
          font-size: 14px;
          font-family: "Audi-Normal";
          color: #000;
          padding-top: 24px;
        }
      }
    }
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  border-top: 2px #f2f2f2 solid;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
  .checkbox_button {
    margin-left: 16px;
    margin-bottom: 5px;
    width: 18px;
    height: 18px;
    color: #000;
  }
  .checkbox_style {
    display: flex;
    height: 20px;
    justify-content: space-between;
    font-family: "Audi-Normal";
    color: #999999;
    width: 100%;
    font-size: 16px;
    margin-bottom: 16px;
    span {
      font-size: 17px;
      color: #000;
      font-family: "Audi-Normal";
    }
    .checkbox_styleRight {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #000;
      margin-right: 16px;
    }
  }
}
.btn-delete-wrapper {
  margin: 0 16px;
}

.btn-delete-height {
  height: 130px;
}
</style>
