<template>
  <div class="unsubscribeSucceed">
    <div>
      <img src="../../assets/img/contract-success.png">
    </div>
    <p v-html="`${context.tip || '您的充电桩安装订单已提交成功，<br/>详情请查看我的服务订单'}`" />
    <div class="btnWarp">
      <div
        class="buttons"
        @click="checkOrder"
      >
        {{ context.btnText || '查看我的订单' }}
      </div>
    </div>
  </div>
</template>

<script>
import { callNative } from '@/utils'
import Q5E_TRON_DATA from '@/config/Q5e-tron-map.data'
import { HUI_XING } from '@/config/conf.data'
import storage from '../../utils/storage'

export default {
  data() {
    return {
      type: 0,
      context: [
        {
          // 暂缺
        },
        {
          title: '提交成功',
          tip: '您的充电桩安装订单已提交成功，<br/>详情请查看我的服务订单',
          btnText: '查看我的订单'
        },
        {
          title: '提交成功',
          tip: `恭喜您将获得${HUI_XING.WW}奥金，该笔奥金将在您确认交车后，发放到您的奥金账户中！`,
          btnText: '查看我的订单'
        },
        {
          title: '兑换成功',
          tip: `恭喜您将获得${HUI_XING.WW}奥金，该笔奥金将在您确认交车后，发放到您的奥金账户中！`,
          btnText: '我知道了'
        },
        {
          // 暂缺
        },
        {
          title: '兑换成功',
          tip: '您的充电桩安装订单已提交成功，充电卡将在交车后发放至您的卡包中',
          btnText: '我知道了'
        },
        {
          title: '提交成功',
          tip: '恭喜您获得7200元充电卡，权益将在<br/>您确认交车后，发放至您的卡包中',
          btnText: '查看我的订单'
        },
        {
          title: '提交成功',
          tip: `恭喜您获得${HUI_XING.WW}奥金，奥金将尽快发送至您的账户中，请注意查收，充电卡已在交车后发送至您的卡包中。`,
          btnText: '查看我的订单'
        }
      ],
      HUI_XING
    }
  },
  mounted() {
    const { context, $route: { query: { type, orderStatus } } } = this
    const text = context[`${type}`]
    this.type = +type
    if (+type >= 0) {
      this.context = text
      this.$store.state.title = text.title
    }

    console.log('%c [ this.context ]-73', 'font-size:14px; background:#cf222e; color:#fff;', this.context, this.type)
  },
  methods: {
    checkOrder() {
      if (this.type == 2 || this.type == 3) {
        // 奥金中心
        // callNative("openRoutePath", { path: "scaudi://mine/sign/home" });
        // 保存选择了奥金
        storage.set('isOrderIntegral', 1)
        this.$router.back(-1)
      } else {
        // 查看我的订单
        callNative('openRoutePath', { path: 'scaudi://mall/orderlist?index=3' })
        callNative('close', { type: 'service' })
      }
    }
  }
}
</script>

<style scoped lang="less">
@import url("../../assets/style/scroll.less");
@import url("../../assets/style/buttons.less");

.unsubscribeSucceed {
  padding: 16px;
  text-align: center;
  img {
    margin-top: 110px;
    width: 72px;
    height: 72px;
  }

  p {
    text-align: center;
    font-size: 18px;
    color: #000000;
    line-height: 25px;
    margin-top: 25px;
  }

  .btnWarp {
    position: fixed;
    z-index: 2;
    width: 100%;
    bottom: 0;
    background: #fff;
  }
}
</style>
