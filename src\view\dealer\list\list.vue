<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-10-19 16:44:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-12-21 13:32:53
 * @FilePath     : \src\view\dealer\list\list.vue
 * @Descripttion :
-->
<script src="./list"></script>

<template>
  <div
    :class="
      ['dealer-list-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
    />
    <div
      class="main-wrapper"
    >
      <div
        class="dealer-list-main"
        :style="{
          'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
        }"
      >
        <div class="search-box">
          <van-search
            v-model="dealerNameKW"
            placeholder="请输入代理商名称"
            @clear="onClearSearch"
            @search="onSearch"
          />
        </div>
        <van-cell-group>
          <van-field
            is-link
            readonly
            :label-align="labelAlign"
            :label-width="labelWidth"
            name="citesCode"
            v-model="citesCode"
            label="省份城市"
            class="cites-picker"
          >
            <template #input>
              <input
                type="text"
                :placeholder="`${locationLoading || '请点击选择省份城市'}`"
                :value="citesName"
                readonly
                class="van-field__control"
                @click="locationLoading || (showCitesPicker = true)"
              >
            </template>
          </van-field>
          <!-- 购买人与个人车主证件选择公用 -->
          <van-popup
            v-model="showCitesPicker"
            position="bottom"
          >
            <van-picker
              show-toolbar
              ref="citesPicker"
              title="省份城市"
              :loading="citesLoading"
              :default-index="citesDefaultIndex"
              :columns="citesList"
              @confirm="onConfirmCitesPicker"
              @cancel="showCitesPicker = false"
            />
          </van-popup>
        </van-cell-group>
        <div class="dealer-list-length">
          共找到{{ dealerList.length }}条结果
        </div>
        <div
          class="dealer-list-box"
        >
          <div
            v-if="dealerList.length"
            class="list-box"
            style="height: calc(100vh - 230px)"
          >
            <van-pull-refresh
              v-model="refreshing"
              @refresh="onRefresh"
              style="height: calc(100vh - 260px)"
            >
              <van-list
                v-model="loading"
                :finished="finished"
                :offset="100"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <div
                  class="list van-hairline--bottom"
                  v-for="(item,index) in dealerList"
                  :key="index"
                  @click="handlePickDealer(item)"
                >
                  <h3 class="h3 text-one-hidd">
                    {{ index < 10 ? '0' : '' }}{{ index + 1 }} {{ item.dealerName }}
                  </h3>
                  <p class="info text-two-hidd">
                    {{ item.distanceStr ? item.distanceStr + ' | ' : '' }}{{ item.dealerAdrress }}
                  </p>
                </div>
              </van-list>
            </van-pull-refresh>
          </div>
        </div>
      </div>

      <network @reload="networkReload()" />
      
    </div>

    
  </div>
</template>

<style lang="less" scoped>
.page-wrapper, .main-wrapper, .dealer-list-main, .dealer-list-box {
  height: 100%;
}
.dealer-list-main {
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  .van-hairline--top-bottom {
    &::after {
      border-top: none;
    }
  }
  .van-picker {
    /deep/.van-picker__title {
      font-weight: 600;
    }
    /deep/.van-picker__confirm {
      color: #000;
    }
    /deep/.van-picker__cancel {
      color: #fff;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        background: url(../../../assets/img/icon05.png) no-repeat 50% / contain;
      }
    }
    /deep/.van-picker-column__item--selected {
      font-weight: 600;
    }
  }
}
.search-box {
  .van-search {
    height: 74px;
    padding: 20px 16px;
    .van-cell {
      height: 34px;
    }
  }
}
.van-cell-group {
  height: 56px;
  .cites-picker {
    padding: 16px;
    font-size: 16px;
  }
}
.dealer-list-length {
  padding: 22px 16px 0 16px;
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  font-family: 'Audi-WideBold';
}
.dealer-list-box {
  /deep/.van-pull-refresh {
    overflow: auto;
  }
  .list-box {
    box-sizing: border-box;
    padding-bottom: 30px;
    overflow: auto;
    /deep/.van-list__finished-text {
      color: #e5e5e5;
    }
    .list {
      padding: 26px 0 14px;
      margin: 0 16px 0 16px;
      .h3 {
        margin: 0 0 8px 0;
        font-family: 'Audi-WideBold';
        font-weight: normal;
        font-size: 16px;
        height: 24px;
        line-height: 24px;
      }
      .info {
        margin: 0;
        font-size: 12px;
        line-height: 20px;
      }
    }
  }

}
</style>
