<template>
    <div class="container">
        <navigation title="请选择用户类型" :back-type="backType" :custom="true" @onBack="toBack" :titleSize="'16'" :isBorder="false" :isFamily="'medium'"/>
        <div class="connter">
            <div class="user-list-select" :class="tagIndex == index ? 'user-list-select-y' : ''" v-for="(item, index) in userList"
                :key="index" @click="chengUser(item, index)">
                <span>{{ item.name }}</span>
                <img :src="tagIndex == index ? require('./img/radio-y.png') : require('./img/radio-n.png')" alt="">
            </div>
            <div class="submit" :class="tagIndex != null ? '' : 'submit-agreement'">
                <p @click="submit">
                    下一步
                </p>
            </div>
        </div>
    </div>
</template>
<script>
import Vue from 'vue'
import {
    Toast,
    Checkbox
} from 'vant'
import navigation from '../../components/navigation'
import {
    queryCheckCustomizeTagInformation,
    queryCheckCustomizeTagInformationHis
} from '@/api/api'
import { callNative } from '@/utils/index'

Vue.use(Checkbox)
    .use(Toast)
export default {
    components: { navigation },
    data() {
        return {
            backType: 'app',
            isShowBack: false,
            checked: false,
            isAgreement: false,
            userList: [
                // {
                //     type: 'old',
                //     name: '进取精英',
                //     id: 'B9',
                //     routePathName: 'enterprisingElite'
                // },
                {
                    type: 'old',
                    name: '留学生免税车',
                    id: 'B8',
                    routePathName: 'car-guide-index'
                },
                // {
                //     type: 'new',
                //     name: '车补企业员工',
                //     id: 'BF',
                //     routePathName: 'userFlexibleAuthentication-users'
                // },
                {
                    type: 'old',
                    name: '警采平台私户购车',
                    id: 'BD',
                    routePathName: 'government'
                },
                {
                    type: 'old',
                    name: '供应商员工',
                    id: 'B5',
                    routePathName: 'supplier'
                },
                {
                    type: 'old',
                    name: '总对总企业员工',
                    id: 'B9',
                    routePathName: 'relativeInformation-users'
                },
                {
                    type: 'old',
                    name: '股东方和集团下属企业员工',
                    id: 'BB',
                    routePathName: 'enterprise-users'
                },
                {
                    type: 'new',
                    name: '代理商投资人及员工',
                    id: 'B4',
                    routePathName: 'userFlexibleAuthentication-users'
                },
                {
                    type: 'new',
                    name: '归国精英（门店）',
                    id: 'BG',
                    routePathName: 'userFlexibleAuthentication-users'
                },
                {
                    type: 'new',
                    name: '外交人员个人购车',
                    id: 'B1',
                    routePathName: 'userFlexibleAuthentication-users'
                },
                {
                    type: 'new',
                    name: '总对总限时礼遇',
                    id: 'BC',
                    routePathName: 'userFlexibleAuthentication-users'
                },
                {
                    type: 'new',
                    name: 'VVIP',
                    id: 'B6',
                    routePathName: 'userFlexibleAuthentication-users'
                },
                {
                    type: 'new',
                    name: 'VIP',
                    id: 'B7',
                    routePathName: 'userFlexibleAuthentication-users'
                },
                {
                    type: 'new',
                    name: '特定机构',
                    id: 'BI',
                    routePathName: 'userFlexibleAuthentication-users'
                }
            ],
            tag: {},
            tagIndex: null
        }
    },
    created() { },
    mounted() {
        // 判断跳转来源
        const { backType } = this.$route.query
        if (backType) {
            this.backType = backType
        } else {
            this.backType = 'app'
        }
        localStorage.removeItem('userFlexibleAuthenticationAgreement')

    },
    methods: {
        submit() {
            if (this.tagIndex != null) {
                console.log(this.tag, 'this.tag')
                if (this.tag.type == 'old') {
                    this.$router.push({
                        name: this.tag.routePathName
                    })
                } else {
                    let params = {
                        tag: this.tag.id
                    }
                    queryCheckCustomizeTagInformationHis(params).then(res => {
                        if (res.data.data) {
                            this.$router.push({
                                name: 'userFlexibleAuthentication-success',
                                query: {
                                    tag: this.tag.id,
                                    id:res.data.data
                                }
                            })
                        } else {
                            queryCheckCustomizeTagInformation(params).then((res) => {
                                if (res.data.data && res.data.data.informationStatus != null) {
                                    // this.isShowBack = true
                                    if (res.data.data.informationStatus === 2) {
                                        this.$router.push({
                                            name: 'userFlexibleAuthentication-information',
                                            query: {
                                                tag: this.tag.id
                                            }
                                        })
                                    }
                                    if (res.data.data.informationStatus === 1) {
                                        this.$router.push({
                                            name: 'userFlexibleAuthentication-success',
                                            query: {
                                                tag: this.tag.id
                                            }
                                        })
                                    }
                                    if (res.data.data.informationStatus === 0) {
                                        this.$router.push({
                                            name: 'userFlexibleAuthentication-uploaded-success',
                                            query: {
                                                tag: this.tag.id
                                            }
                                        })
                                    }
                                } else {

                                    this.$router.push({
                                        name: this.tag.routePathName,
                                        query: {
                                            tag: this.tag.id
                                        }
                                    })
                                }
                            })
                        }
                    })

                }
            }
        },

        chengUser(item, ind) {
            this.tag = item
            this.tagIndex = ind
        },
        toBack() {
            callNative('prepage', { times: 1 })

        },
    }
}
</script>
  
<style lang="less" scoped>
@import url("../../assets/style/cell.less");
@import url("../../assets/style/scroll.less");
@import url("../../assets/style/dialog.less");
@import url("../../assets/style/buttons.less");
@import url("../../assets/style/animation.less");
@import url("../../assets/style/nor.less");
@import url("../../assets/style/common.less");

.connter {
    padding: 0 16px;
    padding-bottom: 120px;
    margin-top: 8px;

    .user-list-select {
        // width: calc(100% - 32px);
        height: 55px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 0.5px solid #E5E5E5;
        padding: 0 16px;
        margin-bottom: 16px;
        border-radius: 2px;
        span {
            font-size: 16px;
            color: #333333;
            // height: 24px;
        }

        img {
            width: 24px;
            height: 24px;
        }
    }

    .user-list-select-y {
        border: 0.5px solid #000000;

    }
}
.submit {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 95px;
    background-color: #FFFFFF;
    padding-top: 16px;

    // padding: 0 16px;
    p {
        // width:100%;
        height: 56px;
        background-color: #1A1A1A;
        color: #FFFFFF;
        font-size: 16px;
        font-family: 'Audi-Normal';
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 16px;

    }
}

.submit-agreement {
    p {
        background-color: #E5E5E5;
        color: #FFFFFF;
    }


}
</style>
