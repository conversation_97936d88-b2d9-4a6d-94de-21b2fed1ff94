<template>
  <div class='confirm-order' v-if="preOrders">
    <div class="border-top">
      <addre :address="userAddress" :order-type="orderType"></addre>
    </div>
    <div style="padding: 8px 16px;">

      <div class="_card-box border-bottom">
        <div class="_title">商品</div>
        <div class="_product">
          <div class="_img"><img :src="preOrders.itemList[0].imageUrl" /></div>
          <div class="_content">
            <div class="_text1">{{preOrders.itemList[0].productName}}</div>
            <div class="_text2">
              <p v-if="skuNamelist.length > 0">规格：{{skuNamelist.join(',')}}</p>
              &nbsp;
            </div>
            <div class="_text3">
              <p>{{ showProPrice(preOrders.itemList[0])}}</p>
              <p>x{{preOrders.itemList[0].quantity}}</p>
            </div>
          </div>
        </div>
      </div>

      <div style="padding: 8px 0;">
        <div class="_list">
          <div>商品金额</div>
          <div class="align-center">¥ {{ (preOrders.totalAmount / 100)
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                }}</div>
        </div>
        <div class="_list">
          <div>运费</div>
          <div class="align-center">¥{{ (preOrders.deliveryFee / 100)
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                }}</div>
        </div>
        <div class="_list" v-if="orderType === 9 || orderType === 2">
          <div>服务商</div>
          <div class="align-center" @click="toDealer">
            {{serviceProvider ? serviceProvider.dealerName : '无服务商'}}
            <!-- <van-icon style="margin-left: 3px;" name="arrow" color='#333' /> -->
          </div>
        </div>
        <div class="_list">
          <div>优惠券</div>
          <div class="align-center" @click="updateCouponState">
            {{couponList.length > 0 ? couponItem?couponItem.couponName : '有可使用优惠券' : "暂无优惠券可用"}}
            <van-icon style="margin-left: 3px;" name="arrow" color='#333' />
          </div>
        </div>
        <div class="_list">
          <div>发票</div>
          <div class="align-center" @click="onInvoiceInfo">
            {{receiptInfo?'本次已开具发票':'本次不开具发票'}}
            <van-icon style="margin-left: 3px;" name="arrow" color='#333' />
          </div>
        </div>
        <div class="_textarea">
          <van-field v-model="remark" autosize type="textarea" rows="2" placeholder="备注" />
        </div>
      </div>

      <div class="_pat-type border-bottom" v-show="preOrders.totalMaxCredit !== preOrders.totalMinCredit">
        <div class="_title">
          <p class="">支付方式</p>
          <p>您有{{myPoints}}奥金可用</p>
        </div>
        <div class="_radio">
          <img :src="isAmountSelected ? activeIcon : inactiveIcon" @click="onAmountSelected">
          <p>¥{{ ((preOrders.totalAmount + preOrders.deliveryFee)/ 100)
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                }}</p>
        </div>
        <div class="_radio">
          <img :src="isPointsSelected ? activeIcon : inactiveIcon" @click="onPointsSelected">

          <p>{{preOrders.totalMaxCredit}}奥金</p>
        </div>
        <div class="_radio">
          <img :src="isEditPointsSelected ? activeIcon : inactiveIcon" @click="onEditPointsSelected">
          <van-field v-model="inputPoints" type="digit" autosize placeholder="自定义奥金支付" />
        </div>
      </div>

      <div class="_totalPrice">
        <p>总价</p>
        <p class="p1" v-if="bottomTotalAmount > 0">¥ {{( (couponItem? bottomTotalAmount * couponItem.discountOff / 10 : bottomTotalAmount) / 100)
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}}</p>
        <p class="p1" v-if="bottomTotalPoints > 0">奥金{{bottomTotalPoints * (couponItem ? couponItem.discountOff / 10 : 1)}}</p>

      </div>

      <div style="padding-bottom: 50px;" v-if="buttonStatus">
        <AudiButton text="确认提交" color="black" height="56px" font-size="16px" @click="onSubmitOrder" />
      </div>
    </div>

    <van-action-sheet v-model="couponState" title="可用优惠券">
      <div class="_box">
        <div class="_scroll">
          <div class="_list" v-for="(item, idx) in couponList" :key="idx">
            <div class="_left">
              <p style="font-size: 16px;">{{item.couponName}}</p>
              <p class="">有效期:{{item.effectiveDate}}</p>
            </div>
            <div class="_right">
              <p>{{item.discountOff}}折券</p>
              <img @click="getCouponItem(idx,item.checked)" v-if="item.checked" src="../../assets/img/radio_checked.png">
              <img @click="getCouponItem(idx,item.checked)" v-else src="../../assets/img/radio_normal.png">
            </div>
          </div>
        </div>
        <AudiButton text="完成" color="black" @click="couponState = false" height="56px" font-size="16px" />
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
  import addre from "./address.vue"
  import { mapState } from "vuex"
  import AudiButton from '@/components/audi-button'
  import Vue from 'vue';
  import { Field, ActionSheet, Toast } from 'vant';
  import { postSubmitOrder, postPayments } from '../../api/product.js';
  import { getMyPointsV2, postPreorders, getUserAddress, getAfterSalesByOrderId, postBindCardInfo, getProductDetail } from '../../api/api.js';
  import { callNative } from '@/utils'
  import storage from "../../utils/storage";

  Vue.use(ActionSheet);
  Vue.use(Field);
  Vue.use(Toast);
  export default {
    components: { addre, AudiButton },
    data() {
      return {
        activeIcon: require('../../assets/img/radio_checked.png'),
        inactiveIcon: require('../../assets/img/radio_normal.png'),
        remark: '',
        couponState: false,
        myPoints: 0, //用户可用奥金
        preOrders: null, //预下单信息 包含商品优惠券

        isAmountSelected: true, //使用权金额支付 默认使用
        isPointsSelected: false, //是否选择了使用奥金
        isEditPointsSelected: false, //是否选择了奥金输入
        bottomTotalAmount: 0, //底部显示的金额
        bottomTotalPoints: 0, //底部显示的使用奥金
        inputPoints: '', //输入的奥金
        itemList: [],
        buttonStatus: true,
        couponList: [], //优惠券
        couponItem: null,
        orderId: null,
        skuNamelist: [], //全部的sku名称
        prodGroupId: null,
        productDetail:null,//商品详情
        orderType: null, //订单类型
        /*
          1 普通商品 2 到店自提 9 MPC卡券 协助类安装
          1. 普通商品用户写地址 
          2. 到店自提 只会从订单详情进来回有orderId，查询这个orderId的服务商 
          3 协助安装类从订单详情进来有orderId， 查询这个orderId的服务商 如果从售后商城进来 判断这个人是否有vin没有不让买，有让用户选择服务商
        */
      }
    },
    computed: {
      ...mapState({
        serviceProvider: (state) => state.serviceProvider || null, //代理商
        userAddress: (state) => state.appUserAddress, //用户地址
        receiptInfo: (state) => state.invoiceInfo, //发票信息
        isInit: (state) => state.isInit, //是否是第一次进来
      })
    },
    watch: {
      //输入的奥金
      inputPoints(value) {
        if (value > this.preOrders.totalMaxCredit) {
          this.inputPoints = this.preOrders.totalMaxCredit;
          callNative("toast", {
            type: "fail",
            message: "奥金最大可输入" + this.preOrders.totalMaxCredit,
          });
        }
        this.changeAmount()
      },
      isAmountSelected(value) {
        if (value) this.changeAmount()
      },
      isPointsSelected(value) {
        if (value) this.changeAmount()
      },
      isEditPointsSelected(value) {
        if (value) this.changeAmount()
      },
      userAddress(value, value1) {
        console.log(value, value1, 123456)
      },
    },
    async mounted() {
      const { orderId } = this.$route.query
      if (orderId) this.orderId = orderId

      this.getMyPoints()
      this.postPreorders()
    },
    methods: {
      updateCouponState() {//弹窗卡券
        // this.couponState = true
      },

      async getAfterSalesByOrderId() { //获取订单绑定的服务商
        const { orderId } = this.$route.query
        const { data } = await getAfterSalesByOrderId({ orderId })
        if (data.data) {
          const userAddress = {
            receiverName: data.data.dealerName,
            mobile: data.data.dealerPhone,
            province: data.data.provinceName,
            city: data.data.cityName,
            district: data.data.areaName,
            details: data.data.dealerAdrress,
            addreType: '服务商'
          }
          this.$store.commit('saveappUserAddress', userAddress)
          this.$store.commit('updateServiceProvider', data.data)
        } else {
          this.buttonStatus = false
          Toast({ type: 'fail', message: '该商品暂无服务商', icon: require('../../assets/img/contract-fail.png') })
        }
      },

      //获取用户的默认地址
      async getUserAddress() {
        if (!this.userAddress?.mobile) {
          const data = await getUserAddress({ keyword: '', addressType: 2 })
          const userAddress = data.data.data.find((i) => i.isDefault)
          this.$store.commit('saveappUserAddress', userAddress)
        }
      },
      //先预下单接口获取优惠券和商品信息
      async postPreorders() {
        const { skuId, quantity } = this.$route.query
        const params = { itemList: [{ quantity, skuId }] }
        const res = await getProductDetail({ id: skuId })
        const skuType = res.data?.data?.prodSkus[0].skuType
        this.productDetail = res.data?.data
        if (parseInt(skuType) === 2 || parseInt(skuType) === 9) {
          const dealerInfo = res.data?.data?.prodAttrs.find(i => i.propName === '经销商')
          dealerInfo.propValue = JSON.parse(dealerInfo.propValue)
          params.dealerId = dealerInfo.propValue.dealerIdList[0]
          // this.prodGroupId = res.data?.data?.prodRelGroupVOS[0].prodGroupId
        }
        const data = await postPreorders(params)
        this.preOrders = data.data.data
        this.skuNamelist = data.data.data.itemList.map(i => i.skuName)
        this.skuNamelist = this.skuNamelist.filter(i=> i!=='无需配送')
        this.itemList = data.data.data.itemList
        const dealerInfo = data.data.data.dealerInfo
        if (parseInt(skuType) === 2 || (parseInt(skuType) === 9 && this.orderId)) {
          this.getAfterSalesByOrderId() //整车订单商品不能修改服务商  使用默认服务商地址
        } else if (parseInt(skuType) === 1) {
          this.getUserAddress() //用户自己的地址
        }
        this.couponList = data.data.data.couponInfo.usableCouponList
        this.orderType = parseInt(data.data.data.orderType)
        //单一支付
        if (this.preOrders.totalMaxCredit === this.preOrders.totalMinCredit) {
          if (this.preOrders.totalMaxCredit !== 0 && this.preOrders.totalAmount === this.preOrders.totalMaxCreditAmount) {
            // 全奥金商品
            this.bottomTotalPoints = this.preOrders.totalMaxCredit
          } else if (this.preOrders.totalMaxCredit !== 0) {
            // 固定价格和固定奥金以支付  总金额减去奥金抵扣总金额
            this.bottomTotalAmount = this.preOrders.totalAmount - this.preOrders.totalMaxCreditAmount

            this.bottomTotalPoints = this.preOrders.totalMaxCredit
          } else {
            // 全钱支付
            this.bottomTotalAmount = this.preOrders.totalAmount
          }
        } else {
          //混合支付 需要计算
          this.changeAmount()
        }
      },
      //获取用户奥金
      async getMyPoints() {
        const userInfo = localStorage.getItem('token')
        const data = await getMyPointsV2({ userId: userInfo.accountId })

        this.myPoints = data.data.data.vwTotalPoint

      },
      //显示价格
      showProPrice(product) {
        if (product.price === 999990) {
          //特殊商品 不显示价格
          return ''
        } else {
          if (product.creditPercent === product.creditPercentFloor && product.creditPercent === 100) {
            // 表示全奥金支付
            return product.price / 10 + "奥金"
          } else if (product.creditPercent == product.creditPercentFloor && product.creditPercent == 0) {
            // 表示不可以使用奥金支付 全部使用金钱支付
            return "¥ " + (product.price / 100).toString()
          } else if (product.creditPercent === product.creditPercentFloor && product.creditPercent < 100 && product.creditPercent > 0) {
            // 固定价格和固定奥金以支付
            return "¥ " + (product.price * (100 - product.creditPercent) / 10000).toString() +
              " | " + (product.price * 10 * product.creditPercent / 10000) + "奥金"
          } else if (product.creditPercent > 0 && product.price > 0) {
            //表示可以混合支付
            return "¥ " + (product.price / 100).toString() + " | " + (product.price / 10) + "奥金"
          } else {
            return "¥ " + (product.price / 100).toString()
          }
        }
      },
      onAmountSelected() {
        this.isAmountSelected = !this.AmountSelected
        if (this.isAmountSelected) {
          this.isPointsSelected = false //是否选择了使用奥金
          this.isEditPointsSelected = false //是否选择了奥金输入
        }
      },
      onPointsSelected() {
        this.isPointsSelected = !this.isPointsSelected
        if (this.isPointsSelected) {
          this.isAmountSelected = false //是否选择了使用奥金
          this.isEditPointsSelected = false //是否选择了奥金输入
        }
      },
      onEditPointsSelected() {
        this.isEditPointsSelected = !this.isEditPointsSelected
        if (this.isEditPointsSelected) {
          this.isPointsSelected = false //是否选择了使用奥金
          this.isAmountSelected = false //是否选择了奥金输入
        }
      },

      //显示底部价格
      changeAmount() {
        //选择金额支付
        const deliveryFee = this.preOrders.deliveryFee || 0 //费送费
        if (this.isAmountSelected) {
          this.bottomTotalPoints = 0
          this.bottomTotalAmount = this.preOrders.totalAmount - deliveryFee
        }
        //使用了奥金
        if (this.isPointsSelected) {
          this.bottomTotalPoints = this.preOrders.totalMaxCredit
          this.bottomTotalAmount = 0 //this.preOrders.totalAmount - (this.preOrders.deliveryFee || 0) - (this.preOrders.totalMaxCredit / 10)
        }
        //使用了自定义奥金
        if (this.isEditPointsSelected) {
          const inputPoints = this.inputPoints || 0
          this.bottomTotalPoints = inputPoints
          this.bottomTotalAmount = (this.preOrders.totalAmount - deliveryFee) / 100 - (inputPoints / 10)
          if (this.bottomTotalAmount > 0) this.bottomTotalAmount *= 100
        }
      },
      //选择发票
      onInvoiceInfo() {
        // this.$store.commit('saveisInit', false)
        this.$router.push({
          path: "/product-invoice",
          query: { receiptInfo: this.receiptInfo },
        });
      },
      //提交订单
      async onSubmitOrder() {
        const userAddress = this.userAddress
        const param = {
          buyerRemark: this.remark, //	备注
          cartIds: null, //购物车商品ID 用逗号隔开
          deliveryInfo: { //地址信息
            cityCode: userAddress.cityCode,
            districtCode: userAddress.districtCode,
            provinceCode: userAddress.provinceCode,
            receiverAddress: userAddress.details,
            receiverName: userAddress.receiverName,
            receiverContact: userAddress.mobile,
            provinceCityDistrictDesc: `${userAddress.province},${userAddress.city},${userAddress.district}`
          },
          credit: this.bottomTotalPoints * (this.couponItem ? this.couponItem.discountOff / 10 : 1), //使用奥金
          itemList: this.itemList, //商品list
        };
        if (this.receiptInfo) { //填写了发票信息
          param.receiptInfo = { //发票信息
            type: 1, // = 1 //"发票类型（1：普通发票，2：专票）"
            titleType: this.receiptInfo?.titleType, // = 2//"抬头类型（1：企业，2：个人）
            defaultReceipt: 0, // = 0 //"默认抬头（0：否，1：是）
            title: this.receiptInfo.title, // = "" //"发票抬头"
            dutyParagraph: this.receiptInfo.dutyParagraph, // = ""//"税号"
            bankName: '', // = ""//"开户银行"
            bankNumber: '', // = ""//"银行账号"
            comAddress: '', // = ""//"企业地址"
            comPhone: '', // = ""//"企业电话"
            email: this.receiptInfo.email, // = ""//"邮箱"
          }

        }
        if (this.couponItem) { //选择了优惠券
          param.equityId = this.couponItem.couponEquityId
        }
        if (this.orderType === 2) { //到店自提
          delete param.deliveryInfo
          param.carBuyerInfo = { //默认先塞一下地址里面的名字电话
            fullName: userAddress.receiverName,
            mobile: userAddress.receiverContact
          }
          if (localStorage.getItem('userInfo')) { //有登陆用户的 替换地址里面的名字电话
            const userInfo = JSON.parse(localStorage.getItem('userInfo'))
            if(userInfo.displayName)param.carBuyerInfo.fullName = userInfo.displayName
            if(userInfo.mobile)param.carBuyerInfo.mobile = userInfo.mobile
          }
          const { orderId } = this.$route.query
          param.itemList.map(i => i.dealerId = this.serviceProvider.dealerCode)

          if (this.orderId) {
            param.ngaOrderId = this.orderId
            param.productGroupId = this.productDetail.prodId
            if (!this.serviceProvider?.dealerCode) { //如果商品详情没有服务商id 就拿一下
              const { data } = await getAfterSalesByOrderId({ orderId })
              param.itemList.map(i => i.dealerId = data.data.dealerCode)
            }
          }
        }

        if (this.orderType === 9) { //安装类附件参数
          delete param.deliveryInfo
          if (this.orderId) {
            param.ngaOrderId = this.orderId
            const { orderId } = this.$route.query
            const { data } = await getAfterSalesByOrderId({ orderId })
            param.itemList.map(i => i.dealerId = data.data.dealerCode)
            param.productGroupId = this.productDetail.prodId
          } else {
            if (!this.serviceProvider) {
              Toast({ type: 'fail', message: '请选择服务商', icon: require('../../assets/img/contract-fail.png') })
              return
            }
            param.itemList.map(i => i.dealerId = this.serviceProvider.dealerCode)
            const { data } = await postBindCardInfo({})
            param.vin = data.data[0]?.vin
          }
        }
        const data = await postSubmitOrder(param)
        console.log(data)
        if(data.data.code !== '00'){
          Toast({ type: 'fail', message: data.data.message, icon: require('../../assets/img/contract-fail.png') })
          return
        }
        //这里有个判断 payAmount>0的话要跳转支付页面
        if (data.data.data.payAmount > 0) {
          await callNative('callPayCenter', {
            orderId: Number(data.data.data.orderId)
          },{orderInfo: data.data.data && JSON.stringify(data.data.data)})
          callNative('callPayResult', {}).then((res) => {
            //支付结果的返回 调用订单详情 获取支付状态
          })
        } else {
          //说明是奥金或者优惠券抵用完了不需要在去支付
          let payType = ''
          if (data.data.data.creditAmount != 0) {
            // 这里表示奥金支付
            payType = "JFP"
          } else {
            payType = "NA" // 优惠券支付
          }
          //无金额支付
          const str = await postPayments({
            orderId: data.data.data.orderId,
            payType: payType

          })
          //获取支付结果
        }
      },
      getCouponItem(index, checked) {
        if (checked) {
          this.couponItem = null
          this.couponList[index].checked = false
        } else {
          this.couponList.map(i => i.checked = false)
          this.couponItem = this.couponList[index]
          this.couponList[index].checked = true
        }

        this.$forceUpdate()
      },
      toDealer() {
        // if (this.orderType === 9 && this.orderId) return
        // this.$store.commit('saveisInit', false)
        // this.$router.push({
        //   path: '/aftersales/select-service-providers-list',
        //   query: {
        //     isBack: true
        //   }
        // })
      },
    }
  }
</script>

<style lang='less' scoped>
  .border-top {
    border-top: 1px solid #E5E5E5;
  }

  .border-bottom {
    border-bottom: 1px solid #E5E5E5;
  }

  div {
    box-sizing: border-box;
    font-family: "Audi-Normal";
  }

  p {
    margin: 0;
  }

  .align-center {
    display: flex;
    align-items: center;
  }

  ._card-box {
    width: 100%;
    padding-bottom: 24px;

    ._title {
      padding: 8px 0;
      line-height: 20px;
      font-size: 16px;
    }

    ._product {
      display: flex;
      align-items: center;
      justify-content: space-between;

      ._img {
        img {
          width: 100px;
          height: 100px;
          object-fit: cover;
        }
      }

      ._content {
        width: calc(100% - 100px);
        display: flex;
        flex-flow: column;
        padding-left: 17px;

        ._text1 {
          font-size: 14px;
          line-height: 17px;
          padding: 3px 0;
        }

        ._text2 {
          font-size: 12px;
          padding: 3px 0;
          line-height: 15px;
        }

        ._text3 {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          margin-top: 26px;
        }
      }
    }
  }

  ._list {
    padding: 8px 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
  }

  ._textarea {
    border: 1px solid #E5E5E5;
    margin: 12px 0;

    ::v-deep .van-cell {
      padding: 8px 12px;
      font-size: 12px;
    }
  }

  ._pat-type {
    display: flex;
    flex-flow: column;
    font-size: 14px;
    padding-bottom: 8px;

    ._title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 8px 0;

      p:first-child {
        font-size: 16px;
        font-weight: 500;
      }
    }

    ._radio {
      display: flex;
      align-items: center;
      padding: 8px 0;

      .van-cell {
        padding: 0;
        font-size: 14px;
        color: #000;
      }

      img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

    }
  }

  ._totalPrice {
    width: 100%;
    padding: 16px 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    p {
      font-size: 16px;
      margin-left: 8px;
    }

    .p1 {
      font-size: 16px;
      font-weight: bold;
      margin-left: 8px;
    }
  }

  ._popup-heard {
    position: relative;
    width: 100%;
    padding: 13px 0;
    text-align: center;

  }

  ::v-deep .van-popup--round {
    border-radius: 0;

    .van-action-sheet__header {
      font-family: "Audi-WideBold";
      font-size: 15px !important;
    }
  }

  ::v-deep .van-action-sheet__close {
    color: #333;
    font-size: 16px;
  }

  .van-action-sheet__content {
    ._box {
      padding: 10px 16px;
      box-sizing: border-box;

      ._scroll {
        overflow: scroll;
        height: 300px;
        padding: 1%;

        ._list {
          width: 98%;
          background: #FFFFFF;
          margin-bottom: 16px;
          box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
          padding: 10px 0;

          ._left {
            padding: 8px 17px;
            display: flex;
            flex-flow: column;
            width: calc(100% - 110px);
            border-right: 1px #E5E5E5 dashed;

            > p:last-child {
              color: #999999;
              font-size: 12px;
              margin-top: 9px;
            }
          }

          ._right {
            width: 110px;
            padding: 0 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            > p {
              font-size: 16px;
              font-family: "Audi-WideBold";
              color: #333333;
              line-height: 30px;
            }

            img {
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }
  }

  ::v-deep .van-field {
    .van-field__body {
      textarea {
        &::placeholder {
          color: #000;
        }
      }

      input {
        &::placeholder {
          color: #000;
        }
      }
    }
  }
</style>
