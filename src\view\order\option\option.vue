<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-10-27 16:03:07
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-27 15:10:53
 * @FilePath     : \src\view\order\option\option.vue
 * @Descripttion :
-->
<script src="./option"></script>
<template>
  <div :class="['order-price-box', !spread ? 'shrink-box' : 'affirm-box']">
    <div
      class="title van-hairline--bottom cell-top"
      data-flex="main:justify cross:center"
    >
      <div class="h2">
        {{ title }}
      </div>
      <span
        v-if="collapse && (options.some(i => Math.abs(i.price)) || options.every(i => !Math.abs(i.price)))"
        class="triangle-btn"
        @click="handleSpreadEachOther"
      />
    </div>
    <div class="order-config-list van-hairline--bottom">
      <div
        class="list"
        data-flex="main:justify"
      >
        <p
          class="p"
          data-flex="main:left"
        >
          <span class="type">车型</span>
          <font class="separator">
            |
          </font>
          <span
            class="name"
            data-block
          >{{ goods.modelNameCn }}</span>
        </p>
        <p class="price">
          <span>{{ goods.modelPrice | prefixFormatPrice }}</span>
        </p>
      </div>
      <div
        :class="['list', Math.abs(list.price) === 0 ? 'is-hide' : '', options.findIndex((i) => Math.abs(i.price) === 0) === index ? 'omit' : '']"
        data-flex="main:justify"
        v-for="(list, index) of options"
        :key="index"
      >
        <p
          class="p"
          data-flex="main:left"
        >
          <span class="type">{{ list.typeName }}</span>
          <font class="separator">
            |
          </font>
          <span
            class="name"
            data-block
          >{{ list.optionNameCn || list.colorNameCn }}</span>
        </p>
        <p class="price">
          <span v-if="list.price">{{ list.price | prefixFormatPrice }}</span>
          <span v-else>价格已包含</span>
        </p>
      </div>
      <div
        class="list total-prices"
        data-flex="main:justify"
      >
        <p
          class="p"
          data-flex="main:left"
        >
          <span class="type">总价</span>
        </p>
        <p class="price">
          <span>{{ goods.totalPrice | prefixFormatPrice }}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.order-price-box {
  border-top: 8px solid #f2f2f2;
  .title {
    box-sizing: content-box;
    margin: 0 16px;
    padding: 16px 0;
    .h2 {
      margin: 0;
      font-size: 16px;
      line-height: 24px;
      font-family: AudiTypeGB-WideBold, AudiTypeGB;
      &.car-owner {
        margin-top: 26px;
      }
    }
    .triangle-btn {
      display: block;
      width: 24px;
      height: 24px;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        left: 50%;
        top: 50%;
        
        margin: -3px 0 0 -5px;
        border: 5px solid transparent;
        border-top-color: #000;
      }

    }
  }
  &.shrink-box {
    .triangle-btn {
      &::after {
        
        margin: -7px 0 0 -5px;
        transform: rotate(180deg);
      }
    }
    .order-config-list {
      .list {
        &.is-hide:not(.omit) {
          display: none;
        }
        &.omit {
          .p {
            position: relative;
            text-indent: -999em;
            // color: #fff;
            &::after {
              position: absolute;
              left: 0;
              content: '· · ·';
              color: #808080;
              text-indent: 0;
              width: 60px;
            }

            // .separator {
            //   color: #fff;
            // }
          }
          // .price {
          //     color: #999
          // }
        }
      }
    }
  }
  &.affirm-box {
     background: #fff;
   
  
   }
  .order-config-list {
    margin: 0 16px;
    padding-bottom: 12px;
    .list {
      margin: 12px 0 0 0;
      font-size: 12px;
      color: rgba(0, 0, 0, .5);
      &.total-prices {
        color: #000;
        font-weight: 700;
      }
      p {
        margin: 0;
        line-height: 20px;
        &.price {
          // margin-left: 20px;
          width: 80px;
          text-align: right;
          color: rgba(0, 0, 0, .88);
        }
        .type {
        }
        .separator {
          font-size: 10px;
          position: relative;
          margin: -1px 8px 0;
          color: rgba(0, 0, 0, .2);
        }
        .name {
        }
      }
    }
  }
}
.cell-top{
  padding-top: 32px !important;
}

</style>
