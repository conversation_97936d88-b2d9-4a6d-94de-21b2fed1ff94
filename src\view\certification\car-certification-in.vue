<template>
  <div class="unsubscribeSucceed">
    <div style="text-align: center; margin-top: 78px">
      <img src="../../assets/img/correct.png" />
    </div>

    <p>{{ showReviewStatus() }}</p>

    <div class="content" v-if="reviewStatus == '0'">
      工作人员会尽快审核，请耐心等待
    </div>
    <div class="interval_line" />

    <div class="line-border-bottom">
      <div class="name">姓名</div>
      <div class="str">
        {{ customerCarList.name }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">车牌号码</div>
      <div class="str">
        {{ customerCarList.plateNumber }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">VIN码</div>
      <div class="str">
        {{ customerCarList.vin }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">开票日期</div>
      <div class="str">
        {{ customerCarList.ocrOrderDate.split(' ')[0] }}
      </div>
    </div>

    <div class="btnWarp">
      <div class="buttons" @click="onBackMyCar">完成</div>
    </div>
  </div>
</template>

<script>
import { callNative } from '@/utils'
import { getFindCustomerCarList, getManCarMemberInfo } from '@/api/api'
import storage from '../../utils/storage'

export default {
  data() {
    return {
      customerCarList: {},

      reviewStatus: '',
      memberInfo: {} // 会员信息
    }
  },
  mounted() {
    const model = storage.get('certificationList') || '{}'
    this.customerCarList = JSON.parse(model)
    this.reviewStatus = this.customerCarList.reviewStatus
    // this.getFindCustomerCarList();
    // this.getManCarMemberInfo()
  },
  methods: {
    // 查询是否有绑定车辆
    async getFindCustomerCarList() {
      const { data } = await getFindCustomerCarList({
        bindingStatus: 4,
        bindingType: 0
      })
      this.customerCarList = data.data[0]
      this.reviewStatus = this.customerCarList.reviewStatus
    },
    // 显示审核状态车辆状态，0：待审核，1：审核通过，2：审核被拒，3：已解绑8:废弃
    showReviewStatus() {
      if (this.reviewStatus == '0') {
        return '提交成功，待审核'
      }
      if (this.reviewStatus == '1') {
        return '恭喜您，认证成功'
      }
    },
    async getManCarMemberInfo() {
      const { data } = await getManCarMemberInfo({})
      this.memberInfo = data.data
    },
    // 返回
    onBackMyCar() {
      if (this.$route.query.carMemberRelId) {
        this.$router.go(-2)
      } else {
        this.$store.commit('showLoading')
        callNative('vehicleStatusChange', {
          bind: true,
          vin: this.customerCarList.vin,
          scene: this.$store.state.pageFromScene,
          source: this.$store.state.pageSource
        })
        storage.set('certificationList', JSON.stringify({}))
        setTimeout(() => {
          this.$store.commit('hideLoading')
          if (this.$store.state.pageFromScene === 'myCertification') {
            this.$router.replace({
              path: '/certification/my-certification'
            })
          } else {
            callNative('close', {})
          }
        }, 2000)
      }
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/buttons.less');

.unsubscribeSucceed {
  padding: 0 16px;
  img {
    width: 66px;
    height: 66px;
  }

  p {
    margin: 0;
    text-align: center;
    font-size: 16px;
    color: #000000;
    margin-top: 8px;
    line-height: 24px;
    font-family: 'Audi-Normal';
    margin-bottom: 4px;
  }
  .content {
    text-align: center;
    font-size: 12px;
    color: #999999;
    line-height: 20px;
    font-family: 'Audi-Normal';
  }

  .interval_line {
    margin-top: 24px;
    margin-left: -16px;
    margin-right: -16px;
    height: 8px;
    background: #f2f2f2;
    margin-bottom: 8px;
  }
  .line-border-bottom {
    width: 100%;
    display: flex;
    //   align-items: center;
    // margin-top: 30px;
    padding: 16px 0;
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 15px;
    .name {
      width: 80px;
      font-size: 16px;
      color: #000;
    }
    .str {
      font-family: 'Audi-Normal';
      font-size: 16px;
      color: #333;
    }
  }
  .btnWarp {
    width: 100%;
    background-color: #ffffff;
    // display: flex;
    // flex-direction: column;
    // justify-content: space-between;
    position: fixed;
    bottom: 0;
    padding-bottom: 38px;
    left: 16px;
    .checkbox_button {
      margin-left: 16px;
      margin-bottom: 5px;
      width: 18px;
      height: 18px;
      color: #000;
    }
  }
}
</style>
