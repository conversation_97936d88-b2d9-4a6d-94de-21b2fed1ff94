<template>
  <div class="modelline-wrapper">
    <div v-for="(item, idx) in currentVersionData" :key="idx" class="item" :class="{
              disabled: item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode == 'SLC0001',
            }" @click="toSelectModelLine(item, idx)">
      <div class="left c-font14">
        <div class="title c-bold">
          <div class="title-name"> {{ item.externalFullModelName }}</div>
          <div class="tag-tightstock" v-if="item.stockout === 1">库存紧张</div>
        </div>
        <div class="price-wrapper">
          <div class="price">
            <PreferentialPrice :preferential="item.msrp" :price="originPrice[item.modelUnicode]" />
          </div>
        </div>
        <div class="sellblip-wrapper" v-if="!isEmptyObj(item.cc)">
          <div class="sellblip" v-for="(sellpoint, idx) in item.cc.sellBlips" :key="idx">
            {{ sellpoint }}
          </div>
        </div>
      </div>

      <!-- car image -->
      <div class="img-wrapper" v-if="!isEmptyObj(item.cc)">
        <img :src="BaseConfigrationOssHost + item.cc.imageUrl | imgFix(450)">
      </div>
      <!-- 修改后的完整代码段 -->
      <div class="icon-equity-wrapper" v-if="item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode">
        <!-- SLC0004 热门车型 -->
        <div class="label-with-text"
             :class="'label-' + item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode.toLowerCase()"
             v-if="item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode == 'SLC0004'">
          <img src="../../assets/icon-remenchexing.png" alt="热门车型">
          <span class="text">{{ item?.children?.[0]?.labelChildren?.[0]?.salesLabelZh }}</span>
        </div>

        <!-- SLC0001 已售罄 -->
        <div class="label-with-text"
             :class="'label-' + item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode.toLowerCase()"
             v-if="item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode == 'SLC0001'">
          <img src="../../assets/icon-yishouqing.png" alt="已售罄">
          <span class="text">{{ item?.children?.[0]?.labelChildren?.[0]?.salesLabelZh }}</span>
        </div>

        <!-- SLC0002 十月交付 -->
        <div class="label-with-text"
             :class="'label-' + item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode.toLowerCase()"
             v-if="item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode == 'SLC0002'">
          <img src="../../assets/icon-shiyuejiaofu.png" alt="十月交付">
          <span class="text">{{ item?.children?.[0]?.labelChildren?.[0]?.salesLabelZh }}</span>
        </div>

        <!-- SLC0003 十一月交付 -->
        <div class="label-with-text"
             :class="'label-' + item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode.toLowerCase()"
             v-if="item?.children?.[0]?.labelChildren?.[0]?.salesLabelCode == 'SLC0003'">
          <img src="../../assets/icon-shiyiyuejiaofu.png" alt="十一月交付">
          <span class="text">{{ item?.children?.[0]?.labelChildren?.[0]?.salesLabelZh }}</span>
        </div>
      </div>

    </div>
    <div class="equity-img-wrapper" v-if="equityImgVisible">
      <div class="header">
        <div class="btn-back" @click="equityImgVisible = false">
          <img :src="require('../../assets/img/icon03.png')" alt="">
        </div>
      </div>
      <div class="img-wrapper">
        <img :src="equityUrl" alt="">
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import url from "@/config/url";
import { getCopCouponInfo, getModellineList, getOmdModels, getOmdModelsPrice, postOmdModelConfig } from "@/configratorApi";
import PreferentialPrice from "./components/preferentialPrice.vue";
import { callNative, isEmptyObj } from "@/utils";

export default {
  inject: ["checkLoginFn"],
  components: {
    PreferentialPrice
  },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      modelVersionList: [],
      equityImgVisible: false,
      equityUrl: "",
      originPrice: {
        "F08BAY-Y2026-V0": 349900, // "A5L Sportback 旗舰型"
        "F08BZG-Y2026-V0-GWAMWAM-GWAOWAO-GWA3WA3-GW1LW1L-GYEBYEB": 319900, // "A5L Sportback 智领型"
        "F08BZG-Y2026-V1-GWAMWAM-GWAOWAO-GWA3WA3-GW1LW1L-GYEBYEB": 319900, // "A5L Sportback 智领型"
        "F08BZY-Y2026-V0-GPFOPFO-GYEAYEA": 334900, // "A5L Sportback 首发限量版"
        "F08BZG-Y2026-V0-MLIA8G4-MHSW8IY": 279900, // A5L Sportback 豪华型
        "F08BZG-Y2026-V1-MLIA8G4-MHSW8IY": 279900, // A5L Sportback 豪华型
        "F08BAY-Y2026-V0-MHUDKS1-GWAMWAM-GWTZWTZ-GYEDYED": 399900, // A5L Sportback 旗舰智曜型
        "F08BZY-Y2026-V0": 309900, // A5L Sportback 尊享quattro型
        "F08BZY-Y2026-V0-GWAMWAM-GYECYEC-MHIM6NQ": 339900, // A5L Sportback 智领quattro型
      },
    };
  },
  computed: {
    ...mapGetters(["currentSeriesName", "currentSeriesId", "isCouponValid"]),
    ...mapState({
      userCoupon: (state) => state.configration.userCoupon,
      currentVersionData: (state) => state.configration.currentVersionData,
      currentVersion: (state) => state.configration.currentVersion,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentCarSeriesData: (state) => state.configration.currentCarSeriesData,
      carIdx: (state) => state.configration.carIdx,
      orderTime: (state) => state.configration.orderTime,
      equityPopupDisplayed: (state) => state.configration.equityPopupDisplayed,
    }),
  },
  // ... 其他代码
  activated() {
    // 从其他页面返回时执行的方法
    this.initCoupon();
  },
  watch: {
    $route(to, from) {
      // 判断是否是从其他页面返回当前页面
      // 替换成你的当前页面路由路径（如 '/modelline'）
      const currentPath = "/configration";
      if (to.path === currentPath && from.path !== currentPath) {
        // 从其他页面返回时执行
        this.initCoupon();
      }
    }
  },
  async mounted() {
    console.log("%c A5L carVersion mounted", "font-size:16px;color:green;");
    let currentCarSeriesData = {
      seriesName: "a5l",
      customSeriesId: "b51aead3-2a06-43b8-86a8-fcb49ce6f985",
      seriesId: "60527959-68b6-41d3-9201-0a9667d9e734",
      customSeriesCode: "F0",
      seriesCode: "F0",
    };
    await this.$store.commit("updateCurrentCarSeriesData", currentCarSeriesData);
    await this.$store.commit("updateCarIdx", "e0172bf8-d547-4ad7-adf7-1292f53ea0df");

    // 获取早鸟包
    await this.initCoupon();

    this.$store.commit("showLoading");
    // 初始化车型数据
    const modelLineMap = await this.loadModelLineList();

    const result = await getOmdModels("F08B");
    let models = result.data.data.result;

    // 填充价格
    for (const item of models) {
      const response = await getOmdModelsPrice(item.children[0].modelUnicode, item.seriesCode);
      const priceData = response.data.data.result[0];
      Object.assign(item, priceData);
      item["cc"] = modelLineMap.get(item.modelUnicode);
    }
    models.sort((a, b) => a.msrp - b.msrp);
    console.log("%c A5L models:", "font-size:16px;color:green;", models);
    this.$store.commit("updateCurrentVersionData", models);
    this.$store.commit("hideLoading");
  },

  async beforeDestroy() {
    console.log("%c A5L carVersion beforeDestroy", "font-size:16px;color:green;");
  },

  methods: {
    isEmptyObj,
    // 选中
    async toSelectModelLine(item, idx) {
      console.log("%c A5L SelectModelLine:", "font-size:16px;color:green;", item);

      this.$store.commit("updateCurrentModelLineData", item);

      const carDefaultConfig = await postOmdModelConfig({
        brandCode: "A",
        children: [],
        modelUnicode: item.children[0].modelUnicode,
        mstMgrpId: "",
        seriesCode: item.seriesCode
      });
      if (carDefaultConfig.data.data.code !== "20000") return this.$toast.fail(carDefaultConfig.data.data.message);
      this.$store.commit("updateCurrentVersion", carDefaultConfig.data.data.result[0]);

      // 再次调用，用作初始化
      const defaultColorExterieur = this.currentVersion.tempDefaultColorExterieur;
      const paramDto = await this.$store.dispatch("getOmdModelConfigParams", { item: defaultColorExterieur, key: "AADD" });
      const carConfig = await postOmdModelConfig(paramDto);
      if (carConfig.data.data.code !== "20000") return this.$toast.fail(carConfig.data.data.message);
      this.$store.commit("updateCurrentVersion", carConfig.data.data.result[0]);

      // 处理车型所有组合包
      await this.$store.dispatch("setEquipmentGroup");

      // 处理轮毂组合包
      await this.$store.dispatch("setRadEquipmentGroup");

      // 处理内饰组合包
      await this.$store.dispatch("setIIEquipmentGroup");

      // 初始化进入车型配置界面的数据
      await this.$store.dispatch("doA5ExteriorPageInitAction");

      // 初始化订金
      this.$store.dispatch("getDeliveryTimeAndDeposit");

      // 计算总价
      await this.$store.dispatch("computeA5LTotalPriceAndEquityPrice", { isCouponValid: this.isCouponValid });

      // 重新初始化内饰数据
      // await this.$store.dispatch("doA5InteriorPageInitAction");

      // 埋点
      this.clickVersionSensors(item, idx);
      console.log("当前路由参数：", this.$route.query);
      // 修正大小写/拼写 
      const { orderStatus, orderId, ccid, from, getback, action } = this.$route.query;
      this.$router.push({
        path: "/a5lConfigrationContainer",
        query: {
          orderStatus,
          orderId,
          ccid,
          from,
          getback,
          action
        }
      });
    },

    // 埋点
    clickVersionSensors(item, idx) {
      const param = {
        source_module: "H5",
        car_series: "A5L",
        car_version: item.styleName,
        delivery_type: "定制交付", // 快速交付|定制交付
        select_price: `${item.price}起`,
        select_index: idx + 1,
        is_hot: item.recommendStatus === 1
      };

      // console.log(param, item)
      this.$sensors.track("CC_SelectCar_Version_Click", param);
    },

    async loadModelLineList() {
      const a5FromCC = await getModellineList(this.currentSeriesId);
      const result = new Map();
      for (const item of a5FromCC.data.data) {
        result.set(item.omdModelUnicode, item);
      }
      this.$store.commit("updateModelLineMap", result);
      console.log("%c A5L updateModelLineMap:", "font-size:16px;color:green;", result);
      return result;
    },

    async initCoupon() {
      const { ccid, fromPage } = this.$route.query;
      const token = localStorage.getItem("token");
      if (token) {
        const userInfo = await callNative("getAudiUserInfo", {});
        console.log("%c A5L userInfo:", "font-size:16px;color:green;", userInfo);
        if (userInfo) {
          let couponInfo;
          if (ccid && fromPage === "orderDetail") {
            couponInfo = await getCopCouponInfo(userInfo.userId, "F0", ccid);
          } else {
            couponInfo = await getCopCouponInfo(userInfo.userId, "F0");
          }
          console.log("%c A5L couponInfo:", "font-size:16px;color:green;", couponInfo.data.data);
          if (couponInfo.data && couponInfo.data.data && Array.isArray(couponInfo.data.data) && couponInfo.data.data.length > 0) {
            await this.$store.commit("updateUserCoupon", couponInfo.data.data[0]);
          }
        }
      }
    },
  }
};
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.modelline-wrapper {

  .item {
    position: relative;
    margin-top: 18px;
    background-color: #fff;
    font-size: 12px;
    padding: 14px 0;
    // border: 1px solid transparent;
    overflow: hidden;

    &.selected {
      border: 1px solid #000;
    }

    &.disabled {
      background-color: #F2F2F2;
      pointer-events: none;
    }

    .left {
      min-height: 100px;

      .title {
        position: relative;
        line-height: 20px;
        padding-left: 24px;

        > .title-name {
          display: inline-block;
        }

        > .tag-tightstock {
          background: #855B3F;
          color: #fff;
          font-size: 10px;
          line-height: 18px;
          display: inline-block;
          padding: 0 5px;
          margin-left: 10px;
          transform: scale(0.8);
          transform-origin: 0 center;
        }

        > .tag-wrapper {
          position: absolute;
          right: 0;
          top: 50%;
          width: 40px;
          transform: translateY(-50%);
          background: #EB0D3F;
          color: #fff;
          font-weight: normal;
          font-size: 10px;
          line-height: 14px;
          box-sizing: border-box;
          text-align: right;
          padding-right: 7px;

          &::before {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            top: 0;
            background-color: #fff;
            border-top: 7px solid transparent;
            border-bottom: 7px solid #EB0D3F;
            border-left: 4px solid transparent;
            border-right: 4px solid #EB0D3F;
          }
        }
      }

      .price-wrapper {
        display: flex;
        align-items: center;
        margin-top: 2px;

        .price {
          color: #4C4C4C;
          line-height: 20px;
          padding-left: 24px;

          .orginal-price {
            font-size: 12px;
            transform: scale(0.8);
            transform-origin: 0 center;
            display: block;
          }

          .price-before-preferential {
            font-size: 12px;
            margin-right: 10px;
            text-decoration: line-through;
            color: #999
          }

          .price-after-preferential {
            color: #F50538;
          }
        }

        .red-tag-wrapper {
          display: flex;
          align-items: center;
          border: 1px solid #EB0D3F;
          height: 14px;
          margin-left: 5px;

          > .tag-wrapper {
            height: 100%;

            img {
              width: auto;
              height: 100%;
              vertical-align: top;
            }
          }

          > .text {
            color: #EB0D3F;
            font-size: 10px;
            line-height: 14px;
            padding: 0 4px;
          }
        }
      }

      // 亮点
      .sellblip-wrapper {
        margin-top: 14px;
        color: #999;
        font-size: 12px;
        margin-left: 30px;

        > .sellblip {
          position: relative;
          line-height: 18px;

          &::before {
            position: absolute;
            content: '· ';
            right: 101%;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }

    .img-wrapper {
      position: absolute;
      bottom: -9%;
      right: -7%;
      width: 62%;

      &.a7mr2 {
        width: 58%;
        right: -5%;
      }

      &.q6Scale {
        width: 60%;
      }

      &.q6rsScale {
        width: 59%;
      }
    }

    .icon-equity-wrapper {
      position: absolute;
      top: 8px;
      right: 14px;
      padding: 6px 7px 6px 10px;
      display: flex;
      align-items: center;

      img {
        height: 20px;
        width: auto;
        object-fit: contain;
      }

      // 在原有的style标签内添加
      .label-with-text {
        position: relative;
        display: inline-block;
        margin-right: 8px; /* 标签之间的间距 */
        transform: scale(0.7);
        transform-origin: top right;

        img {
          display: block;
          width: auto;
          height: 26px; /* 根据实际图片尺寸调整 */
        }

        .text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 12px;
          white-space: nowrap;
          pointer-events: none; /* 避免文字遮挡图片点击 */
        }

        // SLC0001 文字颜色
        &.label-slc0001 .text {
          color: #808080;
        }

        // SLC0002、SLC0003 文字颜色
        &.label-slc0002 .text,
        &.label-slc0003 .text {
          color: #787878;
        }

        &.label-slc0004 .text {
          color: #DD7D5D;
        }
      }


      // .van-icon {
      //   // font-size: 12px;
      //   // color: #855B3F;
      //   margin-left: 4px;
      // }

      // &::after {
      //   position: absolute;
      //   right: 100%;
      //   top: 0;
      //   border-left: 6px solid transparent;
      //   border-right: 6px solid #F9F1E6;
      //   border-top: 13px solid #F9F1E6;
      //   border-bottom: 14px solid transparent;
      //   display: block;
      //   content: '';
      // }
    }
  }
}

.equity-img-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 999;
  background-color: #000;

  > .header {
    padding-top: 35px;
    height: 44px;
    background-color: #000;
    display: flex;
    align-items: center;

    .btn-back {
      width: 30px;
      margin-left: 12px;

      img {
        filter: invert(1);
        width: 100%;
      }
    }
  }

  > .img-wrapper {
    text-align: center;
    height: calc(100% - 79px);
    display: flex;
    align-items: center;
    overflow-y: auto;

    img {
      width: 100%;
    }
  }
}
</style>
