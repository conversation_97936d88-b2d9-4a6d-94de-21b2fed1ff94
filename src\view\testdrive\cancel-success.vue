<template>
  <div class="unsubscribeSucceed">
    <div class="circle">
      <van-icon
        name="success"
        size="20"
      />
    </div>
    <p>{{ context[type].tip }}</p>
    <div
      class="tips"
      v-if="env==='minip'"
    >
      <div>注意事项：</div>
      <div>可在APP查看预约及后续事项</div>
      <!-- <div>2.请必须在APP上传证件</div> -->
    </div>
    <!-- <div class="box">
      <div class="btnWarp">
        <div
          class="buttons"
          @click="confim"
        >
          确认完成
        </div>
        <div class="bt" />
      </div>
    </div> -->
    <div class="btnWarp">
      <div
        class="buttons"
        @click="chakan"
      >
        {{ context[type].buttontext }}
      </div>
      <!-- <div class="buttons2">
        查看预约信息
      </div> -->
      <div class="bt" />
    </div>
    <model
      :modalshow.sync="modalshow"
      @update:modalshow="submit"
      title="在App内打开"
      confirm-text="复制链接"
      content="点击下方复制链接，前往浏览器打开"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import { Icon, Toast } from 'vant'
import model from '@/components/model.vue'

Vue.use(Icon)
Vue.use(Toast)
export default {
  components: {
    model
  },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      type: 0,
      modalshow: false,
      context: [
        { title: '预约成功', tip: '恭喜您预约成功！', buttontext: '查看我的预约' },
        { title: '预约取消成功', tip: '你预约试驾已取消成功！', buttontext: '返回我的预约' },
        { title: '评价成功', tip: '评价成功！', buttontext: '返回预约试驾订单' }
      ]
    }
  },
  mounted() {
    this.type = this.$route.query.type || 0
    if (this.type >= 0) {
      this.$store.state.title = this.context[this.type].title
    }
  },
  computed: {
    ...mapState({
      env: 'env'
    })
  },
  methods: {
    chakan() {
      if (this.env === 'minip') {
        this.modalshow = true
        return
      }
      this.$router.push({
        path: this.fromType ? '/testdrive/appointment-list?fromType=fromPurple' : '/testdrive/appointment-list'
      })
      if (+this.type === 0) {
        const params = {
          page_name: '预约成功'
        }
        this.$sensors.track('checkMyAppointments', params)
      }
    },
    confim() {
      this.$bridge.callHandler('goHome', {}, (err, data) => {
        if (err) {
          // 发生错误
          // alert(err);
          console.log('goHomeError=', err)
          return
        } // 成功返回结果
        console.log('goHome=', data)
      })
    },
    submit(isSubmit) {
      if (isSubmit) {
        // 复制到粘贴板
        const copyResult = this.InsertShearPlate()
        this.modalshow = false
        if (copyResult) {
          Toast({
            type: 'fail',
            message: '已复制到剪贴板',
            icon: require('../../assets/img/success.png')
          })
        } else {
          Toast({
            type: 'fail',
            message: '复制失败',
            icon: require('../../assets/img/error.png')
          })
        }
      }
    },
    InsertShearPlate() {
      // 复制到粘贴板
      const text = 'https://app.saic-audi.mobi/index.html#/?channel=miniapp_testdrive'
      const copyString = text
      // 创建input标签存放需要复制的文字
      const myInput = document.createElement('input')
      // 把文字放进input中，供复制
      myInput.value = copyString
      document.body.appendChild(myInput)
      // 选中创建的input
      myInput.select()
      // 执行复制方法， 该方法返回bool类型的结果，告诉我们是否复制成功
      const copyResult = document.execCommand('copy')
      // 操作中完成后 从Dom中删除创建的input
      document.body.removeChild(myInput)
      return copyResult
    }
  }
}
</script>

<style scoped lang="less">
    @import url("../../assets/style/scroll.less");
    @import url("../../assets/style/buttons.less");

    .unsubscribeSucceed {
        padding: 16px;

        .circle {
            position: relative;
            width: 72px;
            height: 72px;
            border: 1px solid #000000;
            border-radius: 50%;
            margin: 110px auto auto auto;

            /deep/.van-icon-success {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                margin: auto;

                &::before {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    width: 20px;
                    height: 18px;
                    bottom: 0;
                    margin: auto;
                }
            }
        }

        p {
            text-align: center;
            font-size: 18px;
            color: #000000;
            line-height: 32px;
            margin-top: 25px;
        }
        .tips{
          text-align: left;
          font-size: 14px;
          line-height: normal;
          margin: 80px auto;
          width: 60vw;
        }
        .btnWarp {
            position: fixed;
            z-index: 2;
            height: 56px;
            width: 100%;
            bottom: 0;
            padding-bottom: 50px;
            background: #fff;

            .buttons2 {
                top: 60px;
            }
        }
    }
</style>
