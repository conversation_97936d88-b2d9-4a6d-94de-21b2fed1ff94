<template>
  <div class="interior-wrapper">
    <div class="interior-headImg" style="position: fixed;z-index: 9;">
      <img @click="handleShow3D(2)" class="interior-3dIcon" src="../../assets/img/icon02.png" v-if="!isMinip && has3D"
        alt="">
      <img class="bgimg" v-if="interieurImg" :src="interieurImg | imgFix(640, true)" alt="" @load="endLoad">
    </div>
    <div class="interior-main" style="margin-top: 230px;">
      <!-- 座椅,面料,饰条概念 -->
      <div class="interior-summary">
        <div class="summary-item chair-summary" v-if="currentInteriorChair&&idx==='0'">
          <word-detail :title="currentInteriorChair.optionName || ''" :id="currentInteriorChair.optionId"
            :show="currentIndex === '3'" />
          <div class="summary-price" v-if="hasPrice">
            {{ currentInteriorChair.price | finalFormatPrice }}
          </div>
        </div>
        <div class="summary-delimiter" v-if="currentInteriorChair&&idx==='0'" />
        <div class="summary-item sib-summary">
          <word-detail :title="currentSibColorInterieur.description || ''" :id="currentSibColorInterieur.sibOptionId"
            :show="currentIndex === '3'" />

          <div class="summary-price" v-if="hasPrice">
            {{ currentSibColorInterieur.price | finalFormatPrice }}
          </div>
        </div>
        <div class="summary-delimiter" v-if="currentSibColorInterieur" />
        <div class="summary-item eih-summary" v-if="currentInteriorEih">
          <word-detail :title="currentInteriorEih.optionName ? (currentInteriorEih.optionName) : ''"
            :id="currentInteriorEih.optionId" :show="currentIndex === '3'" />
          <div class="summary-price" v-if="hasPrice">
            {{ currentInteriorEih.price | finalFormatPrice }}
          </div>
        </div>
      </div>
      <!-- 座椅,面料,饰条详细信息以及选择 -->
      <div class="interior-box">
        <!-- 座椅 -->
        <div class="interior-chair" v-if="hasPrice&&idx==='0'">
          <div class="title chair-title">
            座椅
          </div>
          <div class="slide chair-slide">
            <div v-for="(item,index) in interiorChairList" :key="index" @click="selectedChair(item)" :class="[
              'slide-imgItem',
              item.optionCode ===
                (currentInteriorChair && currentInteriorChair.optionCode)
                ? 'selected'
                : '',
            ]">
              <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(140, true)" alt="">
            </div>
          </div>
          <div class="title subtitle">
            产品功能亮点
          </div>
          <div class="text interior-chair-optionName" v-if="currentInteriorChair">
            {{ currentInteriorChair.optionName }}
          </div>
          <div class="text interior-chair-price" v-if="currentInteriorChair">
            {{ currentInteriorChair.price | finalFormatPrice }}
          </div>
        </div>
        <!-- 面料 -->
        <div class="interior-sib">
          <div class="title sib-title">
            面料
          </div>
          <div class="slide sib-slide">
            <div v-for="item in sibColorInterieurList" :key="item.sibInterieurId + item.sibInterieurId"
              class="sib-slide-item">
              <div v-if="showInterieurSib(item)">
                <img v-if="item.opacityBool" @click="handleChange(item)"
                  style="width: 90px;height: 90px;position: absolute;" src="@/assets/bg.png" alt="" />
                <div @click="selectedSibColorInterieur(item)" style="" :class="[
                  'slide-imgItem',
                  item.sibInterieurId ===
                    (currentSibColorInterieur &&
                      currentSibColorInterieur.sibInterieurId)
                    ? 'selected'
                    : '',
                ]">
                  <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(140, true)" alt="">
                </div>
                <div class="optionName sib-optionName" v-show="item.sibInterieurId ===
                currentSibColorInterieur.sibInterieurId">
                  <word-detail :title="item.description || ''" :id="'eih_' + item.sibInterieurId" :show="item.sibInterieurId ===
                  currentSibColorInterieur.sibInterieurId &&
                  currentIndex === '3'" />
                  <div class="price" v-if="hasPrice">
                    {{ currentSibColorInterieur.price | finalFormatPrice }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div style="color:#999;font-size:12px;margin-left:16px;margin-bottom:10px;"
            :class="[(('498BZY001,498BZY002,498B2Y001,498B2Y002').includes(selectCarInfo.modelLineCode)&&currentSibColorInterieur.sibInterieurCode==='N2R-AW')?'opcity1':'opcity0']">
            受产能限制，计划于01月17日起暂停供应
          </div>
          <!-- 饰条 -->
          <div class="interior-eih" v-if="hasPrice || idx == 2">
            <div class="title eih-title">
              饰条
            </div>
            <div class="slide eih-slide">
              <div v-for="item in interiorEihList" :key="item.optionId + item.optionCode" class="eih-slide-item">
                <div @click="selectedEih(item)" :class="[
                  'slide-imgItem',
                  item.optionId ===
                    (currentInteriorEih && currentInteriorEih.optionId)
                    ? 'selected'
                    : '',
                ]">
                  <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(140)" alt="">
                </div>
                <div class="optionName eih-optionName" v-show="item.optionId === currentInteriorEih.optionId">
                  <word-detail ref="refWordDetail" :title="item.optionName || ''" :id="'sib_' + item.optionId" :show="
                    item.optionId === currentInteriorEih.optionId &&
                      currentIndex === '3'
                  " />
                  <div class="price">
                    {{ item.price | finalFormatPrice }}
                  </div>
                </div>
              </div>
            </div>
            <div style="color:#999;font-size:12px;margin-left:16px;margin-bottom:10px;"
              :class="[(('498BZY001,498BZY002').includes(selectCarInfo.modelLineCode)&&currentInteriorEih.optionCode==='5MK')?'opcity1':'opcity0']">
              受产能限制，计划于01月17日起暂停供应
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <option-popup v-if="showOptionPopup" /> -->
    <van-popup v-model:show="show">
      <div class="ex-popup">
        <main>
          <span>{{ popupDesc }}</span>
        </main>
        <footer @click="iknowHandle">
          <div class="popup-iknow">
            我知道了
          </div>
        </footer>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import url from '@/config/url'
import WordDetail from '@/components/word-detail.vue'
import OptionPopup from '@/components/option-popup.vue'
import {
  callNative, arraySortByKeys, getUrlParamObj, checkV2
} from '@/utils'
import { getCarConfigcc, getQ5EConfigcc, getQ6cc } from '@/api/api'
import {
  resolveConflict, outputChangeList, outputCurrentOptionsList, outputData
} from '@/utils/sp'

import { patchesHandleColor } from '@/view/configration/fix_configration/ccFn.js'

const { env } = getUrlParamObj()
const isMinip = env === 'minip'
export default {
  components: { WordDetail, OptionPopup },
  props: {
    currentIndex: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      opacityBool: false,
      popupDesc: '此面料与当前所选的车型颜色有冲突，请先修改颜色',
      show: false,
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      isMinip: isMinip,
      conflicts: [],
      has3D: false,
      hasPrice: false
    }
  },
  async created() {
    this.$store.commit('showLoading')
  },
  computed: {
    ...mapState([
      'currentTabIndex',
      'idx',
      'showOptionPopup',
      'selectCarInfo',
      'currentInteriorChair',
      'currentInteriorEih',
      'currentSibColorInterieur',
      'currentOptionsList',
      'clickOption',
      'finishRequestInterior',
      'ccConfigration',
      'currentExColor',
      'currentModelHub',
      'sibColorInterieurList',
      'currentPrice',
      'allSibColorInterieurList',
      'allInteriorEihList',
      'allInteriorChairList',
      'privateOrderList',
      'allPrivateOrderList'
    ]),
    ...mapGetters([
      'interiorChairList',
      'interiorEihList',
      'allOptionsItems'
    ]),
    interieurImg() {
      const { seriesName, modelLineCode } = this.selectCarInfo
      if (!seriesName) { return }
      const series = seriesName.toLowerCase()
      if (series === 'a7l') {
        this.changeHas3D(true)
      } else if (series === 'q5e') {
        this.changeHas3D(true)
      } else {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.has3D = true
      }
      if (this.currentInteriorChair && series != 'q5e') {
        let arr = []
        let img
        if (this.currentInteriorChair.optionCode === 'Q4Q' && '498B2Y004,498B2Y003'.includes(this.selectCarInfo.modelLineCode)) {
          return `${this.BaseConfigrationOssHost}/ccpro-backend/${series}/tmp/interieur/Q4Q.png`
        }
        const hsdp = this.currentOptionsList.find((res) => res.optionCode === '6NQ')
        if (hsdp) {
          arr.push(hsdp.optionCode)
        }

        // 498BZY007 a7l 黑武士 45
        // 498B2Y007 a7l 黑武士 55
        // 498BZY008 a7l 影武士45
        // 498B2Y008 a7l 影武士55
        // 498BZY002  奥迪A7L 见远型 曜黑套装
        if (['498BZY002','498BZY007','498B2Y007', '498BZY008','498B2Y008'].includes(this.selectCarInfo.modelLineCode)) {
          arr.push('6NQ')
        }
        if (Object.keys(this.currentSibColorInterieur).length > 0 && Object.keys(this.currentInteriorChair).length > 0 && Object.keys(this.currentInteriorEih).length > 0) {

          if (this.idx == 2) {
            // 座椅+面料+内饰颜色+饰条
            // https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/q6/tmp/interieur/G6IBAY001/Q2J-N5D-TT-7TL.png
            arr = [this.currentInteriorChair.optionCode, this.currentSibColorInterieur.sibInterieurCode, this.currentInteriorEih.optionCode]
            img = arr.join('-')
            return `${this.BaseConfigrationOssHost}/ccpro-backend/${series}/tmp/interieur/${modelLineCode}/${img}.png`
          } else {
            arr = [...arr, this.currentSibColorInterieur.interieurOptionCode, this.currentInteriorChair.optionCode, this.currentInteriorEih.optionCode]
            img = arr.join('-')
            if ('498BZG004'.includes(modelLineCode)) {
              img = 'N5J-' + img
            }
            return `${this.BaseConfigrationOssHost}/ccpro-backend/${series}/tmp/interieur/${img}.png`
          }
        }
        return null
      }
      if (series === 'q5e') {
        let arr = []
        let img
        if (Object.keys(this.currentSibColorInterieur).length > 0 && Object.keys(this.currentInteriorEih).length > 0) {
          arr = [...arr, this.currentSibColorInterieur.sibInterieurCode, this.currentInteriorEih.optionCode]

          img = arr.join('-')
          //q5e车型 内饰大图 区分锦衣和机甲 锦衣的2D图是白顶棚，机甲是黑色
          if (this.$store.state.selectCarInfo.modelLineName.includes('机甲')) {
            img = '6NQ-' + img
          }

          return `${this.BaseConfigrationOssHost}/ccpro-backend/q5e/tmp/interieur/${img}.png`
        }
      }
      return null
    }
  },
  watch: {
    currentExColor: {
      handler(next) {
        if (next) {
          if (!checkV2()) {
            this.conflicts = []
            for (const val of next.optionRelates) {
              if (val.relateType === 'conflict') {
                this.conflicts.push(val.optionRelateCode)
              }
            }
            this.reSelectedSibColorInterieur()
            patchesHandleColor(this)
          }
        }
      },
      deep: true
    },
    async currentInteriorChair(next) {
      if (next) {
        if (checkV2()) {
          await this.$store.dispatch('measureQuery', { step: '4' })
        }
      }
    },
    async currentInteriorEih(next) {
      if (next) {
        if (checkV2()) {
          await this.$store.dispatch('measureQuery', { step: '6' })
        }
      }
    },
    currentSibColorInterieur: {
      async handler(next) {
        if (checkV2()) {
          await this.$store.dispatch('measureQuery', { step: '5' })
        } else {
          const depends = []
          const conflicts = []
          const conflictsall = []
          if (next.sibInterieurRelates) {
            for (const val of next.sibInterieurRelates) {
              if (this.idx === '1' || this.idx === '2') {
                if ('conflict'.includes(val.relateType)) {
                  conflictsall.push(val.optionRelateCode)
                }
                if ('depend'.includes(val.relateType)) {
                  depends.push(val.optionRelateCode)
                  if(this.currentTabIndex == 3 && !this.showOptionPopup) this.$store.commit('setShowOptionPopup', true)
                }
              } else {
                conflictsall.push(val.optionRelateCode)
              }
              if (val.relateType === 'conflict' && val.optionRelateCategory === 'EIH') {
                conflicts.push(val.optionRelateCode)
              }
            }
            if (this.idx != 2) {
              const newArrays = this.allInteriorEihList.filter((e) => !conflicts.includes(e.optionCode) && (e.status === 2 || e.status === 1 || e.show === true))
              if(this.idx == 1) {
                newArrays.length && newArrays[0] && this.$store.commit('setCurrentInteriorEih', newArrays[0])
                newArrays.length && this.$store.commit('setInteriorEihList', newArrays)
              } else {
              this.$store.commit('setCurrentInteriorEih', newArrays[0])
              this.$store.commit('setInteriorEihList', newArrays)
              }
            }
            // debugger
            if (this.idx === '1' || this.idx == 2) {
              this.$store.commit('setCurrentOptionsList', [])

              // const YEA = this.$store.state?.allPrivateOrderList.find(e => e.optionCode == "YEA");
              // if (this.idx == 2 && YEA) {
              //   this.$store.commit('setCurrentOptionsList', [...this.currentOptionsList, YEA])
              // }

              if (this.idx != 2) {
                const newArrays1 = this.allPrivateOrderList.filter((e) => !conflictsall.includes(e.optionCode))
                this.$store.commit('setPrivateOrderList', newArrays1)
              }

              if (depends.length > 0) {
                this.$store.commit('setClickOption', next)
              }
              if (conflictsall && conflictsall.length > 0 && this.idx == 2) {
                // let newArrays = JSON.parse(JSON.stringify(this.allPrivateOrderList))
                // newArrays?.forEach(e => {
                //   if (conflictsall.includes(e.optionCode)) {
                //     e.conflict = "此装备与当前所选内饰冲突，请选中其他内饰后尝试"
                //   } else {
                //     e.conflict = ""
                //   }
                // });
                // this.$store.commit('setPrivateOrderList', newArrays)
              }

              conflictsall.forEach((r) => {
                const findOption = this.currentOptionsList.findIndex(
                  (item) => item.optionCode === r
                )
                if (findOption !== -1) {
                  this.$store.commit('setDelOption', findOption)
                }
              })
            }
          }
        }
      },
      deep: true,
      immediate: true
    },
    sibColorInterieurList: {
      handler(next) {
        if (!checkV2()) {
          this.reSelectedSibColorInterieur()
        }
      },
      deep: true
    },
    currentOptionsList: {
      async handler(newData, oldData) {
        if (!checkV2()) {
          const newCurrentOptionsList = newData
          let flag
          if ((newCurrentOptionsList instanceof Array) && newCurrentOptionsList.length > 0) {
            // 存在选装包时,判断依赖关系
            for (const item of newCurrentOptionsList) {
              if (item.category === 'PACKET') {
                const _obj = {
                  clickOption: item,
                  allOptionsItems: this.allOptionsItems,
                  selectCarInfo: this.selectCarInfo,
                  currentOptionsList: newCurrentOptionsList,
                  currentInteriorChair: this.currentInteriorChair,
                  currentSibColorInterieur: this.currentSibColorInterieur,
                  price: item.price
                }
                const _result = resolveConflict(_obj)
                if ((_result.tempdependList instanceof Array) && _result.tempdependList.length > 0) {
                  // 需要深拷贝一下,否则会无限循环
                  const tempOptionList = JSON.parse(JSON.stringify(newCurrentOptionsList))
                  const parma = {
                    equipment: item,
                    conflicts: _result.conflictList,
                    depends: _result.tempdependList,
                    sibSelected: this.currentSibColorInterieur,
                    eihSelected: this.currentInteriorEih,
                    selectedPackageEquipment: tempOptionList,
                    packagesList: this.privateOrderList,
                    composeVosList: this.allSibColorInterieurList,
                    composeSibColorList: this.allInteriorChairList
                  }
                  const obj = outputData(parma)
                  obj.VosListEvent && this.$store.commit('setInteriorChairList', obj.VosListEvent)
                  if (this.idx == 2) {
                    return
                  } else {
                    obj.SibColorListEvent && this.$store.commit('setSibColorInterieurList', obj.SibColorListEvent)
                  }
                  flag = true
                }
              }
            }
            // 已经处理过依赖关系,则无需还原内饰,否则进行还原操作(如果不是选装包且没有依赖互斥关系，则不还原？待测试)   添加，只有选装包会存在冲突情况，故如果点击的是选装件，则不需要重置
            // q5e暂时不执行这个，待测试？？
            if (this.idx === '0') {
              if (!flag && this.clickOption.optionRelates !== null && this.clickOption.category === 'PACKET') {
                const a = this.clickOption?.optionRelates?.find((item) => 'VOS,EIH,F_SIB_COLOR_INTERIEUR'.includes(item.optionRelateCategory))
                a && this.$store.dispatch('getAllInterior', 'noeih')
              }
            }
          } else {
            if (this.idx == 2) {
              return
            }
            // 选装包列表为空时,还原内饰(如果不是选装包且没有依赖互斥关系，则不还原？待测试)  添加，只有选装包会存在冲突情况，故如果点击的是选装件，则不需要重置
            if (this.clickOption.optionRelates !== null && this.clickOption.category === 'PACKET') {
              const a = this.clickOption?.optionRelates?.find((item) => 'VOS,EIH,F_SIB_COLOR_INTERIEUR'.includes(item.optionRelateCategory))
              a && this.$store.dispatch('getAllInterior', 'noeih')
            }
          }
        } else {
          await this.$store.dispatch('measureQuery', { step: '7', noQuery: "noQuery" })
        }
      },
      deep: true
    }
  },
  mounted() {
    patchesHandleColor(this)
  },
  methods: {
    iknowHandle() {
      this.show = false
    },
    changeHas3D(val) {
      this.has3D = val
      this.hasPrice = val
    },
    reSelectedSibColorInterieur() {
      // 如果当前面料不在冲突项，则不变
      if (!this.conflicts.includes(this.currentSibColorInterieur.sibInterieurCode)) {
        return
      }
      // 有默认面料则选中默认的
      const tmp = this.sibColorInterieurList.find((e) => [1, '1'].includes(e.defaultConfig))
      if (!this.conflicts.includes(tmp.sibInterieurCode)) {
        this.selectedSibColorInterieur(tmp)
        return
      }
      // 没有默认面料的则选第一个不在冲突项的
      for (const val of this.sibColorInterieurList) {
        val.defaultConfig
        if (!this.conflicts.includes(val.sibInterieurCode)) {
          this.selectedSibColorInterieur(val)
          break
        }
      }
    },
    showInterieurSib(item) {
      const modelLineCode = this.selectCarInfo.modelLineCode
      const arr = ["G6ICAY001", "G6ICBY001"]
      const condition = arr.includes(modelLineCode)
      if (condition) {
        return true
      }

      if (this.currentOptionsList.find((res) => res.optionCode === '6NQ')) {
        if ('FF,FH'.includes(item.interieurOptionCode)) {
          return false
        }
      }
      if ('498B2Y004,498B2Y002'.includes(this.selectCarInfo.modelLineCode) && 'FF,FH'.includes(item.interieurOptionCode)) {
        return false
      }
      for (const val of this.conflicts) {
        if (val === item.sibInterieurCode) {
          return false
        }
      }
      return true
    },
    endLoad() {
      this.$store.commit('hideLoading')
    },
    selectedChair: function (item) {
      this.sensors(item, null, null)
      this.$store.commit('setCurrentInteriorChair', item)
    },
    selectedEih: function (item) {
      this.sensors(null, null, item)
      this.$store.commit('setCurrentInteriorEih', item)
    },
    handleChange() {
      // this.showOptionPopup = true
      this.show = true
    },
    selectedSibColorInterieur(item) {
      // this.sensors(null, item, null)
      this.$store.commit('setCurrentSibColorInterieur', item)
    },
    handleShow3D: async function (k) {
      // 注册一个方法, 同步从3d配置器携带的信息
      // const res = callNative('ccInfoPage', {})
      // if (res.params) {
      //   const paramArr = res.params.split('=')
      //   if (paramArr[1] === 'green') {
      //     this.currentExteriorIndex = 0
      //   }
      //   if (paramArr[1] === 'blue') {
      //     this.currentExteriorIndex = 1
      //   }
      // }
      // 跳转到3d配置器页面
      this.$store.commit('showLoading')
      const { seriesName } = this.selectCarInfo
      const series = seriesName.toLowerCase()

      const currentModelCode = this.selectCarInfo.modelLineCode
      const currentExCode = this.currentExColor.optionCode
      let currentInCode = []
      if (series === 'a7l') {
        this.currentInteriorChair && currentInCode.push(this.currentInteriorChair.optionCode)
        this.currentSibColorInterieur && currentInCode.push(this.currentSibColorInterieur.sibOptionCode)
        this.currentSibColorInterieur && currentInCode.push(this.currentSibColorInterieur.interieurOptionCode)
      } else {
        this.currentSibColorInterieur && currentInCode.push(this.currentSibColorInterieur.interieurOptionCode)
        this.currentSibColorInterieur && currentInCode.push(this.currentSibColorInterieur.sibOptionCode)
        // 这几个面料默认7座，选了WE8就是6座
        if ('G4IBC3001,G4ICC3001'.includes(currentModelCode) && 'EO-N4X,DS-N4X,AJ-N4X,TO-N7K'.includes(this.currentSibColorInterieur.sibInterieurCode)) {
          if (!this.currentOptionsList.find((option) => option.optionCode === 'WE8')) {
            currentInCode.push('seat7')
          }
        }
      }
      currentInCode = currentInCode.join('+')
      const currentHub = this.currentModelHub.optionCode

      const currentOptions = this.currentOptionsList.map((item) => item.optionCode) || []
      currentOptions.push('HSP') // iframe隐藏热点
      if (series === 'a7l') {
        // 6NQ 黑色顶棚
        const BLACK_TOP = '6NQ'
        const SPEC_CAR = ['498B2Y002','498B2Y004','498B2Y005','498BZY002','498BZY003'] // a7l 耀黑套装

        const optionExist = this.currentOptionsList.find((i) => i.optionCode === BLACK_TOP)   //选装包数据
        const privateExist = this.allPrivateOrderList.find(i=>i.optionCode === BLACK_TOP && i.status === 1)   // 查找私人订制数据,如果有6NQ的标装,则添加

        if (!optionExist) {
          if (SPEC_CAR.includes(currentModelCode) || privateExist) {
            currentOptions.push(BLACK_TOP) // 曜黑配置,默认添加黑顶选装
          } else {
            currentOptions.push('N6NQ') // 若没有黑色顶棚,则手动添加自创白色顶棚的code
          }
        }
      }
      currentOptions.push(this.currentInteriorEih.optionCode)
      const currentCar = {
        currentModelCode,
        currentExCode,
        currentInCode,
        currentHub,
        currentOptions,
        platform: 'app',
        showCloseBtn: true,
        showInnerHotspot: '0',
        currentView: 'in'
      }
      let url = 'https://testcc.fontre.com/audi-a7l0.0.6/index.html'
      const { data } = series === 'a7l' ? await getCarConfigcc() : (series == "q6" ? await getQ6cc() : await getQ5EConfigcc())
      url = data.data.configValue
      if (data.code !== '00') return console.error('获取url出错')
      const currentCarBase64 = window.btoa(JSON.stringify(currentCar))
      const iframeUrl = `${url}?hidepanel=1&currentCar=${currentCarBase64}`
      this.$store.commit('hideLoading')
      if (data.code !== '00') return console.error('获取url出错')

      await callNative('audiOpen', {
        path: iframeUrl,
        params: 'test',
        pageOrientation: 'landscape'
      })
    },
    sensors: function (vos, sib, eih) {
      if (this.currentInteriorChair) {
        const tempvos = vos
          ? vos.optionName
          : this.currentInteriorChair.optionName
        const tempsib = sib
          ? `${sib.sibName}/${sib.interieurName}`
          : `${this.currentSibColorInterieur?.sibName}/${this.currentSibColorInterieur.interieurName}`
        const tempeih = eih ? eih.optionName : this.currentInteriorEih.optionName
        let arr = this.currentOptionsList
          && this.currentOptionsList.map((res) => res.optionName)
        arr = arr.join('/')
        const params = {
          model_name: this.selectCarInfo.modelLineName,
          price: this.currentPrice,
          cars_appearance: this.currentExColor.optionName,
          hub_model: this.currentModelHub.optionName,
          interior_type: `${tempvos}/${tempsib}/${tempeih}`,
          personal_tailor: arr
        }
        this.$sensors.track('interiorSelection', params)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.interior-3dIcon {
  width: 52px;
  height: 52px;
  position: absolute;
  top: 18px;
  left: 24px;
  z-index: 2;
}

.opcity1 {
  opacity: 1;
}

.opcity0 {
  opacity: 0;
}

.interior-wrapper {
  height: calc(100vh - 190px);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  .interior-headImg {
    width: 100%;
    height: 220px;

    .bgimg {
      height: 220px;
      // object-fit: fill;
    }
  }

  .interior-main {
    .interior-summary {
      margin-top: 22px;
      display: flex;
      justify-content: center;
      padding: 0 16px;
      box-sizing: border-box;

      .summary-delimiter {
        width: 1px;
        background-color: #1a1a1a;
        height: 24px;
        margin: 0 10px;
      }

      .summary-item {
        &.chair-summary {
          width: 25%;
        }

        &.sib-summary {
          width: 30%;
          box-sizing: border-box;
          // margin: 0 auto;
        }

        &.eih-summary {
          width: 25%;
        }

        >div {
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: center;
          line-height: 24px;
        }

        .summary-price {
          font-size: 12px;
          margin-top: 8px;
        }
      }
    }

    .interior-box {
      >div {
        margin-top: 12px;
      }

      .interior-chair {
        .text {
          font-size: 12px;
          font-weight: 400;
          color: #333;
          line-height: 24px;
          margin-left: 16px;
        }

        .interior-chair-price {
          line-height: 20px;
        }
      }

      .title {
        font-size: 14px;
        font-family: Audi-WideBold;
        font-weight: normal;
        color: #000000;
        line-height: 17px;
        margin-left: 16px;
      }

      .subtitle {
        line-height: 20px;
      }

      .slide {
        width: 100%;
        display: inline-flex;
        overflow-x: auto;
        padding: 12px 0 8px 0;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .slide-imgItem {
          box-sizing: border-box;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          width: 90px;
          height: 90px;
          padding: 6px;
          margin-right: 24px;
          box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);

          &:first-of-type {
            margin-left: 16px;
          }

          &:last-of-type {
            margin-right: 16px;
          }

          &.selected {
            border: 1px solid #000;
          }

          >img {
            width: 74px;
            height: 74px;
            object-fit: cover;
          }
        }
      }

      .sib-slide-item,
      .eih-slide-item {
        display: flex;
        flex-direction: column;
        overflow: visible;

        &:first-of-type {
          margin-left: 16px;
        }

        &:last-of-type {
          margin-right: 16px;
        }

        .slide-imgItem {
          &:first-of-type {
            margin-left: 0px;
          }

          &:last-of-type {
            margin-right: 0px;
          }

          &.selected {
            border: 1px solid #000;
          }
        }
      }

      .optionName {
        max-width: 90px;
        font-size: 12px;
        font-weight: 400;
        color: #000000;
        line-height: 24px;
        margin-top: 7px;

        .price {
          font-size: 12px;
          margin-top: 8px;
          color: #333;
        }
      }
    }
  }
}

.ex-popup {
  width: calc(100vw - 32px);
  // height: 200px;
  background-color: #fff;
  padding: 32px;
  box-sizing: border-box;
  position: relative;

  main {
    font-size: 16px;
    color: #333333;
    line-height: 25px;
    text-align: center;
  }

  footer {
    width: 100%;
    height: 56px;
    background: #000000;
    border: 1px solid #000000;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    // position: absolute;
    margin-top: 32px;
  }
}
</style>
