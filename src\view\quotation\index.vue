<template>
  <div id="quoPage" class="quo-wrapper">
    <div class="hint-wrapper" v-if="invalid == 0">
      已失效
    </div>
    <div class="hint-wrapper" v-if="updateFlag == 1">
      此配置单价格有更新
    </div>

    <div class="tip-orderlock c-font12" v-show="lockTextVisible">
      *温馨提示：当前订单已锁定，暂不支持修改，如有疑问请联系门店管家
    </div>

    <div style="position: relative;padding-top: 30px; background-color: #fff;">
      <div class="carna">
        <div v-if="isA7MrCar" style="padding: 0 16px;" class="model-name">{{ carModelName | a7MrTextWrap }} </div>
        <div v-else style="padding: 0 16px;" class="model-name">{{ carModelName | textWrap }} </div>
      </div>
      <div class="carimg-wrapper">
        <img :src="imageUrlBox | imgFix(900)" alt="" srcset="">
      </div>
    </div>

    <div class="container" style="height: 100%;">
      <!-- 我的配置 -->
      <div class="sc-m-top">
        <div class="c-flex-between">
          <div class="c-font16">
            车辆配置
          </div>
          <div class="c-flex-center c-font12" style="color: #333333;" @click="toPowerPage" v-if="reConfigBtnVisible">
            重新配置
          </div>

          <div class="c-flex-center c-font12" style="color: #333333;" @click="toPowerPageByModify" v-if="modifyConfigBtnVisible">
            修改配置
          </div>
        </div>

        <CarConfigDetail @dataTrack="clickQuotationSensors" />

        <div class="buy-car-equity-wrapper" style="margin-top: 30px;margin-bottom: 16px;" v-if="equityList.length > 0 && newNga">
          <div class="c-font16">
            购车权益
          </div>
          <!-- <div class="c-flex-center" style="color: #333333;font-size: 12px;" @click="lookAll()">
            查看全部
          </div> -->

          <div class="equity-wrapper">
            <div v-for="item, idx in equityList" :key="idx" class="card">
              <div class="name-wrapper c-flex-between">
                <div class="name c-bold c-font14"> {{ item.ruleName }}</div>
                <div class="btn-detail c-font12" v-if="item.ruleDetails" @click="showEquityDetail(item)">查看详情</div>
              </div>

              <div class="equity-desc c-font12" v-html="item.ruleDesc"></div>
            </div>
          </div>
          <div class="c-font10 equity-tail" style="color:#999999; ">您可享受的购车权益，将按照支付定金的时间予以确定</div>
        </div>

      </div>
      <div class="carB" v-if="!orderDetail">
         <img :src="'https://sx-audi.oss-cn-shanghai.aliyuncs.com/audicc/app/order/trycar/y.jpg' | audiwebp" @click="tryCar" alt="">
      </div>

      <div class="moreB" @click="toFinancePage" v-if="!orderDetail">
        <div class="leftB">
          更多金融贷款方案
        </div>
        <div class="rightB">
          <span style="padding-right: 5px;"> 立即了解</span>
          <van-icon style="vertical-align: -22%;" size="14px" name="arrow" />
        </div>
      </div>


      <div class="layout-bottom" v-if="!orderDetail">
        <!-- 交付时间文案 -->
        <div v-if="carSeries.seriesCode != 'F0'" class="delivery-text-wrapper">
          {{ deliveryTimeText }}
        </div>

        <div class="left">
          <div class="price-wrapper">
            <div>
              <span class="bold">{{ totalPrice | prefixFormatPrice }}</span>
            </div>
          </div>
          <div class="price-wrapper bottom">
            <div v-if="orderStatus === '30' || orderStatus === '301'">
              <span>定金</span>
              <span class="font14" :class="{ 'line-through': disabledRights }">¥{{ toPayPrice | formatPrice }}</span> <span v-if="disabledRights" class="blacks">￥0</span>
            </div>
            <div v-else-if="orderStatus === '31'">
              <span>定金</span>
              <span class="font14" :class="{ 'line-through': disabledRights }">¥{{ payedPrice | formatPrice }}</span> <span v-if="disabledRights" class="blacks">￥0</span>
            </div>
            <div v-else>
              <!-- <template v-if="!bestRecommendId"> -->
              <template v-if="isOneStep">
                <span>定金：</span>
                <span class="font14" :class="{ 'line-through': disabledRights }">¥{{ (allPayPrice / 100) | formatPrice }}</span><span v-if="disabledRights" class="blacks">￥0</span>
              </template>
              <template v-else>
                <span>意向金：</span>
                <span class="font14">¥{{ (earnestPrice / 100) | formatPrice }}</span>
              </template>
              <!-- <template v-if="bestRecommendId">
                <span>定金</span>
                <span class="bold">¥{{ (allPayPrice / 100) | formatPrice }}</span>
              </template> -->
            </div>
          </div>
        </div>
        <div class="right align-center" v-if="orderStatus !== '31'">
          <AudiButton @click="addConfigFun" v-if="showAddConfigBtn" style="margin-right: 4px;" text="保存配置" color="white"
            height="56px" font-size="16px" />
          <!-- 置灰 -->
          <!-- !orderDetail -->
          <!-- //valid   0是普通失效只能去退订，1有效不变，2选装下架失效，3车型下架失效 -->
          <div v-if="showBox || '0,2,3'.includes(invalid) || (updateFlag == 1 && updatePrice === '1')" class="goPay">
            立即定购
          </div>
          <AudiButton
            v-if="!orderDetail && !(showBox || '0,2,3'.includes(invalid) || (updateFlag == 1 && updatePrice === '1'))"
            @click="toOrderDetail" style="max-width: 70%;" :text="mainBtnText" color="black" height="56px"
            font-size="16px" />
        </div>
      </div>
    </div>
    <canvasCard ref="canvasImg" @getCanvasImg="getCanvasImg" :imgurl="carImgUrl" :title="carModelName + '配置单'" />
    <model :modalshow.sync="modalshow" @update:modalshow="submit" title="在App内打开" confirm-text="复制链接"
      content="点击下方复制链接，前往浏览器打开" />
    <van-dialog v-model="orgShow" :show-cancel-button="false" :show-confirm-button="false">
      <div class="dialog-title">
        你已经在{{ orgName }}建卡，请前往{{ orgName }}选择车辆。
      </div>
      <div class="D-buttons" @click="toDealerDetail">
        确认前往
      </div>
    </van-dialog>

    <van-dialog v-model="visible" :title="invalidReason">
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div class="goB" @click="handleBack">
          返回
        </div>
        <div class="okB" @click="handleReCC">
          重新配置
        </div>
      </div>
    </van-dialog>
    <van-dialog v-model="updateVis" title="此配置单价格有更新">
      <div style="display: flex;justify-content: center;margin-top: 40px;">
        <div class="goB" @click="handleBack">
          返回
        </div>
        <div class="okB" @click="handleMoney">
          确认更新
        </div>
      </div>
    </van-dialog>

    <network @reload="networkReload()" />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  RadioGroup,
  Radio,
  Cell,
  CellGroup,
  Toast,
  Dialog
} from 'vant'
import { mapState } from 'vuex'
import {
  getCarConfig,
  getProductDetail,
  addCarShoppingCart,
  getShareUrl,
  canUpdateCCConfig,
  getOrgBankList,
  judgeReservationClient,
  getUserInfo,
  getAudiCarUrl,
  getNgaPrice,
  getAPITimeOutTesting,
  getUserRightsByCarModelId
} from '@/api/api'
import {
  getDeliveryTimeByCcid,
  getUpdateNew,
  getModelLineQuery,
  getEnergyStyleList,
  getCopCouponInfo
} from '@/configratorApi/index'
import CarConfigDetail from '@/components/car-config-detail.vue'
import model from '@/components/model.vue'
import confiUrl from '@/config/url'
import {
  callNative, getUrlParamObj, getMonthWeek
} from '@/utils'
import canvasCard from '@/components/canvas-card.vue'
// import deliveryPattern from '@/components/delivery-pattern.vue'
import network from '@/components/network.vue'
import AudiButton from '../../components/audi-button.vue'
import debounce from './debounce.js'
import { getCarType } from '@/view/newConfigration/util/helper.js'
import { A7MR } from '@/view/newConfigration/car/a7mr'

const baseOssHost = confiUrl.BaseOssHost
Vue.use(Cell)
Vue.use(CellGroup)
Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Toast)
const VIN_CAR = 'ONEAPP_LONGSTOCK' // 长库龄车

/**
   * 从业务上来分, 报价单页面可能会在各个地方去独立使用,
   * 所以要求进入此页面必须在url中携带 ccid 字段
   */
export default {
  inject: ['reload', 'checkLoginFn'],
  components: {
    AudiButton,
    CarConfigDetail,
    model,
    canvasCard,
    network,
  },
  data() {
    return {
      youkeVis: true,
      imageUrlBox: '',
      invalidReason: '',
      measure: '',
      updatePrice: '1',
      invalid: {},
      updateFlag: {},
      packageItem: '',
      updateVis: false,
      visible: false,
      showBox: false,
      showPDE: false,
      allPayPrice: 0,
      earnestPrice: 0, // 小订金额
      mainBtnText: '立即定购',
      orderStatus: '',
      modalshow: false,
      routeType: 'h5',
      time: Date.now(),
      scopeShowHeader: true,
      isConfig: 0,
      dealerCode: '',
      orgBankList: [],
      orgShow: false,
      orgName: '',
      orgCode: '',
      deliveryTimeByCcid: '',
      orderDetail: false,
      prodId: '',
      estimateDeliveryTime: '4周起',
      carDetailByCcid: {},
      currentModelLine: {},
      isOneStep: true,
      orderLocked: null, // string 0 未锁单 1 锁单
      equityList: [], // 权益列表
      isVinCar: false, // 是否长库龄车(二维码带vin)
      newNga: true, // 是否是新的nga1.1订单
      disabledRights:false, // 显影,
    }
  },
  computed: {
    ...mapState({
      dealerMainImg: (state) => {
        const imgurl = state.dealerInfo?.imageUrl
        return imgurl ? `${baseOssHost}${imgurl}` : ''
      },
      standardConfigData: 'standardConfigData',
      dealerName: (state) => state.dealerInfo.dealerName,
      dealerDistance: (state) => state.dealerInfo.distance,
      dealerAddress: (state) => state.dealerInfo.dealerAdrress,
      carSeries: (state) => state.carSeries,
      carImgUrl: (state) => (state.carDetail.configDetail?.carModel?.imageUrl ? `${baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl}`
        : ''), // 主图
      totalPrice: (state) => (state.carDetail.configDetail?.totalPrice - state.carDetail.configDetail?.discount),
      carHeadImageUrl: (state) => (state.carDetail.configDetail?.carModel?.headImageUrl ? baseOssHost + state.carDetail.configDetail?.carModel
        ?.headImageUrl : ''),
      carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn, // 车辆名称
      deliveryTime: (state) => state.carDetail.configDetail?.carModel?.deliveryTime, // 车辆名称
      bestRecommendId: (state) => state.carDetail.bestRecommendId, // 是否是推荐车型
      isReserveCard: 'isReserveCard',
      bestRecommandDealerCode: 'bestRecommandDealerCode',
      env: 'env',
      ccid: 'ccid',
      skuid: 'skuid',
      loginCheck: 'loginCheck',
      isA7MrCar: (state) => A7MR.map((i) => i.code).includes(state.carDetail?.configDetail?.carModel?.modelLineCode),
      queryParams: (state) => state.configration.queryParams,
      orderTime: (state) => state.configration.orderTime
    }),
    payment: {
      get() {
        return this.$store.state.payment
      },
      set(value) {
        this.$store.commit('updatePayment', value)
      }
    },

    showAddConfigBtn() {
      const { orderStatus } = this.$route.query
      // 报价单页显示的按钮, orderStatus 代表从详情页跳转过来
      const boo = this.$route.path === '/quotation' && !orderStatus && !this.bestRecommendId && !this.isVinCar
      // console.log('showAddConfigBtn>>', boo)
      return boo
    },

    // 是否显示重新配置按钮
    reConfigBtnVisible() {
      const { orderStatus, fromPage } = this.$route.query
      const condition1 = !['00', '31', '301'].includes(orderStatus) && this.isConfig === 1 && !this.bestRecommendId
      // nga1.1新增条件:整车订单跳转过来并且没有orderStatus
      // const condition2 = (fromPage === 'orderDetail') && !orderStatus
      const spCar = this.isVinCar // 长库龄车
      return condition1 && !spCar
    },

    // 是否显示修改配置按钮
    modifyConfigBtnVisible() {
      const { orderStatus } = this.$route.query

      const condition1 = ['00', '301'].includes(orderStatus) // 定金待支付状态
      const condition2 = ['31'].includes(orderStatus) && this.orderLocked === '0' // 已支付意向金,且当前是未锁单状态
      const spCar = this.isVinCar // 长库龄车
      const newNgaOrder = this.newNga // new nga1.1订单
      const res = (condition1 || condition2) && !spCar && newNgaOrder
      return res
    },

    // 是否显示锁单文案
    lockTextVisible() {
      const { orderStatus } = this.$route.query
      return this.orderLocked === '1' && orderStatus === '31' // 定金支付并且锁单
    },

    // 定金要付的金额
    toPayPrice() {
      return (this.allPayPrice - this.earnestPrice) / 100
    },
    // 定金的总金额
    payedPrice() {
      return this.allPayPrice / 100
    },

    // 交付日期
    deliveryTimeText() {
      return this.estimateDeliveryTime
    }
  },
  created() {
    this.youkeVis = !this.checkLoginFn('报价单')
    this.$store.commit('setHeaderVisible', true)
    /**
     * 跳转到此页面如果携带orderStatus ,那么跳转到ccpro配置页要带上这个信息
     * 1. 从未支付的订单页面跳转过来
     * 2. 从ccpro 选车跳转过来
     * deposited: 0|1 是否大定
     * orderTime: 如果大定一定会有这个参数（用来获取新的权益）
     * orderLocked:  0|1  是否锁单
     * orderType: 07:代表nga1.0|  10: 代表nga1.1
     */
    const {
      ccid, skuid, orderStatus, invalid, fromPage, dealerCode, env, step, deposited, orderTime, orderLocked, orderType
    } = this.$route.query
    if (step !== undefined) {
      this.isOneStep = step === 'one'
    }
    this.$store.commit('setHeaderVisible', env !== 'minip')
    this.dealerCode = dealerCode
    dealerCode && this.getOrgBankList()
    this.orderDetail = fromPage === 'orderDetail'
    this.$store.commit('setTitle', this.orderDetail ? '车辆配置' : '报价单')

    if (!ccid || !skuid) {
      console.error(`此页面依赖 ccid : ${ccid} and skuid: ${skuid}`)
    }
    // 只有已支付意向金,未支付定金的状态下显示[去支付定金]
    if (['30', '301'].includes(orderStatus)) {
      this.mainBtnText = '立即定购'
    }
    this.orderStatus = orderStatus
    this.$store.commit('saveSkuId', skuid)
    this.$store.commit('updateCcid', ccid)
    this.$store.commit('setPageFrom', fromPage)

    this.orderLocked = orderLocked

    // 配置与权益业务
    if (deposited || orderTime || orderLocked) {
      this.$store.commit('setTitle', '配置与权益')
      if (orderTime) {
        this.$store.commit('updateOrderTime', orderTime)
      }
      // orderType === 07 代表是老的nga订单
      this.newNga = orderType === '10'
    }
  },

  mounted() {
    this.fixStyle()
    this.initData()
    this.setCouponValid();
  },

  methods: {
    // 修复滚动条以及各种hardcode样式
    fixStyle() {
      document.getElementById('common-view-wrapper').scrollTop = 0// 滚动条置顶

      // 导航栏
      // const app = document.getElementById('app')
      // const header = app.querySelector('.header')
      // if (!header) {
      //   return console.error('找不到 classname: header')
      // }
      // const paddingTop = header.style.paddingTop
      // const currentPaddingTopValue = parseFloat(paddingTop)
      // header.style.paddingTop = `${currentPaddingTopValue + 15}px`
    },
    toFinancePage() {
      this.clickQuotationSensors('金融方案立即了解') // 埋点
      const seriesCode = this.carSeries.seriesCode
      // const selectCarInfo = this.$storage.getPlus('selectCarInfo') || seriesCode
      this.$router.push({
        path: '/configration/financial-calculator',
        query: {
          ccid: this.ccid || '',
          skuid: this.skuid || '',
          dealerCode: this.dealerCode || '',
          from: 'quotation',
          seriesCode: seriesCode || ''
        }
      })
    },
    // 查看全部
    lookAll() {
      this.clickQuotationSensors('客户权益查看全部') // 埋点

      const configDetail = this.$store.state.carDetail.configDetail
      let caseCode = ''
      configDetail?.optionList && configDetail?.optionList.forEach((e) => {
        if (e.optionCode === 'YEG' || e.optionCode === 'YEA') {
          caseCode = e.optionCode
        }
      })
      const obj = {
        modelLineCode: configDetail?.carModel?.modelLineCode || '',
        caseCode: caseCode || '',
        orderId: this.$route.query?.orderId || '',
        seriesName: this.carSeries?.seriesCode || '',
        modelYear: configDetail?.carModel?.modelYear,
        modelVersion: configDetail?.carModel?.omdModelVersion
      }
      this.$router.push({
        path: '/theEquity',
        query: obj
      })
    },
    // 预约试驾
    tryCar() {
      this.clickQuotationSensors('预约试驾')// 埋点

      const seriesCode = this.carSeries.seriesCode
      // console.log(seriesCode, ['49', 'G4', 'G6'].findIndex((e) => e == seriesCode))
      this.$router.push({
        path: '/testdrive/create',
        query: {
          idx: ['49', 'G4', 'G6','F0'].findIndex((e) => e == seriesCode)
        }
      })
    },
    handleReCC() {
      this.visible = false
      const seriesCode = this.carSeries.seriesCode
      console.log('车系：seriesCode 49 G4 G6 F0', seriesCode)
      // 49 G4 G6
      let obj = {}
      // if (seriesCode == 49 || seriesCode == 'G6') {
      obj = {
        path: '/configration',
        query: {
          idx: ['49', 'G4', 'G6','F0'].findIndex((e) => e === seriesCode)
        }
      }
      this.$router.push(obj)
    },
    handleBack() {
      this.visible = false
      this.updateVis = false
    },
    async handleMoney() {
      this.$store.commit('showLoading')
      // 点击确认，则自动将有更新的价格
      const { ccid, skuid } = this.$route.query
      const res = await getUpdateNew(ccid)
      this.$store.commit('hideLoading')
      this.updateVis = false
      this.updatePrice = ''
      this.reload()
    },
    async getCarConfigfn() {
      const { ccid, skuid } = this.$route.query
      const { data } = await getCarConfig({ ccid: ccid })
      console.log('car info >>>>>>>>>>>>>.', data.data)
      if (+data?.data?.valid != 1) {
        // valid  0是普通失效只能去退订，1有效不变，2选装下架失效，3车型下架失效
        this.invalid = +data?.data?.valid
        this.invalidReason = data?.data?.invalidReason
        // console.log(1111111111111, this.invalidReason)
        if (this.orderDetail) return
        this.visible = this.invalid != 1
      }

      if (data?.valid == 1) {
        this.invalid = +data.valid
      }

      if (+data?.data?.updateFlag == 1 && data && data?.data?.updateContent) {
        this.updateFlag = +data?.data?.updateFlag
        this.packageItem = data?.data?.updateContent
        if (this.orderDetail) return
        this.updateVis = this.updateFlag == 1
      }

      // 交付时间
      this.estimateDeliveryTime = data.data?.estimateDelivery

      const carDetail = data.data.configDetail
      const heibaiche = [
        '9bb9a4e5-096b-4f3a-8660-96feb126b271',
        '3bcc54ad-28e9-4499-98f3-2a8abf8fdcda'
      ].includes(carDetail?.carModel?.modellineId) && this.$route.query?.shoppingCartId

      this.showBox = [
        '0d035558-7a87-4267-b0c4-fd7f11936956',
        '3d722843-7016-4a9c-b339-4aeb5b16e13a'
      ].includes(carDetail?.carModel?.modellineId) && [
        'AJ-N4X',
        'DS-N4X',
        'EO-N4X'
      ].includes(carDetail?.insideColor?.colorCode) || heibaiche
      console.log('this.showBox <<', this.showBox)
    },
    async getDeliveryTimeByCcid(ccid, dealerCode) {
      const { data } = await getDeliveryTimeByCcid(ccid, dealerCode)
      if (data.code === '00') {
        const strarr = data.data.estimateDate.split('-')
        const week = getMonthWeek(Number(strarr[0]), Number(strarr[1]), Number(strarr[2]))
        this.deliveryTimeByCcid = `${strarr[0]}年${Number(strarr[1])}月第${week}周左右交付`
      }
    },

    // 接口数据置用于埋点
    getBuriedPointData(modellineId) {
      getModelLineQuery(modellineId).then((res) => {
        if (res.data.code !== '00') {
          return console.error('接口错误：', res.data.message)
        }
        this.currentModelLine = res.data.data[0]
      })
    },

    // 通过ccid 获取车辆详情
    async getCarDetailByCcid(ccid, skuid) {
      const { data } = await this.$store.dispatch('getCarDetailByCcid', { ccid, youkeVis: this.youkeVis, page: 'quotation' })
      const { totalPrice, depositType, configDetail: { carModel: { modelCode }, carSeries: { seriesCode } } } = data || {}
      if ([modelCode, seriesCode, depositType, skuid].every((i) => i)) {
        this.handleGetNgaPrice(modelCode, seriesCode, depositType, skuid, totalPrice)
      }
      console.log('$store.state.carDetail:', data)
      this.carDetailByCcid = data

      this.measure = data.measureId
      this.isVinCar = data.entryPoint === VIN_CAR
      const { modelLineCode, modellineId } = data.configDetail?.carModel
      const hub = data.configDetail?.optionList.find((f) => f.optionClassification == 'RAD')?.optionCode || ''
      const outsideColor = data.configDetail?.outsideColor.colorCode || ''

      // 绑定ccid 到用户
      if (!data.accountId && !this.youkeVis) {
        this.$store.dispatch('bindCcid', { ccid })
      }
      // 获取埋点所需的数据
      this.getBuriedPointData(modellineId)

      // item.imageUrl = `/ccpro-backend/a7lbest/${item.outsideColor.optionName}_${item.hub.optionName}.png`
      // if (hub) this.imageUrlBox = `${baseOssHost}/ccpro-backend/storebest/${modelLineCode}_${hub}_${outsideColor}.png`
      // if (!hub) this.imageUrlBox = baseOssHost + data.configDetail?.carModel?.headImageUrl

      // 头部大图
      if (hub) {
        const seriesNameMap = { 49: 'a7l', G4: 'q5e', G6: 'q6',F0: 'a5l' }
        const seriesName = seriesNameMap[data.configDetail.carSeries.seriesCode]
        const modelLineCode = data.configDetail.carModel.modelLineCode
        const exteriorColor = data.configDetail.outsideColor.colorCode
        this.imageUrlBox = `${baseOssHost}ccpro-backend/${seriesName}/carImages/${modelLineCode}/${exteriorColor}/${hub}/Front45.png`
      } else {
        this.imageUrlBox = baseOssHost + data.configDetail?.carModel?.headImageUrl
      }
    },
    async getOrgBankList() {
      const { data } = await getOrgBankList({ dealerCode: this.dealerCode })
      this.orgBankList = data.data
    },
    async handleGetNgaPrice(modelCode, seriesCode, depositType, skuid, totalPrice) {
      const { orderStatus, isOneStep } = this
      const { data: { data: { prodId } } } = await getProductDetail({ id: skuid }) || {}
      prodId && (this.prodId = prodId)
      const { data: { data: { prodNgaDepositPrice, prodNgaDownPayPrice } } } = await getNgaPrice({
        modelCode, seriesCode, depositType, prodId
      })
      // !!! 这里的逻辑 ===> 续接以前各类金额显示
      if (prodNgaDepositPrice) {
        this.earnestPrice = prodNgaDepositPrice
      }
      if (prodNgaDownPayPrice) {
        if (['30', '31', '301'].includes(orderStatus) || isOneStep) {
          this.allPayPrice = prodNgaDownPayPrice
        }
        // 小定 toPayPrice   payedPrice 大定  意向金 earnestPrice
      }
    },

    // 返回到动力页面
    toPowerPage() {
      this.$store.commit('updateCarModelTab', 'power')

      const { orderStatus, orderId, shoppingCartId } = this.$route.query
      const carIdx = { 49: 0, G4: 1, G6: 2,F0: 3 }
      const code = this.carSeries.seriesCode
      /**
       * measureType: 1高定 2:半定
       * 这里的需求是:
       * 动力页默认是高定+半定的混合车型.
       * 半定的车型选择重新到动力页,要过滤掉高定的车型,只能选择半定
       * 高定同理
       */
      this.$router.push({
        path: '/configration',
          query: {
            idx: carIdx[code] === 3 ? 'e0172bf8-d547-4ad7-adf7-1292f53ea0df' : carIdx[code],
            orderStatus,
            orderId,
            shoppingCartId: shoppingCartId || '',
            measureType: this.measure ? 2 : 1
          }
      })

      this.clickQuotationSensors('重新配置') // 埋点
    },

    // （配置与权益）未锁单的情况下，可修改配置
    toPowerPageByModify() {
      this.$store.commit('updateCarModelTab', 'power')
      // const { deposited, orderTime, orderLocked } = this.$route.query
      const { orderStatus, orderId, orderType, ccid, fromPage } = this.$route.query

      const carIdx = { 49: 0, G4: 1, G6: 2, F0: 3}
      const code = this.carSeries.seriesCode

      Dialog.confirm({
        message: '是否确认修改配置',
        className: 'quotation-dialogstyle',
        confirmButtonText: '取消',
        cancelButtonText: '确认'
      }).then(() => {
        // null
      }).catch(() => {
        this.$router.push({
          path: '/configration',
          query: {
            idx: carIdx[code] === 3 ? 'e0172bf8-d547-4ad7-adf7-1292f53ea0df' : carIdx[code],
            orderStatus,
            orderId,
            orderType,
            action: 'modelDetailModify', // 配置权益修改
            measureType: this.measure ? 2 : 1,
            ccid,
            fromPage,
          }
        })
      })
    },

    // 获取权益数据
    async getEquityData() {
      const {
        modelLineCode, modelYear, omdModelVersion
      } = this.carDetailByCcid.configDetail?.carModel

      const { orderId } = this.queryParams
      const { orderType } = this.$route.query

      let serachTime = Date.now()
      if (this.orderTime) {
        const decodedTimeStr = this.orderTime.replace(/\+/g, ' ')
        const date = new Date(decodedTimeStr) // 转为日期对象
        serachTime = Math.floor(date.getTime() / 1000) // 转为时间戳（秒）
      }

      const params = {
        carModelId: modelLineCode, //
        modelYear: modelYear,
        modelVersion: omdModelVersion,
        searchTime: serachTime,
        // 有orderId 的时候需要传orderType
        ...orderId ? { orderId, orderType } : {}
      }

      // console.log('🚀 ~ getEquityData ~ data:', data, this.newNga)
      const res = await getUserRightsByCarModelId(params)
      if(res.data && res.data.data && res.data.data.rights) {
        const data = JSON.parse(res.data.data.rights)
        this.equityList = data
      }
    },

    // 跳转到权益详情
    showEquityDetail(item) {
      this.$store.commit('updateEquityDetail', item.ruleDetails)
      this.$router.push({
        path: '/configrationEquityDetail'
      })
    },

    /**
     * 查看ccid是否还能更新配置
     * 0 不可以 1 可以
     */
    async setCanUpdateCCConfig() {
      const param = { ccid: this.$route.query.ccid }
      const res = await canUpdateCCConfig(param)
      this.isConfig = res.data.data
    },

    // 立即预定
    async toOrderDetail() {
      this.clickQuotationSensors('立即预定')
      console.log('检查是否需要跳转到登录页')
      if (!this.checkLoginFn()) return // 检查是否需要跳转到登录页
      const carDetail = this.$store.state.carDetail.configDetail
      const condition = this.showBox
      // 4. 下架星耀锦衣逐日和星耀机甲逐日里面的N4X面料
      // 灰翎-冰痕棕、橙翎-冰痕灰、红玲-冰痕黑
      // 5. 606个逐日的配置单，进入报价单后，支付按钮置灰，不可点击
      if (condition) {
        console.log(this.$store.state.carDetail)
        return
      }

      // if (this.env === 'minip') {
      //   this.modalshow = true
      //   return
      // }

      if (this.bestRecommandDealerCode.length && !this.youkeVis) {
        console.log('虎头车绑定代理商 ', this.bestRecommandDealerCode)
        const { data } = await getUserInfo()
        const mobile = data.data?.userInfo?.mobile
        if (!mobile) {
          return console.error('获取mobile失败')
        }
        const res = await judgeReservationClient({
          custMobile: mobile,
          seriesCode: this.carSeries.seriesCode
        })
        console.log('创始卡数据', res)
        if (res.data.data && res.data.data.orgCode !== this.bestRecommandDealerCode) {
          this.orgShow = true
          this.orgName = res.data.data.orgName
          this.orgCode = res.data.data.orgCode
          return
        }
      }
      const {
        ccid,
        skuid,
        orderStatus,
        orderId,
        dealerCode,
        shoppingCartId,
        drmCode
      } = this.$route.query
      const { prodId } = this
      let param = {}
      // 有orderStatus 说明此页面是从订单详情页返回回来的
      // 30: 已支付意向金,未支付定金 ,00: 待支付意向金
      // 这串逻辑已经迁移到车型页

      const params = getUrlParamObj()
      console.log(`params: ${JSON.stringify(params)}`);
      let minip = {}
      if (params?.env === 'minip') {
        const { env, token, refreshToken } = params
        minip = { env, token, refreshToken }
        console.log(`minip: ${JSON.stringify(minip)}`);
      }
      // const string = Object.keys(params).reduce((i, n) => i + (params[n] ? (`&${n}=${params[n]}`) : ''), '')

      if (orderStatus === '30' || orderStatus === '00') {
        param = {
          path: '/order/money-detail',
          query: { orderId, ...minip }
        }
      } else {
        const isBigOrder = this.$route.query.isBigOrder || ''
        param = {
          path: '/order/detail',
          query: {
            ccid, skuid, shoppingCartId, dealerCode, isBigOrder, prodId, ...minip, drmCode
          }
        }
      }
      const queryString = new URLSearchParams(param.query).toString()
      console.log(`queryString: ${queryString}`)
      // window.location.href = `https://dev-audi-p.svwsx.cn/one-mall/audi/order/otd/confirmOrder/${ccid}?${queryString}`
      // window.location.href = `https://uat-audi.saic-audi.cn/one-mall/audi/order/otd/confirmOrder/${ccid}?${queryString}`
      window.location.href = `https://audi-embedded-wap.saic-audi.mobi/one-mall/audi/order/otd/confirmOrder/${ccid}?${queryString}`


      // 待测试
      // if (this.fromModelDetailPage) {
      //   param = {
      //     path: '/order/model-detail',
      //     query: {
      //       ccid,
      //       skuid,
      //       shoppingCartId,
      //       dealerCode,
      //       prodId,
      //       ...{ minip },
      //       drmCode
      //     }
      //   }
      // }

      this.scopeShowHeader = false
      // this.$router.push(param)
    },
    submit(isSubmit) {
      if (isSubmit) {
        // 复制到粘贴板
        const copyResult = this.InsertShearPlate()
        this.modalshow = false
        if (copyResult) {
          Toast({
            type: 'fail',
            message: '已复制到剪贴板',
            icon: require('../../assets/img/success.png')
          })
        } else {
          Toast({
            type: 'fail',
            message: '复制失败',
            icon: require('../../assets/img/error.png')
          })
        }
      }
    },
    InsertShearPlate() {
      // 复制到粘贴板
      const text = 'https://app.saic-audi.mobi/index.html#/?channel=miniapp_VehiclePurchase'
      const copyString = text
      // 创建input标签存放需要复制的文字
      const myInput = document.createElement('input')
      // 把文字放进input中，供复制
      myInput.value = copyString
      document.body.appendChild(myInput)
      // 选中创建的input
      myInput.select()
      // 执行复制方法， 该方法返回bool类型的结果，告诉我们是否复制成功
      const copyResult = document.execCommand('copy')
      // 操作中完成后 从Dom中删除创建的input
      document.body.removeChild(myInput)
      return copyResult
    },
    audiShare() {
      this.time = Date.now() + 10
      this.$refs.canvasImg.getCanvasImg()
    },

    async getCanvasImg(e) {
      console.log(`图片+${e.fileStorageId}`, baseOssHost + e.fileUrl)
      const subTitle = this.standardConfigData.map((i) => i.name)
      const res = await getShareUrl()
      const path = `${res.data.data.configValue}#/carConfig?ccid=${this.$route.query.ccid}`
      console.log(baseOssHost + e.fileUrl)
      callNative('audiShare', {
        type: 'carConfig',
        path: path, // `${window.location.origin}/order/index.html#/car-config-other?ccid=${this.$route.query.ccid}`,
        imageUrl: baseOssHost + e.fileUrl,
        ccid: this.$route.query.ccid,
        title: `欢迎围观我的${this.carModelName}`,
        subTitle: `我的配置${subTitle.join('|')}`
      }).then((data) => {
        console.log(data, '分享回调')
      })
    },
    addConfigFun() {
      this.clickQuotationSensors('保存配置')

      debounce(() => {
        this.addConfigFn()
      }, 1000)
    },

    // 添加配置单到购物车列表
    async addConfigFn() {
      console.log('检查是否需要跳转到登录页 ')
      if (!this.checkLoginFn()) return // 检查是否需要跳转到登录页
      this.$store.commit('showLoading')
      const { ccid, skuid, inviteBuyCarCode } = this.$store.state
      const shoppingCartId = this.$route.query?.shoppingCartId || ''
      const entryPoint = this.$storage.getPlus('entryPoint')
      const { data } = await addCarShoppingCart({
        ccid, skuid, invitationCode: inviteBuyCarCode, shoppingCartId, entryPoint
      })
      if (data.code === '00') {
        this.$store.commit('hideLoading')
        Toast({
          message: data.message,
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      }
    },
    // 跳转到代理商详情
    async toDealerDetail() {
      const { data } = await getAudiCarUrl()
      const url = `${data.data.configValue}dealerDetail?dealerCode=${this.orgCode}`
      callNative('audiOpen', { path: url })
    },

    async initData() {
      // this.$store.dispatch('getDealerInfo')
      const { ccid, skuid, routeType } = this.$route.query
      this.routeType = routeType || 'h5'
      console.log(this.routeType)
      getAPITimeOutTesting()
      await this.getCarDetailByCcid(ccid, skuid)
      // this.getProductDetail(skuid)
      // this.getDeliveryTimeByCcid(ccid, this.dealerCode)
      this.getCarConfigfn()
      const isToken = this.$storage.get('token')
      if (isToken) {
        this.setCanUpdateCCConfig()
      }

      await this.getEquityData()
      Toast.clear()
    },

    networkReload() {
      this.initData()
    },

    // 获取早鸟包
    async setCouponValid() {
      const token = this.$storage.get("token");
      if (token) {
        const userInfo = await callNative("getAudiUserInfo", {});
        if (userInfo) {
          const couponInfo = await getCopCouponInfo(userInfo.userId, "F0");
          if (couponInfo.data && couponInfo.data.data && Array.isArray(couponInfo.data.data) && couponInfo.data.data.length > 0) {
            this.disabledRights = true;
            return;
          }
        }
      }
      this.disabledRights = false;
    },

    // 埋点
    clickQuotationSensors(buttonName) {
      const carMap = {
        49: {
          carName: 'A7L',
          idx: 0
        },
        G4: {
          carName: 'Q5 e-tron',
          idx: 1
        },
        G6: {
          carName: 'Q6',
          idx: 2
        }
      }
      const configDetail = this.$store.state.carDetail.configDetail

      const { modelLineCode, modellineId } = configDetail.carModel
      const { customSeriesId, seriesCode } = configDetail?.carSeries
      if (!carMap[seriesCode]) {
        console.error('未找到对应的车型:', seriesCode)
        return
      }
      const carType = getCarType()

      Promise.all([
        getModelLineQuery(modellineId),
        getEnergyStyleList(customSeriesId, carType)
      ]).then((res) => {
        const currentModelLine = res[0].data.data[0]
        const carList = res[1].data.data
        // 获取carVersion 字段
        let carVersionName = ''
        for (const car of carList) {
          for (const i of car.styleVos) {
            for (const j of i.modelLineList) {
              if (j.modelLineCode === modelLineCode) {
                carVersionName = i.styleName
                break
              }
            }
          }
        }
        const param = {
          source_module: 'H5',
          car_series: carMap[seriesCode].carName,
          car_type: currentModelLine.typeFlag,
          power_type: `${carMap[seriesCode].carName} ${currentModelLine.engine}`,
          car_version: carVersionName,
          delivery_type: '定制交付', // 快速交付|定制交付
          button_name: buttonName
        }
        console.log('CC_Quotation_BtnClick埋点:', param)
        this.$sensors.track('CC_Quotation_BtnClick', param)
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "../../assets/style/common.less";
@import url("../../assets/style/dialog.less");
@import url("../../assets/style/buttons.less");
/** 公共样式 */

@leftWith: 18vw;
@rightMargin: 15px;
@topMargin: 20px;
@fontColor: #999;

.quo-wrapper {
  margin-bottom: 60px;
}

.tip-orderlock {
  background-color: #000;
  color: #fff;
  padding: 3px 16px;
  line-height: 20px;
}

.shB {
  width: 22px;
  height: 22px;
  position: absolute;
  z-index: 999;
  top: 7px;
  right: 16px;
}

.qeB {
  color: #62615F;
  font-size: 12px;
  margin-top: 4px;
}

.texB {
  color: #B1B1B1;
  font-size: 10px;
  text-align: center;
  margin-top: 4px;
}

.imgC {
  width: 24px;
  margin-top: 8px;
}

.goPay {
  font-size: 16px;
  width: 100%;
  line-height: 45px;
  max-width: 70%;
  background: #E5E5E5;
  color: #FFFFFF;
  line-height: 45px;
  text-align: center;
}

.goB {
  border: 1px solid;
  padding: 15px 0;
  width: 50%
}

.okB {
  background: black;
  color: white;
  padding: 15px 0;
  width: 50%;
  margin-left: 2px;
}

.moreB {
  line-height: 44px;
  padding: 0 12px;
  background: #F5F5F5;
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  margin-bottom: 20px;

  .leftB {
    font-size: 12px;
    font-family: AudiTypeGB-Normal, AudiTypeGB;
    font-weight: 400;
    color: #888;
  }

  .rightB {
    font-size: 12px;
    font-family: AudiTypeGB-Normal, AudiTypeGB;
    color: #000000;
  }
}

.carB {
  width: 100%;
  height: 170px;
  overflow: hidden;
  margin-top: 24px;
  position: relative;
}

.gocar {
  font-size: 14px;
  position: absolute;
  left: 16px;
  bottom: 36px;
  line-height: 40px;
  text-align: center;
  color: #000;
  width: 88px;
  height: 40px;
  background: #FFFFFF;
}


.carna {
  position: absolute;
  width: 100%;
  top: 12px;
  line-height: 24px;

  >.model-name {
    font-size: 16px;
    white-space: pre;
  }
}

//向上的边距
.sc-m-top {
  margin-top: @topMargin;
}

.sc-left {
  width: @leftWith;
  margin-right: @rightMargin;
}

.sc-nowrap {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
}

.sc-height {
  .c-flex-between;
  flex-direction: column;
  flex: 1;
  height: @leftWith;
  padding: 12px 0;
  box-sizing: border-box;
}

//box阴影
.sc-shadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
}

// 下划线
.sc-u-line {
  border-bottom: 1px solid #e5e5e5;
}

//
.bold18 {
  font-size: 16px;
  .c-bold;
}

.small-font {
  .c-font12;
  color: #999;
}

.container {
  .sc-m-top;
  position: relative;
  padding: 0 18px;
  padding-bottom: 100px;
}

.hint-wrapper {
  background-color: #f2f2f2;
  font-size: 14px;
  padding: 10px 18px;
}

.carimg-wrapper {
  height: 185px;
  background: linear-gradient(to bottom, #fff, #f0f0f0);
  > img {
    object-fit: cover;
    width: 100%;
    height: 104%;
  }
}

/** 其他样式 */
//代理商
.proxy-wrapper {
  .sc-shadow;
  .c-flex-center;

  position: relative;
  margin-top: 15px;

  >.left {
    .c-flex-center;
    width: 30%;
    margin-right: 15px;
  }

  >.right {
    position: relative;
    height: 110px;
    flex: 1;

    .title {
      margin: 10px 0;
    }

    .bottom {
      .small-font;
      position: absolute;
      bottom: 4px;
    }
  }
}

.layout-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 5px 16px 35px 16px;
  box-sizing: border-box;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);

  >.delivery-text-wrapper {
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 100%;
    line-height: 32px;
    background-color: #F2F2F2;
    padding-left: 16px;
    box-sizing: border-box;
    font-size: 12px;
  }

  .left {
    font-size: 12px;
    color: #999999;
    margin-top: 6px;

    .price-wrapper {
      line-height: 24px;
      &.bottom {
        line-height: 18px;
        margin-top: 2px;
      }
      >div {
        display: flex;

        >span:first-child {
          // width: 55px;
        }
      }
    }

    .bold {
      font-size: 16px;
      color: #000;
      line-height: 24px;
    }

    .font14 {
      font-size: 12px;
      color: #999999;
    }
  }

  .right {
    width: 65%;
  }
}

.align-center {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.dialog-title {
  .c-font16;
  .c-bold;
  text-align: left;
  margin-bottom: 20px;
}


.equity-wrapper {
  .card {
    border: 1px solid #E5E5E5;
    border-radius: 4px;
    padding: 12px;
    margin-top: 14px;

    .name{
      line-height: 22px;
    }
    .btn-detail {
      line-height: 22px;
      color: #333333;
    }

    >.equity-desc {
      margin-top: 12px;
      white-space: pre-wrap;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.5);
    }
  }
}

.equity-tail {
  line-height: 18px;
  margin-top: 12px;
}

/deep/.van-dialog {
  overflow-y: auto;
  max-height: 80%;
  padding: 15px 16px 16px;
  top: 52% !important;
  z-index: 33336;

  h3 {
    margin: 0;
  }

  .item {
    color: #000;
    font-size: 14px;
    text-align: left;
    margin-bottom: 24px;

    .title {
      line-height: 24px;
    }

    .itemCotent {
      display: flex;
      line-height: 17px;

      div {
        margin-top: 8px;
      }
    }
  }
}
.line-through {
  text-decoration: line-through;
  color: gray;
}
.balcks{
  font-size: 12px;
  color: #000;
}
</style>
