<template>
  <div class="">
    <div
      class="paymentSuccess"
      v-if="carSeries.seriesCode === '49'"
    >
      <div class="headimg">
        <div class="imgwarp">
          <img
            v-for="(item, index) in numbers"
            :key="index"
            :src="require('@/assets/img/' + item + '.png')"
            alt=""
          >

          <img
            src="../../assets/img/xg.png"
            alt=""
          >
          <img
            src="../../assets/img/9.png"
            alt=""
          >
          <img
            src="../../assets/img/9.png"
            alt=""
          >
          <img
            src="../../assets/img/9.png"
            alt=""
          >
        </div>
      </div>
      <div
        class="success"
        id="success"
        :class="data.numberDisplay.includes('4') ? 'sanjiao' : ''"
      >
        <p
          class="px"
          v-if="!isUpdate"
        >
          预订成功
        </p>
        <p
          class="px"
          v-if="isUpdate"
        >
          更换成功
        </p>
        <p class="p1">
          青山远黛，星河梦蓝
        </p>
        <!-- <p class="p1">{{ colorNameCn }}</p> -->
        <p class="p1">
          恭喜您获得专属限量号
        </p>
        <p class="p2">
          {{ data.numberDisplay }}
        </p>
        <p class="p1">
          与A7L一起领跑未来
        </p>
        <van-popover
          :close-on-click-action="false"
          :close-on-click-outside="false"
          get-container="#success"
          placement="top"
          v-model="showPopover"
        >
          <div
            v-if="data.numberDisplay.includes('4')"
            class="p3"
            @click="replaceA"
            :style="{ padding: '20px' }"
          >
            可选择将“4”更换为“A”
            <van-icon name="arrow" />
          </div>
          <template #reference />
        </van-popover>
        <div
          class="imgWarp"
          v-if="cipCampaign"
        >
          <div v-show="showQrcode">
            <img
              :src="cipCampaign"
              alt=""
            >
            <p class="p4">
              微信扫一扫添加您的专属顾问
            </p>
          </div>
        </div>
      </div>
      <div
        v-show="!showQrcode"
        class="carBox"
      >
        <img
          class="car"
          :src="carModelImg"
          alt=""
        >
      </div>
      <van-dialog
        v-model="isChange"
        overlay
        :show-cancel-button="true"
      >
        <div class="title">
          请选择您想要的限量号，确认后不可更改
        </div>
        <ul>
          <li
            v-for="(item, index) in numbers"
            :class="item == 4 ? 'kuang' : ''"
            :key="index"
            @click="changeA(index)"
          >
            {{ item }}
          </li>
        </ul>
        <div
          class="D-buttons"
          @click="confim"
        >
          确认
        </div>
        <div
          class="D-buttons2"
          @click="cancel"
        >
          取消
        </div>
      </van-dialog>

      <div class="box">
        <div class="btnWarp">
          <div
            class="buttons"
            @click="goPayDetail"
          >
            查看购车订单
          </div>
        </div>
      </div>
    </div>

    <div v-else>
      <div class="_q5_content">
        <img
          class="_icon-success"
          src="../../assets/img/contract-success.png"
        >
        <div class="_title">
          恭喜您预约成功
        </div>
        <div class="_title">
          您的客户权益已锁定
        </div>
        <img
          class="_qrCode"
          v-if="cipCampaign"
          :src="cipCampaign"
        >
        <div
          class="_title2"
          v-if="cipCampaign"
        >
          企业微信激活码
        </div>
      </div>

      <div class="box">
        <div class="btnWarp">
          <div
            class="buttons"
            @click="goPayDetail"
          >
            查看购车订单
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import { Dialog, Toast, Popover } from 'vant'
import { XIAN_XING_VERSION } from '@/config/constant'
import api from '../../config/url'
import {
  getMyOrders,
  limitedNumbers,
  putLimitedNumbers,
  floorsIntroduce,
  getCipCampaign
} from '../../api/payment-success'
// import { getCarConfig } from '../../api/detail.js'

Vue.use(Dialog).use(Popover).use(Toast)
const codeType = ['00', '200']
export default {
  data() {
    return {
      imgList: {
        url0: require('../../assets/img/0.png'),
        url1: require('../../assets/img/1.png'),
        url2: require('../../assets/img/2.png'),
        url3: require('../../assets/img/3.png'),
        url4: require('../../assets/img/4.png'),
        url5: require('../../assets/img/5.png'),
        url6: require('../../assets/img/6.png'),
        url7: require('../../assets/img/7.png'),
        url8: require('../../assets/img/8.png'),
        url9: require('../../assets/img/9.png'),
        urlA: require('../../assets/img/A.png')
      },
      BaseOssHost: api.BaseOssHost,
      carModelImg: require('../../assets/img/car.png'),
      isChange: false,
      data: {
        number: '000',
        carType: '',
        customerTel: null,
        id: '',
        numberDisplay: '000',
        orderNumber: null,
        reserved: false,
        reservedTel: null,
        stage: 1,
        status: 1
      },
      formData: {},
      isUpdate: false,
      // 无活码
      imgUrl1: '',
      // 有活码
      imgUrl2: '',
      numbers: '000',
      cipCampaign: '',
      colorNameCn: '',
      indexList: []
    }
  },

  methods: {
    changeA(index) {
      if (this.indexList.includes(index)) {
        // 埋点
        this.haveConfirmed(1)
        const arr = this.numbers.split('')
        if (this.numbers[index].toString() === '4') {
          arr.splice(index, 1, 'A')
          let str = ''
          for (let i = 0; i < arr.length; i++) {
            str += arr[i]
          }
          this.numbers = str
        } else if (this.numbers[index].toString() === 'A') {
          arr.splice(index, 1, '4')
          let str = ''
          for (let i = 0; i < arr.length; i++) {
            str += arr[i]
          }
          this.numbers = str
        }
      }
    },
    setCarModelImg() {
      if (this.currentModelLineCode !== XIAN_XING_VERSION) {
        this.carModelImg = require('@/assets/img/other-carModel.png')
      } else {
        this.carModelImg = require('@/assets/img/car.png')
      }
    },
    haveConfirmed(num) {
      const params = {
        types_name: this.formData.carBuyerInfo.fullName || '',
        types_telephone: this.formData.carBuyerInfo.mobile || 0,
        document_id: this.formData.carBuyerInfo.moreContact.split(',')[1] || '',
        owner_name: this.formData.carBuyerInfo.carOwnerName || '',
        owner_number: this.formData.carBuyerInfo.carOwnerMobile || 0,
        owner_id: this.formData.carBuyerInfo.carOwnerCertificateNumber || '',
        cars_appearance: this.$route.query.colorNameCn || '',
        cars_interior: this.$route.query.inColorNameCn || ''
      }
      if (num === 1) {
        this.$sensors.track('changeNumber', params)
      } else if (num === 2) {
        params.original_number = this.data.number
        params.change_number = this.data.numberDisplay
        this.$sensors.track('confirmChange', params)
      } else if (num === 3) {
        this.$sensors.track('confirmCancel', params)
      }
    },
    goPayDetail() {
      this.$router.push({
        path: '/order/money-detail',
        query: {
          orderId: this.$route.query.orderId || ''
        }
      })
    },
    confim() {
      this.$store.commit('showLoading')
      this.putLimitedNumbers()
      this.haveConfirmed(2)
    },
    cancel() {
      this.isChange = false
      this.numbers = this.data.numberDisplay
      this.haveConfirmed(3)
    },
    replaceA() {
      this.isChange = true
      this.indexList = []
      const numbers = this.numbers
      numbers.split('').forEach((item, index) => {
        if (item === 4) {
          this.indexList.push(index)
        }
      })
    },
    async putLimitedNumbers() {
      const param = this.data
      param.numberDisplay = this.numbers
      this.$store.commit('hideLoading')
      const { data } = await putLimitedNumbers(param)

      if (codeType.includes(data.code)) {
        this.isChange = false
        if (this.data.number !== data.data.numberDisplay) {
          this.isUpdate = true
        }
        if (this.numbers !== data.data.numberDisplay) {
          Toast({
            type: 'success',
            message: '更换成功',
            icon: require('../../assets/img/success.png')
          })
        }

        this.numbers = data.data.numberDisplay
        this.data = data.data
      } else {
        Toast({
          type: 'fail',
          message: '更新失败',
          icon: require('../../assets/img/error.png')
        })
      }
    },
    async getMyOrders() {
      const { data } = await getMyOrders({
        orderId: this.$route.query.orderId || ''
      })
      if (codeType.includes(data.code)) {
        this.formData = data.data
      }
    },
    async limitedNumbers() {
      const { data } = await limitedNumbers({
        orderId: this.$route.query.orderId || localStorage.getItem('orderId') || ''
      })
      if (codeType.includes(data.code)) {
        this.data = data.data
        this.numbers = this.data.numberDisplay
      }
    },
    async floorsIntroduce() {
      const { data } = await floorsIntroduce()
      console.log(data)
      if (codeType.includes(data.code)) {}
    },
    async getCipCampaign() {
      const { $route: { query: { dealerCode, areaCode } } } = this
      const param = {
        dealerCode: dealerCode,
        code: areaCode
      }
      const { data: { code, data } } = await getCipCampaign(param)
      if (codeType.includes(code)) {
        const { qrcode, bind } = data || {} // bind => '0': 未绑定， '1'，已绑定
        if (bind === '0' && qrcode) {
          this.cipCampaign = qrcode
          this.$bus.$emit('rightShow', true, qrcode)
        }
      }
    }
    // async getCarConfig() {
    //   const { data } = await getCarConfig({
    //     ccid:
    //       this.$route.query.carCustomId || localStorage.getItem('ccid') || ''
    //   })
    //   if (codeType.includes(data.code)) {
    //     this.colorNameCn = data.data.configDetail.outsideColor.colorNameCn
    //   }
    // }
  },
  mounted() {
    console.log('carSeries', this.carSeries)
    console.log('carSeries', this.$route.query)

    this.getMyOrders()
    this.limitedNumbers()
    this.floorsIntroduce()
    this.getCipCampaign()
    // this.getCarConfig()
    this.$emit('isBg', true)

    this.setCarModelImg()
  },
  computed: {
    ...mapState({
      dealerCode: (state) => state.dealerInfo.dealerCode,
      carSeries: (state) => state.carSeries,
      currentModelLineCode: (state) => state.carDetail.configDetail?.carModel?.modelLineCode
    }),
    showQrcode() {
      const ZONG_BU = ['76600000', '76600019'] // 默认的代理商
      return this.cipCampaign && ZONG_BU.includes(this.dealerCode || this.$route.query.dealerCode)
    },
    showPopover: {
      get() {
        return this.numbers.includes('4')
      },
      set(val) {
        return val
      }
    }
  },
  destroyed() {
    this.$bus.$emit('rightShow', false)
    this.$emit('isBg', false)
  }
}
</script>

<style scoped lang="less">
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/dialog.less");
  @import url("../../assets/style/background.less");

  .box {
    padding-bottom: 0 !important;
  }

  .paymentSuccess {
    background-image: url("../../assets/img/bgImg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-bottom: 100px;
    min-height: 630px !important;

    .success {
      margin: 0 16px 0 16px;
      color: #fff;
      position: relative;
      text-align: center;
      background: rgb(198, 199, 200);
      padding-bottom: 22px;

      // /deep/.van-popup {
      //   top: 350px !important;
      //   left: 100px !important;
      // }
      .imgWarp {
        text-align: center;
        margin-top: 20px;

        img {
          width: 80px;
          height: 80x;
          margin-bottom: 16px;
        }
      }

      sp {
        margin: 0;
        font-size: 16px;
        font-weight: 400;
      }

      p {
        margin-block-start: 0;
        margin-block-end: 0;
        letter-spacing: 3px;
      }

      .p1 {
        font-size: 16px;
        line-height: 22px;
        color: #fff;
      }

      .p2 {
        color: #fff;
        font-size: 24px;
        line-height: 32px;
      }

      .px {
        padding-top: 24px;
        margin-bottom: 16px;
        font-size: 28px;
        font-family: "Audi-WideBold";
      }

      .p4 {
        color: #fff;
        font-size: 10px;
      }
    }

    .carBox {
      margin-top: 13px;
    }

    .title {
      font-size: 18px;
      font-family: "Audi-ExtendedBold";
      margin-bottom: 14px;
    }

    /deep/.van-dialog__content {
      ul {
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 60px;
        margin-bottom: 14px;

        li {
          font-size: 22px;
          width: 34px;
          height: 34px;
          line-height: 34px;
          font-family: "Audi-ExtendedBold";
        }

        .kuang {
          border: 1px solid;
          border-color: #000;
        }
      }

      .span {
        font-size: 12px;
        color: #fff;
        text-align: left;
        margin-left: 16px;
      }
    }

    .btnWarp {
      background-color: transparent !important;

      .buttons {
        // border: 1px solid #fff !important;
      }
    }
  }

  ._q5_content{
    display: flex;
    flex-flow: column;
    align-items: center;
    padding-top: 120px;

    ._icon-success{
      width: 80px;
      height: 80px;
      margin-bottom: 10px;
    }
    ._title{
      font-size: 17px;
      line-height: 30px;
      font-family: "Audi-Normal";
    }
    ._qrCode{
      width: 100px;
      height: 100px;
      margin-top: 80px;
      margin-bottom: 10px;
    }
    ._title2{
      font-size: 15px;
      line-height: 30px;
      font-family: "Audi-Normal";
      font-weight: 500;
    }
  }

  /deep/.van-popup {
    width: 300px;
    left: 0 !important;
    right: 0 !important;
    top: -24px !important;
    margin: 0 auto !important;

    .van-popover__content {
      border-radius: 0 !important;
    }

    .p3 {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px 20px !important;
    }
  }

  /deep/ .van-overlay {
    z-index: 22223 !important;
  }

  /deep/.van-dialog {
    z-index: 22224 !important;
  }

  /deep/.van-popover__arrow {
    display: none;
  }

  .sanjiao {
    /deep/.van-popover__arrow {
      display: block;
    }
  }
</style>>
