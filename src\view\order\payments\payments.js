/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-03 14:23:53
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2025-01-03 11:50:41
 * @FilePath     : \src\view\order\payments\payments.js
 * @Descripttion : 支付
 */
import Vue from 'vue'
import { Toast } from 'vant'
import { router } from '@/router/router'
import store from '@/store/index'
import {
  callNative
} from '@/utils'
import {
  getOrderLimitedNumberStatus,
  carPlaceOrder
} from '@/api/detail'
import {
  getMyOrders,
  saveKfLog
} from '@/api/api'
import { RES_SUCCEED_CODE, LIMIT_NUMBERS_A7L, ORDER_STATUS_DISTRICT } from '@/config/conf.data'
import wx from 'weixin-js-sdk'
Vue.use(Toast)

const ORDER_SUCCEED_CODE = ['30', '31']
const ORDER_PASSING_CODE = ORDER_STATUS_DISTRICT.DA_DING_BEFORE

export const createOrder = async (params, goods, spare = {}, mobile) => {
  const { data } = (await carPlaceOrder(params)) || ''

  // 缓存存oldccid 下次进入更新
  localStorage.setItem('oldCcid', 'oldCcid')

  if (data?.code && RES_SUCCEED_CODE.includes(data.code)) {
    console.log(
      '%c [ createOrder ]-29',
      'font-size:14px; background:#cf222e; color:#fff;',
      data.data
    )
    const { orderId } = data.data || ''
    const orderStatus = '00'
    if (orderId && ORDER_PASSING_CODE.includes(orderStatus)) {
      await saveKfLog({
        key:"原生支付的接口：callNative('callPayCenter')", 
        value:data.data && JSON.stringify(data.data),  
        source:"audi-order-h5"
      })
      payOrder(orderId, { ...goods, orderStatus }, spare, mobile)
      return true
    }
  }

  console.log(
    '%c [ Error createOrder ]-35',
    'font-size:14px; background:#cf222e; color:#fff;',
    data.message
  )
  Toast({
    className: 'toast-dark-mini toast-pos-middle',
    message: data.message || '创建订单失败！',
    forbidClick: true,
    duration: 800
  })
  return false
}

export const payOrder = async (
  orderId,
  goods,
  { device, location, minipay, step },
  mobile
) => {
  console.log(
    '%c [ payOrder ]-50',
    'font-size:14px; background:#cf222e; color:#fff;',
    orderId,
    goods,
    device,
    minipay,
    step,
    mobile
  )
  const { orderStatus } = goods
  // 已创建订单
  if (orderId && ORDER_PASSING_CODE.includes(orderStatus)) {
    if (device === 'minip') {
      if (+minipay === 1) {
        checkPayOrders(orderId, goods, { device, step }, mobile)
        return
      }
      const { origin, pathname } = location
      // const string = Object.keys(goods).reduce((i, n) => i + (goods[n] ? (`&${n}=${goods[n]}`) : ''), '')
      // const strp = string.substring(1, string.length)
      const url = encodeURIComponent(
        `${origin}${pathname}#/order/confirm?form=form&oos=${orderStatus}`
      )
      wx.miniProgram.navigateTo({
        url: `/pages/pay/auto/index?orderId=${orderId}&url=${url}&mobile=${mobile}`
      })
    } else {
      // 原生支付 无返回值
      await callNative('callPayCenter', {
        orderId: orderId
      })
      const { payType } = (await callNative('callPayResult', {})) || ''
      // 使用数字人民币支付
      if (payType === 'CNY') {
        return router.push({
          path: '/order/pay-flow',
          query: { orderId }
        })
      }
      checkPayOrders(orderId, goods, { device, step }, mobile)
    }
  }
}

const checkPayOrders = async (orderId, goods, { device, step }, mobile) => {
  if (orderId) {
    const { seriesCode, modelLineCode, orderStatus: status, ccid } = goods
    let i = 0
    const timer = setInterval(async () => {
      i++
      if (i >= 6) {
        clearInterval(timer)
        router.push({ name: 'new-money-detail', query: { orderId } })
        console.log(
          '%c [ 关闭(超时)查询 ... ]',
          'font-size:14px; background:#327eef; color:#fff;'
        )
      } else {
        /*
         * 小大定查询orderStatus
         */
        const { data } = await getMyOrders({ orderId })
        if (data?.code && RES_SUCCEED_CODE.includes(data.code)) {
          const { orderStatus } = data.data || ''
          console.log(
            '%c [ 订单支付状态查询中 status ... ]',
            'font-size:14px; background:#0070d9; color:#fff;',
            i,
            orderStatus,
            status,
            step
          )
          // 牵扯A7L 先行版限量号发放故使用此接口，{确认车型查询）
          if (
            ORDER_SUCCEED_CODE.includes(orderStatus) &&
            +orderStatus > +status
          ) {
            if (
              orderStatus === '30' &&
              modelLineCode === LIMIT_NUMBERS_A7L?.modelLineCode
            ) {
              const { data } = await getOrderLimitedNumberStatus({ orderId })
              console.log(
                '%c [ A7L 状态查询中 status ... ]',
                'font-size:14px; background:#0070d9; color:#fff;',
                i,
                +data.data.status
              )
              // if (data?.code && RES_SUCCEED_CODE.includes(data.code)) {
              //   if (+data.data.status === 1) {
              //   }
              // }
            }

            store.commit('updateReaded', 0)
            clearInterval(timer)
            // let name = ''
            // if (device === 'minip') {
            //   name = 'order-guide-page-download-app'
            // } else {
            const name =
              (orderStatus === '30' || (orderStatus === '31' && +step === 1)) &&
              status === '00'
                ? 'order-pay-succeed'
                : 'dading-success'
            // }
            sessionStorage.setItem('orderId',orderId)
            router.push({
              name,
              query: {
                orderId,
                seriesCode,
                modelLineCode,
                orderStatus,
                ccid,
                mobile
              }
            })
          }
        }
      }
    }, 1000)
  }
}
