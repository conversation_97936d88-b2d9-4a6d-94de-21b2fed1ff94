<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-04-06 10:31:47
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-04-17 14:57:12
 * @FilePath     : \src\view\lucky-bag\index.vue
 * @Descripttion : 福袋购车
-->
<template>
  <div class="lucky-bag-wrapper">
    <div class="lucky-bag-count-down">
      <div
        class="count-down"
        data-flex="dir:top main:center"
      >
        <p>当前活动限时4小时 限量60台</p>
        <div
          class="count-down-box"
          data-flex="main:center cross:center"
        >
          <template v-if="timeNow > endTime">
            活动已结束
          </template>
          <template v-else>
            活动还有<van-count-down
              :time="time"
              format="DD 天 HH : mm : ss"
            /><span>{{ timeNow <=endTime && timeNow > startTime ? '结束' : '开始' }}</span>
          </template>
        </div>
      </div>
    </div>
    <div class="lucky-bag-box">
      <!-- <h2 class="h2">
        请选择您想要的福袋：
      </h2> -->
      <div
        class="lucky-bag-list"
        v-for="item in list"
        :key="item.id"
        @click="handleCheckInfo(item)"
      >
        <div class="lucky-bag-item">
          <img
            class="img"
            :title="item.blessedBagName"
            :alt="`${item.carModelName}_${item.id}`"
            :src="require(`@/assets/bags/${item.id}.gif`)"
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { CountDown, Toast } from 'vant'
import dayjs from 'dayjs'
import { getLuckyBagList, getUserLuckyBagInfo } from '@/api/lucky-bag'

Vue.use(CountDown).use(Toast)
export default {
  name: 'LuckyBagCars',
  data() {
    return {
      timeNow: dayjs().valueOf(),
      time: 0,
      startTime: 0,
      endTime: 0,
      luckyBag: [],
      list: []
    }
  },
  created() {
    this.getLuckyBagListData()
    this.getUserLuckyBagInfoData()
  },
  methods: {
    async getUserLuckyBagInfoData() {
      const { data } = await getUserLuckyBagInfo()
      if (data?.data) {
        const {
          carModelName, id, blessedBagProductId, ccid, luckyPackageId
        } = data.data
        // luckyBagId, skuid, ccid, luckyPackageId
        this.luckyBag = [carModelName, id, blessedBagProductId, ccid, luckyPackageId]
      }
    },
    async getLuckyBagListData() {
      const { data } = await getLuckyBagList()
      if (data?.data?.length) {
        const { timeNow } = this
        const startTime = data.data[0]?.activityStarttimeL || ''
        const endTime = data.data[0]?.activityEndtimeL || ''
        this.startTime = startTime
        this.endTime = endTime
        this.time = timeNow <= startTime ? startTime - timeNow : endTime - timeNow
        this.list = data.data.reverse()
      }
    },
    handleCheckInfo(data) {
      const { id, blessedBagPrice, blessedBagProductId } = data || 0
      const [title, luckyBagId, skuid, ccid, luckyPackageId] = this.luckyBag || ''
      if (this.luckyBag?.length && luckyBagId) {
        return luckyBagId !== id
          ? Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '您已参加周岁进取福包活动'
          })
          : this.$router.push({
            name: 'lucky-bag-cars-details',
            query: {
              luckyBagId, skuid, ccid, luckyPackageId
            }
          })
      }

      // const { timeNow, endTime, startTime } = this
      // if (timeNow > endTime || timeNow < startTime) {
      //   return Toast({
      //     className: 'toast-dark-mini toast-pos-middle',
      //     message: timeNow > endTime ? '活动已结束' : '活动未开始'
      //   })
      // }
      this.$router.push({ name: 'lucky-bag-cars-lottery', query: { luckyBagId: id, skuid: blessedBagProductId } })
    }
  }
}
</script>

<style lang="less" scoped>
.lucky-bag-wrapper {
  padding-bottom: 55px;
  background-color: #fff;
}
.count-down {
  padding: 20px 30px;
  font-size: 18px;
  text-align: center;
  color: #fff;
  background-color: #000;
  line-height: 160%;
  p {
    margin: 0;
  }
  .van-count-down {
    margin: 0 4px;
    padding: 4px 6px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    color: #000;
    background-color: #fff;
    overflow: hidden;
  }
}
.lucky-bag-box {
  .h2 {
    font-size: 18px;
    line-height: 120%;
    padding: 16px 16px 7px 16px;
    margin: 0;
  }
  .lucky-bag-list {
    margin: 17px 16px;
    box-shadow: 2px 5px 20px rgba(60, 60, 60, .06), -2px -5px 20px rgba(60, 60, 60, .06);
  }
}
</style>
