<template>
    <div class="price-preferential">
        <span class="price-before-preferential">¥{{ props.price | formatPrice }}{{showFinalFormat.showFinalFormat ? '起' : ''}}</span>
        <div class="preferential-tag">
            <div class="tag-left">限时优惠价</div>
            <div class="tag-right">¥{{ props.price - (props.preferential || 0) | formatPrice }}</div>
        </div>
    </div>
</template>

<script setup>
/**
 * q6的限时优惠组件
 * 齐云的七座显示黑， 其他显示红
 * 7座modellinecode: G6IBAY002
 */
import { defineProps } from 'vue'

// 获取 props
const props = defineProps({
  preferential: {
    type: [String, Number],
    default: ''
  },
  price: {
    type: Number,
    default: 0
  },
  showFinalFormat: {
    type: Boolean,
    default: true
  }
})


</script>

<style lang="less" scoped>
.price-preferential {
    display: flex;
    align-items: center;
}
.price-before-preferential {
    font-size: 12px;
    margin-right: 4px;
    // text-decoration: line-through;
    color:#4C4C4C;
    // color: #999;
    transform: scale(0.8);
    transform-origin: 0 center;
}
.price-after-preferential {
    color: #F50538;
}

// 添加限时优惠价样式
.preferential-tag {
    display: inline-flex;
    overflow: hidden;
    width: fit-content;
    line-height: 20px;
    background: url('../../../assets/preferential-bg.jpg') center/cover no-repeat;
    transform: scale(0.7);
    transform-origin: 0 center;
    flex-shrink: 0;
    position: relative;
    left: -10px;

    .tag-left {
        // background-color: #fdf6e9;
        color: #8c6e4e;
        font-size: 12px;
        padding: 0px 10px 0px 4px;
        display: flex;
        align-items: center;
        position: relative;
        white-space: nowrap;
    }

    // .tag-left::after {
    //     content: '';
    //     position: absolute;
    //     right: -10px;
    //     top: 0;
    //     width: 0;
    //     height: 0;
    //     border-top: 24px solid transparent;
    //     border-bottom: 12px solid transparent;
    //     border-left: 10px solid #fdf6e9;
    //     z-index: 1;
    // }

    .tag-right {
        // background-color: #F50538;
        color: #fff;
        font-size: 14px;
        padding: 0px 4px 0px 4px;
        display: flex;
        align-items: center;
        white-space: nowrap;
    }
}
</style>


<!-- <style lang="less" scoped>
.price-preferential {
    display: flex;
    align-items: center;
    position: relative;
}

.price-before-preferential {
    font-size: 12px;
    margin-right: 6px; // 增加间距补偿缩放效果
    text-decoration: line-through;
    color: #999;
    position: relative;
    display: inline-block;
    transform: scale(0.83);
    transform-origin: 0 center;
    margin-bottom: -1px; // 微调垂直对齐
}

.price-after-preferential {
    color: #F50538;
}

// 添加限时优惠价样式
.preferential-tag {
    display: flex;
    font-size: 0; // 消除内联元素间的空隙
    margin-left: -8px;
    .tag-left {
        position: relative;
        background-color: #fdf6e9;
        color: #8c6e4e;
        font-size: 12px;
        height: 14px;
        padding: 2px 2px 2px 4px;
        display: inline-flex;
        align-items: center;
        white-space: nowrap;
        transform: scale(0.75);
        transform-origin: 0 center;
        z-index: 1;
    }

    .tag-left::after {
        content: '';
        position: absolute;
        right: -6px; // 调整箭头位置
        top: 0;
        width: 0;
        height: 0;
        border-top: 18px solid transparent;
        border-bottom: 0 solid transparent;
        border-left: 6px solid #fdf6e9;
        z-index: 1;
    }

    .tag-right {
        background-color: #F50538;
        color: #fff;
        font-size: 12px;
        height: 14px;
        padding: 2px 4px 2px 6px; // 调整左内边距以适应箭头
        display: inline-flex;
        align-items: center;
        white-space: nowrap;
        transform: scale(0.75);
        transform-origin: 0 center;
        margin-left: -17px; // 微调位置
        z-index: 0;
    }
}
</style> -->
