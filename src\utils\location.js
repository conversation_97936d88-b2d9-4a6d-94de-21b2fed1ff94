/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-12-06 10:46:20
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-05-11 14:26:26
 * @FilePath     : \src\utils\location.js
 * @Descripttion :
 */

import { callNative, delay, getLocationCityName } from '@/utils'
import { minipLocation } from '@/utils/weixin-js-sdk'

const getLocationInfo = async (LOCATION_TIMEOUT_MS = 0, device = 'native', data = {}) => {
  let city = ''
  let location = ''
  let locationCity = { city, location, tips: '' }
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    +LOCATION_TIMEOUT_MS && delay(() => {
      if (!city || !location) {
        console.log(`%c [ 获取用户定位超时 => ${LOCATION_TIMEOUT_MS} MS ]-29`, 'font-size:14px; background:#cf222e; color:#fff;')
        resolve({ city, location, tips: '定位超时' })
      } else {
        resolve({ city, location })
      }
    }, LOCATION_TIMEOUT_MS)
    if (device === 'native') {
      locationCity = await callNative('getLocationCity', {}) || ''
    } else if (['minip', 'minippp'].includes(device)) {
      if (device === 'minip') {
        locationCity = await minipLocation()
      } else {
        const wxLocation = data?.wxLocation || ''
        if (wxLocation && wxLocation.split(',')?.length > 1) {
          const minipppLocation = wxLocation.split(',')
          locationCity.latitude = minipppLocation[1]
          locationCity.longitude = minipppLocation[0]
        }
        // wxLocation.split(',')  108.94878,34.22259
        console.log('%c [ locationCity.location ]-34', 'font-size:14px; background:#cf222e; color:#fff;', locationCity.location)
        // locationCity = this.$route.query
      }

      if (locationCity?.latitude && locationCity?.longitude) {
        locationCity.location = `${locationCity.latitude},${locationCity.longitude}`
        locationCity.city = await getLocationCityName([locationCity.longitude, locationCity.latitude])
      }
    }
    city = locationCity?.city || ''
    // 安卓会返回 '0,0', '0.0,0.0'
    location = ((locationCity?.location && !['0,0', '0.0,0.0'].includes(locationCity.location)) && locationCity.location) || ''
    console.log('%c [ getLocationInfo ]-40', 'font-size:14px; background:#cf222e; color:#fff;', locationCity, city, location)
    location && resolve({ city, location })
  })
}
export default getLocationInfo
