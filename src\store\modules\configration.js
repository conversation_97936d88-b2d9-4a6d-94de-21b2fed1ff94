/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-07 17:14:29
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-02-We 07:16:59
 */
import Vue from 'vue'
import { Toast } from 'vant'
import { getOtds } from '@/api/api'
import {
  getQueryParam, checkV2, isEmptyObj, getUrlParamObj
} from '@/utils'
import url from '@/config/url'
import {
  getCarList,
  postCcEstimate,
  getPriceCompute,
  getMeasurePriceCompute,
  getPersonalOptionPlus,
  getSibColorInterieur,
  getInteriorEih,
  getInteriorChair,
  getExterior,
  getModelHub,
  getOmdEquipmentGroup,
  measureQuery,
  getV2CcInfo,
  getCcMeasure,
  updateCcInfo,
  postOmdGetCcid,
  putOmdGetCcid,
  getCcInfo
} from '@/configratorApi'
import {
  getDefaultSelectd, getFilterRelateList, wrapArray, arraysEqual
} from '@/view/newConfigration/util/helper'
import {
  SEAT_DATA_Q5_Q6, Q6_SAMURAI, SUMMER_PACKAGE, STANDARD_CAR
} from '@/view/newConfigration/util/carModelSeatData'
import {
  A7MR, XINCHAO, DONGGAN_CODE, BLACK_TOP_EXCOLOR_CODE
} from '@/view/newConfigration/car/a7mr'
import { A5L_RED_RAD } from "@/view/a5lConfigration/util/carModelSeatData";

const OSS_URL = url.BaseConfigrationOssHost
const MAIN_IMG_SIX_MAP = [
  'Front45.png',
  'Side.png',
  'Rear45.png',
  'Rear.png',
  'Front.png',
  'Top.png'
]
Vue.use(Toast)

export default {
  state: {
    currentCarInfo: [],
    projectStartTime: 0, // 项目开始时间
    carIdx: "", // 0: a7l, 1: q5e, 2: q6
    currentCarSeriesData: {}, // 当前车系数据
    carModelActiveTab: 'power', // power | version | carModel
    configrationActiveTab: 'exterior', // exterior | interior | option,
    referConfigrationActiveTab: '', // 记录上一次的configrationActiveTab供埋点使用
    currentVersionData: [], // 当前要显示的版本数据

    outColorArray: [], // 当前车型的外观颜色列表接口数据
    hubArray: [], // 当前车型的轮毂列表接口数据
    sibArray: [], // 当前车型的内饰(面料)列表接口数据
    eihArray: [], // 当前车型的饰板列表接口数据
    vosArray: [], // 当前车型的座椅列表接口数据
    deliveryTimeData: {}, // 交付时间接口数据

    currentVersion: {}, // 当前选中的版本数据
    currentVersionMap: {}, // 当前选中的版本数据, key:featureCode, value:item
    currentPacketMap: {}, // 当前车型的选装包, key:II | RAD | PACKET , value:Array
    modelLineMap:{},
    currentModelLineData: {}, // 当前选中的车型数据
    currentSeat: {}, // 当前选中的座椅选装包(六七座) q5e q6 才有这个选中项
    currentExterior: {}, // 当前选中的外观颜色
    currentHub: {}, // 当前选中的轮毂
    currentSib: {}, // 当前选中的内饰 (面料)
    currentEih: {}, // 当前选中的饰板
    currentVos: {}, // 当前车型的座椅, 目前只有a7 部分版本work
    currentII: {}, // 当前选中的坐椅面料颜色和材质，目前只在A5车型里使用这个值
    userCoupon:{}, // 用户早鸟包，目前只有A5L在使用

    currentOptionDetail: {}, // 当前选装包详情
    currentEquityDetail: "", // 当前权益详情
    currentSelectedConfigList: [], // 当前选中的配置列表.  外观| 内饰(面料) | 轮毂

    totalPrice: 0, // 当前选中的配置项的总价格
    equityPrice: 0, // 当前选中的配置项的权益价格，目前只有A5L在用
    footerDesc: {
      desc: "" // 底部footer栏描述文案
    },

    personalOptionComposes: [], // 选装包 推荐组合列表
    personalOptions: [], // 选装包 全部选装列表
    selectedOptions: [], // 当前选中的全部选装包列表
    requiredRadPackets: {}, // 必须选中的轮毂+选装包
    currentComposeName: "", // 当前选中的推荐的组合的名字
    onlySeatOption: [], // 只有来获取座椅的选装包

    queryParams: {}, // url中的参数 从configration页面保存
    measureSeatDataVisible: {}, // 半定逻辑下的座椅数据展示条件

    /**
     * 配置与权益相关参数
     */
    orderTime: null// 下单时间
  },

  getters: {
    // 车系名字  a7l | q6 | q5e
    currentSeriesName(state) {
      const name = state.currentCarSeriesData?.seriesName?.toLocaleLowerCase() || "";
      return name;
    },
    // 车系id
    currentSeriesId(state) {
      const id = state.currentCarSeriesData.customSeriesId
      return id
    },

    // 当前车的类型  A B C 类车 可以理解为:(库存, 半定, 全订制)
    currentCarType(state) {
      return state.deliveryTimeData.type
    },

    currentEarnestMoney(state) {
      const earnestMoney = state.deliveryTimeData ? state.deliveryTimeData.earnestMoney : 0;
      return earnestMoney / 100;
    },

    currentSeatsNum(state, getters) {
      if (getters.currentSeriesName === "a7l" || getters.currentSeriesName === "a5l") {
        return '5'
      }

      if (Q6_SAMURAI.includes(state.currentSeat.tagCode)) {
        const seatTag = state.currentModelLineData.tags.find((i) => i.tagCode?.includes('SEAT'))
        if (seatTag?.tagCode?.includes('7SEAT')) {
          return '7'
        }
        return '6'
      }
      if (state.currentSeat.tagCode.includes('7SEAT')) {
        return '7'
      }
      return '6'
    },
    // 座椅选装包数据(只有q5和q6 会显示)
    pageSeatList(state, getters) {
      const { currentModelLineData, onlySeatOption } = state
      if (isEmptyObj(currentModelLineData) || getters.currentSeriesName === 'a7l') return []
      if (isEmptyObj(currentModelLineData) || getters.currentSeriesName === "a5l") return [];

      const seatTag = currentModelLineData.tags.find((i) => i.tagCode?.includes('SEAT'))
      const seatData = SEAT_DATA_Q5_Q6[getters.currentSeriesName]
      if (!seatData) return

      let resSeatData = []

      // 这些车型座椅与配置线绑定, 切换座椅就更新配置线
      const q6Samurai = Q6_SAMURAI.includes(currentModelLineData.modelLineCode)

      if (q6Samurai) {
        const result = state.currentVersion.modelLineList.filter((item) => item.suit === currentModelLineData.suit)
        resSeatData = result.map((i) => ({
          name: i.tags.find((i) => i.tagCode.includes('SEAT')).tagName,
          price: i.price,
          status: 0,
          tagCode: i.tags.find((i) => i.tagCode.includes('SEAT')).tagCode,
          optionCode: '',
          desc: [], // 标配无需展示
          modelLine: i
        }))
      } else {
        const isQ624Year = getters.currentSeriesName === 'q6' && currentModelLineData.modelYear === '2024'

        resSeatData.push({
          name: seatTag.tagName,
          price: isQ624Year ? currentModelLineData.price : 0,
          status: 0,
          tagCode: seatTag.tagCode,
          optionCode: '',
          desc: [], // 标配无需展示,
          modelLine: currentModelLineData
        })
        const seatOptoion = onlySeatOption.find((i) => i.optionCode === seatData.defaultOptionCode && i.status === 2)
        if (seatOptoion) {
          // popover 中的描述列表
          const descList = seatOptoion.packetItems.filter((i) => i.status !== 0).map((i) => ({ text: i.optionName }))

          resSeatData.push({
            name: '尊享六座套装',
            price: seatOptoion.price,
            status: 1,
            tagCode: '6SEAT',
            optionCode: seatOptoion.optionCode,
            desc: descList,
            modelLine: currentModelLineData
          })
        }
      }

      /**
       * 兼容半定车业务
       * seatPacketOptional6 / seatPacketOptional7
       * 0 不显示 ,1 显示对应的座椅数据
       */
      if (currentModelLineData.measure === 1) {
        const { measureSeatDataVisible } = state

        // 过滤6座选装包
        if (measureSeatDataVisible?.seatPacketOptional6 === 0) {
          resSeatData = resSeatData.filter((i) => !i.tagCode.includes('6SEAT'))
        }
        // 过滤7座选装包
        if (measureSeatDataVisible.seatPacketOptional7 === 0) {
          resSeatData = resSeatData.filter((i) => !i.tagCode.includes('7SEAT'))
        }
      }
      return resSeatData
    },

    // 2d 轮播图片列表
    page2dCarList(state, getters) {
      const {
        currentHub, currentExterior, currentModelLineData, personalOptions, currentCarInfo
      } = state;

      if (getters.currentSeriesName === "a5l") {
        const modelLineCode = state.modelLineMap.get(currentCarInfo.modelUnicode).modelLineCode;
        let base = `${OSS_URL}/ccpro-backend/${getters.currentSeriesName}/carImages/${modelLineCode}/${currentExterior.featureCode}/${currentHub.featureCode}/`;

        // 红色卡钳
        if(!isEmptyObj(currentHub.packet) && Array.isArray(currentHub.packet.labelChildren) && currentHub.packet.labelChildren.length > 0) {
          for (const labelChild of currentHub.packet.labelChildren) {
            if(!isEmptyObj(A5L_RED_RAD[labelChild.featureCode])) {
              base += "redRad/";
              break;
            }
          }
        }

        const imgUrlList = MAIN_IMG_SIX_MAP.map((name) => base + name);
        return imgUrlList;
      }

      if (!currentHub.optionCode || !currentExterior.optionCode) return []

      const exist = getters.pageOutColorArray.find((i) => i.optionCode === currentExterior.optionCode)
      if (!exist) return []

      // example img: https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/q5e/carImages/G4IBC3004/2T2T/56E/Front.png
      let hubCode = currentHub.optionCode
      if (getters.currentSeriesName === 'q6') {
        const optionPC2 = personalOptions.find((option) => option.optionCode === 'PC2' && option.condition === 1)

        // q6 选中了带卡钳(pc2)的轮毂
        if (currentHub.optionCode.includes('PC2')) {
          hubCode = currentHub.optionCode.replace(/\+/g, '/')
        } else if (optionPC2) { // 标装里带pc2
          // hubCode = `${currentHub.optionCode}/PC2`
        } else {
          // 推荐组合里有pc2
          const composeName = state.currentComposeName
          console.log('%c 333', 'font-size:16px;color:green;', composeName, getters.pageOptionComposes)
          if (composeName) {
            for (const item of getters.pageOptionComposes) {
              if (item.composeName === composeName) {
                const hasPc2 = item.composePersonalOptions.find((i) => i.optionCode === 'PC2')
                if (hasPc2) {
                  hubCode = `${currentHub.optionCode}/PC2`
                }
              }
            }
          }
          if (state.selectedOptions.find((i) => i.optionCode === 'PC2')) {
            // 全部选装里有pc2
            hubCode = `${currentHub.optionCode}/PC2`
          }
        }
      }
      const base = `${OSS_URL}/ccpro-backend/${getters.currentSeriesName}/carImages/${currentModelLineData.modelLineCode}/${currentExterior.optionCode}/${hubCode}/`

      const imgUrlList = MAIN_IMG_SIX_MAP.map((name) => base + name)
      return imgUrlList
    },

    // 页面中显示的外观颜色列表
    pageOutColorArray(state, getters) {
      const {
        outColorArray, sibArray, personalOptions, currentSeat, currentModelLineData
      } = state
      let resList = outColorArray

      /**
       * A7mr车型特殊需求 (非rs 和非黑武士车型)
       * 如果当前车型选择了新潮套装, 外色都不展示黑车顶的
       * 如果当前车型选择了动感套装, 外色都只展示黑车顶的
       */
      const normalA7MR = A7MR.filter((i) => i.suit === XINCHAO)
      const isA7MR = normalA7MR.find((i) => i.code === currentModelLineData.modelLineCode)

      // 是否选择了动感套装选装包
      const isSelDonggan = DONGGAN_CODE.includes(currentModelLineData.optionCode)
      if (isA7MR) {
        if (isSelDonggan) {
          resList = resList.filter((i) => BLACK_TOP_EXCOLOR_CODE.includes(i.optionCode))
        } else {
          resList = resList.filter((i) => !BLACK_TOP_EXCOLOR_CODE.includes(i.optionCode))
        }
      }

      /**
       * 仅A类车规则: 非半定车
       * 如果当前车型只开了A类, 轮毂的列表依赖于当前外观颜色的typeIdsOfA
       * 需要展示的轮毂的typeIdsOfA 必须包含外观颜色的 typeIdsOfA, 否则不展示到页面中
       */
      if (currentModelLineData.measure !== 1) {
        if (currentModelLineData.typeFlag === 'A') {
          const exteriorIds = getters.paramTypeIdsOfA.exterior
          resList = resList.filter((out) => out.typeIdsOfA.some((typeId) => exteriorIds.includes(typeId)))
        }
      }

      if (resList.length === 0) {
        console.warn('外观颜色列表为空')
      }
      return resList
    },


    // 页面中显示的轮毂列表
    pageHubArray(state, getters) {
      const { hubArray, currentModelLineData, currentExterior } = state

      let resultArr = hubArray
      /**
       * 仅A类车规则: 非半定车
       * 如果当前车型只开了A类, 轮毂的列表依赖于当前外观颜色的typeIdsOfA
       * 需要展示的轮毂的typeIdsOfA 必须包含外观颜色的 typeIdsOfA, 否则不展示到页面中
       */
      if (currentModelLineData.measure !== 1) {
        if (currentModelLineData.typeFlag === 'A' && !isEmptyObj(currentExterior)) {
          const ids = getters.paramTypeIdsOfA.hub
          resultArr = resultArr.filter((hub) => hub.typeIdsOfA.some((typeId) => ids.includes(typeId)))
        }
      }

      /**
       * q6 特殊需求
       * 有一个红色卡钳的选装包（PC2）,需要前置到轮毂列表,做一个虚拟轮毂显示供用户选择
       * 这里分三个条件
       * 1. 仅a类车:  需要判断每个轮毂是否与推荐组合里的PC2,typeIdsOfA有交集
       * 2. 不包含c:  从推荐组合里查询PC2, 有就为每个轮毂生成带卡钳的轮毂副本
       * 3. 包含c: 从全部选装里查询PC2,有就为每个轮毂生成带卡钳的轮毂副本
       */
      const isQ6 = getters.currentSeriesName === 'q6'
      if (isQ6) {
        const onlyA = currentModelLineData.typeFlag === 'A'
        const isIncludedC = currentModelLineData.typeFlag?.includes('C')

        let createPC2Hub = false // 是否生成PC2轮毂
        let pc2OptionId = ''
        let pc2TypeIdsOfA = []

        if (onlyA) {
          // 仅a类车
          for (const item of getters.pageOptionComposes) {
            const includePC2 = item.composePersonalOptions.find((i) => i.optionCode === 'PC2')
            if (includePC2) {
              createPC2Hub = true
              pc2OptionId = includePC2.optionId
              pc2TypeIdsOfA = item.typeIdsOfA
            }
          }
        } else {
          if (isIncludedC) {
            // 非a类车,包含c
            const includePC2 = getters.pageAllOptionList.find((i) => i.optionCode === 'PC2')
            if (includePC2) {
              createPC2Hub = true
              pc2OptionId = includePC2.optionId
            }
          } else {
            // 非a类车,不包含c
            for (const item of getters.pageOptionComposes) {
              const includePC2 = item.composePersonalOptions.find((i) => i.optionCode === 'PC2')
              if (includePC2) {
                createPC2Hub = true
                pc2OptionId = includePC2.optionId
              }
            }
          }
        }
        console.log('%c getters.pageAllOptionList', 'font-size:16px;color:green;', getters.pageAllOptionList)
        const isQ6RS = currentModelLineData.modelLineCode === 'G6ICAY006'
        const condition1 = currentModelLineData.modelLineName.includes('武士') && currentModelLineData.modelYear === '2024'
        const condition1PC2 = getters.pageAllOptionList.find((i) => i.optionCode === 'PC2' && i.condition === 1)
        if (isQ6RS || condition1 || condition1PC2) {
          resultArr = resultArr.map((item) => ({
            ...item,
            optionName: `${item.optionName} 红色卡钳套装`
          }))
        }
      }


      if (resultArr.length === 0) {
        console.warn('轮毂列表为空', resultArr)
      }
      return resultArr
    },

    // 页面中显示的内饰列表
    pageSibArray(state, getters) {
      const {
        sibArray, currentSeat, personalOptions, currentModelLineData, currentExterior, currentHub
      } = state
      let resultArr = sibArray.filter((i) => i.status !== 0)

      /**
       * 如果当前用户在车型页选择了六座选装包
       * 删除与选装互斥的内饰
       * ex: 座椅与面料的依赖
       * 1. q6 观云型 默认7座, 逻辑是查找六座选装包ps1, 把六座包depend的内饰给过滤删除掉
       * 2. 选择六座后,把六座包depend的内饰显示,其他的过滤删除掉.
       */
      if (getters.currentSeriesName !== 'a7l') {
        const seatCode = SEAT_DATA_Q5_Q6[getters.currentSeriesName].defaultOptionCode

        const option = personalOptions.find((i) => i.optionCode === seatCode)
        if (option) {
          const depend = getFilterRelateList(option.optionRelates, 'depend')
          const dependSib = depend.filter((i) => i.optionRelateCategory === 'F_SIB_COLOR_INTERIEUR')
          const is7Seat = state.currentSeat.tagCode?.includes('7SEAT')
          if (dependSib.length > 0) {
            if (is7Seat) {
              // 七座 就过滤掉六座依赖的内饰
              resultArr = resultArr.filter((i) => !dependSib.find((j) => j.optionRelateCode === i.sibInterieurCode))
            } else {
              // 六座就过滤掉非依赖的内饰
              resultArr = resultArr.filter((i) => dependSib.find((j) => j.optionRelateCode === i.sibInterieurCode))
            }
          }
        }
      }

      /**
       * 仅A类车规则: 非半定车
       * 如果当前车型只开了A类:
       * 需要展示的内饰的typeIdsOfA 必须包含外观颜色和轮毂的 typeIdsOfA的交集, 否则不展示到页面中
       */
      if (currentModelLineData.measure !== 1) {
        const isEmpty = !isEmptyObj(currentExterior) && !isEmptyObj(currentHub)
        if (currentModelLineData.typeFlag === 'A' && isEmpty) {
          const ids = getters.paramTypeIdsOfA.sib
          resultArr = resultArr.filter((hub) => hub.typeIdsOfA.some((typeId) => ids.includes(typeId)))
        }
      }

      /**
       * A7Mr前置选装(动感套装)对面料的过滤
       */
      if (getters.paramDongGanConflictOptionList) {
        const { conflicts } = getters.paramDongGanConflictOptionList
        const sibConflicts = conflicts.filter((op) => op.optionRelateCategory === 'F_SIB_COLOR_INTERIEUR')
        if (sibConflicts.length > 0) {
          resultArr = resultArr.filter((i) => !sibConflicts.find((j) => j.optionRelateCode === i.sibInterieurCode))
        }
      }

      if (resultArr.length === 0) {
        console.warn('内饰列表为空', resultArr)
      }
      return resultArr
    },

    // 页面中显示的饰板列表
    pageEihArray(state, getters) {
      const {
        eihArray, currentModelLineData, currentExterior, currentHub, currentSib
      } = state
      let resultArr = eihArray

      /**
       * 如果当前用户在车型页选择了六座选装包
       * 筛选与座椅选装互斥的饰板
       */
      if (getters.currentSeriesName !== 'a7l') {
        const seatCode = SEAT_DATA_Q5_Q6[getters.currentSeriesName].defaultOptionCode

        const option = state.personalOptions.find((i) => i.optionCode === seatCode)
        if (option) {
          const depend = getFilterRelateList(option.optionRelates, 'depend')
          const dependEih = depend.filter((i) => i.optionRelateCategory === 'EIH')
          const is7Seat = state.currentSeat.tagCode?.includes('7SEAT')
          if (dependEih.length > 0) {
            if (is7Seat) {
              // 七座 就过滤掉六座依赖的内饰
              resultArr = resultArr.filter((i) => !dependEih.find((j) => j.optionRelateCode === i.optionCode))
            } else {
              // 六座就过滤掉非依赖的内饰
              resultArr = resultArr.filter((i) => dependEih.find((j) => j.optionRelateCode === i.optionCode))
            }
          }
        }
      }

      /**
       * 仅A类车规则: 非半定车
       * 如果当前车型只开了A类:
       * 需要展示的饰板的typeIdsOfA 必须包含外观颜色和轮毂还有面料的 typeIdsOfA的交集, 否则不展示到页面中
       */
      if (currentModelLineData.measure !== 1) {
        const isEmpty = !isEmptyObj(currentExterior) && !isEmptyObj(currentHub) && !isEmptyObj(currentSib)
        if (currentModelLineData.typeFlag === 'A' && isEmpty) {
          const ids = getters.paramTypeIdsOfA.eih
          resultArr = resultArr.filter((eih) => eih.typeIdsOfA.some((typeId) => ids.includes(typeId)))
        }
      }

      if (resultArr.length === 0) {
        console.warn('饰板列表为空', resultArr)
      }
      return resultArr
    },

    // 页面显示的全部选装列表
    pageAllOptionList(state, getters) {
      const { currentSeat, personalOptions, currentModelLineData } = state
      const seriesName = getters.currentSeriesName

      let hideList = []
      if (SEAT_DATA_Q5_Q6[seriesName]) {
        if (currentSeat.status === 1) {
          // 6座
          hideList = SEAT_DATA_Q5_Q6[seriesName].seat6HideCodeList
        } else {
          // 默认
          hideList = SEAT_DATA_Q5_Q6[seriesName].seat7HideCodeList
        }
      }
      let filterList = personalOptions.filter((i) => !hideList.find((j) => j === i.optionCode))

      /**
       * q5e荣耀型 选装和标装互斥的逻辑
       * G4IBF3003 荣耀锦衣  选装8I6,隐藏标装4D3
       * G4ICF3007 荣耀机甲  选装4D3,隐藏标装8I6
       * G4ICF3012 光耀机甲  选装4D3,隐藏标装8I6
       * G4IBF3005 光耀锦衣  选装8I6,隐藏标装4D3
       * 2025/3 新增
       * G4ICF3017 光耀机甲  选装8I6,隐藏标装4D3
       * G4IBF3007 光耀锦衣  选装8I6,隐藏标装4D3
       */
      if (
        [
          'G4IBF3003',
          'G4ICF3007',
          'G4ICF3012',
          'G4IBF3005',
          'G4ICF3017',
          'G4IBF3007'
        ].includes(currentModelLineData.modelLineCode)) {
        const massage = filterList.find((i) => i.optionCode === '8I6')
        const ventilate = filterList.find((i) => i.optionCode === '4D3')

        if (massage.selected && massage.status === 2) {
          filterList = filterList.filter((i) => i.optionCode !== ventilate.optionCode)
        }

        if (ventilate.selected && ventilate.status === 2) {
          filterList = filterList.filter((i) => i.optionCode !== massage.optionCode)
        }
      }
      // A7筑梦未来,数据里有两个4I3 这里处理过滤一个 optionType 为hotpost的
      if (currentModelLineData.modelLineCode === '498BZG003') {
        filterList = filterList.filter((i) => (i.optionCode !== '4I3' || i.optionType !== 'hotspot'))
      }

      /**
       * A7mr车型需求
       * 1. 车型页面选择的套装为新潮套装,需要过滤动感套装的选装包
       * 2. 车型页面选择的套装为动感套装，需要过滤动感套装互斥的选装包
       */
      const isA7MR = A7MR.find((i) => i.code === currentModelLineData.modelLineCode)
      const isXinchao = state.currentModelLineData.suit === XINCHAO
      if (isA7MR && isXinchao) {
        filterList = filterList.filter((i) => !DONGGAN_CODE.includes(i.optionCode))
      }

      if (getters.paramDongGanConflictOptionList) {
        const { conflicts } = getters.paramDongGanConflictOptionList
        // 这里主要针对 6nq 处理
        const himConflicts = conflicts.find((op) => op.optionRelateCode === '6NQ')
        if (himConflicts) {
          filterList = filterList.filter((op) => op.optionCode !== '6NQ')
        }
      }

      return filterList
    },

    // 页面显示的推荐组合列表
    pageOptionComposes(state, getters) {
      const { currentSeat, personalOptionComposes, currentModelLineData } = state

      const currentCarTypeFlag = currentModelLineData.typeFlag
      let res = personalOptionComposes
      /**
       * 仅A类车规则: 非半定车
       * 如果当前车型只开了A类, 轮毂的列表依赖于当前外观颜色的typeIdsOfA
       * 需要展示的轮毂的typeIdsOfA 必须包含外观颜色的 typeIdsOfA, 否则不展示到页面中
       */
      if (currentModelLineData.measure !== 1) {
        if (currentCarTypeFlag === 'A') {
          const ids = getters.paramTypeIdsOfA.optionCompose
          res = res.filter((eih) => eih.typeIdsOfA.some((typeId) => ids.includes(typeId)))
        }
      }

      /**
       * 这里主要根据当前的座椅数量,来进行过滤,不考虑5座的,只考虑6座和7座
       * 只有q5e和q6才有座椅选择
       */
      if (getters.currentSeriesName !== 'a7l' && !isEmptyObj(currentSeat)) {
        const seatNum = getters.currentSeatsNum
        res = res.filter((i) => i.seats === seatNum)
      }

      // 搜索到夏日礼包,则排到首位 (夏日礼包下线时可删除)
      SUMMER_PACKAGE.forEach((item) => {
        const resCode = res.map((i) => i.composePersonalOptions[0]?.optionCode)
        if (resCode.includes(item)) {
          const index = res.findIndex((el) => el.composePersonalOptions[0]?.optionCode === item)
          res.unshift(res.splice(index, 1)[0])
        }
      })

      /**
       * A7Mr前置选装(动感套装)对推荐组合的过滤
       * 组合包里有互斥的内容就过滤掉此组合
       */
      if (getters.paramDongGanConflictOptionList) {
        const { conflicts } = getters.paramDongGanConflictOptionList
        // 这里主要针对 6nq 处理
        const himConflicts = conflicts.filter((op) => op.optionRelateCode === '6NQ')
        if (himConflicts.length > 0) {
          res = res.filter((compose) => !compose.composePersonalOptions.find((i) => i.optionCode === '6NQ'))
        }
      }

      return res
    },

    // 常用的参数 optionsIds
    paramOptionIds(state, getters) {
      /**
       * 这里的需求是如果没有配置,就不传
       * 车型页 + 外观页: 外观色 | 轮毂 |  六七座选装包
       * 内饰页: 外观色 | 轮毂 | 饰板 | 内饰座椅
       */
      let optionIds = []

      // 轮毂optionId array, 兼容q6的红色卡钳
      const hubOptionIdArr = Array.isArray(state.currentHub.optionId)
        ? [...state.currentHub.optionId]
        : [state.currentHub.optionId]

      if (state.configrationActiveTab === 'exterior') {
        optionIds = [
          state.currentExterior.optionId,
          ...hubOptionIdArr
        ]
      } else {
        optionIds = [
          state.currentExterior.optionId,
          ...hubOptionIdArr,
          state.currentEih.optionId,
          state.currentSib.sibOptionId,
          state.currentSib.interieurOptionId
        ]
        /**
         * 按照需求,暂时先不传座椅数据给后端
         */
        // if (state.currentVos.optionId) {
        //   optionIds.push(state.currentVos.optionId)
        // }
      }

      /**
       * 选装包: 分推荐组合和全部选装
       * currentComposeName 有值,说明选中推荐组合
       */
      let selectedOptionIds = []
      if (state.currentComposeName) {
        const compose = getters.pageOptionComposes.find((i) => i.composeName === state.currentComposeName)
        if (!compose) {
          return console.error('没有找到当前推荐组合')
        }
        selectedOptionIds = compose.composePersonalOptions.map((i) => i.optionId)
      } else {
        selectedOptionIds = state.selectedOptions.map((i) => i.optionId)
      }

      const res = [...optionIds, ...selectedOptionIds]
      return res
    },

    // 当前面料依赖的选装列表(选装页使用)
    paramSibDependOptionList(state) {
      const { currentSib, personalOptions } = state
      const depdends = getFilterRelateList(currentSib.sibInterieurRelates, 'depend')
      const res = depdends.filter((i) => i.optionRelateCategory === 'PACKET')
      return res
    },

    // 当前面料互斥的选装列表(选装页使用)
    paramSibConflictOptionList(state) {
      const { currentSib, personalOptions } = state
      const conflicts = getFilterRelateList(currentSib.sibInterieurRelates, 'conflict')
      // const res = conflicts.map((i) => i.optionRelateCode)
      const res = conflicts.filter((i) => i.optionRelateCategory === 'PACKET')
      return res
    },

    // 当前饰板依赖和冲突的选装列表(选装页使用)
    paramEihDependsOptionList(state) {
      const { currentEih, personalOptions } = state
      const depdends = getFilterRelateList(currentEih.optionRelates, 'depend')
      const conflicts = getFilterRelateList(currentEih.optionRelates, 'conflict')

      const eihDepends = depdends.filter((i) => i.optionRelateCategory === 'PACKET')
      const eihConflicts = conflicts.filter((i) => i.optionRelateCategory === 'PACKET')
      return {
        eihDepends,
        eihConflicts
      }
    },

    /**
     * 当前选装包里互斥的列表:
     * a7mr上线后，动感套装（选装包）改为前置选择
     * 目前此方法只针对动感套装 （PAH+PC2）(PAH) 生效
     */
    paramDongGanConflictOptionList(state) {
      const { selectedOptions } = state
      const dongGanOption = selectedOptions.find((i) => DONGGAN_CODE.some((code) => code === i.optionCode))
      if (dongGanOption) {
        const depdends = getFilterRelateList(dongGanOption.optionRelates, 'depend')
        const conflicts = getFilterRelateList(dongGanOption.optionRelates, 'conflict')
        return {
          depdends,
          conflicts
        }
      }
    },

    // 仅A类车的交集id, A类车每一步展示的配置都要经过这个过滤
    paramTypeIdsOfA(state, getters) {
      const {
        currentSeat, currentExterior, currentHub, currentSib, currentEih, currentModelLineData
      } = state
      /**
       * 外观:过滤座椅包的交集
       * 轮毂:过滤座椅包+外观的交集
       * 面料:过滤座椅包+外观+轮毂的交集
       * 饰板:过滤座椅包+外观+轮毂+面料的交集
       * 推荐组合:过滤座椅包+外观+轮毂+面料+饰板的交集
       */

        // 外观
      const seatStringNum = getters.currentSeatsNum
      const seat = currentModelLineData.typeIdsBySeats.find((i) => i.seats === seatStringNum)
      const useExteriorIds = seat.typeIdsOfA
      // 轮毂
      const useHubIds = useExteriorIds.filter((typeId) => currentExterior.typeIdsOfA?.includes(typeId))
      // 面料
      const useSibIds = useHubIds.filter((typeId) => currentHub.typeIdsOfA?.includes(typeId))
      // 饰板
      const useEihIds = useSibIds.filter((typeId) => currentSib.typeIdsOfA?.includes(typeId))
      // 推荐组合
      const useComposeIds = useEihIds.filter((typeId) => currentEih.typeIdsOfA?.includes(typeId))
      return {
        exterior: useExteriorIds,
        hub: useHubIds,
        sib: useSibIds,
        eih: useEihIds,
        optionCompose: useComposeIds
      }
    },
    // 获取当前轮毂套装，在有早鸟包时优惠后价格
    getHubDiscountPrice(state, getters) {
      const { currentHub, currentVersionMap } = state
      const packet = currentHub?.packet ?? {};
      const labelChildren = packet.labelChildren ?? [];
      let packetPrice = packet.featurePrice ?? 0;
      if (!packetPrice) {
        return 0;
      }
      packetPrice = parseInt(packetPrice);
      let excludedPrice = 0;
      for (const children of labelChildren) {
        const item = currentVersionMap.get(children.featureCode);
        if(item.equipmentRights === "1" && !isEmptyObj(item.featurePrice)) {
          excludedPrice += parseInt(item.featurePrice);
        }
      }
      return packetPrice - excludedPrice;
    },

    // 是否购买了早鸟包，目前只有A5L在使用
    isCouponValid(state, getters) {
      const { userCoupon } = state
      if (!userCoupon) {
        return false;
      }
      // 获取两个字段的值
      const { activityCode, mpEquityNo } = userCoupon;

      // 检查两个字段是否都存在且有实际值（非空字符串、非null、非undefined）
      return Boolean(
        activityCode !== undefined &&
        activityCode !== null &&
        activityCode !== "" &&
        mpEquityNo !== undefined &&
        mpEquityNo !== null &&
        mpEquityNo !== ""
      );
    },

    isHubEquipmentRights(state, getters) {
      if (!getters.isCouponValid) {
        return false;
      }
      const {currentHub, currentVersionMap} = state;
      if(isEmptyObj(currentHub) || currentVersionMap === undefined || currentVersionMap === null) {
        return false;
      }

      // 轮毂有权益
      if (currentHub.equipmentRights == "1") {
        return true;
      }

      // 卡钳有权益
      if (!isEmptyObj(currentHub.packet)) {
        for (const children of currentHub.packet.labelChildren) {
          const radOrBahOrBav = currentVersionMap.get(children.featureCode);
          if (radOrBahOrBav.equipmentRights === "1") {
            return true;
          }
        }
      }
      return false;
    },
  },

  mutations: {
    updateCarIdx(state, idx) {
      state.carIdx = idx
    },
    updateCarModelTab(state, name) {
      state.carModelActiveTab = name;
    },
    updateConfigrationActiveTab(state, name) {
      state.referConfigrationActiveTab = state.configrationActiveTab // 记录上一次的tab(埋点使用)
      state.configrationActiveTab = name
    },

    updateCurrentCarSeriesData(state, data) {
      state.currentCarSeriesData = data
    },

    updateCurrentVersionData(state, data) {
      state.currentVersionData = sortByMsrp(data);
    },
    updateCurrentVersion(state, data) {
      if (data.modelLineList) {
        data.modelLineList = data.modelLineList.sort((pre, next) => pre.price - next.price);
        // a7mr 设置套装字段
        data.modelLineList.forEach((item) => {
          const a7item = A7MR.find((i) => i.code === item.modelLineCode);
          if (a7item) {
            item.suit = a7item.suit;
          }
        });
        state.currentVersion = data;
      } else {
        const featureMap = new Map();
        data.children.forEach(item => {
          featureMap.set(item.featureCode, item);
        });
        state.currentVersionMap = featureMap;

        const carConfigs = processCarConfigData(data.children, state);
        console.log("%c data数据", "background:pink; color:#bf2c9f;", data);
        console.log("%c 刷过的carConfigs数据", "background:pink; color:#bf2c9f;", carConfigs);
        console.log("%c A5L currentVersionMap", "font-size:16px;color:green;", state.currentVersionMap);
        state.currentCarInfo = data;
        state.currentVersion = carConfigs;
        state.selectedOptions = [...carConfigs.tempDefaultPackets, ...carConfigs.tempDefaultOptionalEquips];
      }
    },

    // 更新当前座椅(六七座)
    updateCurrentSeat(state, data) {
      state.currentSeat = data
    },

    // 更新外观列表
    updateOutColorArray(state, data) {
      const list = data.filter((i) => i.status !== 0)
      const resList = wrapArray(list)
      state.outColorArray = resList
    },

    // 更新轮毂列表
    updateHubArray(state, data) {
      const list = data.filter((i) => i.status !== 0)
      const resList = wrapArray(list)
      state.hubArray = resList
    },

    // 更新面料列表
    updateSibArray(state, data) {
      const list = data.filter((i) => i.status !== 0 && i.status !== 3)
      const resList = wrapArray(list)
      state.sibArray = resList
    },

    // 更新饰板列表
    updateEihArray(state, data) {
      const list = data.filter((i) => i.status !== 0)
      const resList = wrapArray(list)
      state.eihArray = resList
    },

    // 更新座椅列表
    updateVosArray(state, data) {
      // const list = data.filter((i) => i.status !== 0)
      state.vosArray = data
    },

    updateCurrentVos(state, data) {
      state.currentVos = data
    },

    updateCurrentModelLineData(state, data) {
      state.currentModelLineData = data
    },

    updateCurrentOptionDetail(state, data) {
      state.currentOptionDetail = data
    },

    updateEquityDetail(state, data) {
      state.currentEquityDetail = data
    },

    updateCurrentExterior(state, data) {
      if (!data) {
        return console.error(`updateCurrentExterior 参数为 ${data}`)
      }
      state.currentExterior = data
    },

    updateCurrentHub(state, data) {
      if (!data) {
        return console.error(`updateCurrentHub 参数为 ${data}`)
      }
      state.currentHub = data
    },

    updateCurrentSib(state, data) {
      if (!data) {
        return console.error(`updateCurrentSib 参数为 ${data}`)
      }
      state.currentSib = data
    },

    updateCurrentEih(state, data) {
      if (!data) {
        return console.error(`updateCurrentEih 参数为 ${data}`);
      }
      state.currentEih = data;
    },

    updateCurrentII(state, data) {
      if (!data) {
        return console.error(`updateCurrentII 参数为 ${data}`);
      }
      state.currentII = data;
    },

    updateUserCoupon(state, data) {
      if (!data) {
        return console.error(`updateUserCoupon 参数为 ${data}`);
      }
      state.userCoupon = data;
    },

    updateRequiredRadPackets(state, data) {
      if (!data) {
        return console.error(`updateRequiredRadPackets 参数为 ${data}`);
      }
      Object.assign(state.requiredRadPackets, data);
    },

    updateModelLineMap(state, data) {
      if (!data) {
        return console.error(`updateModelLineMap 参数为 ${data}`);
      }
      state.modelLineMap = data;
    },

    updateFooterDesc(state, data) {
      state.footerDesc = data
    },

    updateSelectedOptions(state, data) {
      const res = data

      /**
       * 处理前置选装的逻辑（车型页面：选择动感套装）
       * 前置选装选择后，在配置过程中需要一直携带
       * A7Mr 动感套装
       */
      if (DONGGAN_CODE.includes(state.currentModelLineData.optionCode)) {
        const dongganOption = state.personalOptions.find((i) => DONGGAN_CODE.some((code) => code === i.optionCode))
        const includeDonggan = res.find((i) => DONGGAN_CODE.some((code) => code === i.optionCode))
        if (!includeDonggan) {
          res.push(dongganOption)
        }
      }

      // 检测更新的数据如果跟当前的数据一样就不更新
      const a1 = state.selectedOptions.map((i) => i.optionCode)
      const a2 = res.map((i) => i.optionCode)
      if (!arraysEqual(a1, a2)) {
        state.selectedOptions = res
      }
    },
    updatePersonalOptionComposes(state, data) {
      data.forEach((i) => {
        i.selected = false // 是否选中
        i.disabled = false // 是否禁用
        i.dependsTag = false // 是否显示依赖的文案
      })
      state.personalOptionComposes = data
    },
    updatePersonalOptions(state, data) {
      data.forEach((i) => {
        i.selected = false // 是否选中
        i.disabled = false // 是否禁用
        i.dependsTag = false // 是否显示依赖的文案
      })
      /**
       * 更新数据之前排序
       * 1. 选装在最前面
       * 2. 标装在最后面
       */
      const res = data.sort((a, b) => (b.status === 2) - (a.status === 2))
      state.personalOptions = res.filter((i) => i.status !== 0)
    },

    onlySetSeatOptions(state, data) {
      state.onlySeatOption = data.filter((i) => i.status !== 0)
    },

    updateDeliveryTimeData(state, data) {
      // data.deliveryTime //交付时间
      // data.earnestMoney // 定金
      // data.type // A B C 类车
      state.deliveryTimeData = data
    },

    updateTotalPrice(state, data) {
      state.totalPrice = data
    },

    updateEquityPrice(state, data) {
      state.equityPrice = data
    },

    updateQueryParams(state, data) {
      state.queryParams = data
    },

    updateCurrentComposeName(state, name) {
      state.currentComposeName = name
    },
    // 更新半定下座椅配置的显示隐藏条件
    updateMeasureSeatData(state, data) {
      state.measureSeatDataVisible = data
    },

    // 清理配置
    clearSelectedConfig(state, tags) {
      for (const name of tags) {
        switch (name) {
          case 'carModel':
            state.currentModelLineData = {}
            state.currentSeat = {}
            break

          case 'exterior':
            state.currentExterior = {}
            state.currentHub = {}
            break
          case 'interior':
            state.currentSib = {}
            state.currentEih = {}
            // state.currentVos = {}
            break
          case 'option':
            // 处理座椅前置选择的case
            if (state.currentSeat.status === 1) {
              const seatOption = state.onlySeatOption.find((i) => i.optionCode === state.currentSeat.optionCode)
              if (seatOption) {
                state.selectedOptions = [seatOption]
              }
            } else if (DONGGAN_CODE.includes(state.currentModelLineData.optionCode)) {
              // a7Mr 动感套装逻辑处理:目前只是从全部选装里查找:::
              const dongganOption = state.personalOptions.find((i) => DONGGAN_CODE.some((code) => code === i.optionCode))
              if (dongganOption) {
                state.selectedOptions = [dongganOption]
              }
            } else {
              state.selectedOptions = []
            }
            state.currentComposeName = ''
            break
          default:
            break
        }
      }
    },

    // 设置
    setProjectStartTime(state) {
      state.projectStartTime = new Date().getTime()
    },

    // 设置下单时间
    updateOrderTime(state, time) {
      state.orderTime = time
    }
  },

  actions: {
    // 获取当前的车系数据,
    async getCarSeriesData({ commit, state }, idx) {
      //  idx =>  0: a7l, 1: q5e, 2:q6
      const carIdx = getQueryParam('idx') ?? idx
      if (!carIdx) {
        return console.error('URL 参数里无 idx, 无法获取车系id')
      }
      const res = await getCarList()
      const currentCarSeriesData = res.data.data[Number(carIdx)]
      commit('updateCarIdx', carIdx)
      commit('updateCurrentCarSeriesData', currentCarSeriesData)
    },

    /**
     * 车型页执行选中座椅的逻辑
     * 只有q5 q6 才有座椅选择
     */
    async setSelectSeat({
                          commit, state, getters, dispatch
                        }, data) {
      if (getters.currentSeriesName === 'a7l') return

      commit('updateCurrentSeat', data)

      if (Q6_SAMURAI.includes(data.modelLine?.modelLineCode)) {
        commit('updateCurrentModelLineData', data.modelLine)
      }

      // 选中了六座, 更新对应的选装包
      if (data.status === 1) {
        const seriesName = getters.currentSeriesName
        const code = SEAT_DATA_Q5_Q6[seriesName].defaultOptionCode
        for (const item of state.onlySeatOption) {
          if (item.optionCode === code) {
            item.selected = true
            // 这个逻辑开了c的话应该会有问题..
            commit('updateSelectedOptions', [item])
            break
          }
        }
      } else {
        commit("updateSelectedOptions", []);
      }
    },

    async getOmdModelConfigParams({ state, commit }, { item, key, alterationType = 1 }) {
      console.log("%c A5L getOmdModelConfigParams:", "font-size:16px;color:green;", item, key, alterationType);

      let childrenParams = [];
      if (item) {
        if (key === "PACKET" && item.labelChildren && item.labelChildren.length > 0) {
          for (const labelChildren of item.labelChildren) {
            childrenParams.push({
              labelCode: state.currentVersionMap.get(labelChildren.featureCode).labelCode,
              familyCode: labelChildren.familyCode,
              featureCode: labelChildren.featureCode,
              alterationType: alterationType
            });
          }
        } else if(key === "RAD" && item.packet && item.packet.labelChildren && item.packet.labelChildren.length > 0) {
          for (const labelChildren of item.packet.labelChildren) {
            childrenParams.push({
              labelCode: state.currentVersionMap.get(labelChildren.featureCode).labelCode,
              familyCode: labelChildren.familyCode,
              featureCode: labelChildren.featureCode,
              alterationType: alterationType
            });
          }
        } else {
          childrenParams.push({
            labelCode: item.labelCode,
            familyCode: item.familyCode,
            featureCode: item.featureCode,
            alterationType: alterationType
          });
        }
      }
      let params = {
        "brandCode": "A",
        "seriesCode": state.currentCarInfo.seriesCode,
        "modelUnicode": state.currentCarInfo.modelUnicode,
        "mstMgrpId": state.currentCarInfo.mstMgrpId,
        "children": childrenParams,
      };

      return params;
    },

    async computeA5LTotalPriceAndEquityPrice({ state, commit, getters, }, {isCouponValid}) {
      let totalPrice = 0;
      let equityPrice = 0;

      // 车型
      totalPrice += state.currentModelLineData?.msrp ?? 0;

      // 外饰
      let currentExteriorPrice = parseInt(state.currentExterior?.featurePrice ?? 0);
      if(isCouponValid && state.currentExterior.equipmentRights == "1") {
        equityPrice += currentExteriorPrice;
        currentExteriorPrice = 0;
      }

      // 轮毂
      let currentHubPrice = parseInt(state.currentHub.packet ? state.currentHub.packet.featurePrice : state.currentHub.featurePrice ?? 0);
      if(getters.isHubEquipmentRights) {
        const hubDiscountPrice = getters.getHubDiscountPrice;
        equityPrice = equityPrice + (currentHubPrice - hubDiscountPrice);
        currentHubPrice = hubDiscountPrice;
      }

      // 内饰
      const currentIIPrice = state.currentII.packet ? state.currentII.packet.featurePrice : state.currentII.featurePrice ?? 0;
      const currentEihPrice = state.currentEih?.featurePrice ?? 0;

      totalPrice  += currentExteriorPrice + currentHubPrice + parseInt(currentIIPrice) + parseInt(currentEihPrice);
      for (const item of state.selectedOptions) {
         let price = parseInt(item.featurePrice ?? 0);
        if(isCouponValid && item.equipmentRights == "1") {
          equityPrice += price;
          price = 0;
        }
        totalPrice += price;
      }

      commit("updateTotalPrice", totalPrice);
      commit("updateEquityPrice", equityPrice);
    },

    /**
     * 1. 计算交付时间/定金/abc类车
     * 2. 计算总价
     */
    async setTimeAndPrice({ commit, state, dispatch }) {
      await Promise.all([
        dispatch('getDeliveryTimeAndDeposit'),
        dispatch('getTotalPrice')
      ])
    },

    // 获取当前交付时间, 交付定金
    async getDeliveryTimeAndDeposit({
                                      commit, state, dispatch, getters
                                    }) {
      // 参数: beforeCheck 选装页: false, 其他页: true
      const param = {
        beforeCheck: state.configrationActiveTab !== 'option',
        seats: getters.currentSeatsNum,
        customSeriesId: getters.currentSeriesId,
        modelLineId: getters.currentSeriesName === "a5l" ? state.currentModelLineData?.cc?.modelLineId : state.currentModelLineData.modelLineId,
        optionIds: getters.currentSeriesName === "a5l" ? [] : getters.paramOptionIds,
        measure: getters.currentSeriesName === "a5l" ? state.currentModelLineData?.cc?.measure : state.currentModelLineData.measure
      }
      const res = await postCcEstimate(param)

      const data = res.data.data
      if (res.data.code !== '00') {
        console.error('获取交付时间失败')
      }

      commit('updateDeliveryTimeData', res.data.data)

      // 更新交付时间
      commit('updateFooterDesc', {
        desc: data.deliveryTime
      })
    },

    // 计算价格,要分半定和高定接口
    async getTotalPrice({
                          commit, state, dispatch, getters
                        }) {
      const modelLineId = state.currentModelLineData.modelLineId
      const optionIds = getters.paramOptionIds
      if (optionIds.some((i) => !i)) {
        return console.warn('optionIds 有空值')
      }

      let res = null
      if (state.currentModelLineData.measure === 1) {
        // 半定
        res = await getMeasurePriceCompute(modelLineId, optionIds)
      } else {
        // 高定
        res = await getPriceCompute(modelLineId, optionIds)
      }

      if (res.data.code !== '00') {
        return console.error('获取价格失败', res.data.msg)
      }
      commit('updateTotalPrice', res.data.data)
    },

    // 设置选装包数据(推荐组合, 全部选装)
    async setPersonalOptions({ commit, state, dispatch }, seats) {
      const modelLineId = await dispatch('getModelLineId')
      const res = await getPersonalOptionPlus(modelLineId, seats)
      const seatOptionRes = await getPersonalOptionPlus(modelLineId)
      const options = res.data.data
      const getSeatOption = seatOptionRes.data.data // 仅用来处理六座选装

      commit('updatePersonalOptionComposes', options.personalOptionComposes) // 推荐组合
      commit('updatePersonalOptions', options.personalOptions) // 全部选装

      /**
       * 根据车辆的类型判断是否需要六座选装
       * 非c 需要从推荐组合查询
       * 包含c 需要从全部选装查询
       * 非半定情况下生效
       */
      const includeC = state.currentModelLineData.typeFlag?.includes('C')
      const { currentModelLineData } = state
      if (currentModelLineData.measure !== 1) {
        if (includeC) {
          commit('onlySetSeatOptions', getSeatOption.personalOptions)
        } else {
          const options = []
          for (const item of getSeatOption.personalOptionComposes) {
            options.push(...item.composePersonalOptions)
          }
          commit('onlySetSeatOptions', options)
        }
      }
    },

    // 获取当前modelLineId
    async getModelLineId({ commit, state, dispatch }) {
      if (state.currentModelLineData.modelLineId) {
        return state.currentModelLineData.modelLineId
      }
      console.warn('需要重新获取modelLineinfo 数据')
      // const res = await getCarModelLineList(state.currentSeriesId)
    },

    // 设置外观颜色数据
    async setOutColorArray({ commit, state, dispatch }, seats) {
      const modelLineId = await dispatch('getModelLineId')
      const res = await getExterior(modelLineId, seats)
      const data = res.data.data
      if (data.length === 0) {
        console.error('外观颜色数据为空')
      }
      commit('updateOutColorArray', data)
    },

    // 设置轮毂数据
    async setHubArray({ commit, state, dispatch }, seats) {
      const modelLineId = await dispatch('getModelLineId')
      const res = await getModelHub(modelLineId, seats)
      const data = res.data.data
      if (data.length === 0) {
        console.warn('轮毂数据为空')
      }
      commit('updateHubArray', data)
    },

    // 设置内饰面料数据
    async setSibArray({ commit, state, dispatch }, seats) {
      const modelLineId = await dispatch('getModelLineId')
      const res = await getSibColorInterieur(modelLineId, seats)
      const data = res.data.data
      if (data.length === 0) {
        console.error('内饰面料数据为空')
      }
      commit('updateSibArray', data)
    },

    // 设置饰板数据
    async setEihArray({ commit, state, dispatch }, seats) {
      const modelLineId = await dispatch('getModelLineId')
      const res = await getInteriorEih(modelLineId, seats)
      const data = res.data.data
      if (data.length === 0) {
        console.warn('饰板数据为空')
      }
      commit('updateEihArray', data)
    },

    // 设置座椅数据
    async setVosArray({ commit, state, dispatch }) {
      const modelLineId = await dispatch('getModelLineId')
      const res = await getInteriorChair(modelLineId)
      const data = res.data.data
      commit('updateVosArray', data)

      /**
       * 设置默认的座椅数据
       * 新版UI已经去掉了座椅的入口, 这里有数据就直接给
       */
      const defaultVos = data.find((i) => i.status === 1) ?? data[0] ?? {}
      commit('updateCurrentVos', defaultVos)
    },

    // 获取外观颜色数据(半定)
    async setOutColorArrayMeasure1({
                                     commit, state, dispatch, getters
                                   }, seats) {
      const modelLineId = await dispatch('getModelLineId')
      const res = await measureQuery(modelLineId, [], 6, 'exColor')
      const {
        colorExterieur, personals, seatPacketOptional6, seatPacketOptional7
      } = res.data.data

      if (colorExterieur?.length === 0) {
        console.error('半定外观颜色数据为空')
      }
      commit('updateOutColorArray', colorExterieur)

      /**
       * 半定的逻辑下,座椅包的显示隐藏条件
       * seats 为 null 代表切换配置线请求
       * seats 有值就不用再次处理座椅包的隐藏显示
       */
      if (state.currentModelLineData.measure === 1 && !seats) {
        commit('updateMeasureSeatData', {
          seatPacketOptional6,
          seatPacketOptional7
        })
        commit('onlySetSeatOptions', personals)
      }
    },

    // 获取轮毂数据(半定)
    async setHubArrayMeasure1({
                                commit, state, dispatch, getters
                              }) {
      const modelLineId = await dispatch('getModelLineId')
      const seats = getters.currentSeatsNum
      const res = await measureQuery(modelLineId, [state.currentExterior.optionId], seats, 'getHub')
      const { rad } = res.data.data

      if (rad?.length === 0) {
        console.error('半定轮毂数据为空')
      }
      commit('updateHubArray', rad)
    },

    // 获取座椅数据(半定)
    async setVosArrayMeasure1({
                                commit, state, dispatch, getters
                              }) {
      const modelLineId = await dispatch('getModelLineId')
      const seats = getters.currentSeatsNum
      const optionIds = [state.currentExterior.optionId, state.currentHub.optionId]
      const res = await measureQuery(modelLineId, optionIds, seats, 'getVos')
      const { vos, sibInterieur } = res.data.data

      if (vos?.length === 0) {
        console.error('半定座椅数据为空')
      }
      commit('updateVosArray', vos)
      commit('updateSibArray', sibInterieur)
      /**
       * 设置默认的座椅数据
       * 新版UI已经去掉了座椅的入口, 这里有数据就直接给
       */
      const defaultVos = vos.find((i) => i.status === 1) ?? vos[0] ?? {}
      commit('updateCurrentVos', defaultVos)
    },

    // 获取面料数据(半定)
    async setSibArrayMeasure1({
                                commit, state, dispatch, getters
                              }) {
      const modelLineId = await dispatch('getModelLineId')
      const optionIds = [
        state.currentExterior.optionId,
        state.currentHub.optionId,
        state.currentVos.optionId
      ]
      const seats = getters.currentSeatsNum
      const res = await measureQuery(modelLineId, optionIds, seats, 'getSib')
      const { sibInterieur } = res.data.data

      if (sibInterieur?.length === 0) {
        console.error('半定面料数据为空')
      }
      commit('updateSibArray', sibInterieur)
    },

    // 获取饰条数据(半定)
    async setEihArrayMeasure1({
                                commit, state, dispatch, getters
                              }) {
      const modelLineId = await dispatch('getModelLineId')
      const optionIds = [
        state.currentExterior.optionId,
        state.currentHub.optionId,
        state.currentVos.optionId,
        state.currentSib.sibOptionId,
        state.currentSib.interieurOptionId
      ]
      const seats = getters.currentSeatsNum
      const res = await measureQuery(modelLineId, optionIds, seats, 'getEih')
      const { eih } = res.data.data

      if (eih?.length === 0) {
        console.error('半定饰条数据为空')
      }
      commit('updateEihArray', eih)
    },

    // 获取选装包数据(半定)
    async setPersonalOptionsMeasure1({
                                       commit, state, dispatch, getters
                                     }) {
      const modelLineId = await dispatch('getModelLineId')
      const optionIds = [
        state.currentExterior.optionId,
        state.currentHub.optionId,
        state.currentVos.optionId,
        state.currentSib.sibOptionId,
        state.currentSib.interieurOptionId,
        state.currentEih.optionId
      ]
      const seats = getters.currentSeatsNum
      const res = await measureQuery(modelLineId, optionIds, seats, 'getOptions')
      const { personals, measureConfigCodeList } = res.data.data

      if (personals?.length === 0) {
        console.error('半定选装包数据为空')
      }
      /**
       * 半定车大部分走推荐组合的逻辑,
       * 除了一些特殊车型STANDARD_CAR是展示全部选装(正常来说,特殊车型无选装,只有标装)
       */
      if (!STANDARD_CAR.includes(state.currentModelLineData.modelLineCode)) {
        commit('updatePersonalOptions', personals) // 全部选装
      }

      /**
       * 根据高定的数据结构, 这里需要把数据结构转换一下
       * STANDARD_CAR 车型为只显示标装(全部选装的,不管半定还是高定)
       */
      const composeOptions = measureConfigCodeList.map((item, idx) => {
        const options = []
        let itemPrice = 0
        for (const code of item.prCodes) {
          const optionItem = personals.find((j) => j.optionCode === code)

          if (!optionItem) {
            return null
          }
          options.push(optionItem)
          itemPrice += optionItem.price
        }

        return {
          composeName: `推荐组合${idx}`,
          composePrice: itemPrice,
          typeIdsOfA: [],
          seats: getters.currentSeatsNum,
          composePersonalOptions: options,
          // measureId: 用于生成配置单
          measureId: item.measureId
        }
      })

      let result = composeOptions.filter((i) => i)

      // 裸车情况
      if (measureConfigCodeList.length === 1 && measureConfigCodeList[0].prCodes.length === 0) {
        result = [
          {
            composeName: '无需选装',
            composePrice: 0,
            typeIdsOfA: [],
            seats: getters.currentSeatsNum,
            composePersonalOptions: [],
            // measureId: 用于生成配置单
            measureId: measureConfigCodeList[0].measureId
          }
        ]
      }

      commit('updatePersonalOptionComposes', result) // 推荐组合
    },

    // 修改或者生成已有配置单
    async getCCid({
                    commit, state, dispatch, getters, rootState
                  }, { orderStatus,ccids }) {
      const { measure, modelLineId } = state.currentModelLineData;
      const entryPoint = getEntryPoint(measure);
      const ccid = rootState.ccid;
      let res;
      if (getters.currentSeriesName !== "a5l") {
        const sibInterieurId = state.currentSib.sibInterieurId;
        const optionIds = getters.paramOptionIds;
        if (measure === 1) {
          // 半定的逻辑
          // measureId
          let measureId = "";
          const currentCompose = state.personalOptionComposes.find((i) => i.composeName === state.currentComposeName);
          if (currentCompose) {
            measureId = currentCompose.measureId;
          }

          if (orderStatus && ccid) {
            // 半定修改配置单
            res = await getCcMeasure(measureId, ccid, "");
          } else {
            // 半定生成配置单
            res = await getV2CcInfo(measureId, entryPoint, "");
          }
        } else {
          // 高定逻辑
          if (orderStatus && ccid) {
            // 高定修改配置单
            res = await updateCcInfo(ccid, modelLineId, optionIds, sibInterieurId);
          } else {
            // 高定生成配置单
            res = await getCcInfo(modelLineId, optionIds, sibInterieurId, entryPoint);
          }
        }

        if (res.data.code !== "00") {
          console.error("生成配置单失败", res.data.message);
        }
        /**
         * 项目里这个接口的数据里只依赖一个ccid
         */
        const { ccId } = res.data.data;
        commit("updateCcid", ccId);
      } else {
        // const sibInterieurId = state.currentSib.featureCode;
        // const optionIds = getters.paramOptionIds;

        if (orderStatus && ccids) {
          // res = await updateCcInfo(ccid, modelLineId, optionIds, sibInterieurId);
          res = await dispatch("getCcOptionsIdParam",ccids);
        } else {
          res = await dispatch("getCcOptionsIdParam");
          console.log(res, "getCcOptionsIdParam");
          const { ccId } = res.data.data;
          commit("updateCcid", ccId);
          // console.log(1,modelLineId,2,optionIds,3,sibInterieurId,4,entryPoint,);
          // 高定生成配置单
          // res = await getCcInfo(modelLineId, optionIds, sibInterieurId, entryPoint,mstMgrpId);
          // console.log(res);
        }
        if (res.data.code !== "00") {
          console.error("生成配置单失败", res.data.message);
        }
        /**
         * 项目里这个接口的数据里只依赖一个ccid
         */
      }
    },
    async getCcOptionsIdParam({ commit, state, dispatch },ccids) {
      const entryPoint = getEntryPoint();
      const modelCode = state.currentCarInfo.modelUnicodeShort;
      const mstMgrpId = state.currentCarInfo.mstMgrpId;
      const { modelYear, version } = state.currentModelLineData.cc;
      console.log(state.currentModelLineData);

      return new Promise((resolve, reject) => {
        let params = {
          "classCode": "A",
          "colorCode": state.currentExterior?.featureCode || "", // 增加空值处理
          "interiorCode": state.currentII?.featureCode || "",
          "modelCode": modelCode || "",
          "entryPoint": entryPoint || "",
          "modelVersion": version || "",
          "modelYear": modelYear || "",
          "prList": [],
          "mstMgrpId": mstMgrpId || ""
        };
        params.prList = params.prList.concat([state.currentEih.featureCode]);

        // 检查是否存在packet，且packet下有有效的labelChildren
        if (state.currentHub.packet?.labelChildren?.length > 0) {
          // 提取labelChildren中所有元素的featureCode并合并
          const childFeatureCodes = state.currentHub.packet.labelChildren.map(child => child.featureCode);
          params.prList = params.prList.concat(childFeatureCodes);
        } else {
          // 不存在packet，直接使用currentHub的featureCode
          params.prList = params.prList.concat([state.currentHub.featureCode]);
        }

        if (state.currentII.packet?.labelChildren?.length > 0) {
          const childFeatureCodes = state.currentII.packet.labelChildren.map(child => child.featureCode);
          params.prList = params.prList.concat(childFeatureCodes);
        } else {
          params.prList = params.prList.concat([state.currentII.featureCode]);
        }

        state.selectedOptions.length && state.selectedOptions.forEach((item) => {
          // 检查是否有 labelChildren 且长度大于 0
          if (item.labelChildren?.length > 0) {
            // 遍历 labelChildren，取出每个子元素的 featureCode
            const childFeatureCodes = item.labelChildren.map(child => child.featureCode);
            params.prList = params.prList.concat(childFeatureCodes);
          } else {
            // 没有 labelChildren 时，直接使用当前 item 的 featureCode
            params.prList = params.prList.concat([item.featureCode]);
          }
        });
        params.prList = params.prList.filter(item => item !== params.interiorCode);
        if (ccids) {
          params.ccid = ccids;
        }
        params.prList = params.prList.join(",");
        if(!ccids){
          try {
          postOmdGetCcid(params).then((res) => {
            resolve(res);
          });
          } catch (e) {
            reject(e);
          }
        }else{
          try {
          putOmdGetCcid(params).then((res) => {
            resolve(res);
          });
          } catch (e) {
            reject(e);
          }
        }
      });
    },

    // 获取skuId
    async getSkuId({ commit, state,getters,dispatch }) {
      const seriesIdMap = {
        0: "ADA7",
        1: "G4",
        2: "G6",
        3: "F0"
      };
      let seriesId = ""
      if(getters.currentSeriesName === "a5l"){
         seriesId = 'F0';
      }else{
         seriesId = seriesIdMap[state.carIdx];
      }
      const res = await getOtds({ seriesId });

      if (res.data.code !== '00') {
        return console.error('获取skuId失败', res.data.msg)
      }
      const { prodSkuId } = res.data.data
      commit('saveSkuId', prodSkuId)
    },

    // 点击推荐组合
    async clickOptionCompose({
                               commit, state, dispatch, getters
                             }, item) {
      /**
       * 选中时查找面料依赖, 当前面料有依赖的选装包, 且点击的当前组合无面料依赖的选装,则禁止选中
       */
      if (getters.paramSibDependOptionList.length > 0) {
        const isExist = getters.paramSibDependOptionList.find((depend) => item.composePersonalOptions.find((i) => i.optionCode === depend.optionRelateCode))
        if (!isExist) {
          return Toast('您选择的装备需您先更换内饰')
        }
        if (item.composeName === state.currentComposeName) {
          return Toast('您选择的装备需您先更换内饰')
        }
      }

      /**
       * 选中时查找饰板的依赖, 当前饰板有依赖的选装包, 且点击的当前组合无饰板依赖的选装,则禁止选中
       */
      const { eihDepends } = getters.paramEihDependsOptionList
      if (eihDepends.length > 0) {
        const isExist = eihDepends.find((depend) => item.composePersonalOptions.find((i) => i.optionCode === depend.optionRelateCode))
        if (!isExist) {
          return Toast('您选择的装备需您先更换饰板')
        }
        if (item.composeName === state.currentComposeName) {
          return Toast('您选择的装备需您先更换饰板')
        }
      }

      // 更新当前组合
      let name = ''
      if (item.composeName !== state.currentComposeName) {
        name = item.composeName
      }
      commit('updateCurrentComposeName', name)

      // 选中推荐组合后,取消所有选装里的选装包
      if (name) {
        for (const item of getters.pageAllOptionList) {
          if (!item.disabled) {
            item.selected = false
          }
        }
      }

      await dispatch('setTimeAndPrice')
    },

    async setDefaultSibAndEih({
                                commit, state, dispatch, getters
                              }) {
      /** ***************更新面料的逻辑 ***** */
      const { currentExterior, currentSib } = state
      const conflicts = getFilterRelateList(currentExterior.optionRelates, 'conflict')
      // 更新面料禁用状态
      for (const sib of getters.pageSibArray) {
        sib.disabled = false
        sib.selected = false

        for (const item of conflicts) {
          if (sib.sibInterieurCode === item.optionRelateCode) {
            sib.disabled = true
          }
        }
      }

      /**
       * 设置当前面料的条件
       * 1. 当前面料为空
       * 2. 当前面料不可用在不可用的面料里面
       */
      const availabelSibs = getters.pageSibArray.filter((sib) => !sib.disabled)
      const defaultSib = availabelSibs.find((sib) => sib.status === 1)
      const currentSibAvailabel = availabelSibs.find((sib) => sib.sibInterieurCode === currentSib.sibInterieurCode)
      if (isEmptyObj(state.currentSib) || !currentSibAvailabel) {
        commit('updateCurrentSib', defaultSib || availabelSibs[0])
        await dispatch('setSibRelatePacket') // 选中关联选装包
      }

      // 半定的时候,根据更新的面料获取饰条数据
      if (state.currentModelLineData.measure === 1) {
        await dispatch('setEihArrayMeasure1')
      }

      /** ***************更新饰板的逻辑 ***** */
      const arr = getFilterRelateList(state.currentSib.sibInterieurRelates, 'conflict')
      const disabledEihs = arr.filter((i) => i.optionRelateCategory === 'EIH')

      // 设置饰板的禁用状态
      for (const eih of getters.pageEihArray) {
        eih.disabled = false
        eih.selected = false

        for (const item of disabledEihs) {
          if (eih.optionCode === item.optionRelateCode) {
            eih.disabled = true
          }
        }
      }
      /**
       * 设置当前饰板的条件
       * 1. 当前饰板为空
       * 2. 当前饰板不可用在不可用的饰板里面
       */
      const availabelEihs = getters.pageEihArray.filter((eih) => !eih.disabled)
      const defaultEih = availabelEihs.find((sib) => sib.status === 1)
      const currentEihAvailabel = availabelEihs.find((eih) => eih.optionCode === state.currentEih.optionCode)
      if (isEmptyObj(state.currentEih) || !currentEihAvailabel) {
        commit('updateCurrentEih', defaultEih || availabelEihs[0])
      }

      // 半定的时候,根据更新的饰板获取选装包数据
      // 只展示标装(全部选装)的特殊车型也不需要更新半定选装包
      if (state.currentModelLineData.measure === 1) {
        await dispatch('setPersonalOptionsMeasure1')
      }
    },

    // 显示内饰tab页面要处理的事情
    async doInteriorPageAction({
                                 state, commit, getters, dispatch
                               }) {
      await dispatch('setDefaultSibAndEih')
      // 计算交付时间价格...
      dispatch('setTimeAndPrice')
    },

    // 显示外观页要处理的事情
    async doExteriorPageAction({
                                 state, commit, getters, dispatch
                               }) {
      // 每次进入重置外观色
      const outColor = getters.pageOutColorArray.find((item) => item.status === 1)
      if (isEmptyObj(state.currentExterior)) {
        commit('updateCurrentExterior', outColor ?? getters.pageOutColorArray[0])
      }
      // 重置轮毂数据(半定)
      if (state.currentModelLineData.measure === 1) {
        await dispatch('setHubArrayMeasure1')
      }

      // 每次进入重置轮毂
      const hub = getters.pageHubArray.find((item) => item.status === 1)
      if (isEmptyObj(state.currentHub)) {
        commit('updateCurrentHub', hub || getters.pageHubArray[0])
      }
      // 重置座椅数据(半定)
      if (state.currentModelLineData.measure === 1) {
        await dispatch('setVosArrayMeasure1')
        // 新版ui无座椅入口, 有数据就直接给,所以这里直接拿座椅数据去换面料数据
        await dispatch('setSibArrayMeasure1')
      }

      // await dispatch('setDefaultSibAndEih')
      dispatch("setTimeAndPrice");
    },

    doA5ExteriorPageInitAction({ state, commit, getters, dispatch }) {
      // 每次进入重置外观色
      commit("updateCurrentExterior", state.currentVersion.tempDefaultColorExterieur);

      // 每次进入重置轮毂
      commit("updateCurrentHub", state.currentVersion.tempDefaultRad);
    },

    doA5InteriorPageInitAction({ state, commit, getters, dispatch }) {
      // 每次进入重置内饰面料
      commit("updateCurrentII", state.currentVersion.tempDefaultII);

      // 每次进入重置饰板
      commit("updateCurrentEih", state.currentVersion.tempDefaultEih);

      // 每次进入重置选装包
    },

    // 前置配置有任何变化则重置全部选装的状态
    resetPageAllOptionState({ state, commit, getters }) {
      for (const option of getters.pageAllOptionList) {
        option.disabled = option.status === 1
        option.selected = option.status === 1
        option.initDisable = false
      }
    },

    // 显示选装tab页面要处理的事情
    async doOptionPageAction({
                               state, commit, getters, dispatch
                             }) {
      // 半定更新数据
      if (state.currentModelLineData.measure === 1) {
        await dispatch('setPersonalOptionsMeasure1')
      }

      /**
       * 全部选装
       */
      for (const option of getters.pageAllOptionList) {
        if (option.status === 1) {
          // 重置标装的UI状态 置灰+选中
          option.disabled = true
          option.selected = true
        } else {
          option.disabled = false
          option.initDisable = false
        }

        /**
         * 反向依赖： 选装依赖某面料
         * 获取每个选装包的依赖关系,如果当前选装的依赖的 F_SIB_COLOR_INTERIEUR 非当前面料,则置灰
         * 按照需求,目前只考虑optionRelateCategory 为 F_SIB_COLOR_INTERIEUR 的情况
         * initDisable 代表面料的强制依赖
         */
        const depends = getFilterRelateList(option.optionRelates, 'depend')
        const dependsSib = depends.filter((i) => i.optionRelateCategory === 'F_SIB_COLOR_INTERIEUR')
        if (dependsSib.length > 0) {
          const isExist = dependsSib.find((i) => i.optionRelateCode === state.currentSib.sibInterieurCode)
          if (!isExist) {
            option.disabled = true
            option.initDisable = true
          }
        }

        // 处理当前面料对选装的冲突
        const conflicts = getFilterRelateList(state.currentSib.sibInterieurRelates, 'conflict')
        if (conflicts.length > 0) {
          for (const conflict of conflicts) {
            if (option.optionCode === conflict.optionRelateCode) {
              option.selected = false
              option.disabled = true
              option.initDisable = true
            }
          }
        }

        // 处理当前饰板对选装的冲突
        const { eihConflicts } = getters.paramEihDependsOptionList
        if (eihConflicts.length > 0) {
          for (const i of eihConflicts) {
            if (i.optionRelateCode === option.optionCode) {
              option.selected = false
              option.disabled = true
              option.initDisable = true
            }
          }
        }

        // 添加选装关联文案的tag,文案: 与当前所选内饰关联
        option.dependsTag = false
        for (const item of getters.paramSibDependOptionList) {
          if (item.optionRelateCode === option.optionCode) {
            option.dependsTag = true
          }
        }

        /**
         * a7Mr车型:
         * 选择了动感套装，选装页需要自动勾选此选装包，且无法取消。
         * 新潮套装, 选装页需要隐藏动感套装
         */
        if (DONGGAN_CODE.includes(state.currentModelLineData.optionCode) && option.optionCode === state.currentModelLineData.optionCode) {
          option.disabled = true
          option.selected = true
          option.manualStatus = 1 // 设置为特殊的标装:覆盖toast提示
        }
      }

      // 设置推荐组合里的置灰状态
      for (const composes of getters.pageOptionComposes) {
        for (const item of composes.composePersonalOptions) {
          // 选装对面料的依赖:
          // 如果当前组合里有选装依赖某个面料,但是当前面料不是依赖的面料,则置灰
          const depends = getFilterRelateList(item.optionRelates, 'depend')
          const dependsSib = depends.filter((i) => i.optionRelateCategory === 'F_SIB_COLOR_INTERIEUR')
          if (dependsSib.length > 0) {
            const isExist = dependsSib.find((i) => i.optionRelateCode === state.currentSib.sibInterieurCode)
            if (!isExist) {
              composes.disabled = true
              break
            }
          }
          // 面料对选装的冲突
          const isConflict = getters.paramSibConflictOptionList.find((i) => i.optionRelateCode === item.optionCode)
          if (isConflict) {
            composes.disabled = true
            break
          }

          /** ***********-- */
            // 饰板对选装的依赖
          const { eihConflicts } = getters.paramEihDependsOptionList
          // 当前饰板与推荐组合冲突
          if (eihConflicts.length > 0) {
            const conflictItem = eihConflicts.find((i) => i.optionRelateCode === item.optionCode)
            if (conflictItem) {
              composes.disabled = true
              break
            }
          }
        }
      }

      // dispatch('setSibRelatePacket') // 面料对选装的依赖自动选中
      dispatch('setEihRelatePacket') // 饰板对选装的依赖自动选中
      dispatch('setHubRelatePacket') // 轮毂对选装的依赖自动选中

      // 全部选装中对选中的包设置同级的互斥逻辑
      const selAllOption = getters.pageAllOptionList.filter((i) => i.selected && i.status !== 1)
      for (const op of selAllOption) {
        const conflicts = getFilterRelateList(op.optionRelates, 'conflict')
        // console.log('选中的互斥', result, conflicts)
        for (const option of getters.pageAllOptionList) {
          for (const conflict of conflicts) {
            if (conflict.optionRelateCode === option.optionCode) {
              // 查询当前互斥的包是否是面料依赖的包
              const isExist = getters.paramSibDependOptionList.find((depend) => depend.optionRelateCode === option.optionCode)
              if (!isExist) {
                option.disabled = true
                option.initDisable = true
              } else {
                option.disabled = false
                option.initDisable = false
              }
              option.selected = false
            }
          }
        }
      }


      // 非c的时候,如果没有默认选中组合,就默认选中第一个可用的推荐组合
      const isTurnonC = state.currentModelLineData.typeFlag?.includes('C')
      if (!isTurnonC && state.currentComposeName === '') {
        if (getters.pageOptionComposes.length > 0) {
          const availableCompose = getters.pageOptionComposes.find((i) => !i.disabled)
          if (availableCompose) {
            commit('updateCurrentComposeName', availableCompose.composeName)
          } else {
            console.error('无可选的组合')
          }
        }
      }

      await dispatch('setTimeAndPrice')

      /**
       * canNoEquipment: 是否有AB类裸车
       * composePersonalOptions.length === 0: [无需选装]组合
       * 无裸车的时候: 无需选装置灰不可选中
       * 半定制下永远可选
       */
      const noConfig = getters.pageOptionComposes.find((i) => i.composePersonalOptions.length === 0)
      if (noConfig) {
        if (state.currentModelLineData.measure === 1) {
          // 半定制
          noConfig.disabled = false
        } else {
          noConfig.disabled = !state.deliveryTimeData.canNoEquipment
        }
      }
    },
    // 显示选装tab页面要处理的事情
    async doA5LOptionPageAction({ state, commit, getters, dispatch }) {

      /**
       * 全部选装
       */
      for (const option of getters.pageAllOptionList) {
        if (option.status === 1) {
          // 重置标装的UI状态 置灰+选中
          option.disabled = true
          option.selected = true
        } else {
          option.disabled = false
          option.initDisable = false
        }

        /**
         * 反向依赖： 选装依赖某面料
         * 获取每个选装包的依赖关系,如果当前选装的依赖的 F_SIB_COLOR_INTERIEUR 非当前面料,则置灰
         * 按照需求,目前只考虑optionRelateCategory 为 F_SIB_COLOR_INTERIEUR 的情况
         * initDisable 代表面料的强制依赖
         */
        const depends = getFilterRelateList(option.optionRelates, 'depend')
        const dependsSib = depends.filter((i) => i.optionRelateCategory === 'F_SIB_COLOR_INTERIEUR')
        if (dependsSib.length > 0) {
          const isExist = dependsSib.find((i) => i.optionRelateCode === state.currentSib.sibInterieurCode)
          if (!isExist) {
            option.disabled = true
            option.initDisable = true
          }
        }

        // 处理当前面料对选装的冲突
        const conflicts = getFilterRelateList(state.currentSib.sibInterieurRelates, 'conflict')
        if (conflicts.length > 0) {
          for (const conflict of conflicts) {
            if (option.optionCode === conflict.optionRelateCode) {
              option.selected = false
              option.disabled = true
              option.initDisable = true
            }
          }
        }

        // 处理当前饰板对选装的冲突
        const { eihConflicts } = getters.paramEihDependsOptionList
        if (eihConflicts.length > 0) {
          for (const i of eihConflicts) {
            if (i.optionRelateCode === option.optionCode) {
              option.selected = false
              option.disabled = true
              option.initDisable = true
            }
          }
        }

        // 添加选装关联文案的tag,文案: 与当前所选内饰关联
        option.dependsTag = false
        for (const item of getters.paramSibDependOptionList) {
          if (item.optionRelateCode === option.optionCode) {
            option.dependsTag = true
          }
        }
      }

      // 设置推荐组合里的置灰状态
      for (const composes of getters.pageOptionComposes) {
        for (const item of composes.composePersonalOptions) {
          // 选装对面料的依赖:
          // 如果当前组合里有选装依赖某个面料,但是当前面料不是依赖的面料,则置灰
          const depends = getFilterRelateList(item.optionRelates, 'depend')
          const dependsSib = depends.filter((i) => i.optionRelateCategory === 'F_SIB_COLOR_INTERIEUR')
          if (dependsSib.length > 0) {
            const isExist = dependsSib.find((i) => i.optionRelateCode === state.currentSib.sibInterieurCode)
            if (!isExist) {
              composes.disabled = true
              break
            }
          }
          // 面料对选装的冲突
          const isConflict = getters.paramSibConflictOptionList.find((i) => i.optionRelateCode === item.optionCode)
          if (isConflict) {
            composes.disabled = true
            break
          }

          /** ***********-- */
            // 饰板对选装的依赖
          const { eihConflicts } = getters.paramEihDependsOptionList
          // 当前饰板与推荐组合冲突
          if (eihConflicts.length > 0) {
            const conflictItem = eihConflicts.find((i) => i.optionRelateCode === item.optionCode)
            if (conflictItem) {
              composes.disabled = true
              break
            }
          }
        }
      }

      // dispatch('setSibRelatePacket') // 面料对选装的依赖自动选中
      // dispatch('setEihRelatePacket') // 饰板对选装的依赖自动选中
      // dispatch('setHubRelatePacket') // 轮毂对选装的依赖自动选中

      // 全部选装中对选中的包设置同级的互斥逻辑
      const selAllOption = getters.pageAllOptionList.filter((i) => i.selected && i.status !== 1)
      for (const op of selAllOption) {
        const conflicts = getFilterRelateList(op.optionRelates, 'conflict')
        // console.log('选中的互斥', result, conflicts)
        for (const option of getters.pageAllOptionList) {
          for (const conflict of conflicts) {
            if (conflict.optionRelateCode === option.optionCode) {
              // 查询当前互斥的包是否是面料依赖的包
              const isExist = getters.paramSibDependOptionList.find((depend) => depend.optionRelateCode === option.optionCode)
              if (!isExist) {
                option.disabled = true
                option.initDisable = true
              } else {
                option.disabled = false
                option.initDisable = false
              }
              option.selected = false
            }
          }
        }
      }


      // 非c的时候,如果没有默认选中组合,就默认选中第一个可用的推荐组合
      const isTurnonC = state.currentModelLineData.typeFlag?.includes('C')
      if (!isTurnonC && state.currentComposeName === '') {
        if (getters.pageOptionComposes.length > 0) {
          const availableCompose = getters.pageOptionComposes.find((i) => !i.disabled)
          if (availableCompose) {
            commit('updateCurrentComposeName', availableCompose.composeName)
          } else {
            console.error('无可选的组合')
          }
        }
      }

      /**
       * canNoEquipment: 是否有AB类裸车
       * composePersonalOptions.length === 0: [无需选装]组合
       * 无裸车的时候: 无需选装置灰不可选中
       * 半定制下永远可选
       */
      const noConfig = getters.pageOptionComposes.find((i) => i.composePersonalOptions.length === 0)
      if (noConfig) {
        if (state.currentModelLineData.measure === 1) {
          // 半定制
          noConfig.disabled = false
        } else {
          noConfig.disabled = !state.deliveryTimeData.canNoEquipment
        }
      }
    },

    // 面料关联选装包需要自动选中
    setSibRelatePacket({
                         state, commit, getters, dispatch
                       }) {
      if (getters.paramSibDependOptionList.length > 0) {
        // 优先默认选中推荐组合
        for (const depend of getters.paramSibDependOptionList) {
          for (const item of getters.pageOptionComposes) {
            /**
             * 夏日礼包的冲突:
             * 夏日礼包在A7L的车型下,依赖42F轮毂
             */
            if (getters.currentSeriesName === 'a7l') {
              const hubCode = state.currentHub.optionCode
              if (item.composePersonalOptions.find((i) => SUMMER_PACKAGE.includes(i.optionCode))) {
                if (hubCode !== '42F') {
                  item.disabled = true
                }
              }
            }

            // 选中推荐组合
            const isExist = item.composePersonalOptions.find((i) => i.optionCode === depend.optionRelateCode)
            if (isExist && !item.disabled) {
              item.dependsTag = true
              commit('updateCurrentComposeName', item.composeName)
            }
          }
        }

        // 未命中推荐组合,且此车开了C类,则查找全部选装
        if (!state.currentComposeName && state.currentModelLineData.typeFlag.includes('C')) {
          for (const dependOption of getters.paramSibDependOptionList) {
            const sibOption = getters.pageAllOptionList.find((i) => i.optionCode === dependOption.optionRelateCode)
            if (sibOption) {
              sibOption.selected = true
              // 对选中的包设置同级的互斥逻辑
              const conflicts = getFilterRelateList(sibOption.optionRelates, 'conflict')
              // console.log('选中的互斥', result, conflicts)
              for (const option of getters.pageAllOptionList) {
                for (const conflict of conflicts) {
                  if (conflict.optionRelateCode === option.optionCode) {
                    // 查询当前互斥的包是否是面料依赖的包
                    const isExist = getters.paramSibDependOptionList.find((depend) => depend.optionRelateCode === option.optionCode)
                    if (!isExist) {
                      option.disabled = true
                      option.initDisable = true
                    } else {
                      option.disabled = false
                      option.initDisable = false
                    }
                    option.selected = false
                  }
                }
              }
              break
            }
          }

          // 更新选装包的选中状态
          const res = getters.pageAllOptionList.filter((item) => item.selected && !item.disabled)
          commit('updateSelectedOptions', res)

          // log
          const isSelected = getters.pageAllOptionList.find((i) => i.selected)
          if (!isSelected) {
            console.error('全部选装里无法找到面料依赖的选项')
          }
        }
      }
    },

    // 与面料互斥的选装包需要去掉
    setSibConflictPacket({
                           state, commit, getters
                         }) {
      if (getters.paramSibConflictOptionList.length > 0) {
        const delList = []
        for (const item of getters.paramSibConflictOptionList) {
          const toDelPacket = state.selectedOptions.find((i) => i.optionCode === item.optionRelateCode)
          if (toDelPacket) {
            delList.push(toDelPacket.optionCode)
          }
        }

        if (delList.length > 0) {
          const selPacketList = state.selectedOptions.filter((i) => delList.find((code) => code === i.opeionCode))
          commit('updateSelectedOptions', selPacketList)
        }
      }
    },

    // 饰板关联选装包需要自动选中
    setEihRelatePacket({
                         state, commit, getters, dispatch
                       }) {
      const { eihDepends } = getters.paramEihDependsOptionList
      if (eihDepends.length > 0) {
        for (const depend of eihDepends) {
          // 推荐组合
          for (const item of getters.pageOptionComposes) {
            const dependItem = item.composePersonalOptions.find((i) => i.optionCode === depend.optionRelateCode)
            if (dependItem) {
              item.dependsTag = true
              commit('updateCurrentComposeName', item.composeName)
            }
          }

          // 全部选装
          const allDependOption = state.personalOptions.filter((o) => o.optionCode === depend.optionRelateCode)
          if (allDependOption.length > 0) {
            for (const i of allDependOption) {
              i.selected = true
              i.dependsTag = true
            }
            commit('updateSelectedOptions', allDependOption)
          }
        }
      }
    },

    // 轮毂对选装的依赖自动选中,
    setHubRelatePacket({
                         state, commit, getters, dispatch
                       }) {
      // 目前只有q6的红色卡钳(PC2)会使用到此逻辑
      const currentSeriesName = getters.currentSeriesName
      if (currentSeriesName !== 'q6') return

      const { currentHub } = state
      const isRelatePc2 = currentHub.optionCode.includes('PC2')

      // 推荐组合
      for (const item of getters.pageOptionComposes) {
        const composePc2 = item.composePersonalOptions.find((i) => i.optionCode === 'PC2')
        if (composePc2 && isRelatePc2) {
          item.dependsTag = true
          commit('updateCurrentComposeName', item.composeName)
        }
      }

      // 全部选装
      const allOptionsPc2 = getters.pageAllOptionList.find((i) => i.optionCode === 'PC2')
      if (allOptionsPc2 && isRelatePc2) {
        allOptionsPc2.selected = true
      }
    },

    // 点击内饰的面料和饰板要清理的数据
    clearOptionData({ state, commit, getters }) {
      /**
       * 空选装包的选中状态
       * 但是选中了六座, 更新对应的选装包
       */
      if (isEmptyObj(state.currentModelLineData) || getters.currentSeriesName === "a5l") return [];
      if (state.currentSeat.status === 1) {
        const seriesName = getters.currentSeriesName
        const code = SEAT_DATA_Q5_Q6[seriesName].defaultOptionCode
        for (const item of state.personalOptions) {
          if (item.optionCode === code) {
            item.selected = true
            commit('updateSelectedOptions', [item])
            break
          }
        }
      } else {
        const sibDependOption = state.personalOptions.filter((i) => getters.paramSibDependOptionList.find((j) => j.optionRelateCode === i.optionCode))
        commit('updateSelectedOptions', sibDependOption)
      }

      commit('updateCurrentComposeName', '') // 推荐组合

      // 撤销推荐组合禁用状态
      for (const item of getters.pageOptionComposes) {
        item.disabled = false
      }
    },

    /**
     * 返回如下格式：
     * 内饰配置(II): ["雾隐灰", "幻影黑"]
     * 轮毂配置(RAD): ["18寸轮毂配灰卡钳", "20寸轮毂配红卡钳", "19寸轮毂配红卡钳"]
     * 其他配置包(PACKET): ["座椅舒适包"]
     */
    async setEquipmentGroup({ state, commit, getters }) {
      const res = await getOmdEquipmentGroup(state.currentCarInfo.modelUnicode);
      const packets = res?.data?.data?.result?.children;

      // 检查是否为有效数组
      if (!Array.isArray(packets)) return;

      const result = new Map();
      for (const packet of packets) {
        const { labelCode, labelChildren } = packet;
        if (labelCode === "II") {
          // 处理 II 分组
          const hasSIB = labelChildren.some(item => item.familyCode === "SIB");
          if (hasSIB) {
            const list = result.get("II") || [];
            list.push(packet);
            result.set("II", list);
          }
        } else if (labelCode === "CMBPACKET") {
          // 检查是否存在 RAD 项目
          const hasRAD = labelChildren.some(item => item.familyCode === "RAD");
          if (hasRAD) {
            // 添加到 RAD 分组
            const list = result.get("RAD") || [];
            list.push(packet);
            result.set("RAD", list);
          } else {
            // 添加到 PACKET 分组
            const list = result.get("PACKET") || [];
            list.push(packet);
            result.set("PACKET", list);
          }
        }
      }

      state.currentPacketMap = result;
      console.log("%c A5L currentPacketMap: ", "font-size:16px;color:green;", state.currentPacketMap);
    },

    async setRadEquipmentGroup({ state, commit, getters }) {
      // 没有可选轮毂时直接返回
      if (state.currentVersion.carRad.length === 0) return;

      const packets = state.currentPacketMap.get("RAD");

      // 检查是否为有效数组
      if (!Array.isArray(packets)) return;

      const added = [];
      const featureCodeMap = new Map();

      // 预构建 featureCode 映射表
      for (const item of packets) {
        if (!Array.isArray(item.labelChildren)) continue;
        for (const labelChild of item.labelChildren) {
          featureCodeMap.set(labelChild.featureCode, item);
        }
      }

      // 处理每个轮毂配置
      for (const rad of state.currentVersion.carRad) {
        const matchedPacket = featureCodeMap.get(rad.featureCode);
        if (!matchedPacket) continue;

        if (rad.packet) {
          added.push({ ...rad, packet: matchedPacket });
        } else {
          rad.packet = matchedPacket;
        }
      }

      // 添加新创建的配置项
      if (added.length) {
        state.currentVersion.carRad.push(...added);
      }

      state.currentVersion.carRad = state.currentVersion.carRad.slice(0);
      console.log("%c A5L CurrentVersion carRad: ", "font-size:16px;color:green;", state.currentVersion.carRad);
    },

    async setIIEquipmentGroup({ state, commit, getters }) {
      if (state.currentVersion.carII.length === 0) return;

      const packets = state.currentPacketMap.get("II");
      // 检查是否为有效数组
      if (!Array.isArray(packets)) return;

      const added = []; // 统一收集要添加的新项

      // 处理每个内饰
      for (const carII of state.currentVersion.carII) {
        // 创建featureCode到packet的映射
        const featureMap = new Map();
        for (const item of packets) {
          if (!Array.isArray(item.labelChildren)) continue;
          for (const child of item.labelChildren) {
            // 只保留第一个匹配项
            if (!featureMap.has(child.featureCode)) {
              featureMap.set(child.featureCode, item);
            }
          }
        }

        const matchedPacket = featureMap.get(carII.featureCode);
        if (!matchedPacket) continue;

        if (carII.packet) {
          added.push({ ...carII, packet: matchedPacket });
        } else {
          carII.packet = matchedPacket;
        }
      }

      // 添加新创建的项目
      if (added.length) {
        state.currentVersion.carII.push(...added);
      }
      state.currentVersion.carII = state.currentVersion.carII.slice(0);
      console.log("%c A5L CurrentVersion carII: ", "font-size:16px;color:green;", state.currentVersion.carII);
    },
  },
};

/**
 * 半定 = 个性定制 'entryPoint', minip ? 'MINIP_MEASURE' : 'ONEAPP_MEASURE'
 * 高定 = 私人高定 'entryPoint',  minip ? 'MINIP_PERSONAL' : 'ONEAPP_PERSONAL'
 *
 * measure: 1 半定
 * measure: 非1 (目前是0) 高定
 *
 */
function getEntryPoint(measure) {
  const { env } = getUrlParamObj()
  const isMinip = env === 'minip'
  const carName = ''// todo 获取车型名字

  let entryPoint = ''
  if (measure === 1) {
    entryPoint = isMinip ? 'MINIP_MEASURE' : 'ONEAPP_MEASURE'
  } else {
    entryPoint = isMinip ? 'MINIP_PERSONAL' : 'ONEAPP_PERSONAL'
  }
  return entryPoint
}

// store/configration.js 中
// 排序函数：按msrp从小到大排序（有msrp的在前，无msrp的在后）
const sortByMsrp = (list) => {
  // 复制原数组避免修改源数据，确保排序稳定性
  return [...list].sort((a, b) => {
    // 提取msrp，非数字或不存在时视为Infinity（排在后面）
    const msrpA = typeof a.msrp === "number" ? a.msrp : Infinity;
    const msrpB = typeof b.msrp === "number" ? b.msrp : Infinity;
    // 升序排序（小的在前）
    return msrpA - msrpB;
  });
};

function processCarConfigData(data, state) {
  let configData = {
    "carColor": [],
    "carRad": [],
    "carSib": [],
    "carEih": [],
    "carII": [],
    "packets": [],
    "optionalEquips": [],
    "tempDefaultColorExterieur": null, // 外饰颜色
    "tempDefaultRad": null, // 轮毂
    "tempDefaultII": null, // 坐椅面料颜色和材质
    "tempDefaultEih": null, // 饰板
    "tempDefaultSib": null, // 坐椅面料
    "tempDefaultPackets": [],
    "tempDefaultOptionalEquips": [],
  };

  let defaultCarConfigs = [];

  for (const el of data) {
    const { labelCode, familyCode, featureStatus, featureStatusCode } = el;
    // 不可选，暂时过滤
    if (featureStatus == 3) {
      continue;
    }

    if (featureStatus == 2 && featureStatusCode !== "L") {
      defaultCarConfigs.push({ "labelCode": el.labelCode, "featureCode": el.featureCode, "familyCode": el.familyCode, "alterationType": "3" });
    }

    if (labelCode === "AADD") {
      configData.carColor.push(el);
      if (featureStatus == 2) configData.tempDefaultColorExterieur = el;
    } else if (labelCode === "II") {
      configData.carII.push(el);
      if (featureStatus == 2) configData.tempDefaultII = el;
    } else if (familyCode === "RAD") {
      configData.carRad.push(el);
      if (featureStatus == 2) configData.tempDefaultRad = el;
    } else if (familyCode === "EIH") {
      configData.carEih.push(el);
      if (featureStatus == 2) configData.tempDefaultEih = el;
    } else if (labelCode === "PR" && familyCode !== "EIH" && familyCode !== "RAD") {
      if (featureStatus == 2) {
        configData.tempDefaultOptionalEquips.push(el);
      }
      if (featureStatus == 0 || featureStatus == 2) {
        // if (featureStatus == 0) {
        configData.optionalEquips.push(el);
      }
    } else if (labelCode === "PACKET") {
      if (featureStatus == 2) {
        configData.tempDefaultPackets.push(el);
      }
      if (featureStatus == 0 || featureStatus == 2) {
        // if (featureStatus == 0) {
        configData.packets.push(el);
      }
    } else if (labelCode === "SIB") {
      if (featureStatus == 2) {
        configData.tempDefaultSIB = el;
      }
      if (featureStatus == 0) {
        configData.carSib.push(el);
      }
    }
  }

  configData.tempDefaultColorExterieur = configData.tempDefaultColorExterieur || configData.carColor[0] || {};
  configData.tempDefaultRad = configData.tempDefaultRad || configData.carRad[0] || {};
  configData.tempDefaultEih = configData.tempDefaultEih || configData.carEih[0] || {};
  configData.tempDefaultSib = configData.tempDefaultSib || {};
  configData.tempDefaultII = configData.tempDefaultII || configData.carII[0] || {};

  configData.carColor.sort((a, b) => b.featureWeight - a.featureWeight);
  configData.carRad.sort((a, b) => b.featureWeight - a.featureWeight);
  configData.carEih.sort((a, b) => b.featureWeight - a.featureWeight);
  configData.carII.sort((a, b) => b.featureWeight - a.featureWeight);

  return configData;
}
