/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-11-03 15:07:14
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-11-03 22:12:03
 * @FilePath     : \src\store\modules\order.js
 * @Descripttion :
 */
export default {
  state: {
    order: {}
  },
  mutations: {
    SET_ORDER: (state, order) => {
      state.order = order
    },
    SET_DEALER_INFO: ({ rootState }, dealerInfo) => {
      rootState.dealerInfo = dealerInfo
    }
  },
  actions: {
    async SetOrder({ commit }, order) {
      console.log('%c [ SetOrder ]-21', 'font-size:14px; background:#cf222e; color:#fff;', order)
      commit('SET_ORDER', order)
      localStorage.setItem('order', JSON.stringify(order))
    },
    async GetOrder({ state }) {
      let { order } = state
      if (!order || !Object.keys(order)?.length) {
        order = await JSON.parse(await localStorage.getItem('order'))
      }
      return order
    }
  }
}
