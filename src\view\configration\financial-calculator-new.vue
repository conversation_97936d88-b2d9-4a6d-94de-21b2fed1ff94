
<!--金融计算器-->
<template>
  <div class="financial-calculator">
    <div class="rate-calculator">
      <div class="rate_box">
        <div class="car_img">
          <img
            :src="
              (ccid
                ? BaseOssHost +
                  (carConfig.configDetail
                    ? carConfig.configDetail.carModel.imageUrl
                    : '')
                : /^https.*/.test(showModel.imageUrl)
                ? showModel.imageUrl
                : BaseOssHost + showModel.imageUrl) | audiwebp
            "
          />
        </div>
        <div>
          <div class="car_type" v-if="!(dealerCode || ccid || skuid)">
            <div
              :class="[customSeriesId == item.customSeriesId ? 'tabs' : '']"
              v-for="(item, index) of SeriesList"
              :key="index"
              @click="changSeries(item, index)"
            >
              {{ item.customSeriesName.replace('Audi ', '') }}
            </div>
          </div>
          <div class="car_type" v-else>
            <div class="tabs">
              {{
                ccid
                  ? carConfig.configDetail
                    ? carConfig.configDetail.carSeries.seriesNameCn.replace(
                      'Audi ',
                      ''
                    )
                    : ''
                  : showModel.modelLineName
              }}
            </div>
          </div>
        </div>
        <div class="car_msg">
          <div class="car_info">
            <div class="car_name" v-if="isA7MrCar">
              {{ formatmodelLineName | a7MrTextWrap }}
            </div>
            <div class="car_name" v-else>
              {{ formatmodelLineName | textWrap }}
            </div>
            <div class="car_price" v-show="totalprice != 0">
              {{ showTotalprice }}
            </div>
          </div>
          <div
            class="carChange"
            @click="toSelectModel"
            v-if="!dealerCode && !ccid"
          >
            更改<van-icon
            style="top: 2px"
            class="top-content-text-icon"
            name="arrow"
          />
          </div>
        </div>
      </div>
      <div class="rate_box rate_box_top">
        <div class="title">金融方案</div>
        <div class="rate_plan">
          <div
            class="plan_item"
            :class="[planIndex == 0 ? 'item_active' : '']"
            @click="setPlan(0)"
          >
            标准信贷
          </div>
          <!-- v-show="seriesCode != 'G4'" -->
          <div
            v-if="typeLoanVis"
            class="plan_item"
            :class="[planIndex == 1 ? 'item_active' : '']"
            @click="setPlan(1)"
          >
            融资租赁
          </div>
        </div>
        <div v-if="planIndex == 0">
          <div class="rate_plan_sub">
            <div
              class="plan_sub_item"
              @click="setCredit('1')"
            >
              <span>零首付</span>
              <img
                v-if="credit == 1"
                src="../../assets/img/checkbox_checked.png"
                alt=""
              />
              <img v-else src="../../assets/img/checkbox_normal.png" alt="" />
            </div>
            <!-- <div
              class="plan_sub_item"
              @click="setCredit('2')"
            >
              <span>零利率</span>
              <img
                v-if="credit == 2"
                src="../../assets/img/checkbox_checked.png"
                alt=""
              />
              <img v-else src="../../assets/img/checkbox_normal.png" alt="" />
            </div> -->
            <div
              class="plan_sub_item"
              @click="setCredit('3')"
            >
              <span>低利率</span>
              <img
                v-if="credit == 3"
                src="../../assets/img/checkbox_checked.png"
                alt=""
              >
              <img
                v-else
                src="../../assets/img/checkbox_normal.png"
                alt=""
              >
            </div>
          </div>
          <div class="first_payment">
            <div class="title">首付金额</div>
            <div class="payment_money">
              <van-field
                v-model="jine"
                type="number"
                @blur="blur"
                input-align="right"
                placeholder="输入先付金额"
              />
            </div>
          </div>
          <div class="first_proportion">
            <div class="title">首付比例</div>
            <div class="proportion_num">
              <div class="prop_slider">
                <van-slider
                  class="slider"
                  v-model="proportion"
                  :step="1"
                  :min="minproportion"
                  :max="maxproportion"
                  active-color="#000"
                  @drag-end="changeProportion"
                />
                <div class="slider-bottom">
                  <span>{{ minproportion }}</span>
                  <span>{{ maxproportion }}</span>
                </div>
              </div>
              <div class="prop_stepper">
                <van-stepper
                  disable-input
                  v-model="proportion"
                  :min="minproportion"
                  :max="maxproportion"
                  @change="changeProportion"
                />
              </div>
            </div>
          </div>
          <div class="rate_lease">
            <div class="title">贷款期限（月）</div>
            <div class="lease_type">
              <span
                :class="[deadlineIndex == index ? 'lease_type_active' : '']"
                v-for="(item, index) in interestRateObj"
                :key="index"
                @click="deadlineChange(interestRateObj[item], index)"
              >{{ index }}</span
              >
            </div>
          </div>
        </div>
        <div v-if="planIndex == 1">
          <div class="rate_plan_sub">
            <div class="plan_sub_item" @click="setleaseType(2)">
              <span>零首租</span>
              <img
                v-if="leaseType == 2"
                src="../../assets/img/checkbox_checked.png"
                alt=""
              />
              <img v-else src="../../assets/img/checkbox_normal.png" alt="" />
            </div>
            <div class="plan_sub_item" @click="setleaseType(3)">
              <span>带尾款</span>
              <img
                v-if="leaseType == 3"
                src="../../assets/img/checkbox_checked.png"
                alt=""
              />
              <img v-else src="../../assets/img/checkbox_normal.png" alt="" />
            </div>
          </div>
          <div class="first_payment">
            <div class="title">先付金额</div>
            <div class="payment_money">
              <van-field
                v-model="jine"
                type="number"
                @blur="blur"
                input-align="right"
                placeholder="输入先付金额"
              />
            </div>
          </div>
          <div class="first_proportion">
            <div class="title">先付比例</div>
            <div class="proportion_num">
              <div class="prop_slider">
                <van-slider
                  class="slider"
                  v-model="proportion"
                  :step="1"
                  :min="minproportion"
                  :max="maxproportion"
                  active-color="#000"
                  @drag-end="changeProportion"
                />
                <div class="slider-bottom">
                  <span>{{ minproportion }}</span>
                  <span>{{ maxproportion }}</span>
                </div>
              </div>
              <div class="prop_stepper">
                <van-stepper
                  disable-input
                  v-model="proportion"
                  :min="minproportion"
                  :max="maxproportion"
                  @change="changeProportion"
                />
              </div>
            </div>
          </div>
          <div class="rate_lease">
            <div class="title">租赁期限（月）</div>
            <div class="lease_type">
              <span
                :class="[deadlineIndex == index ? 'lease_type_active' : '']"
                v-for="(item, index) in interestRateObj"
                :key="index"
                @click="deadlineChange(interestRateObj[item], index)"
                >{{ index }}</span
              >
            </div>
          </div>
        </div>
      </div>
      <div v-if="planIndex == 0" class="rate_box rate_mechanism">
        <div class="title">金融机构</div>
        <div class="mechanism_box">
          <div
            class="mechanism_item"
            :class="[
              mechanism.loanAgency == item.loanAgency ? 'mechanism_active' : ''
            ]"
            v-for="(item, index) in mechanismList"
            :key="index"
            @click="changeMechanism(item, index)"
          >
            <div class="mechanism_titt">
              {{ item.loanAgency }}
            </div>
            <p>{{ item.investRate | investRateFilter }}%</p>
          </div>
        </div>
        <div class="mechanism_money">
          <div class="money_item">
            <span class="money_titt">首付</span>
            <span class="money_msg">￥{{ shoufu }}</span>
          </div>
          <div class="money_item">
            <span class="money_titt">年化利率</span>
            <span class="money_msg">{{ nianhualilv | investRateFilter }}%</span>
          </div>
          <div class="money_item">
            <span class="money_titt">月供</span>
            <span class="money_msg">￥{{ yuegong }}</span>
          </div>
        </div>
        <div class="rate_illustrate">
          <p>报价仅供参考，实际价格以金融合同为准</p>
          <!-- <p>您可在签署完购车合同后，在APP内查看相关信贷方案</p> -->
        </div>
        <div class="rate_terms" v-show="btnVisible">
          <div class="tiaokuan">
            <img
              class="checkicon"
              v-show="!isread"
              @click="okFn()"
              src="../../assets/img/checkbox_normal.png"
            />
            <img
              class="checkicon"
              v-show="isread"
              @click="isread = false"
              src="../../assets/img/checkbox_checked.png"
            />
            <div>
              我已阅读并同意<span class="b" @click="toTiaokuan"
            >《法律条款》</span
            >
            </div>
          </div>
        </div>
        <div class="gou" @click="toBack" v-show="btnVisible">立即定购</div>
      </div>
      <div v-if="planIndex == 1" class="rate_box rate_mechanism">
        <div class="title">金融机构</div>
        <div class="mechanism_box">
          <div
            class="mechanism_item"
            :class="[
              mechanism.loanAgency == item.loanAgency ? 'mechanism_active' : ''
            ]"
            v-for="(item, index) in mechanismList"
            :key="index"
            @click="changeMechanism(item, index)"
          >
            <div class="mechanism_titt">
              {{ item.loanAgency }}
            </div>
            <!-- <p>{{ item.investRate }}%</p> -->
          </div>
        </div>
        <div class="mechanism_money">
          <div class="money_item">
            <span class="money_titt">先付</span>
            <span class="money_msg">￥{{ xianfu }}</span>
          </div>
          <div class="money_item" v-if="leaseType == 3">
            <span class="money_titt">尾款金额</span>
            <span class="money_msg">￥{{ wKmoney }}</span>
          </div>
          <div class="money_item">
            <span class="money_titt">月租金</span>
            <span class="money_msg">￥{{ yuemoney }}</span>
          </div>
        </div>
        <div class="rate_illustrate">
          <p>报价仅供参考，实际价格以金融合同为准</p>
          <!-- <p>您可在签署完购车合同后，在APP内查看相关信贷方案</p> -->
        </div>
        <div class="rate_terms" v-show="btnVisible">
          <div class="tiaokuan">
            <img
              class="checkicon"
              v-show="!isread"
              @click="okFn"
              src="../../assets/img/checkbox_normal.png"
            />
            <img
              class="checkicon"
              v-show="isread"
              @click="isread = false"
              src="../../assets/img/checkbox_checked.png"
            />
            <div>
              我已阅读并同意<span class="b" @click="toTiaokuan"
            >《法律条款》</span
            >
            </div>
          </div>
        </div>
        <div class="gou" @click="toBack" v-show="btnVisible">立即定购</div>
      </div>
    </div>
    <model
      :modalshow.sync="modalshow"
      @update:modalshow="submit"
      title="在App内打开"
      confirm-text="复制链接"
      content="点击下方复制链接，前往浏览器打开"
    />
  </div>
</template>
<script>
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import {
  Slider, Field, Toast, Icon, RadioGroup, Radio, Stepper
} from 'vant'
import {
  canBuyA7LEdtionOne,
  bestRecommendCar,
  getBestRecommendConfig,
  bestRecommendCarAgent
} from '@/configratorApi/index'
import {
  getLoanCompute,
  getLoanRule,
  getCalculatorModelLine,
  getCarConfig,
  getCheckNowBuyStatus,
  getOtds,
  getCustomSeries2,
  getrentCompute
} from '@/api/api'
import { callNative, nativeCallback } from '@/utils/index'
import model from '@/components/model.vue'
import api from '../../config/url'
import { A7MR } from '@/view/newConfigration/car/a7mr'

Vue.use(Slider)
  .use(Field)
  .use(Toast)
  .use(Icon)
  .use(Radio)
  .use(RadioGroup)
  .use(Stepper)
export default {
  inject: ['reload', 'checkLoginFn'],
  name: 'FinancialCalculator',
  // eslint-disable-next-line vue/no-unused-components
  components: { model },
  data() {
    return {
      seriesCodeBox: '',
      times: null,
      typeLoanVis: true,
      orderh5BaseUrl: '',
      SeriesList: [], // 车系
      customSeriesId: '', // 车系id
      seriesCode: '',
      mechanismAllList: [], // 所有机构
      mechanismList: [], // 当前机构
      mechanismIndex: 0, // 当前选择机构
      mechanismItem: {},
      deadline: '12',
      deadlineList: [12, 24, 36, 48, 60], // 期限列表
      proportion: 20,
      minproportion: 20,
      maxproportion: 100,
      nianhualilv: '', // 年化利率
      planIndex: 0,
      leaseType: 2, // 租赁类型
      credit: 1, // 信贷类型
      xianfu: '',
      wKmoney: '',
      yuemoney: '',

      /**
       *
       */
      BaseOssHost: api.BaseOssHost,
      totalprice: 0,
      isread: false,
      fangan: 0, // 金融方案下标 0-低利率贷款 1-气球贷
      jine: '', // 首付金额
      minbili: 20,
      maxbili: 100,
      bili: 20,
      biliIndex: 1, // 首付比例下标
      biliArr: [
        20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100
      ],

      qixianIndex: 1, // 首付期限下标
      qixianArr: [12, 24, 36, 48, 60],
      jigouIndex: 0, // 金融机构
      jigouArr: [],
      yuegong: '0.00',
      shoufu: '0.00',

      modelLine: [], // 所有配置线
      showModel: {}, // 当前选择的配置线
      modalshow: false,
      fromselectmodel: false,
      dealerCode: '',
      modalshowBox: false,
      modalcontentBox: '',
      carConfig: {},
      ccid: '',
      skuid: '',
      from: '',
      interestRateObj: {},
      deadlineIndex: 0,
      mechanism: null,
      btnVisible: true
    }
  },
  created() {
    this.seriesCodeBox = this.$route.query?.seriesCode || '' // G6
    console.log(
      'financial-calculator-new created query===========',
      this.$route.query
    )
    const skuid = this.$route.query.skuid
    const ccid = this.$route.query.ccid
    this.$store.commit('saveSkuId', skuid)
    this.$store.commit('updateCcid', ccid)

    /**
     * 从整车订单过来根据订单状态来显示或者隐藏按钮
     * 只有状态为00（待付款）时显示，其他状态都隐藏
     */
    const { from, orderStatus } = this.$route.query
    if (from === 'order-forms' && orderStatus !== '00') {
      this.btnVisible = false
    }
  },
  mounted() {
    if (window.history && window.history.pushState) {
      history.pushState(null, null, document.URL)
      window.addEventListener('popstate', this.handelBack, false) // false阻止默认事件
    }
    const query = this.$route.query
    console.log('query===========', query)
    if (query && query.dealerCode) {
      this.dealerCode = query.dealerCode
    }
    if (query && query.from) {
      this.from = query.from
    }
    this.ccid = this.$route.query.ccid || this.$store.state.ccid || ''
    this.skuid = this.$route.query.skuid || this.$store.state.skuid || ''
    console.log('this.ccid', this.ccid)
    console.log('this.skuid', this.skuid)
    this.fromselectmodel = this.$route.query.fromselectmodel || false
    if (this.fromselectmodel) {
      this.showModel = this.$route.query.showModel
      this.totalprice = this.showModel.price
      this.seriesCode = this.showModel.customSeriesCode
      this.customSeriesId = this.showModel.customSeriesId
    }
    let carInfo = this.$route.query.carInfo // 从门店小程序过来的
    if (carInfo && carInfo.length > 0) {
      carInfo = JSON.parse(decodeURIComponent(carInfo))
      this.fromselectmodel = true
      this.showModel = carInfo
      this.totalprice = carInfo.price
    }
    // this.dealerCode = "76600019";

    // this.getLoanRuleFunc()
    // this.getLoanCompute();
    if (this.ccid.length > 0) {
      this.getCarConfig()
    } else if (this.dealerCode) {
      this.getModelLine1()
    } else {
      // this.getModelLine();
      this.getCustomSeries()
    }
  },
  computed: {
    ...mapState(['env', 'idx']),
    ...mapGetters(['getterCcid']),
    showTotalprice() {
      return this.formatNum(this.totalprice, 0)
    },
    formatmodelLineName() {
      const val = this.ccid
        ? this.carConfig.configDetail
          ? this.carConfig.configDetail.carModel.modelNameCn
          : ''
        : this.showModel.modelLineName
      if (!val) return ''

      return val
    },
    isA7MrCar() {
      if (this.formatmodelLineName.includes('24VX款')) {
        return true
      }
      return false
    }
  },
  watch: {
    bili(val, oldval) {
      const minPaymentRatio = this.jigouArr[this.jigouIndex].interestRates[
        this.qixianArr[this.qixianIndex]
        ][0].minPaymentRatio
      if (val < minPaymentRatio) {
        this.bili = oldval
        Toast({
          type: 'fail',
          message: `请输入首付比例不低于${minPaymentRatio}%且不高于100%的金额`,
          icon: require('../../assets/img/error.png')
        })
      }
      this.jine = Math.ceil((this.totalprice * this.bili) / 100)
      // this.blur();
    },
    qixianIndex(val) {
      const minPaymentRatio = this.jigouArr[this.jigouIndex].interestRates[this.qixianArr[val]][0]
        .minPaymentRatio
      if (this.bili < minPaymentRatio) {
        this.jine = Math.ceil((this.totalprice * minPaymentRatio) / 100)
        Toast({
          type: 'fail',
          message: `请输入首付比例不低于${minPaymentRatio}%且不高于100%的金额`,
          icon: require('../../assets/img/error.png')
        })
      }
      this.blur()
    },
    jigouIndex(val) {
      const minPaymentRatio = this.jigouArr[val].interestRates[this.qixianArr[this.qixianIndex]][0]
        .minPaymentRatio
      if (this.bili < minPaymentRatio) {
        this.jine = Math.ceil((this.totalprice * minPaymentRatio) / 100)
        Toast({
          type: 'fail',
          message: `请输入首付比例不低于${minPaymentRatio}%且不高于100%的金额`,
          icon: require('../../assets/img/error.png')
        })
      }
      this.blur()
    },
    carConfig(val) {
      if (val && val.configDetail.carSeries.seriesCode) {
        if (val.configDetail.carSeries.seriesCode === '49') {
          this.minbili = 20
          this.biliArr = [
            20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100
          ]
        } else if (val.configDetail.carSeries.seriesCode === 'G4') {
          this.minbili = 15
          this.biliArr = [
            15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95,
            100
          ]
        }
        if (
          val.configDetail.carModel
          && val.configDetail.carModel.modellineId
        ) {
          this.getLoanRuleCheck(val.configDetail.carModel.modellineId)
          this.getLoanRuleFunc(val.configDetail.carModel.modellineId)
        }
      }
    },
    showModel(val) {
      if (val && val.customSeriesCode) {
        if (val.customSeriesCode === '49') {
          this.minbili = 20
          this.biliArr = [
            20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100
          ]
        } else if (val.customSeriesCode === 'G4') {
          this.minbili = 15
          this.biliArr = [
            15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95,
            100
          ]
        }
        if (val.modelLineId) {
          this.getLoanRuleCheck(val.modelLineId)
          this.getLoanRuleFunc(val.modelLineId)
        }
      }
    },
    proportion(val) {
      console.log(val)
    }
  },
  filters: {
    showlilv(item, qixianArr, qixianIndex, bili) {
      // console.log(item, qixianArr, qixianIndex, bili);
      const qixian = qixianArr[qixianIndex]
      const interestRate = item.interestRates[qixian]
      let str = `${item.loanAgency}(0.00%)`
      interestRate.forEach((e) => {
        if (bili >= e.minPaymentRatio && bili <= e.maxPaymentRatio) {
          str = `${item.loanAgency}(${e.investRate}%)`
        }
      })
      return str
    },
    filtersRate(item, deadlineList, deadlineIndex, bili) {
      const qixian = deadlineList[deadlineIndex]
      const interestRate = item.interestRates[qixian]
      let str = ''
      interestRate.forEach((e) => {
        if (bili >= e.minPaymentRatio && bili <= e.maxPaymentRatio) {
          str = `${e.investRate}%`
        }
      })
      return str
    },
    investRateFilter(e) {
      return Number(e).toFixed(2)
    }
  },
  beforeDestroy() {
    clearTimeout(this.times)
  },
  destroyed() {
    window.removeEventListener('popstate', this.handelBack, false) // false阻止默认事件
  },
  methods: {
    okFn() {
      this.isread = true
      this.sensorsFn('', '同意条款')
    },
    handelBack() {
      console.log('Physical return 监听到了', this.$route.query)
      const { from } = this.$route.query
      if (from == 'quotation' || from == 'order-forms') {
        this.$router.go(-1)
      }
    },
    // 标准信贷
    setCredit(val) {
      const arr = ['', '零首付', '零利率', '低利率']
      this.sensorsFn('金融方案选择', arr[val])
      this.credit = val
      this.filterRule1()
      // this.setProp()
    },
    // 融资租赁
    setleaseType(val) {
      this.leaseType = val
      this.getLoanRuleCheck()
      this.getLoanRuleFunc()
      this.setProp()
    },
    // 金融方案选择
    setPlan(val) {
      console.log('金融方案选择', val)
      this.sensorsFn('金融方案选择', ['标准信贷', '融资信贷'][val])
      this.planIndex = val
      // console.log("getLoanRule 22-------", this.getLoanRuleFunc);
      // console.log("modelLineId-------", this.getLoanRule.modelLineId);
      this.getLoanRuleFunc()
      this.setProp()
    },
    calculateFistMoney() {
      this.jine = Math.ceil((this.totalprice * this.proportion) / 100)
      this.sensorsFn('首付金额', this.jine)
      this.sensorsFn('贷款期限', this.deadlineIndex)
      this.getLoanCompute(
        this.mechanism.loanRuleId,
        this.deadlineIndex,
        this.proportion,
        this.totalprice
      )
    },
    changeProportion(val) {
      console.log('首付比例==', val)
      this.sensorsFn('首付比例', val)
      if (this.times) {
        clearTimeout(this.times)
      }
      this.times = setTimeout(() => {
        // 调用接口
        this.processMechanismList()
        this.calculateFistMoney()
      }, 500)

      // this.processMechanismList();
      // this.calculateFistMoney();
    },
    // 选择金融机构
    changeMechanism(item, index) {
      const { loanAgency, max, min } = item
      console.log('金融机构选择', item, index)
      this.sensorsFn('金融机构选择', item.loanAgency)
      // 金融机构选择
      // investRate: "0.000"
      // loanAgency: "上汽财务"
      // loanRuleId: "1427934520326590465"
      // max: 100
      // min: 70
      if (!item.gray) {
        this.mechanismIndex = index
        this.mechanism = item
        this.setProp()
        if (loanAgency == '上汽财务' && this.planIndex == 0 && this.credit == 2 && this.deadlineIndex == 24) {
          this.proportion = min
          this.minproportion = min
        }
        this.getLoanCompute(
          this.mechanism.loanRuleId,
          this.deadlineIndex,
          this.proportion,
          this.totalprice
        )
      }
    },
    deadlineChange(item, index) {
      this.sensorsFn('贷款期限', index)
      console.log('贷款期限改变>>>', this.interestRateObj, item, index)
      // 贷款期限改变
      this.deadlineIndex = index
      this.setProp()
      if (this.planIndex == 0 && this.credit == 2 && this.deadlineIndex == 24) {
        console.log('设置默认的首付比例>>>>>>', this.proportion)
        this.proportion = 70
      }
      this.processMechanismList()
      this.getLoanCompute(
        this.mechanism.loanRuleId,
        this.deadlineIndex,
        this.proportion,
        this.totalprice
      )
    },
    setProp() {
      // 修改首付比例
      this.proportion = this.interestRateObj[this.deadlineIndex].minmax.min
      this.minproportion = this.interestRateObj[this.deadlineIndex].minmax.min
      this.maxproportion = this.interestRateObj[this.deadlineIndex].minmax.max
      console.log('修改首付比例1', this.minproportion)
    },
    async getLoanCompute(loanRuleId, month, paymentRatio, price) {
      console.log('getLoanCompute>>>>>', loanRuleId)
      // console.log('getLoanCompute')
      // 金融方案  planIndex: 0 => 标准信贷
      if (this.planIndex == 0) {
        await getLoanCompute({
          loanRuleId: loanRuleId,
          month: month,
          paymentRatio: paymentRatio,
          price: price
        }).then((res) => {
          console.log('getLoanCompute==>00', res)
          if (res.status === 200 && res.data.code === '00') {
            const data = res.data.data
            this.shoufu = data.payment
            this.nianhualilv = data.interestRate
            this.yuegong = data.monthPay
          }
        })
      } else {
        await getrentCompute({
          loanRuleId: loanRuleId,
          month: month,
          paymentRatio: paymentRatio,
          price: price
        }).then((res) => {
          console.log('先付、尾款、月租 res', res)
          if (res.status === 200 && res.data.code === '00') {
            const data = res.data.data
            this.xianfu = data.payment
            this.wKmoney = data.balance
            this.yuemoney = data.monthPay
          }
        })
      }
    },
    changSeries(item, index) {
      this.sensorsFn('车系选择', item.customSeriesName)
      console.log(item)
      this.seriesCodeBox = item.seriesCode
      // 修改车系
      this.customSeriesId = item.customSeriesId
      this.seriesCode = item.seriesCode
      this.planIndex = 0
      this.fromselectmodel = false // 更换车系的时候是要下面的车型跟着替换的
      this.getModelLine()
    },
    async getCustomSeries() {
      // 获取车系
      await getCustomSeries2({}).then(async (res) => {
        if (res.status === 200 && res.data.code === '00') {
          this.SeriesList = res.data.data
          if (!this.fromselectmodel) {
            this.customSeriesId = res.data.data[0].customSeriesId
            this.seriesCode = res.data.data[0].seriesCode
          }
          await this.getModelLine()

          this.$nextTick(() => {
            if (this.$route.query.seriesCode) {
              const code = this.$route.query.seriesCode
              this.changSeries(
                this.SeriesList.find((ele) => ele.seriesCode === code)
              )
            }
          })
        }
      })
    },

    /** *
     *
     */
    blur() {
      // 失去焦点时才根据输入的首付金额计算月供
      this.proportion = Math.floor((this.jine / this.totalprice) * 100)
      console.log('this.proportion:', this.proportion)
      const minPaymentRatio = this.mechanism.min
      if (this.proportion >= minPaymentRatio && this.proportion <= 100) {
        // this.shoufu = this.formatNum(this.jine);
        // this.yuegong = this.formatNum(
        //   this.calculateMonthlyInterest(
        //     parseFloat(this.totalprice),
        //     this.bili,
        //     this.qixianArr[this.qixianIndex],
        //     this.nianhualilv
        //   )
        // );
        // this.jine = Math.ceil((this.totalprice * this.bili) / 100);
        this.getLoanCompute(
          this.mechanism.loanRuleId,
          this.deadlineIndex,
          this.proportion,
          this.totalprice
        )
      } else {
        // this.shoufu = '¥0.00'
        // this.yuegong = '¥0.00'
        this.jine = Math.ceil((this.totalprice * minPaymentRatio) / 100)
        this.blur()
        Toast({
          type: 'fail',
          message: `请输入首付比例不低于${minPaymentRatio}%且不高于100%的金额`,
          icon: require('../../assets/img/error.png')
        })
      }
      this.sensorsFn('首付金额', this.jine)
    },
    async getCarConfig() {
      console.log(' this.ccid ', this.ccid)
      const { data } = await getCarConfig({ ccid: this.ccid })
      console.log('getCarConfig data (this.carConfig)', data)
      if (data && data.code === '00') {
        this.carConfig = data.data
        const originalTotalPrice = this.carConfig?.configDetail?.totalPrice
        const discount = this.carConfig?.configDetail?.discount || 0
        this.totalprice = originalTotalPrice - discount
        const p = this.carConfig?.configDetail?.totalPrice // q6: "预订价￥650,000",  q5e: totalPrice: 306831
        const arr = `${p}`.split('￥')
        if (
          arr.length > 1
          && this.carConfig?.configDetail?.carSeries?.seriesCode == 'G6'
        ) {
          const temp = arr[1].split(',')
          const pr = `${temp[0]}${temp[1]}`
          this.totalprice = pr
        }
        console.log('this.totalprice......', this.totalprice)
        this.jine = Math.ceil((this.totalprice * this.bili) / 100)

        this.seriesCode = this.carConfig.configDetail.carSeries.seriesCode
      }
    },
    toSelectModel() {
      this.sensorsFn(
        this.showModel.modelLineName
        || this.carConfig?.configDetail?.carModel?.modelNameCn,
        '更改'
      )
      this.$sensors.track('A_LoveCar_Calculator_Operate', {
        source_module: 'H5',
        refer_page: '爱车页',
        operation_type: '车型选择',
        button_name:
          this.showModel.modelLineName
          || this.carConfig?.configDetail?.carModel?.modelNameCn
      })
      // if (this.env === 'minip') {
      //   this.modalshow = true
      //   return
      // }
      if (this.ccid && !this.dealerCode) {
        this.$router.push({
          path: '/configration',
          query: {
            idx: this.idx
          }
        })
      } else {
        const query = { dealerCode: this.dealerCode }
        if (this.modelLine.length > 0) {
          query.modelLineList = this.modelLine
        }
        console.log('query', query)
        this.$router.push({
          path: '/configration/select-model2',
          query: query
        })
      }
    },
    goback() {
      this.$router.go(-1)
    },
    sensorsFn(name, value) {
      this.$sensors.track('A_LoveCar_Calculator_Operate', {
        source_module: 'H5',
        refer_page: '爱车页',
        operation_type: name,
        button_name: value
      })
    },
    async toBack(e) {
      this.sensorsFn('立即订购', '立即订购')
      const _this = this
      // 如果选择的是A7L先行版，先判断是否购买过A7L先行版
      if (this.showModel.modelLineCode === '498B2Y005') {
        this.$store.commit('showLoading')
        const ccid = this.ccid || this.getterCcid
        console.log('toBack ccid:', ccid)
        const res = await canBuyA7LEdtionOne(ccid)
        const { data: canData } = res
        console.log('toBack res:', res)
        this.$store.commit('hideLoading')
        if (canData.code === '00' && canData.data === 0) {
          Toast({
            type: 'fail',
            message: '您已购买过A7L先行版，请购买其他车型',
            icon: require('../../assets/img/error.png')
          })
          return
        }
      }
      // if (this.env === 'minip') {
      //   this.modalshow = true
      //   return
      // }
      if (!this.isread) {
        Toast({
          type: 'fail',
          message: '请先阅读并同意《法律条款》',
          icon: require('../../assets/img/error.png')
        })
        return
      }

      const loanAgency = this.mechanismItem.loanAgency || this.mechanismList[0].loanAgency
      console.log(
        loanAgency,
        '>>>>>>>>>>this.mechanismItem',
        this.mechanismItem
      )
      console.log('toBack loanAgency:', this.mechanismList, this.mechanismIndex)
      const params = {
        financial_programme: '低利率贷款',
        down_payment_ratio: `${this.bili}%`,
        loan_term: `${this.qixianArr[this.qixianIndex]}个月`,
        financial_institution: loanAgency
      }
      this.$sensors.track('OrderNow', params)
      if (this.dealerCode) {
        // quotation
        const id = this.showModel?.bestRecommendId || this.carConfig.bestRecommendId
        const entryPoint = this.$storage.getPlus('entryPoint')
        getBestRecommendConfig(id, entryPoint).then((res) => {
          console.log(this.showModel)
          let seriesId = ''
          const customSeriesCode = this.showModel?.customSeriesCode
            || this.carConfig?.configDetail?.carSeries?.seriesCode
          if (customSeriesCode == '49') {
            seriesId = 'ADA7'
          } else {
            seriesId = 'G4'
          }
          if (customSeriesCode == 'G6') {
            seriesId = 'G6'
          }
          console.log('customSeriesCode', customSeriesCode)
          getOtds({ useType: 1, seriesId: seriesId }).then((res1) => {
            const ccid = res.data.data.ccId
            const skuid = res1.data.data.prodSkuId
            if (ccid && skuid) {
              _this.$router.push({
                path: '/quotation',
                query: {
                  ccid: ccid,
                  skuid: skuid,
                  dealerCode: _this.dealerCode
                }
              })
              // let path = `${_this.orderh5BaseUrl}quotation?from=newCCPRO&ccid=${ccid || ''}&skuid=${skuid || ''}&dealerCode=${_this.dealerCode || ''}`;
              // _this.nativeCallbackFn(path)
            }
          })
        })
      } else if (this.from === 'quotation') {
        _this.$router.push({
          path: '/quotation',
          query: {
            ccid: _this.ccid,
            skuid: _this.skuid,
            dealerCode: _this.dealerCode
          }
        })
        // let path = `${_this.orderh5BaseUrl}quotation?from=newCCPRO&ccid=${this.ccid || ''}&skuid=${this.skuid || ''}&dealerCode=${_this.dealerCode || ''}`;
        // _this.nativeCallbackFn(path)
      } else if (this.ccid && !'h5,minip'.includes(this.env)) {
        const data = await callNative('prepage', { times: 1 })
        console.log('goBack=', data)
      } else {
        // 修复从小程序爱车页进入计算器后无法返回的bug
        if (this.env === 'minip' && !this.ccid) {
          const customSeriesCode = this.showModel.customSeriesCode
          const mapping = {
            49: 0,
            G6: 2,
            G4: 1
          }
          this.$router.push({
            path: '/configration',
            query: { idx: mapping[customSeriesCode] }
          })
          return
        }
        if ('h5,minip'.includes(this.env)) {
          return this.$router.back()
        }
        const customSeriesCode = this.showModel.customSeriesCode
        if (customSeriesCode == 49) {
          this.$router.push({
            path: '/configration',
            query: { idx: 0 }
          })
        }

        if (customSeriesCode == 'G6') {
          this.$router.push({
            path: '/configration',
            query: { idx: 2 }
          })
        }

        if (customSeriesCode == 'G4') {
          this.$router.push({
            path: '/configration',
            query: { idx: 1 }
          })
        }
      }
    },
    toTiaokuan() {
      // if (this.env === 'minip') {
      //   this.modalshow = true
      //   return
      // }
      this.$router.push({
        path: '/configration/legal-provision',
        query: this.$route.query
      })
    },
    submit(isSubmit) {
      if (isSubmit) {
        // 复制到粘贴板
        const copyResult = this.InsertShearPlate()
        this.modalshow = false
        if (copyResult) {
          Toast({
            type: 'fail',
            message: '已复制到剪贴板',
            icon: require('../../assets/img/success.png')
          })
        } else {
          Toast({
            type: 'fail',
            message: '复制失败',
            icon: require('../../assets/img/error.png')
          })
        }
      }
    },
    changeJigou(item, index) {
      if (!item.gray) {
        this.jigouIndex = index
      }
    },
    // 计算月供
    // zj:总价 75000、sf:首付比例 20(如果显示20%则sf值为20)、dkqs:贷款期数 12(单位月)、fl:年化费率 2.15(如果显示2.15%则fl值为2.15)
    calculateMonthlyInterest(zj, sf, dkqs, fl) {
      if (fl <= 0) {
        const monthInterest = (zj * (1 - sf / 100)) / dkqs
        return monthInterest.toFixed(2)
      }
      const monthInterestRate = parseFloat(fl) / 100 / 12
      // eslint-disable-next-line no-restricted-properties
      const repayMonthPow = Math.pow(1 + monthInterestRate, dkqs)
      const monthInterest = (zj * (1 - sf / 100) * monthInterestRate * repayMonthPow)
        / (repayMonthPow - 1)
      return monthInterest.toFixed(2)
    },
    // 数字转金钱格式，每三位加分隔符
    // num:待转换的数值、precision:小数位数、separator:分隔符
    formatNum(num, precision = 2, separator = ',') {
      if (!isNaN(parseFloat(num)) && isFinite(num)) {
        let parts
        num = Number(num)
        num = (
          typeof precision !== 'undefined' ? num.toFixed(precision) : num
        ).toString()
        // eslint-disable-next-line prefer-const
        parts = num.split('.')
        parts[0] = `¥${parts[0]
          .toString()
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, `$1${separator}`)}`
        return parts.join('.')
      }
      return num
    },
    async getLoanRuleCheck(modelLineId = '') {
      const modelLineIdBox = modelLineId
        || this.carConfig?.configDetail?.carModel?.modellineId
        || this.showModel.modelLineId
      await getLoanRule({
        dealerCode: this.dealerCode,
        modelLineId: modelLineIdBox,
        type: this.leaseType
      }).then((res) => {
        const data = res.data.data
        console.log('融资租赁显示res:', res)
        if (data == false || data == null) {
          this.typeLoanVis = false
        }
      })
    },
    async getLoanRuleFunc(modelLineId = '') {
      const modelLineIdBox = modelLineId
        || this.carConfig?.configDetail?.carModel?.modellineId
        || this.showModel.modelLineId
      console.log('this.carConfig--------', this.carConfig, modelLineId)
      console.log('this.showModel--------', this.showModel)
      this.$store.commit('showLoading')
      await getLoanRule({
        dealerCode: this.dealerCode,
        modelLineId: modelLineIdBox,
        type: this.planIndex == '0' ? 1 : this.leaseType
      }).then((res) => {
        console.log('机构数据 res:', res)
        this.$store.commit('hideLoading')
        if (res.status === 200 && res.data.code === '00') {
          const data = res.data.data
          if (data.length > 0) {
            // data.forEach((e) => {
            //   e.gray = false;
            // });
            // const interestRates = data[0].interestRates;
            // this.qixianArr = Object.keys(interestRates);
            // this.jigouArr = data;

            // this.mechanismList = data;
            // this.mechanism = data[0];
            // this.deadlineList = Object.keys(interestRates);
            // this.deadlineIndex = this.qixianArr[0];

            // this.getLoanCompute(
            //   this.mechanism.loanRuleId,
            //   this.deadlineIndex,
            //   this.bili,
            //   this.totalprice
            // );
            data.forEach((e) => {
              e.gray = false
            })
            console.log('data ============11', data)
            this.mechanismAllList = data
            this.planIndex == '0' ? this.filterRule1() : this.filterLeaseRule()
          }
        }
      })
    },
    // 融资租赁
    filterLeaseRule() {
      const data = this.mechanismAllList
      this.interestRateObj = {}
      data.forEach((item, index) => {
        // 机构
        // console.log(item);
        const interestRates = item.interestRates

        // interestRateObj[12].value=[]

        for (const key in interestRates) {
          // 贷款期数
          interestRates[key].forEach((obj, j) => {
            const jigouObj = {
              loanAgency: item.loanAgency,
              min: obj.minPaymentRatio, // minPaymentRatio
              max: obj.maxPaymentRatio, // maxPaymentRatio
              investRate: obj.investRate,
              discountAmount: obj.discountAmount,
              balanceRatio: obj.balanceRatio,
              loanRuleId: item.loanRuleId
            }
            if (!this.interestRateObj[key]) {
              this.interestRateObj[key] = {
                value: [],
                minmax: {
                  min: 101,
                  max: -1
                }
              }
            }
            this.interestRateObj[key].value.push(jigouObj)
          })
          // 一个期数处理完毕，循环value的objs找出minmax
          if (this.interestRateObj[key]) {
            this.interestRateObj[key].minmax = this.calculateMinMax(
              this.interestRateObj[key].value
            )
          }
        }
      })
      // 设置默认选中的贷款期限
      console.log(this.interestRateObj)
      console.log(
        '设置默认选中的贷款期限>>',
        Object.keys(this.interestRateObj)[1]
      )
      this.deadlineIndex = Object.keys(this.interestRateObj)[1]
      // 设置默认的首付比例
      this.proportion = this.interestRateObj[this.deadlineIndex].minmax.min
      this.minproportion = this.interestRateObj[this.deadlineIndex].minmax.min
      this.maxproportion = this.interestRateObj[this.deadlineIndex].minmax.max
      this.processMechanismList()
      this.getLoanCompute(
        this.mechanism.loanRuleId,
        this.deadlineIndex,
        this.proportion,
        this.totalprice
      )
    },
    // 标准信贷
    filterRule1() {
      const data = this.mechanismAllList
      console.log('标准信贷, data====>', data)
      this.interestRateObj = {}
      data.forEach((item, index) => {
        // 机构
        // console.log(item);
        const interestRates = item.interestRates

        for (const key in interestRates) {
          // 贷款期数
          interestRates[key].forEach((obj, j) => {
            const jigouObj = {
              loanAgency: item.loanAgency,
              min: '',
              max: '',
              investRate: '',
              loanRuleId: item.loanRuleId
            }


            // 零首付
            if (this.credit == 1) {
              if (obj.minPaymentRatio === 0) {
                jigouObj.min = obj.minPaymentRatio
                jigouObj.max = obj.maxPaymentRatio
                jigouObj.investRate = obj.investRate
                if (!this.interestRateObj[key]) {
                  this.interestRateObj[key] = {
                    value: [],
                    minmax: {
                      min: 101,
                      max: -1
                    }
                  }
                }
                this.interestRateObj[key].value.push(jigouObj)
              }
            }

            // 零利率
            if (this.credit == 2) {
              if (obj.investRate == '0.000') {
                jigouObj.min = obj.minPaymentRatio
                jigouObj.max = obj.maxPaymentRatio
                jigouObj.investRate = obj.investRate
                if (!this.interestRateObj[key]) {
                  this.interestRateObj[key] = {
                    value: [],
                    minmax: {
                      min: 101,
                      max: -1
                    }
                  }
                }
                this.interestRateObj[key].value.push(jigouObj)
              }
            }

            // 低利率
            if (this.credit == 3) {
              if (obj.minPaymentRatio !== 0) {
                jigouObj.min = obj.minPaymentRatio
                jigouObj.max = obj.maxPaymentRatio
                jigouObj.investRate = obj.investRate
                if (!this.interestRateObj[key]) {
                  this.interestRateObj[key] = {
                    value: [],
                    minmax: {
                      min: 101,
                      max: -1
                    }
                  }
                }
                console.log('222', this.interestRateObj[key].value)
                this.interestRateObj[key].value.push(jigouObj)
              }
            }
          })
          // 一个期数处理完毕，循环value的objs找出minmax
          if (this.interestRateObj[key]) {
            this.interestRateObj[key].minmax = this.calculateMinMax(
              this.interestRateObj[key].value
            )
          }
        }
      })
      // 设置默认选中的贷款期限
      console.log('this.interestRateObj', this.interestRateObj)
      console.log(
        '设置默认选中的贷款期限::',
        Object.keys(this.interestRateObj)[1]
      )
      this.deadlineIndex = Object.keys(this.interestRateObj)[1]

      // 设置默认的首付比例
      const shangqi = this.interestRateObj[this.deadlineIndex]?.value.find((e) => e.loanAgency === '上汽财务')
      if (this.credit == 2
        && this.planIndex == 0
        && this.deadlineIndex == 24
        && shangqi.loanAgency == '上汽财务'
      ) {
        this.proportion = 70
        this.minproportion = 70
      } else {
        this.proportion = this.interestRateObj[this.deadlineIndex].minmax.min
        this.minproportion = this.interestRateObj[this.deadlineIndex].minmax.min
      }
      this.maxproportion = this.interestRateObj[this.deadlineIndex].minmax.max

      this.processMechanismList()
      this.getLoanCompute(
        this.mechanism.loanRuleId,
        this.deadlineIndex,
        this.proportion,
        this.totalprice
      )
    },
    processMechanismList() {
      this.mechanismList = []
      console.log('//设置默认选中的机构:', this.interestRateObj[this.deadlineIndex].value)
      if (this.credit == 2 && this.planIndex == 0 && this.deadlineIndex == 24) {
        this.mechanismList = this.interestRateObj[this.deadlineIndex].value
      } else {
        this.interestRateObj[this.deadlineIndex].value.forEach((item, i) => {
          if (item.min <= this.proportion && item.max >= this.proportion) {
            this.mechanismList.push(item)
          }
        })
      }
      // 设置默认选中的机构
      let needRestMechanism = true
      console.log('------------', this.mechanism)
      if (this.mechanism) {
        if (this.proportion > 59 && this.proportion < 70 && this.credit == 2 && this.planIndex == 0 && this.deadlineIndex == 24 && this.mechanism != '上汽财务') {
          this.mechanismList = []
          this.interestRateObj[this.deadlineIndex].value.forEach((item, i) => {
            if (item.min <= this.proportion && item.max >= this.proportion) {
              this.mechanismList.push(item)
            }
          })
        }
        this.mechanismList.forEach((item) => {
          if (item.loanRuleId == this.mechanism.loanRuleId) {
            needRestMechanism = false
          }
        })
      }
      if (needRestMechanism && this.mechanismList.length > 0) {
        this.mechanism = this.mechanismList[0]
      }

      console.log('mechanismList>>>>>>>.', this.mechanismList)
    },
    calculateMinMax(objArray) {
      let min = 101
      let max = -1
      objArray.forEach((element) => {
        if (element.min < min) {
          min = element.min
        }
        if (element.max > max) {
          max = element.max
        }
      })
      return {
        min: min,
        max: max
      }
    },

    async getModelLine() {
      await getCalculatorModelLine({
        channel: 'oneapp',
        customSeriesId: this.customSeriesId
      }).then((res) => {
        console.log('getModelLine res', res)
        if (res.status === 200 && res.data.code === '00') {
          this.modelLine = res.data.data
          console.log('车 数据 拉来 》》》》》》》》》》》》》', this.modelLine)
          if (!this.fromselectmodel) {
            this.showModel = this.modelLine[0]
            this.totalprice = this.showModel.price
          } else {
            // this.showModel = this.modelLine[0]
            // this.seriesCodeBox = this.showModel.customSeriesCode
          }
          this.jine = Math.ceil((this.totalprice * this.bili) / 100)
        }
      })
    },
    async getModelLine1() {
      await bestRecommendCarAgent(this.dealerCode).then((res) => {
        this.modelLine = res.data.data.map((e) => {
          const bestRecommendId = e.bestRecommendId
          const totalPrice = e.totalPrice
          const modelLine = e.modelLine
          modelLine.bestRecommendId = bestRecommendId
          modelLine.price = totalPrice
          return modelLine
        })
        if (!this.fromselectmodel) {
          this.showModel = this.modelLine[0]
          this.totalprice = this.showModel.price
          this.seriesCode = this.showModel?.modelLine?.customSeriesCode
        }
        this.jine = Math.ceil((this.totalprice * this.bili) / 100)
      })
      console.log('getModelLine1  this.modelLine', this.modelLine)
    },
    submit1(isSubmit) {
      if (isSubmit) {
        callNative('openRoutePath', { path: 'scaudi://mall/orderlist' })
      }
    },

    InsertShearPlate() {
      // 复制到粘贴板
      const text = 'https://app.saic-audi.mobi/index.html#/?channel=miniapp_VehiclePurchase'
      const copyString = text
      // 创建input标签存放需要复制的文字
      const myInput = document.createElement('input')
      // 把文字放进input中，供复制
      myInput.value = copyString
      document.body.appendChild(myInput)
      // 选中创建的input
      myInput.select()
      // 执行复制方法， 该方法返回bool类型的结果，告诉我们是否复制成功
      const copyResult = document.execCommand('copy')
      // 操作中完成后 从Dom中删除创建的input
      document.body.removeChild(myInput)
      return copyResult
    }
  }
}
</script>
<style lang="less" scoped>
.zindex10 {
  position: relative;
  z-index: 10;
}
.rate-calculator {
  .title {
    font-size: 16px;
    margin: 8px 0;
  }
  .rate_box {
    padding: 0 16px;
  }
  .car_img {
    width: 343px;
    height: auto;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .car_type {
    display: flex;
    justify-content: space-around;
    font-size: 18px;
    height: 40px;
    line-height: 40px;
    color: #999;
    .tabs {
      font-weight: 900;
      color: #000;
      position: relative;
      &::after {
        content: '';
        display: table;
        width: 100%;
        height: 2px;
        background: #000;
        position: absolute;
        bottom: 2px;
      }
    }
  }
  .car_msg {
    margin: 16px auto;
    // width: 100%;
    // height: 70px;
    padding: 10px;
    border: 2px solid #333333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .car_info {
      display: inline-block;
      line-height: 22px;
      color: #333333;
      // margin-left: 12px;
      .car_name {
        font-size: 16px;
        white-space: pre-line;
      }
      .car_price {
        font-size: 14px;
        margin-top: 5px;
      }
    }
    .carChange {
      display: inline-block;
      // float: right;
      // margin-right: 12px;
    }
  }
  .rate_box_top {
    border-top: 10px solid #f2f2f2;
    .rate_plan {
      width: 100%;
      height: auto;
      .plan_item {
        display: inline-block;
        text-align: center;
        width: 30%;
        // width: 102px;
        height: 40px;
        line-height: 40px;
        background: #ffffff;
        font-size: 14px;
        color: #666666;
        border: 1px solid #e5e5e5;
        margin-right: 14px;
        margin-bottom: 19px;
        box-sizing: border-box;
        &.item_active {
          border: 2px solid #333333;
          font-family: AudiTypeGB-Normal, AudiTypeGB;
          font-weight: 400;
          color: #000000;
        }
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
    .rate_plan_sub {
      width: 100%;
      height: auto;
      display: flex;
      // justify-content: space-between;
      .plan_sub_item {
        // width: 168px;
        width: 30%;
        height: 54px;
        line-height: 54px;
        display: inline-block;
        background: #f2f2f2;
        font-size: 14px;
        font-family: AudiTypeGB-Normal, AudiTypeGB;
        font-weight: 400;
        color: #000000;
        margin-right: 14px;
        margin-bottom: 7px;
        &:nth-child(3n) {
          margin-right: 0;
        }
        span {
          display: inline-block;
          // width: 110px;
          width: 65%;
          margin-left: 15px;
        }
        img {
          width: 15%;
          height: auto;
        }
      }
    }
    .first_payment {
      display: flex;
      justify-content: space-between;
      // width: 343px;
      width: 100%;
      height: 56px;
      background: #ffffff;
      border-bottom: 1px solid #e5e5e5;
      .title {
        margin: 0;
        line-height: 54px;
      }
      .payment_money {
        width: 75%;
        .van-field {
          font-size: 16px;
          line-height: 54px;
          padding: 0;
        }
      }
    }
    .first_proportion {
      .proportion_num {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        width: 100%;
        border-bottom: 1px solid #e5e5e5;
        padding-bottom: 15px;
        .prop_slider {
          // width: 220px;
          width: 65%;
          height: 40px;
          margin-top: 35px;
          .slider-bottom {
            width: 100%;
            span {
              font-size: 12px;
            }
          }
        }
        .prop_stepper {
          // width: 95px;
          width: 35%;
          height: 24px;
          margin-left: 10px;
          .van-stepper__plus {
            border: 1px solid #e5e5e5;
          }
        }
      }
    }
    .rate_lease {
      margin-top: 25px;
      .lease_type {
        display: flex;
        span {
          display: inline-block;
          // width: 50px;
          width: 17.5%;
          height: 40px;
          background: #ffffff;
          border: 1px solid #e5e5e5;
          font-size: 14px;
          font-family: AudiTypeGB-Normal, AudiTypeGB;
          font-weight: 400;
          color: #666666;
          margin-right: 2%;
          margin-bottom: 20px;
          text-align: center;
          line-height: 40px;
          &:nth-child(5n) {
            margin-right: 0;
          }
          &.lease_type_active {
            border: 2px solid #333333;
          }
          &.lease_type_hide {
            display: none;
          }
        }
      }
    }
  }
  .rate_mechanism {
    border-top: 10px solid #f2f2f2;
    .mechanism_box {
      width: 100%;
      .mechanism_item {
        display: inline-block;
        margin-right: 3.5%;
        margin-bottom: 12px;
        // width: 165px;
        width: 47%;
        height: auto;
        padding: 10px 0;
        background: #ffffff;
        border: 2px solid #e5e5e5;
        text-align: center;
        &:nth-child(2n) {
          margin-right: 0;
        }
        .mechanism_titt {
          // margin-top: 10px;
          font-size: 16px;
          font-family: AudiTypeGB-Normal, AudiTypeGB;
          font-weight: 400;
          color: #666666;
        }
        p {
          font-size: 14px;
          font-family: AudiTypeGB-WideBold, AudiTypeGB;
          font-weight: normal;
          color: #999999;
          margin: 5px 0 0 0;
        }
        &.mechanism_active {
          border: 2px solid #333333;
          .mechanism_titt {
            color: #000000;
          }
          p {
            color: #333333;
            font-weight: normal;
          }
        }
      }
    }
    .mechanism_money {
      background: #f2f2f2;
      // width: 343px;
      width: 100%;
      height: auto;
      margin: 12px auto 24px;
      padding: 16px 0;
      .money_item {
        // width: 314px;
        width: 90%;
        height: 56px;
        line-height: 56px;
        border-bottom: 1px solid #e5e5e5;
        margin: 12px 15px;
        font-size: 16px;
        .money_titt {
          font-family: AudiTypeGB-Normal, AudiTypeGB;
          font-weight: 400;
          color: #666660;
        }
        .money_msg {
          font-size: 16px;
          font-family: AudiType-Bold, AudiType;
          font-weight: bold;
          color: #333333;
          float: right;
        }
      }
    }
    .rate_illustrate {
      p {
        width: 100%;
        word-wrap: normal;
        font-size: 12px;
        font-family: AudiTypeGB-Normal, AudiTypeGB;
        font-weight: 400;
        color: #999999;
        line-height: 20px;
        padding: 0;
        margin: 0px 00;
      }
    }
    .rate_terms {
      margin-top: 17px;
    }
    .gou {
      margin: 50px auto;
    }
  }
}
.financial-calculator {
  // padding: 15px 16px 30px 16px;
  // box-sizing: border-box;
  font-size: 14px;
  overflow-y: auto;
  .nodata {
    position: fixed;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    .nodataicon {
      width: 80px;
      height: 80px;
      display: block;
    }
    .nodatatext {
      font-size: 18px;
      margin-top: 20px;
    }
  }
  .order-calculator {
    // display: none;
  }
  .head {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    position: relative;
    .back {
      width: 24px;
      height: 24px;
      position: absolute;
      left: 0;
    }
    .title {
      font-size: 18px;
      font-weight: bold;
    }
  }
  .banner {
    height: 180px;
    overflow: hidden;
    .bannerimg {
      width: 100%;
    }
  }
  .top {
    box-shadow: 0 0 6px 0 #e5e5e5;
    padding: 15px 14px;
    overflow: hidden;
    .top-title {
      font-size: 16px;
      font-weight: bold;
    }
    .top-content {
      margin-top: 16px;
      // display: flex;
      // align-items: flex-start;
      height: 130px;
      position: relative;
      .top-content-text {
        display: flex;
        align-items: center;
        margin-right: 26px;
        position: relative;
        z-index: 1;
        .top-content-text-icon {
          margin-left: 10px;
        }
      }
      .top-content-img {
        position: absolute;
        width: 340px;
        height: 190px;
        top: -40px;
        right: -30px;
        & > img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
    }
    .top-price {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .title {
    text-align: left;
    // font-size: 16px;
    font-weight: bold;
    height: 40px;
    line-height: 40px;
  }
  .fangan {
    display: flex;
    flex-wrap: wrap;
    .item {
      padding: 8px 16px;
      margin: 0 10px;
      border: solid 1px #000;
      &.current {
        border: solid 1px #f00;
      }
    }
  }
  .jine {
    .jine-field {
      padding: 0;
    }
  }
  .label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 20px 0 10px;
    .label-left {
      color: #999;
    }
  }
  .slider-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px 0;
    font-size: 16px;
    font-weight: bold;
    overflow: hidden;
    span {
      position: relative;
      opacity: 0;
      &.current {
        opacity: 1;
      }
    }
  }
  .slider {
    width: calc(100% - 16px);
    margin-left: 8px;
    .custom-button {
      width: 24px;
      height: 24px;
      background-color: #fff;
      border-radius: 50%;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 50%);
      text-align: center;
      font-weight: bold;
      font-size: 16px;
      .custom-button-text {
        position: relative;
        top: -23px;
      }
    }
  }
  .slider-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px 0;
    font-size: 14px;
    span {
      position: relative;
      color: #999;
      text-align: center;
      &.current {
        color: #000;
      }
      &.opacity {
        opacity: 0;
      }
    }
  }
  .jigou {
    display: flex;
    flex-wrap: wrap;
    .item {
      padding: 8px 16px;
      margin: 5px;
      border: solid 1px #000;
      width: calc(50% - 10px);
      box-sizing: border-box;
      text-align: center;
      &.current {
        border: solid 1px #f00;
      }
      &.gray {
        border: solid 1px #ccc;
        color: #ccc;
      }
    }
  }
  .result,
  .result1 {
    display: flex;
    box-shadow: 0 0 6px 0 #e5e5e5;
    padding: 20px 0;
    margin: 16px 0;
    .item {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &.br {
        border-right: solid 1px #e5e5e5;
      }
      .sub1 {
        font-size: 18px;
      }
      .sub2 {
        color: #999;
        margin-top: 3px;
      }
    }
  }
  .tip {
    text-align: left;
    color: #bbb;
  }
  .tiaokuan {
    display: flex;
    align-items: center;
    text-align: left;
    color: #bbb;
    margin-top: 16px;
    .checkicon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    .b {
      color: #000;
    }
  }
  .gou {
    background-color: #000;
    color: #fff;
    margin: 20px 0 0px;
    padding: 16px 0;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
