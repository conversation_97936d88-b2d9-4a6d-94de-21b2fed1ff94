<template>
  <div>
    <div class="config-item-wrapper" v-show="visible">
      <div class="config-item c-flex-center " v-for="item in standardConfigData"
        v-show="item.optionClassification != 'PACKET'" :key="item.id">
        <div class="sc-left">
          <img :src="$loadWebpImage(item.img)" alt="" @click="imagePreview(item.img)">
        </div>
        <div class="sc-height">
          <div class="f14">
            {{ item.name }}
          </div>
          <div class="small-font">
            <span :class="{ 'line-through': item.discount === item.price && item.discount > 0 }">{{ isNumber(item.price) ? '¥' : '' }}{{ item.price | formatPrice }}</span>  <span v-if="item.discount === item.price && item.discount > 0" class="balcks">￥0</span><span v-if="item.discount === item.price && item.discount > 0" class="presale-tag">预售权益</span>
            <!-- {{ "G6ICAY003,G6ICAY002".includes(item.modelLineCode) ? "(价格已包含)" : '' }} -->
            <span v-if="A7L_INTELLIGENT_AUDIO.optionCode.every(code => standardConfigData.map(i=>i.optionCode).includes(code)) && item.optionCode === A7L_INTELLIGENT_AUDIO.optionCode[1]" class="down-price">
              ¥{{ A7L_INTELLIGENT_AUDIO.price9wp | formatPrice}}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="titleBox" v-show="standardConfigData.find(f => f.optionClassification == 'PACKET')">
      选装
    </div>
    <div class="config-item-wrapper" v-show="standardConfigData.find(f => f.optionClassification == 'PACKET')">
      <div class="config-item c-flex-center " v-for="item in standardConfigData"
        v-show="item.optionClassification == 'PACKET'" :key="item.id">
        <div class="sc-left">
          <img :src="$loadWebpImage(item.img)" alt="" @click="imagePreview(item.img)">
        </div>
        <div class="sc-height">
          <div class="f14">
            {{ item.name }} {{ a7lFigureKey.includes(item.optionCode) ? '(支持部分手机机型)': '' }}
          </div>
          <div class="small-font">
             <span :class="{ 'line-through': item.discount === item.price && item.discount > 0 }">{{ isNumber(item.price) ? '¥' : '' }}{{ item.price | formatPrice }}</span>  <span v-if="item.discount === item.price && item.discount > 0" class="balcks">￥0</span><span v-if="item.discount === item.price && item.discount > 0" class="presale-tag">预售权益</span>
            <!-- {{ isNumber(item.price) ? '¥' : '' }}{{ item.price | formatPrice }} -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { ImagePreview } from 'vant'
import { A7L_FIGURE_KEY, A7L_INTELLIGENT_AUDIO } from '../view/newConfigration/util/carModelSeatData'

export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapState({
      standardConfigData: 'standardConfigData'
    })
  },
  data() {
    return {
      orderDetail: true,
      a7lFigureKey: A7L_FIGURE_KEY,
      A7L_INTELLIGENT_AUDIO
    }
  },
  mounted() {
    const { fromPage } = this.$route.query
    this.orderDetail = !(fromPage && fromPage === 'orderDetail')
  },
  methods: {
    imagePreview(url) {
      ImagePreview([url])
    },
    isNumber(value) {
      return typeof value === 'number' && !isNaN(value)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../assets/style/common.less";
@leftWith: 14.5vw;
@rightMargin: 15px;
@topMargin: 20px;
@fontColor: #999;
.f14 {
  font-size: 14px;
  line-height: 14px;
  max-width: 265px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.config-item-wrapper {
  position: relative;
}

.titleBox {
  margin-left: -16px;
  height: 26px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 26px;
}


//向上的边距
.sc-m-top {
  margin-top: @topMargin;
}

.sc-left {
  width: @leftWith;
  margin-right: @rightMargin;
  img {
    height: 56px;
    width: 56px;
  }
}

//box阴影
.sc-shadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
}

// 下划线
.sc-u-line {
  border-bottom: 1px solid #e5e5e5;
}

//box阴影
.sc-shadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
}

// 下划线
.sc-u-line {
  border-bottom: 1px solid #e5e5e5;
}

//
.bold18 {
  .c-font18;
  .c-bold;
}

.small-font {
  .c-font12;
  color: #999;
  padding-top: 8px;
  >.down-price{
    .c-font10;
    color: #ccc;
    margin-left: 6px;
    text-decoration: line-through;
  }
}

.container {
  .sc-m-top;
  position: relative;
  padding: 0 18px;
}

.hint-wrapper {
  background-color: #f2f2f2;
  font-size: 14px;
  padding: 10px 18px;
}

.sc-nowrap {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
}

.sc-height {
  .c-flex-between;
  flex-direction: column;
  flex: 1;
  height: @leftWith;
  padding: 8px 0;
  box-sizing: border-box;
}

// 我的配置
.config-wrapper {
  .sc-shadow;
  padding: 0px 16px;
  margin-top: 15px;

  >.padding {
    .sc-u-line;
    padding: 10px 0;
  }

  .config-item {
    position: relative;
    padding: 12px 0;
    height: @leftWith;
  }
}

// 客户权益
.client-wrapper {
  .sc-shadow;

  margin-top: 20px;
  padding: 5px 20px;
  border-bottom: 1px solid #e5e5e5;

  >.title {
    padding: 5px 0;
    margin-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
  }
}
.line-through {
  text-decoration: line-through;
  color: gray;
  }
.balcks{
  color: black;
}
// 预售权益标签样式
.presale-tag {
  background-color: #EB0D3F; // 红色背景
  color: white; // 白色文字
  font-size: 10px;
  padding: 2px 6px 2px 6px;
  margin-left: 6px;
  line-height: 16px;
  // 上尖下缩，右侧带弧度的形状
  // clip-path: polygon(0 0, 100% 0, 85% 100%, 0 100%);
  z-index: 2; // 确保在图片上方显示
  white-space: nowrap;
}
</style>
