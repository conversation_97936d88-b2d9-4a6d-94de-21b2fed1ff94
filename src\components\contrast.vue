<template>
  <div v-if="carTable.carModel">
    <div class="box-left">
      <div
        class="border-bottom"
        v-show="item.show === 1 || item.show === 2"
        v-for="(item,index) in titleArray"
        :key="index"
        :style="{height: item.height}"
      >
        {{ item.title }}
      </div>
    </div>
    <div
      class="contrast"
      v-show="carTable.carModel && carTable.carModel.length > 0"
    >
      <div
        class="car_item border-bottom"
        ref="carModel"
        :style="{ width: carTable.carModel.length * 125 + 'px' }"
      >
        <div
          class="item_val border-left"
          v-for="(item, index) in carTable.carModel"
          :key="index"
        >
          <div class="_content1">
            {{ item.modelNameCn || '--' }}
          </div>
          <div class="_content1 margin-top-sm">
            ￥{{
              item.modelPrice.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
            }}
          </div>
          <div class="_content2 margin-top-sm">
            <img
              :style="{ width: 50 + 'px' }"
              height="50"
              style="object-fit: cover"
              :src="BaseOssHost + item.headImageUrl"
            >
          </div>
          <div class="_content1 margin-top-sm">
            <div v-html="`预计交付周期：${item.deliveryTime || ''}`" />
          </div>
        </div>
      </div>

      <div
        class="car_item border-bottom"
        ref="outsideColor"
        :style="{ width: carTable.carModel.length * 125 + 'px' }"
      >
        <div
          class="item_val border-left"
          v-for="(item, index) in carTable.outsideColor"
          :key="index"
        >
          <div class="_content1">
            {{ item.colorNameCn || '--' }}
          </div>
          <div class="_content1 margin-top-sm">
            ￥{{ item.price || '0' }}
          </div>
          <div class="_content2 margin-top-sm">
            <img
              :style="{ width: 50 + 'px' }"
              height="50"
              style="object-fit: cover"
              :src="BaseOssHost + item.imageUrl"
            >
          </div>
        </div>
      </div>
      <div
        class="car_item border-bottom"
        v-show="carTable.RADlist[0][0].show === 1"
        ref="RADlist"
        :style="{ width: carTable.carModel.length * 125 + 'px' }"
      >
        <div
          class="flex-column border-left"
          v-for="(item,index) in carTable.RADlist"
          :key="index"
        >
          <div
            class="item_val"
            v-for="(val, valIndex) in item"
            :key="valIndex"
          >
            <div class="_content1">
              {{ val.optionNameCn || '--' }}
            </div>
            <div
              class="_content1 margin-top-sm"
              v-if="val.optionCode !== '02'"
            >
              ￥{{ val.optionPrice || '0' }}
            </div>
            <div
              class="_content2 margin-top-sm"
              v-if="val.optionCode !== '02'"
            >
              <img
                :style="{ width: 50 + 'px' }"
                height="50"
                style="object-fit: cover"
                :src="BaseOssHost + val.imageUrl"
              >
            </div>
          </div>
        </div>
      </div>

      <div
        class="car_item border-bottom"
        v-show="carTable.VOSlist[0][0].show === 1"
        ref="VOSlist"
        :style="{ width: carTable.carModel.length * 125 + 'px' }"
      >
        <div
          class="flex-column border-left"
          v-for="(item,index) in carTable.VOSlist"
          :key="index"
        >
          <div
            class="item_val "
            v-for="(val, valIndex) in item"
            :key="valIndex"
          >
            <div class="_content1">
              {{ val.optionNameCn || '--' }}
            </div>
            <div
              class="_content1 margin-top-sm"
              v-if="val.optionCode !== '02'"
            >
              ￥{{ val.optionPrice || '0' }}
            </div>
            <div
              class="_content2 margin-top-sm"
              v-if="val.optionCode !== '02'"
            >
              <img
                :style="{ width: 50 + 'px' }"
                height="50"
                style="object-fit: cover"
                :src="BaseOssHost + val.imageUrl"
              >
            </div>
          </div>
        </div>
      </div>

      <div
        class="car_item border-bottom"
        v-show="carTable.EIHlist[0][0].show === 1"
        ref="EIHlist"
        :style="{ width: carTable.carModel.length * 125 + 'px' }"
      >
        <div
          class="flex-column border-left"
          v-for="(item,index) in carTable.EIHlist"
          :key="index"
        >
          <div
            class="item_val"
            v-for="(val, valIndex) in item"
            :key="valIndex"
          >
            <div class="_content1">
              {{ val.optionNameCn || '--' }}
            </div>
            <div
              class="_content1 margin-top-sm"
              v-if="val.optionCode !== '02'"
            >
              ￥{{ val.optionPrice || '0' }}
            </div>
            <div
              class="_content2 margin-top-sm"
              v-if="val.optionCode !== '02'"
            >
              <img
                :style="{ width: 50 + 'px' }"
                height="50"
                style="object-fit: cover"
                :src="BaseOssHost + val.imageUrl"
              >
            </div>
          </div>
        </div>
      </div>

      <div
        class="car_item border-bottom"
        ref="totalPrice"
        :style="{ width: carTable.carModel.length * 125 + 'px' }"
      >
        <div
          class="item_val border-left"
          v-for="(item, index) in carTable.totalPrice"
          :key="index"
        >
          ￥ {{ item.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import url from '@/config/url'

export default {
  name: 'Name',
  data() {
    return {
      BaseOssHost: url.BaseOssHost,
      titleArray: []
    }
  },
  props: {
    carTable: {
      type: Object,
      default: () => ({})
    },
    hiddenEqual: {
      type: Boolean,
      value: false
    }
  },
  watch: {
    hiddenEqual(newValue, oldValue) {
      this.titleArray.forEach((i) => {
        if (i.show === 1) {
          i.show = 0
          return
        }
        if (i.show === 0) {
          i.show = 1
        }
      })
      const carTableCopy = { ...this.carTable }
      carTableCopy.VOSlist = this.dataHandle(carTableCopy.VOSlist, carTableCopy.VOSlist[0][0].show)
      carTableCopy.RADlist = this.dataHandle(carTableCopy.RADlist, carTableCopy.RADlist[0][0].show)
      carTableCopy.EIHlist = this.dataHandle(carTableCopy.EIHlist, carTableCopy.EIHlist[0][0].show)
      console.log(carTableCopy)
      this.$emit('update:carTable', carTableCopy)
    },
    carTable: {
      immediate: true,
      handler(newValue, oldValue) {
        this.$nextTick(() => {
          const titleArray = []
          if (newValue.carModel && newValue.carModel.length > 0) {
            titleArray.push({
              height: window.getComputedStyle(this.$refs.carModel).height,
              title: '型号',
              show: 2
            })
          }
          if (newValue.carModel && newValue.outsideColor.length > 0) {
            titleArray.push({
              height: window.getComputedStyle(this.$refs.outsideColor).height,
              title: '外饰',
              show: 2
            })
          }
          if (newValue.carModel && newValue.RADlist.length > 0) {
            titleArray.push({
              height: window.getComputedStyle(this.$refs.RADlist).height,
              isHide: newValue.RADlist[0][0].isHide,
              title: '轮毂',
              show: newValue.RADlist[0][0].show
            })
          }
          if (newValue.carModel && newValue.VOSlist.length > 0) {
            titleArray.push({
              height: window.getComputedStyle(this.$refs.VOSlist).height,
              isHide: newValue.VOSlist[0][0].isHide,
              title: '座椅',
              show: newValue.VOSlist[0][0].show
            })
          }
          if (newValue.carModel && newValue.EIHlist.length > 0) {
            titleArray.push({
              height: window.getComputedStyle(this.$refs.EIHlist).height,
              isHide: newValue.EIHlist[0][0].isHide,
              title: '饰条',
              show: newValue.EIHlist[0][0].show
            })
          }
          if (newValue.carModel && newValue.totalPrice.length > 0) {
            titleArray.push({
              height: window.getComputedStyle(this.$refs.totalPrice).height,
              title: '总价',
              show: 2
            })
          }
          this.titleArray = titleArray
          console.log(this.titleArray)
        })
      }
    }
  },
  mounted() {

  },
  methods: {

    dataHandle(list, show) {
      list.forEach((i) => {
        i.forEach((j) => {
          if (j.show === 1 && j.isHide === true) {
            j.show = 0
            return
          }
          if (j.show === 0) {
            j.show = 1
          }
        })
      })
      return list
    }
  }
}
</script>

<style lang='less' scoped>
  div {
    box-sizing: border-box;
  }

  .box-left {
    width: 100px;
    display: flex;
    flex-flow: column;
    position: absolute;
    left: 0;
    padding-left: 15px;

    div {
      width: 85px;
      font-size: 12px;
      font-family: "Normal";
      color: #999999;
      line-height: 24px;
      padding: 15px 8px;
      background: #fff;
    }
  }

  .contrast {
    padding-bottom: 50px;
    stroke-width: 100%;
    overflow-y: auto;
    margin-left: 100px;

    .car_item {
      display: flex;
      width: 700px;
      position: relative;

      .item_val {
        width: 125px;
        font-family: "Audi-Normal";
        padding: 15px 8px;

        ._content1 {
          font-size: 12px;
          color: #000000;
          line-height: 16px;
          font-family: "Audi-Normal";

        }

        ._content2 {
          font-size: 10px;
          color: #333333;
          line-height: 12px;
          font-family: "Audi-Normal";

        }
      }
    }
  }

  .flex-column {
    display: flex;
    flex-flow: column;
    align-items: center;
  }

  .margin-top-sm {
    margin-top: 10px;
  }

  .border-bottom {
    border-bottom: 1px solid #e5e5e5;
  }

  .border-left {
    border-left: 1px solid #e5e5e5;
  }
</style>
