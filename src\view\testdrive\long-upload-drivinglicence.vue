<!--上传驾驶证-->
<template>
  <div class="idcertification">
    <img
      class="item-img"
      v-if="!base64"
      src="../../assets/img/icon-identity2.png"
      @click="onUploadOcrIdCard(2)"
    >
    <img
      class="item-img"
      v-if="base64"
      :src="base64"
      style="margin-top: 16px"
      @click="onUploadOcrIdCard(2)"
    >
    <div style="font-size: 14px; color: #999; margin-top: 10px">
      请您上传驾驶证正面照，请保证光线充足！
    </div>
    <div
      class="line-border-bottom"
      style="margin-top: 20px"
    >
      <div class="name">
        姓名
      </div>
      <div class="str">
        {{ name }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">
        身份证号
      </div>
      <div class="str">
        {{ idNumber }}
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">
        联系方式
      </div>
      <div class="str">
        {{ mobile }}
      </div>
    </div>

    <div class="bottom_style">
      <div class="div-btn">
        <AudiButton
          @click="onConfirm"
          :text="'确认'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
    <input
      class="hide_file"
      ref="leftFile"
      id="upload"
      type="file"
      @change="getFile($event)"
      capture="camera"
      accept="image/camera"
    >
    <!--拍照提示-->
    <van-action-sheet
      v-model="show"
      :safe-area-inset-bottom="true"
      :round="false"
    >
      <div style="padding-right: 16px; padding-left: 16px">
        <div
          class="item-title-bold"
          style="
            text-align: center;
            justify-content: center;
            font-size: 16px;
            margin-top: 18px;
          "
        >
          证件上传示例
        </div>
        <div
          style="
            text-align: center;
            justify-content: center;
            font-size: 14px;
            margin-top: 16px;
            color: #666;
            padding-right: 26px;
            padding-left: 26px;
          "
        >
          请于光线充足的环境下，纯色背景下，四角对齐，横向拍照
        </div>
        <img
          class="item-img"
          src="../../assets/img/icon-identity3.png"
        >
      </div>
      <van-grid
        :border="false"
        :column-num="4"
        :gutter="16"
      >
        <van-grid-item>
          <img src="../../assets/img/icon-identity5.png">
          <div style="font-size: 12px; margin-top: 8px; color: #333">
            缺失
          </div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity6.png">
          <div style="font-size: 12px; margin-top: 8px; color: #333">
            模糊
          </div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity7.png">
          <div style="font-size: 12px; margin-top: 8px; color: #333">
            过滤
          </div>
        </van-grid-item>

        <van-grid-item text="">
          <img src="../../assets/img/icon-identity8.png">
          <div style="font-size: 12px; margin-top: 8px; color: #333">
            背景模糊
          </div>
        </van-grid-item>
      </van-grid>
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onUploadingImg"
          :text="'知道了'"
          color="black"
          font-size="16px"
          height="50px"
        />
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Image as VanImage,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover,
  ActionSheet,
  Grid,
  GridItem
} from 'vant'
import HtmlImageCompress from 'html-image-compress'
import { callNative, compressFileImg } from '@/utils'

import {
  postOrcIdentifyIdCard,
  postOcrDrivingLicence
} from '@/api/api'
import AudiButton from '@/components/audi-button'
import storage from '../../utils/storage'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)
  .use(ActionSheet)
  .use(Grid)
  .use(GridItem)
  .use(VanImage)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      show: false, // 上传拍照提示
      uploadType: 1, // 上传识别类型 1=身份证，2=驾驶证
      isPhotograph: true, // 拍照页面
      isFileOK: false, // 上传成功页面
      isChange: false, // 修改页面

      name: '',
      address: '',
      idNumber: '',
      base64: '',
      boolRepeatCertificate: 0,
      updateGid: '',
      updateName: '',
      mobile: ''
    }
  },

  mounted() {
    this.mobile = this.$route.query.mobile

    const identityModel = storage.get('saveDrivingLicence') || '{}'
    this.idNumber = JSON.parse(identityModel).idNumber
    this.name = JSON.parse(identityModel).name
    // this.base64 = JSON.parse(identityModel).base64;
  },
  methods: {
    async getFile(e) {
      const file = e.target.files[0]

      const reader = new FileReader()
      if (file) {
        // 通过文件流将文件转换成Base64字符串
        reader.readAsDataURL(file)
        // 转换成功后
        const this1 = this
        reader.onloadend = function () {
          // 输出结果
          this1.formData(file, reader.result)
        }
      }
    },
    async formData(file, base64) {
      const htmlImageCompress = new HtmlImageCompress(file, {
        quality: 0.5
      }).then((result) => {
        console.log(result)
        this.base64 = result.base64
        const formData = new FormData()
        formData.append('file', result.file)
        formData.append('fileType', 1)
        formData.append('categoryType', 1)
        formData.append('fileCategoryId', 1)

        if (this.uploadType === 1) {
          this.postOrcIdentifyIdCard(formData)
        } else {
          this.postOcrDrivingLicence(formData)
        }
      })
    },

    // 识别身份证
    async postOrcIdentifyIdCard(formData) {
      this.$store.commit('showLoading')
      const { data } = await postOrcIdentifyIdCard(formData)
      this.$store.commit('hideLoading')

      this.show = false
      if (data.code === '00') {
        this.isPhotograph = false
        this.isFileOK = true
        this.isChange = false
        this.name = data.data.name
        this.address = data.data.address
        this.idNumber = data.data.idNumber
        this.updateGid = data.data.idNumber
        this.updateName = data.data.name
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    // 识别驾驶证
    async postOcrDrivingLicence(formData) {
      this.$store.commit('showLoading')
      const { data } = await postOcrDrivingLicence(formData)
      this.$store.commit('hideLoading')
      this.show = false
      if (data.code === '00') {
        this.isPhotograph = false
        this.isFileOK = true
        this.isChange = false
        this.name = data.data.name
        this.idNumber = data.data.number
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    },
    // 修改
    onReturnUpdate() {
      // 修改状态点击是重新上传
      if (this.isChange) {
        this.isPhotograph = true
        this.isFileOK = false
        this.isChange = false
      } else {
        this.isPhotograph = false
        this.isFileOK = false
        this.isChange = true
      }
    },

    onUploadOcrIdCard(type) {
      this.uploadType = type
      this.show = true
    },

    // 上传图
    onUploadingImg() {
      this.$refs.leftFile.click()
    },
    // 提交
    onConfirm() {
      if (!this.name) {
        callNative('toast', { type: 'fail', message: '请上传驾驶证' })
        return
      }
      const param = {
        idNumber: this.idNumber,
        name: this.name
        // base64: this.base64
      }
      storage.set('saveDrivingLicence', JSON.stringify(param))
      this.$router.back(-1)
    }

  }
}
</script>

<style scoped lang="less">
.idcertification {
  // height: 100%;
  padding-top: 16px;
  padding-right: 16px;
  padding-left: 16px;
}

.item-img {
  // width: 92%;
  // height: 200px;
}

.item-title-bold {
  width: 100%;
  font-size: 16px;
  color: #000;
  font-family: "Audi-WideBold";
  margin-bottom: 16px;
}

.line_content {
  margin-top: 16px;
  margin-bottom: 16px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .s_line {
    width: 100%;
    height: 1px;
    margin-right: 16px;
    background: #e5e5e5;
  }

  .text_center {
    text-align: center;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #999;
    font-size: 14px;
  }

  .e_line {
    width: 100%;
    height: 1px;
    margin-left: 16px;
    background: #e5e5e5;
  }
}

.interval_line {
  margin-top: 30px;
  margin-left: -16px;
  margin-right: -16px;
  height: 8px;
  background: #f2f2f2;
}

.line {
  padding-top: 16px;
  padding-bottom: 16px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 16px;
    color: #000;
  }

  .item_btn {
    color: #666;
    font-size: 14px;
  }

  .btn-change {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn-icon {
      align-items: center;
      font-size: 12px;
      color: #000;
    }
  }
}

.btn-delete-wrapper {
  margin: 16px;
}

.hide_file {
  display: none;
}

.line-border-bottom {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f2f2f2;

  .name {
    width: 68px;
    font-size: 16px;
    color: #000;
  }

  .str {
    font-family: "Audi-Normal";
    font-size: 16px;
    color: #000;
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0px;
  padding-bottom: 16px;
  left: 0px;
  position: fixed;

  .div-btn {
    width: 100%;
    margin: 4px;
  }
}
</style>
