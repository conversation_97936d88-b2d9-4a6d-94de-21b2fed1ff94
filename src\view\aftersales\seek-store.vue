<template>
  <div
    class="container seek-store"
    style="position: relative"
  >
    <!-- <navigation :back-type="backType"> </navigation>  -->
    <div
      style="height:100%"
      @click="hideshowClassify"
    >
      <div ref="condition">
        <van-search
          v-model="dealerName"
          placeholder="请输入门店名称"
          @search="onSearch"
          @clear="onCancel"
        />
        <div class="seachBox">
          <span>省份城市</span>
          <div @click.stop="showAreaCeng">
            {{ provinceName }}/{{ cityName }}
          </div>
          <img
            src="../../assets/img/icon_20.png"
            alt=""
            @click.stop="showAreaCeng"
          >
        </div>
        <div class="select_lable">
          <div class="line">
            <div
              class="title"
              style="font-weight: bold"
            >
              {{ "已为您找到" + addressAllList.length + "条结果" }}
              <img
                @click.stop="showClassify = !showClassify"
                style="width: 18px; height: 18px"
                src="@/assets/img/classify.png"
                alt=""
              >
            </div>
            <div class="btn-change">
              <img
                v-if="isShowMap==false"
                class="btn-icon"
                :src="require('../../assets/img/icon-cut2.png')"
                style="margin-right: 8px; height: 24px; width: 24px"
                @click.stop="switchType "
              >
              <img
                v-else
                class="btn-icon"
                style="height: 24px; width: 24px"
                :src="require('../../assets/img/icon-cut1.png')"
                @click.stop="switchType "
              >
            </div>
          </div>
        </div>
      </div>
      <div
        id="container"
        style="width: 100vw; height: calc(100vh - 57px - 57px - 110px  )"
        v-show="isShowMap"
      />

      <div v-if="!isShowMap">
        <div
          v-for="(item, idx) in addressAllList"
          @click.stop="onChange(idx,item)"
          :key="idx"
        >
          <div class="item-wrapper">
            <div class="content-wrapper">
              <div
                class="c-font16 c-bold"
                v-html="`${idx>8?(1+idx):'0'+(1+idx)} ${showDealerName(item)}`"
              />
              <!-- <div
                style="
                  display: flex;
                  display: flex;
                  align-items: center;
                  margin-top: 8px;
                "
              >
                <div class="item_btn">
                  {{ item.dealerType === 2 ? "销售" : "售后" }}
                </div>
                <div class="c-font14" style="margin-top: 5px; color: #333">
                  {{ item.dealerFullName }}
                </div>
              </div> -->

              <div
                class="c-font12"
                style="margin-top: 5px; color: #666"
                v-if="item.dealerAdrress"
              >
                <!-- {{parseFloat(item.distance).toFixed(1)}}KM | {{item.dealerAdrress }} -->
                {{ item.distance | selectAddress }} | {{ item.dealerAdrress }}
              </div>
              <!-- <div
                v-if="item.dealerPhone"
                class="c-font12"
                style="margin-top: 5px; color: #666"
              >
                {{ (item.dealerType === 2 ? "销售热线：" : "售后热线：") + item.dealerPhone }}
              </div>
              <div
                v-if="item.servicePhone"
                class="c-font12"
                style="margin-top: 5px; color: #666"
              >
              24h热线： {{   item.hotPhone24 || "暂无电话"}}
              </div> -->
              <template v-if="item.dealerType == '2'">
                <!-- <div class="text">销售热线：{{ item.dealerPhone || "暂无电话" }}</div> -->
                <div class="lables">
                  <span>零售交付</span>
                  <span>试驾</span>
                  <span>充电</span>
                  <span v-if="item.exhibitionHallForm == 3">品牌体验</span>
                </div>
              </template>
              <template v-else-if="['0', '1,2'].includes(item.dealerType)">
                <!-- <div class="text">销售热线：{{ item.dealerPhone || "暂无电话" }}</div> -->
                <div class="lables">
                  <span>零售交付</span>
                  <span v-if="item.dealerType === '1,2' ? 1 : item.controlStatus === '有'" style="background: #000000;color: #ffffff;">服务中心</span>
                  <span>试驾</span>
                  <span>充电</span>
                  <span v-if="item.exhibitionHallForm == 3">品牌体验</span>
                  <span v-if="item.dealerType === '1,2' ? 1 : item.controlStatus === '有' && item.saicAudiMaintenanceLevel == 'A'">新能源维修</span>
                  <span v-if="item.dealerType === '1,2' ? 1 : item.controlStatus === '有' && item.fawAudiBatteryMaintenanceCenter == '1'">高压电池维修</span>
                </div>
              </template>
              <template v-else-if="item.dealerType === '4'">
                <div class="lables">
                  <span>零售交付</span>
                  <span v-if="item.controlStatus === '有'" style="background: #000000;color: #ffffff;">服务中心</span>
                  <span>试驾</span>
                  <span>充电</span>
                  <span v-if="item.exhibitionHallForm == 3">品牌体验</span>
                  <span v-if="item.controlStatus === '有' && item.saicAudiMaintenanceLevel == 'A'">新能源维修</span>
                  <span v-if="item.controlStatus === '有' && item.fawAudiBatteryMaintenanceCenter == '1'">高压电池维修</span>
                </div>
              </template>
              <template v-else>
                <!-- <div class="text">售后热线：{{ item.dealerPhone || "暂无电话" }}</div>
                <div class="text">
                  24小时救援电话：{{ item.rescuePhone || "暂无电话" }}
                </div> -->
                <div class="lables">
                  <span>服务中心</span>
                  <span v-if="item.saicAudiMaintenanceLevel == 'A'">新能源维修</span>
                  <span v-if="item.fawAudiBatteryMaintenanceCenter == '1'">高压电池维修</span>
                </div>
              </template>
            </div>
          </div>
        </div>
        <div
          v-if="addressAllList.length === 0"
          style="height: 224px"
        >
          {{ " " }}
        </div>
      </div>
      <van-popup
        v-model="showArea"
        position="bottom"
        :style="{ height: '39%' }"
      >
        <van-area
          :area-list="areaList"
          :columns-num="2"
          title="选择地点"
          @change="areaChange"
          @confirm="areaConfirm"
          @cancel="areaCancle"
        />
      </van-popup>
    </div>

    <div
      class="classify_box"
      v-if="showClassify"
      :style="{top:classifyHeight}"
    >
      <van-checkbox-group
        v-model="result"
        checked-color="#000000"
        direction="horizontal"
        @change="changeClassify"
      >
        <van-checkbox
          shape="square"
          name="全部"
        >
          全部门店
        </van-checkbox>
        <van-checkbox
          shape="square"
          name="零售交付"
        >
          零售交付
        </van-checkbox>
        <van-checkbox
          shape="square"
          name="试驾"
        >
          试驾
        </van-checkbox>
        <van-checkbox
          shape="square"
          name="充电"
        >
          充电
        </van-checkbox>
        <van-checkbox
          shape="square"
          name="品牌体验"
        >
          品牌体验
        </van-checkbox>
        <van-checkbox
          shape="square"
          name="服务中心"
        >
          服务中心
        </van-checkbox>
        <van-checkbox
          shape="square"
          name="新能源维修"
        >
          新能源维修
        </van-checkbox>
        <van-checkbox
          shape="square"
          name="高压电池维修"
        >
          高压电池维修
        </van-checkbox>
      </van-checkbox-group>
    </div>

    <network @reload="networkReload()" />
  </div>
</template>

<script>
import { Promise } from 'q'
import Vue from 'vue'
import {
  ActionSheet, Picker, Popup, Search, Checkbox, CheckboxGroup, Area, Toast
} from 'vant'
import { mapGetters } from 'vuex'
import {
  getOrgList, getProvinceList, getAgentCityList, getAudiMinipUrl, getAPITimeOutTesting
} from '@/api/api'
import baseUrl from '@/config/url'
import {
  callNative, getdiscount2, getUrlParamObj, getLocationCityName, getLocation
} from '@/utils'
// import { wxLocationss } from "../../utils/wxutil";
import network from '@/components/network.vue'
import { appToast } from '@/utils/bridgeApi'
import { networkToast } from '@/utils/timeout'
import getLocationInfo from '@/utils/location'
import navigation from '../../components/navigation.vue'

Vue.use(ActionSheet).use(Picker).use(Popup).use(Search)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Area)

const latitude = '31.2304'
const longitude = '121.4737'

export default {
  components: { navigation, network },
  name: 'SeekStore',
  data() {
    return {
      dealerName: '', // 搜索内容
      ossUrl: baseUrl.BaseOssHost,
      addressAllList: [], // 全部地址
      location: '', // 模拟本地的经纬度信息.
      map: null,

      backType: 'app',
      isShowMap: true,

      initCityName: '', // 初始进来的城市

      provinceList: [], // 省份列表
      provinceCode: '', // 省份代码
      provinceName: '', // 省份名称
      cityList: [], // 城市列表
      cityCode: '', // 城市代码
      cityName: '', // 城市名称
      isShowCityList: false, // 显示城市选择
      isShowProvinceList: false, // 显示省份选择
      storeTypeList: [
        { searchType: 1, searchName: '授权服务商' },
        { searchType: 2, searchName: '授权代理商' }
      ],
      searchType: 0, // 授权类型（0:全部 1:服务商 2:代理商
      searchName: '',
      isShowStoreTypeList: false,
      markerList: [],
      env: '',
      /**
       *
       */
      showArea: false,
      areaList: {
        province_list: {},
        city_list: {}
      },
      result: ['全部'],
      showClassify: false,
      allList: '', // allList
      classifyHeight: '',
      hasSelectAll: true,
      pageType: '',
      inviteUserId: '',
      inviteeMobile: '',
      inviteePassword: '',
      series: '',
      isMiniProgram: false,
      device: {},
      containerSize: [0, 0]

    }
  },
  computed: {},
  watch: {
    addressAllList() {
      this.mapAddress()
    }
  },
  created() {
    const { env } = this.$route.query

    // 小程序
    if (env === 'minip') {
      this.device.sign = env
    } else {
      this.device = this.getDevice() || {}
      // app / h5
      this.device.sign = this.device?.nativeApp ? 'native' : 'html5'
    }

    console.log('%c [ this.device.sign ]-330', 'font-size:14px; background:#cf222e; color:#fff;', this.device.sign)
  },

  async mounted() {
    this.initData()
  },
  async activated() {
    console.log(
      'actived------------------------'
    )
  },
  methods: {
    ...mapGetters(['getDevice']),
    switchType() {
      if (this.showClassify) {
        this.showClassify = false
        return
      }
      this.isShowMap = !this.isShowMap
    },
    hideshowClassify() {
      if (this.showClassify) {
        this.showClassify = false
      }
    },
    changeClassify(e) {
      console.log(e)
      const list = this.allList
      this.result = e
      this.selectClassify(e, list)
    },
    selectClassify(data, allList) {
      const checkList = data
      const arr = []
      const list = allList
      // console.log(checkList);
      if (checkList.length == 0) {
        this.addressAllList = this.allList
        this.result = ['全部']
        return
      }
      if (checkList.length == 1 && checkList[0] == '全部') {
        this.addressAllList = this.allList
      }
      if (!this.hasSelectAll && data.indexOf('全部') > -1) {
        this.addressAllList = this.allList
        this.result = ['全部']
        this.hasSelectAll = true
        return
      }

      // console.log(checkList);
      if (this.hasSelectAll && checkList.length > 1 && data.indexOf('全部') > -1) {
        console.log(data.indexOf('全部'))
        this.hasSelectAll = false
        checkList.splice(data.indexOf('全部'), 1)
      }
      // console.log(checkList);
      if (data.indexOf('零售交付') > -1 && data.indexOf('服务中心') > -1) {
        const selectList = []
        const selectArr = []
        list.map((item) => {
          if (item.dealerType === '1,2') {
            selectList.push(item)
          } else if (['0', '4'].includes(item.dealerType) && item.controlStatus === '有') {
            selectArr.push(item)
          }
        })
        if (data.indexOf('品牌体验') > -1) {
          selectList.map((item) => {
            if (item.exhibitionHallForm == 3) {
              selectArr.push(item)
            }
          })
        } else if (data.indexOf('新能源维修') > -1) {
          // console.log(data.indexOf("新能源维修"), arr);
          selectList.map((item) => {
            if (item.saicAudiMaintenanceLevel == 'A') {
              selectArr.push(item)
            }
          })
        } else if (data.indexOf('高压电池维修') > -1) {
          // console.log(data.indexOf("高压电池维修"));
          selectList.map((item) => {
            if (item.fawAudiBatteryMaintenanceCenter == '1') {
              selectArr.push(item)
            }
          })
        } else {
          selectList.map((item) => {
            selectArr.push(item)
          })
        }
        // console.log(selectList);

        console.log(selectArr)
        this.addressAllList = selectArr
        return
      }

      if (
        // 选择代理商
        data.indexOf('零售交付') > -1
        || data.indexOf('试驾') > -1
        || data.indexOf('充电') > -1
        || data.indexOf('品牌体验') > -1
      ) {
        if (data.indexOf('品牌体验') > -1) {
          const selectArr = []
          list.map((item) => {
            if (item.exhibitionHallForm == 3) {
              selectArr.push(item)
            }
          })
          this.addressAllList = selectArr
        } else {
          list.map((item) => {
            if (['0', '2', '1,2', '4'].includes(item.dealerType)) {
              arr.push(item)
            }
          })
          this.addressAllList = arr
        }

        if (
          data.indexOf('服务中心') > -1
          || data.indexOf('新能源维修') > -1
          || data.indexOf('高压电池维修') > -1
        ) {
          this.addressAllList = []
        }
        return
      }
      if (
        // 选择服务商
        data.indexOf('服务中心') > -1
        || data.indexOf('新能源维修') > -1
        || data.indexOf('高压电池维修') > -1
      ) {
        list.map((item) => {
          if (data.indexOf('服务中心') !== -1 && ['0', '4'].includes(item.dealerType)) {
            item.controlStatus === '有' && arr.push(item)
          } else if (['0', '1', '1,2', '4'].includes(item.dealerType)) {
            arr.push(item)
          }
        })

        let selectList = []
        if (data.indexOf('新能源维修') > -1) {
          // console.log(data.indexOf("新能源维修"), arr);
          arr.map((item) => {
            if (item.saicAudiMaintenanceLevel == 'A') {
              if( ['0', '4'].includes(item.dealerType) && item.controlStatus === '有'){
                selectList.push(item)
              } else if (['1', '1,2'].includes(item.dealerType)) {
                selectList.push(item)
              }
            } 
          })
        } else {
          selectList = arr
        }

        // console.log(selectList);
        let selectArr = []
        if (data.indexOf('高压电池维修') > -1) {
          // console.log(data.indexOf("高压电池维修"));
          selectList.map((item) => {
            if (item.fawAudiBatteryMaintenanceCenter == '1') {
              if( ['0', '4'].includes(item.dealerType) && item.controlStatus === '有'){
                selectArr.push(item)
              } else if (['1', '1,2'].includes(item.dealerType)) {
                selectArr.push(item)
              }
            }
          })
        } else {
          selectArr = selectList
        }
        console.log(selectArr)
        this.addressAllList = selectArr

        if (
          data.indexOf('零售交付') > -1
          || data.indexOf('试驾') > -1
          || data.indexOf('充电') > -1
        ) {
          this.addressAllList = []
        }
      }
    },
    // 点击确定
    areaConfirm(e) {
      console.log(e)
      const value = e[1].name
      console.log(value)
      this.showArea = false
      this.cityName = e[1].name
      this.cityCode = e[1].code
      this.provinceCode = e[0].code
      this.getStoreList()
    },
    areaCancle() {
      this.showArea = false
    },
    showAreaCeng() {
      if (this.showClassify) {
        this.showClassify = false
        return
      }
      this.showArea = true
    },
    // 数据变化
    areaChange(picker, value, index) {
      console.log(value, 222222)
      if (value[1]) {
      } else {
        this.provinceCode = value[0].code
        this.provinceName = value[0].name
        this.cityName = ''
        this.cityCode = ''
        this.getCityList()
      }
    },
    // 修改省份数据
    setphonePro(data) {
      const list = data
      let code = ''
      const arr = {}
      list.forEach((el, i) => {
        arr[el.provinceCode] = el.name
        if (i == 0) {
          code = el.provinceCode
        }
      })
      this.areaList.province_list = arr
      this.getCityList()
    },
    // 修改城市数据
    setphoneCity(data) {
      const list = data
      const arr = {}
      list.forEach((el, i) => {
        arr[el.cityCode] = el.name
      })
      this.areaList.city_list = arr
    },
    /**
     * old function
     */
    // 显示门店名称
    showDealerName(item) {
      if (this.dealerName && this.dealerName.length > 0) {
        // 匹配关键字正则
        const replaceReg = new RegExp(this.dealerName, 'g')
        // 高亮替换v-html值
        const replaceString = `<span style="color: #F50537; font-size: 16px">${
          this.dealerName
        }</span>`
        return (item.dealerType == 1 ? '上汽奥迪授权服务商' : ' ') + item.dealerName.replace(replaceReg, replaceString)
      }
      return (item.dealerType == 1 ? '上汽奥迪授权服务商' : ' ') + item.dealerName
    },
    // 搜索
    onSearch() {
      this.getSearchStoreList()
    },
    async getSearchStoreList() {
      const { data } = await getOrgList({
        provinceCode: this.dealerName ? '' : this.provinceCode,
        cityCode: this.dealerName ? '' : this.cityCode,
        searchType: this.dealerName ? '' : this.searchType,
        dealerName: this.dealerName
      })
      this.setData(data)
    },
    onCancel() {
      this.dealerName = ''
      this.getStoreList()
    },
    // 选择省份
    onSelectProvince(item) {
      this.isShowProvinceList = false

      if (this.provinceCode !== item.provinceCode) {
        this.provinceCode = item.provinceCode
        this.provinceName = `${item.capInitials} ${item.name}`
        this.cityName = ''
        this.cityCode = ''
        this.getCityList()
        // 获取门店
        this.getStoreList()
      }
    },
    // 选择城市
    onSelectCity(item) {
      this.isShowCityList = false

      if (this.cityName !== item.name) {
        this.cityName = item.name
        this.cityCode = item.cityCode
        // 获取门店
        this.getStoreList()
      }
    },
    // 选择门店类型
    onSelectStoreType(item) {
      this.isShowStoreTypeList = false
      if (this.searchType !== item.searchType) {
        this.searchType = item.searchType
        this.searchName = item.searchName
        // 获取门店
        this.getStoreList()
      }
    },

    // 初始化进来获取全部城市
    async getAllCityList() {
      const { data } = await getAgentCityList({ searchAll: 2, page: 'seek-store' })
      this.cityList = data.data
      // 没有定位城市先取第一条
      if (this.initCityName === undefined) {
        this.cityCode = this.cityList[0].cityCode
        this.cityName = this.cityList[0].name
        this.provinceCode = this.cityList[0].provinceCode
      } else {
        const item = this.cityList.find((i) => i.name === this.initCityName)
        if (item) {
          this.value = item.name
          this.cityCode = item.cityCode
          this.cityName = item.name
          this.provinceCode = item.provinceCode
        } else {
          this.cityCode = this.cityList[0].cityCode
          this.cityName = this.cityList[0].name
          this.provinceCode = this.cityList[0].provinceCode
        }
      }
      this.getProvinceList()
    },
    // 获取省份
    async getProvinceList() {
      const { data } = await getProvinceList({ searchAll: 2 })
      this.provinceList = data.data
      const item = this.provinceList.find(
        (i) => i.provinceCode === this.provinceCode
      )
      // this.provinceName = item.capInitials + " " + item.name;
      this.provinceName = item.name
      console.log('getProvinceList', data.data)
      this.setphonePro(data.data)
      this.getCityList()
      // 获取门店
      this.getStoreList()
    },
    // 获取筛选的城市
    async getCityList() {
      const { data } = await getAgentCityList({
        searchAll: 2,
        provinceCode: this.provinceCode,
        page: 'seek-store-province-change'
      })
      console.log('getCityList', data)
      this.setphoneCity(data.data)
      this.cityList = data.data
    },
    async getStoreList() {
      console.log(this.cityCode, this.dealerName, 1111)
      networkToast()
      const { data } = await getOrgList({
        provinceCode: this.provinceCode,
        cityCode: this.cityCode,
        searchType: this.searchType,
        dealerName: this.dealerName
      })
      this.setData(data)
    },

    async setData(data) {
      this.addressAllList = data.data

      if (this.addressAllList.length > 0) {
        // 高德api计算两点之间的距离
        const location = this.location.split(',')

        const results = await Promise.all(
          this.addressAllList.map(async (dealer) => {
            if (dealer.dealerAdrress) {
              dealer.dealerAdrress = dealer.dealerAdrress.trim()
            }
            if (dealer.latitude && dealer.longitude) {
              console.log(
                '距离计算 ',
                location[1],
                location[0],
                dealer.longitude,
                dealer.latitude
              )
              return getdiscount2(
                location[1],
                location[0],
                dealer.longitude,
                dealer.latitude
              )
            }
            return 0
          })
        )

        this.addressAllList.forEach((dealer, i) => {
          dealer.distance = results[i]
        })
        console.log(this.addressAllList)
        this.addressAllList.sort(this.compare('distance'))
        this.addressAllList = this.resort(this.addressAllList)

        this.addressAllList.forEach((item, idx) => {
          if (idx < 9) {
            item.number = `0${idx + 1}`
          } else {
            item.number = idx + 1
          }
        })
        // this.addressAllList =this.addressAllList.filter(item=>item.distance>0)
        this.allList = this.addressAllList
        console.log(this.addressAllList, '地址')
        Toast.clear()
        // this.mapAddress();
      } else {
        Toast.clear()
      }
    },
    compare(prop) {
      return function (obj1, obj2) {
        const val1 = obj1[prop]
        const val2 = obj2[prop]
        if (val1 < val2) {
          return -1
        }
        if (val1 > val2) {
          return 1
        }
        return 0
      }
    },

    resort(addressAllList) {
      const idx = addressAllList.findIndex((item) => item.distance !== 0)
      const truncate = addressAllList.slice(0, idx)
      addressAllList.splice(0, idx)
      return addressAllList.concat(truncate)
    },

    async  onChange(idx, item) {
      if (this.showClassify) {
        this.showClassify = false
        return
      }
      console.log('111111111111', item)
      const params = getUrlParamObj()
      const string = Object.keys(params).reduce((i, n) => i + (params[n] ? (`&${n}=${params[n]}`) : ''), '')
      // this.isShowMap = true;
      // console.log(document.getElementsByTagName("amap-marker"));
      // this.markerList.selectByDataIndex(item);
      if (this.pageType === 'invite') {
        const res = await getAudiMinipUrl()
        window.location = `${res.data.data.configValue}acceptInviteFriends?userId=${this.inviteUserId}&dealerCode=${item.dealerCode}&dealerName=${item.dealerName}${string}`
      } else {
        this.$router.push({
          name: 'storeAddress',
          params: { store: item, idx, result: this.result },
          query: {
            store: item, idx, result: this.result, ...params
          }
        })
      }
    },

    // 初始化坐标
    mapAddress() {
      const map = new AMap.Map(document.getElementById('container'), {
        zoom: 13, // 显示范围
        resizeEnable: true
      })
      this.map = map
      // if (this.markerList.length!=0) {
      //   console.log(1111111111111111111111111)
      //   this.markerList.clearData();
      //   console.log(this.markerList)
      // }
      const this1 = this

      new AMapUI.loadUI(
        ['misc/MarkerList', 'overlay/SvgMarker', 'overlay/SimpleInfoWindow'],
        ((MarkerList, SvgMarker, SimpleInfoWindow) => {
          if (!SvgMarker.supportSvg) {
            // 当前环境并不支持SVG，此时SvgMarker会回退到父类，即SimpleMarker
            alert('当前环境不支持SVG')
          }
          const markerList = new MarkerList({
            // 关联的map对象
            map: this1.map,
            // //需要监听的列表节点事件
            // listElementEvents: ['click', 'mouseenter', 'mouseleave'],
            // //需要监听的marker事件
            // markerEvents: ['click', 'mouseover', 'mouseout'],
            // //需要监听的infoWindow事件
            infoWindowEvents: ['click'],
            autoSetFitView: false,
            // 返回数据项的Id
            getDataId: function (dataItem, index) {
              // index表示该数据项在数组中的索引位置，从0开始，如果确实没有id，可以返回index代替
              return dataItem.number
            },
            // 返回数据项的位置信息，需要是AMap.LngLat实例，或者是经纬度数组，比如[116.789806, 39.904989]
            getPosition: function (dataItem) {
              if (dataItem.longitude > 0 && dataItem.latitude > 0) {
                return [dataItem.longitude, dataItem.latitude]
              }
              return [121.176221, 31.285732]
            },
            // 返回数据项对应的Marker
            getMarker: function (dataItem, context, recycledMarker) {
              const label = dataItem.number
              // 存在可回收利用的marker
              if (recycledMarker) {
                // 直接更新内容返回
                recycledMarker.setIconLabel(label)
                return recycledMarker
              }
              // 返回一个新的Marker
              return new SvgMarker(
                new SvgMarker.Shape.SquarePin({
                  height: 40,
                  strokeWidth: 1,
                  strokeColor: 'black',
                  fillColor: 'black'
                  // infoBody:'SquarePin',
                }),
                {
                  iconLabel: label,
                  containerClassNames: 'my-svg-marker',
                  showPositionPoint: false // 显示定位点
                }
              )
            },
            // 返回数据项对应的列表节点
            getListElement: function (dataItem, context, recycledListElement) {
              const tpl = '<p><%- dataItem.id %>：<%- dataItem.desc %>'

              const content = MarkerList.utils.template(tpl, {
                dataItem: dataItem,
                dataIndex: context.index
              })

              if (recycledListElement) {
                // 存在可回收利用的listElement, 直接更新内容返回
                recycledListElement.innerHTML = content
                return recycledListElement
              }

              // 返回一段html，MarkerList将利用此html构建一个新的dom节点
              return `<li>${content}</li>`
            },
            getInfoWindow: null
            // getInfoWindow: function (item, context, recycledInfoWindow) {
            //   // if (recycledInfoWindow) {
            //   //     //存在可回收利用的infoWindow, 直接更新内容返回
            //   //     recycledInfoWindow.setInfoTitle(dataItem.id);
            //   //     recycledInfoWindow.setInfoBody(dataItem.desc);
            //   //     return recycledInfoWindow;
            //   // }

            //   //返回一个新的InfoWindow

            //   var infoWindow = new SimpleInfoWindow({
            //     infoTitle: `<div style="margin-top: 16px;margin-left: 10px;" class="my-desc"><strong>${
            //       item.number + ' ' + this1.showDealerName(item)
            //     }</strong></div>
            //       <div
            //   style="
            //     display: flex;
            //     align-items: center;
            //     margin-top: 8px;
            //   "
            // >
            //   <div style="text-align: center;
            //     display: flex;
            //     align-items: center;
            //     color: #fff;
            //     font-size: 8px;
            //     padding-top: 2px;
            //     padding-left: 3px;
            //     padding-right: 3px;
            //     height: 16px;
            //     background: #000;
            //     margin-left: 10px;
            //     margin-right: 5px;">${item.dealerType === 2 ? "销售" : "售后"}
            //   </div>
            //   <div style="color: #666; font-size: 12px;" >${
            //     item.dealerFullName
            //   }</div>
            // </div>`,
            //     infoBody: `<div style="display: flex; margin-left: 10px;margin-top: 10px;"><img
            //         style="width: 80px; height: 58px;"
            //         src=${
            //           (item.thumbnailUrl || "").includes("http")
            //             ? item.thumbnailUrl
            //             : this1.ossUrl + item.thumbnailUrl
            //         }
            //       /> <div>
            //       <div style="margin-left: 10px;margin-top: 10px; color: #666; font-size: 12px;" >${item.dealerType === 2 ? "销售热线：" : "售后热线：" } ${
            //         item.dealerPhone ? item.dealerPhone : ""
            //       }</div>
            //       <div  style="color: #666; font-size: 12px;margin-left: 10px;margin-top: 10px;" >24h热线：${
            //         item.hotPhone24 ? item.hotPhone24 : "暂无电话"
            //       }</div></div> </div>

            //         <div style="margin-left: 10px;margin-top: 10px; color: #666; font-size: 12px;" >地址：${
            //           item.dealerAdrress
            //         }</div>
            //         <div  style=" display: ${this1.env === "pub"  ? 'none' : 'flex'}; margin-left: 10px;margin-top: 10px;margin-bottom: 15px;">
            //         <img
            //         style="width: 16px; height: 16px;"
            //         src=${require("../../assets/img/icon-poi-search.png")}
            //         />
            //         <div class="mybtn" style=" color: #666; font-size: 12px;" >查看行车路线</div>
            //       </div>`,
            //     // @click=${}
            //     //基点指向marker的头部位置
            //     offset: new AMap.Pixel(0, -41),
            //   });
            //   // 监听点击事件
            //   // infoWindow.get$InfoBody().on("click", ".mybtn", function (event) {
            //   //   this1.onToNavigation(item);

            //   //   //阻止冒泡
            //   //   event.stopPropagation();
            //   // });
            //   return infoWindow;
            // },
          })

          // 创建一个SquarePin，显示在选中的Marker位置
          const svgMarker = new SvgMarker(
            new SvgMarker.Shape.SquarePin({
              height: 40,
              strokeWidth: 1,
              strokeColor: 'red',
              fillColor: 'red'
            }),
            {
              containerClassNames: 'my-svg-marker',
              showPositionPoint: false
            }
          )
          markerList.render([])
          // 监听选中改变
          markerList.on('selectedChanged', function (event, changedInfo) {
            console.log('111111111111', changedInfo)
            if (this.showClassify) {
              this.showClassify = false
            } else {
              this1.$router.push({ name: 'storeAddress', params: { store: changedInfo.selected.data, idx: changedInfo.selected.index, result: this1.result }, query: { store: changedInfo.selected.data, idx: changedInfo.selected.index, result: this1.result } })
            }

            // 重复选中，取消当前选中
            // if (changedInfo.selectAgain) {
            //   this.clearSelected();
            //   return;
            // }
            // const this2 = this;
            // var selectedRecord = changedInfo.selected,
            //   unSelectedRecord = changedInfo.unSelected,
            //   marker;

            // if (selectedRecord) {
            //   marker = selectedRecord.marker;
            //   marker.hide();

            //   svgMarker.setMap(marker.getMap());
            //   svgMarker.setPosition(marker.getPosition());
            //   svgMarker.setIconLabel(selectedRecord.id);
            //   svgMarker.show();
            //   //给新的svg添加点击事件
            //   svgMarker.on("click", function (event, changedInfo) {
            //     marker.show();
            //     svgMarker.hide();
            //     this2.clearSelected();
            //   });
            // } else {
            //   svgMarker.hide();
            // }

            // if (unSelectedRecord) {
            //   marker = unSelectedRecord.marker;

            //   marker.show();
            // }
          })
          // 展示该数据
          if ((this1.result.indexOf('服务中心') > -1
              || this1.result.indexOf('新能源维修') > -1
              || this1.result.indexOf('高压电池维修') > -1)
              && this1.result.indexOf('零售交付') == -1
              && this1.result.indexOf('试驾') == -1
              && this1.result.indexOf('充电') == -1
              && this1.result.indexOf('品牌体验') == -1
              && this1.result.indexOf('全部') == -1
          ) {
            const arrayLists = []
            this1.addressAllList.forEach((item, idx) => {
              arrayLists.push({
                ...item,
                longitude: ['0', '1,2', '4'].includes(item.dealerType) ? item.afterSalesLongitude : item.longitude,
                latitude: ['0', '1,2', '4'].includes(item.dealerType) ? item.afterSalesLatitude : item.latitude,
                number: idx < 9 ? `0${idx + 1}` : idx + 1
              })
            })
            console.log(arrayLists, '11111')
            markerList.render(arrayLists)
          } else {
            console.log(this1.addressAllList, 'this1.addressAllListthis1.addressAllList')
            const arrayLists = []
            this1.addressAllList.forEach((item, idx) => {
              arrayLists.push({
                ...item,
                number: idx < 9 ? `0${idx + 1}` : idx + 1
              })
            })
            console.log(arrayLists, '11111')
            markerList.render(arrayLists)
          }

          // markerList.render(this1.addressAllList)
          markerList.clearRecycle()
          // markerList=this1.addressAllList
          console.log('markerList', markerList)
          this1.markerList = markerList
        })
      )
      this.map.on('click', (params) => {
        this1.showClassify = false
      })
      this.autozoom()
    },

    autozoom() {
      let minLong = Number.MAX_VALUE; let maxLong = Number.MIN_VALUE; let minLat = Number.MAX_VALUE; let
        maxLat = Number.MIN_VALUE
      this.addressAllList.forEach((address) => {
        if (address.longitude) {
          if (minLong >= +address.longitude) {
            minLong = +address.longitude
          }
          if (maxLong <= +address.longitude) {
            maxLong = +address.longitude
          }

          if (minLat >= +address.latitude) {
            minLat = +address.latitude
          }
          if (maxLat <= +address.latitude) {
            maxLat = +address.latitude
          }

          if (minLat >= +address.latitude) {
            minLat = +address.latitude
          }
          if (maxLat <= +address.latitude) {
            maxLat = +address.latitude
          }
        }
      })
      if (this.addressAllList[0]) {
        const center = [(maxLong + minLong) / 2, (maxLat + minLat) / 2]
        const markersDistance = [
          AMap.GeometryUtil.distance(new AMap.LngLat(maxLong, center[1]), new AMap.LngLat(minLong, center[1])),
          AMap.GeometryUtil.distance(new AMap.LngLat(center[0], maxLat), new AMap.LngLat(center[0], minLat))
        ]

        let zoom = 3
        // 获取屏幕尺寸
        if (!this.containerSize[0]) {
          this.containerSize = AMap.DomUtil.getViewport(document.getElementById('container'))
        }

        for (let i = 11; i >= 3; i -= 1) {
          // 通过距离像素比公式将地理长度转为像素尺寸
          const getScalePerPixel = (zoom) => 0.2531 * Math.pow(2, (19 - zoom))
          const markersSize = [markersDistance[0] / getScalePerPixel(i), markersDistance[1] / getScalePerPixel(i)]
          // 检查屏幕是否能容纳（四边留有余量）
          if (markersSize[0] < (this.containerSize[0] - 20) && markersSize[1] < (this.containerSize[1] - 20)) {
            zoom = i
            break
          }
        }

        this.map.setZoomAndCenter(zoom, [
          this.addressAllList[0] ? (minLong + maxLong) / 2 : '',
          this.addressAllList[0] ? (minLat + maxLat) / 2 : ''
        ])
      }
    },

    // 调用APP地图
    onToNavigation(item) {
      if (item.longitude > 0) {
        callNative('navigationMap', {
          lat: item.latitude.toString(),
          long: item.longitude.toString(),
          des: item.dealerName
        })
      } else {
        // 没有经纬度
      }
    },
    wxLocationss(param) {
      wxConf(window.location.href.split('#')[0])
      console.log('获取经纬度前')
      wx.error((res) => {
        console.error('wxconfig 失败:', res)
      })
      wx.ready(() => {
        wx.openLocation({
          latitude: Number(res.longitude),
          longitude: Number(res.longitude),
          scale: 28,
          success: function (res) {
            console.log(res.address)
          },
          fail(res1) {
            console.log(res1, 'res1res1')
          }
        })
      })
    },

    getLocations() {
      console.log('高德获取定位信息====')
      const map = new AMap.Map(document.getElementById('container'), {
        zoom: 13, // 显示范围
        center: [121.20215, 31.41444], // 初始显示的坐标
        resizeEnable: true
      })

      // 获取定位
      const options = {
        showButton: true, // 是否显示定位按钮
        buttonPosition: 'RB', // 定位按钮的位置
        buttonOffset: new AMap.Pixel(10, 20), // 定位按钮距离对应角落的距离
        showMarker: true, // 是否显示定位点
        // 'markerOptions': { //自定义定位点样式，同Marker的Options  个人的点
        //   'offset': new AMap.Pixel(-18, -36),
        //   'content': '<img src="https://a.amap.com/jsapi_demos/static/resource/img/user.png" style="width:36px;height:36px"/>'
        // },
        showCircle: true, // 是否显示定位精度圈
        circleOptions: { // 定位精度圈的样式
          strokeColor: '#0093FF',
          noSelect: true,
          strokeOpacity: 0.5,
          strokeWeight: 1,
          fillColor: '#ff0000',
          fillOpacity: 0.25
        }
      }
      AMap.plugin(['AMap.Geolocation'], () => {
        const geolocation = new AMap.Geolocation(options)
        console.log('定位信息', geolocation)
        map.addControl(geolocation) // 授权弹窗
        geolocation.getCurrentPosition()
        AMap.event.addListener(geolocation, 'complete', onComplete) // 返回定位信息
        AMap.event.addListener(geolocation, 'error', onError) // 返回定位出错信息
      })
    },
    onComplete(obj) {
      const res = `经纬度：${obj.position
      }\n精度范围：${obj.accuracy
      }米\n定位结果的来源：${obj.location_type
      }\n状态信息：${obj.info
      }\n地址：${obj.formattedAddress
      }\n地址信息：${JSON.stringify(obj.addressComponent, null, 4)}`
      console.log('====当前位置信息', res)
    },

    getEvn() {
      const ua = navigator.userAgent.toLowerCase()
      if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        console.log('在微信中')
        // 在微信中
        this.isMiniProgram = true
      }
    },

    async initData() {
      const { env } = this.$route.query
      this.env = env
      console.log('this.env', this.env)

      this.pageType = this.$route.query?.pageType
      this.inviteUserId = this.$route.query?.userId

      this.classifyHeight = `${this.$refs.condition.offsetHeight}px`

      // if (this.env === undefined) {
      //   const data = await callNative('getLocationCity', {})
      //   console.log('------当前定位信息----------', data)
      //   this.location = data.location || `${latitude},${longitude}`
      //   console.log('getLocationCity', data)

      //   this.initCityName = data.city
      // }
      const { device, device: { sign } } = this
      if (['minip', 'native'].includes(sign)) {
        const data = {}
        if (sign === 'minip') {
          data.wxLocation = this.$route.query.wxLocation || ''
        }
        const { location, city } = await getLocationInfo(3000, sign === 'minip' ? 'minippp' : sign, data) || { city: '', location: '', tips: '' }
        console.log('%c [ initData location ]-398', 'font-size:14px; background:#cf222e; color:#fff;', location, city)
        this.location = location || `${latitude},${longitude}`
        const arrayLocation = this.location.split(',')
        // let cityCode=''
        // console.log(this.location.split(',').reserve(),'this.location.split(',').reserve()')
        const cityName = await getLocationCityName([arrayLocation[1] * 1, arrayLocation[0] * 1])
        // cityCode = await getLocation([arrayLocation[1]*1,arrayLocation[0]*1])
        // console.log(acity,'11111',acitycode,'acityacityacity')
        this.initCityName = cityName
        this.getAllCityList()
        return
      }

      this.getAllCityList()


      // this.getEvn()
      // console.log('====是否是微信环境===', this.isMiniProgram)
      // if (this.isMiniProgram) {
      //   // 获取当前城市的城市名
      //   AMap.plugin('AMap.Geolocation', () => {
      //     const geolocation = new AMap.Geolocation({
      //       // 是否使用高精度定位，默认：true
      //       enableHighAccuracy: true,
      //       // 设置定位超时时间，默认：无穷大
      //       timeout: 10000
      //     })
      //     console.log('====geolocation===', this.geolocation)

      //     geolocation.getAddress(
      //       [o.Ce.center.lng, o.Ce.center.lat],
      //       (status, result) => {
      //         console.log('result', result)
      //         if (status === 'complete' && result.info === 'OK') {
      //           if (result && result.regeocode) {
      //             // this.city =
      //             //   result.regeocode.addressComponent.city
      //           }
      //         }
      //       }
      //     )
      //   })
      //   //  通过高德获取定位
      //   // this.getLocation()
      // }

      getAPITimeOutTesting()
    },

    networkReload() {
      this.initData()
    }
  }

}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";
.seachBox {
  // width: 100%;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E5E5E5;
  height: 56px;

  span{
    font-size: 16px;
    font-weight: 400;
    color: #000000;
    line-height: 24px;
  };
  img{
    width: 24px;
    height: 24px;
  }
  div{
    // padding-left: 20px;
    width: 60%;
    font-size: 16px;
    font-weight: 400;
    color: #333333;
    line-height: 24px;
  }
}
.select_lable{
  position: relative;
}
.classify_box {
  padding: 10px 12px;
  font-size: 16px;
  transition: 0.5s;
  background: #fff;
  z-index: 9999;
  position: absolute;
 ::v-deep .van-checkbox--horizontal {
      margin: 10px;
      margin-left: 0;
      margin-right: 15px;
      :nth-child(2n) {
          margin-left: 15px;
      }
  }
}
.c-font12{
  font-size: 12px;
  font-weight: 400;
  color: #000000;
  line-height: 20px;
}
.lables {
  font-weight: normal;
  color: #000000;
  line-height: 16px;
  margin-top: 8px;

  span {
      background-color: #E5E5E5;
      font-size: 10px;
      padding: 2px 6px;
      margin-right: 8px;
      &:nth-child(1){
        background: #000000;
        color:#ffffff;
      }
  }
}
.container.seek-store {
  width: 100vw;
  height: 100%;
}
.item_btn {
  text-align: center;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 8px;
  padding-left: 3px;
  padding-right: 3px;
  height: 16px;
  background: #000;
  margin-top: 5px;
  margin-right: 5px;
}

.item-wrapper {
  .c-flex-between;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 10px;
  padding-top: 10px;
}

.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  margin: 0 15px;
  padding: 5px 0;
}

.navgation-wrapper {
  .c-font14;
  .c-flex-center;
  color: #b2b2b2;
  text-align: center;

  .nav-icon {
    width: 24px;
    margin: 0 auto 5px auto;
  }
}

.line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f2f2f2;
  position: relative;
  .btn-change {
    display: flex;
    align-items: center;

    .title {
      width: 90px;
      font-size: 16px;
      color: #000;
      margin: 5px 5px;
    }

    .change-name {
      font-size: 16px;
      color: #000;
      margin-right: 10px;
    }
  }

  .btn-icon {
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #000;
  }
}

._scroll {
  overflow: scroll;
  max-height: 184px;
}

.actionSheet {
  position: absolute;
  box-sizing: border-box;
  z-index: 9999;
  width: 100%;

  left: 0;
  background-color: #f2f2f2;
  //   border: 1px solid #000000;
  padding: 0 10px;
  display: flex;
  flex-flow: column;

  .text1 {
    font-size: 16px;
    color: #333;
    padding-left: 10px;
    line-height: 40px;
    border-bottom: 1px solid #e5e5e5;
  }
}

/deep/.my-svg-marker .amap-simple-marker-label {
  color: #fff;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
}
/deep/ .van-checkbox__icon--checked .van-icon{
  background-color:#ffffff !important;
  color:#000000;
}
</style>
