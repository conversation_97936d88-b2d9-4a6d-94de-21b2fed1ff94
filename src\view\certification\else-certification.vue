<template>
  <div class="idcertification">
    <div class="line">
      <div class="title">证件类型</div>
      <div class="btn-change" @click="onShowCertificateType">
        <div class="item_text">
          {{ certificateName }}
        </div>
        <van-icon
          class="btn-icon"
          :name="showCertificateType ? 'arrow' : 'arrow-down'"
          size="16px"
        />
      </div>
    </div>
    <div v-if="!showCertificateType">
      <div class="certificate-type" @click="onCertificateType(101)">
        外籍护照
      </div>
      <div class="certificate-type" @click="onCertificateType(102)">
        港澳台居民往内地通行证（回乡证）
      </div>
      <div class="certificate-type" @click="onCertificateType(103)">
        台湾居民来往大陆通行证（台胞证）
      </div>
      <div class="certificate-type" @click="onCertificateType(104)">
        港澳居民居住证
      </div>
      <div class="certificate-type" @click="onCertificateType(105)">
        台湾居民居住证
      </div>
      <div class="certificate-type" @click="onCertificateType(106)">
        中国人民解放军军官证（军官证）
      </div>
      <div class="certificate-type" @click="onCertificateType(107)">
        中国人民武装警察部队警官证（武警证）
      </div>
       <div class="certificate-type" @click="onCertificateType(108)">
        外国人永久居留居住证（外国人永居证）
      </div>
    </div>
    <div class="line-border-bottom">
      <div class="name">姓名</div>

      <van-field
        class="str"
        type="text"
        v-model="updateName"
        rows="1"
        placeholder="请输入您的姓名"
      />
    </div>
    <div class="line-border-bottom">
      <div class="name">证件号</div>
      <van-field
        class="str"
        type="text"
        v-model="updateGid"
        rows="1"
        placeholder="请输入您的证件号"
      />
    </div>

    <img
      v-if="!base64"
      class="item-img"
      style="margin-top: 16px"
      src="../../assets/img/icon-identity10.png"
      @click="onUploadOcrIdCard()"
    />
    <img
      class="item-img"
      v-if="base64"
      :src="base64"
      style="margin-top: 16px"
      @click="onUploadOcrIdCard()"
    />

    <div
      class="item-title-bold"
      style="
        text-align: center;
        justify-content: center;
        font-size: 14px;
        margin-top: 16px;
      "
    >
      上传证件
    </div>

    <input
      class="hide_file"
      ref="leftFile"
      id="upload"
      type="file"
      @change="getFile($event)"
      capture="camera"
      accept="image/camera"
    />
    <div class="btnWarp" @click="onConfirmsubmit">
      <div class="buttons">确认</div>
    </div>
    <!--拍照提示-->
    <van-action-sheet
      v-model="show"
      :safe-area-inset-bottom="true"
      :round="false"
    >
      <div style="padding-right: 16px; padding-left: 16px">
        <div
          class="item-title-bold"
          style="
            text-align: center;
            justify-content: center;
            font-size: 16px;
            margin-top: 18px;
          "
        >
          证件上传示例
        </div>
        <div
          style="
            text-align: center;
            justify-content: center;
            font-size: 14px;
            margin-top: 16px;
            color: #666;
            padding-right: 26px;
            padding-left: 26px;
          "
        >
          请于光线充足的环境下，纯色背景下，四角对齐，横向拍照
        </div>
        <img class="item-img" src="../../assets/img/icon-identity3.png" />
      </div>
      <van-grid :border="false" :column-num="4" :gutter="16">
        <van-grid-item>
          <img src="../../assets/img/icon-identity5.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">缺失</div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity6.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">模糊</div>
        </van-grid-item>

        <van-grid-item>
          <img src="../../assets/img/icon-identity7.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">过滤</div>
        </van-grid-item>

        <van-grid-item text="">
          <img src="../../assets/img/icon-identity8.png" />
          <div style="font-size: 12px; margin-top: 8px; color: #333">
            背景模糊
          </div>
        </van-grid-item>
      </van-grid>
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onUploadingImg"
          :text="'知道了'"
          color="black"
          font-size="15px"
          height="50px"
        />
      </div>
    </van-action-sheet>
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols lan-dialog-content-f12"
      v-model="dialogShow"
      cancel-button-text="取消"
      confirm-button-text="确定"
      :title="`上汽奥迪申请获取相机权限`"
      show-cancel-button
      :message="`上汽大众需要申请相机权限，以便通过扫一扫、拍摄照片或视频为您提供上传头像、车辆图片、绑车/会员认证、专属桩绑定、发动态、提问题、写文章、评论、扫码充电、形象定制、AI助手相关服务。拒绝或取消授权不影响使用其他服务。`"
      @confirm="dialogConfirm"
      @cancel="dialogCancel"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Image as VanImage,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Popover,
  ActionSheet,
  Grid,
  GridItem
} from 'vant'
import HtmlImageCompress from 'html-image-compress'
import { callNative, compressFileImg } from '@/utils'

import {
  postOcrImageUpload,
  getManCarMemberInfo,
  postOcrIdDrivingSubmit
} from '@/api/api'
import AudiButton from '@/components/audi-button'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Popover)
  .use(ActionSheet)
  .use(Grid)
  .use(GridItem)
  .use(VanImage)

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      show: false, // 上传拍照提示
      uploadType: 101, // 101-外国人护照，102-港澳居民来往内地通行证（回乡证），103-台湾居民来往大陆通行证（台胞证
      certificateName: '外籍护照',
      updateGid: '',
      updateName: '',
      carMemberInfoModel: {}, // 会员信息
      base64: '',
      showCertificateType: true,
      boolRepeatCertificate: 0,
      dialogShow: false
    }
  },

  mounted() {
    this.getManCarMemberInfo()
  },
  methods: {
    async getManCarMemberInfo() {
      const { data } = await getManCarMemberInfo({})
      // 判断 实名认证状态,1-认证用户，2-未认证用户，3-认证审核中，4-认证审核被拒'
      this.boolRepeatCertificate = `${data.data.certificationStatus}`
    },
    // 选择证件类型
    onShowCertificateType() {
      this.showCertificateType = !this.showCertificateType
    },
    onCertificateType(type) {
      this.uploadType = type
      this.showCertificateType = true
      if (type === 101) {
        this.certificateName = '外籍护照'
      } else if (type === 102) {
        this.certificateName = '港澳台居民往内地通行证（回乡证）'
      } else if (type === 103) {
        this.certificateName = '台湾居民来往大陆通行证（台胞证）'
      } else if (type === 104) {
        this.certificateName = '港澳居民居住证'
      } else if (type === 105) {
        this.certificateName = '台湾居民居住证'
      } else if (type === 106) {
        this.certificateName = '中国人民解放军军官证（军官证）'
      } else if (type === 107) {
        this.certificateName = '中国人民武装警察部队警官证（武警证）'
      } else if (type === 108) {
        this.certificateName = '外国人永久居留居住证（外国人永居证）'
      }
      
    },
    async getFile(e) {
      const file = e.target.files[0]

      const reader = new FileReader()
      if (file) {
        // 通过文件流将文件转换成Base64字符串
        reader.readAsDataURL(file)
        // 转换成功后
        const this1 = this
        reader.onloadend = function () {
          // 输出结果
          this1.formData(file, reader.result)
        }
      }
    },
    async formData(file, base64) {
      // const fileTemp = await compressFileImg({
      //   content: base64,
      //   file: file
      // })
      // var reader = new FileReader();
      // if (fileTemp) {
      //   //通过文件流将文件转换成Base64字符串
      //   reader.readAsDataURL(fileTemp);
      //   //转换成功后
      //   const this1 = this;
      //   reader.onloadend = function() {
      //     //输出结果
      //     this1.base64 = reader.result;
      //   };
      // }

      const htmlImageCompress = new HtmlImageCompress(file, {
        quality: 0.5
      }).then((result) => {
        console.log(result)
        this.base64 = result.base64
        this.show = false
      })
    },

    onUploadOcrIdCard() {
      this.show = true
    },

    // 上传图
    async onUploadingImg() {
      const data = await callNative('albumCameraEvent', { type: 1 })
      console.log(data, '是否开启相册权限')
      if (data.status) {
        this.$refs.leftFile.click()
      } else {
        this.dialogShow = true
      }
    },
    dialogCancel() {},
    dialogConfirm() {
      callNative('openpage', {})
    },
    // 确认提交
    async onConfirmsubmit() {
      if (this.base64) {
        const param = {
          authType: 0,
          image: this.base64,
          // certificateType: this.uploadType,
          certificateType: 0,
          channelCode: '05',
          name: this.updateName,
          idNumber: this.updateGid
        }
        this.$store.commit('showLoading')
        const { data } = await postOcrImageUpload(param)
        this.$store.commit('hideLoading')
        if (data.code === '200') {
          this.postOcrIdDrivingSubmit(data.data.imageId)
        } else {
          callNative('toast', { type: 'fail', message: data.message })
        }
      } else {
        callNative('toast', { type: 'fail', message: '证件照片不能为空' })
      }
    },
    // 提交
    async postOcrIdDrivingSubmit(imageId) {
      if (!this.updateGid) {
        callNative('toast', { type: 'fail', message: '姓名不能为空' })
        return
      }
      if (!this.updateName) {
        callNative('toast', { type: 'fail', message: '证件号不能为空' })
        return
      }
      const param = {
        authType: 0,
        certificateType: this.uploadType,
        // certificateType: 102,

        channelCode: '05',
        certificateOcrId: imageId,
        // name: this.updateName,
        // idNumber: this.updateGid,

        submitType: this.boolRepeatCertificate !== '2' ? 2 : 1,
        updateGid: this.updateGid,
        updateName: this.updateName
      }
      console.log('param------', param)
      const { data } = await postOcrIdDrivingSubmit(param)
      if (data.code === '200') {
        this.$router.push({
          path: '/certification/identity-certification-in',
          query: {}
        })
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/buttons.less');

.idcertification {
  height: 100%;
  padding: 16px;
  padding-bottom: 80px;
}

.item-img {
  padding-right: 16px;
  padding-left: 16px;
  width: 92%;
  height: 200px;
}

.item-title-bold {
  width: 100%;
  font-size: 16px;
  color: #000;
  font-family: 'Audi-WideBold';
  margin-bottom: 16px;
}

.line {
  padding-top: 16px;
  padding-bottom: 16px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 16px;
    color: #000;
  }

  .btn-change {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .item_text {
      color: #000;
      font-size: 14px;
      margin-right: 8px;
    }

    .btn-icon {
      align-items: center;
      font-size: 12px;
      color: #000;
    }
  }
}

.btn-delete-wrapper {
  margin: 16px;
}

.hide_file {
  display: none;
}

.line-border-bottom {
  // width: 100%;
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f2f2f2;

  .name {
    width: 90px;
    font-size: 16px;
    color: #000;
    font-family: 'Audi-Normal';
  }

  .str {
    font-family: 'Audi-Normal';
    font-size: 16px;
    color: #000;
  }
}

.certificate-type {
  display: flex;
  align-items: center;
  background: #f2f2f2;
  padding: 14px;
  border-bottom: 1px solid #e5e5e5;
  font-size: 14px;
  color: #333;
}

.btnWarp {
  position: fixed;
  z-index: 2;
  height: 50px;
  width: 100%;
  bottom: 0;
  padding-bottom: 30px;
  background: #fff;

  .buttons2 {
    top: 60px;
  }
}
</style>
