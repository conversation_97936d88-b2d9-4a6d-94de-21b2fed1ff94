<template>
  <div
    :class="['container',{'activeDrop':dropdownModel}]"
    style="position: relative"
  >
    <navigation :back-type="backType">
      <!-- <div @click="onMap">
        <img
          class="title_img"
          slot="icon"
          :src="isShowMap ? activeIcon : inactiveIcon"
        >
      </div> -->
    </navigation>
    <div
      id="container3"
      :style="{width: '100vw', height: px2rem(900)}"
      v-show="isShowMap"
    />

    <van-action-sheet
      v-model="isShowMap"
      :round="false"
      :overlay="false"
      :style="{height: `calc(100% - ${px2rem(900)})`}"
    >

    <van-dropdown-menu active-color="#024258" :close-on-click-outside="false">
      <van-dropdown-item  :title="cityNameTab" ref="itemChange">
        <van-tabs v-model="active" @click="onClickTab" color="#000">
          <van-tab :title="provinceName"></van-tab>
          <van-tab :title=" provinceName == '选择省' ? '' :cityNameTab" ></van-tab>
        </van-tabs>

        <div class="cityNameJons">
          <van-cell center :title="v.cityName" v-for="(v,i) in columns" :key="i" @click="changeCity(i,v)">
            <template #right-icon>
               <img src="@/assets/back-small.png" alt=""  v-show="i == proVinceCurrentIndex">
            </template>
          </van-cell>
        </div>
      </van-dropdown-item>

      <van-dropdown-item :title="changeStatus"  @open="dropdownModel = true"  @closed="dropdownModel = false" ref="itemChangeTwo">
        <van-cell center :title="v.text" v-for="(v,i) in option2" :key="i" @click="changeStatusClick(i,v)">
            <template #right-icon>
               <img src="@/assets/back-small.png" alt=""  v-show="v.value == changeStatusVal" class="rightImgs">
            </template>
          </van-cell>
      </van-dropdown-item>
    </van-dropdown-menu>

      <!-- <div class="line">
        <div class="title">
          城市
        </div>
        <div
          class="btn-change"
          @click="showPicker = true"
        >
          <div class="change-name">
            {{ value }}
          </div>
          <van-icon
            class="btn-icon"
            name="arrow"
            size="16px"
          />
        </div>
      </div> -->

      <div v-if="notCarList" class="notList">此城市没有服务商，请切换其他城市~</div>

      <div class="listStyle">
        <div
          v-for="(item, idx) in cityAddressList"
          :key="idx"
          @click.stop="chooseDealer(item)"
        >
        <div class="item-wrapper">
          <div >
            <img
              class="img-wrapper"
              :src="
                ((item.imageUrl || '').includes('http')
                    ? item.imageUrl
                    : ossUrl + item.imageUrl) | audiwebp
                "
              alt=""
            >
          </div>

          <div class="content-wrapper flex1">
            <div class="c-font16 c-bold">
              {{ item.dealerName }}
            </div>
            <div class="c-font14 " style="margin-top: 4px" v-if="item.bindAgentName">
              {{ '代理商：'+item.bindAgentName }}
            </div>
            <div style="margin-top: 4px" />
            <div class="c-font12">
              {{ item.dealerAdrress }}
            </div>

            <div style="margin-top: 15px" />
            <div class="c-font12">
              <img
                class="phone-icon"
                :src="require('@/assets/img/dealer-phone.png')"
                alt=""
              >
              {{ item.dealerPhone }}
            </div>
          </div>

          <div class="navgation-wrapper" v-show="item.longitude !== 0">
            <div>
              <div
                class="nav-icon"

                @click.stop="
                  toNavigation(item.longitude, item.latitude, item.dealerName)
                "
              >
                <img
                  :src="require('@/assets/img/nav.png')"
                  alt=""
                >
              </div>
              <span class="c-font12">
                {{ item.distance | formatDistance }}
              </span>
            </div>
          </div>
        </div>
          <div class="item_bottom">
            <div v-if="item.provideSaicAudiPickUpService === '1'" class="item_btn" style="width: 50px; height: 24px; margin-right: 10px;">
              取送车
              </div>
            <!-- <div v-if="item.businessStatus === '3'" class="item_btn" style="width: 60px; height: 24px; ">
              NEV服务
            </div> -->
          </div>
        </div>
      </div>


    </van-action-sheet>

    <div v-if="!isShowMap" >
      <div

        v-for="(item, idx) in addressAllList"
        :key="idx"
        @click.stop="chooseDealer(item)"
      >
      <div class="item-wrapper">
        <div >
          <img
            class="img-wrapper"
            :src="
              ((item.imageUrl || '').includes('http')
                  ? item.imageUrl
                  : ossUrl + item.imageUrl) | audiwebp
              "
            alt=""
          >
        </div>

        <div class="content-wrapper flex1">
          <div class="c-font16 c-bold">
            {{ item.dealerName }}
          </div>
          <div class="c-font14 " style="margin-top: 4px" v-if="item.bindAgentName">
            {{ '代理商：'+item.bindAgentName }}
          </div>
          <div style="margin-top: 4px" />
          <div class="c-font12">
            {{ item.dealerAdrress }}
          </div>

          <div style="margin-top: 15px" />
          <div class="c-font12">
            <img
              class="phone-icon"
              :src="require('@/assets/img/dealer-phone.png')"
              alt=""
            >
            {{ item.dealerPhone }}
          </div>
        </div>

        <div class="navgation-wrapper" v-show="item.longitude !== 0">
          <div>
            <div
              class="nav-icon"
              @click.stop="
                toNavigation(item.longitude, item.latitude, item.dealerName)
              "
            >
              <img
                :src="require('@/assets/img/nav.png')"
                alt=""
              >
            </div>
            <span class="c-font12">
              {{ item.distance | formatDistance }}
            </span>
          </div>
        </div>
      </div>
        <div class="item_bottom">
          <div v-if="item.provideSaicAudiPickUpService === '1'" class="item_btn" style="width: 50px; height: 24px; margin-right: 10px;">
            取送车
            </div>
          <!-- <div v-if="item.businessStatus === '3'" class="item_btn" style="width: 60px; height: 24px; ">
            NEV服务
          </div> -->
        </div>
      </div>
    </div>

    <!-- <van-popup
      v-model="showPicker"
      round
      position="bottom"
    >
      <van-picker
        show-toolbar
        :visible-item-count="5"
        :columns="columns"
        value-key="cityName"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </van-popup> -->
  </div>
</template>

<script>
import { Promise } from 'q'
import { mapState } from 'vuex'
import Vue from 'vue'
import { ActionSheet, Picker, Popup,DropdownMenu,DropdownItem,Tabs,Cell } from 'vant'
import {
  getCityList,
  getAfterSalesListForReservation,
  getProvinceCityList
} from '@/api/api'


import baseUrl from '@/config/url'
import { callNative, getdiscount2 } from '@/utils'
import navigation from '../../components/navigation.vue'
import storage from "../../utils/storage";


Vue.use(ActionSheet).use(Picker).use(Popup).use(DropdownMenu).use(DropdownItem).use(Tabs).use(Cell)

// const latitude = ''
// const longitude = ''
const latitude = '31.2304'
const longitude = '121.4737'

export default {
  components: { navigation },
  name: 'Name',
  data() {
    return {
      notCarList:false,
      changeStatus:"智能推荐",
      changeStatusVal:1,
      getLocationCityData:null,
      dropdownModel:false,
      columnsCopy:[],
      provinceName:"选择省",
      cityNameTab:"选择城市",
      proVinceCurrentIndex:0,
      active:0,

      option2: [
        { text: '智能推荐', value: 1 },
        { text: '距离优先', value: 2 },
      ],

      ossUrl: baseUrl.BaseOssHost,
      addressAllList: [], // 全部地址
      location: '', // 模拟本地的经纬度信息.
      map: null,
      infoWindow:null,
      backType: 'app',
      isShowMap: true,
      activeIcon: require('../../assets/img/icon-cut1.png'),
      inactiveIcon: require('../../assets/img/icon-cut2.png'),

      cityAddressList: [], // 城市地址
      value: '',
      showPicker: false,
      columns: [],
      initCityName: null, // 初始进来的城市
      markerList:[],
      cityLists:[],//城市集合
      cityName:'',
      bindDealerCode:'',
    }
  },

  async mounted() {
    console.log("webp", this.$webp)
    this.bindDealerCode = this.$route.query.bindDealerCode;

    var dealerModel = storage.get("dealerCarModel") || "{}";

    this.dealerModel = dealerModel;

    this.cityName = JSON.parse(dealerModel).cityName || ""; // 服务商城市

    let data = await callNative('getLocationCity', {});
    let mapCenterData = ['',''];
    //这里兼容安卓
    if( 'city' in  data && data.city){
        this.location = data.location || `${latitude},${longitude}`
        this.initCityName = data.city
        mapCenterData = [data.location.split(",")[1],data.location.split(",")[0]]
    }else {
        this.location = `${latitude},${longitude}`
        mapCenterData = [longitude, latitude]
        this.initCityName = "上海市"
    }

    const map = new AMap.Map(document.getElementById('container3'), {
      center:  mapCenterData,
      zoom: 16, // 显示范围
      resizeEnable: true
    })
    this.map = map
    var infoWindow = new AMap.InfoWindow({offset: new AMap.Pixel(0, -31)});
    this.infoWindow = infoWindow

    console.log('getLocationCity', data)
    this.getLocationCityData = data

    this.getProvinceCityList()
    this.getDealerAllList()
  },

  methods: {
    onMap() {
      this.isShowMap = !this.isShowMap
    },
    changeStatusClick(i,v){
      this.changeStatusVal = v.value
      this.changeStatus = v.text
      this.$refs.itemChangeTwo.toggle();
      this.onConfirm(this.cityNameTab)
    },
    px2rem(px) {
      // 获取当前视口的宽度（vw）
      const vw = window.innerWidth / 100;
      // 将 px 转换为 vw
      const vwValue = ((px / 75) * (10 * vw) * 100) / window.innerWidth;
      return vwValue + "vw";
    },
    //选择城市列表
    changeCity(i,v){
      console.log(i,v)
      this.proVinceCurrentIndex = i
      if(this.active == 0){
        this.active = 1
        this.columns = v.children
        this.proVinceCurrentIndex = null
        this.provinceName = v.cityName
        this.cityNameTab = '选择城市'
      }else{
        this.cityNameTab = v.cityName
        this.onConfirm(v.cityName)
        this.$refs.itemChange.toggle();
      }
    },

    //城市tab切换
    onClickTab(name,title){
      this.active = name
      console.log(name,"name")
      if(name == 0){
        this.columns = this.columnsCopy
        this.proVinceCurrentIndex = this.getProvinceNameByCityIndexs(this.provinceName)
      }else{
        console.log(this.provinceName,this.columnsCopy,'www',this.cityName)
        this.columns = this.columnsCopy.find( r=> r.cityName === this.provinceName).children

        if(this.cityName === '' || this.cityName === undefined){
          if(this.cityNameTab){
            this.proVinceCurrentIndex = this.columns.findIndex( r => r.cityName == this.cityNameTab)
          }else{
            this.proVinceCurrentIndex = null
          }
        }else{
          console.log(this.columns,"this.columns",this.value)
          this.proVinceCurrentIndex = this.columns.findIndex( r => r.cityName == this.value)
        }
      }
    },
    getProvinceNameByCityIndexs(provinceName){
      console.log(provinceName,"")
        let currentIndex = 0;
       if(provinceName){
         currentIndex = this.columns.findIndex( r => r.cityName == provinceName)
       }

       return currentIndex
    },
    getProvinceNameByCityIndex(cityName){
      console.log("入参：",cityName)
        let currentItem = this.getProvinceNameByCityName(cityName)
        console.log("cityName：",cityName)
        let currentIndex = 0;
       if(currentItem){
         currentIndex = this.columns.findIndex( r => r.cityName == currentItem.cityName)
       }

       return currentIndex

    },

    getProvinceNameByCityName(cityName) {
      for (const province of this.columns) {
        const city = province.children.find(city => city.cityName === cityName);

        if (city) {
          return province; // 找到城市返回省份名称
        }
      }
      return null; // 如果没有找到，返回 null
    },

    // 获取服务商城市
    async getProvinceCityList() {
      const { data } = await getProvinceCityList()

      // this.columns = data.data
      if(data.data.length > 0){
         for (let item of data.data) {
            for(let item2 of item.cityList){
              this.cityLists.push(item2)
            }
           let newItem = {cityName:item.provinceName,provinceCode:item.provinceCode,children:item.cityList}
            this.columns.push(newItem)
            this.columnsCopy.push(newItem)
          }

          // this.columns.push({cityName:'山东省',provinceCode:this.getLocationCityData.adCode,children:[
          // {"cityCode": "120100", "cityName": "济南市"}
          // ]})
          // this.columnsCopy.push({cityName:'山东省',provinceCode:this.getLocationCityData.adCode,children:[
          // {"cityCode": "120100", "cityName": "济南市"}
          // ]})
          // this.cityLists.push({"cityCode": "120100", "cityName": "济南市"})

      }
      console.log("=======",this.cityLists)
      console.log("this.cityName=======",this.cityName)
      console.log("this.columns=======",this.columns)

      if (this.cityName === '' || this.cityName === undefined) {
        console.log('1-1',this.initCityName,"2222")
        // 没有定位城市先取第一条
        if (this.initCityName === undefined) {
          console.log('1-2')
          this.value = data.data[0].cityList[0].cityName
          this.getCityAddressList(data.data[0].cityList[0])

        } else {
          console.log('1-3',this.initCityName,this.cityLists)
          const item = this.cityLists.find( (i) => i.cityName === this.initCityName)
          if(item){
            console.log('1-4')
            this.active = 1
            this.value = item.cityName
            this.getCityAddressList(item)
            this.$refs.itemChange.toggle(true);
          }else{
            console.log('1-5')
            // this.value = data.data[0].cityList[0].cityName
            // this.getCityAddressList(data.data[0].cityList[0])

            ////开启定位且没有选择过门店
            if(this.initCityName ){
              console.log('1-6')
              this.active = 0
              this.proVinceCurrentIndex = null
              this.value = this.initCityName
              //传入原生定位的cityCode
              this.getCityAddressList({cityCode:this.getLocationCityData.adCode})
              this.$refs.itemChange.toggle(true);
            }else{
              console.log('1-7')
              this.active = 0
              this.proVinceCurrentIndex = null
              //未开启定位且没有选择过门店
              this.getCityAddressList()
              this.$refs.itemChange.toggle(true);
            }

          }
        }
      } else {
        // 选择过经销商回显
        const item = this.cityLists.find((i) => i.cityName === this.cityName);
        console.log(item,"item")
        if(item){
          this.value = item.cityName
          this.getCityAddressList(item)
          this.active = 1
          this.proVinceCurrentIndex = this.getProvinceNameByCityIndex(this.value)
        }

      }

      this.cityNameTab = this.value || '选择城市'
      this.provinceName = this.getProvinceNameByCityName(this.value)?.cityName || '选择省'

      if(this.getProvinceNameByCityName(this.value)?.children){
        this.columns = this.getProvinceNameByCityName(this.value)?.children || []
      }

      if(this.active == 1){
        this.proVinceCurrentIndex =  this.columns.findIndex( r => r.cityName == this.value)
      }

    },
    async getDealerAllList() {
      this.$store.commit('showLoading')
      const { data } = await getAfterSalesListForReservation( {
        latitude : this.location.split(',')[0],
        longitude : this.location.split(',')[1],
        dealerCode : this.bindDealerCode,
      })
      if (data.code === '00') {
        this.addressAllList = data.data
        // 高德api计算两点之间的距离
        // const location = this.location.split(',')

        // const results = await Promise.all(
        //   this.addressAllList.map(async (dealer) => {
        //     if (dealer.latitude && dealer.longitude) {
        //       console.log(
        //         '距离计算 ',
        //         location[1],
        //         location[0],
        //         dealer.longitude,
        //         dealer.latitude
        //       )
        //       return getdiscount2(
        //         location[1],
        //         location[0],
        //         dealer.longitude,
        //         dealer.latitude
        //       )
        //     }
        //     return 0
        //   })
        // )

        // this.addressAllList.forEach((dealer, i) => {
        //   dealer.distance = results[i]
        // })
        // console.log(this.addressAllList)
        // this.addressAllList.sort(this.compare('distance'))
        // this.addressAllList = this.resort(this.addressAllList)
        // console.log(this.addressAllList)
        this.$store.commit('hideLoading')
      }
    },

    getOtherPoint({ latitude, longitude }) {
      if (latitude && longitude) {
        return `${latitude},${longitude}`
      }
      return `${latitude},${longitude}`
    },

    chooseDealer(item) {
      if(item.provideSaicAudiPickUpService === '1' && item.businessStatus === '3'){
        storage.set('dealerCarModel',JSON.stringify(item) )
        this.$router.back(-1)
      } else if (item.provideSaicAudiPickUpService === '1' && item.businessStatus === '6' && (item.policyList?.length && item.policyList.find(e => (e.businessCode === 'BC002' && e.controlStatus === '有')))) {
        storage.set('dealerCarModel',JSON.stringify(item) )
        this.$router.back(-1)
      } else{
         // 调用APP Dialog
        callNative("popup", {
          type: "alert",
          alertparams: {
            title: "",
            desc: "该服务商不在取送车服务范围中，请重新选择。",
            actions: [
              {
                type: "fill",
                title: "确定",
              }
            ],
          },
        }).then((data) => {
          if (data.type === "fill") {}
        });
      }

    },

    compare(prop) {
      return function (obj1, obj2) {
        const val1 = obj1[prop]
        const val2 = obj2[prop]
        if (val1 < val2) {
          return -1
        }
        if (val1 > val2) {
          return 1
        }
        return 0
      }
    },

    resort(addressAllList) {
      const idx = addressAllList.findIndex((item) => item.distance !== 0)
      const truncate = addressAllList.slice(0, idx)
      addressAllList.splice(0, idx)
      return addressAllList.concat(truncate)
    },

    toNavigation(longitude, latitude, dealerName) {
      callNative('navigationMap', {
        lat: latitude.toString(),
        long: longitude.toString(),
        des: dealerName
      })
    },

    // 选择城市
    onConfirm(value) {
      // this.value = value[1]
      this.value = value
      this.showPicker = false

      const item = this.cityLists.find( (i) => i.cityName === this.value)

      this.getCityAddressList(item)
    },

    async getCityAddressList(value) {
      console.log(value,"getCityAddressList")
      this.$store.commit('showLoading')
      let params = {
        cityCode: value ? value.cityCode : '',
        latitude : this.location.split(',')[0],
        longitude : this.location.split(',')[1],
        dealerCode : this.bindDealerCode,
        sortType:this.changeStatusVal,
      }
      //是否选择 过门店
      if(this.dealerModel && this.dealerModel != '{}'){
        params['dealerCodeNewly'] = JSON.parse(this.dealerModel).dealerCode
      }
      const { data } = await getAfterSalesListForReservation(params)
      if (data.code === '00') {
        // 高德api计算两点之间的距离
        // const location = this.location.split(',')

        // const results = await Promise.all(
        //   this.cityAddressList.map(async (dealer) => {
        //     if (dealer.latitude && dealer.longitude) {
        //       console.log(
        //         '距离计算 ',
        //         location[1],
        //         location[0],
        //         dealer.longitude,
        //         dealer.latitude
        //       )
        //       return getdiscount2(
        //         location[1],
        //         location[0],
        //         dealer.longitude,
        //         dealer.latitude
        //       )
        //     }
        //     return 0
        //   })
        // )

        // this.cityAddressList.forEach((dealer, i) => {
        //   dealer.distance = results[i]
        // })
        // console.log(this.cityAddressList)
        // this.cityAddressList.sort(this.compare('distance'))
        // this.cityAddressList = this.resort(this.cityAddressList)
        // console.log(this.cityAddressList)

        if(data.data.length){
          this.notCarList = false
          this.cityAddressList = data.data
          this.mapAddress()
        }else{
           this.notCarList = true
        }
        this.$store.commit('hideLoading')


      }
    },
    // 初始化坐标
    mapAddress() {
      this.map.setFitView()
      this.map.remove(this.markerList);
      const icon = new AMap.Icon({
        // 自定义图标
        size: new AMap.Size(33, 33),
        image: require('../../assets/img/map-index.png'),
        imageSize: new AMap.Size(33, 33),
        anchor: 'center'
      })
      this.markerList = []
      for (let i = 0; i < this.cityAddressList.length; i++) {
        if (this.cityAddressList[i].longitude > 0) {
          const marker1 = new AMap.Marker({
            // 插点
            icon: icon,
            position: [
              this.cityAddressList[i].longitude,
              this.cityAddressList[i].latitude
            ],
            offset: new AMap.Pixel(-13, -30)

          })
          marker1.content = this.cityAddressList[i].dealerName;
          marker1.on('click', this.markerClick);
          this.markerList.push(marker1)
        }
      }

      this.map.add(this.markerList)

      this.map.panTo([this.cityAddressList[0].longitude,
              this.cityAddressList[0].latitude]);
    },
    markerClick(e){
      this.infoWindow.setContent(e.target.content);
      this.infoWindow.open(this.map, e.target.getPosition());
    },
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.notList{
  text-align: center;
  margin-top: 80px;
}

.listStyle{
  height: calc(100% - 10px);
  overflow-y: auto;
}
.cityNameJons{
  height: 300px;
  overflow-y: auto;
  img{
    width: 24px;
    height: 24px;
  }
}
.container {
  width: 100vw;
  overflow: hidden;
  /deep/ .van-tab{
    color: #808080;
  }
  /deep/ .van-tab--active{
    color: #000;
  }
  /deep/ .van-dropdown-item__content{
      min-height: 100%;
  }
  .rightImgs{
    width: 24px;
    height: 24px;
  }
}
.activeDrop{
  /deep/ .van-dropdown-item__content{
      min-height: auto;
  }
}
.title_img {
  width: 20px;
  height: 20px;
}
.flex1 {
  flex: 1;
}
.transparent {
  background-color: transparent;
}
.item-wrapper {
  .c-flex-between;
  // border-bottom: 1px solid #e5e5e5;
  padding: 16px;
}

.phone-icon {
  width: 12px;
  vertical-align: baseline;
}

.img-wrapper {
  width: 80px;
  height: 80px;
}
.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  margin-left: 15px;
  padding: 5px 0;
}
.navgation-wrapper {
  .c-font14;
  .c-flex-center;
  color: #b2b2b2;
  text-align: center;
  .nav-icon {
    width: 24px;
    margin: 0 auto 5px auto;
  }
}
.item_bottom {
    display: flex;
    margin-left: 114px;

    .item_btn {
        border: 1px solid #999999;
        text-align: center;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 12px;
      }
  }

.line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  .title {
    font-size: 16px;
    color: #000;
    margin: 5px 5px;
  }
  .btn-change {
    display: flex;
    align-items: center;
    .change-name {
      font-size: 16px;
      color: #000;
      margin-right: 10px;
    }
    .btn-icon {
      align-items: center;
      font-size: 12px;
      color: #a3a3a3;
    }
  }
}
</style>
