<template>
  <div class="container">
    <div class="title-bold">
      {{ orderInfo.statusName }}
    </div>
    <div
      v-if="[0, 1, 2, 3].includes(orderInfo.status)"
      class="c-font12"
      style="color: #333; font-size: 14px; margin-top: 8px"
    >
      {{ statusText }}
    </div>
    <div class="store-wrap" style="margin-top: 16px">
      <div
        class="item-store"
        style="margin-top: 16px"
      >
        <div>
          <img
            class="img-wrapper"
            :src="
              ((orderInfo.dealerUrl || '').includes('http')
                  ? orderInfo.dealerUrl
                  : ossUrl + orderInfo.dealerUrl) | audiwebp
            "
            alt=""
          >
        </div>
        <div class="content-wrapper flex1">
          <div class="c-font14 c-bold">
            {{ orderInfo.dealerName }}
          </div>
          <div style="margin-top: 4px" />
          <div
            class="c-font14"
            style="color: #666; font-size: 14px"
          >
            {{ orderInfo.dealerAddress }}
          </div>
        </div>

      </div>
      <div class="link-wrapper">
        <div class="line"  @click="callPhone">
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""

          >
          <span>联系</span>
        </div>
        <div  @click="handleNavigationSoftware(orderInfo.latitude, orderInfo.longitude,orderInfo.dealerAddress)">
          <img
            class="nav-icon"
            :src="require('@/assets/img/location.png')"
            alt=""

          >
          <span>导航</span>
        </div>
      </div>
    </div>
    <div
      class="title-bold"
      style="margin-top: 16px"
    >
      预约时间
    </div>
    <div style="font-size: 14px; margin-top: 8px; margin-bottom: 16px">
      {{ orderInfo.appointmentTime }}
    </div>
    <div class="linediv" />
    <div
      class="title-bold"
      style="margin-top: 24px"
    >
      服务项目
    </div>

    <div
      class="item-wrapper"
      style="margin-top: 6px"
      v-for="(item, idx) in packageList"
      :key="idx"
    >
      <div class="content-wrapper flex1">
        <div class="c-font16">
          {{ item.packageName }}
        </div>
      </div>
      <div
        class="navgation-wrapper"
        style="color: #000; font-size: 16px"
      >
        <div class="c-font16">
          {{ "￥" +(item.saleAmount ).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
        </div>
      </div>
    </div>
    <div class="linediv" />
    <div
      class="title-bold"
      style="color: #000; font-size: 14px; display:flex;justify-content:right;align-items:center;"
    >
      预估价格:{{
        "￥" +
          (orderInfo.price || '0' ).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
      }}
    </div>
    <div class="price-tips">服务项目及价格仅供参考，实际价格请以服务商报价为准</div>
    <div
      class="title-bold"
      style="margin-top: 24px"
    >
      订单信息
    </div>

    <div
      class="item-wrapper"
      style="margin-top: 12px"
    >
      <div
        class="content-wrapper flex1"
        style="color: #333; font-size: 12px"
      >
        服务类型
      </div>
      <div
        class="navgation-wrapper"
        style="color: #000; font-size: 12px"
      >
        {{ setServiceTypeDesc(orderInfo.serviceType) }}
      </div>
    </div>
    <div
      class="item-wrapper"
      style="font-size: 14px; padding-top: 0px"
    >
      <div
        class="content-wrapper flex1"
        style="color: #333; font-size: 12px"
      >
        下单渠道
      </div>
      <div
        class="navgation-wrapper"
        style="color: #000; font-size: 12px"
      >
        {{ CHANNELS[orderInfo.channelId] }}
      </div>
    </div>
    <div
      class="item-wrapper"
      style="font-size: 14px; padding-top: 0px"
    >
      <div
        class="content-wrapper flex1"
        style="color: #333; font-size: 12px"
      >
        订单号
      </div>
      <div
        class="navgation-wrapper"
        style="color: #000; font-size: 12px"
      >
        {{ orderInfo.appoId }}
      </div>
    </div>
    <div
      class="item-wrapper"
      style="font-size: 14px; padding-top: 0px"
    >
      <div
        class="content-wrapper flex1"
        style="color: #333; font-size: 12px"
      >
        下单时间
      </div>
      <div
        class="navgation-wrapper"
        style="color: #000; font-size: 12px"
      >
        {{ orderInfo.createdAt }}
      </div>
    </div>
    <div class="linediv" />
    <div
      class="title-bold"
      style="margin-top: 8px"
    >
      个人信息
    </div>

    <div
      class="item-wrapper"
      style="margin-top: 12px"
    >
      <div
        class="content-wrapper flex1"
        style="color: #333; font-size: 12px"
      >
        姓名
      </div>
      <div
        class="navgation-wrapper"
        style="color: #000; font-size: 12px"
      >
        {{ orderInfo.driveName }}
      </div>
    </div>
    <div
      class="item-wrapper"
      style="font-size: 14px; padding-top: 0px"
    >
      <div
        class="content-wrapper flex1"
        style="color: #333; font-size: 12px"
      >
        手机号
      </div>
      <div
        class="navgation-wrapper"
        style="color: #000; font-size: 12px"
      >
        {{ orderInfo.driveTel | phoneNum}}
      </div>
    </div>
    <div class="linediv" />

    <div
      v-if="orderInfo.isCancel === 1"
      class="title-under-line"
      style="margin-top: 8px"
      @click="onCancelOrder"
    >
      取消订单
    </div>
    <div class="inbox" v-if="orderInfo.isCancel === 1"></div>
    <div class="btn-delete-height" />
    <div
      class="bottom_style"
      v-if="orderInfo.isCancel === 1"
    >
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onModify"
          :text="btnText"
          color="white"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getAfterSaleDetail, getAfterSaleCancel } from '@/api/api'
import baseUrl from '@/config/url'
import { callNative } from '@/utils'
import AudiButton from '@/components/audi-button'

export default {
  components: {
    AudiButton
  },
  data() {
    return {
      ossUrl: baseUrl.BaseOssHost,
      packageList: [],
      orderInfo: {},
      btnText: '修改信息',
      statusText: '待服务商确认，如有疑问可联系服务商。',
      // 1、奥迪官网：2、奥迪APP 3：上汽奥迪小程序 4:运营侧-vipCall
      CHANNELS: {
        "1": "奥迪官网",
        "2": "APP",
        "4": "黑金会员助理"
      }
    }
  },

  async mounted() {
    const { appoId } = this.$route.query

    this.getAfterSaleDetail(appoId)
  },

  methods: {
    async getAfterSaleDetail(appoId) {
      const { data } = await getAfterSaleDetail({ appoId: appoId, type: 1 })

      if (data.code === '200') {
        this.orderInfo = data.data
        this.packageList = data.data.packageList
        if (this.orderInfo.status == 2) {
          this.btnText = '重新预约'
          this.statusText = '您的预约已取消，期待下次为您提供服务。'
        }
        if (this.orderInfo.status == 3) {
          this.btnText = '修改信息'
          this.statusText = '您的车辆已到店，如有疑问可联系渠道商。'
        }
        if (this.orderInfo.status == 1) {
          this.btnText = '修改信息'
          this.statusText = '预约单已生成，期待您准时光临。'
        }
        if (this.orderInfo.status == 0) {
          this.btnText = '修改信息'
          this.statusText = '待服务人员确认，如有疑问可联系渠道商。'
        }
      }
    },
    totalAmount() {
      let total = 0
      for (let i = 0; i < this.packageList.length; i++) {
        total += parseInt(this.packageList[i].partSaleAmount)
      }
      return total
    },
    // 订单状态
    setStatusDesc(status) {
      if (status === 1) {
        return '订单已提交'
      }
      if (status === 2) {
        return '订单已取消'
      }
      if (status === 3) {
        return '订单已完成'
      }
    },
    // 订单状态
    setServiceTypeDesc(serviceType) {
      if (serviceType === 1) {
        return '保养'
      }
      if (serviceType === 2) {
        return '维修'
      }
      if (serviceType === 3) {
        return '检查'
      }
      if (serviceType === 4) {
        return '其他'
      }
    },

    callPhone() {
      window.location.href = `tel:${this.orderInfo.dealerPhone}`
    },
    // 取消订单
    async onCancelOrder() {
      // 调用APP Dialog
      callNative('popup', {
        type: 'alert',
        alertparams: {
          title: '',
          desc: '是否确定取消订单？',
          actions: [
            {
              type: 'fill',
              title: '确定'
            },
            {
              type: 'stroke',
              title: '取消'
            }
          ]
        }
      }).then((data) => {
        if (data.type === 'fill') {
          // 点击确定
          this.getCancelOrder()
        }
      })
      // const { data } = await getAfterSaleCancel({ appoId: this.orderInfo.appoId })
      // this.getAfterSaleDetail(this.orderInfo.appoId)
    },
    async getCancelOrder() {
      const { data } = await getAfterSaleCancel({ orderId: this.orderInfo.appoId,version:'v2' })

      this.getAfterSaleDetail(this.orderInfo.appoId)
    },
    // 修改\\重新预约
    onModify() {
      if (this.orderInfo.status === 2) {
        // 重新预约
        this.$router.push({
          path: '/aftersales/service-appointment-one',
          query: {}
        })
      } else {
        // 修改信息
        this.$router.push({
          path: '/aftersales/service-appointment-one',
          query: { appoId: this.orderInfo.appoId }
        })
      }
    },
    handleNavigationSoftware(latitude, longitude, dealerAdrress) {
      console.log('%c [ handleNavigationSoftware ]-2450', 'font-size:14px; background:#cf222e; color:#fff;', latitude, longitude, dealerAdrress)
      if ([latitude, longitude, dealerAdrress].every((i) => i)) {
        console.log("1111")
        callNative('navigationMap', {
          lat: latitude.toString(),
          long: longitude.toString(),
          des: dealerAdrress
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import url("../../assets/style/scroll.less");

@import "../../assets/style/common.less";
@import url("../../assets/style/buttons.less");

.container {
  padding: 16px;
}
.linediv {
  margin-top: 16px;
  margin-bottom: 16px;
  width: 100%;
  height: 1px;
  background-color: #e5e5e5;
}
.flex1 {
  flex: 1;
}
.title-bold {
  font-size: 16px;
  color: #000;
  font-weight: normal;
  font-family: "Audi-WideBold";
}
.title-under-line {
  font-size: 16px;
  color: #000;
  font-weight: normal;
  text-decoration: underline;
  font-family: "Audi-Normal";
}

.item-store {
  .c-flex-between;

}
.store-wrap{
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
}
.item-wrapper {
  .c-flex-between;
}

.phone-icon {
  width: 12px;
  vertical-align: baseline;
}

.img-wrapper {
  width: 60px;
  height: 60px;
  margin-right: 16px;
  display: flex;
}
.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  padding: 5px 0;
}
.navgation-wrapper {
  .c-font14;
  .c-flex-center;
  color: #b2b2b2;
  text-align: center;
  .nav-icon {
    width: 24px;
    height: 24px;
    margin: 0 auto 5px auto;
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
}
.inbox{
  width: 100%;
  height: 84px;
}
.btn-delete-wrapper {
  margin: 0 16px;
}

.btn-delete-height {
  height: 80px;
}
.price-tips{
  font-size: 12px;
  color: #999999;
  margin-top: 8px;
  text-align:right;
}
.link-wrapper{
  display: flex;
  align-items: center;
  margin: 22px 0;
  >div{
    flex:1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    img{
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
  }
  .line{
    border-right: 1px solid #999;
  }
}
</style>
