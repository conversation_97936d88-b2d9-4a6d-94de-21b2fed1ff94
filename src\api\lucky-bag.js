/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-04-12 14:49:07
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-04-16 14:57:45
 * @FilePath     : \src\api\lucky-bag.js
 * @Descripttion : 福袋
 */
import request from '@/router/axios'
import api from '@/config/url'
import url from '@/api/url'

// 福袋列表信息
export const getLuckyBagList = () => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedBag/findList`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 福袋信息 - 列表（福包）
export const getLuckyBagInfo = (params) => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedPack/findListByBlessedBagId`,
  method: 'GET',
  params,
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 福袋状态
export const getLuckyBagStatus = (params) => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedBag/checkStatus`,
  method: 'GET',
  params,
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 福袋基础信息
export const getLuckyBagIdInfo = (params) => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedBag/findBlessedBagById`,
  method: 'GET',
  params,
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 福袋开启
export const getLuckyBagOpen = (params) => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedPack/blessedPackOpen`,
  method: 'GET',
  params,
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 福袋 获取配置单
export const getLuckyBagConfigList = (params) => request({
  url: `${api.BaseApiUrl}${url.getConfigTable}`,
  method: 'GET',
  params,
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 福袋 关联车型库存
export const getLuckyBagModelStock = (params) => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedPack/queryBlessedPackReserve`,
  method: 'GET',
  params,
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 查询当前用户的福袋商品ID
export const getLuckyBagSkuId = () => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedBag/findBlessedBagProductIdByUserId`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 查询当前用户的福袋商品ID
export const confirmCcid = (params) => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedPack/confirmCcid`,
  method: 'GET',
  params,
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

// 查询当前用户的福袋
export const getUserLuckyBagInfo = () => request({
  url: `${api.BaseApiUrl}/api-wap/audi-marketing-tools/api/blessedBag/findBlessedBagByUserId`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})
