<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-09-05 17:16:23
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2024-01-12 13:52:59
 * @FilePath     : \src\view\rights\sales-card-voucher\index.vue
 * @Descripttion :
-->
<script src="./index"></script>
<template>
  <div
    :class="
      ['limited-number-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <div class="pop-coupons-box">
        <div class="pop-coupons-main">
          <template v-if="equityList && equityList.length">
            <div class="coupons">
              <van-cell
                class="list"
                v-for="list in equityList"
                :key="list.couponEquityId"
              >
                <template #title>
                  <div
                    class="coupon-box"
                    data-flex="main:justify"
                    data-block
                  >
                    <div
                      class="coupon-media"
                    >
                      <div
                        class="media-box"
                        data-flex="main:center"
                      >
                        <img
                          class="img"
                          :src="list.couponImgUrl"
                          :alt="list.couponName"
                        >
                      </div>
                    </div>
                    <div
                      class="coupon-info"
                      data-block
                    >
                      <h4 class="h4 text-two-hidd">
                        {{ list.couponName }}
                      </h4>
                      <p class="text-price">
                        <span>金额：</span>{{ list.deductOff | prefixFormatPrice('') }}
                      </p>
                      <p><span>有效期至： {{ list.effectiveEndDate | dayjsFilter('YYYY.MM.DD') }}</span></p>
                    </div>
                    <div
                      class="coupon-status van-hairline--left"
                      data-flex="cross:center main:center"
                    >
                      {{ couponStatus[['10', '90', '96'].includes(list.status) ? list.status : '000'] || '' }}
                    </div>
                  </div>
                </template>
              </van-cell>
              <div
                class="exception-tips"
                v-if="exceptionTips === 1"
              >
                上述卡券暂只支持叠加使用，由于存在失效卡券导致卡券不可用，请联系专属奥迪管家咨询卡券资格。
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
