<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-02-13 15:00:52
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-02-20 18:55:07
 * @FilePath     : \src\components\rights.q5e.lite.prevent.vue
 * @Descripttion :
-->
<template>
  <van-popup
    v-model="rightsPreventPop"
    class="popup-custom"
    :close-on-click-overlay="false"
  >
    <div class="popup-custom-box">
      <div class="popup-custom-main">
        <div class="text align-center">
          <div class="list">
            您还未选择充电权益，请选择权益后再进行交车确认
          </div>
        </div>
      </div>
      <div
        class="popup-custom-btn"
        data-flex="main:justify"
      >
        <div
          class="lan-button-box white-button line-two-cols"
        >
          <van-button
            :disabled="jumpLoading"
            @click="handleCancelJump"
            class="lan-button"
          >
            取消
          </van-button>
        </div>
        <div class="lan-button-box black-button line-two-cols">
          <van-button
            @click="handleConfirmJump"
            loading-text="正在跳转 ..."
            :loading="jumpLoading"
            class="lan-button"
          >
            选择权益
          </van-button>
        </div>
      </div>
    </div>
  </van-popup>
</template>
<script>
import Vue from 'vue'
import { Toast, Popup, Button } from 'vant'
import { delay } from '@/utils/index'

Vue.use(Toast).use(Popup).use(Button)

export default {
  props: {
    orderId: {
      type: String,
      default: ''
    },
    rightsPrevent: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    rightsPrevent(val) {
      this.rightsPreventPop = val
    }
  },
  data() {
    return {
      jumpLoading: false,
      rightsPreventPop: false
    }
  },
  methods: {
    handleCancelJump() {
      this.$emit('update:rights-prevent', false)
    },
    handleConfirmJump() {
      this.jumpLoading = true
      const { orderId } = this
      delay(() => {
        this.handleCancelJump()
        this.$router.push({
          path: '/charging/select-equities',
          query: { orderId, skukw: 'lite' }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
</style>
