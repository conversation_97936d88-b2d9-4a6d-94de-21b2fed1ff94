<template>
  <div class="attestation">
    <!-- <van-tabs
      style="width: 100%; position: fixed; left: 0; z-index: 90"
      color="#000"
      line-width="70px"
      line-height="2"
      title-active-color="#000"
      v-model="active"
      @click="changeTabs"
    >
      <van-tab v-for="(t, i) in arr" :title="t" :key="i"> </van-tab>
    </van-tabs> -->
    <div class="attestation_connent">
      <div class="attestation_tab"  v-for="(t, i) in arr":key="i" @click="changeTabs(i)">
        <div class="attestation_tab-name" :class="i===active?'attestation_tab_on':''">
          {{t}}
        </div>
        
      </div>
    </div>
  </div>
</template>
<script>
import storage from "../../../utils/storage";

export default {
  props: {
    arr: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      active: 0,
    };
  },
  watch: {
    active(n, o) {
      console.log(n, o);
      return n
    },
  },
  mounted() {
    this.active = storage.getPlus('certificationType') || 0
  },
  methods: {
    changeTabs(e) {
      this.active = e;
      this.$emit("changeTabs", e);
    },
  },
};
</script>

<style scoped lang="less">
.attestation{
  width: 100vw;
  height: 40px;
  border-bottom: 1px solid #E5E5E5;
  display: flex;
  justify-content: space-around;
  .attestation_connent{
  display: flex;
  width: 100vw;
  margin: 0 16px;

    .attestation_tab{
      // margin: 0 26.5px;
      width: 33%;
      display: flex;
      justify-content: center;
      padding-bottom:2px;
      .attestation_tab-name{
        color: #999999;
        line-height: 24px;
        font-size: 16px;
        margin-top: 8px;
        height: 24px;
        font-family: 'Audi-Normal';
        text-align: center;
      }
       .attestation_tab_on{
      border-bottom: 2px solid #000000;
      color: #000000;
      font-family: 'Audi-WideBold';
      }
    }
   
  }
  
}
</style>