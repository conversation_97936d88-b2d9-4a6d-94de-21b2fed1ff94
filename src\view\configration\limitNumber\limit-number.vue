<template>
  <div class="limit-number">
    <img
      class="img1"
      :src="limitnumberimage | imgFix(640, true)"
    >
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  computed: mapState({
    limitnumberimage: (state) => state.configration.limitNumberData[0]?.img
  })
}
</script>

<style lang="less" scoped>
.limit-number {
  height: calc(100vh - 190px);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  .img1 {
    width: 100vw;
    height: auto;
    display: block;
  }

}
</style>
