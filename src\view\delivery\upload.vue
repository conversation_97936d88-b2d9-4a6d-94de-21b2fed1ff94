<template>
  <div class="container">
    <div class="upload-documents box">
      <van-field
        v-model="remark"
        autosize
        type="textarea"
        rows="5"
        placeholder="补充"
      />
      <van-uploader
        v-model="fileList"
        :after-read="afterRead"
        :show-upload="fileList.length <= maxUploadCount"
        :max-count="maxUploadCount"
        multiple
        accept="image/*,.pdf"
      />
      <div class="btn-wrapper">
        <AudiButton
          text="确认上传"
          color="black"
          height="56px"
          font-size="16px"
          @click="submitUpload"
        />
      </div>
    </div>
  </div>
</template>

<script>

import { Field, Toast, Uploader } from 'vant'
import Vue from 'vue'
import { uploadContractFile } from '@/api/api'
import api from '@/config/url'
import AudiButton from '@/components/audi-button'
import { getSubmitToken } from '@/api/detail'
import { compressFileImg } from '@/utils'

Vue.use(Toast).use(Field).use(Uploader)

export default {
  components: { AudiButton },
  data() {
    return {
      remark: '',
      fileList: [],
      compressFileList: [],
      maxUploadCount: 5,
      BaseOssHost: api.BaseOssHost,
      BaseApiUrl: api.BaseApiUrl
    }
  },
  watch: {
    async fileList(file) {
      // compress file
      this.compressFileList = await Promise.all(file.map((i) => compressFileImg(i)))
    }
  },
  mounted() {
    // this.$store.commit('showLoading')
  },
  methods: {
    async afterRead(file) {
      // afterread
    },

    // 上传
    async submitUpload() {
      const { contractId } = this.$route.query
      if (this.compressFileList.length > 0) {
        this.compressFileList.map((i) => console.log(i))

        const params = {
          uploadFile: this.compressFileList.map((i) => i)
        }

        // console.log(params)
        this.$store.commit('showLoading')
        const formData = new FormData()
        for (const i in params) {
          if (Array.isArray(params[i])) {
            for (let idx = 0; idx < params[i].length; idx++) {
              formData.append('uploadFile', params[i][idx])
            }
          } else {
            formData.append(i, params[i])
          }
        }
        const res = await getSubmitToken()
        const submitToken = res.data.data
        const { data } = await uploadContractFile(formData, submitToken, {
          contractId: contractId,
          remark: this.remark
        })
        console.log(data)
        this.$store.commit('hideLoading')

        if (data.code === 200 || data.code === '00') {
          Toast({
            type: 'success',
            message: '上传成功',
            icon: require('@/assets/img/success.png')
          })

          setTimeout(() => {
            this.toOrderDetail()
          }, 800)
        }
      } else {
        Toast({
          type: 'fail',
          message: '请先上传凭证',
          icon: require('../../assets/img/error.png')
        })
      }
    },

    toOrderDetail() {
      const { orderId, orderPageType } = this.$route.query
      this.$router.push({
        path:'/order/new-money-detail',
        query: {
          orderId
        }
      })
    }

  }
}
</script>

<style lang="less" scoped>


.container {
  position: relative;
  height:100%;
}
.van-uploader{
  margin-left: 16px;

}
.btn-wrapper {
  position: absolute;
  bottom: 50px;
  left: 0;
  right: 0;
  padding: 0 16px;
}
/deep/ .van-uploader__upload {
  border: 1px dashed #e5e5e5;
  border-radius: 5px;
}

/deep/ .van-cell::after  {
  border-bottom: none;
}

</style>
