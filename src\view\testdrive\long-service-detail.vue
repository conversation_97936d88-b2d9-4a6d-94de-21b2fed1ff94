<!-- 预约试驾服务订单详情 -->
<template>
  <div name="detail" class="detail-container">
    <div class="_head">
      <div class="_title">
        {{ status[data.status] }}
      </div>
      <div class="_content">
        {{ statusContent[data.status] }}
      </div>
    </div>

    <div class="_box">
      <div class="_list">
        <div class="_li" style="margin-top: 13px">
          <div
            style="font-size: 16px; font-family: Audi-WideBold; color: #000000"
          >
            订单详情
          </div>
          <div style="display: flex" @click="isSatusUp = !isSatusUp">
            <div style="font-size: 14px">{{ isSatusUp ? "收起" : "展开" }}</div>
            <van-icon
              style="color: #9e1f32"
              :name="isSatusUp ? 'arrow-down' : 'arrow'"
            />
          </div>
        </div>
      </div>
      <div class="interval_line2"></div>
      <div class="_list">
        <div class="_li">
          <div>试驾车系</div>
          <div style="color: #666">
            {{ data.seriesDto ? data.seriesDto.customSeriesName : "" }}
          </div>
        </div>
        <div class="_li">
          <div>试驾时间</div>
          <div style="color: #666">
            {{ data.appoBeginTime.substring(5, 7).replace(/\-/g, ".")+"月"+data.appoBeginTime.substring(8, 10).replace(/\-/g, ".")+"日" }}-{{
              data.appoEndTime.substring(5, 7).replace(/\-/g, ".")+"月"+data.appoEndTime.substring(8, 10).replace(/\-/g, ".")+"日"
            }}
          </div>
        </div>
        <div class="_li" v-if="isSatusUp">
          <div>到店时间</div>
          <div style="color: #666">
            {{ data.appoBeginTime.substring(11, 16).replace(/\-/g, ".") }}
          </div>
        </div>
        <div class="_li" v-if="isSatusUp">
          <div>试驾城市</div>
          <div style="color: #666">
            {{ data.dealerDto.provinceName + data.dealerDto.cityName }}
          </div>
        </div>

         <div class="_li" v-if="isSatusUp">
          <div>试驾单号</div>
          <div style="color: #666">
            {{ data.testDriveId }}
          </div>
        </div>
        <div class="_li" v-if="isSatusUp">
          <div>下单时间</div>
          <div style="color: #666">
            {{ data.createTime }}
          </div>
        </div>
        <div class="_li" v-if="isSatusUp">
          <div>支付时间</div>
          <div style="color: #666">
            {{ data.payTime }}
          </div>
        </div>
        <div class="_li" v-if="isSatusUp">
          <div>支付金额</div>
          <div style="color: #666">
            {{'￥ '+ data.payAmount }}
          </div>
        </div>
      </div>
      <div class="interval_line" v-if="isSatusUp"></div>

      <div class="_list" v-if="isSatusUp">
        <div class="_li" style="margin-top: 13px">
          <div>姓名</div>
          <div>{{ data.approveName }}</div>
        </div>
        <div class="_li">
          <div>联系方式</div>
          <div>{{ data.potMobile }}</div>
        </div>
      </div>
      <div class="interval_line" v-if="isSatusUp"></div>
      <div class="_list" v-if="isSatusUp">
        <div class="_li" style="margin-top: 13px">
          <div>代理商</div>
          <div>{{ data.dealerDto.dealerName }}</div>
        </div>
      </div>

      <div class="_agent" v-if="isSatusUp">
        <agentCard :agent-card-data="agentCardData" :location="location" />
      </div>

      <div class="interval_line"></div>

      <van-steps
        direction="vertical"
        :active="0"
        active-color="#000"
        style="margin-top: 13px"
      >
        <van-step v-for="(item, idx) in data.statusHisList" :key="idx">
          <div style="display: flex; justify-content: space-between">
            <div style="font-size: 16px">{{ item.name }}</div>
            <div style="font-size: 14px" v-if="item.files" @click="onStatusHis(item,idx)">查看 ></div>
          </div>

          <div style="color: #999; font-size: 12px; padding-top: 10px">
            {{ item.dateTime }}
          </div>
        </van-step>
      </van-steps>

      <div
        class="_cancel-test"
        @click="goInvoiceUrl"
        v-if="data.invoiceView"
      >
        开发票
      </div>

    </div>
      <div class="btn-delete-height"></div>
       <div class="bottom_style" >

          <div class="btn-delete-wrapper" v-if="data.earlyEndView">
            <AudiButton
              @click="onEarlyEndView"
              :text="'提前结束试驾'"
              color="black"
              font-size="16px"
              height="50px"
            />
          </div>
          
        </div>

  </div>
</template>

<script>
import Vue from "vue";
import { Icon, Step, Steps } from "vant";
import agentCard from "@/components/agent-card.vue";
import model from "@/components/model.vue";
import {
  getVeryLongReservationEarlyEnd,
  getVeryLongReservationTestDriveDetailByAppoid,
  getVeryLongReservationUrl
} from "../../api/test-driver";
import { callNative } from "@/utils";
import AudiButton from "@/components/audi-button";
import storage from "../../utils/storage";

Vue.use(Icon).use(Step).use(Steps);
export default {
  components: { agentCard, model,AudiButton },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      id: "",
      data: {},
      status: {
        90210: "订单待开始",
        90220: "订单进行中",
        90230: "订单已完成",
        90240: "订单已取消",
        90241: "订单已取消",
        90211: "订单待支付",
      },
      statusContent: {
        90210: "",
        90220: "超长试驾进行中，如有任何问题，您可以与您的体验专家直接沟通，我们将竭诚为您提供服务。",
        90230: "超长试驾已完成，您可以前往评价界面对本次超长试驾活动做出评价，感谢您的支持。",
        90240: "超长试驾已取消，如果您已支付押金，预计在3个工作日内退还，感谢您的支持。",
        90241: "超长试驾已取消，如果您已支付押金，预计在3个工作日内退还，感谢您的支持。",
        90211: "您的试驾预约待支付",
      },
      modalshow: false,
      agentCardData: {},
      confirm: false,
      location: "31.230525,121.473667", // 当前的经纬度
      isSatusUp: false,
    };
  },
  mounted() {
    this.id = this.$route.query.appoId;
    this.getdata();
    this.$bridge.callHandler("getLocationCity", {}, (err, data) => {
      if (err) {
        // 发生错误
        console.log(err);
        return;
      }
      if (data.location) {
        this.location = data.location;
      }
    });
  },
  methods: {
    async getdata() {
      this.$store.commit("showLoading");
      await getVeryLongReservationTestDriveDetailByAppoid({
        appoId: this.id,
      }).then((res) => {
        console.log("res", res);
        if (res.status === 200 && res.data.code === "200") {
          this.data = res.data.data;
          this.agentCardData = this.data.dealerDto;
          this.confirm = !!this.data.custIdCard;
        }
        this.$store.commit("hideLoading");
      });
    },
    //开发票页面
    async goInvoiceUrl(){
      //
      this.$store.commit("showLoading");
      await getVeryLongReservationUrl({
        appoId: this.id,
        businessType:2,
        }).then((res) => {
          if (res.data.code === "200") {
            window.location.href = res.data.data
          }
        this.$store.commit("hideLoading");
      });
    },

    onStatusHis(item,idx){
      console.log('idx++++',idx)
      if(item.downloaded === 1){
        storage.set("saveLongItem", JSON.stringify(item));
        this.$router.push({
              path: this.fromType ? '/testdrive/check-contract?fromType=fromPurple' : '/testdrive/check-contract',
              query: {
                appoId: this.id
              }
        })
      }else{
        storage.set("saveLongItem", JSON.stringify(item));
        this.$router.push({
              path: this.fromType ? '/testdrive/check-img?fromType=fromPurple' : '/testdrive/check-img',
              query: {
                appoId: this.id
              }
        })
        
      }
      
      
    },
    //结束试驾
    onEarlyEndView(){
         // 调用APP Dialog
        callNative("popup", {
          type: "alert",
          alertparams: {
            title: "",
            desc: "是否确定提前结束试驾？",
            actions: [
              {
                type: "fill",
                title: "确定",
              },
              {
                type: "stroke",
                title: "取消",
              },
            ],
          },
        }).then((data) => {
          if (data.type === "fill") {
            // 点击确定
            this.getVeryLongReservationEarlyEnd();
          }
        });
        
    },
    async getVeryLongReservationEarlyEnd(){
        this.$store.commit("showLoading");
      await getVeryLongReservationEarlyEnd({
        appoId: this.id,
      }).then((res) => {
        console.log("res", res);
        if (res.data.code === "200") {
          this.getdata()
        }
        this.$store.commit("hideLoading");
      });
    },

     
  },
};
</script>

<style lang='less' scoped>
@import url("../../assets/style/buttons.less");

div {
  box-sizing: border-box;
}

.detail-container {
  display: flex;
  flex-flow: column;
  width: 100%;
  margin: 0;
  padding: 0;
  border-top: 1px #f2f2f2 solid;
  color: #000000;
  padding-bottom: 50px;
  overflow: hidden;

  ._head {
    width: 100%;
    display: flex;
    flex-flow: column;
    box-sizing: border-box;
    border-bottom: 1px #f2f2f2 solid;
    background: #f2f2f2;
    padding: 14px 16px;

    ._title {
      font-size: 16px;
      font-family: Audi-WideBold;
      color: #000000;
      line-height: 24px;
    }

    ._content {
      font-size: 14px;
      font-weight: 400;
      color: #000000;
      line-height: 20px;
      margin-top: 7px;
    }
  }

  ._box {
    padding: 0 16px 56px 16px;

    ._agent {
      padding-top: 24px;
      padding-bottom: 8px;
      width: 100%;
    }
    ._way {
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 16px 0;
      border-bottom: 1px #f2f2f2 solid;
      padding: 0 0 20px 0;
    }
    ._list {
      width: 100%;
      display: flex;
      flex-flow: column;

      ._title {
        width: 100%;
        font-size: 16px;
        font-family: Audi-WideBold;
        color: #000000;
        line-height: 24px;
      }

      ._time {
        font-size: 14px;
        color: #000000;
        line-height: 18px;
        margin-top: 7px;
      }

      ._li {
        height: 56px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        div {
          font-size: 16px;
          color: #333333;
          line-height: 18px;
        }
      }
    }

    .interval_line {
      margin-left: -16px;
      margin-right: -16px;
      height: 8px;
      background: #f2f2f2;
    }
    .interval_line2 {
      margin-left: -16px;
      margin-right: -16px;
      height: 1px;
      background: #f2f2f2;
    }
    ._cancel-test {
      width: 80px;
      // padding: 16px 0;
      height: 30px;
      margin: 16px 0;
      font-size: 12px;
      color: #333;
      text-align: center;
      border: 1px solid #333;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      float: right;
    }



  }
}
 .btn-delete-height {
      height: 90px;
    }
    .btn-delete-wrapper {
      margin: 16px;
    }
    .bottom_style {
      width: 100%;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: absolute;
      bottom: 0px;
      // padding: 16px;
      left: 0px;
    } 
</style>
