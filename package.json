{"name": "vant-demo-vue2", "version": "1.0.0", "description": "Collection of vant demos.", "author": "neverland <chenji<PERSON>@neverl.com>", "license": "MIT", "scripts": {"serve": "vue-cli-service serve --mode development", "serve-qa": "vue-cli-service serve --mode dev-qa", "serve-pre": "vue-cli-service serve --mode dev-pre", "dev": "vue-cli-service serve --mode dev-pre-mock", "build": "vue-cli-service build --mode production", "lint": "eslint --fix --ext .js,.vue src", "build-dev": "vue-cli-service build --mode dev", "build-pre": "vue-cli-service build --mode pre", "build-qa": "vue-cli-service build --mode dev-qa", "build-prod": "vue-cli-service build --mode production", "build-prod4.0": "vue-cli-service build --mode prod4", "build-prod5.0": "vue-cli-service build --mode prod5", "build-prod6.0": "vue-cli-service build --mode prod6"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.14.5", "@babel/plugin-proposal-optional-chaining": "^7.14.5", "@vant/area-data": "^1.1.1", "axios": "^0.21.1", "jsencrypt": "^3.3.2", "clipboard": "^2.0.8", "core-js": "^3.4.3", "dayjs": "^1.10.6", "dom-to-image": "^2.6.0", "html2canvas": "^1.3.2", "js-base64": "^3.7.1", "js-cookie": "^2.2.1", "js-pinyin": "^0.1.9", "lottie-web": "^5.9.2", "nprogress": "^0.2.0", "pdfh5": "^1.4.2", "qrcodejs2": "0.0.2", "sa-sdk-javascript": "1.23.5", "swiper": "^5.3.6", "v-viewer": "^1.6.4", "vant": "^2.2.0", "viewerjs": "^1.10.2", "vue": "^2.7.0", "vue-amap": "^0.5.10", "vue-lottie": "^0.2.1", "vue-pdf": "^4.3.0", "vue-persistedstate": "^1.2.5", "vue-router": "^3.0.7", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@babel/eslint-parser": "^7.21.3", "@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-service": "^4.5.19", "@vue/eslint-config-airbnb": "^5.3.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.12.0", "babel-preset-es2015": "^6.24.1", "element-ui": "^2.15.6", "eslint": "7.5.0", "eslint-plugin-vue": "^9.10.0", "html-image-compress": "^1.1.0", "husky": "^7.0.1", "less": "^3.13.1", "less-loader": "^5.0.0", "lint-staged": "^11.1.2", "mockjs": "^1.1.0", "vconsole": "^3.15.0", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "@babel/eslint-parser"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["Android >= 4.0", "iOS >= 8"], "lint-staged": {"src/**/*.js": ["eslint --fix", "git add"]}, "volta": {"node": "16.15.1", "yarn": "1.22.19"}}