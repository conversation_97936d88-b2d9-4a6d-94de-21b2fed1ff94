/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-19 16:03:47
 * @Last Modified by: mikey.zhaopeng
 * @Last Modified time: 2024-03-Th 11:04:23
 * 接口列表
 */

export default {
  //
  getDealerListByTestDriveUrl:
    '/api-wap/audi-car-config/api/v1/dealerController/getDealerListByTestDrive',
  estimateConfig: '/api-wap/audi-car-config/api/v1/cc/public/estimateConfig',
  // 配置单相似车
  getSimilarityModel:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/similarityModel',

  // 行驶证信息查询
  defUrl: '/api-wap/audi-after-service/api/manCar/',

  // 查询是否可以下单

  getCheckNowBuyStatus:
    '/api-wap/audi-marketing-tools/api/marketingPlans/checkNowBuyStatus',
  getUserInfo: '/api-wap/cop-auth/api/v1/vip/users',
  getTryCarUrl:
    '/api-wap/audi-test-drive/api/reservationClient/getUserTestDriveStatus',
  sendCode: '/api-wap/audi-test-drive/api/v1/astro/third/clue/send',
  privacy: '/api-wap/cop-system/api/v1/config/audi.app.privacy',
  policy: '/api-wap/cop-system/api/v1/config/template.privacy.policy',
  // 获取车型数据
  getCarModelData: '/api-wap/audi-car-config/api/v1/type/list',
  // 获取订单列表
  getOrderList: '/api-wap/cop-order-query/api/v1/my-orders',
  // 获取订单列表 (E-shop)
  getEShopOrderList: '/api-wap/audi-eshop/api/v1/order-query/getMyOrders',
  // 获取订单详情
  getOrderDetails: '/api-wap/audi-eshop/api/v1/getDetails',
  // 获取整车订单H5页面详情地址
  getCarOrderInfoH5: '/api-wap/cop-system/api/v1/config/audi.reserve6.h5',
  // 购物车-整车接口
  getCarShoppingCartList:
    '/api-wap/audi-car-config/api/v1/carShoppingCart/getCarShoppingCartList',
  // 删除购物车 整车
  postDelCarShoppingCart:
    '/api-wap/audi-car-config/api/v1/carShoppingCart/delCarShoppingCart',
  // 获取颜色，含内饰
  getColorData: '/api-wap/audi-car-config/api/v1/color',
  // 获取轮毂
  getHubData: '/api-wap/audi-car-config/api/v1/option/RAD',
  // 获取装备
  getEquipmentData: '/api-wap/audi-car-config/api/v1/option/equipment',
  // 获取权益详情
  getFirstEquityDetail: '/api-wap/cop-system/api/v1/config/audi.early.rights',
  // 贷款规则
  getLoanRule: '/api-wap/audi-car-config/api/v1/loanRule/list',
  // 获取商品详情
  getProductDetail: '/api-wap/cop-prod-query/api/v1/products/sku-product',
  // 签订合同
  signContract: '/api-wap/cop-contract-adapter/api/v1/contract/signs',
  // 个人查看法大大是否认证过
  verifyFadada: '/api-wap/cop-contract-adapter/api/v1/identity/user/verify',
  // 企业查看法大大是否认证过
  enterpriseVerifyFadada: '/api-wap/cop-contract-adapter/api/v1/identity/enterprise/verify',
  // 查询线上合同签订的状态
  getOnlineContractStatus:
    '/api-wap/cop-contract-adapter/api/v1/contract/state',
  // 获取签名之后的合同预览图片
  getSignedContractImage:
    '/api-wap/cop-contract-adapter/api/v1/contract/image/page',
  // 创建线上合同
  getContractInfo:
    '/api-wap/cop-contract-adapter/api/v1/contract/generator/template',
  // 查看合同具体图片信息
  getContractImgDetail:
    '/api-wap/cop-contract-adapter/api/v1/contract/sample/image/page',
  // 获取线下合同的状态
  getContractState:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/getContractState',
  // 上传合同文件
  uploadContractFile:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/uploadOfflineSample',
  // 下载线下合同
  downloadContractPdf:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/downloadOfflineSample',
  // 创建线下的合同
  createOfflineContract:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/generateOfflineSample',
  // 查看合同预览图片
  getContractPreviewImgs:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/contractOfflineTemplate',
  // 获取线下合同预览的图片
  getOfflineContractImage:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/downloadOfflinePaperImage',
  // 获取合同的图片数量，签署合同后需要查看合同的图片
  getContractImageCount:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/getContractCount',
  // 查看车辆配置
  getCarConfig: '/api-wap/audi-car-config/api/v1/carconfig',
  getA7MrConfigcc: '/api-wap/cop-system/api/v1/config/audi.carconfig.mr.cc',
  getCarConfigcc: '/api-wap/cop-system/api/v1/config/audi.carconfig.cc',
  getQ5EConfigcc: '/api-wap/cop-system/api/v1/config/audi.carconfig.q5e.cc',
  getQ6cc: '/api-wap/cop-system/api/v1/config/audi.carconfig.q6.cc',
  getA5Lcc: '/api-wap/cop-system/api/v1/config/audi.carconfig.a5l.cc',
  // 获取车型列表 创建预约使用
  getModelList: '/api-wap/audi-car-config/api/v1/accb/model/list',
  // 创世卡判断
  judgeChuangshi: '/api-wap/audi-test-drive/api/reservationClient/judge',
  // 获取配置线 金融计算器使用
  getModelLine: '/api-wap/audi-car-config/api/v1/cc/public/modelLine',
  getCalculatorModelLine:
    '/api-wap/audi-car-config/api/v1/cc/public/loanCalculator/modelLine',
  getCustomSeries: '/api-wap/audi-car-config/api/v1/cc/public/customSeries',
  // 贷款计算
  getLoanCompute: '/api-wap/audi-car-config/api/v1/loanRule/compute',
  // 融资租赁计算
  getrentCompute: '/api-wap/audi-car-config/api/v1/loanRule/rentCompute',
  // 创建预约试驾
  createTestDriver: '/api-wap/audi-test-drive/api/reservationClient/create',
  createInviteTestDriver:
    '/api-wap/audi-test-drive/api/inviteReservation/create',
  createTouristYouke:
    '/api-wap/audi-test-drive/api/inviteReservation/createTourist',
  // 通过预约日期和车系查询符合试驾预约的门店
  getOrgsForAppointmentList:
    '/api-wap/audi-test-drive/api/reservationClient/getOrgsForAppointmentList',
  // 查询试乘试驾预约详情 id参数为预约单id
  testDriverDetail:
    '/api-wap/audi-test-drive/api/reservationClient/getTestDriveDetailByAppoid',
  // 查询试乘试驾资源
  getListResource:
    '/api-wap/audi-test-drive/api/reservationClient/getListResource',
  // 查询试乘试驾列表
  getListTestDrive:
    '/api-wap/audi-test-drive/api/reservationClient/getListTestDrive',
  // 查询预约列表
  testDriverList: '/api-wap/audi-test-drive/api/reservationClient/list',
  // 更新试乘试驾信息
  updateTestDrive:
    '/api-wap/audi-test-drive/api/reservationClient/updateTestDrive',
  // 修改或取消预约
  cancelOrUpdate:
    '/api-wap/audi-test-drive/api/reservationClient/cancelOrUpdate',
  // OCR身份证识别
  identifyIdCard: '/api-wap/audi-test-drive/api/v1/ocr/ocrIdCard',
  // OCR驾驶证识别
  identifyDrivingLicence:
    '/api-wap/audi-test-drive/api/v1/ocr/ocrDrivingLicence',
  // 获取试驾报告数据
  getShareReport:
    '/api-wap/audi-test-drive/api/reservationClient/getTestDriveReport',
  // 试乘试驾报告回传
  syncReport: '/api-wap/audi-test-drive/api/reservationClient/syncReport',
  // 分享试驾报告获取奥金
  shareReport: '/api-wap/audi-test-drive/api/reservationClient/shareReport',
  // 获取最近的代理商(经销商)
  getNearDealers:
    '/api-wap/audi-car-config/api/v1/dealerController/getNearestDealer',
  // 添加配置单到购物车
  addCarShoppingCart:
    '/api-wap/audi-car-config/api/v1/carShoppingCart/addCarShoppingCart',
  // 获取skuid
  getOtds: '/api-wap/cop-prod-query/api/v1/otds',
  // 限量号
  getLimitnumberRule: '/api-wap/cop-system/api/v1/config/audi.limitnumber.rule',
  // 运营位
  getFloorslist: '/api-wap/audi-page/api/floors',
  // 限量号图片
  getFloors:
    '/api-wap/audi-page/api/floors?pageFrontCode=audi-limit-number&pageAccessType=2',
  // 先行权益图片
  getFloors1:
    '/api-wap/audi-page/api/floors?pageFrontCode=audi-cc-reserve-offers&pageAccessType=2',
  // 动态获取先行权益图片
  getFloors2: '/api-wap/audi-eshop/api/v1/userRights/findByCarModelId',
  // 根据skuid获取车辆的先行权益信息
  getUserRightsBySkuId: '/api-wap/audi-eshop/api/v1/userRights/findBySkuId',
  // 获取推荐人信息
  getInviteUser: '/api-wap/audi-task/api/task/invite/user',
  // 根据CarModelId获取车辆的先行权益信息
  getUserRightsByCarModelId:
    '/api-wap/audi-eshop/api/v1/userRights/findByCarModelId',
  // 获取订单详情
  getOrderDetail: '/api-wap/cop-order-query/api/v1/my-orders',
  // 参数表
  getCarParamTable: '/api-wap/audi-car-config/api/v1/parameter/list',
  // 查询是否使用过数字人民币
  getDigitalPayStatus:
    '/api-wap/audi-eshop/api/v1/whetherOrderDigitalCertificate',
  // 获取数字支付凭证信息
  getDigitalPayInfo: '/api-wap/audi-eshop/api/v1/getOrderDigitalCertificate',
  // 获取A7l限量号
  getLimitedNumber:
    '/api-wap/audi-eshop/api/limited_numbers/getLimitedNumberByOrderId',
  // 获取已有限量号
  getHasLimitedNumber:
    '/api-wap/audi-eshop/api/v1/order-plan-items/queryLmNumber',
  // 查看可贷款的银行平台列表
  getFinancePlatformList:
    '/api-wap/cop-vip-adapter/api/v1/nga-loan/finance/platform',
  // 选择贷款金融服务
  applyFinanceServer:
    '/api-wap/audi-eshop/api/v1/nga-loan/finance/loan/apply/url',
  // 大定退款
  daDingToRefund: '/api-wap/audi-eshop/api/v1/nga-confirm/refund',

  // 获取经销商列表
  getDealerList:
    '/api-wap/audi-car-config/api/v1/dealerController/getDealerList',
  // 更新nga订单
  updateNgaOrderInfo:
    '/api-wap/audi-eshop/api/v1/nga-confirm/updateNgaOrderInfo',
  // code 获取经销商列表
  getDealerByCode:
    '/api-wap/audi-car-config/api/v1/dealerController/getDealerDetails',

  // 查看当前用户是否有创世卡
  judgeReservationClient:
    '/api-wap/audi-test-drive/api/reservationClient/judge',
  // 获取车辆配置信息(新版)
  getConfigTable: '/api-wap/audi-car-config/api/v1/cc/detail',
  // 获取兼容车型列表
  getModelCompatibleList: '/api-wap/cop-system/api/v1/config/audi.app.machines',
  // 通过ccid更新车配信息
  updateCarConfigByCcid: '/api-wap/audi-car-config/api/v1/cc',
  // 自动保存购物车逻辑
  autoSaveCarShoppingCart:
    '/api-wap/audi-car-config/api/v1/carShoppingCart/autoSaveCarShoppingCart',
  // 配置线全配置
  getModelLineConfigs:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs',
  // 刷新ccid (拿旧的ccid 换一个新的ccid)
  refreshCcid: '/api-wap/audi-car-config/api/v1/cc/refresh',
  // 获取购车协议图片
  getBuyCarAgreement:
    '/api-wap/cop-system/api/v1/config/audi.contract.img.purchasecar',
  // 查看贷款状态
  getLoanStatus: '/api-wap/cop-vip-adapter/api/v1/nga-loan/finance/loan',
  // 单独获取配置表
  getCarConfigDetail: '/api-wap/audi-car-config/api/v1/cc/detail',
  // 小程序支付接口
  minipPay: '/api-wap/cop-order/api/v1/payments',
  // 获取分享的路径
  getShareUrl: '/api-wap/cop-system/api/v1/config/audi.share.index',
  // 获取H5的config信息
  getWXConfig: '/api-wap/audi-task/api/task/signature',

  // 获取购买vin的售后服务商
  getAfterSalesByVin: '/api-wap/audi-eshop/api/v1/dealers/getAfterSalesByVin',

  // 获取售后服务商列表
  getAfterSalesList:
    '/api-wap/audi-car-config/api/v1/svcdAfterSales/getAfterSalesList',
  // 创建售后服务预约订单
  postAfterSaleCreate: '/api-wap/audi-after-service/api/afterSale/create',
  getAfterSalesListForReservation:
    '/api-wap/audi-car-config/api/v1/svcdAfterSales/getAfterSalesListForReservation',
  // 修改售后服务预约订单
  postAfterSaleUpdate: '/api-wap/audi-after-service/api/afterSale/update',
  // 取消售后服务预约订单
  getAfterSaleCancel: '/api-wap/audi-after-service/api/afterSale/cancel',
  // 获取预约订单列表
  getAfterSaleFindByPage: '/api-wap/audi-after-service/api/common/findByPage',
  // 获取insaic服务订单列表
  getInsaicServiceList: '/api-wap/audi-after-service/api/insaic/getOrderList',
  // 获取insaic服务的详情页
  getInsaicServiceDetail:
    '/api-wap/audi-after-service/api/insaic/getOrderDetailPage',
  // 获取道路救援服务订单列表
  getRoadSaveServiceList:
    '/api-wap/audi-after-service/api/rescue/getRoadsideAssistanceList',
  // 获取道路救援订单详情
  getRoadSaveOrderDetail:
    '/api-wap/audi-after-service/api/rescue/getRoadsideAssistanceDetailsUrl',
  // 获取预约订单详情
  getAfterSaleDetail:
    '/api-wap/audi-after-service/api/afterSale/getAfterSaleDetail',
  // 用户绑定车辆信息
  postBindCardInfo: '/api-wap/audi-after-service/api/common/getBindCardInfo',
  // 获取服务商所在城市
  getCityList: '/api-wap/audi-after-service/api/common/getCityList',
  // 获取服务商所在省+城市
  getProvinceCityList:
    '/api-wap/audi-after-service/api/common/getProvinceCityList',

  // 获取可预约的时间
  getAfterSaleListResource:
    '/api-wap/audi-after-service/api/afterSale/getListResource',
  // 新获取可预约的时间
  getAfterSaleListResourceNew:
    '/api-wap/audi-after-service/api/afterSale/getListResourceNew',
  // 9、获取可选服务项目 mileage公里数
  getPackageMainData:
    '/api-wap/audi-after-service/api/afterSale/getPackageMainData',
  // 6、查询开通取送车城市信息接口
  getDeliverCarQueryCityList:
    '/api-wap/audi-after-service/api/deliverCar/queryCityList',
  // 7、获取指定城市开通取送车的经销商
  getDeliverCarQueryDealer:
    '/api-wap/audi-after-service/api/deliverCar/queryDealer',
  // 创建取送车订单
  postCreateDeliverCar: '/api-wap/audi-after-service/api/deliverCar/create',
  // 取送车订单详情
  getDeliverCarOrderDetail:
    '/api-wap/audi-after-service/api/deliverCar/getOrderDetail',
  // 取消取送车订单
  postCancelDeliverCarOrder:
    '/api-wap/audi-after-service/api/deliverCar/cancel',
  // 获取取送车优惠券
  getCarCoupon: '/api-wap/audi-coupon/api/v1/users/mp/my-pickup-coupon',
  // 6、获取订单状态列表
  getOrderStatusList:
    '/api-wap/audi-after-service/api/deliverCar/getOrderStatusList',
  // 获取红点状态
  getUnreadDeliverCarOrderStatusCount: '/api-wap/audi-after-service/api/deliverCar/getUnreadDeliverCarOrderStatusCount',
  // 根据订单ID回去轨迹坐标
  getCoordsList: '/api-wap/audi-after-service/api/deliverCar/getCoordsList',
  // 7、根据订单id获取车辆图片集合
  getCarImages: '/api-wap/audi-after-service/api/deliverCar/getCarImage',
  // 如果token过期了，那就需要刷新下
  refreshToken: '/api-wap/audi-miniprogram/api/v1/miniprogram/refreshToken',
  // 车辆对比数据
  getCodelLineConfigsCompare:
    '/api-wap/audi-car-config/api/v1/cc/public/modelLine/configs/compare',
  // 是否显示 申请退款按钮
  getCanRefund: '/api-wap/audi-eshop/api/v1/canRefund',
  // 查看某个ccid是否还能更新配置
  canUpdateCCConfig: '/api-wap/audi-eshop/api/v1/canUpdateCCConfig',
  // 查看当前订单是否有排队号
  getLimitedNumberLineByOrderId:
    '/api-wap/audi-eshop/api/limited_numbers_line/getLimitedNumberLineByOrderId',
  // 10、获取售后服务车辆报告数据
  getReportList: '/api-wap/audi-after-service/api/afterSale/getReportList',
  // 是否点击过尾款支付按钮
  isConfirmed: '/api-wap/audi-eshop/api/v1/nga-confirm/isConfirmed',
  // 确认尾款支付
  confirmPayRest: '/api-wap/audi-eshop/api/v1/nga-confirm/confirmPayRest',
  // 查看车辆的首付/尾款支付信息
  getCarPaymentInfo: '/api-wap/cop-order/api/v1/payment/car',
  // 获取尾款线下二维码
  getBalanceQrcode: '/api-wap/audi-eshop/api/v1/nga-confirm/doPayRest',
  // 刷新尾款二维码
  refreshBalanceQrcode: '/api-wap/cop-pay/api/v1/payments/pos/refresh-pos-url',
  // 添加修改尾款凭证
  addOrderBalancePaymentCertificate:
    '/api-wap/audi-eshop/api/v1/certificate/addOrderBalancePaymentCertificate',
  // 获取订单的验车-交车详情
  getDetailsByOrderId:
    '/api-wap/audi-test-drive/api/v1/inspectionDeliveryInfo/getDetailsByOrderId',
  // 推送确认验车信息
  amsConfCheckCar:
    '/api-wap/audi-test-drive/api/v1/inspectionDelivery/amsConfCheckCar',
  // 修改意向交车方式
  updateAmsDeliverType:
    '/api-wap/audi-test-drive/api/v1/inspectionDelivery/amsDeliverType',
  // 确认车辆交付 信息同步ams
  amsConfDeliverCarSign:
    '/api-wap/audi-test-drive/api/v1/inspectionDelivery/amsConfDeliverCarSign',
  // 获取代理商列表-根据当前坐标按距离排序
  getNearestDealerList:
    '/api-wap/audi-car-config/api/v1/dealerController/getNearestDealerList',
  // 获取用户可用奥金
  getMyPointsV2: '/api-wap/audi-task/api/task/getMyPointsV2',
  // 商品预下单接口
  postPreorders: '/api-wap/cop-order-query/api/v1/pre-orders',
  // 获取用户默认地址
  getUserAddress: '/api-wap/cop-uaa-adapter/api/v1/adress',
  // 是否收藏了商品
  isFavorited: '/api-wap/audi-bbs/api/favorite/isFavorited',
  // 收藏 取消收藏商品
  updateFavorite: '/api-wap/audi-bbs/api/favorite',
  // 获取NGA订单对应的服务商
  getAfterSalesByOrderId:
    '/api-wap/audi-eshop/api/v1/dealers/getAfterSalesByOrderId',
  // 推送确认收车消息
  amsConfDeliverCar:
    '/api-wap/audi-test-drive/api/v1/inspectionDelivery/amsConfDeliverCar',

  // 人车关系相关接口
  // 身份证识别
  postOrcIdentifyIdCard: '/api-wap/audi-after-service/api/v1/ocr/ocrIdCard',
  // 驾驶证识别
  postOcrDrivingLicence:
    '/api-wap/audi-after-service/api/v1/ocr/ocrDrivingLicence',
  // 行驶证识别
  postOcrVehiclelicense:
    '/api-wap/audi-after-service/api/v1/ocr/ocrVehiclelicense',
  // 购车发票识别
  postOcrInvoiceVehiclelicense:
    '/api-wap/audi-after-service/api/v1/ocr/vehicleInvoice',
  // 获取会员信息
  getManCarMemberInfo: '/api-wap/audi-after-service/api/manCar/memberInfo',
  // 加入会员
  postManCarJoinClub: '/api-wap/audi-after-service/api/manCar/joinClub',
  // 获取用户绑车信息
  getFindCustomerCarList:
    '/api-wap/audi-after-service/api/manCar/findCustomerCarList',
  // 上传身份证-驾驶证认证会员
  postIdAndDrivingLicenseRecognition:
    '/api-wap/audi-after-service/api/manCar/idAndDrivingLicenseRecognition',
  // 上传发票认证车辆
  postVehicleInvoiceRecognition:
    '/api-wap/audi-after-service/api/manCar/vehicleInvoiceRecognition',
  // 上传行驶证认证车辆
  postVehicleLicenseRecognition:
    '/api-wap/audi-after-service/api/manCar/vehicleLicenseRecognition',
  // 7、其他认证 上传证件 照片
  postOcrImageUpload: '/api-wap/audi-after-service/api/manCar/ocrImageUpload',
  // 8、其他认证 提交认证
  postOcrIdDrivingSubmit:
    '/api-wap/audi-after-service/api/manCar/ocrIdDrivingSubmit',
  // 获取图形验证码
  getCaptchaImage: '/api-wap/cop-cms/api/v1/captcha/image?random=202202',
  // 发送短信 解绑车辆
  postSendSMS: '/api-wap/audi-after-service/api/manCar/sendSms',
  // 10、车牌绑定
  postBindCarNumber: '/api-wap/audi-after-service/api/manCar/bindCarNumber',
  // 9、车辆解绑认证
  postCarUnbinding: '/api-wap/audi-after-service/api/manCar/unbinding',
  // 根据vin来查询订单的ccid
  getCCIDbyVin: '/api-wap/audi-eshop/api/v1/getCCIDbyVin',
  // 查找门店（获取省份列表,默认查询存在代理商的)
  getProvinceList:
    '/api-wap/audi-car-config/api/v1/svcdProvince/getProvinceList',
  // 查找门店 获取城市列表,默认查询存在经销商的
  getAgentCityList: '/api-wap/audi-car-config/api/v1/svcdCity/getCityList',
  // 查找门店（获取渠道商列表=代理商+服务商）
  // getOrgList: '/api-wap/audi-car-config/api/v1/dealerController/getOrgList',
  getOrgList:
    '/api-wap/audi-car-config/api/v1/dealerController/getDealerListByOfficialWebsite',

  /** 留学生免税接口 */
  // 查询留学生信息
  getOverseasStudentMessage:
    '/api-wap/audi-eshop/api/v1/overseas-student/getOverseasStudentMessage',
  // 获取准购单渠道地址
  getBuyingOrderApproach:
    '/api-wap/audi-eshop/api/v1/overseas-student/getBuyingOrderApproach',

  // 上传图片
  postImageEncryption:
    '/api-wap/audi-eshop/api/v1/overseas-student/getImageEncryption',
  // 上传上传留学生信息
  postAddOverseasStudentMessage:
    '/api-wap/audi-eshop/api/v1/overseas-student/addOverseasStudentMessage',
  // 更改留学生信息
  putUpdateOverseasStudentMessage:
    '/api-wap/audi-eshop/api/v1/overseas-student/updateOverseasStudentMessage',

  /** 超长试驾 */
  // 4、超长试驾预约
  postVeryLongReservationSubmit:
    '/api-wap/audi-test-drive/api/veryLongReservation/submit',
  // 获取代理商
  getVeryLongReservationDealerList:
    '/api-wap/audi-test-drive/api/veryLongReservation/getDealerList',
  // 6、查询试乘试驾资源
  getVeryLongReservationListResource:
    '/api-wap/audi-test-drive/api/veryLongReservation/getListResource',
  // 取消预约
  postVeryLongReservationCancel:
    '/api-wap/audi-test-drive/api/veryLongReservation/cancel',
  // 7、查询试乘试驾详情
  getVeryLongReservationTestDriveDetailByAppoid:
    '/api-wap/audi-test-drive/api/veryLongReservation/getTestDriveDetailByAppoid',
  // 11、结束试驾
  getVeryLongReservationEarlyEnd:
    '/api-wap/audi-test-drive/api/veryLongReservation/earlyEnd',
  // 获取订单列表
  getVeryLongReservationOrderList:
    '/api-wap/audi-test-drive/api/veryLongReservation/list',
  // 13、下载享道合同
  getVeryLongReservationGeneratePDF:
    '/api-wap/audi-test-drive/api/veryLongReservation/generatePDF',
  // 14、获取支付url或开票url
  getVeryLongReservationUrl:
    '/api-wap/audi-test-drive/api/veryLongReservation/getUrl',

  // 13、检查用户有没有权限预约超长试驾
  getVeryLongReservationValidAuth:
    '/api-wap/audi-test-drive/api/veryLongReservation/validAuth',
  // 9、获取试驾报告数据
  getVeryLongReservationTestDriveReport:
    '/api-wap/audi-test-drive/api/veryLongReservation/getTestDriveReport',

  /** 充电桩接口 */
  // 1、客户所选车型是否有充电权益
  getEquityChargingPile:
    '/api-wap/audi-after-service/api/chargingPile/getEquity',
  // 2、创建充电充电桩安装预约订单（记录）
  postCreateChargingPile: '/api-wap/audi-after-service/api/chargingPile/create',
  // 3、获取充电桩订单详情
  getChargingPileInfo: '/api-wap/audi-after-service/api/chargingPile/getOrder',
  // 4、取消订单
  postChargingPileCancel: '/api-wap/audi-after-service/api/chargingPile/cancel',
  // 5、提交安装信息
  postChargingPileSubmit: '/api-wap/audi-after-service/api/chargingPile/submit',
  // 6、根据预约订单号获取车主信息
  getChargingPileCarOwnerInfo:
    '/api-wap/audi-after-service/api/chargingPile/getCarOwnerInfo',
  // 7、根据csms订单号获取状态信息列表
  getChargingPileStatusList:
    '/api-wap/audi-after-service/api/chargingPile/getStatusList',
  // 添加大订备注
  addRemark: '/api-wap/audi-eshop/api/v1/addRemark',
  // 获取整车订单详情信息
  getNgaDetailsOrder: '/api-wap/audi-eshop/api/v1/getNgaDetailsOrder',
  // 物流透明化 查看物流信息
  getTraceInfo: '/api-wap/audi-eshop/api/v1/getTraceInfo',
  // 获取三包凭证信息
  getGenerateMessage:
    '/api-wap/audi-contract-adapter/api/v1/certificate/getGenerateMessage',
  // 获取三包凭证文件流
  downloadPdfGenerate:
    '/api-wap/audi-contract-adapter/api/v1/certificate/generate',
  // 获取渠道商对应的金融机构
  getOrgBankList:
    '/api-wap/audi-car-config/api/v1/dealerController/getOrgBankList',
  getOrgBankListV2:
    '/api-wap/audi-car-config/api/v1/dealerController/getOrgBankListV2',
  // 超长试驾创建
  createVeryLongReservation:
    '/api-wap/audi-test-drive/api/veryLongReservation/create',
  // 获取爱车项目url
  getAudiCarUrl: '/api-wap/cop-system/api/v1/config/audi.car6.index',
  // 获取爱车项目url
  getAudiCar2Url: '/api-wap/cop-system/api/v1/config/audi.car2.index',
  // 是否可以大定支付
  getNgaConfirmCanDdPay: '/api-wap/audi-eshop/api/v1/nga-confirm/canDdPay',
  // 获取minip.h5项目url
  getAudiMinipUrl: '/api-wap/cop-system/api/v1/config/audi.minip.h5',
  // 获取尾款支付信息
  getOrderBalancePaymentCertificate:
    '/api-wap/audi-eshop/api/v1/certificate/getOrderBalancePaymentCertificate',
  // 我的报名详情
  myApplyInfo: '/api-wap/audi-test-drive/api/veryLongReservation/myApplyInfo',
  // 取消报名
  cancelEnroll: '/api-wap/audi-test-drive/api/veryLongReservation/cancelEnroll',
  // 小订后是否可以更新订单的信息
  canXdUpdateInfo: '/api-wap/audi-eshop/api/v1/nga-confirm/canXdUpdateInfo',
  // 贷款改成全款支付
  updateToFullPayment:
    '/api-wap/audi-eshop/api/v1/nga-confirm/updateToFullPayment',

  getSourceId: '/api-wap/audi-car-config/api/v1/cc/public/source',
  // 获取购物车详情
  getShoppingCartDetail:
    '/api-wap/audi-car-config/api/v1/carShoppingCart/getCarShoppingCartDetail',
  // 获取邀请用户的信息
  getInviteUserInfo: '/api-wap/audi-task/api/task/invite/user',
  // 获取服务弹窗提示状态
  getXdLoanAgentStatus:
    '/api-wap/audi-eshop/api/v1/nga-confirm/getXdLoanAgentStatus',
  // 更新信贷服务弹窗提示状态
  clickLoanAgent: '/api-wap/audi-eshop/api/v1/nga-confirm/clickLoanAgent',
  // 查询牌照额度申请状态
  getQueryLicenseStatus:
    '/api-wap/audi-eshop/api/v1/order-plan-items/queryLicenseStatus',
  // 查看当前福袋信息
  getBlessedPackInfo:
    '/api-wap/audi-marketing-tools/api/blessedBag/findBlessedBagByUserId',
  // 确认CCID是否是福袋车CCID
  blessedPackConfirmCcid:
    '/api-wap/audi-marketing-tools/api/blessedPack/confirmCcid',
  // 查看特殊车辆是否有特殊价格
  getCarSpecPrice: '/api-wap/audi-eshop/api/v1/getCarSpecPrice',

  //   /api/v1/limited-number-schedule/querySurplusLimitNumCount                  GET  查询限量号剩余数量是否充足
  // /api/v1/limited-number-schedule/queryOrderLimitedNumScheduleHis            GET  查询限量号选号进度历史信息        orderId
  // /api/v1/limited-number-schedule/checkLimitedNumber                         GET  验证当前限量号是否被占用          limitedNumber
  // /api/v1/limited-number-schedule/queryLimitedNumbersHis                     GET  根据订单号查询限量号选择记录      orderId
  // /api/v1/limited-number-schedule/randomRandomLimitedNumber                  GET  随机限量号                        orderId   schedule  X-User-Idpid
  // /api/v1/limited-number-schedule/bindLimitedNumber                          GET  绑定限量号                        orderId   limitedNumber
  // /private/v1/limited-number-schedule/bindLimitedNumberTimer                 GET  定时绑定限量号

  // 典藏号 start
  // 查询典藏号处于哪一阶段
  // checkLimitedNumberStatus: '/api-wap/audi-eshop/api/v1/limited-number-schedule/queryOrderLimitedNumSchedule',
  // 随机限量号
  getLimitedNumberList:
    '/api-wap/audi-eshop/api/v1/limited-number-schedule/randomRandomLimitedNumber',
  // 绑定限量号码
  bindLimitedNumber:
    '/api-wap/audi-eshop/api/v1/limited-number-schedule/bindLimitedNumber',
  // 查询限量号选号进度历史信息
  getMineLimitedNumberStep:
    '/api-wap/audi-eshop/api/v1/limited-number-schedule/queryOrderLimitedNumScheduleHis',
  // 当前号池剩余数量
  getLimitedNumberCount:
    '/api-wap/audi-eshop/api/v1/limited-number-schedule/querySurplusLimitNumCount',
  // 当前选中限量号（暂存）  => 更新选中限量号记录
  pitchOnLimitedNumber:
    '/api-wap/audi-eshop/api/v1/limited-number-schedule/refreshSelectHis',

  // 保险路径
  getCarInsurance: '/api-wap/audi-after-service/api/insaic/carInsurance',

  // 查询国资委用户基本信息
  getqueryRelativeInformation:
    '/api-wap/audi-eshop/api/v1/relative-information/queryRelativeInformation',
  // 查询国资委用户上传资料信息
  getqueryRelativeMaterials:
    '/api-wap/audi-eshop/api/v1/relativeMaterial/queryRelativeMaterials',
  // 提交国资委用户信息
  saveRelativeInformation:
    '/api-wap/audi-eshop/api/v1/relative-information/saveRelativeInformation',
  // 上传图片
  uploadOSSFile: '/api-wap/audi-eshop/api/v1/relativeMaterial/uploadOSSFile',

  // 站内信进来查看国资委用户提交审核状态
  queryRelativeInformationById:
    '/api-wap/audi-eshop/api/v1/relative-information/queryRelativeInformationById',
  // 大定订单修改详情查询
  queryNgaOrderInfoConfirm:
    '/api-wap/audi-eshop/api/v1/ngaOrderUpdateConfirm/queryNgaOrderInfoConfirm',
  // 确认订单修改
  confirmUpdate:
    '/api-wap/audi-eshop/api/v1/ngaOrderUpdateConfirm/confirmUpdate',
  // 查询金额(大小定金)
  getNgaPrice: '/api-wap/cop-prod-query/api/v1/prod-nga/price',
  // 查询是否可更改配置
  getCheckChangeConfiguration: '/api-wap/audi-eshop/api/v1/order/highToHalf',
  // 确认更改配置
  setConfirmChangeConfiguration:
    '/api-wap/audi-eshop/api/v1/order/confirmHighToHalf',

  // 查询是否是（国资委用户）
  checkOrderModifiedInfo:
    '/api-wap/audi-eshop/api/v1/order-plan-items/queryOrderModifiedStatus',
  // 整车订单是否发过销售卡券
  checkOrderHasSalesCoupons:
    '/api-wap/audi-eshop/api/v1/order-plan-items/queryOrderHasSalesCoupons',
  setSaleETicketTransact:
    '/api-wap/audi-eshop/api/v1/salesCoupons/saleEticketTransact',
  getOrderEquities: '/api-wap/audi-eshop/api/v1/salesCoupons/getOrderEquities',

  // 查询用户当前是否有保险券
  getMineInsuranceVoucher:
    '/api-wap/audi-eshop/api/v1/salesCoupons/queryInsuranceCoupon',
  // 获取筑梦权益包的商品ID
  getZMRightsProdId: '/api-wap/cop-system/api/v1/config/audi.nga.rights.prodId',
  // 获取minip.h5项目url
  getAudiMallH5Url: '/api-wap/cop-system/api/v1/config/audi.mall.h5',
  // 查询重置贷款
  setFinanceReplacement:
    '/api-wap/audi-eshop/api/v1/nga-loan/finance/resetLoan',

  // 核销卡券
  useMpCoupon: '/api-wap/audi-eshop/api/v1/nga-confirm/useMpCoupon',

  // 留学生
  // 查询留学生基本信息
  getqueryOverseasStudentsInformation:
    '/api-wap/audi-eshop/api/v1/overseasStudentsInformation/queryOverseasStudentsInformation',
  // 查询留学生上传资料信息
  getqueryOverseasStudentsMaterial:
    '/api-wap/audi-eshop/api/v1/overseasStudentsMaterial/queryOverseasStudentsMaterial',
  // 站内信进来查看留学生提交审核状态
  getqueryOverseasStudentsInformationById:
    '/api-wap/audi-eshop/api/v1/overseasStudentsInformation/queryOverseasStudentsInformationById',
  // 留学生上传图片
  overseasStudentsMaterial:
    '/api-wap/audi-eshop/api/v1/overseasStudentsMaterial/uploadOSSFile',
  // 提交留学生信息
  saveOverseasStudentsInformationn:
    '/api-wap/audi-eshop/api/v1/overseasStudentsInformation/saveOverseasStudentsInformation',
  // 查询修改数据详情(同一ccid是否已经下单)
  refreshStockCar:
    '/api-wap/audi-eshop/api/v1/OmdStockCarConfig/refreshStockCar',
  getStockCarConfigDetail:
    '/api-wap/audi-eshop/api/v1/OmdStockCarConfig/getStockCarConfigDetail',
  // 是否是长库存车
  checkStockCar: '/api-wap/audi-eshop/api/v1/OmdStockCarConfig/isStockCar',
  // 行驶证信息解绑
  unBindDrivingLicense:
    '/api-wap/audi-after-service/api/manCar/unBindDrivingLicense',
  // 上传二手车发票认证车辆
  postusedCarVehicleInvoice:
    '/api-wap/audi-after-service/api/v1/ocr/usedCarVehicleInvoice',

  // 进取精英扫码认证
  // 提交进取精英信息
  saveEnterprisingEliteInformation:
    '/api-wap/audi-eshop/api/v1/enterprisingEliteInformation/saveEnterprisingEliteInformation',
  // 站内信进来查看进取精英提交审核状态
  queryEnterprisingEliteInformationById:
    '/api-wap/audi-eshop/api/v1/enterprisingEliteInformation/queryEnterprisingEliteInformationById',
  // 查询进取精英基本信息
  queryEnterprisingEliteInformation:
    '/api-wap/audi-eshop/api/v1/enterprisingEliteInformation/queryEnterprisingEliteInformation',
  // 进取精英上传图片
  enterprisingEliteMaterialUploadOSSFile:
    '/api-wap/audi-eshop/api/v1/enterprisingEliteMaterial/uploadOSSFile',
  // 查询进取精英上传资料信息
  queryEnterprisingEliteMaterials:
    '/api-wap/audi-eshop/api/v1/enterprisingEliteMaterial/queryEnterprisingEliteMaterials',
  // 查询PGC数据
  checkPGCData:
    '/api-wap/audi-page/api/floors?pageFrontCode=audi-car-order-steps&pageAccessType=2',
  // 订单透明化节点 变更通知及更改
  checkOrderTraceInfoStatus:
    '/api-wap/audi-eshop/api/v1/OrderTraceInfoStatus/getByOrderId',
  updateOrderTraceInfoStatus:
    '/api-wap/audi-eshop/api/v1/OrderTraceInfoStatus/updateByOrderId',
  // 查询订单状态(31-32)
  getOrderMniStatus: '/api-wap/audi-eshop/api/v1/audiOrderStatus/getByOrderId',
  // 整车订单企业线上签署合同(通知)
  sendEnterpriseWeChatMessage:
    '/api-wap/audi-eshop/api/v1/publicContractOnline/sendEnterpriseWeChatMessage',
  // 整车订单企业线上签署合同(标记)
  setEnterpriseChannelSign: '/api-wap/audi-eshop/api/v1/publicContractOnline/',
  // 行驶证绑车验证用户选择的车辆类型是否一致
  getOrderManCarVerify: '/api-wap/audi-after-service/api/manCar/verify',
  // 发送邮件（合同）
  sendingEMailContract:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/sendMail',
  // 查询下载合同（类型）
  getContractFileType:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/getContractFileType',
  // 下载合同
  downloadContractFile:
    '/api-wap/audi-contract-adapter/api/v1/contract-offline/downloadContractFile',

  // 供应商
  // 检查当前用户是否已经填过资料
  querySupplierEmployeesInformation:
    '/api-wap/audi-eshop/api/v1/supplierEmployeesInformation/querySupplierEmployeesInformation',
  // 站内信进来查看供应商提交审核状态
  querySupplierEmployeesInformationById:
    '/api-wap/audi-eshop/api/v1/supplierEmployeesInformation/querySupplierEmployeesInformationById',
  // 查询供应商上传资料信息
  querySupplierEmployeesMaterials:
    '/api-wap/audi-eshop/api/v1/supplierEmployeesMaterial/querySupplierEmployeesMaterials',
  // 供应商上传图片
  supplierEmployeesMaterialUploadOSSFile:
    '/api-wap/audi-eshop/api/v1/supplierEmployeesMaterial/uploadOSSFile',
  // 提交进取精英信息
  saveSupplierEmployeesInformation:
    '/api-wap/audi-eshop/api/v1/supplierEmployeesInformation/saveSupplierEmployeesInformation',
  // 检查当前用户是否已经填过资料
  getSupplierCheckSupplierEmployees:
    '/api-wap/audi-eshop/api/v1/supplierEmployeesInformation/checkSupplierEmployees',

  // 特定政府机构员工
  // 检查当前用户是否已经填过资料
  queryGovernmentApparatusInformation:
    '/api-wap/audi-eshop/api/v1/governmentApparatusInformation/queryGovernmentApparatusInformation',
  // 站内信进来查看特定政府机构员工提交审核状态
  queryGovernmentEmployeesInformationById:
    '/api-wap/audi-eshop/api/v1/governmentApparatusInformation/queryGovernmentApparatusInformationById',
  // 查询特定政府机构员工上传资料信息
  queryGovernmentApparatusMaterial:
    '/api-wap/audi-eshop/api/v1/governmentApparatusMaterial/queryGovernmentApparatusMaterials',
  // 特定政府机构员工上传图片
  governmentEmployeesMaterialUploadOSSFile:
    '/api-wap/audi-eshop/api/v1/governmentApparatusMaterial/uploadOSSFile',
  // 提交进取精英信息
  saveGovernmentApparatusInformation:
    '/api-wap/audi-eshop/api/v1/governmentApparatusInformation/saveGovernmentApparatusInformation',
  // 数字藏品入口
  digitCollectionJumpURL:
    '/api-wap/cop-system/api/v1/config/audi.car.digit.collection',

  // 归国精英
  // 检查当前用户是否已经填过资料
  queryReturnedEliteInformation:
    '/api-wap/audi-eshop/api/v1/returnedEliteInformation/queryReturnedEliteInformation',
  // 站内信进来查看归国精英提交审核状态
  queryReturnedEliteInformationById:
    '/api-wap/audi-eshop/api/v1/returnedEliteInformation/queryReturnedEliteInformationById',
  // 查询归国精英上传资料信息
  queryReturnedEliteMaterials:
    '/api-wap/audi-eshop/api/v1/returnedEliteMaterial/queryReturnedEliteMaterials',
  // 归国精英上传图片
  returnedEliteMaterialUploadOSSFile:
    '/api-wap/audi-eshop/api/v1/returnedEliteMaterial/uploadOSSFile',
  // 提交进取精英信息
  saveReturnedEliteInformation:
    '/api-wap/audi-eshop/api/v1/returnedEliteInformation/saveReturnedEliteInformation',
  // 办理途径
  queryReturnedEliteHandlingMethods:
    '/api-wap/audi-eshop/api/v1/returnedEliteInformation/queryReturnedEliteHandlingMethods',

  // 政企
  // 检查当前用户是否已经填过资料
  queryGovernmentEnterpriseInformation:
    '/api-wap/audi-eshop/api/v1/governmentEnterpriseInformation/queryGovernmentEnterpriseInformation',
  // 站内信进来查看政企提交审核状态
  queryGovernmentEnterpriseInformationById:
    '/api-wap/audi-eshop/api/v1/governmentEnterpriseInformation/queryGovernmentEnterpriseInformationById',
  // 查询政企上传资料信息
  queryGovernmentEnterpriseMaterials:
    '/api-wap/audi-eshop/api/v1/governmentEnterpriseMaterial/queryGovernmentEnterpriseMaterials',
  // 政企上传图片
  governmentEnterpriseMaterialUploadOSSFile:
    '/api-wap/audi-eshop/api/v1/governmentEnterpriseMaterial/uploadOSSFile',
  // 提交进取精英信息
  saveGovernmentEnterpriseInformation:
    '/api-wap/audi-eshop/api/v1/governmentEnterpriseInformation/saveGovernmentEnterpriseInformation',
  // checkEmployerName: '/api-wap/audi-eshop/api/v1/governmentEnterpriseInformation/checkEmployerName',
  // 认证车辆信息获取
  postCheckVehicleInvoice:
    '/api-wap/audi-after-service/api/manCar/checkVehicleInvoice',
  checkEmployerName:
    '/api-wap/audi-eshop/api/v1/governmentEnterpriseInformation/checkEmployerName',
  // API超时时长
  getAPITimeOut: '/api-wap/cop-system/api/v1/config/audi.api.timeout',
  getAPITimeOutTesting:
    '/api-wap/cop-system/api/v1/config/audi.api.timeout.testing',
  // 整车订单下单
  carPlaceOrder: '/api-wap/audi-eshop/api/v1/orders',
  carSubmitToken: '/api-wap/cop-auth/api/v1/submit-tokens',

  // 自定义大用户
  // 检查当前用户是否已经填过资料
  queryCheckCustomizeTagInformation:
    '/api-wap/audi-eshop/api/v1/customizeTagMaterial/queryCustomizeTagMaterials',
  // 站内信进来查看用户提交审核状态
  queryCustomizeTagInformationById:
    '/api-wap/audi-eshop/api/v1/customizeTagInformation/queryCustomizeTagInformationById',
  // 查询用户上传资料信息
  queryCustomizeTagMaterials:
    '/api-wap/audi-eshop/api/v1/customizeTagMaterial/queryCustomizeTagMaterials',
  // 上传图片
  customizeTagMaterialuploadOSSFile:
    '/api-wap/audi-eshop/api/v1/customizeTagMaterial/uploadOSSFile',
  // 提交国资委用户信息
  saveCustomizeTagInformation:
    '/api-wap/audi-eshop/api/v1/customizeTagInformation/saveCustomizeTagInformation',
  queryCheckCustomizeTagInformationHis:
    '/api-wap/audi-eshop/api/v1/customizeTagInformation/checkCustomizeTagInformationHis',
  // 绑定车辆信息获取
  getBindCarInfoList:
    '/api-wap/audi-after-service/api/afterSale/getBindCarInfoList',
  // 车型解析
  getFavoriteCarData: '/api-wap/audi-miniprogram/api/v1/favoritecar',

  // 获客助手
  // 申请获客助手URL或小程序二维码
  getCustomerHelper:
    '/api-wap/audi-after-service/api/v1/getCustomerHelper/applyAcquireLink',
  getCustomerHelperClick:
    '/api-wap/audi-after-service/api/v1/getCustomerHelper/click',
  getConsentByType:
    '/mos/security/consent/api/v1/consent/actions/getConsentByType?typeCode=personal_information&brand=audi&deviceType=APP',
  getNewConsentByType:
    '/mos/security/consent/api/v1/consent/actions/getConsentByType?typeCode=app_privacy&brand=audi&deviceType=APP',

  // 高德服务
  afterServiceScGeocodeGeo: '/api-wap/audi-after-service/api/v1/sc/geocodeGeo',
  afterServiceScGeo: '/api-wap/audi-after-service/api/v1/sc/regeo',
  afterServiceScPlaceText: '/api-wap/audi-after-service/api/v1/sc/placeText'
}
