const storage = {
  set(key, value) {
    localStorage.setItem(key, value)
  },
  get(key) {
    return localStorage.getItem(key)
  },
  setPlus(key, value) {
    let obj = JSON.stringify({ value })
    localStorage.setItem(key, obj)
  },
  getPlus(key) {
    /* @getPlus返回值与@setPlus存储时的类型保持一致 */
    return JSON.parse(localStorage.getItem(key) || JSON.stringify({ value: undefined })).value
  },
  getForIndex(index) {
    return localStorage.key(index)
  },
  getKeys() {
    const items = this.getAll()
    const keys = []
    for (let index = 0; index < items.length; index++) {
      keys.push(items[index].key)
    }
  },
  getLength() {
    return localStorage.length
  },
  getSupport() {
    return (typeof (Storage) !== 'undefined')
  },
  remove(key) {
    localStorage.removeItem(key)
  },
  removeAll() {
    localStorage.clear()
  },
  getAll() {
    const len = localStorage.length
    const arr = []
    for (let i = 0; i < len.length; i++) {
      const getKey = localStorage.key(i)
      const getVal = localStorage.getItem(getKey)
      arr[i] = {
        key: getKey,
        val: getVal
      }
    }
    return arr
  }
}

export default storage
