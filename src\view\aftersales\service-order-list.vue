<template>
  <div
    name="orderList"
    class="orderList"
  >
    <page-load
      @onRefresh="onRefresh"
      :pulldown.sync="pulldown"
      :pullupstate="false"
    >
      <div
        class="order_content"
        v-for="(item, index) in orderList"
        :key="index"
        @click.stop="toOrderDetail(item)"
      >
        <div class="order_content_left">
          <img
            :src="
              ((item.dealerThumbnailUrl || '').includes('http')
                ? item.dealerThumbnailUrl
                : BaseConfigrationOssHost + item.dealerThumbnailUrl) | audiwebp
            "
          >
        </div>
        <div class="order_content_right">
          <div class="order_content_right_top">
            <span class="order_name">{{ setOrderName(item) }}</span>
            <span class="order_state">{{ showStatusName(item) }}</span>
          </div>
          <div class="order_content_right_mid">
            {{ item.placeOrderTime }}
          </div>
          <div
            class="order_content_right_bottom"
            v-if="isShowCancel(item)"
          >
            <div @click.stop="onCancelOrder(item)">
              <AudiButton
                :text="'取消订单'"
                color="white"
                font-size="14px"
                height="30px"
                width="80px"
              />
            </div>
          </div>
        </div>
      </div>
      <finishedTemplate finishedText="抵达终点" v-if="orderList.length"></finishedTemplate>
      <div
        v-show="showNotData"
        class="not_data"
      >
        <div>
          <img
            :src="fromType ? require('@/assets/img/wenjuanNotDataa.png')  :  require('@/assets/img/empty-new-img.png')"
            alt=""
          >
        </div>
        <p>暂无内容</p>
      </div>
    </page-load>

    <van-popup
      v-if="modalshow"
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal">
        <div
          class="modal-title"
        >
          {{ '请选择取消以下哪项服务？' }}
        </div>
        <div
          class="modal-content"
        >
          {{ content }}
        </div>
        <div>
          <div
            class="service-line"
            style=" margin-top: 8px"
            v-if="onCliickOrderItem.deliverVo.takeIsCancel === 1"
          >
            <div class="title-bold">
              仅取消取车
            </div>
            <div
              class="btn-change"
              @click.stop="onCancelTake"
            >
              <img
                class="btn-icon"
                :src="isTake ? ctiveRadioIcon : inactiveRadioIcon"
              >
            </div>
          </div>
          <div
            class="service-line"
            style=" margin-top: 8px"
            v-if="onCliickOrderItem.deliverVo.sendIsCancel === 1"
          >
            <div class="title-bold">
              仅取消送车
            </div>
            <div
              class="btn-change"
              @click.stop="onCancelSend"
            >
              <img
                class="btn-icon"
                :src="isSend ? ctiveRadioIcon : inactiveRadioIcon"
              >
            </div>
          </div>
          <div
            class="service-line"
            style=" margin-top: 8px"
            v-if=" onCliickOrderItem.deliverVo.takeIsCancel === 1 && onCliickOrderItem.deliverVo.sendIsCancel === 1"
          >
            <div class="title-bold">
              全部取消
            </div>
            <div
              class="btn-change"
              @click.stop="onCancelTakeAndSend"
            >
              <img
                class="btn-icon"
                :src="isAll ? ctiveRadioIcon : inactiveRadioIcon"
              >
            </div>
          </div>
        </div>
        <div
          class="modal-confirm center"
          @click.stop="onConfirm"
        >
          {{ '确定' }}
        </div>
        <div
          class="modal-cancel center"
          @click.stop="onCancel"
        >
          {{ '取消' }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  getAfterSaleFindByPage,
  getCarConfig,
  getAfterSaleCancel,
  postChargingPileCancel
} from '@/api/api'
import { callNative } from '@/utils'
import url from '@/config/url'
import AudiButton from '@/components/audi-button'
import { Popup } from 'vant'
import Vue from 'vue'
import PageLoad from '../../components/page-load.vue'

import finishedTemplate from '@/components/finishedTemplate.vue'
Vue.use(Popup)

export default {
  name: 'OrderList',
  components: { PageLoad, AudiButton,finishedTemplate },
  data() {
    return {
      pulldown: false, // 下拉
      showNotData: false,
      orderList: [],
      BaseConfigrationOssHost: url.BaseOssHost,

      modalshow: false,
      title: '',
      content: '',
      orderId: '',

      ctiveRadioIcon: require('../../assets/img/radio_checked.png'),
      inactiveRadioIcon: require('../../assets/img/radio_normal.png'),
      isTake: false,
      isSend: false,
      isAll: false,
      onCliickOrderItem: {},
      type: 0,
      fromType:this.$route.query?.fromType === 'fromPurple',
    }
  },

  mounted() {
    const { type } = this.$route.query
    if (type == 1) {
      this.$store.state.title = '服务预约列表'
    } else if (type == 2) {
      this.$store.state.title = '取送车列表'
    } else {
      this.$store.state.title = '充电桩服务'
    }
    this.type = type

    this.getCarOrderList()
    // this.appCallJs()
  },
  watch: {},
  methods: {
    // APP调用刷新
    // appCallJs() {
    //   this.onRefresh()
    // },
    // 下拉刷新
    async onRefresh() {
      await this.getCarOrderList()
      this.pulldown = !this.pulldown
    },

    // 获取车订单
    async getCarOrderList() {
      this.$store.commit('showLoading')
      const { data } = await getAfterSaleFindByPage({ type: this.type })
      this.$store.commit('hideLoading')

      this.orderList = data.data.data

      if (this.orderList.length > 0) {
        // const res = await Promise.all(this.orderList.map((i) => this.getOrderCarConfig(i.carCustomId)))
        // console.log(this.BaseConfigrationOssHost)
        // res.forEach((e, i) => {
        //   this.$set(this.orderList[i], 'modelNameCn', e.modelNameCn)
        //   this.$set(this.orderList[i], 'headImageUrl', e.headImageUrl)
        // })
        this.showNotData = false
      } else {
        this.showNotData = true
      }
    },
    showStatusName(item) {
      if (item.type === 1) {
        return item.statusName
      }
      if (item.type === 2 && item.deliverVo) {
        if (item.deliverVo.subType === 20131010) {
          return item.deliverVo.takeOrderStatusName
        } if (item.deliverVo.subType === 20131020) {
          return item.deliverVo.sendOrderStatusName
        }
        if (item.deliverVo.takeOrderStatus === 20101130 && item.deliverVo.sendOrderStatus === 20101130) {
          return '订单已取消'
        } if (item.deliverVo.takeOrderStatus === 20101120 && item.deliverVo.sendOrderStatus === 20101120) {
          return '订单已完成'
        }
        if (item.deliverVo.takeOrderStatus !== 20101130 && item.deliverVo.takeOrderStatus !== 20101120) {
          return `取车-${item.deliverVo.takeOrderStatusName}`
        }
        return `送车-${item.deliverVo.sendOrderStatusName}`
      } if (item.type === 3) {
        return item.statusName
      }
    },
    setOrderName(item) {
      if (item.type === 1) {
        return '服务预约'
      }
      if (item.type === 2 && item.deliverVo) {
        if (item.deliverVo.subType === 20131010) {
          return '取车服务'
        } if (item.deliverVo.subType === 20131020) {
          return '送车服务'
        }
        return '取送车服务'
      }
      if (item.type === 3) {
        return '充电桩安装'
      }
    },

    isShowCancel(item) {
      // 售后
      if (item.type === 1) {
        return item.isCancel === 1
      } if (item.type === 2 && item.deliverVo) {
        // 取送车
        if (item.deliverVo.subType === 20131010) {
          return item.deliverVo.takeIsCancel === 1
        } if (item.deliverVo.subType === 20131020) {
          return item.deliverVo.sendIsCancel === 1
        }
        return (
          item.deliverVo.takeIsCancel === 1
            || item.deliverVo.sendIsCancel === 1
        )
      } if (item.type === 3) {
        return item.isCancel === 1
      }
    },

    async toOrderDetail(item) {
      if (item.type === 1) {
        // 售后预约
        // this.$router.push({
        //   path: "/aftersales/service-appointment-order-detail",
        //   query: {
        //     appoId: item.appoId,
        //   },
        // });

        const { origin, pathname } = window.location
        const url = `${origin}${pathname}#/aftersales/service-appointment-order-detail?appoId=${item.appoId}`
        callNative('audiOpen', { path: url })
      } else if (item.type === 2) {
        // 取送车
        this.$router.push({
          path: '/aftersales/deliver-car-order-detail',
          query: {
            appoId: item.appoId
          }
        })
        // const { origin, pathname } = window.location
        // const url = `${origin}${pathname}#/aftersales/deliver-car-order-detail?appoId=${item.appoId}`
        // callNative('audiOpen', { path: url })
      } else if (item.type === 3) {
        // 充电桩服务
        this.$router.push({
          path: '/charging/install-info',
          query: {
            appoId: item.appoId
          }
        })
        // 区分整车订单和充电墙合订单标识  copOrderId
        // const { origin, pathname } = window.location
        // const url = `${origin}${pathname}#/aftersales/deliver-car-order-detail?appoId=${item.appoId}`
        // callNative('audiOpen', { path: url })
      }

      // // 跳转详情
      //
    },

    async getOrderCarConfig(carCustomId) {
      const { data } = await getCarConfig({ ccid: carCustomId })
      if (data.data) {
        return data.data.configDetail.carModel
      }
      return ''
    },

    onCancelTake() {
      this.isTake = !this.isTake
      if (this.isTake) {
        this.isSend = false
        this.isAll = false
      }
    },
    onCancelSend() {
      this.isSend = !this.isSend
      if (this.isSend) {
        this.isTake = false
        this.isAll = false
      }
    },
    onCancelTakeAndSend() {
      this.isAll = !this.isAll
      if (this.isAll) {
        this.isTake = false
        this.isSend = false
      }
    },

    onCancel() {
      this.isTake = false
      this.isSend = false
      this.isAll = false
      this.modalshow = false
    },
    async onConfirm() {
      let type = 0
      if (this.isAll) {
        this.orderId = `${this.onCliickOrderItem.deliverVo.takeOrderId},${this.onCliickOrderItem.deliverVo.sendOrderId}`
        type = 4
      } else if (this.isTake) {
        this.orderId = this.onCliickOrderItem.deliverVo.takeOrderId
        type = 1
      } else if (this.isSend) {
        this.orderId = this.onCliickOrderItem.deliverVo.sendOrderId
        type = 3
      } else {
        this.modalshow = false
        return
      }

      this.$router.push({
        path: '/aftersales/cancel-service-order',
        query: {
          orderId: this.orderId,
          type: type
        }
      })
      this.modalshow = false
      this.isTake = false
      this.isSend = false
      this.isAll = false
    },
    // 取消订单
    async onCancelOrder(item) {
      this.onCliickOrderItem = item

      if (item.type === 2 && item.deliverVo.subType === 20131030) {
        if (item.deliverVo.takeIsCancel === 0) {
          // 取车不可取消了
          this.title = '请问您是否确认取消送车服务？'

          if (item.deliverVo.takeOrderStatus !== 20101130) {
            this.content = '注意：取车服务已开始，无法取消'
          } else {
            this.content = '注意：取车服务已取消'
          }
          this.isSend = true
        } else if (item.deliverVo.sendIsCancel === 0) {
          // 取车不可取消了
          this.title = '请问您是否确认取消取车服务？'
          if (item.deliverVo.sendOrderStatus !== 20101130) {
            this.content = '注意：送车服务已开始，无法取消'
          } else {
            this.content = '注意：送车服务已取消'
          }
          this.isTake = true
        } else {
          this.title = '请选择取消以下哪项服务？'
          this.content = ''
        }
        this.modalshow = true
      } else {
        // 调用APP Dialog
        callNative('popup', {
          type: 'alert',
          alertparams: {
            title: '',
            desc: '是否确定取消订单？',
            actions: [
              {
                type: 'fill',
                title: '确定'
              },
              {
                type: 'stroke',
                title: '取消'
              }
            ]
          }
        }).then((data) => {
          if (data.type === 'fill') {
            // 点击确定
            this.cancelOrder(item)
          }
        })
      }
    },
    async cancelOrder(item) {
      if (item.type === 1) {
        const { data } = await getAfterSaleCancel({ orderId: item.appoId,version:'v2' })
      } else if (item.type === 2) {
        // 取送车
        let orderId = ''
        let type = 1
        if (item.deliverVo.subType === 20131010) {
          orderId = item.deliverVo.takeOrderId
          type = 1
        } else if (item.deliverVo.subType === 20131020) {
          orderId = item.deliverVo.sendOrderId
          type = 3
        } else {
          orderId = `${item.deliverVo.takeOrderId},${item.deliverVo.sendOrderId}`
          type = 4
        }
        this.$router.push({
          path: '/aftersales/cancel-service-order',
          query: {
            orderId: orderId,
            type: type
          }
        })
      } else if (item.type === 3) {
        // 充电桩订单取消

        const { data } = await postChargingPileCancel({ appointmentOrderId: item.appoId })
      }
      this.onRefresh()
    }
  }
}
</script>

<style lang="less" scoped>
.not_data {
  text-align: center;
  padding-top: 150px;
  img {
    width: 160px;
    height: 160px;
  }
  p{
    height: 20px;
    font-size: 13px;
    margin: 8px 0 0;
    font-weight: 400;
    color: #B3B3B3;
    line-height: 20px;
  }
}
.orderList {
  padding: 16px;
}

.order_content {
  display: flex;
  flex-direction: row;
  border-bottom: 1px #e5e5e5 solid;
  padding-bottom: 16px;
  padding-top: 16px;
  &_left {
    width: 102px;
    height: 102px;
    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }
  &_right {
    flex: 1;
    margin-left: 10px;
    margin-top: 5px;
    &_top {
      overflow: hidden;

      .order_name {
        font-size: 16px;
        font-family: "Audi-WideBold";
        float: left;
      }
      .order_state {
        float: right;
        font-size: 14px;
        color: #666666;
      }
    }

    &_mid {
      font-size: 12px;
      color: #666666;
      margin-top: 8px;
    }

    &_bottom {
      display: flex;
      flex-direction: row-reverse;
      margin-top: 38px;
    }
  }
}


  .center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ._modal {
    width: 343px;
    background: #FFFFFF;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    padding: 24px 0;

    .modal-title {
      font-size: 18px;
      font-weight: 500;
      color: #1A1A1A;
      margin-bottom: 16px;
      font-family: 'Audi-WideBold';
    }

    .modal-content {
      font-size: 14px;
      color: #333333;
      line-height: 18px;
      padding: 0 16px;
      font-family: 'Audi-Normal';
    }
    .service-line {
      width: 295px;
      height: 48px;
      background: #f2f2f2;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-bold {
        padding-left: 16px;
        font-size: 16px;
        color: #000;
        font-weight: normal;
        font-family: 'Audi-Normal';

      }
      .btn-change {
        padding-right: 16px;
        .btn-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
    .modal-confirm {
      margin-top: 24px;
      width: 85%;
      height: 56px;
      background: #1A1A1A;
      font-size: 16px;
      color: #FFFFFF;
    }

    .modal-cancel {
      width: 85%;
      border: 1px solid #1A1A1A;
      height: 56px;
      background: #fff;
      font-size: 16px;
      color: #000;
      margin-top: 8px;
    }

    .modal-bold-content {
      font-size: 18px;
      color: #1A1A1A;
      line-height: 32px;
      padding: 0 25px;
      font-weight: normal;
      font-family: "Audi-WideBold";
    }
  }
</style>
