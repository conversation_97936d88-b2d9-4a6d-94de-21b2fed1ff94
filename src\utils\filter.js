/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-07-06 11:35:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-11-01 20:00:42
 * @FilePath     : \src\utils\filter.js
 * @Descripttion : global filter
 */
import Vue from 'vue'
import dayjs from 'dayjs'
import { loadImage } from './webpImageLoader'

Vue.filter('prefixFormatPrice', (value, prefix = '¥') => {
  if (!value) {
    return value === '' ? '' : 0
  }
  // 返回处理后的值
  const price = value
    .toString()
    .replace(/\d+/, (n) => n.replace(/(\d)(?=(\d{3})+$)/g, ($1) => `${$1},`))
  if (price.indexOf('-') !== -1) {
    const priceArray = price.split('-')
    return `-${prefix}${priceArray[1]}`
  }
  return `${price.includes('预订价') ? '' : prefix}${price}`
})

Vue.filter('dayjsFilter', (dataStr, pattern = 'YYYY-MM-DD HH:mm:ss') =>
  dayjs(dataStr).format(pattern)
)

const formatPrice = (value) =>
  value
    .toString()
    .replace(/\d+/, (n) => n.replace(/(\d)(?=(\d{3})+$)/g, ($1) => `${$1},`))

Vue.filter('formatPrice', (value) => {
  if (!value) {
    return 0
  }
  // 返回处理后的值
  const res = formatPrice(value)
  return res
})

Vue.filter('finalFormatPriceDesc', (value) => {
  if (!value || value === 0 || value === "0") return '价格已包含'
  // 返回处理后的值
  const res = formatPrice(value)
  return `¥${res}`
})

Vue.filter('finalFormatPriceDescPrefixPlus', (value) => {
  if (!value || value === 0 || value === "0") return '价格已包含'
  // 返回处理后的值
  const res = formatPrice(value)
  return `+¥${res}`
})

Vue.filter('finalFormatPriceDescPrefixMinus', (value) => {
  if (!value || value === 0 || value === "0") return '价格已包含'
  // 返回处理后的值
  const res = formatPrice(value)
  return `-¥${res}`
})

Vue.filter('finalFormatPrice', (value) => {
  if (!value && value !== 0) return '价格已包含'
  // 返回处理后的值
  const res = formatPrice(value)
  return `￥ ${res}`
})

Vue.filter('formatDistance', (value) => {
  // 返回处理后的值
  if (value === null) {
    return '0m'
  }
  if (value < 1000) {
    return `${Math.round(value)}m`
  }
  return `${Math.round((value / 1000) * 10) / 10}KM`
})
Vue.filter('selectAddress', (value) => {
  // 返回处理后的值
  if (value === null) {
    return '0m'
  }
  if (value < 1000) {
    return `${Math.round(value)}m`
  }
  return `${Math.round((value / 1000) * 10) / 10}km`
})

const getUrlParamObj = () => {
  let url = window.location.href
  let theRequest = new Object()
  let strs = url.split(/[?&#\/]/)
  strs.forEach((e) => {
    if (e.indexOf('=') > -1) {
      let splitArr = e.split('=')
      theRequest[splitArr[0]] = splitArr[1]
    }
  })
  return theRequest
}

const paramsObj = getUrlParamObj()
if (paramsObj['nsenv'] && paramsObj['nsenv'] === 'audiios') {
  window.audiios = true
}

Vue.filter('audiwebp', (path) => {
  return loadImage(path)
})

Vue.filter('osswebp', (path) => {
  return loadImage(path, true)
})

Vue.filter('phoneNum', (phone) => {
  console.log('phoneNum', phone)
  if (phone) {
    return `${phone.slice(0, 3)}****${phone.slice(7)}`
  } else {
    return ''
  }
})

Vue.filter('idCardNum', (idCard) => {
  if (idCard) {
    let maskedIdCard = '**************' + idCard.substring(14)
    return maskedIdCard
  } else {
    return ''
  }
})


