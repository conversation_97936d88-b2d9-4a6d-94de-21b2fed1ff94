<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-12-12 10:25:44
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-12-10 18:07:23
 * @FilePath     : \src\view\contract\guide.vue
 * @Descripttion : 购车合同签署说明
-->
<template>
  <div
    :class="
      ['contract-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`, height: `calc(100vh - ${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px - 34px)`}"
    >
      <div
        class="contract-box"
        data-flex="dir:top"
      >
        <dl
          class="dl"
          data-flex="dir:top"
          data-block
        >
          <dt class="dt">
            如您当前未完成线上身份认证，您可继续线上签署或联系您的门店管家:
          </dt>
          <dd class="dd">
            <ul>
              <li class="li">
                <h4 class="h4">
                  线上签署
                </h4>
                <p class="p">
                  点击“继续线上签署”，尝试再次进行线上身份认证，认证通过后继续线上签署购车合同。
                </p>
              </li>
              <!-- <li class="li">
                <h4 class="h4">
                  线下签署<span class="spu">（线下签署涉及邮寄等步骤，流程可能较长）</span>
                </h4>
                <p class="p">
                  点击“线下签署”下载待签署购车合同文件，联系代理商奥迪管家进行线下签署购车合同。
                </p>
              </li> -->
            </ul>
          </dd>
        </dl>
        <div
          class="affirm-order-info-box"
        >
          <!-- <div class="lan-button-box black-button ghost-button line-two-cols">
            <van-button
              class="lan-button"
              @click="goToContractPage"
            >
              线下签署
            </van-button>
          </div> -->
          <div
            class="lan-button-box black-button"
          >
            <van-button
              @click="goToContractPage(1)"
              class="lan-button"
            >
              继续线上签署
            </van-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import {
  Button
} from 'vant'
import HeaderCustom from '@/components/header-custom.vue'
import wx from 'weixin-js-sdk'

Vue.use(Button)
export default {
  components: {
    'header-custom': HeaderCustom
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      orderId: 0,
      seriesCode: '',
      destroyNativeBack: false
    }
  },
  created() {
  },
  methods: {
    goToContractPage(isTurnOffLine = 0) {
      const {
        orderId, already, env, ccid
      } = this.$route.query
      // if (isTurnOffLine !== 1 && env === 'minip') {
      //   // return this.handleGoToPageDownloadApp()
      //   const { origin, pathname } = location
      //   const url = `/pages/order/contract/index?orderId=${orderId}&ccid=${ccid}&isTurnOffLine=1&prod=${process.env.VUE_APP_ENV === 'pre' ? 0 : 1}&url=${encodeURIComponent(`${origin}${pathname}#/contract/enterprise/online`)}`
      //   return wx.miniProgram.navigateTo({ url })
      // }
      this.$router.push({
        name: 'contract-info',
        query: { orderId, ...(isTurnOffLine === 1 ? { already: !already ? 1 : 0 } : { turnOffLine: 1 }) }
      })
    },
    handleLeftBack() {
      const { orderId } = this
      const { already,orderType } = this.$route.query
      this.$router.push({ name: 'model-detail', query: { orderId, already,orderType } })
    },
    handleGoToPageDownloadApp() {
      const { orderId, ccid,orderType } = this.$route.query
      this.$router.push({
        name: 'order-guide-page-download-app',
        query: { orderId, ccid,orderType}
      })
    }
  }
}
</script>
<style lang="less" scoped>
.contract-wrapper {
  background-color: #f0f2f5;
  .contract-box {
    height: 100%;
    font-size: 14px;
    color: #333;
    line-height: 22px;
    .dl {
      margin: 0;
      padding: 16px;
      .dd {
        margin: 0;
        .li {
          margin: 24px 0 0 0;
          .h4, .p {
            margin: 0 0 4px 0;
          }
          .h4 {
            font-family: 'Audi-WideBold';
            font-weight: normal;
            .spu {
              color: #999;
              font-size: 12px;
            }
          }
          &:last-child {
            margin-top: 36px;
          }
        }
      }
    }
  }
  .affirm-order-info-box {
    padding: 16px;
  }
}
</style>
