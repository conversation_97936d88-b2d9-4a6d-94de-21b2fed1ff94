/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-04-29 15:04:03
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-02-21 09:14:30
 * @FilePath     : \src\view\limited-number\select-number\index.js
 * @Descripttion : 典藏号首页
 */
import Vue from 'vue'
import {
  Toast, NumberKeyboard, Popup, CountDown, Circle
} from 'vant'
import dayjs from 'dayjs'
import HeaderCustom from '@/components/header-custom.vue'
import audiButton from '@/components/audi-button.vue'
import {
  getLimitedNumberList, bindLimitedNumber, getMineLimitedNumberStep, getLimitedNumberCount, pitchOnLimitedNumber
} from '@/api/limited-number'
import {
  paramsStrict, callNative, delay, combiningStrings
} from '@/utils'
import { LIMIT_NUMBERS, ORDER_STATUS_DISTRICT } from '@/config/conf.data'

Vue.use(Toast).use(NumberKeyboard).use(Popup).use(CountDown)
  .use(Circle)
export default {
  components: {
    'header-custom': HeaderCustom,
    'audi-button': audiButton
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      limitedNumber: '',
      limitedNumberIllegality: false,
      seriesCode: '',
      modelLineCode: '',
      numberLength: 3,
      focused: false,
      orderId: 0,
      step: 0,
      dispositionBtnEnabled: false,
      dispositionBtnText: '确认',
      popShow: false,
      popMainText: '',
      popMainTips: '',
      numberPool: [], // 号码池
      time: 0,
      burningTime: 60 * 30 * 1000,
      popBtnReign: [],
      pitchNumber: 0,
      refreshIntegral: 0,
      spendingBtnStatus: false,
      setNumberCount: 0,
      surplusLimitNumCount: 0,
      stopGetLimitedNumber: true,
      numberNotEnough: 1,
      loadingRate: 0,
      popCircle: false,
      nickCode: '',
      pageEleOffsetHeight: 0,
      orderStatus: ''
    }
  },
  computed: {
    numbers() {
      return this.limitedNumber.split('')
    },
    cursorIndex() {
      return this.limitedNumber.length
    }
  },
  watch: {
    // 输入Number监听处理
    limitedNumber(number) {
      const { numberLength, step } = this
      this.limitedNumber = /^[0-9]+$/.test(number) ? number : number.replace(/[^\d]/g, '')
      this.limitedNumberIllegality = number === '000'
      if (step === 1) {
        this.dispositionBtnEnabled = !!(number !== '000' && number?.length === numberLength)
      }
      // if (number?.length > numberLength) {
      //   this.limitedNumber = number.slice(0, numberLength)
      // }
    },
    focused(status) {
      if (status) {
        this.$nextTick(() => {
          // const { offsetHeight } = this.$refs.numberKeyboard.$el || 250
          this.pageEleOffsetHeight = 220 // offsetHeight
        })
      } else {
        this.pageEleOffsetHeight = 0
      }
    }
    // loadingRate(rate) {
    //   if (rate >= 100) {
    //     this.popCircle = false
    //     this.handleProcessData('countdown')
    //   }
    // }
  },
  created() {
    this.handleProcessData()
  },
  methods: {
    // pop弹窗按钮
    handleClickPopupBtn(index) {
      const {
        step, handleGetLimitedNumber, popBtnReign, spendingBtnStatus
      } = this
      // 获取随机限量号（第二步）
      // if (step === 1) {
      //   handleGetLimitedNumber(2)
      // } else {
      console.log(index)
      this.popMainTips = ''
      const { action, name, query } = popBtnReign[index]
      if (action === 'closed') {
        if (spendingBtnStatus) {
          this.spendingBtnStatus = false
        }
        this.popShow = false
      } else if (action === 'handleStepSet') {
        this.popShow = false
        this.step = 2
      } else if (action === 'push') {
        this.$router.push({ name, query })
      } else if (action === 'callLimitedNumberList') {
        this.handleGetLimitedNumber(step)
      } else if (action === 'callNative') {
        this.handleConfirmNativeRoutePath(name)
      } else if (action === 'stopGetLimitedNumber') {
        this.stopGetLimitedNumber = false
        handleGetLimitedNumber(3)
      }
      // }
    },
    // 选择数字
    async handlePitchOnNumber(number) {
      const { orderId, nickCode } = this
      const { data: { data } } = await pitchOnLimitedNumber({ orderId, limitedNumber: number }, combiningStrings(nickCode)) || ''
      if (!data) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '限量号选择失败，请稍后在试',
          forbidClick: true
        })
      }
      this.pitchNumber = number
    },
    // 倒计时完成回调
    handleCountDownFinished() {
      const {
        step, handleProcessData
      } = this
      if (step === 1) {
        return
      }
      delay(() => {
        handleProcessData('countdown')
        console.log('delay 2000 ms')
      }, 2000)
      // this.popCircle = true
    },
    // 获取限量号
    async handleGetLimitedNumber(number, cid = '') {
      const {
        orderId, step, popShow, spendingBtnStatus, stopGetLimitedNumber, numberPool, burningTime, dispositionBtnEnabled, nickCode
      } = this

      if (step > 1 && !numberPool.length) {
        return
      }

      if (number !== 2 && popShow) {
        this.popShow = false
      }
      if ([2, 3].includes(number)) {
        this.spendingBtnStatus = true
      }

      if (number === 3 && stopGetLimitedNumber) {
        console.log('step:', number, '查询限量号剩余数量开始')
        const { data: { data, code, message } } = await getLimitedNumberCount({ orderId }, combiningStrings(nickCode)) || ''
        console.log('step:', number, '查询限量号剩余数量', data)
        if (code === '00') {
          // this.numberNotEnough = data > (number === 2 ? 3 : 20) ? data : 0
        }

        if (!data || code === '04') {
          this.spendingBtnStatus = false
          this.popShow = true
          this.popMainText = message
          this.popBtnReign = [{
            text: '我知道了', color: 'black', enabled: true, action: 'closed'
          }]
          this.numberNotEnough = 0
          return
        }

        if (code === '02') {
          this.popShow = true
          this.popMainText = message || '剩余限量号不足，仅剩一次更新备选号码的机会，且号码重复率高，是否继续'
          this.popBtnReign = [
            {
              text: '取消', enabled: true, action: 'closed'
            },
            {
              text: '继续选号', color: 'black', enabled: true, action: 'stopGetLimitedNumber'
            }
          ]
          this.spendingBtnStatus = false
          if (stopGetLimitedNumber) {
            return
          }
        }
      }

      const { data: { data, code, message } } = await getLimitedNumberList({ orderId, schedule: number }, combiningStrings(nickCode))

      const {
        limitedNumberList, refreshIntegral, firstSelectTime, refreshUsed
      } = data || ''

      this.spendingBtnStatus = false
      this.numberNotEnough = refreshUsed

      if (code === '00') {
        if (!dispositionBtnEnabled) {
          this.dispositionBtnEnabled = true
        }
        if (number === 3 || cid === 'setStep') {
          this.step = number
        }
        // 每次刷新号码，重新计时
        this.time = burningTime
        if (firstSelectTime) {
          const millisecond = this.handlerDiffDateMillisecond(firstSelectTime, burningTime)
          this.time = millisecond > 0 ? millisecond : burningTime
        }
        // 重置计时器
        number > 2 && this.$refs.countDown.reset()
        this.numberPool = limitedNumberList ?? []
        this.pitchNumber = [...limitedNumberList].shift()
        if (refreshIntegral) {
          this.refreshIntegral = refreshIntegral
        }

        if (cid === '06') {
          this.popShow = true
          this.popMainText = '您输入的意向号码已被占用，系统将为您随机选取3个备选限量号'
          this.popBtnReign = [{
            text: '查看限量号', color: 'black', enabled: true, action: 'handleStepSet'
          }]
        }
      } else if (code === '02') {
        this.popShow = true
        this.popMainText = message
        this.popBtnReign = [
          {
            text: '取消', enabled: true, action: 'closed'
          },
          {
            text: '继续选号', color: 'black', enabled: true, action: 'callLimitedNumberList'
          }
        ]
      } else if (code === '03') {
        this.popShow = true
        this.popMainText = message
        this.numberNotEnough = 0
        this.popBtnReign = [
          {
            text: '取消', enabled: true, action: 'closed'
          },
          {
            text: '获取奥金', color: 'black', enabled: true, action: 'callNative', name: 'scaudi://mine/sign/home'
          }
        ]
      } else if (code === '04') {
        this.popShow = true
        this.popMainText = message
        this.popBtnReign = [{
          text: '我知道了', color: 'black', enabled: true, action: 'closed'
        }]
      } else if (code === '07') {
        this.popShow = true
        this.popMainText = message
        this.popBtnReign = [{
          text: '我知道了',
          color: 'black',
          enabled: true,
          action: 'push',
          name: 'money-detail',
          query: {
            orderId
          }
        }]
      } else {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message,
          forbidClick: true
        })
      }
    },
    handleIntegralBuyBtn() {
      const { spendingBtnStatus, numberNotEnough } = this
      if (spendingBtnStatus || !numberNotEnough) {
        return
      }
      this.handleGetLimitedNumber(3)
    },
    // 跳转原生路由页面
    handleConfirmNativeRoutePath(path = '') {
      if (!path) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '跳转原生路由页面不存在',
          forbidClick: true
        })
      }
      callNative('openRoutePath', { path })
    },
    // 初始化 处理数据
    async handleProcessData(action = '') {
      const {
        dispositionBtnEnabled, pitchNumber, limitedNumber
      } = this
      const {
        orderId, seriesCode, modelLineCode, orderStatus
      } = paramsStrict(this.$route.query)
      this.orderStatus = orderStatus
      this.orderId = orderId
      this.seriesCode = seriesCode
      this.modelLineCode = modelLineCode
      const seriesData = LIMIT_NUMBERS.filter((i) => i.seriesCode === seriesCode)[0] || []
      const { nickCode } = seriesData.seriesCode === 'G4' ? '' : seriesData
      const { burningTime: { seconds } } = seriesData
      const burningTime = (seconds || 60 * 30) * 1000
      this.burningTime = burningTime

      nickCode && (this.nickCode = nickCode)
      const {
        data: {
          data: {
            schedule, firstSelectTime, refreshIntegral, limitNumberHis, numberNotEnough, selectHis, refreshUsed, firstRandomResult
          }, code, message
        }
      } = await getMineLimitedNumberStep({ orderId }, combiningStrings(nickCode))

      if (firstRandomResult !== 1 && schedule === 1) {
        this.handleGetLimitedNumber(2, 'setStep')
        return
      }

      if (action === 'countdown' && schedule === 2) {
        this.popShow = true
        this.popMainText = '当前限量号全部被绑定，您可以选择更新备选号'
        this.popBtnReign = [{
          text: '我知道了', color: 'black', enabled: true, action: 'closed'
        }]
      }

      if (schedule === 4) {
        const pitchOnNumber = pitchNumber || limitedNumber
        this.popShow = true
        this.popMainText = '选号倒计时结束，系统默认为您选择了限量号'
        this.popBtnReign = [{
          text: '查看限量号证书',
          color: 'black',
          enabled: true,
          action: 'push',
          name: 'limited-number-credentials',
          query: {
            orderId, seriesCode, modelLineCode, limitedNumber: pitchOnNumber, orderStatus
          }
        }]
      }

      if (schedule === 5) {
        this.popShow = true
        this.popMainText = '限量号全部被绑定，请您联系奥迪管家'
        this.popBtnReign = [{
          text: '我知道了',
          color: 'black',
          enabled: true,
          action: 'push',
          name: 'money-detail',
          query: {
            orderId
          }
        }]
      }

      this.step = schedule > 3 ? 3 : schedule

      if ([2, 3].includes(schedule) && firstSelectTime) {
        const millisecond = this.handlerDiffDateMillisecond(firstSelectTime, burningTime)
        this.time = millisecond > 0 ? millisecond : burningTime
      }

      if (limitNumberHis) {
        const numberPool = limitNumberHis.split(',')
        if (schedule === 3) {
          const numberPoolLength = numberPool.length || 0
          if (numberPoolLength >= 8 && numberPoolLength < 12) {
            this.pageEleOffsetHeight = 80
          }
          if (numberPool.length > 12) {
            this.pageEleOffsetHeight = 180
          }
        }
        this.numberPool = numberPool
        this.pitchNumber = [...numberPool].shift()
      }
      if (refreshIntegral) {
        this.refreshIntegral = refreshIntegral
      }

      if (!dispositionBtnEnabled && schedule !== 1) {
        this.dispositionBtnEnabled = true
      }

      [2, 3].includes(schedule) && (this.numberNotEnough = refreshUsed)

      if (selectHis) {
        this.pitchNumber = selectHis
      }

      // if (schedule > 3) {
      //   this.popShow = true
      //   this.popMainText = '您已经绑定过限量号了，请勿重复操作'
      //   this.popBtnReign = [{
      //     text: '我知道了', color: 'black', enabled: true, action: 'push', name: 'money-detail', query: { orderId }
      //   }]
      // }
    },
    // 计算当前剩余时间(millisecond)
    handlerDiffDateMillisecond: (startTime, burningTime) => (burningTime - dayjs().diff(dayjs(startTime), 'second') * 1000),
    // 计算当前剩余时间(second)
    handlerDiffDateSecond: (startTime) => dayjs().diff(dayjs(startTime), 'second'),
    // 禁用方向键 ↑、←、→、↓、Home、End
    handleForbidMoveCursor(event) {
      if (event.keyCode >= 35 && event.keyCode <= 40) {
        event.preventDefault()
      }
    },
    // 提交限量号
    async handleDispositionBtn() {
      const {
        limitedNumber, numberLength, orderId, step, dispositionBtnEnabled, pitchNumber, nickCode, numberPool, seriesCode, modelLineCode, handleGetLimitedNumber, orderStatus
      } = this
      if (!dispositionBtnEnabled || (step > 1 && !numberPool.length)) {
        console.log('不满足条件[dispositionBtnEnabled,step > 1 && !numberPool.length]')
        return
      }
      if ((step === 1 && limitedNumber.length < numberLength) || (!pitchNumber && !limitedNumber)) {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '请输入或选择正确的号码',
          forbidClick: true
        })
        return
      }
      this.dispositionBtnText = '限量号绑定中 ...'
      this.dispositionBtnEnabled = false
      const pitchOnNumber = pitchNumber || limitedNumber

      const { data: { data, code, message } } = await bindLimitedNumber({ orderId, limitedNumber: pitchOnNumber }, combiningStrings(nickCode))
      if (code === '00') {
        this.$router.push({
          name: 'limited-number-succeed',
          query: {
            orderId, limitNumber: pitchOnNumber, seriesCode, modelLineCode, orderStatus
          }
        })
      // 号码已被绑定
      } else if (code === '06') {
        // this.popShow = true
        // this.popMainText = message || '您输入的意向号码已被占用，系统为您随机选取3个备选限量号'
        // this.popBtnReign = [{
        //   text: '查看限量号', color: 'black', enabled: true, action: 'handleStepSet'
        // }]
        handleGetLimitedNumber(2, code)
      } else if (['04', '05'].includes(code)) {
        this.popShow = true
        this.popMainText = message
        if (code === '05') {
          this.popMainTips = '若限量号全部被绑定，请您联系奥迪管家'
        }
        this.popBtnReign = [{
          text: code === '05' ? '再选一次' : '我知道了', color: 'black', enabled: true, action: 'closed'
        }]
      } else {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message,
          forbidClick: true
        })
      }

      if (!this.dispositionBtnEnabled) {
        this.dispositionBtnText = '确定'
        this.dispositionBtnEnabled = true
      }
    },
    handleKeyboardStatus(status = '') {

    },
    handleLeftBack() {}
  }
}
