<template>
  <div class="configration">
    <div class="tab-wrapper">
      <van-tabs v-model="configrationActiveTab" :before-change="beforeChange">
        <van-tab title="外饰" name="exterior">
          <Exterior />
        </van-tab>
        <van-tab title="内饰" name="interior">
          <Interior />
        </van-tab>
        <van-tab title="选装" name="option">
          <Option />
        </van-tab>
        <!-- <van-tab title="权益" name="equity">
          <Equity />
        </van-tab> -->
      </van-tabs>
    </div>
  </div>
</template>

<script setup>
import {
  computed, onMounted, onActivated, watch, getCurrentInstance
} from 'vue'
import { Toast } from 'vant'
import Exterior from './exterior.vue'
import Interior from './interior.vue'
import Option from './option/option.vue'
// import Equity from './equity.vue'
import { useStore, useRouter } from './util/vueApi.js'

const instance = getCurrentInstance()
const sensors = instance.proxy.$sensors
const store = useStore()
const router = useRouter()

// store 数据
const configrationActiveTab = computed({
  get() {
    return store.state.configration.configrationActiveTab
  },
  set(name) {
    store.commit('updateConfigrationActiveTab', name)
  }
})
const carIdx = computed(() => store.state.configration.carIdx)
const currentVersion = computed(() => store.state.configration.currentVersion)
const currentModelLineData = computed(() => store.state.configration.currentModelLineData)
const currentCarType = computed(() => store.getters.currentCarType)

// 进入页面的初始化操作
const doExteiorPageInit = async () => {
  store.commit('showLoading')
  await store.dispatch('doExteriorPageAction') // 进入外观页初始化的操作
  store.commit('hideLoading')
}

onMounted(() => {
  doExteiorPageInit()
})

/**
 * 这里做一些清理配置页面数据的操作
 */
watch(configrationActiveTab, (value, oldValue) => {
  switch (value) {
    // 外观页面清理内饰的数据(这次首次进入不会执行,所以在mounted里面执行一次)
    case 'exterior':
      doExteiorPageInit()
      break

    // 内饰页面清理选装的数据
    case 'interior':
      /**
       * 清理选装的数据
       */
      store.dispatch('doInteriorPageAction') // 进入内饰页初始化的操作
      break
    case 'option':
    /**
     * 选装页面的初始化操作,设置当前页面数据的一些状态
     */
      if (oldValue === 'interior') {
        // 只有从内饰页进来才执行初始选装操作
        store.dispatch('doOptionPageAction') // 设置当前选装的置灰/互斥状态
      }
      break
    case 'equity':

      break
    default:
      console.error('未知的值', value)
      break
  }
  // console.log('configrationActiveTab watch', value)
})


const beforeChange = (targetTabName) => {
  clickTabSensors(configrationActiveTab.value, targetTabName) // 埋点
  /**
   * 进入选装tab页 的限制
   * 外观页无法直接跳转到选装页
   */
  if (targetTabName === 'option') {
    if (configrationActiveTab.value === 'exterior') {
      hitToast('内饰')
      return false
    }
  }

  /**
   * 进入权益tab页的限制
   * 外观页和内饰页无法直接跳转到选装页
   */
  if (targetTabName === 'equity') {
    if (configrationActiveTab.value === 'exterior') {
      hitToast('内饰')
      return false
    }
    if (configrationActiveTab.value === 'interior') {
      hitToast('选装')
      return false
    }
  }

  // 权益页面的一个sd埋点
  if (configrationActiveTab.value === 'equity') {
    equityLeavePageSensors(targetTabName)
  }
  // console.log('click', configrationActiveTab.value, targetTabName)

  return true
}

const hitToast = (msg) => {
  Toast(`请先选择${msg}`)
}

// 埋点
const clickTabSensors = (tabName, targetTabName) => {
  const tabMap = {
    exterior: '外观',
    interior: '内饰',
    option: '选装',
    equity: '权益'
  }
  const carMap = {
    0: 'A7L',
    1: 'Q5 e-tron',
    2: 'Q6'
  }
  const param = {
    source_module: 'H5',
    refer_tab_name: tabMap[tabName],
    car_series: carMap[carIdx.value],
    car_model: currentVersion.value.styleName,
    delivery_type: '定制交付', // 快速交付|定制交付
    belong_page: router.history.current.meta?.title,
    tab_name: tabMap[targetTabName]
  }

  // console.log(param)
  sensors.track('CC_Page_Tab_Click', param)
}


// 权益页切换页面的埋点
const equityLeavePageSensors = (buttonName, desc = '') => {
  const carMap = {
    0: 'A7L',
    1: 'Q5 e-tron',
    2: 'Q6'
  }
  const tabMap = {
    exterior: '外观',
    interior: '内饰',
    option: '选装',
    equity: '权益'
  }
  const { engine, customSeriesName } = currentModelLineData.value
  const param = {
    source_module: 'H5',
    refer_tab_name: tabMap[configrationActiveTab.value],
    car_series: carMap[carIdx.value],
    car_type: currentCarType.value,
    power_type: `${customSeriesName} ${engine}`,
    car_version: currentVersion.value.styleName,
    delivery_type: '定制交付', // 快速交付|定制交付
    select_right: desc,
    button_name: buttonName
  }
  // console.log(param)
  sensors.track('CC_CarConfiguration_Right_BtnClick', param)
}

</script>

<style lang="less" scoped>
.tab-wrapper {
  background-color: #F9F9F9;
}

// 覆盖默认样式
/deep/.van-tabs__wrap {
  position: sticky;
  top: 0;
  z-index: 10;
  height: 36px;
  padding: 3px 15px;
  background-color: #fff;

  .van-tabs__nav,
  .van-tabs__nav--line {
    background-color: #F2F2F2;
    justify-content: space-between;
    border-radius: 18px;
    padding-bottom: 0;

    .van-tab {
      flex: 1;
      padding: 0 20px;
      color: #000;
    }

    .van-tab--active {
      background-color: #E5E5E5;
      border-radius: 18px;
      font-weight: bold;
    }
  }

  .van-tabs__line {
    display: none;
  }
}
.sticky{
  position: sticky;
}

/deep/.van-tabs__content {
  padding: 0 0px;
  // overflow-y: auto;
  // padding-bottom: 120px;
}

</style>
