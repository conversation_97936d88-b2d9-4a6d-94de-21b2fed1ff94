import request from '../router/axios'
import api from '../config/url'
import { getToken } from '../utils/auth'

const baseUrl = api.BaseApiUrl
export const getCancelReasons = () => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/v1/cancel/reasons`,
  method: 'GET',
  headers: {
    'x-access-token': localStorage.getItem('token')
  }
})

export const unsubscribe = (data) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/v1/ngaReturnInfosPay`,
  method: 'PUT',
  headers: {
    'x-access-token': localStorage.getItem('token')
  },
  data: data

})

// 回收限量号

// export const recycle = (params) => {
//     return request({
//         url: baseUrl + "/api-wap/audi-eshop/admin/limited_numbers/recycle" ,
//         method: "GET",
//         headers: {
//             'x-access-token': token
//         },
//         params:{
//             ...params
//         }
//     })
// }
