<template>
  <div class="container buycar-agreement">
    <div
      class="content111"
      ref="content"
      @click="clickShowImg"
    >
      <img
        v-for="item in agreementList"
        :key="item"
        :src="item"
        alt=""
      >
    </div>


    <div
      class="btn-wrapper "
      v-if="showBtn"
    >
      <div class="lan-button-box black-button">
        <van-button
          @click="toMoneyDetails"
          :loading-text="payLoadingText"
          :loading="!!payLoadingText"
          :disabled="!showConfirmBtn"
          :class="['lan-button', !payLoadingText || 'lan-button-loading']"
        >
          {{ !showConfirmBtn ? `还需阅读${ countTime }秒` : '协议文本已确认' }}
        </van-button>
      </div>
    </div>

    <div />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Button
} from 'vant'
import { getBuyCarAgreement } from '@/api/api'
import Viewer from '@/utils/lib/viewer.esm'
import '@/utils/lib/viewer.min.css'
import { mapGetters } from 'vuex'
import { payOrder } from './payments/payments'

Vue.use(Button)
export default {
  data() {
    return {
      showBtn: false,
      countTime: 5,
      timeId: 0,
      showConfirmBtn: false,
      agreementList: [],
      isInitViewer: false,
      payLoadingText: ''
    }
  },

  async mounted() {
    await this.getAgreement()

    // 初始化图片查看
    this.clickShowImg()
  },
  methods: {
    ...mapGetters(['getDevice']),
    async getAgreement() {
      const { data } = await getBuyCarAgreement()
      if (data?.code === '00') {
        // 倒计时
        this.timeId = setInterval(() => {
          this.countTime--
          if (this.countTime === 0) {
            clearInterval(this.timeId)
            this.showConfirmBtn = true
          }
        }, 1000)
        // 10购车协议  30交车协议  01个人  02企业  49 : A7l车系  G4 : Q5e车系
        const imgs = JSON.parse(data.data.configValue)
        console.log(Object.keys(imgs))
        const keysArray = Object.keys(imgs)
        const { buyType } = this.$route.query
        const { seriesCode } = this.$store.state.carSeries
        const keyValue = `${seriesCode}-10-${buyType}`
        // 判断是否存在当前情况下的购车协议图
        if (keysArray.includes(keyValue)) {
          this.agreementList = imgs[keyValue].split(',')
        }
        this.showBtn = true
      }
    },

    clickShowImg() {
      // if (!this.isInitViewer) {
      const img = this.$refs.content
      if (!img) return console.error(' img 无法找到')
      new Viewer(img, {
        backdrop: true,
        button: true,
        inline: false,
        // container: div2,
        rotatable: false,
        toolbar: false,
        title: false,
        navbar: false,
        slideOnTouch: false,
        scalable: false,
        movable: true,
        zoomable: true,
        zoomOnWheel: true,
        toggleOnDblclick: false,
        viewed() {
          const imgCanvas = document.querySelector('.viewer-canvas img')
          if (imgCanvas) {
            imgCanvas.style.marginTop = '20%'
          }
        }
      })
      // this.isInitViewer = true
      // }
    },

    toMoneyDetails() {
      const {
        seriesCode, modelLineCode, orderStatus, ccid, orderId, env, minipay, oos
      } = this.$route.query
      const { nativeApp } = this.getDevice() || { nativeApp: false }
      // eslint-disable-next-line no-nested-ternary
      const device = env === 'minip' ? env : (nativeApp ? 'native' : 'html5')
      const location = window.location
      this.payLoadingText = '支付中 ...'
      payOrder(orderId, {
        seriesCode, modelLineCode, orderStatus, ccid
      }, {
        device, location, minipay, step: 2
      })
      // this.$store.commit('updateReaded', 1) //  1 代表已经阅读过合同
      // this.$router.go(-1)
    }
  },

  destroyed() {
    clearInterval(this.timeId)
  }

}
</script>

<style lang="less" scoped>
.buycar-agreement {
  /deep/.lan-button-box.black-button {
    .lan-button {
      &.van-button--disabled {
        background-color: #ccc;
        color: #fff;
      }
    }
  }
}

  .btn-time {
    line-height: 50px;
    background-color: #ccc;
    color: #fff;
    text-align: center;
    font-size: 16px;
  }

  .pos-bottom {
    position: fixed;
  }

  .btn-wrapper {
    margin-top: 40px;
    padding: 0 16px 40px;
  }
</style>
