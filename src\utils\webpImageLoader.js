import BaseOssHost from '../config/url'

export const loadImage = (path, notConvert) => {
  if (!path) {
    return path
  }
  if (window.isSupportsWebp && path.indexOf('image/format,webp') == -1) {
    if (path.indexOf('x-oss-process=') != -1) {
      path += ',image/format,webp'
    } else {
      path += '?x-oss-process=image/format,webp'
    }
  }
  if (!(path.startsWith('http') || path.startsWith('HTTP'))) {
    if (path.startsWith('/')) {
      path = BaseOssHost + path
    } else {
      path = `${BaseOssHost}/${path}`
    }
  }

  if(window.audiios && !notConvert){
    path = path.replace('https://', 'customschema://')
    path = path.replace('http://', 'customschema://')
  }
  
  return path
}

export const supportWebp = (callback) => {
  const img = new Image()
  img.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA'

  img.onload = function () {
    const result = img.width > 0 && img.height > 0
    window.isSupportsWebp = true
    callback && callback()
  }

  img.onerror = function () {
    window.isSupportsWebp = false
    callback && callback()
  }
}
