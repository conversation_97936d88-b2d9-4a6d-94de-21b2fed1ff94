<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-07-26 17:01:08
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-09-12 11:47:18
 * @FilePath     : \src\view\order\components\automobile-item.vue
 * @Descripttion : 默认整车订单(Item)
-->
<template>
  <div class="order-wrapper">
    <div
      class="order-media"
      data-flex="main:left"
      data-block
    >
      <div
        class="media-box"
        data-flex="main:center"
      >
        <img
          class="img"
          :src="orderInfo.imageUrl"
          :alt="orderInfo.modelNameCn"
        >
      </div>
      <div
        class="media-info"
        data-block
      >
        <h3 class="h3">
          {{ orderInfo.modelNameCn }}
        </h3>
        <!-- <p class="p">
          {{ orderInfo.colorNameCn }}
        </p> -->
        <p
          class="p"
        >
          <span>总价：</span><span class="price">{{ orderInfo.configTotalPrice | prefixFormatPrice('¥ ') }}</span>
        </p>
      </div>
    </div>
    <div :class="['order-info', `${pageName}-page`]">
      <ul class="list">
        <li
          class="li"
          data-flex="main:justify"
        >
          <span class="em">车辆金额</span><span class="span">{{ orderInfo.configTotalPrice | prefixFormatPrice('¥ ') }}</span>
        </li>
        <template v-if="(isDoVerification === 1)">
          <template v-if="!(orderInfo.buyType === '02' || orderInfo.buyType === '01' && zeroPayment)">
            <li
              class="li"
              data-flex="main:justify"
              v-if="!isOneStep"
            >
              <span class="em">已付意向金 </span><span class="span">{{ orderInfo.firstDepositMoney | prefixFormatPrice('- ¥ ') }}</span>
            </li>
            <li
              class="li"
              data-flex="main:justify"
            >
              <span class="em">已付定金 </span><span class="span">{{ orderInfo.secondDepositMoney | prefixFormatPrice('- ¥ ') }}</span>
            </li>
          </template>
        </template>
        <template v-else>
          <li
            class="li"
            data-flex="main:justify"
          >
            <span class="em">已付定金 </span><span class="span">{{ orderInfo.confirmPayAmount | prefixFormatPrice('- ¥ ') }}</span>
          </li>
        </template>
        <li
          class="li"
          data-flex="main:justify"
          v-if="orderInfo.loansMoney"
        >
          <span class="em">贷款金额 </span><span class="span">{{ orderInfo.loansMoney | prefixFormatPrice('- ¥ ') }}</span>
        </li>
        <li
          v-if="orderInfo.equityList.length"
          class="li"
          data-flex="main:justify"
          @click="handleCheckCoupons(true)"
        >
          <span
            class="em"
          >
            专属权益</span>
          <span
            class="span blacken"
            data-flex="cross:center"
          >{{ isDoVeriError[isDoVerification] || errorMessage + '' | prefixFormatPrice(isDoVerification === 1 ? '- ¥ ' : '') }}<van-icon
            class="arrow"
            name="arrow"
          /></span>
        </li>
        <li
          class="li elder"
          data-flex="main:justify"
          v-if="orderInfo.obligationStings"
        >
          <span class="em">待付款</span><span class="span">{{ orderInfo.obligationStings }}</span>
        </li>
        <template v-if="orderInfo.buyType === '02' || orderInfo.buyType === '01' && zeroPayment">
          <li
            class="li border-top"
            data-flex="main:justify"
            v-if="!isOneStep"
          >
            <span class="em">已付意向金 </span><span class="span">{{ orderInfo.firstDepositMoney | prefixFormatPrice('¥ ') }}</span>
          </li>
          <li
            class="li"
            data-flex="main:justify"
          >
            <span class="em">已付定金 </span><span class="span">{{ orderInfo.secondDepositMoney | prefixFormatPrice('¥ ') }}</span>
          </li>
          <div
            class="tips-banner"
          >
            *上汽奥迪确认收到尾款后，将退回意向金及定金至您的原付款账户
          </div>
        </template>
        <!-- <li
          class="li"
          v-if="isDoVerification === 2"
        >
          <span class="tips">您有卡券已失效，请联系工作人员 </span>
        </li> -->
      </ul>
    </div>
    <van-popup
      v-model="popupCoupons"
      position="bottom"
      safe-area-inset-bottom
      :style="{ 'max-height': '80vh', 'min-height': '50vh' }"
    >
      <div class="pop-coupons-box">
        <div
          class="pop-coupons-hd van-hairline--bottom"
          data-flex="main:justify cross:center"
        >
          <p
            class="btn close"
          >
            <van-icon
              class="icon-close"
              name="cross"
              size="20"
              @click="() => popupCoupons = false"
            />
          </p>
          <h3 class="h3">
            专属卡券
          </h3>
          <p
            class="btn finish"
          />
        </div>
        <div class="pop-coupons-main">
          <template v-if="orderInfo.equityList.length">
            <div class="coupons">
              <van-cell
                class="list"
                v-for="list in orderInfo.equityList"
                :key="list.couponEquityId"
              >
                <template #title>
                  <div
                    class="coupon-box"
                    data-flex="main:justify"
                    data-block
                  >
                    <div
                      class="coupon-media"
                    >
                      <div
                        class="media-box"
                        data-flex="main:center"
                      >
                        <img
                          class="img"
                          :src="list.couponImgUrl"
                          :alt="list.couponName"
                        >
                      </div>
                    </div>
                    <div
                      class="coupon-info"
                      data-block
                    >
                      <h4 class="h4 text-one-hid">
                        {{ list.couponName }}
                      </h4>
                      <p class="text-price">
                        <span>金额：</span>{{ list.deductOff | prefixFormatPrice('') }}
                      </p>
                      <p><span>有效期至：{{ list.effectiveEndDate | dayjsFilter('YYYY.MM.DD') }}</span></p>
                    </div>
                    <div
                      class="coupon-status van-hairline--left"
                      data-flex="cross:center main:center"
                    >
                      {{ couponStatus[['10', '90', '96'].includes(list.status) ? list.status : '000'] || '' }}
                    </div>
                  </div>
                </template>
              </van-cell>
              <div
                class="exception-tips"
                v-if="exceptionTips === 1"
              >
                上述卡券暂只支持叠加使用，由于存在失效卡券导致卡券不可用，请联系专属奥迪管家咨询卡券资格。
              </div>
            </div>
          </template>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Icon, Cell, Popup
} from 'vant'

import {
  getMyOrders,
  getCarPaymentInfo,
  getCarConfigDetail,
  getOrderEquities
} from '@/api/api'

import api from '@/config/url'
import { paramsStrict } from '@/utils'
import couponMap from '@/config/coupon.map'
import '../../rights/sales-card-voucher/index.less'

const prefixFormatPrice = Vue.filter('prefixFormatPrice')
Vue.use(Icon).use(Cell).use(Popup)

const isDoVeriError = {
  1: 0,
  // 1: '已使用',
  2: '存在失效卡券，请联系奥迪管家'
}
export default {
  name: 'AutomobileItem',
  props: {
    dataSources: {
      type: Object,
      default: () => {}
    },
    orderData: {
      type: Object,
      default: () => {}
    },
    isDoVerification: {
      type: Number,
      default: 0
    },
    errorMessage: {
      type: String,
      default: ''
    },
    pageName: {
      type: String,
      default: ''
    }
  },
  data: function () {
    return {
      isDoVeriError: { ...isDoVeriError },
      prefixFormatPrice,
      popupCoupons: false,
      orderInfo: {
        equityList: []
      },
      couponStatus: couponMap.CARD_VOUCHER_STATUS,
      exceptionTips: 0,
      zeroPayment: false,
      isOneStep: false
    }
  },
  created() {
    this.getOrderInfo()
  },
  methods: {
    async handleCheckCoupons(pop = false) {
      pop && (this.popupCoupons = true)
      const { orderId } = paramsStrict(this.$route.query)
      const { data: { data: allEquityList } } = await getOrderEquities({ orderId })
      // if (this.$route.name === 'order-balance' && this.pageName === 'balance' && !allEquityList.every((i) => i.status === '90')) {
      //   this.$EventBus.$emit('HANDLE_COLLECTION_ERROR', isDoVeriError[2])
      //   return
      // }
      console.log('%c [ allEquityList ]-231', 'font-size:14px; background:#cf222e; color:#fff;', allEquityList)
      allEquityList.map((i) => {
        i.deductOff /= 100
        return i
      })
      this.orderInfo.equityList = allEquityList
      const { isDoVerification, orderInfo: { equityAmount, configTotalPrice } } = this
      if (allEquityList.some((i) => !['90', '10'].includes(i.status))) {
        const { orderInfo } = this
        this.orderInfo = {
          ...orderInfo,
          ...{
            equityAmount: 0,
            obligationStings: ''
          }
        }
        this.exceptionTips = 1
        if (this.$route.name === 'order-balance' && this.pageName === 'balance') {
          return this.$EventBus.$emit('HANDLE_COLLECTION_ERROR', isDoVeriError[2])
        }
        isDoVerification !== 2 && this.$emit('update:isDoVerification', 2)
      } else {
        if (configTotalPrice !== '-') {
          this.$emit('update:isDoVerification', 1)
        }
      }
    },
    async getOrderInfo() {
      // 订单信息
      const { orderId, ccid } = paramsStrict(this.$route.query)
      const { data: { data: { carBuyerInfo: { buyType }, payList } } } = await getMyOrders({ orderId })
      const {
        data: {
          data: {
            configDetail: {
              outsideColor: { colorNameCn },
              carModel: { imageUrl, modelNameCn },
              totalPrice
            }
          }
        }
      } = await getCarConfigDetail({ ccid })

      const payListDetail = payList.filter((i) => ['10', '11'].includes(i.payPhase) && i.status === '3')

      if (payListDetail?.length) {
        const [{ payPhase }] = payListDetail || [{ payPhase: '' }]
        if (payPhase === '11') {
          this.isOneStep = true
        }
      }
      console.log('%c [ this.isOneStep ]-339', 'font-size:14px; background:#cf222e; color:#fff;', this.isOneStep)

      const { paySn } = payList.find((i) => i.payPhase === '20') || {}
      this.$emit('update:orderData', {
        paySn,
        buyType
      })
      // carTotalAmount => 总金额； payableAmount => 待支付(在0首付的情况下为待支付金额金额)；loadDownPaymentAmount => 首付款；confirmPayAmount  => 已付定金
      // paymentMethod 10 全款 20 贷款
      // equityList 优惠券列表
      // equityAmount 优惠金额
      // obligationStings 待付款文案
      // buyType 01 个人  02企业

      const {
        data: {
          data: resData,
          code,
          message
        }
      } = await getCarPaymentInfo({ orderId })
      let obligationStings = ''
      let orderInfo = {
        imageUrl: api.BaseOssHost + imageUrl,
        totalPrice,
        buyType
      }
      let loansMoney = 0
      if (resData && Object.keys(resData).length) {
        const {
          modelNameCn, confirmPayAmount, payableAmount,
          loadDownPaymentAmount, equityAmount, paymentMethod,
          carTotalAmount, configTotalPrice, loanInfo
        } = resData
        console.log('%c [ paymentMethod,buyType ]-270', 'font-size:14px; background:#000; color:#fff;',
          paymentMethod, `支付类型${buyType}===> [${buyType === '01' ? '个人' : '企业'}]`, loadDownPaymentAmount, confirmPayAmount,
          carTotalAmount, equityAmount, payableAmount)
        // 全款支付
        if (paymentMethod === '10') {
          console.log('%c [ 全款 ]-384', 'font-size:14px; background:#000; color:#fff;')
          obligationStings = buyType === '01' ? prefixFormatPrice(payableAmount / 100, '¥ ') : prefixFormatPrice((payableAmount + confirmPayAmount) / 100, '¥ ')
        } else {
          // 贷款 待支付金额包含-优惠券金额
          if (loadDownPaymentAmount) {
            // loanInfo?.downPayments 待支付(收付)金额

            loansMoney = loanInfo?.loanAmount || 0 // 贷款金额
            const unpaidAmount = loanInfo?.downPayments || 0 // 代付款金额
            // 0 首付
            if (loadDownPaymentAmount < confirmPayAmount) {
              this.zeroPayment = true
              obligationStings = prefixFormatPrice(unpaidAmount, '¥ ')
              console.log('%c [ 0 首付 ]-384', 'font-size:14px; background:#000; color:#fff;', loansMoney, unpaidAmount)
            //   // obligationStings = `首付款(${prefixFormatPrice(payableAmount / 100)}) ${prefixFormatPrice((carTotalAmount - payableAmount) / 100)}`
            } else {
              obligationStings = prefixFormatPrice((((unpaidAmount * 100) - (buyType === '01' ? (confirmPayAmount || 0) : 0)) / 100), '¥ ')
              console.log('%c [ 贷款 ]-384', 'font-size:14px; background:#000; color:#fff;', loansMoney, unpaidAmount)
            //   // obligationStings = `首付款(${((loadDownPaymentAmount * 100) / (carTotalAmount - equityAmount)).toFixed(2)}%) ${prefixFormatPrice((payableAmount + (buyType === '01' ? 0 : confirmPayAmount)) / 100)}`
            }
          }
        }

        const paidAmount = payListDetail.reduce((sum, list) => sum + list.amount, 0)
        let firstDepositMoney = 0
        let secondDepositMoney = 0
        if (confirmPayAmount === paidAmount) {
          // 说明支付信息和订单支付信息对账正确
          const { amount } = payListDetail.filter((i) => i.payPhase === '10' && i.status === '3')[0] || { amount: 0 }
          console.log('%c [ amount ]-418', 'font-size:14px; background:#cf222e; color:#fff;', amount)
          firstDepositMoney = amount / 100
          secondDepositMoney = (confirmPayAmount - amount) / 100
        }

        orderInfo = {
          ...orderInfo,
          modelNameCn,
          colorNameCn,
          loansMoney,
          configTotalPrice,
          firstDepositMoney,
          secondDepositMoney,
          confirmPayAmount: confirmPayAmount / 100,
          equityAmount: equityAmount / 100,
          obligationStings
        }
        this.isDoVeriError['1'] = orderInfo?.equityAmount || 0

        // this.$emit('update:isDoVerification', 1)
      } else {
        orderInfo = {
          ...orderInfo,
          imageUrl: api.BaseOssHost + imageUrl,
          equityAmount: 0,
          configTotalPrice: '-',
          obligationStings: '',
          confirmPayAmount: '',
          modelNameCn,
          colorNameCn
        }
        this.$emit('update:isDoVerification', code === 'MSTK0069' ? 2 : 3)
        code !== 'MSTK0069' && this.$emit('update:errorMessage', message === '订单贷款信息不存在' ? '请于贷款合同签署完成后再选择支付尾款' : message)
      }
      this.orderInfo = { ...this.orderInfo, ...orderInfo }
      this.handleCheckCoupons()
    }
  }
}
</script>

<style lang="less" scoped>
.order-wrapper {
  padding: 16px;
  .media-box, .img {
    height: 108px;
  }
  .media-box {
    width: 108px;
    margin-right: 8px;
    // background-color: #f8f8f8;
    overflow: hidden;
    .img {
      width: auto;
    }
  }
  .order-media {
    position: relative;
    margin: -16px -16px 0;
    padding: 0 8px 0 16px;
    background-color: #f8f8f8;
    background: linear-gradient(180deg, #f8f8f8 0%, #fbfbfb 100%);
    .media-info {
      padding: 16px 0 0;
      .h3, .price {
        line-height: 22px;
      }
      .h3 {
        font-size: 14px;
        margin: 0 0 13px;
        font-family: "Audi-Normal";
        font-weight: 400;
        word-break: break-word;
      }
      .p {
        margin: 0 0 11px;
        font-size: 12px;
        color: rgba(#000,.6);
        line-height: 20px;
        &:last-child {
          margin: 0 0 16px 0;
        }
        .price {
          font-family: "Audi-WideBold";
          font-weight: normal;
          color: #000;
        }
      }
    }
  }
  .order-info {
    .list {
      margin-top: 14px;
      .li {
        margin-bottom: 16px;
        line-height: 22px;
        font-size: 14px;
        color: rgba(#333, .7);
        &.border-top {
          border-top: .5px solid #F2F2F2;
          padding-top: 16px;
        }
        .span {
          &.blacken {
            color: #000;
          }
          padding-right: 5px;
          color: rgba(#000, .88);
          .arrow {
            margin-left: 4px;
          }
        }
        .f12 ,.tips{
          font-size: 12px;
        }
        .tips {
          color: #F50537;
        }
        &.elder {
          color: rgba(#000, .88);
          .span {
            font-weight: 600;
          }
          font-size: 16px;
        }
      }
    }
    &.balance-page {
      .list {
        .li {
          &:last-child {
            margin: 0 0 -3px;
          }
        }
      }
    }
  }
}
.tips-banner {
  padding: 7px 16px;
  margin: 12px -16px;
  font-size: 12px;
  line-height: 20px;
  color: rgba(#000,.6);
  background-color: #F2F2F2;
}
// .balance-page {
//   .tips-banner {
//     margin-bottom: -16px;
//     padding-bottom: 0;
//   }
// }
.pop-coupons-box {
  .pop-coupons-hd{
    height: 52px;
    padding: 0 16px;
    .icon-close {
      position: relative;
      top: 2px;
    }
    .h3{
      font-size: 16px;
    }
    .btn {
      width: 60px;
    }
    &::after{
        border-color: #e5e5e5;
    }
  }
  .pop-coupons-main {
    max-height: calc(80vh - 52px);
    min-height: calc(50vh - 52px);
  }
}
</style>
