<template>
  <div
    name="orderList"
    :class="['orderList',{purpleActiveStyle:purpleState}]"
  >
    <page-load
      @onRefresh="onRefresh"
      :pulldown.sync="pulldown"
      :pullupstate="false"
    >
      <div
        class="order-list-item"
        v-for="item in orderList"
        :key="item.orderId"
        @click="toOrderDetail(item)"
      >
        <div
          class="status"
          data-flex="main:justify cross:center"
        >
          <div class="time">
            {{ item.orderTime }}
          </div>
          <div class="step">
            <span v-if="item.valid === 0">已失效</span>
            <span v-if="item.valid == 1 && item.updateFlag == 0">{{ setStatusDesc(item) }}</span>
            <span v-if="[1,2].includes(item.updateFlag)">有更新</span>
          </div>
        </div>
          <div
            class="info"
            data-flex="main:left"
          >
            <div
              class="vehicles"
              data-flex="main:left"
              data-block
            >
              <div class="media-img">
                <img :src="$loadWebpImage(BaseConfigrationOssHost + item.headImageUrl)">
              </div>
              <div class="model-series" >
                <div v-if="item.seriesPower === '奥迪A7L'">
                  <div class="series name text-two-hidd" v-if="item.isA7MR">
                  {{ item.versionModel }}
                  </div>
                  <div class="series name text-two-hidd" v-else>
                  {{ (item.seriesPower +' ' + item.versionModel) }}
                  </div>
                </div>
                <div v-else>
                  <div class="series name text-two-hidd">
                    {{ item.seriesPower }}
                  </div>
                  <div class="model name text-one-hidd">
                    {{ item.versionModel }}
                  </div>
                </div>
              </div>
            </div>
            <div class="prices">
              {{ item.totalPrice | prefixFormatPrice('¥ ') }}
            </div>
          </div>
          <div class="handles">
            <div
              class="btn-box"
              data-flex="main:right"
              v-if="item.contractInfo?.purchaseContractStatus != '2' && item.orderStatus === '31'"
            >
              <div
                class="btn"
                :class="item.orderType == '07'?'btn-disabled':''"
                @click.stop="handleGoToPage(item)"
                v-if="item.btnText && item.orderType == '07'"
              >
                {{ item.btnText }}
              </div>
              <div
                class="btn"
                :class="item.orderType == '07'?'btn-disabled':''"
                @click.stop="handleGoToPage(item)"
                v-else-if="!item.extInfo.boolConfigModifiable && item.extInfo.boolPurchaseSignable"
              >
                {{ item.btnText }}
              </div>
              <div
                class="btn"
                :class="item.orderType == '07'?'btn-disabled':''"
                @click.stop="handleGoToPage(item)"
                v-else-if="item.orderType != '07' && item.extInfo?.boolConfirmReturn"
              >
                {{ item.btnText }}
              </div>
          </div>
          <div
              class="btn-box"
              data-flex="main:right"
              v-else
            >
              <div
                class="btn"
                :class="item.orderType == '07'?'btn-disabled':''"
                @click.stop="handleGoToPage(item)"
                v-if="item.btnText"
              >
                {{ item.btnText }}
              </div>
          </div>
        </div>
      </div>
      <!-- <div
        class="item-list"
        v-for="(item, index) in orderList"
        :key="index"
        @click="toOrderDetail(item)"
      >
        <div class="item">
          <div class="item_header">
            <span class="item-p">{{ item.orderId }}</span>
            <div class="item_headerRight">
              //orderStatus
// 00-待付款， 10-待发货， 20-待收货， 21-待提货， 22-待使用, 90-已完成， 98-已取消， 99-已关闭
// 30-小订已付款 ，31-大订已付款，32-已付尾款待交车	 -->
      <!--  <span
                v-if="item.valid === 0"
                style="color: #999;font-size: 14px"
              >已失效</span>
              <span v-if="item.valid == 1 && item.updateFlag == 0">{{ setStatusDesc(item) }}</span>
              <span
                v-if="[1,2].includes(item.updateFlag)"
                style="color: #999;position:relative;font-size: 14px"
              > 有更新</span>
            </div>
          </div>
          <div class="item_foot">
            <img :src="$loadWebpImage(BaseConfigrationOssHost + item.headImageUrl)">

            <div class="item-content">
              <span class="div">
                {{ item.modelNameCn }}
              </span>
              'A7LE sdsdsds名字很长名字很长名ghgjhklhgfdsdfghj很长名字很长'+-->
      <!-- <div
                class="item-p"
                v-if="item.orderItemList[0].status !== '81'"
              >
                {{
                  setPriceDesc(item) +
                    (getCarPrice(item) / 100)
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                }}
              </div>
            </div>
          </div>
        </div>
      </div> -->

      <finishedTemplate finishedText="抵达终点" v-if="orderList.length"></finishedTemplate>
      <div
        v-show="showNotData"
        class="not_data"
      >
        <div>
          <img
            :src="require(purpleState ? '../../assets/img/wenjuanNotDataa.png' : '../../assets/img/empty-new-img.png')"
            alt=""
          >
        </div>
        <p>暂无内容</p>
      </div>
    </page-load>

    <network @reload="networkReload()" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Toast } from 'vant'
import {
  getOrderList, getCarConfig, getAPITimeOutTesting, getEShopOrderList
} from '@/api/api'
import { callNative } from '@/utils'
import url from '@/config/url'
import network from '@/components/network.vue'
import { SERIES_POWER_KW } from '@/config/conf.data'
import PageLoad from '../../components/page-load.vue'
import { networkToast } from '../../utils/timeout'
import { A7MR } from '@/view/newConfigration/car/a7mr'
import finishedTemplate from '@/components/finishedTemplate.vue'


export default {
  name: 'OrderList',
  components: { PageLoad, network,finishedTemplate },
  data() {
    return {
      pulldown: false, // 下拉
      showNotData: false,
      orderList: [],
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      device: { nativeApp: false },
      purpleState:null,
    }
  },

  created(){
    this.purpleState = ['fromPurple','purple'].includes(this.$route.query.fromType) //兼容purple（测试用）
  },

  mounted() {
    this.device = this.getDevice() || {}
    this.getCarOrderList()
    this.appCallJs()
    // this.$store.commit('setHeaderVisible', true)
  },
  watch: {
    $route(val) {
      const { timestamp, env } = this.$route.query
      if (env === 'minip') {
        window.history.go(-1)
      }
    }
  },
  methods: {
    ...mapGetters(['getDevice', 'currentSeriesName']),
    // APP调用刷新
    appCallJs() {
      callNative('appCallJs', {}).then((data) => {
        if (data.refresh) {
          this.onRefresh()
          // App callbackId只生效一次，接到响应需要再次调用
          this.appCallJs()
        }
      })
    },
    // 下拉刷新
    async onRefresh() {
      await this.getCarOrderList()
      this.pulldown = !this.pulldown
    },

    // 获取车订单
    async getCarOrderList() {
      if(this.purpleState){
        networkToast('purple')
      }else{
        networkToast()
      }

      getAPITimeOutTesting()
      try {
        const { data } = await getEShopOrderList() // await getOrderList()
        Toast.clear()
        this.orderList = data.data.records
        if (this.orderList.length) {
          const res = await Promise.all(this.orderList.map(async (i) => await getCarConfig({ ccid: i.carCustomId })))
          console.log('getOrderCarConfig', res)
          console.log(this.BaseConfigrationOssHost)
          res.length > 0 && res.forEach((ele, i) => {
            if (this.orderList[i]) {
              const e = ele.data.data
              this.$set(this.orderList[i], 'modelNameCn', e?.configDetail?.carModel?.modelNameCn)
              this.$set(this.orderList[i], 'headImageUrl', e?.configDetail?.carModel?.headImageUrl)
              this.orderList[i].valid = e?.valid == undefined || e?.valid == 1 ? 1 : 0
              this.orderList[i].updateFlag = e?.updateFlag
              if (e?.configDetail?.carModel?.modelNameCn) {
                const modelNameCn = e.configDetail.carModel.modelNameCn

                console.log('%c [ modelNameCn ]-217', 'font-size:14px; background:#cf222e; color:#fff;', modelNameCn)
                const [SNC] = SERIES_POWER_KW.filter((i) => modelNameCn.indexOf(i) !== -1)
                let seriesPower = ''
                let versionModel = ''
                if (SNC) {
                  seriesPower = SNC
                  versionModel = modelNameCn.replace(SNC, '')
                } else if (modelNameCn.indexOf('奥迪A7L') !== -1 || e?.configDetail?.carSeries?.seriesCode === '49') {
                  seriesPower = '奥迪A7L'
                  versionModel = modelNameCn.replace(seriesPower, '')
                } else {
                  seriesPower = modelNameCn
                }
                this.orderList[i].seriesPower = seriesPower
                this.orderList[i].versionModel = versionModel

                // 判断是否是A7MR车型
                this.orderList[i].isA7MR = A7MR.map((i) => i.code).includes(e.configDetail.carModel.modelLineCode)
              }
              // carCustomId ccid
              // 订单状态
              const orderStatus = this.orderList[i].orderItemList[0]?.status || ''
              // 是否开启大定(非q5e车型后端已经判断，前端无需考虑)
              const canDdPay = this.orderList[i].canDdPay || false
              // 是否已签合同
              const isSignedOnlineContract = this.orderList[i].contractInfo?.purchaseContractStatus === '2'
              const [{ payPhase }] = this.orderList[i].payList || [{ payPhase: '' }]

              this.orderList[i].orderPayStatus = orderStatus
              if (orderStatus === '00' && this.orderList[i].payType !== '1' && payPhase !== '11') {
                this.orderList[i].btnText = '去支付意向金'
                this.orderList[i].btnStatus = orderStatus
              } else if (((['30', '301'].includes(orderStatus)) || (orderStatus === '00' && payPhase === '11')) && this.orderList[i].valid === 1 && canDdPay) {
                this.orderList[i].btnText = '去支付定金'
                this.orderList[i].btnStatus = '30301'
              } else if (this.orderList[i].confirmReturn?.status == '00' && this.orderList[i].orderType != '07') {
                this.orderList[i].btnText = '撤回申请'
                this.orderList[i].btnStatus = '32'
              } else if (this.orderList[i].extInfo?.boolConfirmReturn && this.orderList[i].orderType != '07') {
                this.orderList[i].btnText = '申请退款'
                this.orderList[i].btnStatus = '32'
              } else if (!isSignedOnlineContract && orderStatus === '31' ) {
                this.orderList[i].btnText = '去签署合同'
                this.orderList[i].btnStatus = '31'
              } else if (this.orderList[i].extInfo.boolReceivePurchaseInvoice && orderStatus === '32' && this.orderList[i].orderType != '07') {
                this.orderList[i].btnText = '签署交车确认书'
                this.orderList[i].btnStatus = '33'
              }
              // 订单状态
              this.orderList[i].totalPrice = isSignedOnlineContract ? this.orderList[i].contractInfo?.purchaseContractTotalPrice : e?.configDetail?.totalPrice
            }
          })
          this.showNotData = false
        } else {
          this.showNotData = true
        }
      } catch (e) {
        this.showNotData = true
      }
      console.log('orderList', this.orderList)
    },

    async toOrderDetail(item) {
      this.sensorsBuriedPoint(item)
      const skuid = item.orderItemList[0].skuId

      if (!item.orderId && !skuid) {
        // 如果 orderid 或者 skuid 没有获取到, 则报错
        return console.error(
          `order-list page, orderId:${item.orderId} skuid:${skuid}`
        )
      }
      // if (this.device?.nativeApp) {
      const query = {
        from: 'order-list',
        name: 'order-list',
        sign: 'native',
        times: 1,
        skuid: skuid,
        orderId: item.orderId,
        orderType:item.orderType
      }
      const params = Object.keys(query).reduce((str, list) => `${str}&${list}=${query[list]}`, '?')
      const { origin, pathname } = window.location
      const url = `${origin}${pathname}#/order/model-detail${params}`
      console.log(url,query,'url url');
      // 跳转详情
      callNative('audiOpen', { path: url })
      // this.$router.push({
      //     path: '/order/model-detail',
      //     query: {
      //      ...query
      //     }
      //   })
      // } else {
      //   this.$router.push({
      //     path: '/order/money-detail',
      //     query: {
      //       orderId: item.orderId,
      //       skuid
      //     }
      //   })
      // }
    },
    setStatusDesc(item) {
      if(item.orderType == '07') {
        if (item.orderItemList[0].statusDesc.indexOf('小订') !== -1) {
        return '已支付意向金'
        }
        if (item.orderItemList[0].statusDesc.indexOf('大订') !== -1) {
          return '已支付定金'
        }
      }else {
        if(item.confirmReturn?.status == '00' ){
        return '退款审批中'
        } else if(item.extInfo?.boolConfirmReturn) {
          return '已开启退款入口'
        } else if(item.contractInfo?.purchaseContractStatus == '2' && (item.orderStatus == '31' || item.orderStatus == '32')) {
          return '已签署购车协议'
        } else if (item.orderItemList[0].statusDesc.indexOf('大订') !== -1) {
          return '已支付定金'
        }
      }

      console.log('状态文案', item.orderItemList[0].statusDesc)
      return item.orderItemList[0].statusDesc
    },
    setPriceDesc(item) {
      if (item.orderItemList[0].statusDesc.indexOf('小订') !== -1) {
        return '意向金：¥'
      }
      if (item.orderItemList[0].statusDesc.indexOf('大订') !== -1) {
        return '定金：¥'
      }
      if (item.orderItemList[0].status === '32' || item.orderItemList[0].status === '90') {
        /// / 32 已支付尾款，90已交车显示全部金额
        return '总价：¥'
      }
      if (item.payList) {
        // 福袋车直接就是大订
        if (item.payList[0].payPhase === '11') {
          return '定金：¥'
        }
      }

      return '意向金：¥'
    },

    getCarPrice(item) {
      if (item.orderItemList[0].status === '30' || item.orderItemList[0].status === '98') {
        // 福袋车金额显示总价
        if (item.payList) {
          if (item.payList[0].payPhase === '11') {
            return item.payList[0].amount
          }
        }
        // 显示小订的金额
        return item.orderItemList[0].reserveOrderAmount
      } if (['31', '301'].includes(item.orderItemList[0].status)) {
        // 大订的支付成功显示大订的金额
        return item.payList.reduce((sum, list) => sum + (list.status !== '3' ? 0 : list.amount), 0)
      } if (item.orderItemList[0].status === '32' || item.orderItemList[0].status === '90') {
        // 32 已支付尾款，90已交车显示全部金额 - 用合同的总价
        return item.contractInfo.purchaseContractTotalPrice * 100
      }
      // 福袋车金额显示总价
      if (item.payList) {
        if (item.payList[0].payPhase === '11') {
          return item.payList[0].amount
        }
      }
      // 其他状态先显示小订的金额
      return item.orderItemList[0].reserveOrderAmount
    },

    async getOrderCarConfig(carCustomId) {
      const { data } = await getCarConfig({ ccid: carCustomId })
      if (data.data) {
        return data.data.configDetail.carModel
      }
      return ''
    },

    // 神策埋点
    sensorsBuriedPoint(item) {
      const params = {
        order_id: item.orderId,
        order_status: item.orderItemList[0].statusDesc,
        car_name: item.orderItemList[0].productName,
        deposit: item.orderItemList[0].confirmOrderAmount
      }
      this.$sensors.track('clickOrderCard', params)
    },
    handleGoToPage({
      btnStatus, carCustomId, orderItemList, orderId, orderPayStatus,orderType
    }) {
      if(orderType == '07') return
      const { skuId } = orderItemList[0] || ''
      const { origin, pathname } = window.location
      let query = {
        from: 'order-list',
        skuid: skuId,
        orderId
      }

      if (btnStatus === '00') {
        query = {
          ...query,
          name: 'model-detail',
          ccid: carCustomId,
          orderType,
          orderStatus: orderPayStatus,
          param: `'orderId=${orderId},skuid=${skuId}'`
        }
        // if (this.device?.nativeApp) {
        const params = Object.keys(query).reduce((str, list) => `${str}&${list}=${query[list]}`, '?')
        // this.handlesNativeFormOrderList()
        callNative('audiOpen', {
          path: `${origin}${pathname}#/order/confirm${params}`
        })
      } else if (btnStatus === '31') {
        query = {
          ...query,
          orderId,
          already: 0,
          orderType,
          name: 'model-detail',
          skuid: skuId,
          param: `'orderId=${orderId},skuid=${skuId}'`
        }
        const params = Object.keys(query).reduce((str, list) => `${str}&${list}=${query[list]}`, '?')
        callNative('audiOpen', {
          path: `${origin}${pathname}#/contract-info${params}`
        })
      } else if(btnStatus === '33') {
        query = {
          ...query,
          orderId,
          orderType,
        }
        const params = Object.keys(query).reduce((str, list) => `${str}&${list}=${query[list]}`, '?')
        callNative('audiOpen', {
          path: `${origin}${pathname}#/delivery-contract${params}`
        })
      } else {
        query = {
          ...query,
          name: 'order-list',
          sign: 'native',
          times: 1,
          orderType
        }
        // if (this.device?.nativeApp) {
        const params = Object.keys(query).reduce((str, list) => `${str}&${list}=${query[list]}`, '?')
        callNative('audiOpen', {
          path: `${origin}${pathname}#/order/model-detail${params}`
        })
      }
    },
    handlesNativeFormOrderList() {
      // this.$store.commit('setPageFrom', 'order-list')
      // localStorage.setItem('page-from', 'order-list')
    },
    networkReload() {
      this.getCarOrderList()
    },
    // 计算整车价格
    getVehiclesDealPrice() {}
  }
}
</script>

<style lang="less" scoped>
.not_data {
  text-align: center;
  padding-top:150px;
  img {
    width: 160px;
    height: 160px;
  }
  p {
    height: 20px;
    font-size: 13px;
    margin: 0px;
    font-weight: 400;
    color: #B3B3B3;
    line-height: 20px;
  }
}
.orderList {
  padding: 0 16px;
  .item-list {
    width: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px #f2f2f2 solid;
    padding-bottom: 16px;
  }
  .item {
    width: 100%;
    display: flex;
    align-items: center;
    flex-flow: column;
    // justify-content: space-between;
    //

    .item_header {
      display: flex;
      height: 20px;
      justify-content: space-between;
      font-family: "Audi-Normal";
      color: #999999;
      width: 100%;
      font-size: 16px;
      margin-top: 10px;
      margin-bottom: 8px;
      .item_headerRight {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #000;
      }
      i {
        margin-left: 8px;
      }
    }
    .item_foot {
      display: flex;
      width: 100%;

      img {
        width: 88px;
        height: 88px;
        object-fit: cover;
      }

      .item-content {
        width: calc(100% - 88px);
        height: 88px;
        padding: 10px 16px;
        box-sizing: border-box;
        display: flex;
        flex-flow: column;
        justify-content: space-between;
        align-items: flex-start;

        .div {
          width: 100%;
          font-size: 16px;
          padding-top: 5px;
          color: #000000;
          text-align: left;
          white-space: normal;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          -webkit-line-clamp: 2;
          font-family: "Audi-WideBold";
        }

        .item-p {
          font-size: 14px;
          font-family: "Audi-Normal";
          color: #000;
          line-height: 16px;
        }
      }
    }
  }

  .color-grey {
    color: #999999 !important;
  }


  .order-list-item {
    padding: 16px 0 12px;
    border-bottom: solid .6px #F2F2F2;
    .status {
      height: 20px;
      line-height: 20px;
      .time {
        font-size: 12px;
        font-weight: 400;
        color: #808080;
      }
      .step {
        font-size: 12px;
        font-family: Audi-ExtendedBold;
        font-weight: normal;
        color: #262626;
      }
    }
    .info {
      margin: 8px 0;
      .vehicles {
        .media-img {
          margin-right: 8px;
          width: 80px;
          height: 80px;
          img {
            width: 85px;
            height: 85px;
            object-fit: cover;
          }
        }
        .model-series {
          // margin-right: 22px;
          width: calc(100vw - 218px);
          .name {
            color: #333;
            font-size: 14px;
            line-height: 22px;
            &.series {
              word-wrap: break-word;
              word-break: normal
            }
          }
        }
      }
      .prices {
        width: 72px;
        text-align: right;
        font-size: 12px;
        color: #262626;
        line-height: 20px;
      }
    }
    .handles {
      .btn-box {
        .btn {
          width: 93px;
          height: 32px;
          line-height: 32px;
          font-size: 12px;
          background: #000000;
          color: #E5E5E5;
          text-align: center;
        }
        .btn-disabled {
          background: #b3b3b3 !important;
          color: #e5e5e5 !important;
        }
      }
    }
  }
}

@purple-txt-color:#efefef;

//purple样式
.purpleActiveStyle{
  background: #191919;
    .order-list-item{
      border-color: rgba(250,250,250,.1);
      .status{
        .time{
          color: @purple-txt-color;
        }
        .step{
          color: @purple-txt-color;
        }
      }
      .info{
        .prices{
          color: @purple-txt-color;
        }
        .vehicles{
          .model-series{
            .name{
              color: @purple-txt-color;
            }
          }
        }
      }
    }
}
</style>
