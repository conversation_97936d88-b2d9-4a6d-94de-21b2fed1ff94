<template>
  <div class="container">
    <div class="chi">
      <img
        :src="imgUrl"
        alt=""
      >
      <div
        class="btn-more"
        @click="toEquityDetail"
      >
        权益细则 <van-icon name="arrow" />
      </div>
    </div>
  </div>
</template>

<script>
import { getRightsDetail } from '../../api/smallDetail'
import api from '../../config/url'

const codeType = ['00', '200']
export default {
  beforeRouteEnter(to, from, next) {
    if (['rights-detail'].includes(to.name)) {
      next((vm) => {
        vm.$emit('set-bottom', true)
      })
    } else {
      next()
    }
  },
  beforeRouteLeave(to, form, next) {
    if (['detail'].includes(to.name)) {
      this.$emit('set-bottom', false)
      next()
    } else {
      next()
    }
  },
  name: '',
  data() {
    return {
      BaseOssHost: api.BaseOssHost,
      imgUrl: ''
    }
  },
  components: {},
  created() {},
  mounted() {
    this.getRightsDetail()
  },
  methods: {
    async getRightsDetail() {
      const { data } = await getRightsDetail()
      if (codeType.includes(data.code)) {
        this.imgUrl = this.BaseOssHost + data.data[0].content.contentDetailList[0].imageUrl
      }
    },

    toEquityDetail() {
      this.$router.push({
        path: '/equity-detail',
        query: {
          skuId: '1407545686728208386'
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
@import "../../assets/style/common.less";

.container {
  .chi {
    position: relative;
    min-height: inherit;
    background-size: 100%;
    background-repeat: no-repeat;

    .btn-more {
      .c-font14;
      .c-flex-center;
      position:absolute;
      bottom: 5px;
      right: 10px;
      color:#fff;
    }
  }
}
</style>
