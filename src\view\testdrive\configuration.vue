<!-- 某车型配置表 -->
<template>
  <div
    name="name"
    class="container"
  >
    <div class="configuration">
      <div
        class="_head"
        ref="configHead"
        @click="show = !show"
      >
        基本参数
        <van-icon name="arrow-down" />
      </div>
      <van-popup
        v-model="show"
        position="top"
        :style="{ height: '70px', top: distance + 'px' }"
      >
        <div class="_option">
          <div
            :class="[navIndex === 1 ? '_select':'']"
            @click="getNavIndex(1)"
          >
            标准配置
          </div>
          <div
            :class="[navIndex === 2 ? '_select':'']"
            @click="getNavIndex(2)"
          >
            基本参数
          </div>
          <div
            :class="[navIndex === 3 ? '_select':'']"
            @click="getNavIndex(3)"
          >
            选装包
          </div>
        </div>
      </van-popup>
    </div>

    <div
      class="_table"
      style="margin-top: 60px;"
    >
      <div
        class="_list"
        :class="[index%2 === 0 ? '_gay':'']"
        v-for="(item,index) in 21"
        :key="index"
      >
        <div class="_left">
          轴距（mm）
        </div>
        <div class="_right">
          汽油+48V轻混系统
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Popup } from 'vant'

Vue.use(Popup)

// import { getModelLineConfigs } from '@/api/api'
export default {
  data() {
    return {
      show: false,
      distance: 64,
      navIndex: 1
    }
  },
  // async mounted() {
  //   const { data } = await getModelLineConfigs()
  //   if (data.code === '00' && data.data) {

  //   }
  // },
  created() {
    this.$store.state.title = 'A7L edition one 配置表'

    this.$nextTick(() => {
      this.distance = this.$refs.configHead.getBoundingClientRect().bottom
    })
  },
 

  methods: {
    getNavIndex(e) {
      this.navIndex = e
    }
  }
}
</script>

<style lang='less' scoped>
    div {
        box-sizing: border-box;
    }

    .container {
        display: flex;
        flex-flow: column;
        width: 100%;
        margin: 0;
        padding: 0;
        border-top: 1px #F2F2F2 solid;
        color: #000000;

        .configuration {
            ._head {
                padding-top: 8px;
                height: 60px;
                width: 100%;
                display: flex;
                padding: 8px 16px 0;
                align-items: center;
                justify-content: space-between;
                font-size: 16px;
                font-family: Audi-WideBold;
                color: #000000;
                line-height: 20px;
                z-index: 9999 !important;
                position: fixed;
                left: 0;
                background-color: #FFFFFF;
            }
        }

        ._content {
            z-index: 9999;
            position: fixed;
            top: 120px;
            left: 0;
            background-color: #000000;
        }

        ._table {
            width: 100%;
            display: flex;
            flex-flow: column;

            ._list {
                width: 100%;
                background: #FFFFFF;
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #000000;

                ._left {
                    width: 125px;
                    height: 42px;
                    display: flex;
                    align-items: center;
                    padding-left: 16px;
                }

                ._right {
                    width: calc(100% - 125px);
                    border-left: 1px #E5E5E5 solid;
                    display: flex;
                    align-items: center;
                    height: 42px;
                    justify-content: center;
                }

                &._gay {
                    background-color: #F2F2F2;
                }
            }
        }
    }

    ._option {
        width: 100%;
        height: 64px;
        background: #FFFFFF;
        display: flex;
        align-items: center;
        padding: 18px 16px;
        justify-content: space-between;

        div {
            width: 109px;
            height: 30px;
            border: 1px solid #000000;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            color: #000000;

            &._select {
                color: #FFFFFF !important;
                background-color: #000000 !important;
                border: none !important;
            }
        }

    }
</style>
