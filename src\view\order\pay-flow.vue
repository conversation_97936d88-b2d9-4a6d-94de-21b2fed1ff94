<template>
  <div class="pay-flow container">
    <img
      :src="imgUrl"
      alt=""
    >
    <van-dialog
      v-model="show"
      overlay
      :show-cancel-button="true"
    >
      <div class="title">
        您需前往【数字人民币APP】向【上海上汽大众汽车销售有限公司】支付￥{{
          (money / 100) | formatPrice
        }}
      </div>
      <div
        class="D-buttons"
        :data-clipboard-text="'0022500981268520'"
        id="co"
        @click="copy"
      >
        复制银行账号
      </div>
      <div
        class="D-buttons2"
        @click="show = false"
      >
        取消
      </div>
    </van-dialog>
    <div
      class="btnWarp"
      style="bottom: 0"
    >
      <div
        class="buttons"
        @click="show = true"
      >
        去支付
      </div>
      <div class="bt" />
    </div>
  </div>
</template>

<script>
import Clipboard from 'clipboard'
import { Toast } from 'vant'
import { getPayFlowImg } from '../../api/pay-flow'
import { getMyOrders } from '../../api/payment-success'
import api from '../../config/url'

const codeType = ['00', '200']
export default {
  name: '',
  data() {
    return {
      show: false,
      BaseOssHost: api.BaseOssHost,
      imgUrl: '',
      money: 0
    }
  },
  methods: {
    copy() {
      const clipboard = new Clipboard('#co')
      clipboard.on('success', (e) => {
        window.console.log('复制成功', e)
        Toast({
          type: 'fail',
          message: '复制成功',
          icon: require('../../assets/img/success.png')
        })
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', () => {
        // 不支持复制
        Toast({
          type: 'fail',
          message: '手机权限不支持复制功能',
          icon: require('../../assets/img/error.png')
        })
        // 释放内存
        clipboard.destroy()
      })
      this.$router.push({
        path: '/order/upload-documents',
        query: {
          orderId: this.$route.query.orderId || ''
        }
      })
    },
    async getPayFlowImg() {
      const { data } = await getPayFlowImg()
      if (codeType.includes(data.code)) {
        console.log(data.data)
        this.imgUrl = this.BaseOssHost + data.data[0].content.contentDetailList[0].imageUrl
      }
    },
    async getMyOrders() {
      const { data } = await getMyOrders({
        orderId: this.$route.query.orderId
      })
      if (codeType.includes(data.code)) {
        /**
         * 小订的价格取 data.data.orderItemList[0].reserveOrderAmount
         * 大定的价格需要用总价去减去小订价格
         * 然后还需要用orderstatus, 大定状态:orderStatus: 30 || 301)
         */
        const { orderStatus, orderItemList } = data.data

        if (orderStatus === '30' || orderStatus === '301') {
          const totalPrice = data.data.orderItemList[0].confirmOrderAmount
          this.money = totalPrice - orderItemList[0].reserveOrderAmount
        } else {
          this.money = orderItemList[0].reserveOrderAmount
        }

        if (process.env.VUE_APP_ENV !== 'production') {
          console.warn('数字人民价格支付显示', this.money)
        }
      }
    }
  },
  mounted() {
    this.getPayFlowImg()
    this.getMyOrders()
  }
}
</script>
<style lang="less" scoped>
@import url("../../assets/style/buttons.less");
@import url("../../assets/style/dialog.less");

.container {
  padding: 0 16px;
  padding-bottom: 120px;
}


</style>
