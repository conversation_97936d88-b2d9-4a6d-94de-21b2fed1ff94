<template>
  <div>
    <!-- <Tabs current-active="exterior" /> -->
    <div class="collapse-wrapper">
      <div class="main-wrapper">
        <ShowModelButton type="out" />
        <!-- <ImgSwiper :img-list="page2dCarList" /> -->
        <ImgSwiper :img-list="page2dCarList" :active-view="currentView" :hub-images="currentHubImages" />
        <DescriptionText/>
      </div>

      <div class="wrapper-scroll">
        <!-- 外观颜色列表 -->
        <div class="desc-wrapper">
          <div class="c-font14 c-bold c-lh22">
            {{ currentExterior.optionName }}
          </div>
          <div class="desc c-lh20">
            <span class="relative" :class="{ 'line-through':discountTagVisible }">
              &nbsp;&nbsp;{{ colorDiscountPrice | finalFormatPriceDesc }} &nbsp;
              <div class="discount" v-show="discountTagVisible"> 限时优惠 </div>
            </span>
          </div>
        </div>
        <div class="exterior-wrapper">
          <div class="wrapper">
            <div v-for="item in pageOutColorArray" :key="item.optionCode" class="item" :class="{
              selected: item.optionCode === currentExterior.optionCode,
              disabled: item.disabled
            }" @click="toSelectExerior(item)">
              <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(140, true)" alt="">
            </div>
          </div>
        </div>
        <!-- 轮毂列表 -->
        <div class="desc-wrapper hub-margin-top">
          <div class="c-font14 c-bold c-lh22 hub-name">
            {{ currentHub.optionName }}
          </div>
          <div class="desc c-lh20">
            {{ currentHub.price | finalFormatPriceDesc }}
          </div>
        </div>
        <div class="hub-wrapper">
          <div class="wrapper">
            <div v-for="item,index in pageHubArray" :key="item.optionCode" class="item" :class="{
              selected: item.optionCode === currentHub.optionCode
            }" @click="toSelectHub(item,index)">
              <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(140, true)" alt="">
            </div>
          </div>
        </div>
      </div>

    </div>

    <CommonFooter @click="nextPage" />
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import { Toast } from 'vant'
import CommonFooter from './components/commonFooter.vue'
import url from '@/config/url'
import ImgSwiper from './imgSwiper.vue'
import ShowModelButton from './components/showModelButton.vue'
import DescriptionText from './components/descriptionText.vue'

const OSS_URL = url.BaseConfigrationOssHost
Vue.use(Toast)

export default {
  name: 'ConfigrationExterior',
  components: {
    ImgSwiper, CommonFooter, ShowModelButton, DescriptionText
  },
  data() {
    return {
      BaseConfigrationOssHost: OSS_URL,
      pageStartTime: 0,
      currentView: 'color', // 'color' 或 'hub'
      // 存储当前轮毂图片
      currentHubImages: {}
    }
  },
  computed: {
    ...mapGetters([
      'currentSeriesName',
      'page2dCarList',
      'pageOutColorArray',
      'pageHubArray',
      'currentCarType'
    ]),
    ...mapState({
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentExterior: (state) => state.configration.currentExterior,
      currentHub: (state) => state.configration.currentHub,
      currentVersion: (state) => state.configration.currentVersion,
      carIdx: (state) => state.configration.carIdx,
      configrationActiveTab: (state) => state.configration.configrationActiveTab,
      colorDiscountPrice(state) {
        const currentExterior = state.configration.currentExterior
        const item = currentExterior.tags.find((i) => i.tagCode === 'OPTION_PREFERENTIAL')
        if (item) {
          return item.tagValue
        }
        return currentExterior.price
      },
      discountTagVisible(state) {
        const currentExterior = state.configration.currentExterior
        return Array.isArray(currentExterior.tags) && currentExterior.tags.length > 0
      }
    })
  },
  watch: {

    // A类车更新默认外观色
    pageOutColorArray(colorArray) {
      this.updateOutColor(colorArray)
    },

    // A类车更新默认轮毂
    pageHubArray(hubArray) {
      if (hubArray.length > 0) {
        if (!hubArray.find((i) => i.optionCode === this.currentHub.optionCode)) {
          this.toSelectHub(hubArray[0])
        }
      }
    },

    configrationActiveTab(val) {
      if (val === 'exterior') {
        this.pageStartTime = Date.now()
      }
    },

    currentExterior(color) {
      // 半定获取轮毂数据
      if (this.currentModelLineData.measure === 1 && color) {
        this.$store.dispatch('setHubArrayMeasure1')
      }
    }

  },
  mounted() {
    this.pageStartTime = Date.now()
    this.updateOutColor(this.pageOutColorArray)
  },
  methods: {
    updateOutColor(colorArray) {
      if (colorArray.length > 0) {
        if (!colorArray.find((i) => i.optionCode === this.currentExterior.optionCode)) {
          this.toSelectExerior(colorArray[0])
        }
      }
    },
    // 选择外观颜色
    async toSelectExerior(item) {
      console.log('%c 选择的颜色', 'font-size:16px;color:red;',item);
      if(this.carIdx == 3){
        this.currentView = 'color';
      }
      this.$store.commit('updateCurrentExterior', item)
      this.$store.commit('clearSelectedConfig', ['interior', 'option']) // 清理配置
      this.$store.dispatch('resetPageAllOptionState')
      this.$store.dispatch('setTimeAndPrice')
    },

    // 选择轮毂
    async toSelectHub(item,index) {
      console.log('%c 选择的轮毂', 'font-size:16px;color:red;',item);
      if (item.optionCode.includes('PC2')) {
        Toast('当前外饰包含组合的选装套装')
        this.$store.commit('updateFooterDesc', {
          desc: '当前外饰包含组合的选装套装，点击下一步查看'
        })
      }
      if(this.carIdx == 3){
        let hubarr = [
          {
            carimg:require("../../assets/testcar.jpg"),
            hubimg:require("../../assets/testhub1.png")
          },
          {
            carimg:require("../../assets/testcar.jpg"),
            hubimg:require("../../assets/testhub2.png")
          }
        ]
          const normalizedIndex = (index % 2);
          console.log(normalizedIndex,'normalizedIndexnormalizedIndexnormalizedIndex');
          
          this.currentHubImages = hubarr[normalizedIndex]
          this.currentView = 'hub'; // 显示轮毂区域
      }
      this.$store.commit('updateCurrentHub', item)
      this.$store.commit('clearSelectedConfig', ['interior', 'option']) // 清理配置
      this.$store.dispatch('resetPageAllOptionState')

      this.$store.dispatch('setTimeAndPrice')
    },

    // 下一步
    async nextPage() {
      /**
       * 半定逻辑
       * 半定在这里获取座椅数据,因为只有新UI无座椅展示,并且同时依赖外观和轮毂
       * 导致切换外观或轮毂时都需要更新座椅, 又因为外观色的切换可能会导致轮毂数据的更新,
       * 所以放在外观色里更新有可能会造成vos(座椅)脏请求, 故放在整理
       */
      if (this.currentModelLineData.measure === 1) {
        this.$store.commit('showLoading')
        await this.$store.dispatch('setVosArrayMeasure1')
        this.$store.commit('hideLoading')
      }

      this.$store.commit('updateConfigrationActiveTab', 'interior')

      this.clickExteriorSensors()// 埋点
    },

    // 埋点
    clickExteriorSensors() {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const { engine, customSeriesName } = this.currentModelLineData
      const param = {
        source_module: 'H5',
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        select_colour: this.currentExterior.optionName,
        select_hub: this.currentHub.optionName,
        button_name: '下一步',
        $event_duration: Math.floor((Date.now() - this.pageStartTime) / 1000)
      }

      // console.log(param)
      this.$sensors.track('CC_CarConfiguration_Exterior_BtnClick', param)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.collapse-wrapper {
  padding-bottom: @FooterHeight;
}

// 滚动区域
@ExteiorHeight: 220px;
.wrapper-scroll {
  overflow-y: auto;
  height: calc( 100vh - @HeaderHeight - @TabHeight - @ExteiorHeight - @FooterHeight);
}


.main-wrapper {
  position: relative;
  min-height: 220px;
  margin: 10px 0;

  >.to3d-btn {
    width: 52px;
    height: 52px;
    position: absolute;
    top: 18px;
    left: 24px;
    z-index: 2;
  }
}

.desc-wrapper {
  text-align: center;
  color: #333333;

  &.hub-margin-top{
    margin-top: 48px;
  }
  >.desc {
    font-size: 12px;
    margin-top: 4px;
    >.relative {
      position: relative;
      &.line-through {
        text-decoration:line-through;
      }
      .discount {
        position: absolute;
        left: 110%;
        top: 50%;
        transform: translateY(-50%);
        background-color: #EB0D3F;
        padding: 0 5px;
        line-height: 16px;
        color: #fff;
        font-size: 10px;
        width: max-content;
        text-wrap: nowrap;
      }
    }
  }

  .hub-name {
    color:#000;
  }
}


.exterior-wrapper,
.hub-wrapper {
  overflow: auto;
  padding:0 16px;
  padding-bottom: 15px;
  margin-bottom: 20px;
  .wrapper {
    width: max-content;
    margin-top: 14px;

    >.item {
      margin-left: 4px;
      display: inline-block;
      width: 70px;
      height: 70px;
      transform: scale(0.7);
      box-sizing: border-box;

      &.selected {
        transform: scale(1);
        border: 1px solid #000;
        padding: 1px;
        box-sizing: border-box;
      }

      img {
        vertical-align: inherit;
      }
    }
  }
}
</style>
