<template>
  <div name="orderList" class="orderList">
    <page-load
      @onRefresh="onRefresh"
      :pulldown.sync="pulldown"
      :pullupstate="false"
    >
      <div
        class="order_content"
        v-for="(item, index) in orderList"
        :key="index"
        @click.stop="toOrderDetail(item)"
      >
        <div class="order_content_left">
          <img
            :src="
              (item.dealerDto.imageUrl || '').includes('http')
                ? item.dealerDto.imageUrl
                : BaseConfigrationOssHost + item.dealerDto.imageUrl
            "
          />
        </div>
        <div class="order_content_right">
          <div class="order_content_right_top">
            <span class="order_name">{{ item.seriesDto.customSeriesName+"超长试驾" }}</span>
            <span class="order_state">{{ showStatusName(item) }}</span>
          </div>
          <div class="order_content_right_mid">
            {{ item.appoBeginTime }}
          </div>
          <div
            class="order_content_right_bottom"
            v-if="isShowCancel(item)"
          >
          <div @click.stop="onCancelOrder(item)">
              <AudiButton
              :text="'取消订单'"
              color="white"
              font-size="14px"
              height="30px"
              width="80px"
            />
          </div>

          </div>
        </div>
      </div>
      <div v-show="showNotData" class="not_data">
        <div>
          <img  :src="fromType ? require('@/assets/img/wenjuanNotDataa.png') : require('@/assets/img/empty-new-img.png')" alt="" />
        </div>
        <p>暂无内容</p>
      </div>
    </page-load>


  </div>
</template>

<script>
import PageLoad from "../../components/page-load.vue";
import {
  getVeryLongReservationOrderList,

} from "@/api/api";
import { callNative } from "@/utils";
import url from "@/config/url";
import AudiButton from "@/components/audi-button";
import { Popup } from 'vant'
import Vue from 'vue'

Vue.use(Popup)

export default {
  name: "OrderList",
  components: { PageLoad, AudiButton },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      pulldown: false, // 下拉
      showNotData: false,
      orderList: [],
      BaseConfigrationOssHost: url.BaseOssHost,

      modalshow: false,
      title: '',
      content: '',
      orderId:'',

      isTake:false,
      isSend:false,
      isAll:false,
      onCliickOrderItem:{},
    };
  },

  mounted() {


    this.getCarOrderList();
    // this.appCallJs()
  },
  watch: {},
  methods: {
    // APP调用刷新
    // appCallJs() {
    //   this.onRefresh()
    // },
    // 下拉刷新
    async onRefresh() {
      await this.getCarOrderList();
      this.pulldown = !this.pulldown;
    },

    // 获取车订单
    async getCarOrderList() {
      this.$store.commit("showLoading");
      const { data } = await getVeryLongReservationOrderList();
      console.log("哈哈", data)
      this.$store.commit("hideLoading");

      this.orderList = data.data || [];

      if (this.orderList.length > 0) {
        // const res = await Promise.all(this.orderList.map((i) => this.getOrderCarConfig(i.carCustomId)))
        // console.log(this.BaseConfigrationOssHost)
        // res.forEach((e, i) => {
        //   this.$set(this.orderList[i], 'modelNameCn', e.modelNameCn)
        //   this.$set(this.orderList[i], 'headImageUrl', e.headImageUrl)
        // })
        this.showNotData = false;
      } else {
        this.showNotData = true;
      }
    },
    showStatusName(item) {
     return ''
    },


    isShowCancel(item) {
     return false
    },

    async toOrderDetail(item) {
        this.$router.push({
          path: this.fromType ?  "/testdrive/long-service-detail?fromType=fromPurple" : '/testdrive/long-service-detail',
          query: {
            appoId: item.appoId,
          },
        });
    },


  },
};
</script>

<style lang="less" scoped>
.not_data {
  text-align: center;
  padding-top: 150px;
  img {
    width: 160px;
    height: 160px;
  }
  p{
    height: 20px;
    font-size: 13px;
    margin: 8px 0 0;
    font-weight: 400;
    color: #B3B3B3;
    line-height: 20px;
  }
}
.orderList {
  padding: 16px;
}

.order_content {
  display: flex;
  flex-direction: row;
  border-bottom: 1px #e5e5e5 solid;
  padding-bottom: 16px;
  padding-top: 16px;
  &_left {
    width: 102px;
    height: 102px;
    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }
  &_right {
    flex: 1;
    margin-left: 10px;
    margin-top: 5px;
    &_top {
      overflow: hidden;

      .order_name {
        font-size: 16px;
        font-family: "Audi-WideBold";
        float: left;
      }
      .order_state {
        float: right;
        font-size: 14px;
        color: #666666;
      }
    }

    &_mid {
      font-size: 12px;
      color: #666666;
      margin-top: 8px;
    }

    &_bottom {
      display: flex;
      flex-direction: row-reverse;
      margin-top: 38px;
    }
  }
}


  .center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ._modal {
    width: 343px;
    background: #FFFFFF;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    padding: 24px 0;

    .modal-title {
      font-size: 18px;
      font-weight: 500;
      color: #1A1A1A;
      margin-bottom: 16px;
      font-family: 'Audi-WideBold';
    }

    .modal-content {
      font-size: 14px;
      color: #333333;
      line-height: 18px;
      padding: 0 16px;
      font-family: 'Audi-Normal';
    }
    .service-line {
      width: 295px;
      height: 48px;
      background: #f2f2f2;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-bold {
        padding-left: 16px;
        font-size: 16px;
        color: #000;
        font-weight: normal;
        font-family: 'Audi-Normal';

      }
      .btn-change {
        padding-right: 16px;
        .btn-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
    .modal-confirm {
      margin-top: 24px;
      width: 85%;
      height: 56px;
      background: #1A1A1A;
      font-size: 16px;
      color: #FFFFFF;
    }

    .modal-cancel {
      width: 85%;
      border: 1px solid #1A1A1A;
      height: 56px;
      background: #fff;
      font-size: 16px;
      color: #000;
      margin-top: 8px;
    }

    .modal-bold-content {
      font-size: 18px;
      color: #1A1A1A;
      line-height: 32px;
      padding: 0 25px;
      font-weight: normal;
      font-family: "Audi-WideBold";
    }
  }
</style>
