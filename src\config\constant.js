/*
 * <AUTHOR> <PERSON>
 * @Date         : 2022-06-23 13:24:51
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-11-01 12:44:18
 * @FilePath     : \src\config\constant.js
 * @Descripttion :
 */
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-09-09 13:39:40
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-24 00:30:24
 * 一些公用的配置文件
 */


export const buyType = [
  '个人', '企业'
]

export const modifyBuyType = [
  {
    text: '个人',
    type: '01'
  },
  {
    text: '企业',
    type: '02'
  }
]

// 购车人证件类型
export const columns1 = [
  { text: '居民身份证', value: '1' },
  { text: '外国人护照', value: '101' },
  // { text: '军官证', value: '104' },
  { text: '港澳台居民往内地通行证（回乡证）', value: '102' },
  { text: '台湾居民来往大陆通行证（台胞证）', value: '103' },
  { text: '港澳居民居住证', value: '104' },
  { text: '台湾居民居住证', value: '105' },
]

// 购车人证件类型1.1
export const certificateJson = {
  1: '居民身份证',
  101: '外国人护照',
  102: '港澳台居民往内地通行证（回乡证）',
  103: '台湾居民来往大陆通行证（台胞证）',
  104: '港澳居民居住证',
  105: '台湾居民居住证'
  // 104:'军官证',
}

// 购车人证件类型

export const certificateType = [
  // 购车人证件类型
  {
    text: '居民身份证',
    value: '1',
    rules: new RegExp(/^(\d{15}$)|(^\d{17}([0-9]|X)$)/i),
    maxlength: 18
  },
  {
    text: '外国人护照',
    value: '101',
    rules: new RegExp(/^[A-Za-z0-9\u4e00-\u9fa5]+$/),
    maxlength: 21
  },
  {
    text: '港澳台居民往内地通行证（回乡证）',
    value: '102',
    rules: new RegExp(/^[A-Za-z0-9\u4e00-\u9fa5]+$/),
    maxlength: 21
  },
  {
    text: '台湾居民来往大陆通行证（台胞证）',
    value: '103',
    rules: new RegExp(/^[A-Za-z0-9\u4e00-\u9fa5]+$/),
    maxlength: 21
  },
  {
    text: '港澳居民居住证',
    value: '104',
    rules: new RegExp(/^[A-Za-z0-9\u4e00-\u9fa5]+$/),
    maxlength: 21
  },
  {
    text: '台湾居民居住证',
    value: '105',
    rules: new RegExp(/^[A-Za-z0-9\u4e00-\u9fa5]+$/),
    maxlength: 21
  },
]

export const LOAN_STATUS = [
  {
    status: '0000',
    text: ''
  },
  {
    status: '0001',
    text: ''
  },
  {
    status: '0002',
    text: '已提交'
  },
  {
    status: '0003',
    text: '审批通过'
  },
  {
    status: '0004',
    text: '审批未通过'
  },
  {
    status: '0005',
    text: '已签合同'
  },
  {
    status: '0006',
    text: '已签合同'
  },
  {
    status: '0007',
    text: '已放款'
  }
]

// 付款方式
export const paymentMethod = [
  {
    text: '全款',
    type: '10'
  },
  {
    text: '分期',
    type: '20'
  }
]

// 先行版 modelLineId
export const XIAN_XING_VERSION = '498B2Y005'
export const XIAN_JIAN_VERSION = '498B2Y006'
export const XIAN_XING_VERSION_Q5 = 'G4ICF3002'
export const ZHU_MENG_QING_CHUN_VERSION = '498BZG002'
export const ZHU_MENG_WEI_LAI_VERSION = '498BZG003'
