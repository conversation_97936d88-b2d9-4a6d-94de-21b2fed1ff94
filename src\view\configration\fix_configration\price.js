import { checkV2 } from '@/utils'
export const handlePriceHook = ($store) => {
    const optionIdsArr = []
    const {
        optionsSelected, idx,
        currentExColor,
        currentModelHub,
        currentInteriorChair,
        currentInteriorEih,
        currentSibColorInterieur,
        currentOptionsList
    } = $store.state
    if (idx != 2) return
    let carConfigArr = [{ currentExColor }, { currentModelHub },
    { currentInteriorEih, currentInteriorChair, currentSibColorInterieur },
    { currentOptionsList }]
    console.log("carConfigArr", carConfigArr);
    // carConfigArr.forEach((e, i) => {
    //     let index = optionsSelected.findIndex(op => i == (+op - 1))
    //     if (index != -1) {
    //         i == 0 && optionIdsArr.push(currentExColor.optionId);
    //         i == 1 && optionIdsArr.push(currentModelHub.optionId)
    //         i == 2 && currentInteriorEih.optionId && optionIdsArr.push(currentInteriorEih.optionId)
    //         i == 2 && currentInteriorChair.optionId && optionIdsArr.push(currentInteriorChair.optionId)
    //         i == 2 && currentSibColorInterieur.sibOptionId && optionIdsArr.push(currentSibColorInterieur.sibOptionId)
    //         i == 2 && currentSibColorInterieur.interieurOptionId && optionIdsArr.push(currentSibColorInterieur.interieurOptionId)
    //         i == 3 && currentOptionsList.forEach(el => el.optionId && el.optionCode && optionIdsArr.push(el.optionId))
    //     }
    // })
    optionIdsArr.push(currentExColor.optionId);
    optionIdsArr.push(currentModelHub.optionId)
    currentInteriorEih.optionId && optionIdsArr.push(currentInteriorEih.optionId)
    currentInteriorChair.optionId && optionIdsArr.push(currentInteriorChair.optionId)
    currentSibColorInterieur.sibOptionId && optionIdsArr.push(currentSibColorInterieur.sibOptionId)
    currentSibColorInterieur.interieurOptionId && optionIdsArr.push(currentSibColorInterieur.interieurOptionId)
    currentOptionsList.forEach(el => el.optionId && el.optionCode && optionIdsArr.push(el.optionId))
    let arr = optionIdsArr.filter(e => e)
    console.log(arr);
    $store.dispatch('getMeasurePriceComputeAction', arr)
    $store.dispatch('getDeliveryTimeComputeAction', arr)
}
