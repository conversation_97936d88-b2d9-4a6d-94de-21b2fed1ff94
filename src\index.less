.container {
  height: calc(100vh - 100px);
  .info-box {
    padding: 0 16px;
  }
}
.popup-custom {
  width: calc(100vw - 32px);
  padding: 16px 24px;
  z-index: 10001 !important;
  .custom-align-center {
    text-align: center;
  }
  &.popup-circle {
    width: 68vw;
    padding: 40px 0 20px;
    p {
      margin-top: 24px;
      font-size: 14px;
    }
  }
  // padding: 23px 16px;
  box-sizing: border-box;
  .popup-custom-main {
    padding-top: 20px;
    .align-center {
      text-align: center;
    }
    .h3 {
      margin-bottom: 16px;
      font-size: 18px;
      color: #1A1A1A;
    }
    .text {
      font-size: 16px;
      line-height: 24px;
      p {
        font-size: 12px;
        color: #666;
        line-height: 24px;
        margin: 6px 0 -12px 0;
      }
    }
  }
  .popup-custom-btn {
    &:not(.un-has-margin-top) {
      margin-top: 36px;
    }
    .btn-box {
      width: 100%;
    }
  }
}
.custom-ux-btn {
  .button {
    &.text-white-btn {
      background-color: #e5e5e5 !important;
      border-color: #e5e5e5;
    }
  }
}

.lan-dialog-custom {
  &.dialog {
      width: calc(100vw - 32px);
      padding: 23px 16px !important;
      box-sizing: border-box;
      border-radius: 0;
     .van-dialog__footer {
        width: 100%;
        height: 56px;
        padding-top: 1px;
        .van-button {
          margin: 0;
          width: 100%;
          height: 56px;
          line-height: 56px;
          font-size: 16px;
          text-align: center;
          border-radius: 0;
          border: 1px solid #000;
          color: #000;
          box-sizing: border-box;
          background: #fff;
          cursor: pointer;
          -webkit-box-flex: 0 0 auto;
          -webkit-flex: 0 0 auto;
          flex: 0 0 auto;
          display: block;
          &::after, &::before {
            display: none !important;
          }
          &.van-dialog__confirm {
            color: #fff;
            background: #000;
          }
        }
     }
     .van-dialog__content {
        padding: 20px 0 36px;
        min-height: 0;
        font-family: 'Audi-Normal';
        .van-dialog__message {
          min-height: 24px;
          line-height: 24px !important;
          font-size: 16px !important;
          padding: 0;
       }
     }
     .van-dialog__header {
        padding-top: 20px;
        font-weight: 600;

     }
     &.lan-dialog-content-f12 {
        .van-dialog__content{
          .van-dialog__message {
            font-size: 14px !important;
          }
        }
     }
     &.lan-dialog-anti-human {
        padding: 23px 23px 16px !important;
     }
     &.lan-dialog-middle {
        padding: 24px 20px !important;
        .van-dialog__footer {
          height: 42px;
          .van-button {
            height: 42px;
            line-height: 42px;
            font-size: 14px;
          }
        }
        .van-dialog__header {
          font-size: 16px;
          line-height: 24px;
          padding: 0;
        }
        .van-dialog__content {
          padding: 16px 0 20px;
          .van-dialog__message {
            color: #1A1A1A;
          }
        }
     }
  }
  &.line-two-cols {
    .van-dialog__footer {
      -webkit-box-pack: justify !important;
      -webkit-justify-content: space-between !important;
      -ms-flex-pack: justify !important;
      justify-content: space-between !important;
      -webkit-flex-direction: row !important;
      flex-direction: row  !important;
      .van-button {
        width: calc(50% - 2px) !important;
      }
    }
  }
  &.line-row-reverse {
    .van-dialog__footer {
      -webkit-flex-direction: row-reverse !important;
      flex-direction: row-reverse !important;
    }
  }
  &.lan-dialog-swap-skin {
    .van-dialog__footer {
      .van-button {
        color: #fff;
        background: #000;
        &.van-dialog__confirm {
          border: 1px solid #000;
          color: #000;
          background: #fff;
        }
      }
    }
  }
}

.lan-radio-group {
  .van-radio__icon--round {
    &.van-radio__icon {
      height: auto;
    }
    .van-icon {
      width: 24px;
      height: 24px;
      border-color: #808080;
      transition: all .1s;
      &::before {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #e5e5e5;
        transition: all .1s;
        overflow: hidden;
      }
    }
    &.van-radio__icon--checked {
      .van-icon {
        background-color: #fff;
        &::before {
          width: 12px;
          height: 12px;
          background-color: #333;
        }
      }
    }
    &.van-radio__icon--disabled {
      .van-icon {
        background-color: #fff;
        border-color: #ececec;
        &::before {
          background-color: #e5e5e5;
          opacity: .8;
        }
      }
    }
  }
}

.lan-square-checkbox {
  .van-checkbox__icon--square {
    .van-icon {
      border-color: #7f7f7f;
      &::before {
        position: relative;
        top: -2px;
        left: -1;
        color: #fff;
        opacity: 0;
      }
    }
    &.van-checkbox__icon--checked {
      .van-icon {
        border-color: #000;
        background-color: transparent;
        &::before {
          top: -1px;
          color: #000;
          opacity: 1;
        }
      }
    }
  }
  &.lan-square-img {
    .van-checkbox__icon--square {
      .van-icon {
        border: none;
        background: url('~@/assets/img/checkbox_normal.png') no-repeat 50%;
        background-size: contain;
        width: 13px;
        height: 13px;
        &::before {
          display: none;
        }
      }
      &.van-checkbox__icon--checked {
        .van-icon {
          background: url('~@/assets/img/checkbox_checked.png') no-repeat 50%;
          background-size: contain;
        }
      }
    }
  }
}

.has-fixed-bottom-button {
  padding-bottom: 78px
}
.lan-button-box {
  width: 100%;
  box-sizing: border-box;
  .lan-button {
    &.van-button--default {
      display: block !important;
    }
    height: 56px;
    line-height: 56px;
    font-size: 16px;
    width: 100%;
    text-align: center;
    border-radius: 0;
    &.lan-button-small {
      width: 93px;
      height: 32px;
      font-size: 12px;
    }
  }
  &.line-two-cols {
    width: calc(50% - 2px);
  }
  &.black-button {
    .lan-button {
      color: #fff;
      background-color: #000;
      border-color: #000;
      &.van-button--disabled {
        background-color: #d2d2d2;
        border-color: #d2d2d2;
        color: #666;
        &.lan-darkly {
          border-color: #B3B3B3;
          background-color: #B3B3B3;
          color: #fff;
        }
      }
      &.lan-button-loading {
        color: #eee;
        background-color: #999;
        border-color: #999;
      }
    }
  }
  &.white-button {
    .lan-button {
      color: #000;
      background-color: #fff;
      border-color: #000;
      &.van-button--disabled {
        border-color: #666;
        color: #333;
      }
    }
    &.white-border {
      .lan-button {
        border-color: #fff;
      }
    }
  }
  &.ghost-button {
    .lan-button {
      background-color: transparent;
    }
    &.white-button {
      .lan-button {
        color: #fff;
        border-color: #fff;
      }
    }
    &.black-button {
      .lan-button {
        color: #000;
        border-color: #000;
      }
    }

  }
  &.fixed-button {
    position: fixed;
    z-index: 999;
  }
  &.fixed-bottom-button {
    bottom: -1px;
    padding: 5px 16px 22px;
    width: 100%;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, .05);
    box-sizing: border-box;
    background-color: #fff;
  }
}

.order-tips {
  height: 34px;
  line-height: 34px;
  width: 100vw;
  font-size: 12px;
  color: #fff;
  text-align: center;
  padding: 0 16px;
  background-color: #000;
  box-sizing: border-box;
  &.align-left {
    text-align: left;
  }
}

.has-warning {
  position: relative;
  padding-right: 6px;
  &.is-warning {
    padding-right: 14px;
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 4px;
      width: 6px;
      height: 6px;
      margin-top: -3px;
      border-radius: 50%;
      background-color: #eb0d3f;
      overflow: hidden;
    }
  }
}

.van-loading {
  &.lan-loading-custom {
    position: fixed;
    z-index: 999999;
    &.enable-masking-out {
      &::after {
        content: '';
        position: fixed;
        z-index: 999998;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
      }

    }
    &.lan-loading-darkly {
      background-color: rgba(0, 0, 0, .9);
      color: #f50537;
      &.lan-custom-center {
        width: 118px;
        height: 104px;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        &::after {
          left: calc(-50vw + 59px);
          top: calc(-50vh + 52px);
        }
        .van-loading__spinner {
          width: 50px !important;
          height: 50px !important;
          position: absolute;
          z-index: 9;
          top: 14px;
          overflow: hidden;
          &::after {
            content: '';
            position: absolute;
            z-index: -1;
            left: 50%;
            top: 50%;
            width: 38px;
            height: 38px;
            border: 2px solid #e5e5e5;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            overflow: hidden;
          }
          .van-loading__circular {
            circle {
              stroke-width: 2;
            }
          }
        }
        .van-loading__text {
          padding-top: 55px;
          margin: 0;
          height: 22px;
          font-size: 14px;
          font-weight: 400;
          color: #FFF;
          line-height: 22px;
        }
        &.lan-loading-decephaly {
          .van-loading__spinner {
            top: 8px;
          }
        }
        &.lan-loading-lonely {
          .van-loading__spinner {
            top: 27px;
          }
        }
      }
    }
  }


  .lan-popup-area {
    padding-bottom: 34px;
    .van-area {
      .van-picker__toolbar {
        height: 54px;
        line-height: 54px;
        border-bottom: .5px solid #D9D9D9;
        .van-picker__confirm , .van-picker__title {
          font-size: 16px;
          color: #000;
        }
        .van-picker__title {
          font-weight: 500;
        }
      }
      &.cancel-icon {
        .van-picker__toolbar {
          .van-picker__cancel {
            text-indent: -999em;
            position: relative;
            padding-left: 22px;
            &::after {
              content: '';
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 24px;
              height: 24px;
              background: url(~@/assets/img/cross-leptonema.png) no-repeat 50%;
              background-size: 100%;
            }
          }
        }
      }
      .van-picker__mask {
        // top: -1px;
        height: calc(100% + 4px);
      }
      .van-picker__columns {
        .van-hairline-unset--top-bottom van-picker__frame {
          border-top: .5px solid #E5E5E5;
          border-bottom: .5px solid #E5E5E5;
        }
        .van-picker-column__item--selected {
          .van-ellipsis {
            font-size: 18px;
          }
        }
      }
    }
  }
}

.van-dialog.customstyle {
  border-radius: 0;
  padding: 16px 24px;
  top:50%;
  .van-dialog__header {
    font-size: 18px;
  }
  .van-dialog__content {
    margin-top: 16px;
    .van-dialog__message {
      color: #333333;
      font-size: 16px;
      line-height: 24px;
      padding: 0;
      margin-bottom:12px;
    }
  }

  .van-button {
    border: 1px solid #000;
    margin: 0 2px;
    height: 56px;
    line-height: 56px;
    &.van-dialog__confirm {
      color: #fff;
      background-color: #000;
    }
  }

  .van-hairline--left:after{
    border: none;
  }
}

// 报价单页修改配置弹窗ui
.van-dialog.quotation-dialogstyle {
  border-radius: 0;
  padding: 28px 24px 24px 24px;
  top:50%;
  box-sizing: border-box;
  width: 343px;

  .van-dialog__header {
    font-size: 15px;
  }
  .van-dialog__content--isolated{
    min-height: auto;
  }
  .van-dialog__content {
    .van-dialog__message {
      color: #333333;
      font-size: 16px;
      line-height: 24px;
      padding: 0;
    }
  }
  .van-dialog__footer {
    justify-content: space-between;
  }

  .van-dialog__cancel, .van-dialog__confirm  {
    flex: none;
  }

  .van-button {
    border: 1px solid #000;
    height: 44px;
    width: 144px;
    font-size: 14px;
    line-height: 44px;
    margin-top: 28px;
    box-sizing: border-box;
    &.van-dialog__confirm {
      color: #fff;
      background-color: #000;
    }
  }

  .van-hairline--left:after,
  .van-hairline--top:after{
    border: none;
  }
}


.top-banner-tips {
  height: 34px;
  line-height: 34px;
  padding: 0 16px;
  font-size: 12px;
  color: #000;
  background: #E5E5E5;
  &.no-both-sides {
    margin: 0 -16px;
  }
  .right {
    color: #333;
    .icon {
      font-size: 14px;
      position: relative;
      margin: 1px -5px 0 7px;
    }
  }
}

.lan-popup {
  &.lan-carseries-popup {
    background-color: #f7f7f7;
    padding: 20px 23px 70px;
    .carseries-title {
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      font-weight: 600;
      color: #000;
    }
    .van-popup__close-icon {
      display: block;
      top: 28px;
      right: 23px;
      width: 24px;
      height: 24px;
      background: url('~@/assets/icon-close.png') no-repeat 50% / contain;
      &::before {
        display: none;
      }
    }
  }
}