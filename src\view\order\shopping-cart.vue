<template>
  <!-- 我的购物车，给官方小程序使用的 -->
  <div class="container">
    <div class="top">
      <div
        :class="[tab===1?'current':'']"
        @click="tab=1"
      >
        整车
      </div>
      <div
        :class="[tab===2?'current':'']"
        @click="tab=2"
      >
        商品
      </div>
    </div>
    <div class="content">
      <div
        class="car"
        v-show="tab===1"
      >
        <shopping-cart-list />
      </div>
      <div
        class="goods"
        v-show="tab===2"
      >
        <div class="qidai">
          敬请期待
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import ShoppingCartList from './shopping-cart-list.vue'

export default {
  data() {
    return {
      tab: 1 // 1.整车 2.商品
    }
  },
  components: { ShoppingCartList },
  computed: {
    ...mapState(['env'])
  },
  mounted() {
    if (this.env === 'minip') {
      const query = this.$route.query
      console.log('queryyyyyqqqq', query)
      const token = query.token
      if (token) {
        localStorage.setItem('token', token)
        console.log(token)
      }
    }
  },

  methods: {

  }
}
</script>
<style lang="less" scoped>
.container{
  .top{
    box-sizing: border-box;
    height: 56px;width: 100vw;
    background-color: #f2f2f2;
    color: #999;
    border-top: solid 1px #e5e5e5;
    border-bottom: solid 1px #e5e5e5;
    display: flex;
    div{
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      &.current{
        color: #000;
        font-weight: bold;
      }
      &:active{
        background-color: #ccc;
      }
    }
  }
  .content{
    height: calc(100vh - 56px);
    overflow-y: auto;
    .qidai{
      text-align: center;
      margin-top: 40vh;
    }
  }
}
</style>
