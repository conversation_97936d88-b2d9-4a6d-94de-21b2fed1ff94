<template>
  <div class="modelline-wrapper">

    <div class="mainimg-wrapper">
      <img :src="mainImg | imgFix(640)" v-if="currentModelLineData.imageUrl" alt="">
    </div>

    <div class="title-wrapper c-bold">{{ carModelName }}</div>
    <div class="btn-detail" @click="showDetail">查看详情 <van-icon name="arrow" /></div>

    <!-- 套装列表 -->
    <div class="desc-text">请选择套装</div>
    <div class="c-flex-between">
      <div v-for="item in localModelLineList" :key="item.suit" class="item" :class="{
        selected: item.suit === currentModelLineData.suit
      }" @click="toSelectModel(item)">
        <div class="title">{{ item.suit }}</div>
        <div class="price" :class='{
          q6hide: q6BySeatSuit
        }'>
          <PreferentialPrice
            v-if="!!(seatPreferential(item.tags))"
            :price="item.price"
            :preferential="seatPreferential(item.tags)"
            :showFinalFormat="false"
          />
          <span class="orginal-price" v-else>¥{{ item.price | formatPrice }}</span>
        </div>
      </div>
    </div>

    <!-- 座椅列表 -->
    <div v-if="seatVisible" class="margin-top">
      <div class="desc-text">请选择座椅</div>
      <div class="c-flex-between">
        <div class="item" v-for="item in pageSeatList" :key="item.name" :class="{
        selected: item.name === currentSeat.name
      }" @click="toSelectSeat(item)">
          <div class="title">{{ item.name }}
            <span v-if="item.desc.length > 0">
              <van-popover v-model="showPopover" theme="dark" placement="top-end" trigger="click" :actions="item.desc">
                <template #reference>
                  <van-icon name="info-o" />
                </template>
              </van-popover>
            </span>
          </div>
          <div class="price">
            <PreferentialPrice
              v-if="seatPreferential(item.modelLine.tags) && item.price && seatPreferentialVisible"
              :preferential="seatPreferential(item.modelLine.tags)"
              :price="item.price"
              :showFinalFormat="false"
            />
              <span class="orginal-price" v-else>{{ item.price | finalFormatPriceDesc }}</span>
          </div>
        </div>
      </div>

      <!-- 只有羽林6座提示 -->
      <div v-show="currentModelLineData.modelLineCode === 'G6IBAY003' && currentSeat" class="yulin-sixseat" > *选装Audi AI Pro领航套装，可享限时优惠 </div>
    </div>

    <div class="layout-bottom">
      <AudiButton class='bottom-btn' color="black" height='56px' font-size="16px" text="去配置" @click="goToConfig" />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Icon, Popover } from 'vant'
import { mapState, mapGetters } from 'vuex'
import url from '@/config/url'
import AudiButton from '@/components/audi-button.vue'
import { isEmptyObj, getQueryParam } from '@/utils'
import { Q6_SAMURAI } from './util/carModelSeatData'
import {
  A7MR, DONGGAN_CODE, DONGGAN, XINCHAO
} from './car/a7mr'
import PreferentialPrice from './components/preferentialPrice.vue'

Vue.use(Icon)
Vue.use(Popover)

const OSS_URL = url.BaseConfigrationOssHost

export default {
  components: { AudiButton, PreferentialPrice },
  data() {
    return {
      ossUrl: OSS_URL,
      showPopover: false
    }
  },

  computed: {
    ...mapGetters(['currentSeriesName', 'pageSeatList', 'currentSeatsNum', 'pageAllOptionList', 'pageOptionComposes']),
    ...mapState({
      currentVersion: (state) => state.configration.currentVersion,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentOptions: (state) => state.configration.personalOptions,
      currentSeat: (state) => state.configration.currentSeat,
      carIdx: (state) => state.configration.carIdx
    }),

    seatPreferential() {
      return (tags) => {
        const currentTag = tags?.find((i) => i.tagCode === 'MODEL_PREFERENTIAL')
        if (currentTag && parseInt(currentTag.tagValue)) {
          return parseInt(currentTag.tagValue)
        }
        return 0
      }
    },

    // 特殊车型需要隐藏价格
    q6BySeatSuit() {
      return Q6_SAMURAI.includes(this.currentModelLineData.modelLineCode) || (this.currentSeriesName === 'q6' && this.currentModelLineData.modelYear === '2024')
    },
    // 显示座椅列表(只有q5和q6才会显示)
    seatVisible() {
      return this.currentSeriesName !== 'a7l'
    },

    seatPreferentialVisible() {
      return this.currentSeriesName !== 'q5e'
    },

    // 是否a7 mr 车
    isA7MrCar() {
      return A7MR.map((i) => i.code).includes(this.currentModelLineData.modelLineCode)
    },

    // a7 mr车需要切换套装大图(是否动感)
    mainImg() {
      const xinchaoCar = A7MR.filter((i) => i.suit === XINCHAO).map((i) => i.code).includes(this.currentModelLineData.modelLineCode)
      if (xinchaoCar && this.currentModelLineData.suit === DONGGAN) {
        return OSS_URL + this.currentModelLineData.imageUrl1
      }
      return OSS_URL + this.currentModelLineData.imageUrl
    },

    // 套装列表
    localModelLineList() {
      const seen = new Set()
      const result = this.currentVersion.modelLineList.filter((item) => {
        if (!seen.has(item.suit)) {
          seen.add(item.suit)
          return true
        }
        return false
      })


      /**
       * 新的a7mr车型业务逻辑,这里是做一种选装包
       * 默认展示的为展示一种套装,根据车型默认可能为新潮或者动感,见 a7mr.js
       * 如果是c类, 检测currentOptions 选装包里是否有 PAH+PC2 (动感套装),则手动创建数据展示
       * 如果是非c, 则还需要继续检查推荐组合里的内容
       */
      const hasDonggan = this.currentOptions.find((i) => DONGGAN_CODE.some((code) => code === i.optionCode))

      const isTurnonC = this.currentModelLineData.typeFlag?.includes('C')
      if (isTurnonC) {
        if (hasDonggan && hasDonggan.status !== 1) {
          // c 类, 同时选装包里的选装不为标装
          result.push({
            ...result[0],
            suit: hasDonggan.optionName,
            price: result[0].price + hasDonggan.price,
            optionCode: hasDonggan.optionCode // 用这个字段来判断是否是选装包(动感套装)
          })
        }
      } else {
        // 非c ,从组合里查询
        for (const compose of this.pageOptionComposes) {
          for (const item of compose.composePersonalOptions) {
            if (DONGGAN_CODE.includes(item.optionCode)) {
              result.push({
                ...result[0],
                suit: item.optionName,
                price: result[0].price + item.price,
                optionCode: item.optionCode // 用这个字段来判断是否是选装包(动感套装)
              })
              break
            }
          }
        }
      }

      return result
    },

    carModelName() {
      if (this.isA7MrCar) {
        // a7mr 车不显示套装名称
        return `${this.currentModelLineData.engine} ${this.currentVersion.styleName.replace('24VX款', '').replace('25款', '')} `
      }
      return `${this.currentModelLineData.engine} ${this.currentVersion.styleName} ${this.currentModelLineData.suit} `
    }
  },
  watch: {
    'currentVersion.styleId'() {
      this.toSelectModel(this.currentVersion.modelLineList[0])
    }
  },

  mounted() {
    this.defaultSelectModelLine()
  },

  methods: {
    // 数据排序以及默认选中车型套装
    async defaultSelectModelLine() {
      const defaultModelLine = this.getDefaultModelLine()
      this.toSelectModel(defaultModelLine)
    },

    // 获取默认配置线数据
    getDefaultModelLine() {
      if (!isEmptyObj(this.currentModelLineData)) {
        return this.currentModelLineData
      }
      return this.currentVersion.modelLineList[0]
    },

    // defaultSelectSeat
    getDefaultSeat() {
      // if (!isEmptyObj(this.currentSeat)) {
      //   return this.currentSeat
      // }

      /**
        * 特殊需求：
        * q6 24款的齐云型默认选中六座
        * styleid :6dba6e6f-362b-4e7c-94e2-603765de30ff
        */
      let seat6
      if (['6dba6e6f-362b-4e7c-94e2-603765de30ff'].includes(this.currentVersion.styleId)) {
        seat6 = this.pageSeatList.find((i) => i.tagCode === 'Q6_6SEAT')
      }
      return seat6 ?? this.pageSeatList[0]
    },

    // 显示配置详情
    showDetail() {
      const modelLineData = this.currentModelLineData

      // 这些车要显示特别版的图片+pdf
      const modelLineCodes = [
        // a7l
        '498B2Y007', // 黑武士55TFSI
        '498BZY007', // 黑武士45TFSI
        '498B2Y008', // 影武士45TFSI
        '498BZY008', // 影武士45TFSI
        '498B2Y005', // 先行耀黑套装
        '498BZY013', // 24款 45黑武士
        '498BZY012', // 24款 45影武士
        '498B2Y012', // 24款 55影武士
        '498B2Y011' // 24款 55 黑武士
      ]
      const isSpecial = modelLineCodes.includes(modelLineData.modelLineCode)

      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: modelLineData.modelLineId,
          carModelName: modelLineData.modelLineName,
          seriesCode: modelLineData.customSeriesCode,
          from: 'newConfigration',
          ...isSpecial ? { special: isSpecial } : {}
        }
      })

      this.clickCarModelSensors('查看详情')// 埋点
    },

    // 选中指定的配置线
    async toSelectModel(item) {
      if (this.currentModelLineData.suit === item.suit) {
        return
      }

      /**
       * a7mr车型特殊处理:
       * a7mr的车型这里切换配置线的时候(实际为添加选装包)
       */
      if (this.isA7MrCar) {
        if (DONGGAN_CODE.includes(item.optionCode)) {
          for (const option of this.currentOptions) {
            if (option.optionCode === item.optionCode) {
              // 动感套装， 选中选装包
              option.selected = true
              this.$store.commit('updateSelectedOptions', [option])
              break
            }
          }
        } else {
          // 新潮套装,清空选装包
          this.$store.commit('updateSelectedOptions', [])
        }

        this.$store.commit('updateCurrentModelLineData', item)
        return
      }


      this.$store.commit('updateCurrentModelLineData', item)

      // 这里要提前获取半定的座椅数据主要是设置座椅的显示条件
      if (this.currentModelLineData.measure === 1) {
        await this.$store.dispatch('setOutColorArrayMeasure1', null)
      }

      this.toSelectSeat(this.getDefaultSeat())
    },

    // 选中指定座椅
    toSelectSeat(item) {
      this.$store.commit('clearSelectedConfig', ['exterior', 'interior', 'option']) // 清空已选配置
      this.$store.dispatch('setSelectSeat', item)

      // 获取对应座椅数据
      const seats = this.currentSeatsNum
      this.requestData(seats)
    },

    async requestData(seats) {
      this.$store.commit('showLoading')

      /**
       * 获取配置的数据
       * 这里半定和全定的区别是半定的数据都依赖前一步的optionId请求,所以要前置请求
       * 后面的高定数据都是独立的,所以并行请求
       */
      if (this.currentModelLineData.measure === 1) {
        // 半定
        await Promise.all([
          this.$store.dispatch('setOutColorArrayMeasure1', seats),
          this.$store.dispatch('setPersonalOptions', seats)
        ])
      } else {
        // 更新配置
        await Promise.all([
          this.$store.dispatch('setPersonalOptions', seats),
          this.$store.dispatch('setOutColorArray', seats),
          this.$store.dispatch('setHubArray', seats),
          this.$store.dispatch('setSibArray', seats),
          this.$store.dispatch('setEihArray', seats),
          this.$store.dispatch('setVosArray', seats)
        ])

        // test debug
        if (process.env.NODE_ENV === 'development') {
          console.log('allConifg:', {
            modelLine: this.$store.state.configration.currentModelLineData,
            outColor: this.$store.state.configration.outColorArray,
            hub: this.$store.state.configration.hubArray,
            sib: this.$store.state.configration.sibArray,
            eih: this.$store.state.configration.eihArray,
            vos: this.$store.state.configration.vosArray,
            options: this.$store.state.configration.personalOptions
          })
        }
      }


      this.$store.commit('hideLoading')
    },

    // 跳转到配置页面
    goToConfig() {
      const {
        orderStatus, orderId, ccid, from, getback, action
      } = this.$route.query

      this.$router.push({
        path: '/configrationContainer',
        query: {
          orderStatus,
          orderId,
          ccid,
          from,
          getback,
          action
        }
      })

      this.clickCarModelSensors('去配置')// 埋点
    },

    // 埋点
    clickCarModelSensors(buttonName) {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const {
        engine, customSeriesName, suit
      } = this.currentModelLineData
      const param = {
        source_module: 'H5',
        car_series: carMap[this.carIdx],
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        suit_name: suit,
        seat_name: this.currentSeat.name,
        button_name: buttonName
      }

      // console.log(param)
      this.$sensors.track('CC_SelectCar_CarModel_BtnClick', param)
    }
  }
}
</script>


<style lang="less" scoped>
@import "../../assets/style/common.less";

.van-icon {
  font-size: 14px;

  &.van-icon-info-o {
    left: 2px;
    font-size: 15px;
  }

  &::before {
    vertical-align: middle;
  }
}

.margin-top {
  margin-top: 10px;
}

.yulin-sixseat {
  margin-top: 15px;
  width: 100%;
  height: 24px;
  font-size: 12px;
  line-height: 24px;
  background: #F2F2F2;
}

.modelline-wrapper {
  .mainimg-wrapper {
    min-height: 200px;
  }

  .title-wrapper {
    text-align: center;
    font-size: 14px;
    line-height: 22px;
  }

  .btn-detail {
    text-align: center;
    font-size: 12px;
    color: #000;
    opacity: 0.5;
    margin-top: 4px;
    line-height: 20px;

    display: flex;
    align-items: center;
    justify-content: center;
    >.van-icon {
      margin-left: 2px;
      display: flex;
    }
  }

  .desc-text {
    font-size: 14px;
    margin-top: 23px;
    color: #000;
    opacity: 0.5;
  }

  .item {
    position: relative;
    margin-top: 15px;
    background-color: #fff;
    font-size: 14px;
    padding: 16px 0px 16px 12px;
    border: 1px solid #D9D9D9;
    width: 49%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &.selected {
      border: 1px solid #000;
    }

    .title {
      line-height: 22px;
      display: flex;
      align-items: center;
    }

    .price {
      margin-top: 4px;
      color:#4C4C4C;
      // color: #999;
      // opacity: 0.4;
      line-height: 18px;
      .orginal-price {
        font-size: 12px;
        transform: scale(0.8);
        transform-origin: 0 center;
        display: block;
      }

      .price-before-preferential {
        font-size: 12px;
        margin-right: 10px;
        text-decoration: line-through;
        color: #999
      }
      .price-after-preferential {
        color: #F50538;
      }
      &.q6hide {
        opacity: 0;
      }
    }
  }

  .layout-bottom {
    position: fixed;
    bottom: 50px;
    width: 90%;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>


<style lang="less">
// 细节弹窗样式
.van-popover {
  .van-popover__content {
    border-radius: 0;
    padding: 8px 0;
  }

  .van-popover__action {
    height: auto;
    width: 200px;
    padding: 2px 12px;
  }

  .van-popover__action-text {
    justify-content: flex-start;
  }

  .van-hairline--bottom:after {
    border: none;
  }
}
</style>
