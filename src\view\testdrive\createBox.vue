<template>
  <div class="page">
    <div class="testdrive">
      <div id="detailBox" :style="{background: '#ccc'}">
        <div
          class="custom-header"
          :style="{
            height: `${device && device.headerHeight || 50}px`,
            'border-width': `${device && device.statusBarHeight || 0}px`
          }"
          data-flex="main:justify cross:center"
        >
          <div class="back-left" @click="toBack" v-if='platformType !== "wechat" && env != "minip"'>
            <img src="~@/assets/Icon-back-left-black.png" />
          </div>
          <div class="share-right" @click="toShare" v-if='platformType !== "wechat" && env != "minip"'>
            <img src="~@/assets/Icon-share-right-black.png" />
          </div>
        </div>
        <div class="lan-swipe-box" v-if="!initTimeout">
          <div
            class="lan-swipe-screen"
            :style="{
              background: `url(${$loadWebpImage(
                images[idx].imgs[0]
              )}) no-repeat 0 50% / cover`
            }"
          ></div>
          <!-- :initial-swipe="initialSwipe" -->
          <!-- <van-swipe
            ref="refSwipe"
            class="lan-swipe-screen"
            :duration="900"
            :autoplay="3200"
          >
            <van-swipe-item
              v-for="(list, index) in imagesSwipe.imgs"
              :key="index"
              :style="{ background:`url(${$loadWebpImage(list)}) no-repeat 0 50% / cover`}"
            >
              <p class="captions">
                {{ imagesSwipe.text[index] }}
              </p>
            </van-swipe-item>
          </van-swipe> -->
        </div>

        <div :class="['tabBox']" v-if="!initTimeout">
          <div class="title-box">
            <div class="title">预约试驾</div>
            <div class="sub text-two-hidd">{{ images[idx].text[0] }}</div>
          </div>
          <div class="formBox">
            <van-form
              ref="form"
              :show-error-message="showErrorMessage"
              @failed="failed"
            >
              <div
                class="form-field"
                @click.stop="animation('carSeries')"
                id="carSeries"
              >
                <van-field
                  label="意向车系"
                  input-align="left"
                  placeholder="请选择意向车系"
                  v-model="images[idx].seriesName"
                  :label-align="labelAlign"
                  :label-width="labelWidth"
                  readonly
                  is-link
                  ref="carSeries"
                  @click="carSeriesPopupShow = true"
                  :rules="[{ required: true, message: '请选择意向车系' }]"
                />
              </div>
              <div class="form-field">
                <van-field
                  label="体验地点"
                  name="destination"
                  :class="`${!isLocation ? 'no-location' : ''}`"
                  :label-align="labelAlign"
                  input-align="left"
                  :label-width="labelWidth"
                  readonly
                  is-link
                  placeholder="'请选择体验地点"
                  v-model="formData.agent.dealerName"
                  :rules="[{ required: true, message: '请选择体验地点'}]"
                  @click="locationShow = true"
                >
                  <template v-if="!isLocation" #button>
                    <div
                      @click.stop="handleGetLocation"
                      class="location-btn text-btn"
                    >
                      获取位置
                    </div>
                  </template>
                </van-field>
              </div>
              <div class="form-field field-hidden">
                <van-field
                  label="试驾姓名"
                  input-align="left"
                  placeholder="请输入姓名"
                  name="name"
                  readonly
                  :label-align="labelAlign"
                  :label-width="labelWidth"
                  v-model="formData.fullName"
                  :maxlength="maxlength.fullName"
                  :rules="[{ required: true, message: `请填写姓名！` }]"
                />
              </div>
              <div class="form-field">
                <van-field
                  label="手机号码"
                  input-align="left"
                  placeholder="请输入手机号"
                  name="mobile"
                  :readonly="!youkeVis"
                  :label-align="labelAlign"
                  :label-width="labelWidth"
                  v-model="formData.mobile"
                  :maxlength="11"
                  type="number"
                  :rules="[
                    { required: true, message: `请填写手机号` },
                    {
                      pattern: /^1[3456789]\d{9}$/,
                      message: '请填写正确的手机号'
                    }
                  ]"
                />
              </div>
              <div class="form-field" v-if="youkeVis">
                <van-field
                  label="验证码"
                  :label-align="labelAlign"
                  input-align="left"
                  :label-width="labelWidth"
                  placeholder="请输入验证码"
                  v-model="formData.code"
                  :maxlength="6"
                  type="number"
                  :rules="[
                    { required: true, message: '请输入验证码' },
                    { pattern: /^\d{6}$/, message: '请输入正确的验证码' }
                  ]"
                >
                  <template #button>
                    <div
                      @click.stop="onSendSMS"
                      :class="[
                        'send-code-btn text-btn',
                        isGainCode ? '' : ' closed'
                      ]"
                    >
                      {{
                        !showBool && count ? `${count}s 可发送` : '获取验证码'
                      }}
                    </div>
                  </template>
                </van-field>
              </div>

              <div class="copyright">
                <van-field
                  name="checkbox"
                  :rules="[
                    {
                      required: true,
                      message: '请阅读并勾选《隐私条款》和《用户协议》'
                    }
                  ]"
                >
                  <template #input>
                    <van-checkbox v-model="checkbox">
                      我已阅读并接受<span @click.stop="handleBtn(0)"
                        >《隐私条款》</span
                      >和<span @click.stop="handleBtn(1)">《用户协议》</span>
                    </van-checkbox>
                  </template>
                </van-field>
              </div>
              <van-button @click="onSubmit" class="subBtn" type="primary">
                立即预约
              </van-button>
            </van-form>
          </div>
          <div
            v-if="successVisible"
            class="succB lan-testdrive-succeed"
            data-flex="main:center cross:center"
          >
            <div class="succeed">
              <div class="circle">
                <img src="../../assets/img/contract-success.png" />
              </div>
             <p class="title">预约试驾成功</p>
             <p>感谢您预约试驾上汽奥迪汽车，会有相关人员尽快与您联系</p>
            </div>

            <van-button
              v-if="0"
              @click="handleGo"
              style="
                width: 100%;
                margin-top: 140px;
                height: 56px;
                background-color: black;
                border: none;
              "
              type="primary"
            >
              查看我的预约
            </van-button>
          </div>
        </div>
        <model
          :modalshow.sync="modalshow"
          @update:modalshow="submitTestDriverOrder"
          title="确定提交预约试驾？"
        />

        <van-dialog
          v-model="visibleOrder"
          title="您有未完成的试驾订单，您可预约其他车系"
        >
          <div style="display: flex; justify-content: center; margin-top: 40px">
            <div class="borB" @click="handleBack">取消</div>
            <div class="goB" @click="handleMoney">进入试驾</div>
          </div>
        </van-dialog>

        <div class="masking" v-if="idx != 3">
          <div class="more">
            <span>了解更多</span><img src="~@/assets/Icon-arrow-down--.png" />
          </div>
        </div>
        <van-popup
          closeable
          position="bottom"
          class="lan-popup lan-carseries-popup"
          v-model="carSeriesPopupShow"
        >
          <div class="carseries-title">请选择车系</div>
          <car-series-pop @confirmSelectionSeries="handleReceiveSeries" />
        </van-popup>
      </div>
    </div>
    <van-loading
      class="lan-loading-custom lan-custom-center lan-loading-darkly enable-masking-out lan-loading-lonely"
      size="24px"
      vertical
      v-if="loading"
    />
    <div class="model-analysis">
      <template
        v-if="
        modelAnalysisPage &&
          modelAnalysisPage.content &&
          modelAnalysisPage.content.contentDetailList
        "
      >
        <img
          class="image"
          :src="$loadWebpImage(BaseOssHost + list.imageUrl)"
          :alt="`车型解析 - ${list.title}`"
          v-for="list of modelAnalysisPage.content.contentDetailList"
          :key="list.title"
        />
      </template>
    </div>
    <div class="totop" @click="scrollToTop" v-if="scrollTo">
      <img src="~@/assets/Icon-totop.png" />
    </div>
    <van-dialog
      class="dialog lan-dialog-custom lan-dialog-middle line-two-cols"
      v-model="isLocationDialog"
      :close-on-click-overlay="true"
      cancel-button-text="取消"
      confirm-button-text="去设置"
      show-cancel-button
      title="位置授权提示"
      message="当前需要开启位置授权，请在手机系统设置，开启位置授权"
      @confirm="handleGetOpenpage"
      @cancel="isLocationDialog = false"
    />
    <van-popup v-model="locationShow" position="bottom">
      <Lan-cascader
        title="请选择体验地点"
        :close.sync="locationShow"
        :options="dealerCitesList"
        :field-names="{ value: 'code' }"
        :async-tab-index="1"
        :async-column="cityDealerList"
        @asyncNext="handleGetDealerList"
        @finish="handleGetDealerFinished"
      />
    </van-popup>
    <network @reload="networkReload()"></network>
  </div>
</template>

<script>
import Vue from 'vue'
import dayjs from 'dayjs'
import {
  Lazyload,
  Swipe,
  SwipeItem,
  Tab,
  Tabs,
  Button,
  Form,
  Field,
  Icon,
  Picker,
  Popup,
  DatetimePicker,
  RadioGroup,
  Radio,
  Checkbox,
  Toast
} from 'vant'
import { mapState, mapGetters } from 'vuex'
import wx from 'weixin-js-sdk'
import { Base64 } from 'js-base64'
import { JSEncrypt_KEY, Encrypt } from "@/utils/configs.js";
import {
  getUserInfo,
  getDealerList,
  getNearestDealerList,
  getInviteUser,
  getDealerByCode,
  getUserTestDriveStatus,
  getDealerListByTestDrive,
  sendCode,
  getprivacy,
  getpolicy,
  getAPITimeOutTesting,
  getFavoriteCarMiniprogramData
} from '@/api/api'
import model from '@/components/model.vue'
import network from '@/components/network.vue'
import CarSeriesPop from '@/components/car-series-pop.vue'
import { WEICHAT_MINIP } from '@/config/conf.data'
import cityList from '@/utils/city'
import LanCascader from '@/components/cascader.vue'
import { getCity } from '../../api/detail'
import {
  getQueryParam,
  callNative,
  getLocationCityName,
  sleep,
  delay
} from '../../utils'
import {
  createTestDriver,
  createInviteTestDriver,
  getModelList,
  getListResource,
  judgeChuangshi,
  getCustomSeries,
  getOrgsForAppointmentList,
  createTouristYouke
} from '../../api/test-driver'
import api from '../../config/url'
import axios from 'axios'

Vue.use(Form)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(Popup)
  .use(Toast)
  .use(DatetimePicker)
  .use(Swipe)
  .use(SwipeItem)
  .use(Lazyload)
  .use(Tab)
  .use(Tabs)
  .use(Button)
  .use(Radio)
  .use(RadioGroup)
  .use(Checkbox)
const codeType = ['00', '200']
export default {
  name: 'TestdriverCreate',
  inject: ['reload', 'checkLoginFn'],
  // eslint-disable-next-line vue/no-unused-components
  components: {
    model,
    network,
    'car-series-pop': CarSeriesPop,
    LanCascader: LanCascader
  },
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      youkeVis: true,
      arrowVis: true,
      initialSwipe: 0,
      successVisible: false,
      checkbox: false,
      visibleOrder: false,
      activeName: 'G4',
      imagesSwipe: {},
      images: [
        {
          // url: require('@/assets/trycar/A7L_01.jpg'),
          share: {
            title: '上汽奥迪A7L 37.87万元起 性能豪华旗舰',
            img: 'https://audi-oss.saic-audi.mobi/audicc/app/order/td/A7L_SR.jpg'
          },
          name: '49',
          seriesName: 'A7L',
          typeName: 'A7L',
          text: [
            '上汽奥迪A7L 37.87万元起 性能豪华旗舰',
            '上汽奥迪A7L，可享融资租赁超低首付1元起，超低日付39元起，更可享至多18重购车权益',
            '上汽奥迪A7L，搭载静音电吸无框车门，尽显豪华新潮；3.0T V6发动机及主动式空气悬架，稳稳领跑'
          ],
          imgs: [
            'https://audi-oss.saic-audi.mobi/audicc/app/order/td/A7L_BG_01.jpg'
            // 'http://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/A7L_02.jpg',
            // 'http://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/A7L_03.jpg'
          ]
        },
        {
          // url: require('@/assets/trycar/Q5e_01.jpg'),
          share: {
            title: '上汽奥迪Q5 e-tron，20万级6/7座纯电旗舰SUV',
            img: 'https://audi-oss.saic-audi.mobi/audicc/app/order/td/Q5e_SR.jpg'
          },
          name: 'G4',
          seriesName: 'Q5 e-tron',
          typeName: 'Q5e',
          text: [
            '上汽奥迪Q5 e-tron，20万级6/7座纯电旗舰SUV',
            '上汽奥迪Q5 e-tron，现可享最低0首付，最长60期融资租赁方案',
            '上汽奥迪Q5 e-tron，购车可享最高12项礼遇和多项无忧充电服务'
          ],
          imgs: [
            'https://audi-oss.saic-audi.mobi/audicc/app/order/td/Q5e_BG_01.jpg'
            // 'http://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/Q5e_01.jpg',
            // 'http://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/Q5e_03.jpg'
          ]
        },
        {
          // url: require('@/assets/trycar/Q6_01.jpg'),
          share: {
            title: '上汽奥迪Q6，全家人的奥迪',
            img: 'https://audi-oss.saic-audi.mobi/audicc/app/order/td/Q6_SR.jpg'
          },
          name: 'G6',
          seriesName: 'Q6',
          typeName: 'Q6',
          text: [
            '上汽奥迪Q6，46.76万起，大6/7座奢华SUV',
            '上汽奥迪Q6，可享融资租赁超低首付1元起，超低日付41元起，更可享至多18 重购车权益',
            '上汽奥迪Q6，首付低至1元起，最长60 期融资租赁方案，全系搭载智能quattro四驱'
          ],
          imgs: [
            'https://audi-oss.saic-audi.mobi/audicc/app/order/td/Q6_BG_01.jpg'
            // 'http://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/Q6_02.jpg',
            // 'http://audi-oss.saic-audi.mobi/audicc/app/order/testdriver/Q6_01.jpg'
          ]
        },
        {
          // url: require('@/assets/trycar/A7L_01.jpg'),
          share: {
            title: '全新上汽奥迪 A5L Sportback 点击了解详情',
            img: 'https://audi-oss.saic-audi.mobi/audicc/app/order/td/A5L_SR.jpg'
          },
          name: 'F0',
          seriesName: 'A5L',
          typeName: 'A5L',
          text: [
            '全新上汽奥迪 A5L Sportback 智美绝尘'
          ],
          imgs: [
            'https://audi-oss.saic-audi.mobi/audicc/app/order/td/A5L_BG_01.jpg'
          ]
        },
      ],
      idx: '3',
      cityDefultIndex: 0,
      listResource: [],
      BaseOssHost: api.BaseOssHost,
      modalshow: false,
      minDate: new Date(),
      maxDate: new Date(2022, 10, 30),
      formDataShow: {
        inclinedModelPicker: false,
        showPicker: false,
        showCarCity: false,
        showPicker1: false,
        showPicker2: false,
        showPicker3: false,
        monthDay: false,
        hourMinute: false
      },
      cascaderValue: '',
      // 2.0T/3.0T
      displacement: [
        { code: '2.0T', description: '2.0T' },
        { code: '3.0T', description: '3.0T' }
      ],
      columns: [
        // { code: '498B', description: '奥迪A7L' }
      ],
      // columns1: [{ id: 1, text: '到店试驾' }, { id: 2, text: '上门试驾' }, { id: 3, text: '深度试驾' }],
      columns1: [
        {
          text: '到店',
          children: [
            { text: '试驾', id: 1 },
            { text: '试乘', id: 4 }
          ]
        }
      ],
      columns2: [
        {
          dealerCode: '76631019',
          dealerName: 'Audi house of progress SH',
          dealerAdrress: '上海市'
        }
      ],
      columns3: [{ id: '76631019', text: '展厅' }],
      columns4: ['2021-09-01', '2021-09-02'], // 日期
      columns5: [
        '10:00-11:00',
        '11:00-12:00',
        '12:00-13:00',
        '13:00-14:00',
        '14:00-15:00'
      ], // 时间段
      meishijian: false, // 是否有可选时间
      formData: {
        code: '',
        inclinedModel: '2.0T',
        testdriveSeries: '', // 试驾车系 试驾车型
        testdriveMode: { text: '试乘试驾', id: 1 }, // 试驾方式 columns1
        testdrivePlace: { id: '76631019', text: '展厅' }, // 预约地点
        agent: '', // 代理商
        fullName: '游客用户', // 设置默认值
        remarks: '',
        monthDay: '',
        monthDayValue: 0,
        hourMinute: '',
        mobile: '',
        carLicenseCityName: '', // 省市区
        inviteUserMobile: ''
      },
      defaultIndex: {
        testdriveSeries: 0,
        testdrivePlace: 0,
        testdriveMode: 0,
        columns4: 0,
        agent: 0
      },
      labelWidth: 72,
      labelAlign: 'left',
      areaList: [],
      animations: {
        fullName: false,
        remarks: false,
        mobile: false
      },
      rows: {
        fullName: '1',
        mobile: '1',
        remarks: '1'
      },
      maxlength: {
        fullName: '32',
        inviteUserMobile: '32',
        remarks: '400'
      },
      dealerList: [], // 经过各种筛选之后的
      dealerListAll: [], // 根据接口获取的
      isChuanshika: false, // 是否是传世卡用户
      agentfromcar: {}, // 从爱车页传来的经销商数据
      inviteCode: '',
      cityName: '',
      infoPosition: {},
      showBool: true,
      count: '',
      timer: null,
      urlObj: null,
      positioning: '',
      carSeriesPopupShow: false,
      device: {},
      showErrorMessage: false,
      modelAnalysisStorage: [],
      modelAnalysisPage: [],
      locationShow: false,
      isLocation: true,
      isLocationDialog: false,
      dealerCitesList: [],
      cityDealerList: [],
      isGainCode: false,
      scrollTo: false,
      loading: false,
      onLoadTimestamp: 0,
      isLocationLostToast: true,
      isSetPageLeaveData: false,
      platformType: ''
    }
  },
  computed: {
    ...mapState({
      env: 'env',
      wxLocation: 'wxLocation',
      userLocation: 'userLocation',
      initTimeout: 'initTimeout'
    }),
    showBottomButton() {
      return this.$store.state.showBottomButton
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log('beforeRouteEnter from', from)
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path // 获取上一级路由的路径
    })
  },
  async mounted() {
    this.onPageScroll()
    this.initFn()
    const idx = getQueryParam('idx')
    if (this.env === 'minip') {
      this.$sensors.track('MP_LoveCar_TestDrive_PageView', {
        source_module: 'H5',
        car_model: idx == 1 ? 'Q5 e-tron' : idx == 2 ? 'Q6' : idx == 3 ? 'A5L' : 'A7L'
      })
    }
    if (this.$route.query?.platformType) {
      this.platformType = this.$route.query?.platformType
      document.title='\u200E'
    }
    const onLoadTimestamp = dayjs().unix()
    this.onLoadTimestamp = onLoadTimestamp
    this.SensorsSetPageView()
  },
  created() {
    console.log('检查是否需要跳转到登录页')
    this.youkeVis = !this.checkLoginFn('预约试驾') // youkeVis=true 游客模式
    // this.youkeVis = true
    console.log('document.referrer::', document.referrer)
    console.log('1==========1', this.$route)
    if (!this.youkeVis) this.getUserInfo()
    this.getprivacyFn()
  },
  activated() {
    this.$store.commit('setHeaderVisible', false)
    this.initFn()
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log('获取上一级路由的路径', from, 'from')
      // 获取上一级路由的路径
      if (from.path == '/') {
        const fd = vm.$storage.getPlus('YUYUEFD')
        if (fd) {
          vm.formData = fd
        }
      }
    })
  },
  methods: {
    ...mapGetters(['getDevice']),
    async handleBtn(e) {
      const res = await getprivacy()
      const res1 = await getpolicy()
      this.urlObj = {
        privacy: res.data.data.configValue,
        policy: res1.data.data.configValue
      }
      console.log(this.urlObj)
      let path = ''
      if (e == 0) path = this.urlObj.privacy
      if (e == 1) path = this.urlObj.policy // 用户协议
      this.$storage.setPlus('YUYUEFD', this.formData)
      if (this.env === 'minip') {
        callNative('audiOpen', { path: path, showHeader: true })
      } else {
        this.$router.push({
          path: this.fromType ? '/user-policy?fromType=fromPurple' : '/user-policy',
          query: {
            url: path,
            title: e == 0 ? '隐私条款' : '用户协议'
          }
        })
      }

      // window.location.href = path
    },
    async getprivacyFn() {
      const res = await getprivacy()
      const { data } = await getpolicy()
      data.configValue
      this.urlObj = {
        privacy: res.data.data.configValue,
        policy: data.configValue
      }
    },
    async onSendSMS() {
      const { showBool, count } = this
      if (!showBool && count) return

      try {
        await this.$refs.form.validate('mobile')
      } catch (error) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: error.message || '请填写手机号码',
          forbidClick: true,
          duration: 800
        })
      }

      // const smsToast = Toast({
      //   className: 'toast-dark-mini toast-pos-middle',
      //   message: '短信发送中',
      //   forbidClick: true,
      //   duration: 30000
      // })
      this.loading = true
      const phone = this.formData.mobile
      const { data } = await sendCode({ phone }).catch(async (error) => {
        // smsToast.message = '短信发送失败'
        this.loading = false
        await sleep(200)
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '短信发送失败',
          forbidClick: true
        })
        // await sleep(1500)
        // smsToast.close()
      })
      // smsToast.message = data?.code === '00' ? '短信已发出' : (data?.message || '短信发送失败')
      this.loading = false
      await sleep(200)
      Toast({
        className: 'toast-dark-mini toast-pos-middle',
        message:
          data?.code === '00' ? '短信已发出' : data?.message || '短信发送失败',
        forbidClick: true
      })
      // await sleep(1500)
      // smsToast.close()
      if (data.code !== '00') return
      this.setCountdown()
    },
    setCountdown() {
      const TIME_COUNT = 60
      if (!this.timer) {
        this.count = TIME_COUNT
        this.showBool = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.showBool = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    async initFn() {
      this.device = this.getDevice() || {}

      console.log(
        '%c [ device ]-589',
        'font-size:14px; background:#cf222e; color:#fff;',
        this.device
      )
      this.youkeVis = !this.checkLoginFn('预约试驾')

      this.$store.commit('setHeaderVisible', false)
      console.log('页面 query:', this.$route.query)
      const idx = getQueryParam('idx')
      console.log(idx, 'aaaaaaaaaaa')
      //   if(this.env === 'minip'){
      //   this.$sensors.track('MP_LoveCar_TestDrive_PageView', {
      //     source_module: 'H5',
      //     car_model: idx==1?'Q5 e-tron':idx==2?'Q6':'A7L'
      //   })
      // }
      console.log('idxidx:', idx)
      if (idx.length > 0) {
        this.idx = idx
        this.activeName = ['49', 'G4', 'G6', 'F0'][idx]
        this.initialSwipe = ['G4', 'G6', '49', 'F0'].findIndex(
          (el) => el == this.activeName
        )
        this.imagesSwipe = this.images.find((e) => e.seriesCode == this.activeName)
        console.log(
          this.imagesSwipe,
          'activeName initialSwipe',
          this.activeName,
          this.initialSwipe
        )
      } else {
        this.imagesSwipe = this.images[0]
      }

      if (this.$route.query && this.$route.query.inviteCode) {
        this.inviteCode = this.$route.query.inviteCode
      }

      this.dealerCitesList = cityList
      // await this.initUserLocation()
      getAPITimeOutTesting()
      this.getModelAnalysisData()
      await this.getCustomSeries()
      this.initInfo()
      Toast.clear()
    },
    async toBack() {
      // this.$router.go(-1)
      // if (this.successVisible) {
      //   this.successVisible = false
      //   return
      // }
      this.SensorsSetPageLeaveData()
      this.isSetPageLeaveData = true
      callNative('close', {})
      // callNative('close', { type: 'address' })
      // const data = await callNative('prepage', { times: 1 })
      // form: 'carMalls'

      // console.log('goBack=', data)
    },
    handleMoney() {
      this.visibleOrder = false
      const tmp = this.activeName
      const arr = ['G4', 'G6', '49', 'F0']
      const i = arr.findIndex((e) => e == tmp)
      this.activeName = arr[i == 2 ? 0 : i + 1]
      this.imagesSwipe = this.images.find((e) => e.seriesCode == this.activeName)
    },
    handleBack() {
      this.visibleOrder = false
    },
    handleGo() {
      this.$router.push({
        path: this.fromType ? '/testdrive/appointment-list?fromType=fromPurple' : '/testdrive/appointment-list'
      })
    },
    lookShopping() {
      console.log(this.activeName, this.columns)
      const idx = this.columns.findIndex((e) => e.seriesCode == this.activeName)
      const { positioning } = this
      this.$router.push({
        path: this.fromType ? '/car-malls?fromType=fromPurple' : '/car-malls',
        query: {
          positioning,
          seriesCode: this.activeName,
          idx: idx == -1 ? 0 : idx,
          jumpType: getQueryParam('jumpType') || '2'
        }
      })
    },
    onClick(e, t) {
      console.log(this.activeName, 'onClick', e, t)
      const i = ['G4', 'G6', '49', 'F0'].findIndex((el) => el == e)
      this.imagesSwipe = this.images.find((e) => e.seriesCode == this.activeName)
      // this.$refs.refSwipe.swipeTo(i)
      this.formData.testdriveSeries = {
        seriesCode: e
      }

      if (
        (this.isChuanshika && e === 49) ||
        (getQueryParam('dealerCode') && getQueryParam('form') !== 'order')
      ) {
        this.arrowVis = false
      } else {
        this.arrowVis = true
      }

      if (this.successVisible) {
        this.successVisible = false
      }

      if (e == 49) this.judgeChuangshiFn(e)
      if (this.isChuanshika && e != 49) {
        this.initInfo()
      }
    },
    selectMonthDay() {
      if (this.meishijian) {
        Toast({
          type: 'fail',
          message: '暂无可预约时间',
          icon: require('../../assets/img/error.png')
        })
      } else {
        this.formDataShow.monthDay = true
      }
    },
    selectHourMinute() {
      if (this.columns5.length > 0) {
        this.formDataShow.hourMinute = true
      } else {
        Toast({
          type: 'fail',
          message: '暂无可预约时间',
          icon: require('../../assets/img/error.png')
        })
      }
    },
    wanshan() {
      this.$router.push({
        path: this.fromType ? '/testdrive/upload-license?fromType=fromPurple' : '/testdrive/upload-license',
        query: {
          mobile: this.formData.mobile
        }
      })
    },
    doShowCarCity() {
      if (getQueryParam('dealerCode')) {
        this.formDataShow.showCarCity = false
        return
      }

      if (!this.isChuanshika) {
        // 是传世卡用户则城市不可选
        this.formDataShow.showCarCity = true
      }
    },
    agentChange() {
      if (getQueryParam('dealerCode') && getQueryParam('form') !== 'order')
        return
      if (this.isChuanshika && this.activeName == 49) {
        // 创世卡用户（A7L),且不可更改代理商
        Toast({
          type: 'fail',
          message: '创世卡用户（A7L),且不可更改代理商',
          icon: require('../../assets/img/error.png')
        })
        return
      }
      this.lookShopping()
    },
    inputValue(val) {
      this.formData[val] = this.limitstr(this.formData[val], 32)
    },
    limitstr(strval, strnum) {
      let re = ''
      const strleng = strval.length
      // 返回字符串的总字节数
      // eslint-disable-next-line no-control-regex
      const byteleng = strval.replace(/[^\x00-\xff]/g, '**').length
      if (byteleng <= strnum) return strval
      for (let i = 0, bytenum = 0; i < strleng; i++) {
        const byte = strval.charAt(i)
        // eslint-disable-next-line no-control-regex
        if (/[\x00-\xff]/.test(byte)) {
          bytenum++ // 单字节字符累加1
        } else {
          bytenum += 2 // 非单字节字符累加2
        }
        if (bytenum <= strnum) {
          re += byte
        } else {
          return re
        }
      }
    },

    // 表单提交
    submit() {
      // console.log('提交表单1')
      // console.log('提交表单1', this.formData)
      this.$refs.form.submit()
      // 埋点
      // const timer = new Date().getTime()
      // this.dataCollection( timer )
      const params = {
        test_drive_car: this.formData.testdriveSeries.seriesName,
        name: this.formData.fullName,
        contact_information: this.formData.mobile,
        city: this.formData.carLicenseCityName,
        test_drive_type: this.formData.testdriveMode.text,
        agent: this.formData.agent.dealerName,
        test_drive_date: this.formData.monthDay,
        test_drive_time: this.formData.hourMinute,
        appointment_type: '试乘试驾'
      }
      this.$sensors.track('testDriveInformation', params)
    },
    dataCollection(timer) {
      const params = {
        types_name: this.formData.fullName,
        $event_duration: timer
      }
      this.$sensors.track('booktestdrive', params)
    },
    async onA5lSubmit() {
      const res = await axios.get(
        api.BaseApiUrl + "/api-wap/audi-test-drive/api/v1/audi/token/getAudiToken"
      );
      window.console.log('🚀 ~ file:createBox method:onA5lSubmit line:1010 -----', res)
      let isToken = {};
      if (localStorage.getItem('token')) {
        isToken = {
          "x-access-token": localStorage.getItem('token'),
          "X-AUDI-TOKEN": res.data.data,
        };
      } else {
        isToken = {
          "X-AUDI-TOKEN": res.data.data,
        };
      }
      let userMobile = Encrypt(this.formData.mobile, JSEncrypt_KEY)
      const formData = {
        channelCode: this.env === 'minip' ? this.$route.query.channelCode || "app-audi"
          : "app-audi",
        activityCode: this.$route.query?.activityCode || "",
        // userName: '',
        userMobile: userMobile,
        provinceCode: this.formData?.agent?.provinceCode,
        cityCode: this.formData?.agent?.cityCode,
        seriesCode: 'F0',
        seriesName: 'Audi A5L',
        dealerCode: this.formData?.agent?.dealerCode,
        verificationCode: this.formData?.code || '',
        channel: this.env === 'minip' ? 1 : 2,
      };
      const { data } = await axios.post(
        api.BaseApiUrl +
        "/api-wap/audi-test-drive/api/v1/agreement/checkSubmitClueAgreement/new",
        formData,
        {
          headers: isToken,
        }
      );
      window.console.log('🚀 ~ file:createBox method:onA5lSubmit line:1045 -----', data)

      this.showLoading = false;
      if (data.code === "200") {
        this.a5LSensorsSetSubmitData(true, "提交成功", data.code);
        this.successVisible = true
      } else {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: data.message || '请求错误',
          forbidClick: true
        })
        this.a5LSensorsSetSubmitData(
          false,
          data.message || "提交失败",
          data.code
        );
      }
    },
    a5LSensorsSetSubmitData(success, reason, code) {
      const seriesName = this.formData.carCode;
      this.$sensors.track("H5_LoveCar_InfoCollect_BtnClick", {
        car_series: seriesName,
        is_success: success,
        fail_reason: reason,
        fail_code: code,
        button_name: "立即预约",
        source_module: "H5",
      });
    },
    async onSubmit() {
      this.$sensors.track('MP_LoveCar_TestDrive_BtnClick', {
        source_module: 'H5',
        car_model:
          this.activeName == 'G4'
            ? 'Q5 e-tron'
            : this.activeName == 'G6'
            ? 'Q6'
              : this.activeName == 'F0'
                ? 'A5L'
            : 'A7L',
        belong_page: '预约试驾详情页',
        button_name: '预约试驾'
      })

      try {
        await this.$refs.form.validate()
      } catch (errors) {
        const [{ message, name }] = errors || [
          { message: '请完善预约试驾信息', name: '' }
        ]
        // if (name === 'destination') this.initInfo()

        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message,
          forbidClick: true,
          duration: 800
        })
        console.log(
          '%c [ errors ]-719',
          'font-size:14px; background:#cf222e; color:#fff;',
          errors,
          message
        )
        return
      }
      // A5L不需要执行试乘试驾逻辑
      if (this.activeName == 'F0') {
        this.onA5lSubmit()
        return false
      }
      const seriesCode = this.formData.testdriveSeries.seriesCode
      const mobile = this.formData.mobile
      // if (this.youkeVis && !mobile) return

      this.loading = true

      try {
        const res = await getUserTestDriveStatus({ seriesCode, mobile }).catch(
          (err) => {
            this.loading = false
          }
        )
        console.log('getUserTestDriveStatusFn', res)
        if (res.data.code == '500001') {
          this.visibleOrder = true
          this.loading = false
          this.SensorsSetSubmitData(
            false,
            res.data.message || '预约失败',
            res.data.code
          )
        } else {
          // this.modalshow = true
          this.submitTestDriverOrder()
        }
      } catch (error) {
        this.loading = false
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '请求错误',
          forbidClick: true
        })
      }
    },
    async submitTestDriverOrder() {
      // this.$store.commit('showLoading')
      const data = {
        inclinedModel:
          this.formData.testdriveSeries.customSeriesName == 'A7L'
            ? this.formData.inclinedModel
            : '',
        // custIdCard: '761235123123',
        seriesCode: this.formData.testdriveSeries.seriesCode,
        appoPhone: this.formData.mobile,
        code: this.formData?.code || '',
        appoName: this.formData.fullName,
        appoType: '90030',
        orgCode: this.formData?.agent?.dealerCode,
        activityCode: '0001',
        channel: 2,
        inviteCode: this.inviteCode
      }

      let subFun = createInviteTestDriver
      if (this.youkeVis) {
        subFun = createTouristYouke
        data.mobile = data.appoPhone
        delete data.appoPhone
      }
      console.log('提交表单1', this.formData, data)

      subFun(data)
        .then(async (res) => {
          this.loading = false
          console.log(' 预约试驾 提交表单 res: ', res)
          if (res.status === 200 && res.data.code === '200') {
            // this.$router.push({
            //   path: '/testdrive/cancel-success',
            //   query: {
            //     type: 0
            //   }
            // })
            if (this.env === 'minip') {
              this.$sensors.track('MP_LoveCar_TestDrive_Result', {
                source_module: 'H5',
                car_model:
                  this.activeName == 'G4'
                    ? 'Q5 e-tron'
                    : this.activeName == 'G6'
                    ? 'Q6'
                      : this.activeName == 'F0'
                        ? 'A5L'
                    : 'A7L',
                is_success: true,
                conversion_type:
                  getQueryParam('jumpType') && getQueryParam('jumpType') == '1'
                    ? '落地页直接转化'
                    : '经首页转化'
              })
            }

            this.SensorsSetSubmitData(true, '预约成功', res.data.code)

            // submitToast.message = res.data.message || '预约成功'
            // submitToast.close()
            // this.modalshow = false
            this.successVisible = true
            const params = {
              test_drive_id: '',
              is_it_successful: true,
              failure_reason: '',
              failure_code: '',
              orderFrom: this.env === 'minip' ? 1001 : 1000
            }
            this.$sensors.track('testDriveResults', params)
          } else {
            // res.data.message && Toast({
            //   type: 'fail',
            //   message: res.data.message || '请求错误',
            //   icon: require('../../assets/img/error.png')
            // })
            Toast({
              className: 'toast-dark-mini toast-pos-middle',
              message: res.data.message || '请求错误',
              forbidClick: true
            })
            this.SensorsSetSubmitData(
              false,
              res.data.message || '预约失败',
              res.data.code
            )
            const params = {
              test_drive_id: '',
              is_it_successful: false,
              failure_reason: res.data.message,
              failure_code: res.data.code
            }
            this.$sensors.track('testDriveResults', params)
            if (this.env === 'minip') {
              this.$sensors.track('MP_LoveCar_TestDrive_Result', {
                source_module: 'H5',
                car_model:
                  this.activeName == 'G4'
                    ? 'Q5 e-tron'
                    : this.activeName == 'G6'
                    ? 'Q6'
                      : this.activeName == 'F0'
                        ? 'A5L'
                    : 'A7L',
                is_success: false,
                conversion_type:
                  getQueryParam('jumpType') && getQueryParam('jumpType') == '1'
                    ? '落地页直接转化'
                    : '经首页转化'
              })
            }
          }
          // this.$store.commit('hideLoading')
        })
        .catch(async () => {
          this.loading = false
          this.SensorsSetSubmitData(false, '预约失败', 500)
          Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: '预约失败',
            forbidClick: true
          })
        })
    },
    failed(err) {
      console.log(err)
    },

    resort(dealerList) {
      const idx = dealerList.findIndex((item) => item.distance !== 0)
      const truncate = dealerList.slice(0, idx)
      dealerList.splice(0, idx)
      return dealerList.concat(truncate)
    },
    onCancel() {
      this.formDataShow.showPicker = false
      this.formDataShow.showCarCity = false
      this.formDataShow.showPicker1 = false
      this.formDataShow.showPicker2 = false
      this.formDataShow.showPicker3 = false
      this.formDataShow.monthDay = false
      this.formDataShow.hourMinute = false
    },
    trasfromTimeInfos(info) {
      // appoStatus 可约状态  1.可约  2.不可约
      const tmp = []
      info.forEach((e) => {
        const str1 = e.beginTime.substring(11, 16)
        const str2 = e.endTime.substring(11, 16)
        const boo1 = e.appoStatus === 2
        tmp.push({ text: `${str1}-${str2}`, disabled: boo1 })
      })
      return tmp
    },
    async getCustomSeries() {
      await getCustomSeries().then(async (res) => {
        res.data.data.forEach((series) => {
          series.customSeriesName = series.customSeriesName.replace('Audi ', '')
        })
        this.columns = res.data.data
        this.formData.testdriveSeries = this.columns[this.idx] || ''
        // this.getData()
        // this.getListResource()
        // let dealerCode = getQueryParam('dealerCode')
        // if (dealerCode) {
        //   return
        // }
        // this.judgeChuangshi()
      })
    },
    // 需要调用接口判断是否是传世卡用户，
    // 1).是传世卡用户，界面上将不能选择代理商，当前接口会返回默认的：代理商code，名称
    // 2).不是，接口会返回空。此时界面中可以调用代理商列表接口，让用户自己选择代理商
    async judgeChuangshiFn(seriesCode = '') {
      const data = {
        seriesCode: seriesCode
      }
      console.log(data)
      if (seriesCode == 49 && !this.youkeVis) {
        const res = await judgeChuangshi(data)

        console.log(
          '%c [ judgeChuangshi ]-1031',
          'font-size:14px; background:#cf222e; color:#fff;',
          res
        )
        console.log('judgeChuangshi', res)
        if (res.status === 200 && res.data.code === '200') {
          if (res.data.data) {
            // 有数据说明是传世卡用户，替换返回的经销商,并且城市不可选
            this.isChuanshika = true
            this.arrowVis = false // 不显示箭头
            this.formData.agent.dealerName = res.data.data.orgName
            this.formData.agent.dealerCode = res.data.data.orgCode
          } else if (this.agentfromcar?.dealerName) {
            // 如果有从爱车页传来的经销商数据，替换，但可选
            this.formData.agent = this.agentfromcar
            this.formData.carLicenseCityName = this.agentfromcar.cityName
          }
        }
      }

      const dealerCode = getQueryParam('dealerCode')
      if (!dealerCode) {
        return
      }
      if (getQueryParam('form') !== 'order') {
        this.arrowVis = false
      }
      if (this.isChuanshika && seriesCode == 49) return // 是创始卡 A7L customSeriesCode: "49"
      const res1 = await getDealerByCode({ dealerCode })
      const dataBox = res1.data
      console.log('代理商信息：', dataBox.data)
      const addr = dataBox.data.dealerAdrress
      const dealerAdrress = `${dataBox.data.provinceName}/${dataBox.data.cityName}`
      const dealerName = dataBox.data.dealerName
      this.formDataShow.showCarCity = false
      this.formData.carLicenseCityName = dealerAdrress
      this.formData.agent = {
        dealerCode: dealerCode,
        dealerName: dealerName,
        dealerAdrress: dealerAdrress,
        addr: addr,
        provinceCode: dataBox?.data?.provinceCode,
        cityCode: dataBox?.data?.cityCode,
      }
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`
      }
      if (type === 'month') {
        return `${val}月`
      }
      if (type === 'day') {
        return `${val}日`
      }
      return val
    },
    // lebel平移
    animation(ref) {
      this.animations[ref] = true
      this.$refs[ref].focus()
      if (this.blur) {
        this.$refs[ref].blur()
      }
    },
    async getUserInfo() {
      const { data } = await getUserInfo()
      console.log('getUserInfo data', data)
      if (codeType.includes(data.code)) {
        const { userInfo } = data.data
        this.formData.mobile = userInfo.mobile
        const fullName = userInfo?.nickname || userInfo?.displayName || ''
        if (fullName) this.formData.fullName = fullName
        // if (getQueryParam('form') !== 'order') {
        //   this.formData.fullName = ''
        // }
        localStorage.setItem('userInfo', JSON.stringify(userInfo))
        // this.getCustomSeries()
      }
    },
    filterInput(val) {
      return val.replace(/[^-_a-zA-Z0-9\u4e00-\u9fa5]/, '')
    },

    async initUserLocation() {
      console.log('this.env', this.env)
      if (this.env === 'minip') {
        // this.getData()
        const wxLocation = this.wxLocation
        const cityName = await getLocationCityName(wxLocation.split(','))
        console.log('cityName', cityName)
        if (cityName) {
          this.cityName = cityName
        }
      } else {
        // fix:调试
        // const res = await callNative('getLocationCity', {})
        // if (res.location) {
        //   const arrayLocation = res.location.split(',')
        //   city = await getLocationCityName([
        //     arrayLocation[1] * 1,
        //     arrayLocation[0] * 1
        //   ])
        //   this.cityName = city
        //   this.$store.commit('setUserLocation', res.location)
        // }
      }
      console.log('获取定位信息, ', this.cityName)
    },
    getInviteUser(inviteCode) {
      getInviteUser({ inviteCode: inviteCode }).then((res) => {
        this.formData.inviteUserMobile = res.data.data.mobile
      })
    },
    dealerFn() {
      const { dealerCode, form, dealerNameBox, dealerCodeBox, seriesCode } =
        this.$route.query
      if (seriesCode) {
        this.activeName = seriesCode
        this.imagesSwipe = this.images.find((e) => e.name == this.activeName)
      }
      if (form == 'carMalls') {
        this.formData.agent = {
          dealerName: dealerNameBox,
          dealerCode: dealerCodeBox
        }
        console.log(this.formData.agent)
        return
      }

      if (this.activeName) {
        this.judgeChuangshiFn(this.activeName)
      }
    },
    async getDealerListByTestDriveFn(e) {
      console.log(this.env, '当前定位信息：', e)
      const params = {}
      const location = e?.split(',')
      if (this.env != 'minip') {
        params.latitude = location[0]
        params.longitude = location[1]
      } else {
        params.latitude = location[1]
        params.longitude = location[0]
      }
      const p = {
        defaultHeadquarters: 0,
        latitude: params.latitude,
        longitude: params.longitude // 经度
      }
      console.log('入参：', p)
      const res = await getDealerListByTestDrive(p)
      console.log('出参：', res)
      console.log('列表第一个元素', res?.data?.data[0])
      const { dealerCode, dealerName, provinceCode, cityCode } = res?.data?.data[0] || {}
      this.formData.agent = {
        dealerCode: dealerCode || '',
        dealerName: dealerName || '',
        provinceCode: provinceCode || '',
        cityCode: cityCode || '',
      }
      console.log('代理商', this.formData.agent)
      this.dealerFn()
    },
    async initInfo() {
      // let location =  this.env === 'minip' ?  this.wxLocation : this.userLocation?.location
      // location = location || ''
      let location = ''
      const {
        dealerCode,
        form,
        dealerNameBox,
        dealerCodeBox,
        seriesCode,
        mobile,
        name
      } = this.$route.query
      if (form === 'order' && name) {
        this.formData.fullName = name
      }
      if (form == 'carMalls' && dealerNameBox && dealerCodeBox) {
        this.activeName = seriesCode
        this.imagesSwipe = this.images.find((e) => e.name == this.activeName)
        this.formData.agent = {
          dealerName: dealerNameBox,
          dealerCode: dealerCodeBox
        }
        console.log(this.formData.agent)
        return
      }

      if (this.env != 'minip') {
        // const dataLocal = {}
        delay(() => {
          console.log(
            '%c [ delay loading isLocation : location ]-1262',
            'font-size:14px; background:#cf222e; color:#fff;',
            this.loading,
            this.isLocation,
            location
          )
          if (!this.isLocation) {
            !this.loading &&
              this.isLocationLostToast &&
              Toast({
                className: 'toast-dark-mini toast-pos-middle',
                message: !location
                  ? '获取定位响应超时'
                  : '获取定位超时，请重试',
                forbidClick: true,
                duration: 800
              })
            this.isLocation = false
          }
        }, 10000)
        const dataLocal = (await callNative('getLocationCity', {})) || {}
        location = dataLocal?.location?.length > 7 ? dataLocal.location : ''
        this.positioning = `${location}|${dataLocal.city}`
      } else {
        location = this.wxLocation
        // this.cityName = '上海市'
      }
      console.log(
        '%c [ 定位信息，userLocation： ]-1198',
        'font-size:14px; background:#cf222e; color:#fff;',
        location,
        location?.length
      )

      if (location?.length > 7) {
        this.getDealerListByTestDriveFn(location)
        this.isLocation = true
      } else {
        this.isLocation = false
        this.dealerFn()
      }
    },
    handleReceiveSeries({ idx, name, seriesCode }) {
      if (this.idx !== idx) {
        this.idx = idx
        this.activeName = seriesCode
        this.formData.testdriveSeries = {
          seriesCode: seriesCode,
          customSeriesName: name
        }
      }
      this.SensorsSetPageView(idx)
      console.info('🚀 ~ file:createBox method:handleReceiveSeries line:1584 -----', this.activeName)
      this.getModelAnalysisData()
      setTimeout(() => {
        this.carSeriesPopupShow = false
      }, 300)
    },
    toShare() {
      const { idx, images: cx } = this
      const { PROGRAMID } = WEICHAT_MINIP
      const title = cx[idx].share?.title || cx[idx].text[0] || '上汽奥迪'
      const photo = ''
      const images = cx[idx].share?.img || cx[idx].imgs[0]
      const path = `/pages/testdrive/create?idx=${idx}&from=app-testdrive&getback=strack`
      const url = ''
      callNative('audiMinipShare', {
        title,
        photo,
        images,
        path,
        program_id: PROGRAMID || '',
        url
      })
    },
    async getModelAnalysisData() {
      const { idx, images, modelAnalysisStorage } = this

      console.log(
        '%c [ modelAnalysisStorage ]-1222',
        'font-size:14px; background:#cf222e; color:#fff;',
        modelAnalysisStorage,
        idx
      )
      const MAPD = modelAnalysisStorage[idx] || {}
      if (MAPD && Object.keys(MAPD).length) {
        this.modelAnalysisPage = MAPD
        return
      }
      try {
        const {
          data: { code, data }
        } = await getFavoriteCarMiniprogramData({ code: 'audi-favorite-car4' })
        if (code === '00' && data?.length) {
          const carType = images[idx].typeName
          this.modelAnalysisPage = data.find(
            (i) => i.floorFrontCode === `audi-favorite-car4-${carType}-details`
          )
          this.modelAnalysisStorage[idx] = this.modelAnalysisPage
        }
      } catch (error) {}
    },
    async handleGetLocation() {
      const { status } = (await callNative('positioningStateFetch', {})) || {
        status: false
      }
      console.log(
        '%c [ positioningStateFetch ]-1268',
        'font-size:14px; background:#cf222e; color:#fff;',
        status
      )
      if (!status) {
        this.isLocationDialog = true
        return
      }
      this.initInfo()
    },
    async handleGetOpenpage() {
      callNative('openpage', {})
    },
    async handleGetDealerList({ value, index, text }) {
      if (!value) {
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '请选择正确的城市',
          forbidClick: true,
          duration: 1500
        })
      }

      try {
        const { positioning } = this
        const [latitude, longitude] = positioning
          ?.substring(0, positioning.indexOf('|') - 1)
          ?.split(',') || ['', '']

        const {
          data: { data, code, message }
        } = await getDealerListByTestDrive({
          latitude: latitude || '',
          longitude: longitude || '',
          defaultHeadquarters: 0,
          cityCode: value
          // ifRegionCodeByCity: 1,
          // defaultHeadquarters: 1,
          // page: 'orderDealerList'
        })
        if (code !== '00') {
          return Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: message || '代理商查询失败',
            forbidClick: true,
            duration: 1500
          })
        }
        const dealerList = data.map((i) => {
          const { distance } = i
          i.distance = distance || 0
          // eslint-disable-next-line no-nested-ternary
          i.distanceStr = distance
            ? distance < 1000
              ? `${Math.round(distance)}m`
              : `${Math.round((distance / 1000) * 10) / 10}km`
            : ''
          i.skin = 'dealer'
          i.value = i.dealerCode
          i.text = i.dealerName
          return i
        })

        this.cityDealerList = [index, { value, text }, dealerList]
      } catch (error) {
        console.log(
          '%c [ error ]-1348',
          'font-size:14px; background:#cf222e; color:#fff;',
          error
        )
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '代理商查询失败',
          forbidClick: true,
          duration: 1500
        })
      }
    },
    handleGetDealerFinished({ selected, item }) {
      console.info('🚀 ~ file:createBox method:handleGetDealerFinished line:1720 -----', item)
      const { dealerCode, dealerName, provinceCode, cityCode } = item
      this.formData.agent = {
        dealerCode: dealerCode || '',
        dealerName: dealerName || '',
        provinceCode: provinceCode || '',
        cityCode: cityCode || '',
      }
      if (!this.isLocation) this.isLocation = true
      console.log(
        '%c [ selected, item ]-1358',
        'font-size:14px; background:#cf222e; color:#fff;',
        selected,
        item,
        dealerCode,
        dealerName,
        provinceCode,
        cityCode
      )
    },
    onPageScroll() {
      const scrollListener = document.querySelector('.view-wrapper')
      if (scrollListener) {
        scrollListener.addEventListener('scroll', (a) => {
          const height =
            document.documentElement.clientHeight || document.body.clientHeight
          const scrollTop = scrollListener.scrollTop
          const isScrollTo = scrollTop >= (height || 1000)
          this.scrollTo !== isScrollTo && (this.scrollTo = isScrollTo)
        })
      }
    },
    scrollToTop() {
      const scrollListener = document.querySelector('.view-wrapper')
      const scrollTop = scrollListener.scrollTop

      console.log(
        '%c [ scrollTop ]-1422',
        'font-size:14px; background:#cf222e; color:#fff;',
        scrollTop
      )
      if (scrollListener && scrollTop > 0) {
        // scrollListener.scrollTo({ top: 0, behavior: 'auto' })
        window.requestAnimationFrame(goToTop)
      }
      function goToTop() {
        const currentPosition = scrollListener.scrollTop
        if (currentPosition > 0) {
          window.requestAnimationFrame(goToTop)
          scrollListener.scrollTo(0, currentPosition - currentPosition / 8)
        }
      }
    },
    SensorsSetPageView(ix) {
      const { idx } = this || getQueryParam()
      this.$sensors.track('H5_LoveCar_TestDrive_PageView', {
        source_module: 'H5',
        belong_page: '预约试驾页',
        car_series: this.images[ix ?? idx].seriesName
      })
    },
    SensorsSetSubmitData(success, reason, code) {
      const { idx } = this || getQueryParam()
      this.$sensors.track('H5_LoveCar_TestDrive_BtnClick', {
        car_series: this.images[idx].seriesName,
        is_success: success,
        fail_reason: reason,
        fail_code: code,
        belong_page: '预约试驾页',
        button_name: '预约试驾',
        source_module: 'H5'
      })
    },
    SensorsSetPageLeaveData() {
      const nowTimestamp = dayjs().unix()
      const { idx } = this || getQueryParam()
      this.$sensors.track('H5_LoveCar_TestDrive_PageLeave', {
        source_module: 'H5',
        belong_page: '预约试驾页',
        car_series: this.images[idx].seriesName,
        $event_duration: nowTimestamp - this.onLoadTimestamp
      })
    },
    networkReload() {
      this.initFn()
    }
  },
  watch: {
    $route: {
      immediate: true, // 刷新页面后立即监听
      handler(to, from) {
        console.log('获取前一页的url::', from)
        if (from) {
          // 可能会为空，所以要先判断
          //  this.href=from.path
        } else {
          // this.href=''
        }
      }
    },
    inviteCode(val) {
      if (val) {
        this.getInviteUser(val)
      }
    },
    async 'formData.testdriveSeries'(val) {
      const index = this.columns.findIndex((item) => item.content === val)
      this.defaultIndex.testdriveSeries = index
      if (this.youkeVis) return
      const { data } = await getOrgsForAppointmentList({
        appoDate: '',
        seriesCode: this.formData.testdriveSeries.seriesCode
      })
      console.log(data)
      if (data.code === '200') {
        const orgs = data.data
        // 根据返回的orgs筛选一遍dealerList
        const tmp = []
        for (let i = 0, l = this.dealerListAll.length; i < l; i++) {
          if (orgs.includes(this.dealerListAll[i].dealerCode)) {
            tmp.push(this.dealerListAll[i])
          }
        }
        this.dealerList = tmp
        // this.getData()
      }
      if (this.formData.agent && this.formData.testdriveSeries) {
        // this.getListResource()
      }
    },
    'formData.testdriveMode'(val) {
      const index = this.columns1.findIndex((item) => item === val)
      this.defaultIndex.testdriveMode = index
    },
    'formData.testdrivePlace'(val) {
      const index = this.columns3.findIndex((item) => item === val)
      this.defaultIndex.testdrivePlace = index
    },
    'formData.agent'(val) {
      const index = this.columns2.findIndex((item) => item === val)
      this.defaultIndex.agent = index
      if (this.formData.agent && this.formData.testdriveSeries) {
        // this.getListResource()
      }
    },
    'formData.fullName'(val) {
      // this.formData.fullName = this.filterInput(val)
      this.formData.fullName = val
    },
    'formData.remarks'(val) {
      this.formData.remarks = this.filterInput(val)
    },
    'formData.mobile'(val) {
      if (val.length > 9) this.isGainCode = val.length === 11
      this.formData.mobile = this.filterInput(val)
    },
    'formData.carLicenseCityName'(val) {
      // this.formData.name = this.filterInput(val)
    },
    'formData.monthDay'(val) {
      if (val instanceof Date) {
        this.formData.monthDay = dayjs(val).format('YYYY年MM月DD日')
      }
    }
  },
  beforeRouteLeave(to, from, next) {
    this.isLocationLostToast = false
    if (!this.isSetPageLeaveData) this.SensorsSetPageLeaveData()
    next()
  }
}
</script>

<style lang="less" scoped>
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/dialog.less');
@import url('../../assets/style/buttons.less');
@import url('../../assets/style/animation.less');
@import url('../../assets/style/nor.less');

.tip {
  position: absolute;
  bottom: 90px;
  display: flex;
  color: #333333;
  font-size: 13px;
  span {
    font-size: 13px;
    color: #999999;
    padding: 0 4px;
    text-decoration: underline;
  }
}

// /deep/ .van-checkbox__icon {
//   font-size: 14px;
// }

/deep/ .van-checkbox__icon--checked .van-icon {
  background: none;
  border-color: #c8c9cc;
  color: #000;
}

.codeBtn {
  position: absolute;
  right: 0;
  width: 80px;
  height: 26px;
  color: #b3b3b3;
  font-size: 12px;
  line-height: 25px;
  text-align: center;
  border: 1px solid #b3b3b3;
}

.moB {
  color: #5a5a5a;
  border: 1px solid #5a5a5a;
}

.tabBox {
  margin: 0 16px;
  padding: 24px 16px;
  background: #fff;
  position: absolute;
  z-index: 19;
  bottom: 104px;
  left: 0;
  right: 0;
}
.a5l-tabBox{
  bottom: 48px;
}

.succB {
  position: absolute;
  z-index: 999;
  bottom: 0;
  background: white;
  right: 0;
  left: 0;
  margin: 0 10px;
  height: 417px;
  padding-top: 58px;
}

.borB {
  border: 1px solid;
  padding: 15px 0;
  width: 50%;
}

.goB {
  background: black;
  color: white;
  padding: 15px 0;
  width: 50%;
  margin-left: 2px;
}

.de-box {
  display: inline-block;
  max-width: 69%;
  margin-left: 90px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.subBtn {
  width: 100%;
  // margin-top: 138px;
  height: 48px;
  background-color: #1a1a1a;
  border: none;
  font-family: 'Audi-Normal';
  font-size: 14px;
}

// .subBtnBo {
//   margin-top: 80px;
// }

.circle {
  position: relative;
  width: 72px;
  height: 72px;
  margin: 0 auto;
}

div,
input,
textarea,
i {
  box-sizing: border-box;
}

.box_head {
  width: 100%;

  img {
    width: 100%;
    height: 230px;
    object-fit: cover;
  }
}

#detailBox {
  height: 100%;
  position: relative;

  .header-btn {
    position: absolute;
    width: 24px;
    height: 24px;
    top: 5%;
    left: 12px;
    z-index: 10000;
  }

  .box {
    margin: 16px 15px 80px 17px;
    padding: 0;
  }

  .toolimg {
    width: 100%;
  }

  // .buyMess {
  //   /deep/.van-form {
  //     &>.box-field {
  //       display: flex;
  //       flex-direction: column;
  //       position: relative;
  //       height: 56px;

  //       .box-label {
  //         width: 25%;
  //         font-size: 16px;
  //         color: #000;
  //         position: absolute;
  //         line-height: 20px;
  //         left: 0;
  //         top: 0px;
  //         z-index: 999;
  //       }

  //       .van-cell {
  //         position: relative;
  //         height: 100%;

  //         &:active {
  //           background-color: #fff;
  //         }

  //         .van-cell__value {
  //           .van-field__body {
  //             // min-height: calc(100% - 25px);
  //             // border-bottom: 1px solid #e5e5e5;
  //             font-size: 16px;
  //             overflow: visible;
  //             flex-direction: column;
  //             // justify-content: flex-end;
  //             align-items: flex-start;
  //             position: relative;
  //             // top: 6px;

  //             &.border-none {
  //               border: none;
  //             }

  //             input {
  //               min-height: 20px !important;
  //               line-height: 20px;
  //               padding-top: 1px;
  //               width: 100%;
  //               position: relative;
  //               top: -8px;
  //             }

  //             input::-webkit-input-placeholder {
  //               font-size: 16px;
  //             }

  //             textarea {
  //               line-height: 20px;
  //               min-height: 30px;
  //               width: 100%;
  //               position: relative;
  //               // top: -6px;
  //             }

  //             textarea::-webkit-input-placeholder {
  //               font-size: 16px;
  //             }
  //           }
  //         }
  //       }

  //       ._remarks {
  //         .van-cell__value {
  //           .van-field__body {
  //             border-bottom: none !important;
  //           }
  //         }
  //       }

  //       .van-field--error {
  //         .van-field__body {
  //           border-bottom: 1px solid #9e1f32 !important;
  //         }

  //         .van-field__error-message {
  //           color: #9e1f32;
  //           position: relative;
  //           top: 6px;
  //         }
  //       }
  //     }

  //     .jt {
  //       position: relative;

  //       i {
  //         position: absolute;
  //         right: 0;
  //         top: 2px;
  //         color: #999;
  //       }

  //       input {
  //         font-size: 16px;
  //         padding-right: 20px;
  //       }
  //     }
  //   }

  //   .wanshan {
  //     display: flex;
  //     align-items: center;
  //     justify-content: space-between;
  //     position: relative;
  //     height: 56px;
  //     top: -25px;
  //     border-bottom: solid 1px #000;
  //   }
  // }
}

/deep/.van-dialog {
  overflow-y: auto;
  max-height: 80%;
  padding: 15px 16px 16px;
  top: 52% !important;
  z-index: 33336;

  h3 {
    margin: 0;
  }

  .item {
    color: #000;
    font-size: 14px;
    text-align: left;
    margin-bottom: 24px;

    .title {
      line-height: 24px;
    }

    .itemCotent {
      display: flex;
      line-height: 17px;

      div {
        margin-top: 8px;
      }
    }
  }
}

/deep/.van-popup {
  border-radius: 0;
  // height: 400px;
  font-size: 14px;

  .carCitypicker {
    position: relative;
    z-index: 2333;
    transform: translateY(80px);
    text-align: center;
    width: 100%;
    display: flex;
    background-color: #fff;
    top: -20px;

    div {
      flex: 1;
    }
  }

  .van-picker__columns {
    // top: 80px;

    .van-hairline-unset--top-bottom {
      border-bottom: 1px solid #eee;
      border-top: 1px solid #eee;
    }

    .van-picker-column {
      font-size: 14px;
    }
  }
}

/deep/ .van-swipe__indicator {
  width: 16px;
  height: 6px;
  background: rgb(145 142 142 / 50%);
  border-radius: 0px;
}

/deep/ .van-swipe__indicator--active {
  background: white;
}

/deep/ .van-tabs__line {
  background: #000000;
  height: 2px;
  width: 80px;
  bottom: 22px;
}

/deep/ .van-tab {
  font-size: 16px;
  font-weight: 400;
  color: #999999;
  line-height: 24px;
}

#detailBox /deep/ .van-tab--active {
  font-size: 16px;
  font-weight: 700;
  color: #000000;
  line-height: 24px;
}

#detailBox {
  .lan-swipe-box,
  .lan-swipe-screen {
    height: 100vh;
  }
  .lan-swipe-screen {
    /deep/ .van-swipe__indicators {
      top: auto;
      bottom: 532px;
      .van-swipe__indicator {
        opacity: 0.5;
        &.van-swipe__indicator--active {
          opacity: 1;
        }
      }
    }
    /deep/ .van-swipe-item {
      .captions {
        height: 44px;
        padding: 0 16px;
        position: absolute;
        bottom: 554px;
        font-size: 14px;
        line-height: 22px;
        color: #fff;
        font-family: 'Audi-WideBold';
      }
    }
  }
}

// /deep/ .van-field__value .van-field__body textarea {
//   padding-left: 90px;
// }

// /deep/ .van-field--error .van-field__control,
// .van-field--error .van-field__control::placeholder {
//   color: #323233 !important;
// }

.custom-header {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  z-index: 99;
  height: 40px;
  border-top: solid 20px transparent;
  box-sizing: content-box;
  padding: 0 16px;
  .back-left,
  .share-right {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
  }
}

.masking {
  position: absolute;
  z-index: 10;
  width: 100vw;
  height: 80vh;
  left: 0;
  top: 20vh;
  // background: linear-gradient(to bottom, rgba(29, 36, 46, 0), rgba(29, 36, 46, 1));
  .more {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    img {
      width: 22px;
      height: 22px;
      position: relative;
      top: -1px;
    }
  }
}

.tabBox {
  .title-box {
    padding: 0 0 4px;
    .title {
      height: 24px;
      line-height: 24px;
      font-size: 16px;
      font-weight: 600;
      color: #000;
    }
    .sub {
      // height: 20px;
      margin-top: 4px;
      line-height: 20px;
      font-size: 11px;
      color: rgba(#000, 0.6);
    }
  }

  .formBox {
    .form-field {
      margin-top: 8px;
      border-bottom: 1px solid #f2f2f2;
      &.field-hidden {
        height: 0;
        margin: 0;
        border: none;
        overflow: hidden;
      }
      .van-field {
        padding: 16px 0;
        height: 24px;
        line-height: 24px;
        font-size: 16px;
        color: #4c4c4c;
        box-sizing: content-box;
        overflow: visible;
        .van-cell__right-icon {
          color: #000;
        }
        /deep/ .van-cell__title {
          color: #4c4c4c;
        }
        /deep/ .van-field__value {
          overflow: visible;
          .van-field__body {
            position: relative;
            .van-field__control {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              color: #1a1a1a;
              &::placeholder {
                color: #999;
                font-size: 16px;
              }
            }
          }
          .van-field__error-message {
            color: #9e1f32;
            position: absolute;
            z-index: 19;
            top: 42px;
            left: -84px;
            display: flex;
            flex-direction: row;
            align-items: center;
            height: 16px;
            &::before {
              content: '';
              display: inline-block;
              width: 14px;
              height: 14px;
              background: url('../../assets/img/error.png') no-repeat 0 0px;
              background-size: 14px 14px;
              margin-right: 4px;
            }
          }
        }

        &.no-location {
          /deep/ .van-field__value {
            .van-field__body {
              padding-right: 58px;
            }
          }
        }

        &.van-field--error {
          /deep/ .van-field__value {
            .van-field__control {
              color: #1a1a1a;
              &::placeholder {
                color: #999 !important;
              }
            }
          }
        }
      }
      .text-btn {
        position: absolute;
        z-index: 29;
        top: 50%;
        right: 0;
        font-size: 14px;
        color: #666;
        padding: 17px 0 17px 17px;
        transform: translateY(-50%);
        &.location-btn {
          color: #1a1a1a;
          padding-right: 4px;
        }
        &.send-code-btn {
          &::after {
            position: absolute;
            content: '';
            left: 10px;
            top: 23px;
            height: 13px;
            border-left: solid 1.3px rgba(#666, 0.9);
          }
          &.closed {
            color: #999;
            &::after {
              border-color: rgba(#999, 0.9);
            }
          }
        }
      }
    }
  }
  .copyright {
    margin: 30px 0 18px 0;
    font-size: 12px;
    color: #999;
    .van-cell {
      padding: 0;
      /deep/ .van-field__control--custom {
        min-height: 20px;
        .van-checkbox__label {
          font-size: 12px;
          margin-left: 5px;
          color: #999;
          span {
            color: #1a1a1a;
            margin: 0 3px;
          }
        }
        .van-checkbox__icon {
          font-size: 16px;
          .van-icon {
            border-color: #999;
            transition: none;
            &::before {
              display: none;
            }
            // &::after {
            //   content: '\e728';
            //   position: absolute;
            //   left: .8px;
            //   top: -1.6px;
            //   color: #fff;

            // }
          }
          &.van-checkbox__icon--checked {
            .van-icon {
              color: #fff;
              border-color: #000;
              background: #000 url('~@/assets/icon-checked.png') no-repeat 50% /
                contain;
              // &::after {
              //   color: #000;
              // }
            }
          }
        }
      }
    }
  }
  .lan-testdrive-succeed {
    padding: 0;
    margin: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    .succeed {
      padding: 33px;
      .circle {
        width: 64px;
        height: 64px;
      }
      p {
        margin: 4px 0 0 0;
        line-height: 20px;
        font-size: 13px;
        color: rgba(0, 0, 0, 0.6);
        text-align: center;
        &.title {
          margin-top: 24px;
          font-size: 16px;
          font-weight: 600;
          color: #000;
          line-height: 24px;
        }
      }
    }
  }
}

.totop {
  position: fixed;
  z-index: 39;
  right: 0;
  top: 378px;

  overflow: hidden;
  &,
  img {
    width: 69px;
    height: 28px;
  }
}
.model-analysis {
  .image {
    margin: -1px 0 0 0;
    padding: 0;
    max-width: 100vw;
    vertical-align: top;
    overflow: hidden;
  }
}
</style>
