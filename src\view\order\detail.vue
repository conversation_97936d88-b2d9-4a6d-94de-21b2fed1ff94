<template>
  <div
    id="detail"
    class="detail-default-box"
  >
    <div
      class="goods-img"
      data-flex="main:justify"
    >
      <div
        class="carimg-wrapper"
        data-flex="main:center"
      >
        <img :src="$loadWebpImage(mainImageUrl)">
      </div>

      <div
        class="order-info"
        data-block
        data-flex="box:wrap"
      >
        <div class="order-title">
          <div class="text-two-hidd">
            {{ modelNameCn }}
          </div>
        </div>
        <div
          class="price-box"
          data-flex="box:bottom"
        >
          <p class="p">
            <!-- <span>{{ isOrderBigCate() ? '定' : '意向' }}金：</span> -->
            <span>定金：</span>
            <span class="price">{{ money | prefixFormatPrice }}</span>
          </p>
          <p
            class="p"
            v-if="orderLuckyBags"
          >
            <span>福包：</span>
            <span class="price">{{ orderLuckyPrice | prefixFormatPrice }}</span>
          </p>
        </div>
      </div>
    </div>

    <!--  代理商 -->
    <!-- <div class="dealer-wrapper">
      <div class="line">
        <div class="title">
          代理商
        </div>
        <div class="btn-change" @click="toDealerList" v-if="!isReserveCard && !cityOnlyHeader && !this.bestRecommandDealerCode && !orderId">
          更改
          <van-icon name="arrow" size="16px" />
        </div>
      </div>
      <div class="dealer-info-wrapper">
        <div class="main-img-wrapper">
          <img :src="dealerMainImg" alt="">
        </div>

        <div class="layout-bottom">
          <div class="left">
            <div class="title">
              {{ dealerName }}
            </div>

            <div class="address">
              {{ dealerAdrress }}
            </div>
          </div>

          <div class="right">
            <div class="distance" v-if="dealerDistance" @click="toNavigation">
              <img style="width: 30px" :src="require('@/assets/img/nav.png')" alt="">
              <div>{{ dealerDistance | formatDistance }}</div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
    <!-- <div class="similar">
      <div class="title">相似车型</div>
      <div class="swiper">
        <div class="left">
          A7L 3.0T 仙剑版
        </div>
        <div class="right">
          <img src="https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a7l/model/oneapp/498B2Y-MRADF15-GPAHPAH-GWCVWCV.png?x-oss-process=image/resize,h_512">
        </div>
      </div>
    </div> -->

    <!-- 相似车型 -->
    <!-- <SwipeBox
      v-if="dealerCode"
      :obj-swipe-box="{dealerCode: dealerCode}"
      ref="refSwipeBox"
    /> -->

    <!-- <div
      class="equity-img-wrapper"
      v-if="carSeries.seriesCode ==='49' && floors"
    >
      <img
        :src="floors"
        alt=""
        class="toolimg"
        @click="goRights"
      >
    </div> -->
    <!-- <div
      :class="['equity-img-wrapper', carSeries.seriesCode !== 'G4' ? 'hidden-box' : '']"
      v-if="carSeries.seriesCode === 'G4'"
    >
      <carConfigQuanyi />
    </div> -->
    <div class="order-forms-box">
      <van-form
        ref="form"
        scroll-to-error
        @failed="failed"
        @submit="onSubmit"
      >
        <van-cell-group>
          <div class="cell-title van-hairline--bottom">
            <div class="h2">
              购车信息
            </div>
          </div>
          <van-field
            :label-align="labelAlign"
            :label-width="labelWidth"
            label="购买类型"
            data-flex="cross:center"
          >
            <template
              #input
            >
              <van-radio-group
                class="lan-radio-group"
                v-model="formData.buyType"
                data-flex="main:left"
              >
                <van-radio
                  v-for="t in columns"
                  :key="t"
                  :name="t"
                >
                  {{ t }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>

          <!-- <van-field
            :label-align="labelAlign"
            :label-width="labelWidth"
            label="付款方式"
            data-flex="cross:center"
          >
            <template
              #input
            >
              <van-radio-group
                class="lan-radio-group"
                v-model="formData.paymentMethod"
                data-flex="main:left"
              >
                <van-radio
                  v-for="t in paymentMethod"
                  :key="t.type"
                  :name="t.type"
                >
                  {{ t.text }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field> -->
          <van-field
            :is-link="!$route.query.dealerCode"
            readonly
            :disabled="$route.query.dealerCode"
            :label-align="labelAlign"
            :label-width="labelWidth"
            label="代理商"
            v-model="dealerCode"
            :rules="[ { required: true, message: '请选择代理商', }, ]"
            id="dealerName"
            placeholder="请选择代理商"
            name="dealerCode"
          >
            <template #input>
              <input
                type="text"
                :disabled="$route.query.dealerCode"
                placeholder="请点击选择代理商"
                v-model="dealerName"
                @focus="handlerFocus('dealerName')"
                readonly
                @click="toDealerList"
                class="van-field__control"
              >
            </template>
          </van-field>
        </van-cell-group>
        <van-cell-group>
          <div class="cell-title van-hairline--bottom cell-top">
            <div class="h2">
              购车人信息
            </div>
          </div>
          <van-field
            type="textarea"
            cols="42"
            :rows="rows.fullName"
            autosize
            max-width
            :label-align="labelAlign"
            :label-width="labelWidth"
            :disabled="formNotEditable.fullName"
            v-model="formData.fullName"
            ref="fullName"
            id="fullName"
            @blur="handlerBlur('fullName')"
            @focus="handlerFocus('fullName')"
            @input="formFormatterName('fullName', formData.fullName)"
            :maxlength="maxlength.fullName"
            label="下单姓名"
            placeholder="请输入下单人姓名"
            :rules="[ { trigger: 'onBlur', validator: formValidatorName, message: `请输入正确的下单人姓名！`, }, ]"
          />
          <van-field
            disabled
            :label-align="labelAlign"
            :label-width="labelWidth"
            name="asyncValidator"
            v-model="formData.mobile"
            maxlength="11"
            type="number"
            id="mobile"
            label="联系电话"
            placeholder="请输入下单人联系电话"
            on-key-up="value = value.replace(/[^\d]/g, '')"
            :rules="[ { trigger: 'onBlur', validator: formValidatorMobile, message: '请输入正确的联系电话', } ]"
          />
          <van-field
            :label-align="labelAlign"
            :label-width="labelWidth"
            :disabled="formNotEditable.certificateCode"
            readonly
            clickable
            is-link
            placeholder="请点击选择证件类型"
            label="证件类型"
            v-model="formData.type"
            @click="formDataShow.showType = !formNotEditable.certificateCode"
            :rules="[{ trigger: 'change', required: true, message: '请选择择证件类型' }]"
          />
          <van-popup
            v-model="formDataShow.showType"
            :lazy-render="false"
            round
            position="bottom"
          >
            <van-picker
              title="证件类型"
              show-toolbar
              ref="type"
              :default-index="defaultIndex.type"
              :columns="columns1"
              @confirm="onConfirm2"
              @cancel="onCancel"
            />
          </van-popup>
          <van-field
            id="code"
            :label-align="labelAlign"
            :disabled="formNotEditable.certificateCode"
            :label-width="labelWidth"
            v-model="formData.code"
            ref="code"
            :maxlength="maxlength.code"
            on-key-up="value = value.replace(/[^\w]/g, '')"
            label="证件号码"
            placeholder="请输入证件号码"
            @blur="handlerBlur('code')"
            @focus="handlerFocus('code')"
            @input="formFormatterName('code', formData.code)"
            :rules="[ { trigger: 'onBlur', validator: formValidatorIDCode, type: formData.type, message: '请输入正确的证件号！', } ]"
          />
          <div class="cell-title van-hairline--bottom cell-top">
            <h2 class="h2 mtb">
              车主信息
            </h2>
            <p class="sub">
              车主信息将用以签署购车合同及开具新车购车发票，请确保车主姓名及证件信息真实有效，且手机号为车主本人手机号，否则后续将无法签署合同。
            </p>
          </div>
          <template v-if="formData.buyType === '个人'">
            <van-cell
              title="与购车人信息保持一致"
              class="lan-cell-switch"
            >
              <template #default>
                <van-switch
                  :disabled="!canXdStatus || orderLuckyBags"
                  :size="28"
                  :active-color="'#0da20d'"
                  :inactive-color="'#d9d9d9'"
                  class="lan-switch"
                  v-model="isIdentical"
                />
              </template>
            </van-cell>
            <van-field
              v-if="!isIdentical"
              type="textarea"
              id="carOwnerName"
              :disabled="formNotEditable.carOwnerName"
              cols="42"
              autosize
              :rows="rows.carOwnerName"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="formData.carOwnerName"
              ref="carOwnerName"
              @input="formFormatterName('carOwnerName', formData.carOwnerName)"
              @blur="handlerBlur('carOwnerName')"
              @focus="handlerFocus('carOwnerName')"
              label="车主姓名"
              placeholder="请输入车主姓名"
              :maxlength="maxlength.carOwnerName"
              :rules="[ { trigger: 'onBlur', validator: formValidatorName, message: '请输入正确的车主姓名!', }]"
            />
            <van-field
              v-if="!isIdentical"
              :label-align="labelAlign"
              :disabled="formNotEditable.carOwnerMobile"
              :label-width="labelWidth"
              v-model="formData.carOwnerMobile"
              ref="carOwnerMobile"
              type="number"
              id="carOwnerMobile"
              @blur="handlerBlur('carOwnerMobile')"
              @focus="handlerFocus('carOwnerMobile')"
              on-key-up="value = value.replace(/[^\d]/g, '')"
              maxlength="11"
              label="联系电话"
              placeholder="请输入车主联系电话"
              :rules="[ { trigger: 'onBlur', validator: formValidatorMobile, name: 'carOwnerMobile', message: '请输入正确的车主联系电话，且必须与购车人电话不一致！', }]"
            />
            <van-field
              v-if="!isIdentical"
              :label-align="labelAlign"
              :disabled="formNotEditable.carOwnerCertificateNumber"
              :label-width="labelWidth"
              readonly
              clickable
              is-link
              placeholder="请点击选择证件类型"
              label="证件类型"
              v-model="formData.carOwnerCertificateType"
              @click="formDataShow.showIDType = !formNotEditable.carOwnerCertificateNumber"
              :rules="[{ trigger: 'change', required: true, message: '请点击选择证件类型' }]"
            />
            <van-popup
              v-if="!isIdentical"
              v-model="formDataShow.showIDType"
              :lazy-render="false"
              position="bottom"
            >
              <van-picker
                :default-index="defaultIndex.carOwnerCertificateType"
                title="证件类型"
                show-toolbar
                ref="carOwnerCertificateType"
                :columns="columns1"
                @confirm="onConfirm1"
                @cancel="onCancel"
              />
            </van-popup>
            <van-field
              v-if="!isIdentical"
              id="carOwnerCertificateNumber"
              label="证件号码"
              placeholder="请输入车主证件号码"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="formData.carOwnerCertificateNumber"
              :maxlength="maxlength.carOwnerCertificateNumber"
              :disabled="formNotEditable.carOwnerCertificateNumber"
              on-key-up="value = value.replace(/[^\w]/g, '')"
              ref="carOwnerCertificateNumber"
              @blur="handlerBlur('carOwnerCertificateNumber')"
              @focus="handlerFocus('carOwnerCertificateNumber')"
              @input="formFormatterName('carOwnerCertificateType', formData.carOwnerCertificateType)"
              :rules="[ { trigger: 'onBlur', validator: formValidatorIDCode, type: formData.carOwnerCertificateType, message: '请输入正确的车主证件号码！', }]"
            />
          </template>
          <template v-else>
            <van-field
              type="textarea"
              cols="24"
              id="enterpriseName"
              :disabled="formNotEditable.enterpriseName"
              autosize
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="formData.enterpriseName"
              ref="enterpriseName"
              label="企业名称"
              placeholder="请输入企业名称"
              :rows="rows.enterpriseName"
              @blur="handlerBlur('enterpriseName')"
              @focus="handlerFocus('enterpriseName')"
              @input="formFormatterEnterpriseName('enterpriseName', formData.enterpriseName)"
              :rules="[ { trigger: 'onBlur', validator: enterpriseName, message: '请输入正确企业名称！'}] "
            />
            <van-field
              :label-align="labelAlign"
              :disabled="formNotEditable.enterpriseCode"
              :label-width="labelWidth"
              v-model="formData.enterpriseCode"
              ref="enterpriseCode"
              id="enterpriseCode"
              maxlength="18"
              label="企业代码"
              placeholder="请输入统一社会信用代码"
              on-key-up="value=value.replace(/[\W]/g,'')"
              @blur="handlerBlur('enterpriseCode')"
              @focus="handlerFocus('enterpriseCode')"
              @input="formFormatterName('enterpriseCode', formData.enterpriseCode)"
              :rules="[ { trigger: 'onBlur', validator: enterpriseCode, message: '请填写正确组织机构代码！'}] "
            />
          </template>
        </van-cell-group>
        <order-option
          :spread="false"
          :goods="prices"
          :option-list="optionList"
          title="价格明细"
        />
        <div
          class="financial-plans-box"
        >
          <van-cell
            is-link
            title="更多金融贷款方案"
            value="立即了解"
            @click="goToFinancialCalcPage"
          />
        </div>
        <div
          class="jl"
          v-if="orderLuckyBags"
        >
          <label class="box-label">交车方式</label>
          <van-field
            v-model="deliveryName"
            placeholder="请选择交车方式"
            is-link
            disabled
            @click="toDeliveryPattern"
            :rules="[ { required: true, message: '请选择交车方式', }, ]"
          />
        </div>

        <div
          class="box-field"
          id="mobile"
          v-if="formData.inviteUserMobile"
        >
          <label class="box-label">推荐人</label>
          <van-field
            disabled
            :label-align="labelAlign"
            :label-width="labelWidth"
            name="asyncValidator"
            v-model="formData.inviteUserMobile"
            maxlength="11"
            type="number"
            on-key-up="value = value.replace(/[^\d]/g, '')"
          />
        </div>
      </van-form>
      <div
        class="checked agreement-box"
        data-flex="cross:center"
      >
        <van-checkbox
          v-model="checked"
          shape="square"
          name="checked"
          class="lan-square-checkbox lan-square-img"
          icon-size="13"
        >
          <font>我已阅读并同意</font>
        </van-checkbox>
        <!-- <span
          v-if="isOrderBigCate()"
          @click="toShowBuyCarAgreeMent"
        >《购车协议》</span> -->
        <span
          @click="goUserAgreement"
        >《定金协议》</span>
        <span
          @click="goUserConsentRule"
        >《敏感个人信息处理规则》</span>
      </div>

      <div
        class="btnWarpper"
      >
        <div
          class="lan-button-box black-button"
        >
          <van-button
            class="lan-button"
            @click="submit"
          >
            <!-- {{ isOrderBigCate()?'去支付定金':'去支付意向金' }} -->
            支付定金
            <span>¥{{ orderPrice | formatPrice }}</span>
          </van-button>
        </div>
      </div>
    </div>
    <van-popup
      v-model="emptyBookPopShow"
      class="lottie-lucky-bags popup-custom"
      :close-on-click-overlay="false"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="text align-center">
            该福包已售罄，点击确定前往查看其他车型?
          </div>
        </div>
        <div
          class="popup-custom-btn"
          data-flex="main:justify"
        >
          <div class="line-two-cols lan-button-box">
            <audi-button
              @click="emptyBookPopShow = false"
              font-size="16px"
              height="56px"
              text="取消"
            />
          </div>
          <div class="line-two-cols lan-button-box">
            <audi-button
              @click="$router.push({ name: 'HotRecommended' })"
              color="black"
              font-size="16px"
              height="56px"
              text="确定"
            />
          </div>
        </div>
      </div>
    </van-popup>

    <network @reload="networkReload()" />
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Toast,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Switch
} from 'vant'
import wx from 'weixin-js-sdk'
import pinyin from 'js-pinyin'
import {
  getFloors2,
  getMyOrders,
  canXdUpdateInfo,
  refreshCcid,
  minipPay,
  getAudiCarUrl,
  updateNgaOrderInfo,
  addRemark,
  getNgaDetailsOrder,
  updateAmsDeliverType,
  getShoppingCartDetail,
  getInviteUserInfo,
  blessedPackConfirmCcid,
  getNgaPrice,
  checkRefreshStockCar,
  getAPITimeOutTesting,
  getStockCarConfigDetail,
  getConsentByType
} from '@/api/api'
import {
  callNative, getUrlParamObj, paramsStrict, checkType, delay, getLocationProvince, getLocationCityName
} from '@/utils'
import {
  buyType, certificateType, certificateJson, paymentMethod
} from '@/config/constant'
import { getLuckyBagModelStock, getLuckyBagIdInfo } from '@/api/lucky-bag'
import AudiButton from '@/components/audi-button.vue'
import SwipeBox from '@/view/order/components/SwipeBox.vue'
import ORDER_PAGE_DATA from '@/config/order-page.data'
import { TIMEOUT_MS } from '@/config/conf.data'
import validateIdCard from '@/utils/idcard'
import checkSocialCreditCode from '@/utils/enterprise.code'
import network from '@/components/network.vue'
import { minipLocation } from '@/utils/weixin-js-sdk'
import {
  getCity,
  getUserInfo,
  getFloors,
  getCopProdQuery,
  advance,
  carPlaceOrder,
  getOrderLimitedNumberStatus,
  uploadCcid,
  newCarconfig
} from '../../api/detail'
import api from '../../config/url'
import { limitedNumbers } from '../../api/payment-success'
import carConfigQuanyi from '../../components/car-config-quanyi.vue'
import { judgeChuangshi } from '../../api/test-driver'
import OrderOption from './option/option.vue'
 

const baseOssHost = api.BaseOssHost

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Switch)
const codeType = ['00', '200']
export default {
  name:'detail',
  inject: ['reload'],
  components: {
    carConfigQuanyi, AudiButton, SwipeBox, 'order-option': OrderOption, network
  },
  data() {
    return {
      paymentMethod,
      prices: {},
      optionList: [],
      cites: {},
      buyerInfo: {},
      affirmInfo: false,
      cityDefultIndex: 0,
      emptyBookPopShow: false,
      BaseOssHost: api.BaseOssHost,
      orderStatus: '',
      orderId: '',
      checked: false,
      formDataShow: {
        showPicker: false,
        showCarOwnerCertificateType: false,
        showCarCity: false,
        showType: false,
        showIDType: false
      },
      cascaderValue: '',
      DShow: false,
      orderDetail: null,
      columns: buyType,
      columns1: certificateType,
      dealerCityType: false, // 是否更换过城市
      formData: {
        buyType: '个人',
       // paymentMethod: '10',
        fullName: '',
        mobile: '',
        remark: '',
        type: '居民身份证',
        code: '',
        carOwnerName: '',
        carOwnerMobile: '',
        carOwnerCertificateType: '居民身份证',
        carOwnerCertificateNumber: '',
        carLicenseCityName: '',
        carLicenseCityCode: '',
        enterpriseName: '',
        enterpriseCode: '',
        institutional: false,
        inviteUserMobile: ''
      },
      defaultIndex: {
        type: '0',
        carOwnerCertificateType: '0',
        buyType: '0'
      },
      labelWidth: 72,
      labelAlign: 'left',
      // 意向金协议
      areaList: [],
      isIdentical: false,
      animations: {
        fullName: false,
        code: false,
        carOwnerName: false,
        carOwnerMobile: false,
        carOwnerCertificateNumber: false,
        enterpriseName: false,
        enterpriseCode: false,
        institutional: false,
        inviteUserMobile: true
      },
      carConfigInformation: {
        imageUrl: '',
        colorNameCn: '',
        modellineId: '',
        colorCode: '',
        // 内饰
        inColorNameCn: '',
        seriesNameCn: ''
      },
      rows: {
        fullName: '1',
        carOwnerName: '1',
        enterpriseName: '1'
      },
      maxlength: {
        carOwnerName: '32',
        fullNmae: '32',
        code: 18,
        carOwnerCertificateNumber: 18
      },
      // 意向(定)金
      money: 0,
      floors: '',
      userAgreement: false,
      blur: false,
      skuSnapshotId: '',

      orgCode: '',
      orgName: '',
      orgShow: false,
      dealerCode2: '',
      locationCity: null,
      canXdStatus: true,
      inviteCode: '',
      isModelStock: 0, // 福包关联车型库存
      orderLuckyBags: false, // 福包车
      orderLuckyPrice: 0, // 福包价格
      orderPrice: 0, // 订单价格,
      unalterable: {
        dealerCity: false,
        dealerName: false
      },
      ORDER_PAGE_DATA,
      formNotEditable: {
        fullName: false,
        certificateCode: false,
        carOwnerName: false,
        carOwnerMobile: false,
        carOwnerCertificateNumber: false,
        enterpriseName: false,
        enterpriseCode: false
      },
      ngaParams: {}
    }
  },
  computed: mapState({
    dealerMainImg: (state) => {
      const imgurl = state.dealerInfo?.imageUrl
      if (!imgurl) return ''

      if (imgurl.includes('http')) {
        return imgurl
      }
      return `${baseOssHost}${imgurl}`
    },
    dealerDistance: (state) => state.dealerInfo?.distance,
    dealerName: (state) => state.dealerInfo.dealerName,
    dealerInfo: (state) => state.dealerInfo,
    dealerCode: (state) => state.dealerInfo.dealerCode,
    latitude: (state) => state.dealerInfo.latitude,
    carSeries: (state) => state.carSeries,
    deliveryPattern: (state) => state.deliveryPattern,
    deliveryName: (state) => state.deliveryPattern?.deliveryName,
    readed: (state) => state.readed, //  1 代表已经阅读过合同
    dadingFormData: (state) => state.dadingFormData,
    longitude: (state) => state.dealerInfo.longitude,
    dealerAdrress: (state) => state.dealerInfo.dealerAdrress,
    mainImageUrl: (state) => baseOssHost + state.carDetail.configDetail?.carModel?.imageUrl, // 主图
    colorNameCn: (state) => state.carDetail.configDetail?.outsideColor?.colorNameCn || '',
    inColorNameCn: (state) => state.carDetail.configDetail?.insideColor?.colorNameCn,
    modelNameCn: (state) => state.carDetail.configDetail?.carModel?.modelNameCn,
    modellineId: (state) => state.carDetail.configDetail?.carModel?.modellineId,
    modelLineCode: (state) => state.carDetail.configDetail?.carModel?.modelLineCode,
    bestRecommendId: (state) => state.carDetail.bestRecommendId, // 是否是推荐车型
    bestRecommandDealerCode: 'bestRecommandDealerCode',
    isReserveCard: 'isReserveCard',
    reserveCardInfo: 'reserveCardInfo',
    cityOnlyHeader: 'cityOnlyHeader',
    userLocation: 'userLocation'
  }),
  watch: {
    reserveCardInfo(card) {
      const { orgCode, orgName } = card
      this.dealerName = orgName
      this.unalterable.dealerName = true
      this.getHeadquartersDealerInfo(orgCode)
    },
    money(v) {
      const { orderLuckyBags, orderLuckyPrice } = this

      console.log('%c [ money ]-992', 'font-size:14px; background:#cf222e; color:#fff;', orderLuckyBags ? v + orderLuckyPrice : v)
      this.orderPrice = orderLuckyBags ? v + orderLuckyPrice : v
    },
    orderLuckyPrice(v) {
      const { orderLuckyBags, money } = this
      this.orderPrice = orderLuckyBags ? v + money : v
    },
    isIdentical(val) {
      if (!this.orderId) {
        this.blur = true
        if (val) {
          this.updateForm()
        } else {
          this.formData.carOwnerName = ''
          this.formData.carOwnerMobile = ''
          this.formData.carOwnerCertificateNumber = ''
          if (+sessionStorage.getItem('isFormDetail') === 1) {
            const newFormData = JSON.parse(localStorage.getItem('formDetail'))
            this.formData.carOwnerCertificateType = newFormData.carOwnerCertificateType
          } else {
            this.formData.carOwnerCertificateType = '居民身份证'
          }
        }
        setTimeout(() => {
          [
            'carOwnerName',
            'carOwnerMobile',
            'carOwnerCertificateNumber',
            'fullName',
            'code',
            'enterpriseName',
            'enterpriseCode'
          ].forEach((item) => {
            if (this.formData[item] !== '') {
              // this.animation(item);
              this.animations[item] = true
            } else {
              this.animations[item] = false
            }
          })
          setTimeout(() => {
            if (this.blur) {
              this.blur = false
            }
          }, 0)
        }, 0)
      }
    },
    readed: {
      immediate: true,
      handler(newValue, oldValue) {
        const { orderId } = this.$route.query
        console.log(newValue)
        if (newValue && orderId) {
          this.getFormData()
        }
      }
    },
    DShow: {
      immediate: true,
      handler(newValue, oldValue) {
        console.log('11111111111')
        console.log(newValue)
      }
    },
    // affirmInfo(status) {
    //   console.log('%c [ affirmInfo ]-1202', 'font-size:14px; background:#cf222e; color:#fff;', status)
    //   this.$store.commit('setTitle', status ? '确认信息' : '完善购买信息')
    //   document.getElementById('detail').scrollIntoView({
    //     behavior: 'instant',
    //     block: 'start'
    //   })
    // },
    'formData.buyType'(val) {
      const index = this.columns.findIndex((item) => item == val)
      this.defaultIndex.buyType = index
    },
    'formData.type'(val) {
      const name = this.columns1.find((item) => item.text == val)
      this.maxlength.code = name.maxlength
      const index = this.columns1.findIndex((item) => item.text == val)
      this.defaultIndex.type = index
      // this.formData.code = '''
      this.handlerBlur('code')
    },
    'formData.carOwnerCertificateType'(val) {
      const name = this.columns1.find((item) => item.text == val)
      this.maxlength.carOwnerCertificateNumber = name?.maxlength || 0
      const index = this.columns1.findIndex((item) => item.text == val)
      this.defaultIndex.carOwnerCertificateType = index
      // this.formData.carOwnerCertificateNumber = ''
      this.handlerBlur('carOwnerCertificateNumber')
    },
    // 'formData.fullName'(val) {
    //   this.formData.fullName = this.filterInput(val)
    // },
    'formData.carLicenseCityName'(newValue, oldValue) {
      const { dealerCode } = this || this.$route.query
      console.log('%c [ carLicenseCityName ]-1086', 'font-size:14px; background:#cf222e; color:#fff;', 'newValue', newValue, 'oldValue', oldValue, dealerCode)
      if (dealerCode && newValue) {
      // 代理商门店里的车 默认不允许选择城市/代理商信息
        this.unalterable.dealerCity = true
      }
      if (newValue && oldValue && oldValue !== newValue) { // 只有新的信息，并且和老的不一样才能算更新
        this.dealerCityType = true
      }
      // this.formData.name = this.filterInput(newValue)
    },
    dealerCode(newValue, oldValue) { // 监听更换代理商 是否是因为更换城市导致的
      if (newValue && oldValue && this.dealerCityType === true) {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '根据购车城市已为您更改对应奥迪代理商',
          forbidClick: true,
          duration: 800
        })
        this.dealerCityType = false
      }
      console.log('代理商信息', newValue)
      // if(this.orderId && this.orderDetail.carCustomInfo.carLicenseCityCode){
      //   this.getHeadquartersDealerInfo(orderDetail.dealerInfo.dealerCode)
      // }
    },
    'formData.carLicenseCityCode'(val) {
      this.formData.name = this.filterInput(val)
    },
    'formData.code'(val) {
      if (this.formData.type === '居民身份证') {
        this.formData.code = this.formData.code.toLocaleUpperCase()
      }
    },
    'formData.carOwnerCertificateNumber'(val) {
      if (this.formData.carOwnerCertificateType === '居民身份证') {
        this.formData.carOwnerCertificateNumber = this.formData.carOwnerCertificateNumber.toLocaleUpperCase()
      }
    }
  },
  created() {
    console.log(this.$route)
    this.$bus.$off('getParams') // 为了避免事件被多次触发，先手动销毁该事件。
    this.$bus.$on('getParams', (bool) => {
      const params = {
        intention_money: this.money,
        types_cars: this.formData.buyType === '个人' ? '01' : '02',
        types_name: this.formData.fullName,
        types_telephone: this.formData.mobile,
        types_document: this.formData.type,
        document_id: this.formData.code,
        owner_name: this.formData.carOwnerName,
        owner_number: this.formData.carOwnerMobile,
        owner_types: this.formData.carOwnerCertificateType,
        owner_id: this.formData.carOwnerCertificateNumber,
        enterprise_name: this.formData.enterpriseName,
        enterprise_id: this.formData.enterpriseCode,
        buy_city: this.formData.carLicenseCityName,
        same_information: this.isIdentical
      }
      if (bool) {
        this.$sensors.track('continueOrder', params)
      } else {
        this.$sensors.track('insisQuit', params)
      }
    })
  },
  mounted() {
    document.querySelector('#common-view-wrapper').scrollTop = 0
    this.initData()
  },
  methods: {
    ...mapGetters(['getDevice']),
    isOrderBigCate() { //! 是否为大定
      const { isBigOrder } = this.$route.query
      return this.orderLuckyBags || this.orderId || isBigOrder
    },
    // 福包（内容）信息
    async getLuckyBagIdInfoData(luckyPackageId) {
      // const { data } = await getLuckyBagIdInfo({ blessedBagId: luckyPackageId })
      const { ccid } = this.$route.query
      const { data } = await blessedPackConfirmCcid({ ccid })
      console.log(this.ccid)
      if (data?.data?.blessedBagPrice) {
        this.orderLuckyPrice = data.data.blessedBagPrice
      }
    },
    // 福包关联车型 库存
    async handleGetLuckyBagModelStock(luckyPackageId) {
      const { data } = await getLuckyBagModelStock({ blessedPackId: luckyPackageId })
      this.isModelStock = data.data
    },
    async getShoppingInfo(shoppingCartId) {
      const { data } = await getShoppingCartDetail({ shoppingCartId })
      const invitationCode = data.data?.invitationCode
      if (invitationCode) {
        const inviteData = await getInviteUserInfo({ inviteCode: invitationCode })
        this.inviteCode = invitationCode
        this.formData.inviteUserMobile = inviteData.data.data.mobile
        console.log(this.formData)
      }
    },
    toNavigation() {
      callNative('navigationMap', {
        lat: this.latitude.toString(),
        long: this.longitude.toString(),
        des: this.dealerName
      })
    },
    getFormData() { // 阅读大定购车协议后 赋值之前修改过的内容
      if (this.orderDetail && this.dadingFormData) {
        this.checked = Boolean(this.readed)
        this.formData = this.dadingFormData
        this.isIdentical = this.dadingFormData.isIdentical
        if (this.readed) this.submit()
      }
    },
    // 初始化页面
    initDealerData() {
      // 获取经销商数据
      this.$store.dispatch('getDealerInfo')
    },
    toDealerList() {
      console.log('%c [ toDealerList => orderId, isReserveCard, dealerName]-1304', 'font-size:14px; background:#cf222e; color:#fff;', orderId, isReserveCard, dealerName)
      const {
        orderId, isReserveCard, unalterable: { dealerName }, $route: { query: { citesName } }
      } = this
      if (orderId || isReserveCard || dealerName) {
        if (dealerName) {
          // Toast({
          //   className: 'toast-dark-mini toast-pos-middle',
          //   message: '如需变更代理商请通过APP重新选择配置进入',
          //   forbidClick: true
          // })
          return
        }
        return
      }
      const { carLicenseCityCode, carLicenseCityName } = this.formData || ''

      this.$sensors.track('changeAgent', {
        page_name: '确认订单页面'
      })

      sessionStorage.setItem('isFormDetail', '1')
      localStorage.setItem('formDetail', JSON.stringify(this.formData))
      const bool = this.isIdentical ? '1' : '0'
      localStorage.setItem('isIdentical', bool)
      this.dealerCityType = false
      const city = carLicenseCityName.split('/')[1] || this.cites?.city || ''
      console.log('%c [ this.cites ]-1302', 'font-size:14px; background:#cf222e; color:#fff;', this.cites, city)
      this.$router.push({
        name: 'dealer-list',
        query: { city: citesName || city, isluckybag: this.orderLuckyBags }
      })
    },
    // 跳转到代理商详情
    async toDealerDetail() {
      const { data } = await getAudiCarUrl()
      const url = `${data.data.configValue}dealerDetail?dealerCode=${this.orgCode}`
      callNative('audiOpen', { path: url })
    },

    // 证件号校验
    validator(val) {
      const rules = this.columns1.find(
        (item) => item.text == this.formData.type
      ).rules
      const bool = rules.test(val)
      return bool
    },
    validator2(val) {
      const rules = this.columns1.find(
        (item) => item.text == this.formData.carOwnerCertificateType
      ).rules
      const bool = rules.test(val)
      return bool
    },
    inputValue(val) {
      this.formData[val] = this.limitstr(this.formData[val], 32)
    },
    limitstr(strval, strnum) {
      let re = ''
      const strleng = strval.length
      // 返回字符串的总字节数
      // eslint-disable-next-line no-control-regex
      const byteleng = strval.replace(/[^\x00-\xff]/g, '**').length
      if (byteleng <= strnum) return strval
      for (let i = 0, bytenum = 0; i < strleng; i++) {
        const byte = strval.charAt(i)
        // eslint-disable-next-line no-control-regex
        if (/[\x00-\xff]/.test(byte)) {
          bytenum++ // 单字节字符累加1
        } else {
          bytenum += 2 // 非单字节字符累加2
        }
        if (bytenum <= strnum) {
          re += byte
        } else {
          return re
        }
      }
    },
    // 查看车配信息
    async lookCar() {
      sessionStorage.setItem('isFormDetail', '1')
      localStorage.setItem('formDetail', JSON.stringify(this.formData))
      console.log('setFormData', JSON.stringify(this.formData))
      const bool = this.isIdentical ? '1' : '0'
      localStorage.setItem('isIdentical', bool)
      console.log('查看车配信息', {
        carModelName: this.modelNameCn,
        modellineId: this.modellineId
      })
      console.log('this.carSeries', this.carSeries)
      this.$router.push({
        path: '/car-config-table',
        query: {
          carModelName: this.modelNameCn,
          modellineId: this.modellineId,
          seriesCode: this.carSeries?.seriesCode
        }
      })
    },
    async goUserConsentRule(){
      
      const {Did} = await callNative('getUserInfo', {})
      
      getConsentByType(null, {
        Did: Did || '123456789'
      }).then(rsp => {
          console.log('敏感个人信息', rsp);
          let url = rsp.data?.data?.content;
          if(url){
            callNative('audiOpen', { path: url, showHeader: true })
          }
          
        })
    },
    goUserAgreement() {
      sessionStorage.setItem('isFormDetail', '1')
      this.$router.push({
        path: '/order/deposit-agreement',
        query: {
          // orderId: this.$route.query.orderId || '',
          // isIdentical: this.isIdentical,
          // buyType: this.formData.buyType === '个人' ? '1' : '0',
          // skuid: this.$route.query.skuid,
          // carCustomId: this.$route.query.carCustomId,
          // ccid: this.$route.query.ccid,
          agree: 1,
          ...this.ngaParams
        }
      })
      localStorage.setItem('formDetail', JSON.stringify(this.formData))
      console.log('setFormData', JSON.stringify(this.formData))
      const bool = this.isIdentical ? '1' : '0'
      localStorage.setItem('isIdentical', bool)
    },
    toShowBuyCarAgreeMent() {
      this.$store.commit('setDadingFormData', { ...this.formData, isIdentical: this.isIdentical })
      this.$router.push({
        path: '/buycar-agreement',
        query: {
          buyType: this.formData.buyType === '个人' ? '01' : '02'
        }
      })
    },
    goRights() {
      sessionStorage.setItem('isFormDetail', '1')
      localStorage.setItem('formDetail', JSON.stringify(this.formData))

      console.log('setFormData', JSON.stringify(this.formData))
      const bool = this.isIdentical ? '1' : '0'
      localStorage.setItem('isIdentical', bool)
      const { skuid } = this.$route.query

      this.$router.push({
        path: '/equity-detail',
        query: { skuid, orderId: this.orderId }
      })
    },
    syncOwnerInfo() {
      if (this.isIdentical) {
        this.formData.carOwnerName = this.formData.fullName
        this.formData.carOwnerCertificateNumber = this.formData.code
        this.formData.carOwnerCertificateType = this.formData.type
      }
    },
    // 大定信息修改
    async onModify() {
      console.log(this.formData)
      this.syncOwnerInfo()
      const {
        type,
        code,
        buyType,
        fullName,
        carOwnerCertificateNumber,
        carOwnerCertificateType,
        carOwnerMobile,
        carOwnerName,
        enterpriseName,
        enterpriseCode
      } = this.formData

      const str = this.columns1.find((item) => item.text === type).value
      const str2 = this.columns1.find((item) => item.text === carOwnerCertificateType).value

      const param = {
        moreContact: `${str},${this.formData.code}`,
        buyType: buyType === '个人' ? '01' : '02',
        carBuyerName: fullName,
        carOwnerCertificateNumber: this.formData.carOwnerCertificateNumber,
        carOwnerCertificateType: str2,
        carOwnerMobile,
        carOwnerName,
        enterpriseName,
        enterpriseCode,
        orderId: this.orderId
      }
      const { data } = await updateNgaOrderInfo(param)

      if (data.code === '00') {
        this.goPayMoney('dading')
      }
    },
    // 大定和小订的逻辑是一样的
    async goPayMoney(action) {
      // this.DShow = false
      const { env } = getUrlParamObj()
      const that = this
      const { orderId } = paramsStrict(this.$route.query)
      this.$store.commit('showLoading')
      if (env === 'minip') {
        const { origin, pathname } = window.location
        const url = encodeURIComponent(`${origin}${pathname}#/order/money-detail?orderId=${orderId}`)
        wx.miniProgram
          .navigateTo({ url: `/pages/pay/pay?orderId=${orderId}&url=${url}&colorNameCn=${that.colorNameCn}&inColorNameCn=${that.inColorNameCn}&action=${action}&seriesCode=${that.carSeries.seriesCode}` })
      } else {
        const callPayCenter = await callNative('callPayCenter', { orderId },{orderInfo: paramsStrict(this.$route.query) && JSON.stringify(paramsStrict(this.$route.query))})
        console.log('pay callPayCenter', callPayCenter)

        const data = await callNative('callPayResult', {})
        const payType = data.payType
        console.log(`current action: ${action}, callPayResultData: ${data} `)

        if (payType === 'CNY') {
          // 使用数字人民币支付
          this.$router.push({
            path: '/order/pay-flow',
            query: { orderId }
          })
        } else {
          loopQuery(0)
        }
        // 查询支付结果
        function loopQuery(i) {
          setTimeout(async () => {
            /**
               * 查询支付后的状态.
               * 小订是通过查询限量号的状态
               * 大定查询orderStatus
               */
            // 大定支付后的逻辑
            if (action === 'dading') {
              // 大定的状态获取
              getMyOrders({ orderId }).then(async (res) => {
                if (res.data.data?.orderStatus === '31') {
                  that.$store.commit('updateReaded', 0)
                  that.$store.commit('setDadingFormData', null)
                  const params = { orderId, remark: that.formData.remark }
                  const info = await addRemark(params)
                  // 跳转到支付成功的页面
                  that.$router.push({
                    path: '/dading-success',
                    query: { orderId }
                  })
                } else {
                  // 失败到详情
                  if (i >= 4) {
                    that.reload()
                    that.$store.commit('hideLoading')
                  } else {
                    loopQuery(++i)
                  }
                }
              })
            }
          }, 1000)
        }
      }
    },
    // 表单提交
    async submit() {
      const { orderLuckyBags, isModelStock } = this || 0
      // 检测福包关联车型库存
      if (orderLuckyBags && isModelStock !== 1) {
        this.emptyBookPopShow = true
        return
      }
      // 判断是否创建了创世卡 并且创世卡的代理商和当前选择代理商不一致的情况下 进行拦截并提示
      console.log(this.dealerCode, this.orgCode)
      if (this.orgCode && this.dealerCode !== this.orgCode) {
        this.orgShow = true
        return
      }

      const timer = new Date().getTime()
      if (this.isIdentical) {
        this.updateForm()
      }
      if (this.checked) {
        this.$refs.form.submit()
      } else {
        Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: '请阅读并勾选定金协议',
          forbidClick: true,
          duration: 800
        })
      }

      // 埋点
      this.dataCollection(timer)
    },
    dataCollection(timer) {
      const params = {
        intention_money: this.money,
        types_cars: this.formData.buyType,
        types_name: this.formData.fullName,
        types_telephone: this.formData.mobile,
        types_document: this.formData.type,
        document_id: this.formData.code,
        owner_name: this.formData.carOwnerName,
        owner_number: this.formData.carOwnerMobile,
        owner_types: this.formData.carOwnerCertificateType,
        owner_id: this.formData.carOwnerCertificateNumber,
        enterprise_name: this.formData.enterpriseName,
        enterprise_id: this.formData.enterpriseCode,
        buy_city: this.formData.carLicenseCityName,
        same_information: this.isIdentical,
        $event_duration: timer
      }
      this.$sensors.track('intentionPayment', params)
    },
    // 购车人表单信息冻结/解冻
    handlerFreezeOwnerForms(action = '') {
      const { formNotEditable, unalterable: { formNotEditable: formRelease } } = this
      const formReleaseEditable = formRelease
      if (action === 'freeze') {
        this.unalterable.formNotEditable = { ...formNotEditable }
      } else {
        if (formRelease) {
          delete this.unalterable.formNotEditable
        }
      }
      for (const key in formNotEditable) {
        if (Object.hasOwnProperty.call(formNotEditable, key)) {
          this.formNotEditable[key] = action === 'freeze' ? true : formReleaseEditable[key]
        }
      }
    },
    onSubmit() {
      if (this.orderId && !this.readed) {
        this.toShowBuyCarAgreeMent()
      } else {
        // 此处修复在popup后未冻结表单，导致可修改购车人信息
        this.handlerFreezeOwnerForms('freeze')
        this.iAffirm()
      }
    },
    // 我已确认
    iAffirm() {
      this.advance()
      // 埋点
      this.haveConfirmed(true)
    },
    // 返回修改
    modification() {
      this.handlerFreezeOwnerForms()
      // 埋点
      this.haveConfirmed(false)
    },
    haveConfirmed(bool) {
      const params = {
        types_name: this.formData.fullName || '',
        types_telephone: this.formData.mobile || 0,
        document_id: this.formData.code || '',
        owner_name: this.formData.carOwnerName || '',
        owner_number: this.formData.carOwnerMobile || '',
        owner_id: this.formData.carOwnerCertificateNumber || '',
        cars_appearance: this.colorNameCn || '',
        cars_interior: this.inColorNameCn || ''
      }
      if (bool) {
        params.page_name = '确认订单'
        this.$sensors.track('haveConfirmed', params)
      } else {
        this.$sensors.track('returnModify', params)
      }
    },

    failed(err) {
      console.log(err)
    },
    cityConfirm(value, index) {
      this.formDataShow.showCarCity = false
      this.formData.carLicenseCityName = `${value[0]}/${value[1]}`
      this.formData.carLicenseCityCode = this.areaList[index[0]].children[
        index[1]
      ].code

      // 更新经销商
      const [p, c] = index
      // console.log(this.areaList[p].children[c].text) //城市
      // console.log(this.areaList[p].children[c].code)
      const cityCode = this.areaList[p].children[c].code

      // 非创世卡用户才可以更新经销商
      if (!this.isReserveCard && !this.bestRecommandDealerCode) {
        this.updateDealer(cityCode)
      }
    },
    // 根据经销商code获取详情
    getHeadquartersDealerInfo(dealerCode) {
      this.$store.dispatch('getDealerOfCode', { dealerCode })
    },
    // 根据城市code获取经销商详情
    updateDealer(code) {
      const params = { cityCode: code, ifRegionCodeByCity: 1, defaultHeadquarters: 0 }
      if (this.userLocation && this.userLocation.split(',')?.length) {
        const location = this.userLocation.split(',')
        params.latitude = location[0]
        params.longitude = location[1]
      }
      console.log('更新地址代理商参数 ', params)
      if (!this.isReserveCard) {
        this.$store.commit('updateDealerInfo', {})
        // this.$store.dispatch('updateDealerInfo', params)
      }
    },

    onConfirm(value) {
      if (this.formData.buyType !== value) {
        this.formData.buyType = value
        this.formDataShow.showPicker = false
        this.$refs.form.resetValidation();
        [
          'fullName',
          'code',
          'carOwnerName',
          'carOwnerMobile',
          'carOwnerCertificateNumber',
          'enterpriseName',
          'enterpriseCode',
          'institutional'
        ].forEach((item) => {
          this.formData[item] = ''
          if (this.animations[item]) {
            this.animations[item] = false
          }
        })
      } else {
        this.formDataShow.showPicker = false
      }
    },
    onConfirm1(value, index) {
      this.formData.carOwnerCertificateType = value.text
      this.formDataShow.showIDType = false
    },
    onConfirm2(value, index) {
      this.formData.type = value.text
      this.formDataShow.showType = false
    },
    onCancel() {
      this.formDataShow.showPicker = false
      this.formDataShow.showType = false
      this.formDataShow.showIDType = false
    },

    // lebel平移
    animation(ref) {
      this.animations[ref] = true
      this.$refs[ref].focus()
      if (this.blur) {
        this.$refs[ref].blur()
      }
    },
    handlerBlur(prop) {
      if (!this.formData[prop]) {
        this.animations[prop] = false
      } else {
        this.animations[prop] = true
      }
    },
    handlerFocus(prop) {
      setTimeout(() => {
        const pannel = document?.getElementById(prop)
        if (pannel) {
          pannel.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }
        // 让当前的元素滚动到浏览器窗口的可视区域内

        // 此方法是标准的scrollIntoView()方法的专有变体
        // pannel.scrollIntoViewIfNeeded();
      }, 300)

      if (!this.animations[prop]) {
        this.animations[prop] = true
      }
    },
    sortBankList(propertyName) {
      return function sortList(object1, object2) {
        const value1 = object1[propertyName]
        const value2 = object2[propertyName]
        if (value2 < value1) return 1
        if (value2 > value1) return -1
        return 0
      }
    },
    // async getData() {
    //   // const { data } = await getCity({ code: '' })
    //   // if (codeType.includes(data.code)) {
    //   //   const province = data.data[0]?.children
    //   //   const arr = province.map(async (item, index) => {
    //   //     const data2 = await getCity({ code: item.code })
    //   //     return data2.data.data[0].children
    //   //   })
    //   //   // Promise.all(arr).then((res) => {
    //   //   //   let list = province.map((item, index) => {
    //   //   //     res[index].forEach((it, i) => {
    //   //   //       delete res[index][i].children
    //   //   //     })
    //   //   //     // 文字转拼音
    //   //   //     res[index] = res[index].map((i) => {
    //   //   //       i.textPinyin = pinyin.getCamelChars(i.text)
    //   //   //       return i
    //   //   //     })
    //   //   //     // 按照拼音首字母排序
    //   //   //     res[index] = res[index].sort(this.sortBankList('textPinyin'))
    //   //   //     province[index].children = res[index]
    //   //   //     return province[index]
    //   //   //   })
    //   //   //   // 文字转拼音
    //   //   //   list = list.map((i) => {
    //   //   //     i.textPinyin = i.text === '重庆市' ? 'Chongqingshi' : pinyin.getCamelChars(i.text)
    //   //   //     return i
    //   //   //   })
    //   //   //   this.areaList = list.sort(this.sortBankList('textPinyin'))
    //   //   //   // console.log('areaList', this.areaList)
    //   //   //   // if (sessionStorage.getItem('isFormDetail') === '1') {
    //   //   //   //   const newFormData = JSON.parse(localStorage.getItem('formDetail'))
    //   //   //   //   const str = newFormData?.carLicenseCityName ? newFormData.carLicenseCityName : ''
    //   //   //   //   const index = str.indexOf('/')
    //   //   //   //   if (index !== -1) {
    //   //   //   //     this.getCity(str.slice(index + 1))
    //   //   //   //   } else {
    //   //   //   //     this.getCity(str)
    //   //   //   //   }
    //   //   //   // } else {
    //   //   //   //   this.getCity()
    //   //   //   // }
    //   //   // })
    //   // }


    // },
    async handleGetLocationCity() {
      const device = this.getDevice() || {}
      const { env } = this.$route.query
      this.device = device
      let code = ''
      let provinceName = ''
      let city = ''
      let location = ''
      console.log('%c [ device ]-1753', 'font-size:14px; background:#cf222e; color:#fff;', device)
      if (device?.nativeApp || env === 'minip') {
        if (device?.nativeApp) {
          const nativeLocation = await callNative('getLocationCity', {}) || ''
          nativeLocation?.city && (city = nativeLocation.city)
          nativeLocation?.location && (location = nativeLocation.location)
        } else {
          // 小程序定位
          const minipLocationCity = await minipLocation()
          if (minipLocationCity?.latitude && minipLocationCity?.longitude) {
            location = `${minipLocationCity.latitude},${minipLocationCity.longitude}`
            city = await getLocationCityName([minipLocationCity.longitude, minipLocationCity.latitude])
          }
        }

        if (location) {
          const { regeocode } = await getLocationProvince(location.split(',').reverse())
          if (regeocode?.addressComponent) {
            const { addressComponent: { adcode, province } } = regeocode
            if (province) {
              provinceName = province
            }
            code = `${adcode.slice(0, adcode.length - 2)}00`
            console.log('%c [ regeocode ]-1854', 'font-size:14px; background:#cf222e; color:#fff;', code, province)
          }
        }

        // const {
        //   dealerInfo: {
        //     mineLocationCitesCode, mineLocationCitesName
        //   }
        // } = this

        if (city && location) {
          this.cites = {
            city, location, code, province: provinceName
          }
          if (provinceName && code) {
            this.formData.carLicenseCityCode = code
            this.formData.carLicenseCityName = `${provinceName}/${city}`
          }
        }
        console.log('%c [ getLocationCity ]-441', 'font-size:14px; background:#cf222e; color:#fff;', this.cites, this.formData)
      }
    },
    async getUserInfo() {
      const { data } = await getUserInfo()
      if (codeType.includes(data.code)) {
        this.formData.mobile = data.data.userInfo.mobile
        localStorage.setItem('userInfo', JSON.stringify(data.data.userInfo))
      }
      this.getStockCarConfigDetail(this.$route.query.ccid)
    },
    async getFloors() {
      const param = {
        carModelId: this.modelLineCode,
        type: 3
      }
      console.log('getFloors2', this.modelLineCode)
      const { data } = await getFloors2(param)
      if (codeType.includes(data.code) && data.data) {
        this.floors = data.data.rights
      }
    },
    // 计算金额
    // toPayPrice() {
    //   if (this.orderDetail.payList[0].payPhase === '11') { //虎头车等特定车
    //     return this.allPayPrice
    //   }
    //   if (this.orderStatus === '30' || this.orderStatus === '301') {
    //     return this.allPayPrice
    //   }
    //   return this.allPayPrice
    // },
    getEarnestMoney(list) {
      // if (this.orderDetail.payList[0].payPhase === '11') { // 虎头车等特定车
      //   return this.allPayPrice
      // }
      if (this.orderStatus === '301') {
        // if (this.bestRecommendId) { // 直接大定的
        //   return this.allPayPrice
        // }
        // 已支付意向金的
        return (this.allPayPrice - list[0].reserveOrderAmount).toFixed(2)
      }
      if (this.orderStatus === '30') {
        return (this.allPayPrice - list[0].reserveOrderAmount).toFixed(2)
      }
    },

    // 小订后是否可以更新订单的信息
    async canXdUpdateInfo() {
      const { orderId } = this.$route.query
      const { data: { data: canXdStatus } } = await canXdUpdateInfo({ orderId })
      this.canXdStatus = canXdStatus
      const { ORDER_PAGE_DATA: { BOOK_ORDER_UPDATE_BUYER_INFO } } = this
      const index = checkType(canXdStatus) !== 'Number' ? 0 : canXdStatus
      const fields = ''
      if (index && BOOK_ORDER_UPDATE_BUYER_INFO?.length) {
        const BUYER_INFO = BOOK_ORDER_UPDATE_BUYER_INFO[index] ?? ''
        if (BUYER_INFO && ['Array', 'Boolean'].includes(checkType(BUYER_INFO))) {
          const { formNotEditable } = this
          for (const key in formNotEditable) {
            if (Object.hasOwnProperty.call(formNotEditable, key)) {
              this.formNotEditable[key] = fields === 'all' ? false : !BUYER_INFO.includes(key)
            }
          }
        }
      }
    },

    async getMyOrders() {
      const { orderStatus, orderId } = this.$route.query
      this.orderStatus = orderStatus
      this.orderId = orderId
      const { data } = await getMyOrders({ orderId })
      const res = await getNgaDetailsOrder({ orderId })
      this.formData.remark = res.data?.data?.buyerRemark
      if (codeType.includes(data.code)) {
        this.orderDetail = data.data
        const orderDetail = this.orderDetail
        this.allPayPrice = orderDetail.orderItemList[0].confirmOrderAmount
        this.getHeadquartersDealerInfo(orderDetail.dealerInfo.dealerCode)
        this.formData.buyType = orderDetail.carBuyerInfo.buyType === '01' ? '个人' : '企业'
        this.formData.fullName = orderDetail.carBuyerInfo.fullName
        this.formData.mobile = orderDetail.carBuyerInfo.mobile

        this.formData.type = certificateJson[orderDetail.carBuyerInfo.moreContact.split(',')[0]]
        this.formData.code = orderDetail.carBuyerInfo.moreContact.split(',')[1]
        this.isIdentical = orderDetail.carBuyerInfo.mobile === orderDetail.carBuyerInfo.carOwnerMobile && orderDetail.carBuyerInfo.fullName === orderDetail.carBuyerInfo.carOwnerName

        this.formData.carOwnerName = orderDetail.carBuyerInfo.carOwnerName
        this.formData.carOwnerMobile = orderDetail.carBuyerInfo.carOwnerMobile
        this.formData.showIDType = certificateJson[orderDetail.carBuyerInfo.carOwnerCertificateType] ?? null
        this.formData.carOwnerCertificateNumber = orderDetail.carBuyerInfo.carOwnerCertificateNumber
        this.formData.carOwnerCertificateType = this.formData.type
        this.formData.enterpriseName = orderDetail.carBuyerInfo.enterpriseName
        this.formData.enterpriseCode = orderDetail.carBuyerInfo.enterpriseCode

        this.formData.carLicenseCityName = orderDetail.carCustomInfo.carLicenseCityName
        this.formData.carLicenseCityCode = orderDetail.carCustomInfo.carLicenseCityCode

        this.animations.fullName = true
        this.animations.code = true
        this.animations.carOwnerName = true
        this.animations.carOwnerMobile = true
        this.animations.carOwnerCertificateNumber = true
        this.animations.enterpriseName = true
        this.animations.enterpriseCode = true
        this.getFormData()

        // const { luckyPackageId } = this.$route.query
        // if (!luckyPackageId) {
        //   this.money = this.getEarnestMoney(orderDetail.orderItemList) / 100 // 单位：分 => 元
        // }
        if (this.formData.buyType === '个人' && !this.isIdentical) {
          this.$refs.carOwnerCertificateType.confirm()
        }
      }
    },

    async getCarDetailByCcid(ccid, prodId) {
      const { data } = await this.$store.dispatch('getCarDetailByCcid', { ccid, page: 'orderConfirm' })
      const {
        depositType, configDetail: {
          totalPrice,
          carModel: { modelCode, modelNameCn, modelPrice }, carSeries: { seriesCode }, optionList,
          outsideColor,
          insideColor
        }
      } = data || {}

      this.prices = {
        totalPrice,
        modelNameCn,
        modelPrice
      }

      // this.optionList = optionList || []
      // const { INSIDE, OUTSIDE } = CCPRO_OPTIONS
      // const option = []
      // const inside = []
      // const outside = []
      // optionList.forEach((i) => {
      //   if ([...INSIDE, ...OUTSIDE].includes(i.optionClassification)) {
      //     if ([...INSIDE].includes(i.optionClassification)) {
      //       i.typeName = '内饰'; inside.push(i)
      //     } else {
      //       i.typeName = '外饰'; outside.push(i)
      //     }
      //   } else {
      //     i.typeName = '选配'; option.push(i)
      //   }
      //   i.price = +i.optionPrice
      // })

      this.optionList = [insideColor, outsideColor, optionList]

      if ([modelCode, seriesCode, depositType, prodId].every((i) => i)) {
        this.handleGetNgaPrice(modelCode, seriesCode, depositType, totalPrice, prodId)
      }
      await this.getFloors()
    },
    async handleGetNgaPrice(modelCode, seriesCode, depositType, totalPrice, prodId) {
      this.ngaParams = {
        modelCode, seriesCode, depositType, prodId
      }
      const { orderId, luckyPackageId } = paramsStrict(this.$route.query)
      const { data: { data: { prodNgaDepositPrice, prodNgaDownPayPrice } } } = await getNgaPrice({
        modelCode, seriesCode, depositType, prodId
      })
      // if (!orderId && !luckyPackageId && prodNgaDepositPrice) {
      //   this.money = prodNgaDepositPrice / 100
      // }
      // if (this.isOrderBigCate()) {
      //   this.money = (prodNgaDownPayPrice - prodNgaDepositPrice) / 100
      // }
      this.money = prodNgaDownPayPrice / 100
    },
    async getCopProdQuery(skuid, currentId) {
      const { orderId, luckyPackageId } = this.$route.query
      const { data } = await getCopProdQuery({ skuid })
      console.log('getCopProdQuery=', data)
      if (codeType.includes(data.code)) {
        // if (!orderId && !luckyPackageId) { // 修改订单通过订单详情获取金额 不从这里获取
        //   console.log('%c [ !orderId && !luckyPackageId ]-1887', 'font-size:14px; background:#cf222e; color:#fff;', !orderId && !luckyPackageId)
        //   // this.money = (this.bestRecommendId ? data.data.defaultProdSku.confirmOrderAmount : data.data.defaultProdSku.reserveOrderAmount) / 100 // 单位：分 => 元
        //   // this.money = (data.data.defaultProdSku.reserveOrderAmount) / 100
        // }
        if (luckyPackageId) {
          this.money = (data.data.defaultProdSku.confirmOrderAmount - data.data.defaultProdSku.reserveOrderAmount) / 100
        }
        this.skuSnapshotId = data.data.defaultProdSku.prodSkuSnapshotId
        // this.carConfigInformation.seriesNameCn = data.data.prodName
        const { data: { prodId } } = data || {}
        console.log('%c [ getCopProdQuery ]-1916', 'font-size:14px; background:#cf222e; color:#fff;', prodId)
        this.getCarDetailByCcid(currentId, prodId)
      }
    },
    // 刷新ccid
    async newCarconfig(ccid) {
      const { data } = await refreshCcid({ ccid })

      console.log('%c [ refreshCcid ]-2016', 'font-size:14px; background:#cf222e; color:#fff;', data)
      const newCcid = data.data || ''
      if (!newCcid) {
        return { error: data.message || '切换ccid 出错' }
      }
      this.$store.commit('updateCcid', newCcid)
      localStorage.setItem('ccid', newCcid)
      return { newCcid }
      // this.placeAnOrder()
    },
    toDeliveryPattern() { // 跳转交车方式
      if (this.dealerName) {
        console.log(this.deliveryPattern)
        const deliverAddr = this.deliveryPattern?.deliverAddr
        let deliveryType = this.deliveryPattern?.deliverType
        if (deliveryType > 1) deliveryType = 2
        this.$router.push({
          path: '/delivery-pattern',
          query: { isluckybag: true, deliverAddr, deliveryType }
        })
      } else {
        callNative('toast', { type: 'fail', message: '请先选择代理商' })
      }
    },
    async advance() {
      if (this.orderId) { // 修改订单
        this.onModify()
        return
      }

      // 有值说明下过单
      const oldCcid = localStorage.getItem('oldCcid')
      const { luckyPackageId, ccid } = this.$route.query || ''
      if (oldCcid && !luckyPackageId) {
        const { error, newCcid } = await this.newCarconfig(localStorage.getItem('ccid')) || ' '

        console.log('%c [ error, newCcid ]-2051', 'font-size:14px; background:#cf222e; color:#fff;', error, newCcid)
        if (error) {
          return Toast({
            className: 'toast-dark-mini toast-pos-middle',
            message: error || '网络请求错误',
            forbidClick: true,
            duration: 800
          })
        }
        if (newCcid) {
          // 由于用户前面已下单直接返回当前页面，等场景下，需要刷新ccid下单，这里新增业务只是仅限通知服务端
          const { data: { code, message } } = await checkRefreshStockCar({
            ccid: newCcid,
            oldccid: ccid
          }) || ''

          if (code !== '00') {
            return Toast({
              className: 'toast-dark-mini toast-pos-middle',
              message: message || '网络请求错误',
              forbidClick: true,
              duration: 800
            })
          }
        }
      }
      this.placeAnOrder()
    },
    async placeAnOrder() {
      const { shoppingCartId, dealerCode } = paramsStrict(this.$route.query)
      sessionStorage.removeItem('orderId')
      // 掉预下单接口，
      // this.$store.commit('showLoading')
      const carCustomId = localStorage.getItem('ccid') || ''
      // const { data } = await advance({
      //   carCustomId,
      //   itemList: [{
      //     quantity: 1,
      //     skuId: localStorage.getItem('skuid') || ''
      //   }],
      //   // payPhase: this.bestRecommendId ? '11' : '10',
      //   payPhase: 10
      // })


      // console.log('%c [ advance ]-2280', 'font-size:14px; background:#cf222e; color:#fff;', data)

      // if (codeType.includes(data.code)) {
      const that = this
      if (this.formData.buyType === '个人') {}
      // 成功之后调用NGA车辆下单（带购车人信息）接口
      let param = {}

      const str = this.columns1.find(
        (item) => item.text === this.formData.type
      ).value

      const moreContact = `${str},${this.formData.code}`
      if (this.formData.buyType === '个人') {
        const str2 = this.columns1.find(
          (item) => item.text === this.formData.carOwnerCertificateType
        ).value
        param = {
          carBuyerInfo: {
            fullName: this.formData.fullName,
            mobile: this.formData.mobile,
            buyType: '01',
            remark: this.formData.remark,
            moreContact: moreContact,
            carOwnerName: this.formData.carOwnerName,
            carOwnerMobile: this.formData.carOwnerMobile,
            carOwnerCertificateType: str2,
            carOwnerCertificateNumber: this.formData.carOwnerCertificateNumber
          },
          itemList: [{
            quantity: '1',
            dealerId: this.dealerCode,
            skuId: this.$route.query.skuId || localStorage.getItem('skuid') || '',
            skuSnapshotId: this.skuSnapshotId
          }],
          // extInfo: {
          //   paymentMethod: this.formData.paymentMethod // 全款:10, 贷款:20
          // },
          payPhase: this.orderLuckyBags ? '11' : '10',
          saleConsultantId: null,
          carCustomId,
          carLicenseCityName: this.formData.carLicenseCityName,
          carLicenseCityCode: this.formData.carLicenseCityCode
        }
      } else {
        param = {
          carBuyerInfo: {
            fullName: this.formData.fullName,
            mobile: this.formData.mobile,
            enterpriseName: this.formData.enterpriseName,
            enterpriseCode: this.formData.enterpriseCode,
            buyType: '02',
            remark: this.formData.remark,
            moreContact: moreContact
          },
          itemList: [{
            quantity: 1,
            dealerId: this.dealerCode,
            skuId: localStorage.getItem('skuid') || '',
            skuSnapshotId: this.skuSnapshotId
          }],
          // extInfo: {
          //   paymentMethod: this.formData.paymentMethod // 全款:10, 贷款:20
          // },
          // payPhase: this.bestRecommendId ? '11' : '10',
          payPhase: '10',
          saleConsultantId: null,
          carCustomId,
          carLicenseCityName: this.formData.carLicenseCityName,
          carLicenseCityCode: this.formData.carLicenseCityCode
        }
      }
      if (shoppingCartId) {
        param = {
          ...param,
          shoppingCartId,
          inviteCode: this.inviteCode
        }
      }
      console.log('这是下单信息参数', param)

      if (this.isReserveCard) {
        this.dealerInfo.isReserveCard = this.isReserveCard
      }
      if (!param.carLicenseCityCode) {
        param.carLicenseCityCode = this.dealerInfo.cityCode
      }
      if (!param.carLicenseCityName) {
        param.carLicenseCityName = `${this.dealerInfo.provinceName}/${this.dealerInfo.cityName}`
      }

      document.getElementById('detail')?.scrollIntoView({ block: 'start' })

      this.$store.dispatch('SetOrder', { ...param, ...{ dealerInfo: this.dealerInfo } })

      this.$router.push({ name: 'order-affirm', query: { form: 'froms',dealerCode:dealerCode || '' } })
      // 新版去确认订单页面支付，故终止执行
      if (param) return

      const data2 = await carPlaceOrder(param)
      // 缓存存oldccid 下次进入更新
      localStorage.setItem('oldCcid', 'oldCcid')
      if (codeType.includes(data2.data.code)) {
        if ((this.deliveryPattern && this.bestRecommendId) || this.orderLuckyBags) {
          const params = {
            ...this.deliveryPattern,
            orderId: data2.data.data.orderId
          }
          const res = await updateAmsDeliverType(params)
        }
        localStorage.setItem('orderItem', JSON.stringify(data2.data))
        this.$store.commit('setOrderId', data2.data.data.orderId)
        // 这里要兼容小程序的支付接口
        const { env } = getUrlParamObj()
        if (env === 'minip') {
          // 支付成功后的url地址
          const { origin, pathname } = window.location
          const orderId = data2.data.data.orderId
          const url = encodeURIComponent(`${origin}${pathname}#/order/money-detail?orderId=${orderId}`)
          wx.miniProgram
            .navigateTo({ url: `/pages/pay/pay?orderId=${orderId}&url=${url}&colorNameCn=${that.colorNameCn}&inColorNameCn=${that.inColorNameCn}&seriesCode=${that.carSeries.seriesCode}&mobile=${that.formData.mobile}` })
        } else {
          // 原生支付的接口
          const payCenterData = await callNative('callPayCenter', {
            orderId: Number(data2.data.data.orderId)
          },{orderInfo: data2.data.data && JSON.stringify(data2.data.data)})
          console.log(payCenterData, 'callPayCenterSuccess')
          const data = await callNative('callPayResult', {})

          console.log('%c [ callNative callPayResult ]-2397', 'font-size:14px; background:#07c160; color:#fff;', data)
          const payType = data.payType ? data.payType : ''
          const fun = (i) => {
            console.log('i=', i)
            // 查询支付结果
            setTimeout(() => {
              getOrderLimitedNumberStatus({
                orderId: data2.data.data.orderId
              }).then((resolve) => {
                if (codeType.includes(resolve.data.code)) {
                  if (+resolve.data.data.status === 1) {
                    // 后台处理好限量号了
                    that.$store.commit('hideLoading')
                    sessionStorage.setItem('isFormDetail', '1')
                    limitedNumbers({
                      orderId: data2.data.data.orderId
                    }).then((ress) => {
                      if (codeType.includes(ress.data.code)) {
                        const { dealerCode, areaCode } = this.$store.state.dealerInfo || {}
                        // 查询是否是数字人民币支付
                        if (ress.data.data === false) {
                          that.$router.push({
                            path: '/order/payment-error',
                            query: {
                              dealerCode,
                              areaCode,
                              orderId: data2.data.data.orderId,
                              colorNameCn: that.colorNameCn,
                              inColorNameCn: that.inColorNameCn
                            }
                          })
                        } else {
                          that.$router.push({
                            path: '/order/payment-success',
                            query: {
                              dealerCode,
                              areaCode,
                              orderId: data2.data.data.orderId,
                              colorNameCn: that.colorNameCn,
                              inColorNameCn: that.inColorNameCn
                            }
                          })
                        }
                      } else {
                        // Toast({
                        //   type: "fail",
                        //   message: ress.data.message,
                        //   icon: require("../../assets/img/error.png"),
                        // });
                      }
                    })
                  } else {
                    // 失败到详情
                    if (i >= 4) {
                      sessionStorage.setItem('isFormDetail', '1')
                      that.$router.push({
                        path: '/order/money-detail',
                        query: {
                          orderId: data2.data.data.orderId
                        }
                      })
                    } else {
                      fun(++i)
                    }
                  }
                }
              })
            }, 1000)
          }
          if (payType === 'CNY') {
            // 使用数字人民币支付
            that.$router.push({
              path: '/order/pay-flow',
              query: {
                orderId: data2.data.data.orderId || ''
              }
            })
          } else {
            fun(0)
          }
        }
      } else {
        callNative('toast', { type: 'fail', message: data2.data.message })
      }
    },
    updateForm() {
      this.formData.carOwnerName = this.formData.fullName
      this.formData.carOwnerMobile = this.formData.mobile
      this.formData.carOwnerCertificateType = this.formData.type
      this.formData.carOwnerCertificateNumber = this.formData.code
    },
    showCityList() {
      const { orderId, isReserveCard, unalterable: { dealerCity } } = this
      if (orderId || isReserveCard || dealerCity) return
      this.formDataShow.showCarCity = true
    },
    filterInput(val) {
      return val.replace(/[^-_·a-zA-Z0-9\u4e00-\u9fa5\u439a]/, '').replace(/\s+/g, '')
    },
    formFormatterName(name, value) {
      const strToUpperCase = (string) => string.replace(/[a-z]/g, (s) => s.toUpperCase())
      this.formData[name] = strToUpperCase(value).replace(/\s+/g, '')
    },
    formFormatterEnterpriseName(name, value) {
      this.formData[name] = value.replace(/\s*/g, '').replace(/\(/g, '（').replace(/\)/g, '）').replace(/\s+/g, '')
    },
    formValidatorName: (name) => /^(?!·)(?:[\u4e00-\u9fa5·\-A-Z]{1,50})([\u4e00-\u9fa5\-A-Z])$/.test(name),
    formValidatorMobile(mobile, { name }) {
      const { formData } = this
      const validator = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(mobile)
      return name === 'carOwnerMobile' ? validator && formData.mobile !== mobile : validator
    },
    // eslint-disable-next-line no-nested-ternary
    formValidatorIDCode: (code, { type }) => {
      if (type === '居民身份证') {
        const [result, msg] = validateIdCard(code)
        return result
      }
      return /^.+$/.test(code)
    },
    enterpriseName: (name) => /^(?:[\u4e00-\u9fa5()（）\-a-zA-Z]{5,80})$/.test(name),
    enterpriseCode: (code) => checkSocialCreditCode(code),
    goToFinancialCalcPage() {
      const {
        $router, $route: {
          query: {
            ccid, skuid, dealerCode
          }
        }, seriesCode
      } = this
      $router.push({
        name: 'FinancialCalculator',
        query: {
          ccid,
          skuid,
          dealerCode,
          from: 'order-forms',
          seriesCode
        }
      })
    },
    async getStockCarConfigDetail(ccid) {
      const {data} = await getStockCarConfigDetail({ ccid })
      console.log(data,'123123123');
      if(data.code == '00' && data.data) {
        let carUserInfo = JSON.parse(data.data.carUserInfo)
        this.formData.buyType = carUserInfo.buyType == '01' ? '个人' :'企业'
        if(carUserInfo.buyType == '02') {
          this.formData.enterpriseName = carUserInfo?.enterpriseName
          this.formData.enterpriseCode = carUserInfo?.enterpriseCode
        }else {
          
          this.formData.carOwnerMobile = carUserInfo.carOwnerMobile
          this.formData.carOwnerName = carUserInfo.carOwnerName
          this.formData.carOwnerCertificateNumber = carUserInfo.carOwnerCertificateNumber
          const {text} = this.columns1.find((item) => item.value == carUserInfo.carOwnerCertificateType)
          this.formData.carOwnerCertificateType = text
          if( this.formData.carOwnerMobile == this.formData.mobile) {
            this.formData.type = this.formData.carOwnerCertificateType
            this.formData.code = carUserInfo.carOwnerCertificateNumber
            this.formData.fullName = carUserInfo.carOwnerName
            this.isIdentical = true
          }
        }
       
        console.log(carUserInfo,'carUserInfocarUserInfo');
        this.$store.dispatch('getDealerOfCode', { dealerCode:data.data.dealerCode })
      }
    },
    async initData() {
      getAPITimeOutTesting()
      // luckyPackageId => 福包id
      const {
        carCustomId, skuid, ccid, orderId, shoppingCartId, luckyPackageId, dealerCode, from, drmCode
      } = this.$route.query
      const { ccid: storeCcid } = this.$store.state

      // 大定订单修改信息 获取订单信息
      if (orderId) {
        this.canXdUpdateInfo()
        this.getMyOrders()
      } else {
        // this.getData()
      }

      if (shoppingCartId) {
        this.getShoppingInfo(shoppingCartId)
      }

      /**
         * carCustomid 是旧版本传进来的ccid
         * ccid是新版本接收的ccid, 本质是同一个数据,优先用ccid, 删掉carCustomId
         */
      const currentId = ccid ?? carCustomId ?? storeCcid
      if (currentId && skuid) {
        // 获取ccid和skuId
        localStorage.setItem('ccid', currentId)
        localStorage.setItem('skuid', skuid)
        
      } else {
        console.error(`current page: detail ccid: ${currentId}, skuid :${skuid}`)
      }
      if (!this.dealerCode && !this.bestRecommandDealerCode && !this.orderId) {
        // this.getHeadquartersDealerInfo('76600019')
      }

      console.log('%c [ this.bestRecommandDealerCode, drmCode, this.dealerCode ]-1185', 'font-size:14px; background:#cf222e; color:#fff;', this.bestRecommandDealerCode, ',', drmCode, ',', this.dealerCode)
      if (this.bestRecommandDealerCode || (!this.dealerCode && drmCode)) {
        this.getHeadquartersDealerInfo(this.bestRecommandDealerCode || drmCode)
      }
      // this.getCarDetailByCcid(currentId)
      await this.getCopProdQuery(skuid, currentId)
      this.getUserInfo()
      
      setTimeout(() => {
        if (sessionStorage.getItem('isFormDetail') === '1') {
          const newFormData = JSON.parse(localStorage.getItem('formDetail'))
          this.formData.type = newFormData.type
          this.formData.carOwnerCertificateType = newFormData.carOwnerCertificateType
          setTimeout(() => {
            Object.keys(newFormData).forEach((item) => {
              if (!['type', 'carOwnerCertificateType'].includes(item)) {
                this.formData[item] = newFormData[item]
              } else if (item === 'type') {
                this.isIdentical = localStorage.getItem('isIdentical') === '1'

                console.log('%c [  ]-1196', 'font-size:14px; background:#cf222e; color:#fff;')
              }
            })
            // if (!orderId) {
            //   const {
            //     dealerInfo: {
            //       cityCode, cityName, provinceName, mineLocationCitesCode, mineLocationCitesName
            //     }
            //   } = this

            //   // if (cityCode && cityName) {
            //   //   this.formData.carLicenseCityCode = mineLocationCitesCode || cityCode
            //   //   this.formData.carLicenseCityName = mineLocationCitesName || `${provinceName}/${cityName}`
            //   // }
            // }
          }, 0)
          // const newIsIdentical = localStorage.getItem('isIdentical') === '1'
          // if (this.isIdentical === newIsIdentical) {
          //   setTimeout(() => {
          //     [
          //       'carOwnerName',
          //       'carOwnerMobile',
          //       'carOwnerCertificateNumber',
          //       'fullName',
          //       'code',
          //       'enterpriseName',
          //       'enterpriseCode'
          //     ].forEach((item) => {
          //       if (this.formData[item] !== '') {
          //         // this.animation(item);
          //         this.animations[item] = true
          //       } else {
          //         this.animations[item] = false
          //       }
          //     })
          //   })
          // } else {
          //   // this.isIdentical = newIsIdentical
          // }

          Object.keys(this.animations).forEach((item) => {
            if (this.formData[item]) {
              this.handlerBlur(item)
            }
          })
        }
      }, 0)
      // this.$nextTick((i) => {
      //   this.$refs.buyType.confirm()
      //   this.$refs.type.confirm()
      // })
      if (luckyPackageId) {
        this.orderLuckyBags = true
        this.getLuckyBagIdInfoData(luckyPackageId)
        this.handleGetLuckyBagModelStock(luckyPackageId)
      }
      if (dealerCode) {
        // 代理商门店里的车 默认不允许选择城市/代理商信息
        this.unalterable.dealerName = true
      }
      (!orderId || !dealerCode || !this.bestRecommandDealerCode) && this.handleGetLocationCity()
      console.log('++++carDetail++++++', this.$store.state.carDetail)

      Toast.clear()
      
    },

    networkReload() {
      this.initData()
    }
  }
}
</script>

<style lang="less" scoped>
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/dialog.less");
  // @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/nor.less");
  @import url("../../assets/style/common.less");

  .dialog-title {
    .c-font16;
    .c-bold;
    text-align: left;
    margin-bottom: 20px;
  }

  .goods-img {
    padding: 16px;
    background: linear-gradient(180deg, #F0F0F0 0%, #fbfbfb 100%);
    .carimg-wrapper {
      width: 108px;
      height: 108px;
      overflow: hidden;
      img {
        height: 108px;
        width: auto;
      }
    }
  }


  #detail {
    margin-bottom: 100px !important;
    &.detail-affirm-box {
      margin-bottom: 0 !important;
    }
    .box {
      margin: 0 15px 0 17px;
      padding: 0;
    }

    // padding-bottom: 40px;

    .checked {
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 20px;
      color: rgb(158, 170, 190);

      span {
        // font-size: 14px;
        color: #000;
      }
    }

    .imgCard {
      box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);

      img {
        width: 100%;
        vertical-align: top;
      }

      .imgCardText {
        display: flex;
        justify-content: space-between;
        height: 54px;
        line-height: 54px;
        font-size: 14px;
        padding: 0 6px 0 16px;

        span {
          margin-bottom: 16px;
        }

        .imgCardTextRight {
          display: flex;
          align-items: center;

          span {
            margin-bottom: 0;
          }
        }

        i {
          width: 20px;
          height: 20px;
          color: rgb(145, 144, 144);
          margin-right: 7px;
        }
      }
    }

    .toolimg {
      width: 100%;
    }

    .paymentDetail {
      h3 {
        border-bottom: 1px solid #e5e5e5;
      }
    }

    .paymentDetail,
    .buyMess {
      h3 {
        padding: 16px 0;
        height: 24px;
        line-height: 24px;
        font-size: 16px;
        font-family: "Audi-WideBold";
        font-weight: 500;
        color: #1a1a1a;
        margin: 0 16px ;
      }

      .paymentDetailMoney {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        line-height: 46px;
        font-size: 14px;
        font-weight: 400;
        color: #000;
        border-bottom: 1px solid #e5e5e5;

        span {
          font-size: 14px;
          font-weight: 400;
          color: #000;

          &:first-child {
            font-family: "Audi-ExtendedBold";
          }

          &:last-child {
            font-weight: bold;
            font-family: "Audi-WideBold";
          }
        }
      }

      img {
        margin-top: 16px;
        width: 100%;
        display: block;
      }
    }

    .buyRemark {
      border: 1px #333 solid;
      padding: 5px;
      height: 60px;
      margin-bottom: 10px;

      /deep/.van-cell {
        overflow: visible;
      }

      /deep/.van-field__word-limit {
        margin-top: -15px;
      }

      /deep/.van-field__body {
        textarea {
          line-height: 20px;
          width: 100%;
        }
      }
    }

    .buyMess {
      /deep/.van-form {
        & > .box-field {
          display: flex;
          flex-direction: column;
          position: relative;
          height: 72px;

          .box-label {
            width: 100%;
            font-size: 12px;
            color: #646566;
          }

          .van-cell {
            position: relative;
            height: 100%;

            .van-cell__value {
              .van-field__body {
                min-height: calc(100% - 25px);
                border-bottom: 1px solid #000;
                font-size: 16px;
                overflow: visible;
                flex-direction: column;
                justify-content: flex-end;
                align-items: flex-start;

                input {
                  margin-bottom: 6px;
                  font-size: 14px;
                  line-height: 20px;
                  height: 20px;
                }

                textarea {
                  margin-top: 16px;
                  line-height: 16px;
                  // padding-top: 25px;
                  min-height: 16px;
                }
              }
            }
          }

          .van-field--error {
            .van-field__body {
              border-bottom: 1px solid #9e1f32 !important;
            }

            .van-field__error-message {
              color: #9e1f32;
            }
          }
        }

        .jt {
          position: relative;

          i {
            position: absolute;
            right: 0;
            top: 25px;
          }

          input {
            margin-top: 6px !important;
          }
        }

        .check {
          height: 32px;
          min-height: 0;
          margin-bottom: 26px;

          span {
            color: #000;
            font-size: 14px;
          }

          .van-field {
            position: relative;
            width: 100%;
            border-bottom: 1px solid #000;

            .van-cell__title {
              font-size: 14px;
              line-height: 14px;
              min-width: 200px;
              margin: 0;
            }

            .van-field__value {
              position: absolute;
              right: 0;
              top: -4px;
              width: 16px;
              height: 16px;

              // .van-field__control {
              //   width: 100%;
              //   height: 100%;

              //   .van-checkbox {
              //     width: 16px;
              //     height: 16px;

              //     .van-checkbox__icon--square {
              //       width: 100%;
              //       height: 100%;

              //       i {
              //         position: relative;
              //         width: 16px;
              //         height: 16px;

              //         &::before {
              //           position: absolute;
              //           top: -2px;
              //           left: -1px;
              //         }
              //       }
              //     }
              //   }
              // }

              .van-field__body {
                border: 0;
              }
            }
          }
        }

        .jl {
          height: 48px;
          position: relative;

          .box-label {
            position: absolute;
            z-index: 1;
            font-size: 14px;
          }

          input {
            text-align: right;
            padding-right: 30px;
            margin-top: -8px;
          }

          // border-bottom: 1px solid #999;
          .van-field--error {
            .van-field__body {
              &::after {
                background: #9e1f32;
              }
            }

            .van-field__error-message {
              color: #9e1f32;
            }
          }

          .van-field__body {
            flex: 1;
            position: relative;

            &::after {
              content: "";
              display: block;
              position: absolute;
              bottom: 0;
              width: 100%;
              height: 1px;
              background: #000;
            }
          }

          .box-label {
            position: absolute;
            top: 0;
            .field-tips {
              font-size: 14px;
              color: #F50537;
            }
          }

          .van-cell {
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: flex-start;
          }

          .van-icon {
            position: absolute;
          }
        }

        .ns {
          min-height: 72px;
          height: auto;

          .van-cell {
            .van-field__body {
              margin-bottom: 16px;
            }

            textarea {
              height: 16px;
              // margin-top: 26px;
              margin-bottom: 4px;
              font-size: 14px;
              padding-top: 10px;
              box-sizing: border-box;
              caret-color: #000;
            }
          }

          .van-field--error {
            .van-field__body {
              margin-bottom: 0;
            }
          }

          .noAniName {
            top: 23px;
          }
        }
      }
    }
  }

  .checked {
    // line-height: 20px;

     /deep/.van-icon {
      width: 20px !important;
      height: 20px !important;
     }
  }

  // /deep/.van-field__error-message {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   height: 16px;

  //   &::before {
  //     content: "";
  //     display: inline-block;
  //     width: 14px;
  //     height: 14px;
  //     background: url("../../assets/img/error.png") no-repeat 0 0;
  //     background-size: 14px 14px;
  //     margin-right: 4px;
  //   }
  // }

  // /deep/.van-checkbox__icon--checked {
  //   .van-icon {
  //     background: #fff;
  //     color: #666;
  //     border-color: #666;
  //   }
  // }
   /deep/.van-checkbox__icon{
    height: auto !important;
   }
  /deep/.van-dialog {
    overflow-y: auto;
    max-height: 80%;
    padding: 15px 16px 16px;
    top: 52% !important;
    z-index: 33336;

    h3 {
      margin: 0;
    }

    .item {
      color: #000;
      font-size: 14px;
      text-align: left;
      margin-bottom: 24px;

      .title {
        line-height: 24px;
      }

      .itemCotent {
        display: flex;
        line-height: 17px;

        div {
          margin-top: 8px;
        }
      }
    }
  }

  /deep/.van-popup {
    border-radius: 0;
  }

  .jl {
    /deep/.van-popup {
      border-radius: 0;
      height: 400px;
      font-size: 14px;

      .carCitypicker {
        position: relative;
        z-index: 2333;
        transform: translateY(80px);
        text-align: center;
        width: 100%;
        display: flex;

        div {
          flex: 1;
        }
      }

      .van-picker__cancel {
        visibility: hidden;
      }

      .van-picker__columns {
        top: 80px;

        .van-hairline-unset--top-bottom {
          border-bottom: 1px solid #eee;
          border-top: 1px solid #eee;
        }

        .van-picker-column {
          font-size: 14px;
        }
      }
    }
  }

  // /deep/ .van-field__control {
  //   &:disabled {
  //     color: #000;
  //     -webkit-text-fill-color: #000;
  //   }
  // }

  // 经销商
  .cpage-marign {
    margin: 0 16px;
  }

  .dealer-wrapper {
    .cpage-marign;

    > .line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 0;

      > .title {
        font-size: 16px;
        font-weight: bold;
      }

      > .btn-change {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #a3a3a3;
      }
    }

    > .dealer-info-wrapper {
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);

      > .main-img-wrapper {
        min-height: 120px;
        overflow: hidden;
      }

      > .layout-bottom {
        display: flex;
        position: relative;
        font-size: 12px;

        > .left {
          width: 76%;
          padding: 20px 10px;

          .title {
            font-size: 16px;
          }

          .address {
            margin-top: 15px;
            color: #a3a3a3;
          }
        }

        > .right {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;

          .distance {
            color: #a3a3a3;
            text-align: center;
          }
        }
      }
    }
  }

  .similar{
    margin: 0 16px;
    .title{
      font-size: 16px;
      font-weight: bold;
      margin: 16px 0;
    }
    .swiper{
      display: flex;
      align-items: center;
      justify-content: center;
      border: solid 1px #000;
      .left{
        flex: 1
      }
      .right{
        width: 218px;
        img{
          width: 100%;
          max-height: 100%;
        }
      }
    }
  }

  .equity-img-wrapper {
    .cpage-marign;
    margin-top: 22px;
  }

  .btnWarpper {
    position: fixed;
    height: 98px;
    width: 100%;
    bottom: 0px;
    padding: 9px 16px 39px;
    background: #fff;
    z-index: 1024;
    box-sizing: border-box;
    box-shadow: 0px -1px 16px 0px rgba(0,0,0,0.08);
    .lan-button-box .lan-button{
      height: 48px;
      line-height: 48px;
    }

    .buttons,
    .buttons2 {
      width: calc(100% - 32px);
      position: absolute;
      top: 5px;
      bottom: 0;
      margin: 0 auto;
    }

    .bt {
      content: "";
      display: block;
      background: #000;
      opacity: 0.2;
      width: 134px;
      height: 5px;
      position: absolute;
      bottom: 9px;
      left: -32px;
      right: 0;
      margin: 0 auto;
      border-radius: 100px;
    }
  }
.big-order {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 16px;
  p {
    margin: 0;
  }
  .price-box {
    font-size: 14px;
    padding-top: 16px;
  }
}
#detail {
  .empty-book-pop {
    text-align: center;
    width: 85%;
    padding: 0 24px 16px;
    box-sizing: border-box;
    .h3 {
      padding: 20px 32px 0;
      font-size: 16px;
      font-weight: normal;
      line-height: 180%;
    }
  }
}
// /deep/ .van-field--disabled {
//   .van-field__control {
//     color:#aaa;
//     -webkit-text-fill-color: #aaa
//   }
// }
.order-info {
  padding-left: 8px;
  .order-title {
    width: 100%;
    color: rgba(0, 0, 0, 0.6);
    line-height: 22px;
    font-size: 14px;
    font-family: "Audi-Normal"
  }
  .price-box {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    .price {
      color: #000;
      font-family: Audi-ExtendedBold;
      margin-right: 4px;
    }
  }
}
#detail {
  /deep/.van-form {
    .van-cell-group:first-child {
      border-top-width: 0;
    }
  }
}
.cell-top{
  padding-top: 32px !important;
}
.order-forms-box {
  .agreement-box{
    padding-top: 16px;                                   
  }
  .financial-plans-box{
    padding-top: 16px;
  }
}

</style>
<style lang="less">
@import url("../../assets/style/forms-cell.less");
.view-wrapper {
  height: calc(100vh - 60px);
}

.hidden-box {
  margin: 0;
  padding: 0;
  overflow: hidden;
}
</style>
