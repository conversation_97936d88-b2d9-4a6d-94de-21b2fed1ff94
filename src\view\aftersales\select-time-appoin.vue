<template>
  <div class="_container">
    <selectTime :timelist="timeData" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import selectTime from './select-time.vue'
import { getAfterSaleListResourceNew } from '@/api/api'
import { callNative } from '@/utils'

export default {
  components: { selectTime },
  data() {
    return {
      timeData: []
    }
  },
  async mounted() {
    const { ownerCode } = this.$route.query
    this.$store.commit('showLoading')
    const tempDate = new Date() // 获取今天的日期
    tempDate.setDate(tempDate.getDate() + 1) // 今天的前N天的日期，N自定义
    const beginDate = `${tempDate.getFullYear()}-${
      tempDate.getMonth() + 1
    }-${tempDate.getDate()}`
    console.log(beginDate)

    const tempDate2 = new Date() // 获取今天的日期
    tempDate2.setDate(tempDate2.getDate() + 31) // 今天的前N天的日期，N自定义
    const endDate = `${tempDate2.getFullYear()}-${
      tempDate2.getMonth() + 1
    }-${tempDate2.getDate()}`
    console.log(endDate)
    const { data } = await getAfterSaleListResourceNew({
      beginDate: beginDate,
      endDate: endDate,
      serviceCode: ownerCode
    }) //
    this.$store.commit('hideLoading')
    if (data.data === null) {
      callNative('toast', { type: 'fail', message: '暂无可预约时间' })
      return
    }
    console.info('🚀 ~ file:select-time-appoin method:mounted line:46 -----', data.data)


    tempDate.setDate(tempDate.getDate()+1) // 今天的前N天的日期，N自定义
    tempDate2.setDate(tempDate2.getDate() + 31) // 今天的前N天的日期，N自定义
    const timeDemo = [] // 获取数据
    for (let i = 0; i < 30; i++) {
      const day = dayjs().add(i, 'day').format('YYYY-MM-DD')
      let resourcesList = []
      let findData = data.data.find(e => e.date === day)
      if (findData) {
        resourcesList = findData.resources
      }
      timeDemo.push({
        date: day,
        month: dayjs(tempDate).add(i, 'day').format('MM'),
        resources: resourcesList
      })
    }
    this.timeData = timeDemo // 获取数据
  },
  methods: {}
}
</script>

<style lang="less" scoped>
</style>
