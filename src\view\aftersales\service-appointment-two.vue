<template>
  <div class="serviceAppointmentTwo">
    <div class="order-forms-box">
      <van-form ref="form" @failed="failed" @submit="confirmVisible = true">
        <van-cell-group>
          <div class="cell-title van-hairline--bottom service-line">
            <div><h2 class="h2">选择服务</h2></div>
            <div class="btn-change" @click="onServiceState">
              <img class="btn-icon" src="../../assets/img/icon_explain.png" />
            </div>
          </div>

          <!-- <div class="box-field" @click.stop="animation('vin')" id="vin" style="height: 60px;"> -->

            <van-field
              id="vin"
              :readonly = "loading"
              label="VIN码"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="vin"
              ref="vin"
              type="text"
              maxlength="17"
              on-key-up="value=value.replace(/[\W]/g,'')"
              @click="animation('vin')"
              @blur="handlerBlur('vin')"
              @focus="handlerFocus('vin')"
              placeholder="请输入您的VIN码"
              :rules="[
                  {
                    trigger: 'onChange',
                    required: true,
                    validator: asyncValidatorVin,
                    message: '未查询到该VIN，请检查填写是否正确',
                  },
                  // {
                  //   trigger: 'onBlur',
                  //   required: true,
                  //   validator: asyncValidatorVin,
                  //   message: '未查询到该VIN，请检查填写是否正确',
                  // },
                  {
                    pattern: /\w{17}/,
                    message: '请输入正确的VIN码',
                  }
                ]" />

              <!-- <van-loading
                class="lan-loading-custom lan-custom-center lan-loading-darkly enable-masking-out lan-loading-decephaly"
                size="24px"
                vertical
                v-if="loading"
              >
                正在加载···
              </van-loading> -->

            <div id="vinHistory" style="margin-top: -20px;">
              <van-popover
                :close-on-click-outside="true"
                get-container="#vinHistory"
                v-model="showPopover"
                trigger="click"
                :actions="actions"
                placement="bottom-start"
                @select="onSelect" />
            </div>
          <!-- </div> -->

          <van-field
            is-link
            readonly
            label="车系"
            :placeholder="placeholder"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="seriesName"
            ref="seriesName"
            type="text"
            @click="onSelectSeries"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择车系',
              },
            ]"
          />

          <van-field
            v-if="false"
            is-link
            readonly
            v-show="!omdHasVin"
            label="车型"
            placeholder="请选择车型"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="modelLineName"
            ref="modelLineName"
            type="text"
            @click="onSelectModels"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请选择车型',
              },
            ]"
          />

          <div class="box-field" @click="onInputCarNo" id="license">
            <van-field
              label="车牌"
              type="text"
              cols="24"
              placeholder="请输入您的车牌"
              autosize maxlength="8"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="submitParam.license"
              ref="license"
              @blur="handlerBlur('license')"
              @focus="handlerFocus('license')"
              :rules="[
                {
                  trigger: 'onBlur',
                  required: true,
                  message: '请填写车牌！',
                },
              ]" />
          </div>

        <div
          class="box-field"
          @click.stop="animation('mileage')"
          id="mileage"></div>

          <van-field
            label="里程(km)"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="mileage"
            ref="mileage"
            maxlength="7"
            type="number"
            on-key-up="value=value.replace(/[\W]/g,'')"
            @blur="handlerBlur('mileage')"
            @focus="handlerFocus('mileage')"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写里程！',
              },
            ]"
          />

          <van-field
            is-link
            readonly
            label="服务类型"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="serviceName"
            ref="serviceName"
            type="text"
            @click="onSelectServiceType"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写服务类型！',
              },
            ]"
          />

        </van-cell-group>
      </van-form>
    </div>

    <!-- <div class="input-line">
      <div class="input-title">里程(km)</div>
      <div class="input-change">
        <input class="input-name" type="tel" v-model="mileage" rows="1" @input="inputChange" />
      </div>
    </div> -->


    <!--服务项目只有在保养是在出现 erviceType === '1'-->
    <div class="align-center"
      v-show="upkeepList && upkeepList.length>0"
      style="margin-bottom: 16px;margin-top: 16px; border-bottom: 1px solid #f2f2f2;"
      >
      <p class="item-title-bold" >
        服务项目
      </p>
      <van-icon style="color: #9e1f32;" :name="serviceSatus ? 'arrow-down':'arrow-up'"
      @click="serviceSatus = !serviceSatus" />
    </div>
    <div v-show="serviceSatus">
      <div v-if="isMaintenance">
        {{ "保养件" }}
      </div>
      <div v-for="(item, idx) in upkeepList" :key="item.packageId">
        <div class="servershopmain" v-if="item.packageType === 1">
          <van-checkbox class="item_checkbox_button" v-model="item.checked" @change="onCheckbox(item)">
            <img class="item_checkbox_img" slot="icon" :src="item.checked ? activeIcon : inactiveIcon" />
          </van-checkbox>

          <div class="server_shopsright">
            <div class="server_shop" @click="serviceGoodsDetails(item)">
              <div>
                <img class="img_thumbnai" :src="
                    $loadWebpImage(item.picUrl + '?x-oss-process=image/resize,m_lfit,h_200,w_200')
                  " alt="" />
              </div>
              <div class="item-content">
                <div class="name_div">
                  {{ item.packageName }}
                </div>
                <div class="config_div">
                  {{
                    "￥" +
                    item.saleAmount
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                </div>
              </div>
              <div class="item_next_button">
                <img class="img_next" align="center" src="../../assets/img/icon_20.png" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style=" margin-top: 16px" v-if="isService">
        {{ "易损件" }}
      </div>
      <div v-for="(item, idx1) in upkeepList" :key=" 'idx1'+item.packageId">
        <div class="servershopmain" v-if="item.packageType === 2">
          <van-checkbox class="item_checkbox_button" v-model="item.checked" @change="onCheckbox(item)">
            <img class="item_checkbox_img" slot="icon" :src="item.checked ? activeIcon : inactiveIcon" />
          </van-checkbox>

          <div class="server_shopsright">
            <div class="server_shop" @click="serviceGoodsDetails(item)">
              <div>
                <img class="img_thumbnai" :src="
                    item.picUrl + '?x-oss-process=image/resize,m_lfit,h_200,w_200'
                  " alt="" />
              </div>
              <div class="item-content">
                <div class="name_div">
                  {{ item.packageName }}
                </div>
                <div class="config_div">
                  {{
                    "￥" +
                    item.saleAmount
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                </div>
              </div>
              <div class="item_next_button">
                <img class="img_next" align="center" src="../../assets/img/icon_20.png" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style=" margin-top: 16px" v-if="isDetection">
        {{ "检测项目" }}
      </div>
      <div v-for="(item, idx2) in upkeepList" :key=" 'idx2'+ item.packageId">
        <div class="servershopmain" v-if="item.packageType === 3">
          <van-checkbox class="item_checkbox_button" v-model="item.checked" @change="onCheckbox(item)">
            <img class="item_checkbox_img" slot="icon" :src="item.checked ? activeIcon : inactiveIcon" />
          </van-checkbox>

          <div class="server_shopsright">
            <div class="server_shop" @click="serviceGoodsDetails(item)">
              <div>
                <img class="img_thumbnai" :src="
                    item.picUrl + '?x-oss-process=image/resize,m_lfit,h_200,w_200'
                  " alt="" />
              </div>
              <div class="item-content">
                <div class="name_div">
                  {{ item.packageName }}
                </div>
                <div class="config_div">
                  {{
                    "￥" +
                    item.saleAmount
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                </div>
              </div>
              <div class="item_next_button">
                <img class="img_next" align="center" src="../../assets/img/icon_20.png" />
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>


    <!-- <van-tabs
      v-if="serviceType === '1'  || serviceType === '2'"
      v-model="active"
      color="#000"
      line-width="calc(50%)"
      line-height="2"
      title-active-color="#000"
    >
      <van-tab title="发动机基础保养套餐">

      </van-tab>
      <van-tab title="发动机全面保养套餐"> 暂无内容 </van-tab>
    </van-tabs> -->

    <!-- <p class="item-title-bold" v-if="serviceType === '1'  || serviceType === '2'">其他项目</p> -->

    <!-- <div class="btn-delete-height" /> -->

    <div class="bottom_style">
        <div class="checkbox_style" v-if="upkeepList && upkeepList.length>0">
          <div class="checkbox_styleRight">
            <span style="color: #666666; font-size: 14px">预估价格</span>
            <span>{{
              "￥" +
              getTotalAmount()
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",") +
              "起"
            }}</span>
          </div>
          <div
            class="c-font12"
            style="color: #999999; font-size: 12px; margin-top: 10px;text-align:right;"
          >
            服务项目及价格仅供参考，实际价格请以服务商报价为准
          </div>
        </div>


      <div class="btn-delete-wrapper">
        <AudiButton @click="onBtnNext" :text="'提交'" color="black" font-size="16px" height="50px" />
      </div>
    </div>
  </div>
</template>

<script>
  import Vue from "vue";
  import { mapState } from "vuex";

  import {
    Form,
    Field,
    Icon,
    Button,
    Picker,
    Popup,
    Toast,
    RadioGroup,
    Radio,
    Checkbox,
    CheckboxGroup,
    Dialog,
    AddressEdit,
    Area,
    Cell,
    CellGroup,
    Cascader,
    Tab,
    Tabs,
    Collapse,
    CollapseItem,
    Popover
  } from "vant";
  import AudiButton from "@/components/audi-button";
  import { callNative } from "@/utils";
  import storage from "../../utils/storage";

  import {
    getAfterSaleDetail,
    getPackageMainData,
    postBindCardInfo,
    getBindCarInfoList,
  } from "@/api/api";
  import api from "../../config/url";

  Vue.use(Form)
    .use(Button)
    .use(Field)
    .use(Icon)
    .use(Picker)
    .use(RadioGroup)
    .use(Radio)
    .use(Checkbox)
    .use(CheckboxGroup)
    .use(Dialog)
    .use(Popup)
    .use(Area)
    .use(Cell)
    .use(CellGroup)
    .use(AddressEdit)
    .use(Cascader)
    .use(Tab)
    .use(Tabs)
    .use(Collapse)
    .use(CollapseItem)
    .use(Popover);

  export default {
    components: {
      AudiButton,
    },
    data() {
      return {
        showPopover: false,
        actions: [],
        active: 2,

        labelWidth: 84,
        labelAlign: "left",
        ownerName: "", // 姓名
        driveTel: "", // 电话
        // appointmentTime: '', // 预约时间
        defaultIndex: {
          buyType: 0,
          type: "0",
          carOwnerCertificateType: "0",
        },
        animations: {
          license: false,
          vin: false,
          // mileage: false
        },

        submitParam: {
          license: "", // 车牌
        },
        vin: "", // VIN码
        mileage: "10000", // 里程
        appoId: "", // 预约ID 修改订单需要
        BaseOssHost: api.BaseOssHost,
        activeIcon: require("../../assets/img/checkbox_checked.png"),
        inactiveIcon: require("../../assets/img/checkbox_normal.png"),
        upkeepList: [], // 维修保养list
        isMaintenance:false,//是否有保养项
        isService:false,//是否有维修项
        isDetection:false,//是否有检测项
        selectList: [], // 选择的list

        modelLineName: '', // 车型名字
        modelCode: '', // 车型code
        seriesCode: '', // 车系code
        seriesName:'',//车系名称

        totalAmount: 0,
        serviceSatus: true,
        isInit: '0', // 是否是初次进来
        omdHasVin: true, // omd查询是否能查到vin
        loading: false,
        placeholder:'请选择'

      };
    },
    computed: {
      ...mapState({

        serviceType: (state) => state.selectServiceType.serviceType || "1", // 服务类型
        serviceName: (state) => state.selectServiceType.serviceName || "保养", // 服务类型
        inputRemark: (state) => state.selectServiceType.inputServiceRemark || "", // 服务类型


      }),
    },
    watch: {
      seriesName(value){
        this.getPackageMainData()
      },
      mileage(value) {
        if (value > 1000000) {
          this.mileage = 999999;
          callNative("toast", {
            type: "fail",
            message: "里程最大可输入999999km",
          });
        }
        if(value > 100){
          this.getPackageMainData();
        }

      },
      vin(value) {
        if(!value){
          return
        }
        //判断前三位是否是LSV
        if (value.length > 0 && value.substring(0,1) !== 'L') {
          this.vin = ''
          this.removeVin()
          return
        }
        if (value.length > 1 && value.substring(1,2) !== 'S') {
            this.vin = 'L'
            this.removeVin()
            return

        }
        if (value.length > 2 && value.substring(2,3) !== 'V') {
          this.vin = 'LS'
          this.removeVin()
        }
        if (value.length===17) {
          this.showPopover = false
        }
      },
    },
    mounted() {
      // query: {ownerName:this.submitParam.ownerName,driveTel:this.submitParam.driveTel,appointmentTime:this.appointmentTime},
      const { ownerName, ownerTel } = this.$route.query;
      this.ownerName = ownerName;
      this.ownerTel = ownerTel;

      var ownerVinInfo = storage.get('ownerVinInfo') || "{}"

      this.modelLineName = JSON.parse(ownerVinInfo).modelLineName || ''
      this.modelCode = JSON.parse(ownerVinInfo).modelCode || ''
      this.seriesCode = JSON.parse(ownerVinInfo).seriesCode || ''
      this.seriesName = JSON.parse(ownerVinInfo).seriesName || ''
      this.vin = JSON.parse(ownerVinInfo).vin
      // if (this.$store.state?.license) {
      //   this.submitParam.license = this.$store.state.license;
      // } else {
      this.submitParam.license = storage.get("carNo") || "";
      // }
      // if (this.$store.state?.vin) {
      //   this.vin = this.$store.state.vin;
      // }
      if (this.$store.state?.mileage) {
        this.mileage = this.$store.state.mileage;
      }
      this.changeEdit();

      if (this.$store.state?.isInitView2) {
        this.isInit = this.$store.state.isInitView2
      }
      if (this.isInit === '0') {
        this.getBindCardInfo();
      }

      // let userVin = storage.get('serviceUserVIN') || ''
      // if (userVin) {
      //   let userVinList = userVin.split(",")
      //   for (let i = 0; i < userVinList.length; i++) {
      //     if (userVinList[i] && i < 3) {
      //       this.actions.push({ text: userVinList[i] })
      //     }
      //   }
      // }

      const { appoId } = this.$route.query;
      let typeChange = sessionStorage.getItem('typeChange');
      this.appoId = appoId;
      if (appoId !== undefined && !JSON.parse(typeChange)) {
        this.getAfterSaleDetail(appoId);
      }
      // if (this.seriesCode) {
      //   this.getPackageMainData();
      // }
    },
    methods: {
      async asyncValidatorVin(val) {
        if (val.length != 17) {
          this.omdHasVin = false
          this.vin = val
          storage.set('ownerVinInfo', JSON.stringify({
            vin: this.vin,
            seriesCode: '',
            modelCode: '',
            modelLineName: '',
            seriesName: ''
          }))
          return false
        }

        this.loading = true
        this.placeholder = '查询中'

        const vinInfo = JSON.parse(storage.get('ownerVinInfo'));
        if (vinInfo) {
          this.seriesName =  vinInfo.seriesName || ''
          this.modelLineName = vinInfo.modelLineName  || ''
          this.modelCode =  vinInfo.modelCode || ''
          this.seriesCode =  vinInfo.seriesCode  || ''
        }else{
          this.seriesName =  ''
          this.modelLineName = ''
          this.modelCode = ''
          this.seriesCode = ''
        }

        const { data } = await getBindCarInfoList({ vins: val})
        if (!data || data.data == null ||data.data.length === 0) {
          this.omdHasVin = false
          this.placeholder = '请选择'
          storage.set('ownerVinInfo', JSON.stringify({
            vin: this.vin,
            seriesCode: this.seriesCode,
            modelCode: this.modelCode,
            modelLineName: this.modelLineName,
            seriesName: this.seriesName
          }))
          this.loading = false

          return false
        }
        if (data.data.length > 0) {
          this.omdHasVin = true
          this.vin =  data.data[0].vin
          this.modelLineName =  data.data[0].modelNameCn
          this.modelCode =  data.data[0].modelCode
          this.seriesName =  data.data[0].seriesName
          this.seriesCode =  data.data[0].seriesCode

          storage.set('ownerVinInfo', JSON.stringify({
            vin: this.vin,
            seriesCode: this.seriesCode,
            modelCode: this.modelCode,
            modelLineName: this.modelLineName,
            seriesName: this.seriesName
          }))
          this.loading = false
          return true
        }
        this.loading = false
        return false
      },
      inputChange() { //输入框值改变
        this.mileage = this.mileage.replace(/[^\d]/g, '')
      },
      removeVin(){
           callNative("toast", {
            type: "fail",
            message: "输入的VIN码不正确",
          });
      },
      onSelect(action) {
        console.log("action:",action)
        let vinStr = action.text.split('-')[1];
        console.log("vinStr:",vinStr)
        this.vin = vinStr.substring(1,vinStr.length);
        console.log(this.vin,",vinStr.length",vinStr.length)
        this.changeEdit()
      },
      async getBindCardInfo() {
        if (this.actions && this.actions.length>0) {
          return
        }
        const { data } = await postBindCardInfo({});
        if (data.data.length > 0) {
          this.actions = []
          for (let item of data.data) {
              if (item.vin) {
                this.actions.push({ text: item.shortName+' - '+ item.vin })
              }
            if (this.vin && this.vin === item.vin) {
              this.submitParam.license = item.plateNumber || storage.get("carNo") || "";
              storage.set("carNo", this.submitParam.license);
              //this.vin = data.data[0].vin;
              this.changeEdit();
            }
          }
        }
      },
      async getAfterSaleDetail(appoId) {
        const { data } = await getAfterSaleDetail({ appoId: appoId, type: 1 });

        if (data.code === "200") {
          this.submitParam.license = data.data.carNo;
          storage.set("carNo", this.submitParam.license);

          this.vin = data.data.vin;
          this.mileage = data.data.mileage;
          this.$store.commit("saveSelectModel", {
            seriesCode: data.data.seriesCode,
            modelCode: data.data.modelCode,
            modelLineName: data.data.modelName,
            seriesName:data.data.seriesName
          });
          let serviceName = "";
          if (data.data.serviceType === 1) {
            serviceName = "保养";
          } else if (data.data.serviceType === 2) {
            serviceName = "维修";
          } else if (data.data.serviceType === 3) {
            serviceName = "检查";
          } else {
            serviceName = "其他";
          }
          this.$store.commit("saveSelectServiceType", {
            serviceType: `${data.data.serviceType}`,
            serviceName: serviceName,
            inputServiceRemark:data.data.remark
          });
          this.changeEdit();
          let timer = setTimeout(()=>{
            clearTimeout(timer)

            data.data.packageList.forEach(item=>{
            this.upkeepList.forEach(element=>{
              if(item.packageId == element.packageId) {
                element.checked = true
              }
            })
          })
          },500)
        }
      },
      changeEdit() {
        if (this.submitParam.license !== "") {
          this.animations.license = true;
        }
        if (this.vin !== "") {
          this.animations.vin = true;
        }
        // if (this.submitParam.mileage !== '') {
        //   this.animations.mileage = true
        // }
      },
      // 获取服务项目

      async getPackageMainData() {
        // let packageType = 1; // 具体需业务定义：1：保养 2：易损易耗
        // if (this.serviceType === "1") {
        //   packageType = 1;
        // } else if (this.serviceType === "2") {
        //   packageType = 2;
        // } else {
        //   return;
        // }
        const { data } = await getPackageMainData({
          mileage: this.mileage,
          // packageType: packageType,
          modelCode: this.modelCode.substring(0,6),
          seriesCode: this.seriesCode,
        });
        if (data.code === "200" && data.data) {
          this.serviceSatus = true
          if (data.data.length > 0) {
            data.data.forEach((element) => {
              element.checked = element.checked === 1;
              element.num = null;
            });
          }
          this.upkeepList = data.data;
          const { appoId } = this.$route.query;
          if(appoId) {
            this.upkeepList.forEach(item=>{
              item.checked = false
            })
          }
          if (this.upkeepList.length > 0) {

            for (let item of this.upkeepList) {
              if (item.packageType === 1) {
                  this.isMaintenance = true
                  break
              }else{
                this.isMaintenance = false
              }
            }
            for (let item of this.upkeepList) {
              if (item.packageType === 2) {
                  this.isService = true
                  break
              }else{
                this.isService = false
              }
            }
            for (let item of this.upkeepList) {
              if (item.packageType === 3) {
                  this.isDetection = true
                  break
              }else{
                this.isDetection = false
              }
            }

            this.onCheckbox()
          }

        }else{
          this.serviceSatus = false
          this.upkeepList = []
          this.totalAmount = 0
        }
      },
      //选择车牌
      onInputCarNo() {
        this.sverSubmitParam();
        this.$router.push({
          path: "/aftersales/input-car-no",
          query: {},
        });
      },
      // 服务说明
      onServiceState() {
        if (this.modelCode === "") {
          callNative("toast", { type: "fail", message: "请选择车型" });
          return;
        }
        this.sverSubmitParam();
        this.$router.push({
          path: "/aftersales/service-state",
          query: {
            packageType: this.serviceType,
            seriesCode: this.seriesCode,
            modelCode:this.modelCode.substring(0,6)
          },
        });
      },
      //选择车系
      onSelectSeries(){
        if (this.omdHasVin) {
          return
        }
        this.sverSubmitParam();
        this.$router.push({
          path: "/aftersales/select-series-list",
          query: {},
        });

      },
      // 选择车型
      onSelectModels() {
        if (this.omdHasVin) {
          return
        }
        if(!this.seriesCode){
          callNative("toast", { type: "fail", message: "请先选择车系" });
          return
        }
        this.sverSubmitParam();
        this.$router.push({
          path: "/aftersales/select-models-list",
          query: {},
        });
      },
      // 选择服务类型
      onSelectServiceType() {
        this.sverSubmitParam();
        this.$router.push({
          path: "/aftersales/service-type",
          query: { serviceType: this.serviceType,inputRemark:this.inputRemark },
        });
      },
      sverSubmitParam() {
        // this.$store.state.license = this.submitParam.license;
        this.$store.state.vin = this.vin;
        this.$store.state.mileage = this.mileage;
        this.$store.state.isInitView2 = '1'
        this.$store.state.seriesName = this.seriesName;
      },
      // 选择服务套餐
      onCheckbox() {
        const arr = [];
        const MoneyList = [];
        this.upkeepList.forEach((item) => {
          if (item.checked === true) {
            MoneyList.push(item);
            arr.push(item.checked);
          }
        });

        // 价格要置为0  不然一直会累加的 也会又很大的问题
        this.totalAmount = MoneyList.length
        for (let i = 0; i < MoneyList.length; i++) {
          this.totalAmount += MoneyList[i].saleAmount;
        }

      },
      // 查看服务商品详情
      serviceGoodsDetails(item) {
        this.sverSubmitParam();
        this.$router.push({
          path: "/aftersales/upkeep-detail",
          query: { title: item.packageName, imgList: item.resources },
        });
      },
      getTotalAmount() {
        if (this.upkeepList) {
          let total = 0;
          for (let i = 0; i < this.upkeepList.length; i++) {
            if(this.upkeepList[i].checked){
              total += parseFloat(this.upkeepList[i].saleAmount);
            }
          }
          return total.toFixed(1);
        } else {
          return 0
        }

      },
      // 提交
      onBtnNext() {
        //  license: '', // 车牌
        //   vin: '', // VIN码
        //   mileage: '' //里程

        if (this.submitParam.license === "") {
          callNative("toast", { type: "fail", message: "请输入车牌" });
          return;
        }
        if (this.vin === "" || this.vin.length < 17) {
          callNative("toast", { type: "fail", message: "请输入正确的VIN码" });
          return;
        }
        if (this.mileage === "") {
          callNative("toast", { type: "fail", message: "请输入里程" });
          return;
        }
        // if (this.modelCode === "") {
        //   callNative("toast", { type: "fail", message: "请选择车型" });
        //   return;
        // }
        let selectArr = []
        // 保存选择的服务项
        this.upkeepList.forEach((element) => {
          if (element.checked) {
            selectArr.push(element)
          }
        });
        // 不选择服务项目 提示并阻止跳转
        if (!selectArr.length) {
          callNative("toast", { type: "fail", message: "请选择服务项目" });
          return
        }
        var userVIN = storage.get('serviceUserVIN') || ''
        if (!userVIN) {
          storage.set("serviceUserVIN", this.vin)
        } else {
          var tel = this.vin
          if (userVIN.indexOf(tel) === -1) {
            storage.set("serviceUserVIN", this.vin + ',' + userVIN)
          }
        }
        // const allFalse = this.upkeepList.every(item => item.checked == false)
        // if(allFalse) return
        // 赋值在校验之后
        this.selectList = selectArr
        this.$store.commit("saveSelectServiceShop", this.selectList);
        storage.set("carNo", this.submitParam.license);
        sessionStorage.setItem('typeChange',JSON.stringify(false))
        this.$router.push({
          path: "/aftersales/affirm-order",
          query: {
            ownerName: this.ownerName,
            ownerTel: this.ownerTel,
            // appointmentTime: this.appointmentTime,
            license: this.submitParam.license,
            vin: this.vin,
            mileage: this.mileage,
            appoId: this.appoId,
          },
        });
      },

      animation(ref) {
        if (ref === 'vin') {
          this.getBindCardInfo();
          this.showPopover = true
          return
        } else {
          this.showPopover = false
        }
        this.animations[ref] = true;
        this.$refs[ref].focus();
      },
      handlerFocus(prop) {
        setTimeout(() => {
          const pannel = document.getElementById(prop);

          // 让当前的元素滚动到浏览器窗口的可视区域内
          pannel.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          // 此方法是标准的scrollIntoView()方法的专有变体
          // pannel.scrollIntoViewIfNeeded();
        }, 300);

        if (!this.animations[prop]) {
          this.animations[prop] = true;
        }
      },
      handlerBlur(prop) {
        if (prop === "vin") {
          this.animations[prop] = !!this.vin;
        } else {
          this.animations[prop] = !!this.submitParam[prop];
        }
      },
      failed(err) {
        console.error("failed", err);
      },
    },
  };
</script>

<style scoped lang="less">
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/forms-cell.less");

  .serviceAppointmentTwo {
    padding-bottom: 200px !important;
    padding: 16px;
    padding-top: 0;
    .item-title-bold {
      line-height: 24px;
      font-size: 16px;
      color: #000;
      font-weight: normal;
      font-family: "Audi-WideBold";
      // padding-bottom: 16px;
      // border-bottom: 1px solid #f2f2f2;
    }

    .line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin: 16px 0;
      padding-bottom: 16px;
      border-bottom: 1px solid #e5e5e5;

      .title {
        font-size: 16px;
        color: #000;
      }

      .btn-change {
        // display: flex;
        // align-items: center;
        width: calc(100% - 84px);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .change-name {
           text-align: right;
            width: 100%;
          font-size: 16px;
          color: #000;
          margin-right: 10px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .btn-icon {
          align-items: center;
          font-size: 12px;
          color: #a3a3a3;
        }
      }
    }

    // /deep/.van-field__error-message {
    //   display: flex;
    //   flex-direction: row;
    //   align-items: center;
    //   height: 16px;

    //   &::before {
    //     content: "";
    //     display: inline-block;
    //     width: 14px;
    //     height: 14px;
    //     background: url("../../assets/img/error.png") no-repeat 0 0;
    //     background-size: 14px 14px;
    //     margin-right: 4px;
    //   }
    // }
  }

  .servershopmain {
    display: flex;
    align-items: center;
    // position: relative;
    padding-bottom: 8px;
    overflow: hidden;
    border-bottom: 1px solid #f2f2f2;
    margin-top: 16px;

    .server_shopsright {
      width: calc(100% - 23px);
    }

    .item_checkbox_button {
      vertical-align: middle;
      width: 28px;
      height: 28px;

      .item_checkbox_img {
        width: 18px;
        height: 18px;
      }
    }

    .server_shop {
      width: 100%;
      display: flex;

      .img_thumbnai {
        width: 54px;
        height: 54px;
        object-fit: cover;
      }

      .item-content {
        padding-left: 12px;
        width: calc(100% - 84px);
        flex-flow: column;
        justify-content: space-between;
        align-items: flex-start;

        .name_div {
          // width: 90%;
          font-size: 14px;
          color: #000000;
          font-family: "Audi-Normal";
          padding-top: 6px;
          // overflow: hidden;
          // white-space: nowrap;
          // text-overflow: ellipsis;
        }

        .config_div {
          font-size: 14px;
          padding-top: 6px;
          color: #000;
          font-family: "Audi-Normal";
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          // padding-right: 20px;
        }
      }
    }

    .item_next_button {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .img_next {
        width: 24px;
        height: 24px;
      }
    }
  }

  .bottom_style {
    width: 100%;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: fixed;
    bottom: 0px;
    // border-top: 2px #f2f2f2 solid;
    padding-top: 16px;
    padding-bottom: 32px;
    left: 0px;

    .checkbox_button {
      margin-left: 16px;
      margin-bottom: 5px;
      width: 18px;
      height: 18px;
      color: #000;
    }
    .checkbox_style {

      font-family: "Audi-Normal";
      color: #999999;
      font-size: 16px;
      margin-bottom: 16px;
      margin-right: 16px;
      display: flex;
      justify-content: right;
      flex-wrap: wrap;
      span {
        font-size: 16px;
        color: #000;
        font-family: "Audi-Normal";
      }

      .checkbox_styleRight {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #000;
      }
    }
  }

  .btn-delete-wrapper {
    margin: 0 16px;
  }

  .btn-delete-height {
    height: 80px;
  }

  .input-line {
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #000;

    .input-title {
      font-size: 12px;
      color: #666;
    }

    .input-change {
      padding-top: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .input-name {
        border: none;
        width: 100%;
        font-size: 16px;
        color: #000;
      }

      .input-icon {
        padding-left: 10px;
        align-items: center;
        color: #a3a3a3;
      }
    }
  }

  .align-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>

<style>
  .van-popover__action {
    width: auto;
  }
</style>


<style lang="less">
// .view-wrapper {
//   height: calc(100vh - 60px) !important;
// }
.order-forms-box {
  margin-bottom: 0px !important;

  .van-cell-group {
      border-top: 8px solid #f2f2f2;
      margin: 0 -16px;
      // padding-bottom: 24px;
      &:first-child {
        border-top-width: 0;
      }
      &.van-hairline--top-bottom {
        &::after {
          display: none;
        }
      }
      .van-hairline--bottom:after, .van-cell::after {
        border-color: #e5e5e5;
      }
      .van-field__label{
        margin-right: 0;
      }
      .cell-title {
        box-sizing: content-box;
        margin: 0 16px;
        padding: 16px 0;
        .h2 {
          margin: 0;
          font-size: 16px;
          line-height: 24px;
          font-family: AudiTypeGB-WideBold, AudiTypeGB;
          &.car-owner {
            margin-top: 26px;
          }
          &.mtb {
            margin-top: 8px;
          }
        }
        .sub {
          margin: 0;
          font-size: 10px;
          line-height: 20px;
          color: rgba(#000, .4);
        }
        .btn-change {
          .btn-icon {
            width: 20px;
            height: 20px;
          }
        }

      }
      .van-cell {
        padding: 20px 0 16px;
        min-height: 24px;
        font-size: 16px;
        &.van-field--error {
          padding-bottom: 36px;
          &::after {
            bottom: 20px;
          }
        }
        .van-cell__title {
          flex:none;
          //margin-right: 20px;
          color: #000;
        }
        .van-field__control {
          height: 24px;
          line-height: 24px;
          &:disabled {
            color: #000;
            -webkit-text-fill-color:#000
          }
        }
        .van-radio-group {
          .van-radio {
            margin-right: 24px;
          }
        }
        &:last-child::after {
          display: block;
        }
        &.van-field--error {
          .van-field__control {
            color: #333;
            &::placeholder {
              color: #ccc;
            }
          }
          .van-field__error-message {
            z-index: 19;
            position: absolute;
            bottom: -35px;
            margin-left: -85px;
            color: #EB0D3F;
            // font-size: 50px;
            display: flex;
            flex-direction: row;
            align-items: center;
            height: 16px;
            &::before {
              content: "";
              position: relative;
              display: inline-block;
              width: 14px;
              height: 14px;
              background: url("~@/assets/img/error-icon.png") no-repeat 0 0;
              background-size: 14px 14px;
              margin-right: 4px;
              top: 0;
            }
          }
          &::after {
            border-color: #EB0D3F;
          }
        }
        &.lan-cell-switch {
          .van-cell__value {
            overflow: visible;
          }
          .lan-switch {
            position: absolute;
            top: -3px;
            right: 0;
            .van-switch__node {
              width: .86em;
              height: .86em;
              margin: .07em;
            }
            &::before {
              content: '';
              position: absolute;
              right: 11px;
              top: 50%;
              width: 4px;
              height: 4px;
              border: solid 2px #8b8b8b;
              border-radius: 50%;
              transform: translateY(-50%);
            }
            &.van-switch--on {
              &::before {
                right: auto;
                left: 15px;
                width: 1px;
                height: 8px;
                border: none;
                border-radius: 0;
                border-left: solid 2px #fff;
              }
            }
          }
        }

      }
      .service-line {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 16px 0;
        padding-bottom: 16px;
      }
      #vinHistory{
        .van-popover{
          left: 100px !important;
          top: 105px !important;
          margin: 0px;
        }
      }
    }


}
</style>

