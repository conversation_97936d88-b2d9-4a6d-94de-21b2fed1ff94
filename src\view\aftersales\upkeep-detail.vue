<template>
  <div class="unsubscribeSucceed">
    <div
      class="item-wrapper"
      v-for="(item, idx) in imgList"
      :key="idx"
    >
      <img
        :src="item"
      >
    </div>
  </div>
</template>

<script>

import { callNative } from '@/utils'

export default {
  data() {
    return {
      title: '',
      imgList: []
    }
  },
  created() {
    this.getParams()
  },
  watch: {
    $route: 'getParams'
  },
  mounted() {
    this.title = this.$route.query.title
    this.$store.state.title = this.title
  },
  methods: {
    getParams() {
      this.imgList = this.$route.query.imgList
      console.log('this.imgList:', this.imgList)
    }
  }
}
</script>

<style scoped lang="less">
    @import url("../../assets/style/scroll.less");

    .unsubscribeSucceed {
        text-align: center;
      img {
          width: 100%;
          height: 100%;
        }

    }
</style>
