<template>
  <!-- 特选车 列表 -->
  <div class="wrapper">
    <div class="divider" />
    <div class="tabs">
      <div
        :class="customSeriesId === item.customSeriesId ? 'activecustomSeriesName' : 'customSeriesName'"
        v-for="(item,index) in customSeriesName"
        :key="index"
        @click="toswitch(item.customSeriesId)"
      >
        {{ item.customSeriesName }}
      </div>
    </div>
    <div
      class="modelline-wrapper"
      v-if="modelLineList && modelLineList.length"
      ref="lineModel"
    >
      <div
        class="model-line-list"
        v-for="(line, index) in modelLineList"
        :key="line.modelLineCode + index"
        @click.prevent="selectModel(line)"
      >
        <div :class="['info', `code-${line.customSeriesCode}`]">
          <div class="title">
            {{ formatmodelLineName(line.modelLineName).join('') }}
          </div>
          <div data-flex="main:justify">
            <div class="left">
              <div
                v-for="(item, ind) in line?.options?.slice(0, 3)"
                class="item"
                :key="ind"
              >
                <p class="text-one-hidd">{{ item.optionName }}</p>
              </div>
            </div>
            <div class="right" data-flex="main:center cross:center">
              <img
                :src="BaseConfigrationOssHost + line.imageUrl + '?x-oss-process=image/resize,h_512'"
                alt="车辆配置线图"
              >
            </div>
          </div>
          <div class="bot" data-flex="main:justify cross:center">
            <div class="price">
              &yen;{{ (line.price || '价格待定') | formatPrice }}起
            </div>

              <div
                class="detail"
                @click.stop="showModelDetail(line)"
                v-if="dealerCode"
              >
                查看详情 >
              </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="modelline-wrapper no-list"
      v-else
    >
      <img
        src="../../assets/img/no_list.png"
        alt=""
      >
      <div class="zanwu">
        暂无数据
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import wx from 'weixin-js-sdk'
import {
  bestRecommendCar, getBestRecommendConfig, bestRecommendCarAgent, getCarList
} from '@/configratorApi'
import {
  getOtds
} from '@/api/api'
import url from '@/config/url'
import {
  callNative, getUrlParamObj, paramsStrict, checkType, delay, getLocationProvince
} from '@/utils'
import { AUTOMOBILE } from '@/config/conf.data'

export default {
  name: 'SelectModel',
  inject: ['reload', 'checkLoginFn'],
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      modelLineList: [],
      dealerCode: '',
      from: 'false',
      customSeriesName: [],
      customSeriesId: ''
    }
  },
  created() {
    const query = this.$route.query
    if (query && query.dealerCode) {
      this.dealerCode = query.dealerCode
      this.customSeriesId = query.customSeriesId
    }
    if (query && query.modelLineList) {
      this.modelLineList = query.modelLineList
    } else {
      this.$store.commit('showLoading')
      this.getModelLine()
      this.$store.commit('hideLoading')
    }
    if (query && query.from) {
      this.from = query.from
    }
    console.log('%c11', 'font-size:40px;color:blue;', this.modelLineList)
    this.getCarList()
  },
  watch: {
    modelLineList(val) {
      setTimeout(() => {
        if (document.getElementById('pane-0')) {
          document.getElementById('pane-0').scrollTo({ top: val || 0 })
        }
      })
    }
  },
  activated() {
    console.log(
      '%cactivated',
      'font-size:40px;color:blue;',
      this.modelScrollTop,
      document.getElementById('pane-0')
    )
    document
      .getElementById('pane-0')
      ?.scrollTo({ top: this.modelScrollTop || 0 })
  },
  methods: {
    getCarList() {
      getCarList().then((res) => {
        const customSeries = res.data.data
        customSeries.forEach((item) => {
          item.customSeriesName = item.customSeriesName.replace('Audi ', '')
        })
        this.customSeriesName = customSeries.reverse()
      })
    },
    formatmodelLineName(val) {
      let index = val.length
      // if (this.idx === '1') {
      for (let i = 0, l = val.length; i < l; i++) {
        if (val.charCodeAt(i) > 255) {
          index = i
          break
        }
      }
      // }
      const str1 = val.substring(0, index)
      const str2 = val.substring(index)
      return [str1, str2]
    },
    toswitch(customSeriesId, name) {
      this.customSeriesId = customSeriesId
      this.getModelLine()
    },
    openWebViewMinip(e) {
      const { env } = getUrlParamObj()
      const {
        ccid, dealerCode, skuid, fromPage
      } = e
      const { origin, pathname } = window.location
      const url = encodeURIComponent(`${origin}${pathname}#/quotation?ccid=${ccid}`)
      env === 'minip' && wx.miniProgram.navigateTo({ url: `/pages/web/index?fromPage=${fromPage}&url=${url}&skuid=${skuid}&dealerCode=${dealerCode}` })
    },
    selectModel(line) {
      const seriesId = line.customSeriesCode
      const minip = this.$route.query.env == 'minip'
      this.$storage.setPlus('entryPoint', minip ? 'MINIP_AGENT_RECOMMEND' : 'ONEAPP_AGENT_RECOMMEND')
      const _this = this
      if ('DealerDetail'.includes(this.from)) {
        const entryPoint = this.$storage.getPlus('entryPoint')
        getBestRecommendConfig(line.bestRecommendId, entryPoint).then((res) => {
          getOtds({ useType: 1, seriesId }).then((res1) => {
            console.log(res1)
            const ccid = res.data.data.ccId
            const skuid = res1.data.data.prodSkuId
            if (ccid && skuid) {
              const { env } = getUrlParamObj()
              const pubVis = !_this.checkLoginFn('特选车') && env === 'minip'
              pubVis && _this.openWebViewMinip({
                ccid, dealerCode: _this.dealerCode, skuid, fromPage: 'spec'
              })
              !pubVis && _this.$router.push({
                path: '/quotation',
                query: {
                  fromPage: 'spec', ccid: ccid, skuid: skuid, dealerCode: _this.dealerCode
                }
              })
            }
          })
        })
      } else if (this.dealerCode) {
        this.$router.push({
          path: '/configration/financial-calculator',
          query: {
            showModel: line,
            fromselectmodel: true,
            dealerCode: this.dealerCode
          }
        })
      } else {
        this.$router.push({
          path: '/configration/financial-calculator',
          query: {
            showModel: line,
            fromselectmodel: true
          }
        })
      }
    },
    showModelDetail(line) {
      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: line.modelLineId,
          carModelName: line.modelLineName,
          seriesName: line.customSeriesCode,
          from: 1
        }
      })
    },
    async getModelLine() {
      if (this.dealerCode) {
        // 跳转路径 https://uataudi-embedded-wap.saic-audi.mobi/order/index.html#/configration/select-model?from=DealerDetail&dealerCode=76600019&customSeriesId=ca060a3b-6822-44e3-9956-4d33ae290018
        const p = {
          dealerCode: this.dealerCode,
          customSeriesId: this.customSeriesId
        }
        await bestRecommendCarAgent(p).then((res) => {
          console.log('获取特选车列表', res)
          this.modelLineList = res.data.data.map((e, i) => {
            // console.log(i , "--------e", e);
            const bestRecommendId = e.bestRecommendId
            const totalPrice = e.totalPrice
            const modelLine = e.modelLine
            modelLine.bestRecommendId = bestRecommendId
            modelLine.price = totalPrice
            const colors = e.options.filter((option) => option.category === 'COLOR_EXTERIEUR')
            const currentColor = colors[0]
            const hubs = e.options.filter((option) => option.category === 'RAD')
            let currentHub = {}
            if (hubs.length > 0) {
              currentHub = hubs[0]
            } else {
              currentHub = e.standardRad
            }
            // 去重
            // const modelLineId = e.modelLine.modelLineId
            // const sibInterieurId = e.modelLineSibInterieurVo.sibInterieurId
            // let optionId = e.options.filter((option) => option.category === 'COLOR_EXTERIEUR')
            // optionId = optionId[0].optionId

            // 价格排序
            const arr = []
            const arrs = []
            e.options.find((v) => {
              if (v.price > 0) {
                arr.push(v)
              }
            })
            if (arr.length >= 3) {
              modelLine.options = arr.sort((a, b) => b.price - a.price)
            } else {
              e.options.filter((option) => {
                if (option.category === 'RAD' || option.category === 'EIH' || option.category === 'COLOR_EXTERIEUR') {
                  arrs.push(option)
                  modelLine.options = arrs
                }
              })
            }
            const [{ seriesPathName }] = AUTOMOBILE.filter((a) => a.seriesCode === e.modelLine.customSeriesCode) || [{ seriesPathName: '' }]
            const pinyinSeriesCode = seriesPathName.toLowerCase()
            modelLine.imageUrl = `/ccpro-backend/${pinyinSeriesCode}/carImages/${modelLine.modelLineCode}/${currentColor.optionCode}/${currentHub.optionCode}/Front45.png` // `/ccpro-backend/storebest/${modelLine.modelLineCode}_${currentHub.optionCode}_${currentColor.optionCode}.png`
            // modelLine.onlyId = modelLineId + sibInterieurId + optionId
            return modelLine
          })
          console.log('modelLineList>>>>>>', this.modelLineList)
          // let obj = {}
          // let peon = this.modelLineList.reduce((cur,next)=>{
          //   console.log(next);
          //   obj[next.onlyId] ? '' : obj[next.onlyId] = true && cur.push(next)
          //   return cur
          // },[])
          // this.modelLineList = peon
          // console.log("去重后列表>>>>>>", this.modelLineList)
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.wrapper {
  height: 100%;
  // background: #F2F2F2;
  .divider {
    width: 100%;
    height: 1px;
background: #E5E5E5;
  }
  .tabs {
    height: 46px;
    background: #fff;
    padding: 0 23px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    // margin-bottom: 20px;
    .customSeriesName {
      font-family: AudiTypeGB-Normal, AudiTypeGB;
      font-weight: 400;
      color: #999999;
      font-size: 18px;
      padding-bottom: 10px;
    }
    .activecustomSeriesName {
       font-family: AudiTypeGB-WideBold, AudiTypeGB;
       font-weight: bold;
       color: #000000;
       border-bottom: 2px solid #000;
       padding-bottom: 10px;
    }
  }
  .zindex10 {
  position: relative;
  z-index: 10;
}
.modelline-wrapper {
  height: 100%;
background: #F2F2F2;
  padding: 16px;
  overflow-x: hidden;

  .line {
background: #FFFFFF;
    position: relative;
    // width: 100%;
    // height: 120px;
    min-height: 153px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
    margin-bottom: 16px;
    overflow: hidden;

    .bott {
      position: absolute;
      width: 100%;
      bottom: 12px;
    }

    padding: 14px;
    &.active {
      border: 1px solid #000000;
    }
    .name {
      font-size: 16px;
      font-family: 'Audi-WideBold';
    }
    .detail {
      font-size: 14px;
      display: inline-block;
      margin-right: 26px;
    }
    .price {
      font-family: 'Audi-WideBold';
    }
    .img {
      position: absolute;
      right: -15px;
      top: 10px;
      width: 230px;
      height: 130px;
      img {
        width: 100%;
        height: 100%;
        display: block;
      }
    }

    .info {
      & > div{
        width: calc(100% - 165px);
        margin-top: 8px;
        font-size: 12px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
.no-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .zanwu {
   font-size: 18px;
   font-weight: 400;
  color: #333333;
  line-height: 40px;
  }
  img {
    width: 100px;
  }
}
}

.model-line-list {
  margin-bottom: 16px;
  &:last-child {
    margin-bottom: 40px;
  }
  .info {
    background-color: #fff;
    padding: 12px;
    .title {
      font-size: 16px;
      line-height: 24px;
      color: rgba(0, 0, 0, .7);
      margin-bottom: 8px;
    }
    .right {
      width: 167px;
      height: 111px;
      object-fit: contain;
      position: relative;
      right: -12px;
      top: -25px;
      img {
        width: auto;
        height: 100%;
        position: relative;
        transform: scale(.98);
        top: 6px;
      }
    }
    &.code-49 {
      .right {
        img {
          left: -4px;
          top: 0;
          transform: scale(1.04);
        }
      }
    }
    &.code-G4 {
      .right {
        img {
          left: -4px;
          top: 0;
          transform: scale(.92);
        }
      }
    }
    .left {
      width: calc(100% - 178px);
      .item {
        font-size: 12px;
        line-height: 20px;
        margin-bottom: 2px;
        color: #999;
        &:last-child {
          margin: 0;
        }
        p {
          margin: 0;
        }
      }
    }
  }

  .bot {
    margin-top: -23px;
    color: #000;
    font-family: 'Audi-Normal';
    font-size: 12px;
    line-height: 20px;
    .price {
      font-size: 16px;
      font-family: 'Audi-WideBold';
      font-weight: 600;
    }
  }

}
</style>
