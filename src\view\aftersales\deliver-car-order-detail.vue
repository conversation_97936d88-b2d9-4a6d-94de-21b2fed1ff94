<template>
  <div class="container" v-if="orderInfo">
    <van-tabs
      v-if="orderInfo.orderType === 20131030"
      style="width: 100%;position: fixed; left: 0; z-index: 90"
      color="#000"
      line-width="60px"
      line-height="2"
      title-active-color="#000"
      @click="onTabClick"
    >
      <van-tab title="取车服务" />
      <van-tab title="送车服务" />
    </van-tabs>

     <div class="service-line" :style="{ 'padding-top':orderInfo.orderType === 20131030 ? '60px':'16px'}">
      <div class="title-bold-left">{{ setStatusDesc() }}</div>
      <div class="btn-change" @click="onStatusDesc">
        <span>查看进度<i v-if='isErrorStatus'></i></span>
        <img
          class="btn-icon"
          src="../../assets/img/icon-arrow.png"
        />
      </div>
    </div>


    <div class="c-font12" style="color: #333; font-size: 14px; margin-top: 8px">
      {{ tabType === 0 ? takeStatusText : sendStatusText }}
    </div>

    <div class="store-wrap" style="margin-top: 16px">
      <div class="item-store" style='border-bottom: none;'>
        <div>
          <img
            class="img-wrapper"
            :src="
            ((orderInfo.dealerUrl || '').includes('http')
              ? orderInfo.dealerUrl
              : ossUrl + orderInfo.dealerUrl) | audiwebp
          "
            alt=""
          />
        </div>
        <div class="content-wrapper flex1">
          <div class="c-font14 c-bold">
            {{ orderInfo.dealerName }}
          </div>
          <div style="margin-top: 4px" />
          <div class="c-font14" style="color: #666; font-size: 14px">
            {{ orderInfo.dealerAddress }}
          </div>
        </div>
      </div>
      <div class="link-wrapper">
        <div class="line" @click="callPhone(orderInfo.dealerPhone)">
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""
          >
          <span>联系</span>
        </div>
        <div @click="toNavigationMap(orderInfo.longitude, orderInfo.latitude, orderInfo.dealerName)">
          <img
            class="nav-icon"
            :src="require('@/assets/img/location.png')"
            alt=""
          >
          <span>导航</span>
        </div>
      </div>
    </div>
    <!-- 服务顾问-->
    <div class="item-store" v-if="tabType === 0 && orderInfo.takeConsultantName" >
      <div>
        <img
          style="border-radius: 50%;"
          class="img-wrapper"
          :src="orderInfo.takeConsultantUrl  ? orderInfo.takeConsultantUrl : require('@/assets/img/driving-url.png')"
          alt=""
        />
      </div>
      <div class="content-wrapper flex1">

        <div class="c-font14 c-bold" style="margin-top: 16px">
          {{ '服务顾问：'+orderInfo.takeConsultantName }}
        </div>
      </div>
      <div class="navgation-wrapper">
        <div>
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""
            @click="callPhone(orderInfo.takeConsultantPhone)"
          />
        </div>
      </div>
    </div>
    <!-- 服务顾问 送车-->
     <div class="item-store" v-if="tabType === 1 && orderInfo.sendConsultantName" >
      <div>
        <img
          style="border-radius: 50%;"
          class="img-wrapper"
          :src="orderInfo.sendConsultantUrl ? orderInfo.sendConsultantUrl : require('@/assets/img/driving-url.png')"
          alt=""
        />
      </div>
      <div class="content-wrapper flex1">

        <div class="c-font14 c-bold" style="margin-top: 16px">
          {{ '服务顾问：'+orderInfo.sendConsultantName }}
        </div>
      </div>
      <div class="navgation-wrapper">
        <div>
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""
            @click="callPhone(orderInfo.sendConsultantPhone)"
          />
        </div>
      </div>
    </div>

    <!-- e代价-->
     <div class="item-store" v-if="tabType === 0 && orderInfo.takeDriverName !== null" >
      <div>
        <img
          style="border-radius: 50%;"
          class="img-wrapper"
          :src="orderInfo.takeDrivingPicUrl ? orderInfo.takeDrivingPicUrl : require('@/assets/img/consultant-url.png')"
          alt=""
        />
      </div>
      <div class="content-wrapper flex1">

        <div class="c-font14 c-bold" style="margin-top: 16px">
          {{ '代驾司机：'+orderInfo.takeDriverName }}
        </div>
      </div>
      <div class="navgation-wrapper">
        <div>
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""
            @click="callPhone(orderInfo.takeDriverPhone)"
          />
        </div>
      </div>
    </div>
    <!-- e代价 送车-->
     <div class="item-store" v-if="tabType === 1 && orderInfo.sendDriverName !== null" >
      <div>
        <img
          style="border-radius: 50%;"
          class="img-wrapper"
          :src="orderInfo.sendDrivingPicUrl ? orderInfo.sendDrivingPicUrl : require('@/assets/img/consultant-url.png')"
          alt=""
        />
      </div>
      <div class="content-wrapper flex1">

        <div class="c-font14 c-bold" style="margin-top: 16px">
          {{ '代驾司机：'+orderInfo.sendDriverName }}
        </div>
      </div>
      <div class="navgation-wrapper">
        <div>
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""
            @click="callPhone(orderInfo.sendDriverPhone)"
          />
        </div>
      </div>
    </div>


    <div class="title-bold" style="margin-top: 16px">{{tabType === 0 ? '取车时间':'送车时间'}}</div>
    <div class="item-wrapper" style="margin-top: 12px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        {{ tabType === 0 ? orderInfo.takeDatetime : orderInfo.sendDatetime }}
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px" />
    </div>


    <div v-if="tabType === 0 && orderInfo.takeCouponName">
      <div class="linediv" />
       <div class="title" style="margin-top: 16px">优惠券</div>
        <div class="item-wrapper" style="margin-top: 12px">
          <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
            {{ orderInfo.takeCouponName }}
          </div>
          <div
            class="navgation-wrapper"
            style="color: #000; font-size: 12px"
          >-1
          </div>

        </div>
    </div>

    <div v-if="tabType === 1 && orderInfo.sendCouponName">
      <div class="linediv" />
       <div class="title" style="margin-top: 16px">优惠券</div>
        <div class="item-wrapper" style="margin-top: 12px">
          <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
            {{ orderInfo.sendCouponName }}
          </div>
          <div
            class="navgation-wrapper"
            style="color: #000; font-size: 12px"
          >-1
          </div>

        </div>
    </div>

    <div v-if="tabType === 1 && orderInfo.sendReceiveCode !== null">
      <div class="linediv" />
       <div class="item-wrapper" style="margin-top: 12px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 16px">
        收车验证码
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 14px">
        {{ tabType === 0 ? orderInfo.takeReceiveCode : orderInfo.sendReceiveCode }}
      </div>
    </div>

    </div>

    <div style="color: #333; font-size: 12px;margin-top: 16px" v-if="tabType === 1 && orderInfo.sendReceiveCode !== null">代驾司机到达前请勿提供</div>

     <div v-if="tabType === 1 &&  orderInfo.sendIsCancel === 0 && orderInfo.sendCancelReason">
      <div class="linediv" />
      <div class="title" style="margin-top: 16px">取消订单原因</div>
      <div class="item-wrapper" style="margin-top: 12px">
        <div class="content-wrapper flex1" style="color: #333; font-size: 13px">
          {{ orderInfo.sendCancelReason }}
        </div>
      </div>
    </div>
 <div v-if="tabType === 0 && orderInfo.takeIsCancel === 0 && orderInfo.takeCancelReason">
      <div class="linediv" />
      <div class="title" style="margin-top: 16px">取消订单原因</div>
      <div class="item-wrapper" style="margin-top: 12px">
        <div class="content-wrapper flex1" style="color: #333; font-size: 13px">
          {{ orderInfo.takeCancelReason }}
        </div>
      </div>
    </div>
    <div class="linediv" />

    <div class="title-bold" style="margin-top: 16px">订单信息</div>

    <div class="item-wrapper" style="margin-top: 12px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        订单类型
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{ tabType === 0 ? "取车服务" : "送车服务" }}
      </div>
    </div>

    <div class="item-wrapper" style="margin-top: 4px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        下单渠道
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{ CHANNELS[orderInfo.channelId]}}
      </div>
    </div>


    <div class="item-wrapper" style="margin-top: 4px">
      <div style="color: #333; font-size: 12px;display: flex; flex-wrap: wrap; width:100px">
         {{ tabType === 0 ? "取车地址" : "送车地址" }}
      </div>
      <div  style="color: #000; font-size: 12px">
        {{ tabType === 0 ? orderInfo.takeAddress : orderInfo.sendAddress }}
      </div>
    </div>


    <div style="margin-top: 4px" class="item-wrapper" v-if="orderInfo.sendAddressSupplement && tabType === 1 ">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
         {{  "送车详细地址" }}
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{orderInfo.sendAddressSupplement}}
      </div>
    </div>
    <div style="margin-top: 4px" class="item-wrapper" v-if="tabType === 0 && orderInfo.takeAddressSupplement">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
         {{ "取车详细地址"}}
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{ orderInfo.takeAddressSupplement}}
      </div>
    </div>
     <div  class="item-wrapper" style="font-size: 14px; padding-top: 4px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        需求订单号
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{ orderInfo.appoId }}
      </div>
    </div>

    <div class="item-wrapper" style="font-size: 14px; padding-top: 4px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        下单时间
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{ orderInfo.createdAt }}
      </div>
    </div>
    <div
      v-if=" tabType === 0 && orderInfo.takeIsCancel === 0 && orderInfo.takeCancelDatetime"
      class="item-wrapper"
      style="font-size: 14px; padding-top: 0px"
    >
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        服务取消时间
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{  orderInfo.takeCancelDatetime  }}
      </div>
    </div>
     <div
      v-if=" tabType === 0 && orderInfo.takeIsCancel === 0 && orderInfo.takeCancelName"
      class="item-wrapper"
      style="font-size: 14px; padding-top: 0px"
    >
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        服务取消人
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{  orderInfo.takeCancelName  }}
      </div>
    </div>


     <div
      v-if=" tabType === 1 &&  orderInfo.sendIsCancel === 0 && orderInfo.sendCancelDatetime"
      class="item-wrapper"
      style="font-size: 14px; padding-top: 0px"
    >
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        服务取消时间
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{  orderInfo.sendCancelDatetime  }}
      </div>
    </div>
    <div
      v-if=" tabType === 1 &&  orderInfo.sendIsCancel === 0 && orderInfo.sendCancelName"
      class="item-wrapper"
      style="font-size: 14px; padding-top: 0px"
    >
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        服务取消人
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{  orderInfo.sendCancelName  }}
      </div>
    </div>


    <div class="linediv" />
    <div class="title-bold" style="margin-top: 8px">车辆信息</div>

    <div class="item-wrapper" style="margin-top: 12px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        车型
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{ orderInfo.modelName }}
      </div>
    </div>
    <div class="item-wrapper" style="font-size: 14px; padding-top: 0px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        车牌号
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{ orderInfo.carNo }}
      </div>
    </div>
    <div class="linediv" />
    <div class="title-bold" style="margin-top: 8px">个人信息</div>

    <div class="item-wrapper" style="margin-top: 12px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        姓名
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{
          tabType === 0 ? orderInfo.takeConnectName : orderInfo.sendConnectName
        }}
      </div>
    </div>
    <div class="item-wrapper" style="font-size: 14px; padding-top: 0px">
      <div class="content-wrapper flex1" style="color: #333; font-size: 12px">
        手机号
      </div>
      <div class="navgation-wrapper" style="color: #000; font-size: 12px">
        {{
          tabType === 0
            ? orderInfo.takeConnectPhone
            : orderInfo.sendConnectPhone
       | phoneNum }}
      </div>
    </div>
    <div class="linediv" />

    <div class="btn-delete-height" />
    <div class="bottom_style" v-if="isShowCancel()">
      <div class="btn-delete-wrapper">
        <AudiButton
          @click="onCancelOrder"
          :text="'取消订单'"
          color="white"
          font-size="16px"
          height="50px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { Tab, Tabs } from "vant";
import { getDeliverCarOrderDetail, getUnreadDeliverCarOrderStatusCount } from '@/api/api'
import baseUrl from "@/config/url";
import { callNative } from "@/utils";
import AudiButton from "@/components/audi-button";

Vue.use(Tab).use(Tabs);

export default {
  components: {
    AudiButton,
  },
  data() {
    return {

      ossUrl: baseUrl.BaseOssHost,
      orderInfo: {},
      takeStatusText: "待服务商确认，如有疑问可联系服务商。",
      sendStatusText: "待服务商确认，如有疑问可联系服务商。",

      tabType: 0,

      isErrorStatus: false,

      CHANNELS: {
        "1": "奥迪官网",
        "2": "APP",
        "4": "黑金会员助理"
      }
    };
  },

  async mounted() {
    const { appoId } = this.$route.query;

    this.getDeliverCarOrderDetail(appoId);
  },

  methods: {
    toNavigationMap(longitude, latitude, dealerName) {
      callNative("navigationMap", {
        lat: latitude.toString(),
        long: longitude.toString(),
        des: dealerName,
      });
    },
    async getDeliverCarOrderDetail(appoId) {
      const { data } = await getDeliverCarOrderDetail({
        appoId: appoId,
        type: 2,
      });

      if (data.code === "200") {
        if (data.data.orderType === 20131010) {
          this.$store.state.title = "取车服务";
          this.tabType = 0;
          if (data.data.takeIsCancel === 0) {
            this.takeStatusText = "如有疑问请联系服务商。";
          }
        } else if (data.data.orderType === 20131020) {
          this.$store.state.title = "送车服务";
          this.tabType = 1;
          if (data.data.sendIsCancel === 0) {
            this.sendStatusText = "如有疑问请联系服务商。";
          }
        } else {
          this.orderType = 0; //默认是0
          this.$store.state.title = "取送车服务";
          if (data.data.takeIsCancel === 0) {
            this.takeStatusText = "如有疑问请联系服务商。";
          }
          if (data.data.sendIsCancel === 0) {
            this.sendStatusText = "如有疑问请联系服务商。";
          }
        }
        this.orderInfo = data.data;
        this.getErrorStatus(data.data)
      }
    },

    async getErrorStatus(orderInfo) {
      let appoId = "";
      if ( this.tabType === 0) {
        //取车
        appoId = orderInfo.takeOrderId;
      } else if (this.tabType === 1) {
        //送车
        appoId = orderInfo.sendOrderId;
      }
      const { data } = await getUnreadDeliverCarOrderStatusCount({
        orderId: appoId,
      });
      this.isErrorStatus = (data.data) ? true : false
    },

    // 订单状态
    setStatusDesc() {
      if (this.tabType === 0) {
        return this.orderInfo.takeOrderStatusStr;
      } else {
        return this.orderInfo.sendOrderStatusStr;
      }
    },

    // 订单类型(20131010:取车;20131020:送车;20131030:取送车;)
    setServiceTypeDesc(orderType) {
      if (orderType === 20131010) {
        return "取车服务";
      }
      if (orderType === 20131020) {
        return "送车服务";
      }
    },
    onTabClick(code) {
      this.tabType = code;
    },
    //是否显示取消按钮
    isShowCancel() {
      if (this.tabType === 0) {
        return this.orderInfo.takeIsCancel === 1;
      } else {
        return this.orderInfo.sendIsCancel === 1;
      }
    },
    callPhone(phone) {
      window.location.href = `tel:${phone}`;
    },
    // 取消订单
    async onCancelOrder() {
    this.getCancelOrder();
    },
    async getCancelOrder() {
      let appoId = "";
      let type = 0
      if (this.orderInfo.orderType === 20131010 || this.tabType === 0) {
        //取车
        appoId = this.orderInfo.takeOrderId;
        type = 1
      } else if (this.orderInfo.orderType === 20131020 || this.tabType === 1) {
        //送车
        appoId = this.orderInfo.sendOrderId;
        type  = 3
      }

       this.$router.push({
          path: "/aftersales/cancel-service-order",
          query: {
            orderId: appoId,
            type:type,
            isShowDialog:true
          },
        });
      // if(data.code === '200'){
      //   this.getDeliverCarOrderDetail(this.orderInfo.appoId);
      // }else{
      //   callNative("toast", { type: "fail", message: data.message });
      // }
    },
    onStatusDesc(){
      let appoId = "";
      if ( this.tabType === 0) {
        //取车
        appoId = this.orderInfo.takeOrderId;
        this.isErrorStatus = false
        this.$router.push({
          path: "/aftersales/order-status",
          query: {orderId:appoId},
        });
      } else if (this.tabType === 1) {
        //送车
        appoId = this.orderInfo.sendOrderId;
        this.isErrorStatus = false
        this.$router.push({
          path: "/aftersales/order-status",
          query: {orderId:appoId},
        });
      }
      // const { origin, pathname } = window.location;
      // const url = `${origin}${pathname}#/aftersales/order-status?orderId=${appoId}`;
      // callNative("audiOpen", { path: url });
    }
  },
};
</script>

<style lang="less" scoped>
@import url("../../assets/style/scroll.less");

@import "../../assets/style/common.less";
@import url("../../assets/style/buttons.less");

.container {
  // overflow: hidden;
  // height: 100%;
  padding-left: 16px !important;
  padding-right: 16px;
  background: #fff;
}
.linediv {
  margin-top: 16px;
  margin-bottom: 16px;
  width: 100%;
  height: 1px;
  background-color: #e5e5e5;
}
.flex1 {
  flex: 1;
}
.title-bold {
  font-size: 16px;
  color: #000;
  font-weight: normal;
  font-family: "Audi-WideBold";
  margin-top: 59px;
}
.title-under-line {
  font-size: 16px;
  color: #000;
  font-weight: normal;
  text-decoration: underline;
  font-family: "Audi-Normal";
}

.item-store {
  .c-flex-between;
  border-bottom: 1px solid #e5e5e5;
  // border-top: 1px solid #e5e5e5;
  padding: 16px 0;
}
.store-wrap{
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
}
.item-wrapper {
  .c-flex-between;
}

.phone-icon {
  width: 12px;
  vertical-align: baseline;
}

.img-wrapper {
  width: 66px;
  height: 66px;
  margin-right: 16px;
  display: flex;
}
.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  padding: 5px 0;
}
.navgation-wrapper {
  .c-font14;
  .c-flex-center;
  color: #b2b2b2;
  text-align: center;
  .nav-icon {
    width: 24px;
    height: 24px;
    margin: 0 auto 5px auto;
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
}
.btn-delete-wrapper {
  margin: 0 16px;
}

.btn-delete-height {
  height: 90px;
}
.service-line {
  padding-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title-bold {
    font-size: 16px;
    color: #000;
    font-weight: normal;
    font-family: "Audi-WideBold";
  }
  .title-bold-left{
    font-size: 16px;
    color: #000;
    font-weight: normal;
    font-family: "Audi-WideBold";
  }
  .btn-change {
    display: flex;align-items: center;
    span{
      display: flex;align-items: center;
      font-size: 12px;color: #999;
      i{
        display: block;
        width: 4px;height: 4px;border-radius: 50%;
        background-color: #fd0034;margin: 0 0 0 4px;
      }
    }
    .btn-icon {
      width: 20px;
      height: 20px;
    }
  }
}
.link-wrapper{
  display: flex;
  align-items: center;
  padding: 8px 0 16px;
  >div{
    flex:1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    img{
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
  }
  .line{
    border-right: 1px solid #E5E5E5;
  }
}
</style>
