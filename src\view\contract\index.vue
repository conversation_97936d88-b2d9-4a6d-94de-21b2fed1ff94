<template>
  <div class="container">
    <div class="img-wrapper">
      <img
        v-for="item in imgUrlArr"
        :key="item"
        :src="item"
        alt="合同加载失败"
      />
    </div>

    <div
      class="btn-wrapper"
      v-if="showSignButton && orderType != '07' && boolPurchaseSignable"
    >
      <AudiButton
        text="去签署"
        color="black"
        height="56px"
        font-size="16px"
        @click="clickSignCaontract"
      />
    </div>

    <div class="btn-wrapper" v-if="showDownloadButton || showUploadButton">
      <AudiButton
        v-if="showDownloadButton"
        text="下载合同"
        color="black"
        height="56px"
        font-size="16px"
        @click="downloadContract"
      />
      <AudiButton
        v-if="showUploadButton && orderType != '07'"
        text="上传合同"
        color="black"
        height="56px"
        font-size="16px"
        @click="toUploadPage"
      />
    </div>
    <div class="btn-wrapper" v-if="isSignedContract">
      <div class="popup-custom-btn" data-flex="main:justify">
        <!-- <div class="lan-button-box">
          <audi-button
            height="54px"
            text="发送已签署合同"
            color="black"
            :class="[`black-btn`, 'btn-enabled']"
            @click="popSendContract = true"
          />
        </div> -->
        <div class="line-two-cols lan-button-box">
          <audi-button
            height="54px"
            text="下载已签署合同"
            @click="handleDownloadContractFile"
          />
        </div>
        <div class="line-two-cols lan-button-box">
          <audi-button
            height="54px"
            text="发送已签署合同"
            color="black"
            :class="[`black-btn`, 'btn-enabled']"
            @click="popSendContract = true"
          />
        </div>
      </div>
    </div>
    <van-popup
      v-model="popSendContract"
      class="popup-custom"
      :close-on-click-overlay="false"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="h3 align-center">发送至邮箱</div>
          <div class="text align-left">
            提交成功后，系统会将您此次已完成签署的购车合同发送至您的邮箱中
          </div>
          <div class="email-box">
            <input
              class="email"
              type="text"
              name="email"
              placeholder="请输入您的邮箱"
              v-model="userEmail"
            />
          </div>
        </div>
        <div class="popup-custom-btn" data-flex="main:justify">
          <div class="line-two-cols lan-button-box">
            <audi-button
              height="54px"
              text="取消"
              @click="popSendContract = false"
            />
          </div>
          <div class="line-two-cols lan-button-box">
            <audi-button
              height="54px"
              text="提交"
              color="black"
              class="black-btn btn-enabled"
              @click="handleSendingContract"
            />
          </div>
        </div>
      </div>
    </van-popup>
    <van-popup
      v-model="deliveryDialog"
      class="popup-custom"
      :close-on-click-overlay="false"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="text align-center">
            {{ errorsMessage || '优惠券核销失败' }}
          </div>
        </div>
        <div class="popup-custom-btn">
          <AudiButton
            text="我知道了"
            color="black"
            height="56px"
            @click="deliveryDialog = false"
          />
        </div>
      </div>
    </van-popup>
    <!-- 签署合同前确认弹窗 -->
    <van-dialog
      class="dialog lan-dialog-custom line-two-cols lan-dialog-anti-human line-row-reverse lan-dialog-swap-skin"
      v-model="isAffirmSignContract"
      :close-on-click-overlay="true"
      cancel-button-text="取消"
      confirm-button-text="已完成确认"
      show-cancel-button
      message="开启合同签署后，将锁定您本次购车总价与订单相关权益。请与您的奥迪管家确认订单信息后，开启签署。"
      @confirm="handleSignContract"
      @cancel="isAffirmSignContract = false"
    />
    <van-dialog
      class="dialog lan-dialog-custom"
      v-model="isAuthFaildDialog"
      :close-on-click-overlay="true"
      :message="isAuthFaildMessage"
      confirm-button-text="我已知晓"
      @confirm="confirmIsAuthFaildDialog"
    />
    <van-dialog
      class="dialog lan-dialog-custom lan-dialog-enter"
      v-model="isAuthEnterpriseFaildDialog"
      :close-on-click-overlay="true"
      confirm-button-text="进入企业实名认证"
      @confirm="jumpEnterpriseFaild"
    >
      <div class="content">
        <p>请确认企业信息填写正确：</p>
        <p>1. 所填写企业代码已通过实名认证</p>
        <p>2. 所填写企业名称与实名名称一致</p>
        <p>3. 所填写购车人为该企业认证的经办人</p>
      </div>
    </van-dialog>
    <network @reload="networkReload()" />
  </div>
</template>

<script>
import { Toast, Popup } from 'vant'
import Vue from 'vue'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import wx from 'weixin-js-sdk'
import {
  getContractInfo,
  verifyFadada,
  loginFadada,
  enterpriseVerifyFadada,
  enterpriseLoginFadada,
  signContract,
  createOfflineContract,
  getOfflineContractImage,
  getSignedContractImage,
  getMyOrders,
  getContractPreviewImgs,
  getContractState,
  getContractImageCount,
  setEnterpriseChannelSign,
  sendingEMailContract,
  getCarOrderInfoH5,
  downloadContractFile,
  getContractFileType,
  getAPITimeOutTesting,
  getNgaDetailsOrder
} from '@/api/api'
import AudiButton from '@/components/audi-button'
import {
  Obj2UrlParam,
  callNative,
  handleTimeout,
  isIos,
  delay,
  downloadFile
} from '@/utils'
import store from '@/store'
import api from '@/config/url'

import URL from '@/api/url'

import network from '@/components/network.vue'

const baseUrl = api.BaseApiUrl

Vue.use(Toast).use(Popup)
// 大定的code
const BIZCODE = '000003'

export default {
  components: { AudiButton, network },
  data() {
    return {
      contractInfo: {},
      imgUrlArr: [],
      showDownloadButton: false,
      showSignButton: false,
      showUploadButton: false,
      deliveryDialog: false,
      errorsMessage: '',
      isAuthFaild: false,
      isAuthFaildDialog: false,
      isAuthFaildMessage: '操作错误',
      isAuthEnterpriseFaildDialog: false,
      contractUserInfo: {},
      carBuyerInfo: {},
      isSignedContract: false,
      popSendContract: false,
      userEmail: '',
      contractType: 0,
      isLogged: false,
      verifyRedirectUrl: '',
      enterpriseRedirectUrl: '',
      isEnterpriseVerify: false,
      isAffirmSignContract: false,
      isAgreementSignContract: false,
      isSelectOnline: -1,
      isClickDownloadContract: -1,
      lastSelectOnline: -1,
      orderType: '',
      boolPurchaseSignable: false,
      isAllSignedContract: false,
      orderh5BaseUrl: ''
    }
  },
  created() {
    const { orderId } = this.$route.query
    this.orderType = this.$route.query?.orderType || ''
    if (!orderId) {
      console.error(`contract-info page, orderId:${orderId}`)
    }
  },

  mounted() {
    console.log('合同mounted')
    this.toggleHeaderVisible(false)
    this.getOrderH5Base()
    this.initData()
  },

  methods: {
    ...mapGetters(['getDevice']),
    // 是否签署成功
    _isSignedContract(contractInfo) {
      // purchaseContractStatus 1.线下 2. 线上
      if (this.orderType == '07') {
        return (
          contractInfo?.purchaseContractStatus === '2' ||
          contractInfo?.purchaseContractStatus === '1'
        )
      }
      return contractInfo?.purchaseContractStatus === '2'
    },
    async getOrderH5Base() {
      const orderInfoH5 = await getCarOrderInfoH5()
      this.orderh5BaseUrl = orderInfoH5.data.data.configValue
    },
    async initData() {
      /**
       * carBuyerInfo.buyType:
       * 01 个人订单: 个人订单在验证信息不通过的时候执行线下流程
       * 02 企业订单:只能执行线下签署流程:
       * 线下流程: 显示下载/上传按钮
       */
      this.$store.commit('showLoading')
      getAPITimeOutTesting()
      Toast.clear()
      const {
        orderId,
        turnOffLine,
        isSelectOnline,
        isClickDownloadContract,
        lastSelectOnline,
        ccid,
        already,
        orderType
      } = this.$route.query
      const { data } = await getMyOrders({ orderId })
      this.isAllSignedContract =
        data.data.contractInfo?.purchaseContractStatus === '2'
      this.boolPurchaseSignable = data.data.extInfo.boolPurchaseSignable
      this.contractInfo = data.data.contractInfo
      this.carBuyerInfo = data.data.carBuyerInfo
      // 显示预览的图片
      this.previewImg(data.data.contractInfo)

      // 如果签署过就不执行验签的流程
      if (this._isSignedContract(data.data.contractInfo)) {
        this.$store.state.title = '汽车购买协议'
        document.title = '汽车购买协议'
        this.checkFadadaVerified(orderId, 0)
        this.isSignedContract = true
        const { purchaseContractId } = data.data.contractInfo
        const contract = await getContractFileType({
          contractId: purchaseContractId
        })
        if (contract?.data?.data && contract?.data?.code === '200') {
          this.contractType = contract.data.data
        }
        console.log(
          '%c [ 已签署合同 ]-154',
          'font-size:14px; background:#cf222e; color:#fff;',
          this.contractType
        )
        this.$store.commit('hideLoading')
        return
      }
      this.$store.state.title = '汽车购买协议模板'
      document.title = '汽车购买协议模板'

      // data.contractInfo.purchaseContractBoolOnline 为 false 走线下合同
      // 貌似线下合同，先注掉
      // if (data.data.contractInfo?.purchaseContractBoolOnline === false || +turnOffLine === 1) {
      //   this.checkFadadaVerified(orderId, 0)
      //   this.doOfflineContract(data.data.carBuyerInfo.buyType)
      //   return
      // }

      // 公户线上签
      if (data.data.carBuyerInfo?.buyType === '02') {
        //查询用户是否认证成功
        let { data } = await verifyFadada({ orderId })
        this.isLogged = data.data.verified
        if (!data.data.verified) {
          this.loginFadada()
          this.showSignButton = true
          this.$store.commit('hideLoading')
          return
        }

        // 用户认证成功，查询企业实名认证
        let { data: enterpriseData } = await enterpriseVerifyFadada({ orderId })
        this.isEnterpriseVerify = enterpriseData.data.enterpriseVerified

        if (!enterpriseData.data.enterpriseVerified) {
          await this.enterpriseLoginFadadaFun()
        }
        this.showSignButton = true
        this.$store.commit('hideLoading')
        return
      }

      // 个人合同
      if (data.data.carBuyerInfo?.buyType === '01') {
        // 未签署则执行线上验签流程
        const { data } = await verifyFadada({ orderId })

        if (typeof data.data?.verified === 'undefined') {
          return console.error('contract page/ verifyFadada 验证合同错误')
        }

        console.log(
          '%c [ 是否验证过(可能是信息不符合或者未验证登陆过) ]-289',
          'font-size:14px; background:#cf222e; color:#fff;',
          data.data.verified
        )
        // 是否验证过 (可能是信息不符合或者未验证登陆过)
        // if (data.data.verified) {
        //   this.showSignButton = true
        // } else {
        //   this.loginFadada()
        // }
        if (!data.data.verified) {
          if (this.checkFadadaVerified(orderId) && !+already) {
            return this.$router.push({
              name: 'contract-guide',
              query: {
                orderId,
                ccid,
                orderType
              }
            })
          }
          this.loginFadada()
        } else {
          this.checkFadadaVerified(orderId, 0)
        }

        this.$store.commit('hideLoading')
        this.showSignButton = true
        this.isLogged = data.data.verified
      }
    },
    checkFadadaVerified(orderId, t = 1) {
      const fadadaStorageVerified = localStorage.getItem(`__AOID__${orderId}`)
      if (t === 1) {
        return fadadaStorageVerified
      }
      fadadaStorageVerified && localStorage.removeItem(`__AOID__${orderId}`)
    },
    checkEnterpriseVerifyFadada(orderId, t = 1) {
      const fadadaStorageVerified = localStorage.getItem(`__AOID__${orderId}`)
      if (t === 1) {
        return fadadaStorageVerified
      }
      fadadaStorageVerified && localStorage.removeItem(`__AOID__${orderId}`)
    },
    // 对ios做特殊处理
    routeToSignContract(path) {
      // if (isIos) {
      //   try {
      //     window.webViewJavascriptBridge.callHandler('clearCache', {}, (err, data) => {
      //       setTimeout(() => {
      //         window.location.href = path
      //       }, 1000)
      //     })
      //   } catch (err) {
      //     window.location.href = path
      //   }
      // } else {
      //   window.location.href = path
      // }
      window.location.href = path
    },

    // 执行线下签署流程(下载合同)
    async doOfflineContract(buyType) {
      this.showDownloadButton = true
      const { isClickDownloadContract } = this.$route.query
      if (+isClickDownloadContract === 0) return
      // download
      if (this.contractInfo?.purchaseContractId) {
        const contractId = this.contractInfo?.purchaseContractId
        const { data } = await getContractState({
          contractId
        })

        // 0: 未上传, 1:自己上传过, 2: 运营侧上传过 3: 合同已完成
        if (data.data.status === 0 || data.data.status === 1) {
          this.showUploadButton = true
        }
      }
    },

    // 获取显示预览图片
    async previewImg(contractInfo) {
      if (!contractInfo) {
        console.warn(`perview img function contractInfo: ${contractInfo}`)
      }
      /**
       * 订单是否签署,
       * 如果签署就使用contractId 获取签署后的预览合同图片展示
       * 是否已经签订合同 purchaseContractStatus: 0:未签署, 1: 用户(线下)已签署 2:线上合同已签署, 10: 客户已签署
       * 目前只判断 1 或 2
       */
      console.log(
        this._isSignedContract(contractInfo),
        'this._isSignedContract(contractInfo'
      )
      if (this._isSignedContract(contractInfo)) {
        const contractId = contractInfo?.purchaseContractId
        // 第一次进入 contractId 为null
        if (contractId) {
          // 通用版的合同数量都是6， 后面需要分开A7L是4页，Q5E是6页
          const { data } = await getContractImageCount({ contractId }) // 个人合同和企业合同的长度是一样
          const count = data.data
          const idxArr = Array.from({ length: count }, (v, k) => k)
          const paramArr = idxArr.map((i, idx) =>
            Obj2UrlParam({ contractId, pageNo: idx + 1 })
          )

          if (contractInfo?.purchaseContractBoolOnline) {
            // 线上合同预览
            this.imgUrlArr = paramArr.map((i) =>
              getSignedContractImage(`?${i}`)
            )
          } else {
            // 线下合同预览
            this.imgUrlArr = paramArr.map((i) =>
              getOfflineContractImage(`?${i}`)
            )
          }
        }
      } else {
        // 获取签署前的合同预览图片, 分为个人或企业
        const { orderId } = this.$route.query
        const { data } = await getContractPreviewImgs({ orderId })
        const imgs = data.data
        if (imgs.length === 0) {
          console.error(`合同图片数量为 :${imgs.length}`)
        }
        this.imgUrlArr = imgs.map((img) => this.$loadWebpImage(img))
      }
    },

    // 个人去验证登录
    async loginFadada() {
      const { orderId } = this.$route.query
      const { data } = await loginFadada({
        orderId,
        redirectUrl: window.location.href
      })
      if (data.code === '201') {
        // 如果给定的订单号查询的信息, 身份证号和姓名身份不一致,则会返回201
        // 此时执行线下合同下载的流程
        // this.doOfflineContract()
        // 原有逻辑用户验证失败后默认转线下，当前需要用户手动操作(20221223)
        this.showSignButton = true
        this.isAuthFaild = true
        this.contractUserInfo = data.data?.lastVerified
      }

      if (data.data?.verifyRedirectUrl) {
        this.verifyRedirectUrl = data.data.verifyRedirectUrl
        // 身份验证通过跳转到法大大签合同界面去登录
        // window.location.href = data.data.verifyRedirectUrl
        // this.toggleHeaderVisible(true)
      }
    },
    // 企业登录验证
    async enterpriseLoginFadadaFun() {
      const { orderId, env } = this.$route.query
      //企业认证法大大返回平台重定向到整车订单页
      let redirectUrl = ''
      const token = localStorage.getItem('token') || ''
      const refreshToken = localStorage.getItem('refreshToken')
      if (env == 'minip') {
        redirectUrl = `${this.orderh5BaseUrl}order/new-money-detail?orderId=${orderId}&env=${env}&token=${token}&refreshToken=${refreshToken}&fromPage=successStatus&type=1`
      } else {
        redirectUrl = `${this.orderh5BaseUrl}order/new-money-detail?orderId=${orderId}&fromPage=successStatus&type=1`
      }
      const { data } = await enterpriseLoginFadada({
        orderId,
        redirectUrl
      })

      if (data.data?.verifyRedirectUrl) {
        this.enterpriseRedirectUrl = data.data.verifyRedirectUrl
        // 身份验证通过跳转到法大大签合同界面去登录
        // window.location.href = data.data.verifyRedirectUrl
        // this.toggleHeaderVisible(true)
      }
    },
    // 我已知晓事件
    confirmIsAuthFaildDialog() {
      const { already, orderId } = this.$route.query
      this.$router.push({
        name: 'model-detail',
        query: { orderId, already: +already || 1, orderType: this.orderType }
      })
      // // this.$route.params.backCords = { already: +already || 1 }
      // this.$router.go(-2)
    },
    handleSignContract() {
      this.isAgreementSignContract = true
      this.clickSignCaontract()
    },
    jumpEnterpriseFaild() {
      const { orderId } = this.$route.query
      localStorage.setItem(`__AOID__${orderId}`, 1)
      // 企业验证通过跳转到法大大签合同界面去登录
      window.location.href = this.enterpriseRedirectUrl
      this.toggleHeaderVisible(true)
    },
    // 线上签署
    async clickSignCaontract() {
      const {
        carBuyerInfo: { buyType, mobile: buyerMobile },
        contractInfo: { purchaseContractId }
      } = this

      const {
        orderId,
        isSelectOnline,
        isClickDownloadContract,
        lastSelectOnline,
        env
      } = this.$route.query

      if (
        !purchaseContractId &&
        !this.isAgreementSignContract &&
        !this.checkFadadaVerified(orderId)
      ) {
        this.isAffirmSignContract = true
        return
      }

      const {
        isAuthFaild,
        contractUserInfo: { mobile, name },
        isLogged,
        verifyRedirectUrl,
        isEnterpriseVerify,
        enterpriseRedirectUrl
      } = this

      if (!isLogged && verifyRedirectUrl) {
        localStorage.setItem(`__AOID__${orderId}`, 1)
        // 身份验证通过跳转到法大大签合同界面去登录
        window.location.href = verifyRedirectUrl
        this.toggleHeaderVisible(true)
      }

     

      if (isAuthFaild) {
        this.isAuthFaildDialog = true
        this.isAuthFaildMessage = `第三方认证平台显示手机号码 ${mobile.replace(
          /(\d{3})\d{4}(\d{4})/,
          '$1****$2'
        )} 已完成身份认证 ， 与订单中信息不一致 。 请确认实名信息是否正确 ， 如需调整请联系代理商协助处理 。`
        return
      }

       if (!isEnterpriseVerify && enterpriseRedirectUrl) {
        this.isAuthEnterpriseFaildDialog = true
        return
      }

      // 埋点
      this.$sensors.track('signTheContract', {
        page_name: '购车合同',
        order_id: orderId
      })

      this.$store.commit('showLoading')
      // 生成线上合同
      const res = await getContractInfo({ orderId })
      if (res && res.data) {
        const {
          data: { code, message }
        } = res
        if (code !== '00') {
          this.errorsMessage =
            message.indexOf('卡券已过期') !== -1
              ? '卡券已过期，请您联系奥迪专属管家'
              : message || '网络错误'
          this.deliveryDialog = true
          return
          // return Toast({
          //   className: 'toast-dark-mini toast-pos-middle',
          //   message: message || '网络请求错误',
          //   forbidClick: true
          // })
        }
      }

      if (!res.data.data?.sampleId) {
        return console.error('生成线上合同 sampleid 失败')
      }

      // const orderInfoH5 = await getCarOrderInfoH5()
      // const orderh5BaseUrl = orderInfoH5.data.data.configValue
      // console.log(orderInfoH5,'orderInfoH5');
      const token = localStorage.getItem('token') || ''
      const refreshToken = localStorage.getItem('refreshToken')
      let redirectUrl = ''
      if (env == 'minip') {
        redirectUrl = `${this.orderh5BaseUrl}order/new-money-detail?orderId=${orderId}&env=${env}&token=${token}&refreshToken=${refreshToken}&signStatus=1&fromPage=successStatus&type=1`
      } else {
        redirectUrl = `${this.orderh5BaseUrl}order/new-money-detail?orderId=${orderId}&signStatus=1&fromPage=successStatus&type=1`
      }

      const param = {
        orderId,
        sampleId: res.data.data.sampleId,
        // 这里是设置为合同签署后的状态的页面
        redirectUrl
      }
      const {
        data: { data, code, message }
      } = await signContract(param)
      if (code === '00') {
        if (data.signUrl) {
          this.$store.commit('hideLoading')
          // 去签署合同(跳转到法大大的)
          this.routeToSignContract(data.signUrl)
          console.log('法大大地址:', data.signUrl)
          this.toggleHeaderVisible(true)
        }
      } else {
        console.error(data)
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: message || '网络请求错误',
          forbidClick: true
        })
      }
    },

    // 下载合同(调用native)
    async downloadContract() {
      const {
        carBuyerInfo: { buyType },
        $route: {
          query: { orderId }
        }
      } = this
      if (buyType === '02') {
        const { data: sign } = await setEnterpriseChannelSign(
          { orderId },
          'changeDownLoadContractStatus'
        )
        if (sign?.code !== '00') {
          Toast({
            message: '企业合同转线下标记失败',
            className: 'toast-dark-mini toast-pos-middle',
            forbidClick: true,
            duration: 800
          })
        }
        console.log(
          '%c [ sign ]-213',
          'font-size:14px; background:#cf222e; color:#fff;',
          sign
        )
      }
      // 埋点
      this.$sensors.track('downloadContract', {
        page_name: '购车合同',
        order_id: orderId
      })

      const {
        data,
        status,
        data: { code, message }
      } = await createOfflineContract(
        { orderId },
        { 'headers["unified-intercept-enabled"]': true }
      )

      if (status !== 200 || !['00', 200, '200'].includes(code)) {
        this.errorsMessage =
          message.indexOf('卡券已过期') !== -1
            ? '卡券已过期，请您联系奥迪专属管家'
            : message || '网络错误'
        this.deliveryDialog = true
        return
        // return Toast({
        //   className: 'toast-dark-mini toast-pos-middle',
        //   message: message || '网络请求失败',
        //   forbidClick: true
        // })
      }
      const sampleId = data.data?.sampleId

      if (!sampleId) {
        return console.error(`sampleId 获取失败, sampleId:${sampleId}`)
      }

      const param = {
        callFunc: {
          functionName: 'downloadContract',
          functionParams: { sampleId }
        },
        bizCode: BIZCODE
      }

      const res = await Promise.race([
        callNative('business', param),
        handleTimeout(
          {
            bizCode: BIZCODE,
            text: '超时数据'
          },
          2000
        )
      ])

      console.log('business function', res)
      if (res.bizCode === BIZCODE) {
        this.showUploadButton = true
        Toast({
          type: 'success',
          message: '下载成功',
          icon: require('../../assets/img/success.png')
        })
      }
    },

    // 显示原生header
    async toggleHeaderVisible(visible) {
      // if(!isIos){
      await callNative('business', {
        callFunc: {
          functionName: 'toggleNavigation',
          functionParams: {
            show: visible
          }
        },
        bizCode: BIZCODE
      })
      // }
    },

    // 跳转到上传合同的页面
    async toUploadPage() {
      const { orderId } = this.$route.query
      // 埋点
      this.$sensors.track('confirmUpload', {
        page_name: '上传合同',
        order_id: orderId
      })

      const { data } = await createOfflineContract({ orderId })
      const paperId = data.data?.paperId

      // paperId === contractId
      if (!paperId) {
        return console.error(`上传合同获取 paperId 失败: ${paperId}`)
      }

      this.$router.push({
        path: '/upload-contract',
        query: {
          contractId: paperId,
          orderId,
          orderType: this.orderType
        }
      })
    },
    async handleSendingContract() {
      // $router.push({'name': 'contract-download', query: {type:'purchase', contractId: contractInfo.purchaseContractId || ''}})
      const deadline = await store.dispatch('GetOnOffMap', 'deadline')
      const {
        contractInfo: { orderId, purchaseContractId },
        userEmail
      } = this

      // eslint-disable-next-line camelcase
      const downloaded_contract = deadline?.downloaded_contract || {}
      // eslint-disable-next-line camelcase
      if (downloaded_contract && Object.keys(downloaded_contract)?.length) {
        // eslint-disable-next-line camelcase
        const [contractId, time] = downloaded_contract || ['', '']
        if (contractId === purchaseContractId && time >= dayjs().unix()) {
          return Toast({
            message: '您已提交申请，请勿重复发送',
            className: 'toast-dark-mini toast-pos-middle',
            forbidClick: true,
            duration: 800
          })
        }
      }

      if (
        !/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
          userEmail
        )
      ) {
        return Toast({
          message: !userEmail ? '请输入邮箱地址' : '请输入正确的邮箱地址',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      }
      this.popSendContract = false
      const sendingToast = Toast({
        message: '合同正在发送中 ...',
        className: 'toast-dark-mini toast-pos-middle',
        forbidClick: true,
        duration: 0
      })

      console.log(
        '%c [ this.contractInfo ]-532',
        'font-size:14px; background:#cf222e; color:#fff;',
        this.contractInfo
      )
      const { data } = await sendingEMailContract({
        contractId: purchaseContractId,
        orderId,
        receiveMail: userEmail
      }).catch((e) => {
        console.log(
          '%c [ e ]-564',
          'font-size:14px; background:#cf222e; color:#fff;',
          e
        )
        sendingToast.message = '合同发送失败！'
        delay(() => sendingToast.clear(), 1000)
      })

      console.log(
        '%c [ data ]-564',
        'font-size:14px; background:#cf222e; color:#fff;',
        data
      )
      if (data?.code && data.code === '200') {
        sendingToast.message = '合同发送成功，请注意查收！'
      } else {
        sendingToast.message = '合同发送失败！'
      }
      delay(() => {
        sendingToast.clear()
        store.dispatch('SetOnOffMap', [
          'deadline',
          {
            ...deadline,
            ...{
              downloaded_contract: [purchaseContractId, dayjs().unix() + 5 * 60]
            }
          }
        ])
      }, 1000)
    },
    async handleDownloadContractFile() {
      const {
        contractInfo: { purchaseContractId },
        contractType
      } = this
      const { env } = this.$route.query

      if (env == 'minip') {
        wx.miniProgram.navigateTo({
          url: `/pages/order/download/index?contractId=${purchaseContractId}`
        })
        return
      }

      const { nativeApp, platform } = this.getDevice()

      if (nativeApp) {
        const res = await callNative('downloadFile', {
          url: `${baseUrl}${URL.downloadContractFile}?contractId=${purchaseContractId}`,
          name: '',
          path: '',
          fileName: `购车合同${contractType === 1 ? '.pdf' : '.zip'}`,
          contractId: purchaseContractId,
          type: `${contractType === 1 ? 'pdf' : 'zip'}`
        })
      }

      // const res = callNative('downloadFiles', {
      //   url: `${baseUrl}${URL.downloadContractFile}?contractId=${purchaseContractId}`, name: '购车合同', path: '', type: contractType === 1 ? 'pdf' : 'zip'
      // })

      // const downloadingToast = Toast({
      //   message: '合同正在下载中 ...',
      //   className: 'toast-dark-mini toast-pos-middle',
      //   forbidClick: true,
      //   duration: 0
      // })

      // // console.log('%c [ asd ]-595', 'font-size:14px; background:#cf222e; color:#fff;', asd, `${baseUrl}${URL.downloadContractFile}`)
      // const { data } = await downloadContractFile({ contractId: purchaseContractId }).catch(() => {
      //   downloadingToast.message = '合同下载失败！'
      //   delay(() => downloadingToast.clear(), 1000)
      // })

      // if (data) {
      //   downloadFile(data, '购车合同')
      //   downloadingToast.message = '合同下载成功！'
      // }
      // delay(() => downloadingToast.clear(), 1000)
    },

    networkReload() {
      this.initData()
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  padding-bottom: 100px;
}
.btn-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 5px 16px 25px;
  background-color: #fff;
  box-shadow: 0px -1px 16px 0px rgba(0, 0, 0, 0.08);
  .button {
    &:nth-child(2) {
      margin-top: 10px;
    }
  }
}

.img-wrapper {
  min-height: 80vh;
  padding-bottom: 100px;
  box-sizing: border-box;
}
.popup-custom-main {
  .text {
    font-size: 14px;
  }
  .email-box {
    margin: 6px 0 -24px;
    .email {
      display: block;
      border: none;
      text-align: center;
      width: 100%;
      padding: 10px 5px;
      line-height: 22px;
      height: 22px;
      color: #333;
      font-size: 14px;
      background-color: #efefef;
      &::placeholder {
        color: #333;
      }
    }
  }
}
.lan-dialog-enter{
  padding-top: 16px !important;
  ::v-deep .van-dialog__content{
    padding: 0px 0px 10px;
  }
}
</style>
