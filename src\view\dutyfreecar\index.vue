<template>
  <div class="unsubscribeSucceed">
    <div class="_heardImg">
      <img src="../../assets/img/charging-home-img.png">
      <div
        class="buttons"
        @click="onCreate"
      >
        立即申请
      </div>
    </div>
  </div>
</template>

<script>
import { callNative } from '@/utils'
import {
  getqueryOverseasStudentsInformation
} from '@/api/api'

export default {
  data() {
    return {
      identityCardFront: ''
    }
  },
  created() {
    this.getqueryOverseasStudentsInformation()
  },
  methods: {
    getqueryOverseasStudentsInformation() {
      getqueryOverseasStudentsInformation().then((res) => {
        if (res.data.data) {
          // this.isShowBack = true
          if (res.data.data.informationStatus === 0) {
            this.$router.push({ name: 'success' })
          }
        }
      })
    },
    onCreate() {
      this.$router.push({
        path: '/dutyfreecar/car-guide'
      })
    }
  }
}
</script>

  <style scoped lang="less">
  .unsubscribeSucceed {
    ._heardImg {
        // height: 100vh;
        position: relative;
        >img{
           height:100%;
        }
        .buttons{
            position: absolute;
            bottom:81px;
            left:16px;
            right: 16px;
            border-radius: 0;
            border:1px solid #1a1a1a;
            width: 90%;
            height: 56px;
            text-align: center;
            line-height: 56px;
            font-size:16px
        }
    }
  }
  </style>
