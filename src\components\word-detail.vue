<template>
  <div
    class="ccInfo word-detail"
  >
    <div
      class="word-detail-title"
      :style="{'font-size': fontsize+'px'}"
      :id="id"
    >
      {{ title }}
    </div>
    <el-popover
      :get-container="getContainer"
      placement="top"
      trigger="manual"
      popper-class="mypropper"
      v-model="visible"
      :content="title"
    >
      <div
        :class="['word-detail-button',hidden ? 'none' : '']"
        slot="reference"
        @click="visible = !visible"
        v-clickoutside="setVisible"
      >
        <img
          src="../assets/img/wen2.png"
          alt=""
        >
      </div>
    </el-popover>
  </div>
</template>

<script>
import Clickoutside from 'element-ui/src/utils/clickoutside'

export default {
  name: 'WordDetail',
  directives: { Clickoutside },
  props: {
    title: {
      type: String,
      default: '',
      required: true
    },
    id: {
      type: String,
      default: '',
      required: true
    },
    show: {
      type: Boolean,
      default: false,
      required: true
    },
    fontsize: {
      type: Number,
      default: 16
    }
  },
  computed: {
    id_show() {
      const { id, show } = this
      return { id, show }
    }
  },
  watch: {
    id_show: {
      handler: async function (next) {
        if (next.id) {
          const dom = await this.getDom(next.id)
          const width = dom.offsetWidth
          const scrollWidth = dom.scrollWidth

          if (scrollWidth > width) {
            this.hidden = false
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      visible: false,
      hidden: true
    }
  },
  mounted() {
    window.addEventListener("scroll", this.scrolling);
  },
  methods: {
    getContainer() {
      return document.querySelector('.ccInfo');
    },
    getDom: function (id) {
      return new Promise((resolve) => {
        const dom = document.getElementById(id)
        if (dom) {
          resolve(dom)
        } else {
          setTimeout(async () => {
            resolve(this.getDom(id))
          }, 200)
        }
      })
    },
    setVisible() {
      this.visible = false
    }
  },
  mounted() {
    let that = this
    // document.querySelector('.interior-wrapper').addEventListener("scroll", function() {
    //   let current = document.documentElement.scrollTop || document.body.scrollTop
    //   console.log("this.$refs.refWordDetail====",that.$refs.refWordDetail);
    //     that.setVisible()
    // });
    // const dom = document.querySelector('.word-detail-title')
    // const width = dom.clientWidth
    // const scrollWidth = dom.scrollWidth
    // console.log('%c title', 'font-size:25px;color:green;', width, scrollWidth)
  }
}
</script>
<style scoped lang="less">
.word-detail {
  display: flex;
  align-items: center;
  position: relative;
}
.word-detail-title {
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.word-detail-button {
  width: 14px;
  height: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  > img{
    object-fit: center;
  }
}
.none {
  display: none;
}
</style>
<style lang="less">
.mypropper {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  line-height: 24px;
  border-radius: 0;
  box-shadow: 0px 2px 8px -1px rgba(0, 0, 0, 0.25), 0px 1px 4px 0px rgba(0, 0, 0, 0.15);
}

</style>
<style  lang="less">
/deep/ .el-popover {
  min-width: initial
}
</style>