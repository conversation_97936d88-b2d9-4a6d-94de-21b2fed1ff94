<template>
  <div class='balance-uploader border-top'>
    <van-field v-model="remark" placeholder='补充' type="textarea" :autosize="autosize" :border='false' />
    <div style="padding: 0 16px;">
      <van-uploader v-model="fileList" multiple :after-read="afterRead" :max-count="9" :accept="accept" />

    </div>
    <div class="btn-wrapper">
      <AudiButton text="确认上传" @click="onSubmit" font-size="16px" color="black" height="56px" />
    </div>
  </div>
</template>

<script>
  import Vue from 'vue';
  import { Field, Uploader, Toast } from 'vant';
  import AudiButton from '@/components/audi-button'
  Vue.use(Field);
  Vue.use(Uploader);

  import api from '@/config/url'
  import { compressFileImg } from '@/utils'
  import { uploadImg } from '../../api/upload-documents'
  import { addOrderBalancePaymentCertificate } from '@/api/api.js';

  const baseOssHost = api.BaseOssHost
  const codeType = ['00', '200']
  export default {
    components: { AudiButton },
    data() {
      return {
        remark: '',
        autosize: {
          maxHeight: '110',
          minHeight: '110',
        },
        accept: "image/png,image/jpeg,image/jpg,image/pjpeg,image/bmp",
        fileList: [], //本地文件数组
        formData: [],
      }
    },
    mounted() {

    },
    methods: {

      afterRead(file) { //获取图片
        var filelist = []
        //判断array object
        if (!Array.isArray(file)) filelist = [file]
        else filelist = file
        
        const list = []
        const filelist1 = filelist.map(i => i.file)
        filelist1.forEach((item) => {
          const formData = new FormData()
          formData.append('file', item)
          formData.append('fileType', 1)
          formData.append('categoryType', 1)
          formData.append('fileCategoryId', 1)
          list.push(formData)
        })
        this.formData = [...this.formData,...list]
        this.compressedImage()
      },

      async onSubmit() { //提交按钮
        if (this.formData.length === 0) return
        this.$store.commit('showLoading')
        const list = await Promise.all(
          this.formData.map(i => this.uploadImg(i))
        )
        const fail = list.find(i => !codeType.includes(i.data.code))
        if (fail) { //图片上传失败 删除失败的图片
          this.$store.commit('hideLoading')
          Toast({ type: 'fail', message: '上传失败', icon: require('../../assets/img/error.png') })
          list.forEach((i, index) => {
            if (!codeType.includes(i.data.code)) {
              console.log(index)
              this.fileList.splice(index, 1)
              this.formData.splice(index, 1)
            }
          })
        } else { //图片上传成功 走提交凭证接口
          const { orderId } = this.$route.query
          let ossimglist = list.map(i => baseOssHost + i.data.data[0].fileUrl)
          const params = {
            orderId,
            balancePaymentAddress: ossimglist.join(','),
            remark: this.remark
          }
          const { data } = await addOrderBalancePaymentCertificate(params)
          this.$store.commit('hideLoading')
          if (codeType.includes(data.code)) {
            this.$router.replace({
              path: '/order/balance-success',
              query: { orderId }
            })
          } else {
            Toast({ type: 'fail', message: data.message, icon: require('../../assets/img/error.png') })
          }
        }
      },
      
      //图片上传oss
      async uploadImg(formData){
        const res = await uploadImg(formData)
        return res
      },

      // 压缩图片
      async compressedImage() {
        this.$store.commit('showLoading')
        const compressFileList = await Promise.all(this.fileList.map(async (i) => {
          const fileTemp = await compressFileImg(i)
          return { file: fileTemp, content: i.content }
        }))
        this.$store.commit('hideLoading')
        console.log(compressFileList)
        this.fileList = [...compressFileList]
      }
    }
  }
</script>

<style lang='less' scoped>
  .border-top {
    border-top: 1px solid #E5E5E5;
  }

  .btn-wrapper {
    padding: 0 16px;
    position: fixed;
    bottom: 58px;
    width: 100%;
    box-sizing: border-box;
  }

  ::v-deep .van-field__control {
    font-family: "Audi-Normal";
  }

  ::v-deep .van-uploader__preview-delete {
    border-radius: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  ::v-deep .van-uploader__preview-delete-icon {
    position: relative;
    top: 0;
    left: 0;
    right: 0;
  }

  ::v-deep .van-uploader__upload-icon {
    display: none;
  }

  ::v-deep .van-uploader__upload {
    background-image: url('../../assets/img/icon-balance-uploader.png');
    background-size: 100% 100%;
  }
</style>
