<template>
    <van-popup class="boxpopup" v-model="detailVisible" style="width:100%;">
        <!-- Swiper -->
       
        <div v-if="show" class="swiper-container">
            <div class="swiper-wrapper">
                <div class="swiper-slide" v-for="(item, index) in equData" :key="index">
                    <div class="slideB">
                            <div class="descB">
                                <span> {{ objBox.desc }} </span>
                                <!-- <van-icon @click="closeFn" size="24px" class="iconB" name="cross" /> -->
                                <img @click="closeFn" style="position: absolute;width: 24px;height: 24px;right: 7px;" src="@/assets/eq/c.png" alt="" srcset="">
                            </div>
                            <div class="tB" v-if="item.visible">  {{ item.desc }} </div>
                            <div style="padding: 0 10px 10px 10px ;">
                                <div class="detail-wrapper">
                                    <!-- <div v-if="item.detailTitle" class="title" v-html="item.detailTitle" /> -->
                                    <div v-if="item.detail" class="detail" v-html="item.detail" />
                                </div>
                            </div>
                    </div>
                </div>
            </div>
            <!-- <div class="swiper-pagination"></div> -->
        </div>

        <div class="pagiCont">
            <div class="pagiB" style="width: 100%; height: 12px;">
                <div class="itemB" v-for="(item, index) in equData" :key="index" @click="clickPapiFn(index)"
                    :style="{ background: index === activeIndex ? '#FFF' : '#ccc' }" />
            </div>
        </div>
    </van-popup>

</template>
<script>
import Vue from 'vue';
import { Overlay } from 'vant';
import { Swipe, SwipeItem } from 'vant';

Vue.use(Swipe);
Vue.use(SwipeItem);
Vue.use(Overlay);


export default {
    components: {
        Swipe,
        SwipeItem
    },
    props: {
        myData: {
            type: Object,
            required: false
        },
    },
    data() {
        return {
            // modules: [Pagination],
            objBox: {},
            detailVisible: false,
            show: false,
            equData: {},
            activeIndex: 0,
            swiperObj: null
        }
    },
    mounted() {

    },
    methods: {
        clickPapiFn(e) {
            this.activeIndex = e
            this.swiperObj.slideTo(e, 1000, true);//切换到第一个slide，速度为1秒
        },
        closeFn() {
            this.show = false
            this.detailVisible = false
        },
        init(e, index = '显示详情') {
            console.log("init", e);
            this.detailVisible = true
            let arr = []
            if (e.detail) {
                arr.push({
                    desc: e.desc,
                    detail: e.detail,
                    detailTitle: e.detailTitle,
                })
            }
            if (e.children && e.children?.length > 0) {
                e.children.forEach(element => {
                    element.detail && arr.push({
                        visible: true,
                        desc: element.desc,
                        detail: element.detail,
                        detailTitle: element.detailTitle,
                    })
                });
            }
            this.equData = arr
            this.show = true
            console.log("22222222", this.equData);
            let that = this
            this.$nextTick(() => {
                this.objBox = e
                let obj = {
                    effect: "coverflow",
                    grabCursor: true,
                    centeredSlides: true,
                    slidesPerView: "auto",
                    coverflowEffect: {
                        rotate: 50,
                        stretch: 10,
                        depth: 100,
                        modifier: 1,
                        slideShadows: true,
                    },
                    pagination: {
                        el: ".swiper-pagination",
                    },
                    // noSwipingClass: 'scroll-wrap',
                    observer: true, // 修改swiper自己或子元素时，自动初始化swiper
                    observeParents: true, // 修改swiper的父元素时，自动初始化swiper
                    // effect: "coverflow",
                    // grabCursor: true,
                    // centeredSlides: true,
                    // slidesPerView: "auto",
                    // coverflowEffect: {
                    //     rotate: 50,
                    //     stretch: 0,
                    //     depth: 100,
                    //     modifier: 1,
                    //     slideShadows: true,
                    // },
                    // pagination: {
                    //     el: ".swiper-pagination",
                    // },
                    on: {
                        slideChange: function () {
                            that.activeIndex = this.activeIndex
                        },
                    },
                }
                this.swiperObj = new Swiper(".swiper-container", obj);
                if (index == '查看详情') return
                this.clickPapiFn(!e.detail ? index : Number(++index))
            })
        }
    }
}
</script>
<style scoped lang="less">

.tB {
    padding-left: 10px;
    padding-top: 10px;
    color: #000;
    font-weight: 700;
    text-align: center;
}
/deep/ .popB .container {
    padding: 0 30px;
}


.iconB {
    position: absolute;
    right: 10px;
    top: 2px;
}

.slideB {
    height: calc(100vh - 100px);
    background: white;
    padding-top: 5px;
    overflow-y: scroll;
    margin-bottom: 24px;
}

.descB {
    position: relative;
    font-size: 16px;
    font-weight: bold;
    color: #000000;
    line-height: 24px;
    text-align: center;
    padding: 12px 10px;
    padding-top: 3px;
    padding-bottom: 0;
}


.boxpopup {
    background-color: rgba(0, 0, 0, 0) !important;
    overflow: visible;

}

/deep/ .van-popup--center {
    background: transparen !important;
}

/deep/ .swiper {
    width: 100%;
    padding-top: 50px;
    padding-bottom: 50px;
}

/deep/ .swiper-slide {
    background-position: center;
    background-size: cover;
    width: 310px;
    
    // height: 100vh;
}

/deep/ .swiper-slide .slideB {
    display: block;
    width: 100%;
}

/deep/ .swiper-slide-shadow-left,
.swiper-slide-shadow-right {
    width: 0 !important;
    display: none !important;
}

/deep/ .swiper-pagination-bullet {
    border-radius: 0;
    width: 12px;
    height: 5px;
}

/deep/ .swiper-pagination-bullet-active {
    display: none;
}

.pagiCont {
    position: fixed;
    width: 100%;
    z-index: 1;
    bottom: -12px;
}

.pagiB {
    display: flex;
    justify-content: center;

    .itemB {
        width: 12px;
        height: 5px;
        margin: 0 2px;
    }
}

/deep/ .swiper-container-horizontal>.swiper-pagination-bullets,
.swiper-pagination-custom,
.swiper-pagination-fraction {
    display: none;
}

/deep/ .swiper-slide-next {
    transform: translate3d(30px, 0px, -100px) rotateX(0) rotateY(0) scale(1) !important
}

/deep/ .swiper-slide-prev {
    transform: translate3d(-30px, 0px, -100px) rotateX(0) rotateY(0) scale(1) !important
}



/deep/ .swiper-container-3d .swiper-slide-shadow-right {
    background-image:none;
}

.detail-wrapper {
    >.title {
        font-weight: bold;
        font-size: 16px;
        // padding-right: 50px;
    }

    >.detail {
        margin-top: 15px;
        font-size: 14px;
    }
}
</style>