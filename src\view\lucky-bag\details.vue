<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-04-07 15:48:18
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-04-17 12:10:06
 * @FilePath     : \src\view\lucky-bag\details.vue
 * @Descripttion : 福包说明
-->
<template>
  <div
    class="lucky-bag-wrapper"
  >
    <div class="lucky-bag-title">
      <div class="header">
        <h2 class="h2">
          限量60台 以支付定金为准 先到先得<br>需在 {{ endTime }} 前完成定金支付
        </h2>
      </div>
    </div>
    <div class="lucky-bag-media">
      <img
        v-if="luckyBagId"
        class="media-img"
        :src="require(`@/assets/bags/lucky-bag-cars${luckyBagId}.jpg`)"
      >
    </div>
    <div class="lucky-bag-box">
      <div class="lucky-bag-list">
        <h3 class="lucky-bag-title">
          福包选装
        </h3>
        <div class="bag-box list">
          <div
            class="item van-hairline--bottom"
            data-flex="main:left cross:center"
            v-for="(item, index) in details.luckyList"
            :key="index"
          >
            <div class="img-box">
              <img
                class="media-img"
                :src="item.imageUrl"
              >
            </div>
            <div
              class="info-box"
              data-flex-box="1"
            >
              <h3 class="h3 text-one-hidd">
                {{ item.optionNameCn }}
              </h3>
              <p><span class="clo-a6">¥{{ item.optionPrice | formatPrice }}</span></p>
            </div>
          </div>
        </div>
      </div>
      <div class="lucky-bag-list">
        <h3
          class="lucky-bag-title"
          data-flex="main:justify"
        >
          <span>车辆配置</span><span
            class="btn"
            data-flex="main:left cross:center"
            @click="handleFoldInfo"
          >查看全部<van-icon :name="`arrow-${fold}`" /></span>
        </h3>
        <div class="bag-box list">
          <div
            class="item van-hairline--bottom"
            data-flex="main:left cross:center"
          >
            <div class="img-box">
              <img
                v-if="details.carModel && details.carModel.headImageUrl"
                class="media-img"
                :src="details.carModel.headImageUrl"
                style="object-fit:cover"
              >
            </div>
            <div
              class="info-box"
              data-flex-box="1"
              data-flex="main:justify"
            >
              <div
                class="left"
                data-flex-box="1"
              >
                <p><span class="clo-a6">车型</span></p>
                <h3
                  class="h3 text-one-hidd"
                >
                  {{ details.carModel && details.carModel.modelNameCn ? details.carModel.modelNameCn : '' }}
                </h3>
              </div>
              <div
                class="right"
                data-flex="cross:center"
              >
                <span class="clo-4a">¥{{ details.carModel && details.carModel.modelPrice | formatPrice }}</span>
              </div>
            </div>
          </div>
          <div
            class="auxiliary-box"
            v-show="fold === 'up'"
          >
            <div
              class="item van-hairline--bottom"
              data-flex="main:left cross:center"
              v-for="(item, index) in details.configsList"
              :key="index"
            >
              <div class="img-box">
                <img
                  class="media-img"
                  :src="item.imageUrl"
                >
              </div>
              <div
                class="info-box"
                data-flex-box="1"
                data-flex="main:justify"
              >
                <div class="left">
                  <p><span class="clo-4a">{{ item.optionClassificationName || '-' }}</span></p>
                  <h3 class="h3 text-one-hidd">
                    {{ item.optionNameCn || '' }}
                  </h3>
                </div>
                <div
                  class="right"
                  data-flex="cross:center"
                >
                  <span class="clo-a6">价格已包含</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="lucky-bag-list">
        <h3 class="lucky-bag-title">
          价格明细
        </h3>
        <div class="bag-box list">
          <div
            class="price-box item van-hairline--bottom"
            data-flex="main:justify cross:center"
          >
            <p>车辆原价</p>
            <p>¥{{ details.totalPrice | formatPrice }}</p>
          </div>
          <div
            class="price-box item van-hairline--bottom"
            data-flex="main:justify cross:center"
          >
            <p>福包膨胀价值</p>
            <p>-¥{{ details.luckyPrice | formatPrice }}</p>
          </div>
          <div
            class="price-box item van-hairline--bottom"
            data-flex="main:justify cross:center"
          >
            <p>实付款</p>
            <p><em>¥{{ details.payPrice | formatPrice }}</em></p>
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="emptyBookPopShow"
      class="lottie-lucky-bags empty-book-pop"
      :close-on-click-overlay="false"
    >
      <div
        class="protocol-pop-box"
      >
        <h3 class="h3">
          该福包已售罄，点击确定前往查看其他车型
        </h3>
        <div
          class="btn"
          data-flex="main:justify"
        >
          <audi-button
            @click="handleCancelCheck"
            text="取消"
            height="46px"
            width="calc(50% - 2px)"
          />
          <audi-button
            @click="() => $router.push({ name: 'HotRecommended' })"
            color="black"
            text="确定"
            width="calc(50% - 2px)"
            height="46px"
          />
        </div>
      </div>
    </van-popup>
    <div class="fixed-bottom-box">
      <audi-button
        @click="handleLuckyBagBook"
        :color="buttonColor"
        text="立即预定"
        :height="buttonHeight"
      />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Icon, Popup, Toast
} from 'vant'
import dayjs from 'dayjs'
import AudiButton from '@/components/audi-button.vue'
import {
  getLuckyBagConfigList, getLuckyBagModelStock, getLuckyBagSkuId, getLuckyBagIdInfo, confirmCcid, getUserLuckyBagInfo
} from '@/api/lucky-bag'
import api from '@/config/url'

const baseOssHost = api.BaseOssHost

Vue.use(Icon).use(Popup).use(Toast)
export default {
  name: 'LuckyBagCarsDetails',
  components: { AudiButton },
  data() {
    return {
      ccid: '',
      endTime: '',
      luckyBagId: 0,
      luckyPackageId: 0,
      price: 0,
      params: {},
      fold: 'up',
      buttonColor: 'black',
      buttonHeight: '56px',
      emptyBookPopShow: false,
      baseOssHost,
      isModelStock: 0,
      details: {}
    }
  },
  mounted() {
  },
  async created() {
    const {
      ccid, skuid, luckyPackageId
    } = this.$route.query || 0
    if (ccid) {
      await this.getUserLuckyBagInfoData()
      this.ccid = ccid
      this.luckyPackageId = luckyPackageId
      // 首页(App)直接进入无skuid
      if (!skuid) {
        this.getLuckyBagSkuIdData()
      }
      this.params = { ccid, skuid, luckyPackageId }
      this.handleGetLuckyBagConfigList({ ccid, channel: 'oneapp' })
    }
  },
  methods: {
    // 查询skuid
    async getUserLuckyBagInfoData() {
      const { data } = await getUserLuckyBagInfo()
      if (data?.data) {
        const {
          carModelName, id, blessedBagProductId, ccid, luckyPackageId, activityPayEndtime
        } = data.data
        this.luckyBagId = id
        this.endTime = dayjs(activityPayEndtime).format('YYYY.M.DD HH:mm:ss')
      }
    },
    async getLuckyBagSkuIdData() {
      const { data } = await getLuckyBagSkuId()
      if (data?.data) {
        this.params.skuid = data.data
      }
    },
    handleFoldInfo() {
      const { fold } = this
      this.fold = fold === 'up' ? 'down' : 'up'
    },
    // 福包基础信息(价格、时间)
    // async confirmCcidData(ccid) {
    //   const { data } = await confirmCcid({ ccid })
    //   if (data?.data) {
    //     this.price = data.data.blessedBagPrice
    //     this.endTime = dayjs(data.data.activityEndtime).format('YYYY.M.DD HH:mm:ss')
    //   }
    // },
    // 福包关联车型 库存
    // async handleGetLuckyBagModelStock(luckyPackageId) {
    //   const { data } = await getLuckyBagModelStock({ blessedPackId: luckyPackageId })
    //   this.isModelStock = data.data
    // },
    async handleGetLuckyBagConfigList(params) {
      const { ccid } = params
      // 福包基础信息(价格、时间)
      const luckBagInfo = await confirmCcid({ ccid })
      if (luckBagInfo?.data?.data) {
        this.price = luckBagInfo.data.data.blessedBagPrice
        const { data } = await getLuckyBagConfigList(params) || ''
        if (data?.data) {
          const { configDetail, activityPayEndtime } = data.data || ''
          const {
            carModel, optionList, totalPrice, insideColor, outsideColor
          } = configDetail || ''
          carModel.headImageUrl = baseOssHost + carModel.headImageUrl
          const optionList2 = optionList.filter((option) => option.optionCode != 'Q2J')
          optionList2.forEach((i) => {
            i.imageUrl = baseOssHost + i.imageUrl
          })
          const luckyList = [] // 福包
          const configsList2 = [] // 配置
          const price = Number(this.price) // 福包购买价格
          const modelPrice = Number(carModel.modelPrice) // 车型价格
          const luckyPrice = Number(totalPrice) - modelPrice - price
          // 车辆原价 => totalPrice
          // 福包膨胀价值 => totalPrice - carModel.modelPrice - price(福包购买价格)
          // 实付款 = carModel.modelPrice + price(福包购买价格)
          const payPrice = modelPrice + price
          const optionPrice = optionList2.reduce((a, b) => a + b.optionPrice || 0, 0)
          optionList2.forEach((i) => {
            if (i.optionPrice) {
              luckyList.push(i)
            } else {
              configsList2.push(i)
            }
            if (i.optionClassification === 'VOS') {
              i.optionClassificationName = '座椅'
            } else if (i.optionClassification === 'RAD') {
              i.optionClassificationName = '轮毂'
            }
          }) // 筛选分类
          insideColor.imageUrl = baseOssHost + insideColor.imageUrl
          insideColor.optionClassificationName = '内饰'
          insideColor.optionNameCn = insideColor.colorNameCn

          outsideColor.imageUrl = baseOssHost + outsideColor.imageUrl
          outsideColor.optionNameCn = outsideColor.colorNameCn
          outsideColor.optionClassificationName = '外饰'
          const configsList = [insideColor, outsideColor, ...configsList2]
          this.details = {
            ...{ carModel }, ...{ configsList }, totalPrice, ...{ luckyList }, optionPrice, luckyPrice, payPrice
          }
        }
      }
    },
    handleCancelCheck() {
      this.emptyBookPopShow = false
    },
    handleConfirmCheck() {},
    // 预确认订单（检查是否有相关车型库存）
    async handleLuckyBagBook() {
      // 查询关联车型库存
      const { data } = await getLuckyBagModelStock({ blessedPackId: this.luckyPackageId })
      if (data?.data === 1) {
        return this.$router.push({ name: 'detail', query: { ...this.params } })
      }
      this.emptyBookPopShow = true
    }
  }
}
</script>

<style lang="less" scoped>
.lucky-bag-wrapper {
  padding-bottom: 125px;
  background-color: #fff;
  .header {
    padding: 12px;
    background-color: #181818;
    .h2 {
      margin: 0 auto;
      line-height: 160%;
      text-align: center;
      // width: 200px;
      font-size: 14px;
      font-weight: normal;
      color: #fff;
      overflow: hidden;
    }
    .btn {
    }
  }
  .media-img {
    max-width: 100%;
  }
  .empty-book-pop {
    text-align: center;
    width: 85%;
    padding: 0 24px 16px;
    box-sizing: border-box;
    .h3 {
      padding: 20px 32px 0;
      font-size: 16px;
      font-weight: normal;
      line-height: 180%;
    }
  }
}
.lucky-bag-box {
  padding: 20px 16px;
  .lucky-bag-title {
    margin: 0;
    font-size: 18px;
    line-height: 160%;
    .btn {
      font-size: 14px;
      font-weight: normal;
      color: rgba(0, 0, 0, .5);
      .van-icon {
        margin-left: 2px;
        font-size: 17px;
        color: #919191;
      }
    }
  }
  .lucky-bag-list {
    margin-bottom: 24px;
    .bag-box {
      &.list {
        .item {
          padding-top: 16px;
          padding-bottom: 8px;
          &::after{
            border-color: #a6a6a6;
          }
          p {
            em {
                font-style: normal;
                font-weight: 600;
            }
          }
          .img-box {
            overflow: hidden;
          }
          .img-box, .media-img {
            width: 56px;
            height: 56px;
          }
          .info-box {
            padding-left: 12px;
            .h3, p {
              margin: 0;
              font-size: 14px;
              line-height: 160%;
            }
            .h3 {
              font-size: 15px;
              line-height: 200%;
              font-weight: normal;
            }
            .clo-a6 {
              color: #a6a6a6;
            }
            .clo-4a {
              color: #4a4a4a;
            }
            .left {
              width: 50vw;
            }
          }
        }
      }
    }

  }
}
.fixed-bottom-box {
  position: fixed;
  left: 0;
  right: 0;
  bottom: -1px;
  padding: 25px 16px;
  background: #fff;
  box-shadow: -1px 0 5px rgba(30, 30, 30, 0.05);
}
</style>
