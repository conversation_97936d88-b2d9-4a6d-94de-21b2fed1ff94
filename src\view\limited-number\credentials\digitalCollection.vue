<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-02-09 16:43:51
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-02-28 22:27:29
 * @FilePath     : \src\view\limited-number\credentials\digitalCollection.vue
 * @Descripttion : 数字藏品
-->
<template>
  <van-popup
    v-model="warmPromptPop"
    class="popup-custom"
    :close-on-click-overlay="false"
  >
    <div class="popup-custom-box">
      <div class="popup-custom-main">
        <div class="h3 align-center">
          温馨提示
        </div>
        <div class="text">
          <div class="list">
            即将前往第三方<strong />“腾讯吾得库”</strong>提供的页面
          </div>
          <div class="list">
            1、第三方在提供服务的过程中向您做出的任何承诺、声明或行为仅适用于第三方与您之间的服务，不视为上汽奥迪的承诺、声明或行为。
          </div>
          <div class="list">
            2、如您因未遵守第三方相关授权文件的规定或要求，造成您的任何损失，上汽奥迪不承担任何责任。
          </div>
        </div>
      </div>
      <div
        class="popup-custom-btn"
        data-flex="main:justify"
      >
        <div
          class="lan-button-box white-button line-two-cols"
        >
          <van-button
            :disabled="jumpLoading"
            @click="handleCancelJump"
            class="lan-button"
          >
            取消
          </van-button>
        </div>
        <div class="lan-button-box black-button line-two-cols">
          <van-button
            @click="handleConfirmJump"
            loading-text="跳转中 ..."
            :loading="jumpLoading"
            class="lan-button"
          >
            确认跳转
          </van-button>
        </div>
      </div>
    </div>
  </van-popup>
</template>
<script>
import Vue from 'vue'
import { Toast, Popup, Button } from 'vant'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { digitCollectionJumpURL } from '@/api/api'
import {
  callNative
} from '@/utils/'

Vue.use(Toast).use(Popup).use(Button)
export default {
  props: {
    numbers: {
      type: String,
      default: ''
    },
    warmPrompt: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    warmPrompt(val) {
      this.warmPromptPop = val
    }
  },
  data() {
    return {
      jumpLoading: false,
      warmPromptPop: false
    }
  },
  methods: {
    ...mapGetters(['getDevice']),
    handleCancelJump() {
      this.$emit('update:warm-prompt', false)
      this.jumpLoading = false
    },
    async handleConfirmJump() {
      this.jumpLoading = true
      const { data } = await digitCollectionJumpURL()
      if (data?.data?.configValue && data?.code === '00') {
        this.handleCancelJump()
        const { nativeApp } = this.getDevice() || { nativeApp: false }
        if (nativeApp) {
          callNative('audiOpen', { path: data.data.configValue })
        } else {
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
</style>
