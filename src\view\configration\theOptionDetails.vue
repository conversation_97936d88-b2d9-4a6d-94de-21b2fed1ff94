<template>
    <div class="container">
        <div class="desc" v-if="optionDesc">{{ optionDesc }}</div>
    </div>
</template>
  
<script>
import { getPacketEquity } from '@/configratorApi/index'
export default {
    data() {
        return {
            optionDesc: '',
        }
    },
    created() {
        this.packetEquity()
    },
    methods: {
        async packetEquity() {
            let optionCode = this.$route.query.optionCode
            let modelLineId = this.$route.query.modelLineId
            let res = await getPacketEquity({ modelLineId })
            let packageData = res.data.data
            let obj = packageData.find(e => e.optionCode == optionCode)
            this.optionDesc = obj.optionDetailList[0].optionDesc
            this.$store.commit('setTitle', obj.optionName)
        },
    }
}
</script>
  
<style scoped lang="less">
.container {
    padding: 16px;
}

.desc {
    font-family: 'Audi-Normal';
    font-weight: normal;
    line-height: 1.5;
    letter-spacing: 0;
    color: #000000;
    white-space: pre-wrap;
}
</style>
  