<template>
  <div   :class="['orderList',{purpleActiveStyle:purpleState}]">
    <page-load
      @onRefresh="onRefresh"
      :pulldown.sync="pulldown"
      :pullupstate="false"
    >
      <div
        class="order_container"
        v-for="(item, index) in orderList"
        :key="item.title"
      >
        <div
          class="order_header"
          data-flex="main:justify cross:center"
          @click.stop="toServiceList(index,item.list.length)"
        >
          <span class="">{{ item.title + `（${item.list.length}）` }}</span>
          <img
            align="center"
            src="@/assets/img/icon_20.png"
          >
        </div>
        <!-- 服务预约、取送车服务、充电装服务 -->
        <template v-if="item.list.length > 0 && (index === 0 || index === 1 || index === 4 )">
          <div
            class="order-status"
            data-flex="main:justify cross:center"
          >
            <span class="time">{{ item.list[0].placeOrderTime || '' }} </span>
            <span class="text">{{ setStatusDesc(item.list[0]) }}</span>
          </div>
          <div
            class="order_content"
            @click="toOrderDetail(item.list[0])"
          >
            <div class="order_content_left">
              <img
                :src="
                  $loadWebpImage((item.list[0].dealerThumbnailUrl || '').includes('http')
                    ? item.list[0].dealerThumbnailUrl
                    : BaseConfigrationOssHost + item.list[0].dealerThumbnailUrl)
                "
                alt=""
              >
            </div>
            <div class="order_content_right">
              <div class="order_content_right_top">
                <span class="order_name">{{ setOrderName(item.list[0]) }}</span>
                <!-- <span class="order_state">{{ setStatusDesc(item.list[0]) }}</span> -->
              </div>
              <!-- <div class="order_content_right_mid">
                {{ item.list.length && item.list[0].placeOrderTime || '' }}
              </div> -->
              <!-- <div
                v-if="isShowCancel(item.list[0])"
                class="order_content_right_bottom"
              >
                <div @click.stop="onCancelOrder(item.list[0])">
                  <AudiButton
                    :text="'取消订单'"
                    color="white"
                    font-size="14px"
                    height="30px"
                    width="80px"
                  />
                </div>
              </div> -->
            </div>
          </div>
          <div
            class="order-bottom"
            v-if="isShowCancel(item.list[0])"
          >
            <div
              class="btn-box"
              data-flex="main:right cross:center"
              @click.stop="onCancelOrder(item.list[0])"
            >
              <!-- <AudiButton
                :text="'取消订单'"
                color="white"
                font-size="12px"
                height="32px"
                width="93px"
              /> -->
              <div class="btn">
                取消订单
              </div>
            </div>
          </div>
        </template>
        <template v-if="item.list.length > 0 && index === 3">
          <div
            class="order-status"
            data-flex="main:justify cross:center"
          >
            <span class="time">{{ item.list[0].orderTime | formatDate }} </span>
            <span class="text">{{ item.list[0].statusDesc }}</span>
          </div>
          <div
            class="order_content"
            @click="getServiceDatail(item.list[0])"
          >
            <div class="order_content_left">
              <img
                :src="item.list[0].imageUrl"
                alt=""
              >
            </div>
            <div class="order_content_right">
              <div class="order_content_right_top">
                <span class="order_name">{{
                  item.list[0].serviceName
                }}</span>
                <!-- <span class="order_state">{{ item.list[0].statusDesc }}</span> -->
              </div>
              <!-- <div class="order_content_right_mid">
                {{ item.list[0].orderTime | formatDate }}
              </div> -->
            </div>
          </div>
        </template>
        <template v-if="item.list.length > 0 && index === 5">
          <div
            class="order-status"
            data-flex="main:justify cross:center"
          >
            <span class="time">{{ item.list[0].appoBeginTime }} </span>
            <span class="text">{{ item.list[0].statusDesc }}</span>
          </div>
          <div
            class="order_content"

            @click="getLongServiceDatail(item.list[0])"
          >
            <div class="order_content_left">
              <img
                :src="(item.list[0].dealerDto&&item.list[0].dealerDto.imageUrl.includes('http'))?item.list[0].dealerDto.imageUrl:BaseConfigrationOssHost + item.list[0].dealerDto.imageUrl"
                alt=""
              >
            </div>
            <div class="order_content_right">
              <div class="order_content_right_top">
                <span class="order_name">{{
                  item.list[0].seriesDto.customSeriesName+"超长试驾"
                }}</span>
                <!-- <span class="order_state">{{ item.list[0].statusDesc }}</span> -->
              </div>
              <!-- <div class="order_content_right_mid">
                {{ item.list[0].appoBeginTime }}
              </div> -->
            </div>
          </div>
        </template>
        <template v-if="item.list.length > 0 && index === 2">
          <div
            class="order-status"
            data-flex="main:justify cross:center"
          >
            <span class="time">{{ item.list[0].createTime }} </span>
            <span class="text">{{ item.list[0].serviceStatus }}</span>
          </div>
          <div
            class="order_content"

            @click="getRoadServiceDatail(item.list[0])"
          >
            <div class="order_content_left">
              <img
                src="@/assets/img/home_icon_Road Rescue_black.jpg"
                alt=""
              >
            </div>
            <div class="order_content_right">
              <div class="order_content_right_top">
                <span class="order_name">道路救援</span>
                <!-- <span class="order_state">{{ item.list[0].serviceStatus }}</span> -->
              </div>
              <!-- <div class="order_content_right_mid">
                {{ item.list[0].createTime }}
              </div> -->
            </div>
          </div>
        </template>
      </div>
      <div
        v-show="showNotData"
        class="not_data"
      >
        <div>
          <img
            src="@/assets/img/not_data.png"
            alt=""
          >
        </div>
        <p>暂无内容</p>
      </div>
    </page-load>
    <!-- <van-loading
      class="lan-loading-custom lan-custom-center lan-loading-darkly enable-masking-out lan-loading-decephaly"
      size="24px"
      vertical
      v-if="loading"
    >
      正在加载···
    </van-loading> -->
    <van-popup
      v-if="modalshow"
      v-model="modalshow"
      :close-on-click-overlay="false"
      :transition-appear="true"
    >
      <div class="_modal">
        <div
          class="modal-title"
        >
          {{ '请选择取消以下哪项服务？' }}
        </div>
        <div
          class="modal-content"
        >
          {{ content }}
        </div>
        <div>
          <div
            class="service-line"
            style=" margin-top: 8px"
            v-if="onCliickOrderItem.deliverVo.takeIsCancel === 1"
          >
            <div class="title-bold">
              仅取消取车
            </div>
            <div
              class="btn-change"
              @click.stop="onCancelTake"
            >
              <img
                class="btn-icon"
                :src="isTake ? ctiveRadioIcon : inactiveRadioIcon"
              >
            </div>
          </div>
          <div
            class="service-line"
            style=" margin-top: 8px"
            v-if="onCliickOrderItem.deliverVo.sendIsCancel === 1"
          >
            <div class="title-bold">
              仅取消送车
            </div>
            <div
              class="btn-change"
              @click.stop="onCancelSend"
            >
              <img
                class="btn-icon"
                :src="isSend ? ctiveRadioIcon : inactiveRadioIcon"
              >
            </div>
          </div>
          <div
            class="service-line"
            style=" margin-top: 8px"
            v-if=" onCliickOrderItem.deliverVo.takeIsCancel === 1 && onCliickOrderItem.deliverVo.sendIsCancel === 1"
          >
            <div class="title-bold">
              全部取消
            </div>
            <div
              class="btn-change"
              @click.stop="onCancelTakeAndSend"
            >
              <img
                class="btn-icon"
                :src="isAll ? ctiveRadioIcon : inactiveRadioIcon"
              >
            </div>
          </div>
        </div>
        <div
          class="modal-confirm center"
          @click.stop="onConfirm"
        >
          {{ '确定' }}
        </div>
        <div
          class="modal-cancel center"
          @click.stop="onCancel"
        >
          {{ '取消' }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { Popup } from 'vant'
import Vue from 'vue'
import PageLoad from '@/components/page-load.vue'
import {
  getAfterSaleFindByPage,
  getCarConfig,
  getAfterSaleCancel,
  postCancelDeliverCarOrder,
  getInsaicServiceList,
  getInsaicServiceDetail,
  getRoadSaveServiceList,
  getRoadSaveOrderDetail,
  postBindCardInfo,
  postChargingPileCancel,
  getVeryLongReservationOrderList
} from '@/api/api'
import { callNative } from '@/utils'
import { formatTime } from '@/utils/date'
import url from '@/config/url'
// import AudiButton from '@/components/audi-button'

Vue.use(Popup)
export default {
  components: { PageLoad },
  data() {
    return {
      purpleState:null,
      pulldown: false, // 下拉
      showNotData: false,
      serviceDict: {
        '01': { title: '代驾服务', img: require('../../../assets/img/dj.png') },
        '02': { title: '洗车服务', img: require('../../../assets/img/water_car.png') },
        '03': { title: '机场贵宾厅', img: require('../../../assets/img/gbt.png') },
        '04': { title: '机场接送', img: require('../../../assets/img/js.png') },
        '05': { title: '租车服务', img: require('../../../assets/img/rent_car.png') },
        '06': { title: '机场周边泊车', img: require('../../../assets/img/park.png') },
        '07': { title: '一键加电', img: require('../../../assets/img/park.png') }
      },
      orderList: [
        {
          title: '服务预约',
          groupType: 1,
          list: []
        },
        {
          title: '取送车服务',
          groupType: 2,
          list: []
        },

        {
          title: '道路救援',
          groupType: 3,
          list: []
        },
        {
          title: '出行服务',
          groupType: 4,
          list: []
        },
        {
          title: '充电桩服务',
          groupType: 5,
          list: []
        },
        {
          title: '超长试驾服务',
          groupType: 6,
          list: []
        }
      ],
      BaseConfigrationOssHost: url.BaseOssHost,

      modalshow: false,
      isShowSelect: false,
      title: '',
      content: '',
      orderId: '',

      ctiveRadioIcon: require('../../../assets/img/radio_checked.png'),
      inactiveRadioIcon: require('../../../assets/img/radio_normal.png'),
      isTake: false,
      isSend: false,
      isAll: false,
      onCliickOrderItem: {},
      loading: false
    }
  },

  created(){
    this.purpleState = ['fromPurple','purple'].includes(this.$route.query.fromType) //purple项目,兼容purple（测试用）
  },

  mounted() {
    this.getCarOrderList()
    this.appCallJs()
  },
  watch: {},

  filters: {
    formatDate(timestamp) {
      return formatTime(parseInt(timestamp))
    }
  },

  methods: {
    // APP调用刷新
    appCallJs() {
      callNative('appCallJs', {}).then((data) => {
        if (data.refresh) {
          this.onRefresh()
          // App callbackId只生效一次，接到响应需要再次调用
          this.appCallJs()
        }
      })
    },
    async getServiceDatail(params) {
      const { data } = await getInsaicServiceDetail({
        insaicOrderNo: params.insaicOrderNo,
        serviceType: params.serviceType
      })
      // 调用audiOpen打开
      const url = data.data
      // 跳转详情
      callNative('audiOpen', { path: url, showHeader: false })
    },
    // 超长订单试驾详情
    getLongServiceDatail(item) {
      const { origin, pathname } = window.location
      const url = `${origin}${pathname}#/testdrive/long-service-detail?appoId=${item.appoId}`
      //  this.$router.push({
      //     path: "/testdrive/long-service-detail",
      //     query: {
      //       appoId: item.appoId,
      //     },
      //   });
      callNative('audiOpen', { path: url })
    },

    async getRoadServiceDatail(params) {
      const data = await getRoadSaveOrderDetail()
      if (data.data.code === '200') {
        // 调用audiOpen打开
        const url = `${data.data.data}${params.serviceId}`
        // 跳转详情
        callNative('audiOpen', { path: url, showHeader: false })
      }
    },

    async onRefresh() {
      await this.getCarOrderList()
      this.pulldown = !this.pulldown
    },

    async getInsaicServiceData() {
      // 获取vin码遍历
      console.log('来了')
      const { data } = await postBindCardInfo({})
      console.log(data.data)
      const emptyObj = []
      if (data.code === '200') {
        if (data.data.length > 0) {
          const apis = data.data.map((item) => getInsaicServiceList({
            vin: item.vin,
            serviceType: '',
            page: 1,
            size: 999
          }).then((data) => {
            if (data.data.data != null) {
              return data.data.data.orderList
            }
            return emptyObj
          }))
          const promise = Promise.all(apis).then((datas) => datas.flat())
          return promise
        }
        return new Promise((res) => {
          res(emptyObj)
        })
      }
      return new Promise((res) => {
        res(emptyObj)
      })
    },

    // 获取道路救援服务订单列表
    async getRoadSaveServiceData() {
      // 获取vin码遍历
      const { data } = await postBindCardInfo({})
      const emptyObj = []
      if (data.code === '200') {
        if (data.data.length > 0) {
          const apis = data.data.map((item) => getRoadSaveServiceList({
            vin: item.vin,
            page: 1,
            pageSize: 9999
          }).then((data) => {
            if (data.data.data != null) {
              return data.data.data
            }
            return emptyObj
          }))
          const promise = Promise.all(apis).then((datas) => datas.flat())
          return promise
        }
        const api = getRoadSaveServiceList({
          page: 1,
          pageSize: 9999
        }).then((data) => {
          if (data.data.data != null) {
            return data.data.data
          }
          return emptyObj
        })

        const promise = Promise.all([api]).then((datas) => datas.flat())
        return promise

        // return new Promise((res) => {
        //   res(emptyObj);
        // });
      }
      return new Promise((res) => {
        res(emptyObj)
      })
    },

    // 获取车订单
    async getCarOrderList() {
      // this.loading = true
      await Promise.all([
        getAfterSaleFindByPage({ type: 1 }).then(({ data }) => {
          this.orderList[0].list = data?.data?.data || []
        }),
        getAfterSaleFindByPage({ type: 2 }).then(({ data }) => {
          this.orderList[1].list = data?.data?.data || []
        }),
        getAfterSaleFindByPage({ type: 3 }).then(({ data }) => {
          this.orderList[4].list = data?.data?.data || []
        })
      ])
      // this.loading = false

      await Promise.all([
        // getAfterSaleFindByPage({ type: 1 }).then((data) => data.data.data.data),
        // getAfterSaleFindByPage({ type: 2 }).then((data) => data.data.data.data),
        // eslint-disable-next-line no-return-assign
        this.getRoadSaveServiceData().then((data) => (this.orderList[2].list = data || [])),
        // eslint-disable-next-line no-return-assign
        this.getInsaicServiceData().then((data) => (this.orderList[3].list = data || [])),
        // getAfterSaleFindByPage({type:2}).then((data) => data.data.data.data),
        // 充电桩
        // getAfterSaleFindByPage({ type: 3 }).then((data) => data.data.data.data),
        // eslint-disable-next-line no-return-assign
        getVeryLongReservationOrderList().then(({ data }) => (this.orderList[5].list = data?.data || []))
      ])
      // console.log(datas)

      //   const { data } = await getAfterSaleFindByPage()

      // this.transformData(datas)

      if (this.orderList.length > 0) {
        this.showNotData = false
      } else {
        this.showNotData = true
      }
    },

    toServiceList(index,length) { 
      console.log(index)
      let len = length <= 0;

      const { origin, pathname } = window.location
      let url
      if (index === 0) {
        url = `${origin}${pathname}#/aftersales/afterservice-order-list?type=1${this.purpleState && len ? '&fromType=fromPurple' : ''}`
      } else if (index === 1) {
        url = `${origin}${pathname}#/aftersales/afterservice-order-list?type=2${this.purpleState && len ? '&fromType=fromPurple' : ''}`
      } else if (index === 2) {
        url = `${origin}${pathname}#/aftersales/roadservice-order-list${this.purpleState && len ? '?fromType=fromPurple' : ''}`
        // url = `${origin}${pathname}#/aftersales/insaicservice-order-list`;
      } else if (index === 3) {
        url = `${origin}${pathname}#/aftersales/insaicservice-order-list${this.purpleState && len ? '?fromType=fromPurple' : ''}`
      } else if (index === 4) {
        // 充电桩
        // url = `${origin}${pathname}#/testdrive/long-service-list`;
        url = `${origin}${pathname}#/aftersales/afterservice-order-list?type=3${this.purpleState && len ? '&fromType=fromPurple' : ''}`
      } else if (index === 5) {
        // 超长试驾
        url = `${origin}${pathname}#/testdrive/long-service-list${this.purpleState && len ? '?fromType=fromPurple' : ''}`
      }

      console.log(url)
      callNative('audiOpen', { path: url })
    },

    transformData(datas) {
      for (let index = 0; index < datas.length; index++) {
        // if (index === 0) {
        //   // const afterSaleList = [];
        //   // const getCarsList = [];
        //   // for (const item of datas[index]) {
        //   //   if (item.type === 1) {
        //   //     afterSaleList.push(item);
        //   //   } else {
        //   //     getCarsList.push(item);
        //   //   }
        //   // }
        //   // console.log("getCarsList", getCarsList);
        //   this.$set(this.orderList[0], "list", datas[0]);
        //   this.$set(this.orderList[1], "list", datas[2]);
        // } else if (index === 1) {
        //   this.$set(this.orderList[2], "list", datas[1]);
        // } else if (index === 2) {
        //   this.$set(this.orderList[3], "list", datas[3]);
        // } else if (index === 3) {
        //   this.$set(this.orderList[4], "list", datas[4]);
        // }
        // else if (index === 2) {
        //   this.$set(this.orderList[2], "list", datas[2]);
        // }
        this.$set(this.orderList[index], 'list', datas[index])
      }
    },

    async toOrderDetail(item) {
      if (item.type === 1) {
        // 售后预约
        // this.$router.push({
        //   path: "/aftersales/service-appointment-order-detail",
        //   query: {
        //     appoId: item.appoId,
        //   },
        // });

        const { origin, pathname } = window.location
        const url = `${origin}${pathname}#/aftersales/service-appointment-order-detail?appoId=${item.appoId}`
        callNative('audiOpen', { path: url })
      } else if (item.type === 2) {
        // 取送车
        // this.$router.push({
        //   path: "/aftersales/deliver-car-order-detail",
        //   query: {
        //     appoId: item.appoId,
        //   },
        // });
        const { origin, pathname } = window.location
        const url = `${origin}${pathname}#/aftersales/deliver-car-order-detail?appoId=${item.appoId}`
        callNative('audiOpen', { path: url })
      } else if (item.type === 3) {
        // 充电桩服务
        const { origin, pathname } = window.location
        const url = `${origin}${pathname}#/charging/install-info?appoId=${item.appoId}`
        callNative('audiOpen', { path: url })
      }

      // // 跳转详情
      //
    },
    onCancelTake() {
      this.isTake = !this.isTake
      if (this.isTake) {
        this.isSend = false
        this.isAll = false
      }
    },
    onCancelSend() {
      this.isSend = !this.isSend
      if (this.isSend) {
        this.isTake = false
        this.isAll = false
      }
    },
    onCancelTakeAndSend() {
      this.isAll = !this.isAll
      if (this.isAll) {
        this.isTake = false
        this.isSend = false
      }
    },

    onCancel() {
      this.isTake = false
      this.isSend = false
      this.isAll = false
      this.modalshow = false
    },
    async onConfirm() {
      let type = 1
      if (this.isAll) {
        type = 4
        this.orderId = `${this.onCliickOrderItem.deliverVo.takeOrderId},${this.onCliickOrderItem.deliverVo.sendOrderId}`
      } else if (this.isTake) {
        type = 1
        this.orderId = this.onCliickOrderItem.deliverVo.takeOrderId
      } else if (this.isSend) {
        type = 3
        this.orderId = this.onCliickOrderItem.deliverVo.sendOrderId
      } else {
        this.modalshow = false
        return
      }

      this.modalshow = false
      this.isTake = false
      this.isSend = false
      this.isAll = false

      const { origin, pathname } = window.location
      const url = `${origin}${pathname}#/aftersales/cancel-service-order?orderId=${this.orderId}&type=${type}`
      callNative('audiOpen', { path: url })
    },

    setOrderName(item) {
      if (item.type === 1) {
        return '服务预约'
      }
      if (item.type === 3) {
        return '充电桩安装'
      }
      if (item.type === 2) {
        if (item.deliverVo.subType === 20131010) {
          return '取车服务'
        } if (item.deliverVo.subType === 20131020) {
          return '送车服务'
        }
        return '取送车服务'
      }
    },
    setStatusDesc(item) {
      if (!item || !Object.keys(item).length || !item.type) {
        return ''
      }
      if (item.type === 1) {
        return item.statusName
      }
      if (item.type === 2) {
        if (item.deliverVo.subType === 20131010) {
          return item.deliverVo.takeOrderStatusName
        } if (item.deliverVo.subType === 20131020) {
          return item.deliverVo.sendOrderStatusName
        }
        if (item.deliverVo.takeOrderStatus === 20101130 && item.deliverVo.sendOrderStatus === 20101130) {
          return '订单已取消'
        } if (item.deliverVo.takeOrderStatus === 20101120 && item.deliverVo.sendOrderStatus === 20101120) {
          return '订单已完成'
        }
        if (item.deliverVo.takeOrderStatus !== 20101130 && item.deliverVo.takeOrderStatus !== 20101120) {
          return `取车-${item.deliverVo.takeOrderStatusName}`
        }
        return `送车-${item.deliverVo.sendOrderStatusName}`
      } if (item.type === 3) {
        return item.statusName
      }
    },
    isShowCancel(item) {
      // 售后
      if (item.type === 1) {
        return item.isCancel === 1
      } if (item.type === 2) {
        // 取送车
        if (item.deliverVo.subType === 20131010) {
          return item.deliverVo.takeIsCancel === 1
        } if (item.deliverVo.subType === 20131020) {
          return item.deliverVo.sendIsCancel === 1
        }
        return (
          item.deliverVo.takeIsCancel === 1
            || item.deliverVo.sendIsCancel === 1
        )
      } if (item.type === 3) {
        return item.isCancel === 1
      }
    },
    async getOrderCarConfig(carCustomId) {
      const { data } = await getCarConfig({ ccid: carCustomId })
      if (data.data) {
        return data.data.configDetail.carModel
      }
      return ''
    },
    // 取消订单
    async onCancelOrder(item) {
      this.onCliickOrderItem = item

      if (item.type === 2 && item.deliverVo.subType === 20131030) {
        if (item.deliverVo.takeIsCancel === 0) {
          // 取车不可取消了
          this.title = '请问您是否确认取消送车服务？'

          if (item.deliverVo.takeOrderStatus !== 20101130) {
            this.content = '注意：取车服务已开始，无法取消'
          } else {
            this.content = '注意：取车服务已取消'
          }
          this.isSend = true
        } else if (item.deliverVo.sendIsCancel === 0) {
          // 取车不可取消了
          this.title = '请问您是否确认取消取车服务？'
          if (item.deliverVo.sendOrderStatus !== 20101130) {
            this.content = '注意：送车服务已开始，无法取消'
          } else {
            this.content = '注意：送车服务已取消'
          }
          this.isTake = true
        } else {
          this.title = '请选择取消以下哪项服务？'
          this.content = ''
        }
        this.modalshow = true
      } else {
        // 调用APP Dialog
        callNative('popup', {
          type: 'alert',
          alertparams: {
            title: '',
            desc: '是否确定取消订单？',
            actions: [
              {
                type: 'fill',
                title: '确定'
              },
              {
                type: 'stroke',
                title: '取消'
              }
            ]
          }
        }).then((data) => {
          if (data.type === 'fill') {
            // 点击确定
            this.cancelOrder(item)
          }
        })
      }
    },
    async cancelOrder(item) {
      if (item.type === 1) {
        const { data } = await getAfterSaleCancel({ orderId: item.appoId,version:'v2' })
      } else if (item.type === 2) {
        // 取送车
        let orderId = ''
        let type = 1
        if (item.deliverVo.subType === 20131010) {
          orderId = item.deliverVo.takeOrderId
          type = 1
        } else if (item.deliverVo.subType === 20131020) {
          type = 3
          orderId = item.deliverVo.sendOrderId
        } else {
          orderId = `${item.deliverVo.takeOrderId},${item.deliverVo.sendOrderId}`
          type = 4
        }

        const { origin, pathname } = window.location
        const url = `${origin}${pathname}#/aftersales/cancel-service-order?orderId=${orderId}&type=${type}`
        callNative('audiOpen', { path: url })
      } else if (item.type === 3) {
        // 取消充电桩服务
        const { data } = await postChargingPileCancel({ appointmentOrderId: item.appoId })
      }

      this.onRefresh()
    }
  }
}
</script>

<style lang="less" scoped>
.not_data {
  text-align: center;
  padding-top: 150px;
  img {
    width: 76px;
    height: 76px;
  }
}
.order_container {
  padding: 16px 16px 12px 16px;
  font-family: "Audi-Normal";
  border-bottom: 0.6px solid #eee;
  .order_header {
    font-size: 16px;
    font-family: "Audi-WideBold";
    overflow: hidden;
    height: 44px;
    line-height: 44px;
    img {
      // float: right;
      width: 22px;
      height: 22px;
      object-fit: scale-down;
      // position: relative;
      // top: 2px;
    }
  }

  .order-status {
    margin: 8px 0;
    height: 20px;
    line-height: 20px;
    color: #262626;
    font-size: 12px;
    .text {
      color: #262626;
      font-family: Audi-ExtendedBold;
    }
  }

  .order-bottom {
    margin-top: 8px;
    .btn-box {
      .btn {
        color: #333;
        border: solid 0.6px #333;
        width: 93px;
        height: 32px;
        line-height: 32px;
        font-size: 12px;
        color: #333;
        text-align: center;
      }
    }
  }

  .order_content {
    display: flex;
    flex-direction: row;
    // padding-top: 10px;
    &_left {
      width: 102px;
      height: 102px;
      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }
    &_right {
      flex: 1;
      margin-left: 10px;
      margin-top: 5px;
      &_top {
        overflow: hidden;
        .order_name {
          font-family: "Audi-WideBold";
          float: left;
        }
        .order_state {
          float: right;
          font-size: 12px;
          color: #666666;
        }
      }

      &_mid {
        font-size: 12px;
        color: #666666;
        margin-top: 8px;
      }

      &_bottom {
        display: flex;
        flex-direction: row-reverse;
        margin-top: 38px;
      }
    }
  }
}

 .center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ._modal {
    width: 343px;
    background: #FFFFFF;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    padding: 24px 0;

    .modal-title {
      font-size: 18px;
      font-weight: 500;
      color: #1A1A1A;
      margin-bottom: 16px;
      font-family: 'Audi-WideBold';
    }

    .modal-content {
      font-size: 14px;
      color: #333333;
      line-height: 18px;
      padding: 0 16px;
      font-family: 'Audi-Normal';
    }
    .service-line {
      width: 295px;
      height: 48px;
      background: #f2f2f2;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-bold {
        padding-left: 16px;
        font-size: 16px;
        color: #000;
        font-weight: normal;
        font-family: 'Audi-Normal';

      }
      .btn-change {
        padding-right: 16px;
        .btn-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
    .modal-confirm {
      margin-top: 24px;
      width: 85%;
      height: 56px;
      background: #1A1A1A;
      font-size: 16px;
      color: #FFFFFF;
    }

    .modal-cancel {
      width: 85%;
      border: 1px solid #1A1A1A;
      height: 56px;
      background: #fff;
      font-size: 16px;
      color: #000;
      margin-top: 8px;
    }

    .modal-bold-content {
      font-size: 18px;
      color: #1A1A1A;
      line-height: 32px;
      padding: 0 25px;
      font-weight: normal;
      font-family: "Audi-WideBold";
    }
  }

  @purple-txt-color:#efefef;

  .purpleActiveStyle{
    background: #191919;
    .order_container{
      border-color: rgba(250,250,250, .1);
    }
    span{
      color: @purple-txt-color;
    }
    .text{
      color: @purple-txt-color !important;
    }
    .order_content_left {
      img{
        border-radius: 10px;
      }
    }
  }
</style>
