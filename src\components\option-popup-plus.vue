<template>
  <div class="option-popup">
    <div
      class="option-cover"
    />
    <div class="option-main">
      <header>
        <div>请做出一个选择</div>
      </header>
      <main>
        <option-popup-card
          title="您想增加："
          type="text"
          :list="[defOption.find(e => e.optionCode == showOptionPopupPlus.code)]"
        />
        
        <option-popup-card-plus
          v-if="dependSortArray && dependSortArray.length > 0 && currentSibColorInterieur.sibOptionCode == 'N4X' && selectCarInfo.modelLineName.includes('星耀')"
          :title="clickOption.interieurName?'您需要在下方装备中选择一个加装：':'您必须选择以下选装：'"
          type="checkbox"
          :list="dependSortArray"
        />
        <div v-else>
          <option-popup-card
            v-if="dependSortArray && dependSortArray.length > 0"
            :title="clickOption.interieurName?'您需要在下方装备中选择一个加装：':'您必须选择以下选装：'"
            type="checkbox"
            :list="dependSortArray"
          />
        </div>
        
        <div  v-if="conflictList">
          <option-popup-card
            v-if="conflictList"
            title="以下选装配置将会被移除："
            type="imgbox"
            :list="conflictList"
          />
        </div>
      
      </main>
      <footer>
        <div class="footer-desc" v-if="selectCarInfo.seriesName != 'Q6'">
          <span>当前选项的价格已更新：</span>
          <span>¥{{ price | formatPrice }}</span>
        </div>
        <div
          class="button confirm"
          @click="handleConfirm"
        >
          确认
        </div>
        <div
          class="button cancel"
          @click="handleCancel"
          v-if="!clickOption.interieurName"
        >
          取消
        </div>
      </footer>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { Toast } from 'vant'
import {
  resolveConflict, outputChangeList, outputCurrentOptionsList, outputData
} from '@/utils/sp'
import url from '@/config/url'
import OptionPopupCard from './option-popup-card.vue'
import OptionPopupCardPlus from './option-popup-card-plus.vue'
import { arraySortByKeys } from '@/utils'

import {
  defPatchesFn04, patchesPS138,defPatchesWA3 
} from '@/view/configration/fix_configration/ccFn.js'



export default {
  components: { OptionPopupCard, OptionPopupCardPlus },
  data() {
    return {
      dependList: [],
      dependSortArray: [],
      conflictList: [],
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      price: 0,
      defOption: [
        {
          "optionId": "589c4507-9125-4a2c-8e39-24a49f5260b7",
          "optionName": "前排座椅通风",
          "optionCode": "4D3",
          "category": "KMS",
          "optionType": "personal",
          "optionTypeName": "私人订制",
          "imageUrl": "/ccpro-backend/q5e/option/master/4D3%2B7P1.png",
          "imageUrlDetail": "/ccpro-backend/q5e/option/master/4D3%2B7P1_detail.png",
          "imageUrlList": "/ccpro-backend/q5e/option/master/4D3%2B7P1_list.png",
          "description": null,
          "remark": null,
          "defaultConfig": 0,
          "status": 2,
          "price": 7878,
          "detailPageHidden": null,
          "optionDetailList": [],
          "packetItems": null,
          "optionRelates": null
        }, 
        {
          "optionId": "8348848f-f72a-4cd2-90ce-e3658c93da5a",
          "optionName": "前排座椅按摩",
          "optionCode": "8I6",
          "category": "LOR",
          "optionType": "personal",
          "optionTypeName": "私人订制",
          "imageUrl": "/ccpro-backend/q5e/option/master/8I6%2B4D0.png",
          "imageUrlDetail": "/ccpro-backend/q5e/option/master/8I6%2B4D0_detail.png",
          "imageUrlList": "/ccpro-backend/q5e/option/master/8I6%2B4D0_list.png",
          "description": null,
          "remark": null,
          "defaultConfig": 0,
          "status": 2,
          "price": 35854,
          "detailPageHidden": null,
          "optionDetailList": [],
          "packetItems": null,
          "optionRelates": null
      }
      ]
    }
  },
  computed: {
    ...mapState([
      'showOptionPopupPlus',
      'idx',
      'clickOption',
      'otherClickOption',
      'selectCarInfo',
      'currentOptionsList',
      'interiorChairList',
      'sibColorInterieurList',
      'interiorEihList',
      'modelHubList',
      'changeList',
      'currentModelHub',
      'currentInteriorChair',
      'currentInteriorEih',
      'currentSibColorInterieur',
      'privateOrderList',
      'allSibColorInterieurList',
      'allInteriorChairList'
    ]),
    ...mapGetters(['packetItem', 'allOptionsItems'])
  },
  mounted() {
    if ('G4IBC3001,G4ICC3001'.includes(this.selectCarInfo.modelLineCode) && 'N4X'.includes(this.clickOption.sibOptionCode)) {
      const tmp = this.privateOrderList.find((e) => e.optionCode === '4A4')
      this.$store.commit('setOtherClickOption', tmp)
    }
  },
  watch: {
    clickOption: {
      handler(next) {
        if (next) {
          // clickOption  当前点击的选装包
          // allOptionsItems  当前选装包中的选装件
          // selectedCarInfo  当前选中的配置线
          // currentOptionslList  当前的选中的选装包（件）列表
          // chair： 当前选中的座椅
          // sibcolor ： 当前选中的面料````
          const _obj = {
            clickOption: this.clickOption,
            allOptionsItems: this.allOptionsItems,
            selectCarInfo: this.selectCarInfo,
            currentOptionsList: this.currentOptionsList,
            currentInteriorChair: this.currentInteriorChair,
            currentSibColorInterieur: this.currentSibColorInterieur,
            currentInteriorEih: this.currentInteriorEih,
            price: next.price,
            q6: this.idx == 2
          }
          const _result = resolveConflict(_obj)
          // arraySortByKeys
          let dependSortArray
          if (_result.tempdependList && _result.tempdependList.length > 0) {
            dependSortArray = arraySortByKeys(_result.tempdependList, ['category', 'sibInterieurCategory'])
            if (this.idx == 2 && dependSortArray && dependSortArray[0]?.children) {
              let sib =  this.currentSibColorInterieur.sibInterieurCode
              let obj = dependSortArray[0]?.children?.find(e => e.sibInterieurCode == sib)
              if (obj && sib) {
                dependSortArray = []
              }
            }
          }

// 齐云飞骑、齐云羽林
          let modelLineCode = this.selectCarInfo.modelLineCode
          let sib = this.$store.state.currentSibColorInterieur?.sibInterieurCode
          let WTY = this.clickOption.optionCode == "WTY"
          let G6IBAY001 = 'G6IBAY001'.includes(modelLineCode) && '"N5D-TX,N5D-TT"'.includes(sib)
          let G6ICAY001 = 'G6ICAY001'.includes(modelLineCode) && "N5D-AW".includes(sib)
          let ps1 = this.currentOptionsList.find(e => e.optionCode == 'PS1')
          if (WTY && (G6IBAY001 || G6ICAY001) && ps1) {
            let children =  this.$store.state.allPrivateOrderList
            let arr = JSON.parse(JSON.stringify(children))
            let temp =  arr.filter(e=> "PS8,PS3".includes(e.optionCode))
            temp.forEach(e => {
              e.defVis = 1
            })
            dependSortArray = [
              {
                children: temp
              }
            ]
            this.$store.commit('setOtherClickOption', temp[0])
          }

          let confCodeArr = []
          let confOpion = []
          if (this.idx == 2 && this.clickOption?.optionRelates && this.clickOption?.optionRelates?.length > 0) {
            this.clickOption?.optionRelates.forEach(e => {
              if (e.relateType == "conflict" && e.optionRelateCategory == "PACKET") {
                confCodeArr.push(e.optionRelateCode)
              }
            });
            confCodeArr.forEach(e => {
              let obj = this.currentOptionsList.find(o => o.optionCode == e)
              if (obj) {
                confOpion.push(obj)
              }
            });

            if (confOpion.length > 0) {
               this.conflictList = confOpion
            }
          }

          this.price = _result.price
          this.dependSortArray = dependSortArray
          this.dependList = _result.tempdependList
          if (this.idx != 2) this.conflictList = _result.tempconflictList
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleClickCover: function () {
    },
    handleCancel: function () {
      this.$store.commit('setShowOptionPopupPlus', {bool: false, code: ''})
    },
    handleConfirm: function () {
      this.$emit("handleEventFn", this.showOptionPopupPlus.index)
    }

  },
  created() {
    this.conflictList = [this.defOption.find(e => e.optionCode != this.showOptionPopupPlus.code)]
  },
}
</script>

<style lang="less" scoped>
.option-popup {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    .option-cover {
        width: 100%;
        height: 100%;
        position:absolute;
        top: 0;
        left: 0;
        background: rgba(0,0,0,.5);
        z-index: -1;
    }
    .option-main{
        width: calc(100% - 32px);
        height: 80%;
        background-color: #fff;
        margin: auto 16px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        padding: 16px;
        box-sizing: border-box;
        header {
            font-size: 14px;
            font-family: Audi-WideBold;
            color: #000000;
            line-height: 20px;
            margin-bottom: 13px;
        }
        main {
          height: calc(100% - 167px - 32px);
          overflow-y: auto;
          -ms-overflow-style: none;
          overflow: -moz-scrollbars-none;
          &::-webkit-scrollbar { width: 0 !important }
        }
        footer{
          width: calc(100% - 32px);
          position: absolute;
          bottom: 16px;
          display: flex;
          flex-direction: column;
          border-top: 1px solid #e5e5e5;
          .footer-desc {
            font-size: 12px;
            font-family: Audi-WideBold;
            color: #000000;
            line-height: 50px;
            display: flex;
            justify-content:space-between;
          }
          .button {
            width: 100%;
            height: 56px;
            background: #fff;
            border: 1px solid #1A1A1A;
            box-sizing: border-box;
            display: flex;
            justify-content:center;
            align-items: center;
            &.confirm{
              background: #1A1A1A;
              color: #fff;
              margin-bottom: 4px;
            }
          }
        }
    }
}
</style>
