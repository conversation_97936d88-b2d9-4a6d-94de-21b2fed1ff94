
<template>
  <div class="interior-wrapper">
    <div class="collapse-wrapper">

      <div class="mainimg-wrapper">
        <ShowModelButton type="in"/>
        <img :src="currnetMainImgUrl | imgFix(640, true)" v-if="currnetMainImgUrl" alt="">
        <DescriptionText bottom="15px"/>
      </div>

      <div class="wrapper-scroll">
        <!-- 面料颜色列表 -->
        <div class="content-wrapper">
          <div class="desc-wrapper">
            <div class="c-font14 c-bold c-lh22">
              {{ localCurrentSib.description }}
            </div>
            <div class="desc c-lh20">
              {{ currentSib.price | finalFormatPriceDesc }}
            </div>
          </div>
          <div class="sib-wrapper">
            <div class="wrapper">
              <div v-for="item in pageSibArray" :key="item.sibInterieurCode" class="item" :class="{
                selected: item.sibInterieurCode === localCurrentSib.sibInterieurCode,
                disabled: item.disabled
              }" @click="toSelectSib(item)">
                <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(250, true)" alt="">
              </div>
            </div>
          </div>

          <!-- 饰板列表 -->
          <div class="desc-wrapper c-margin-top">
            <div class="c-font14 c-bold c-lh22">
              {{ localCurrentEih.optionName }}
            </div>
            <div class="desc c-lh20">
              {{ currentEih.price | finalFormatPriceDesc }}
            </div>
          </div>
          <div class="eih-wrapper">
            <div class="wrapper">
              <div v-for="item in pageEihArray" :key="item.optionCode" class="item" :class="{
                selected: item.optionCode === localCurrentEih.optionCode,
                disabled: item.disabled
              }" @click="toSelectEih(item)">
                <img :src="BaseConfigrationOssHost + item.imageUrl | imgFix(250, true)" alt="">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <CommonFooter @click="nextPage" />
  </div>
</template>

<script>
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import { Toast } from 'vant'
import CommonFooter from './components/commonFooter.vue'
import url from '@/config/url'
import { getUrlParamObj, isEmptyObj } from '@/utils'
import { getFilterRelateList } from '@/view/newConfigration/util/helper'
import ShowModelButton from './components/showModelButton.vue'
import { BLACK_A7L } from './util/carModelSeatData'
import { A7MR, A7_VOS_OPTION, DONGGAN_CODE } from '@/view/newConfigration/car/a7mr'
import DescriptionText from './components/descriptionText.vue'

Vue.use(Toast)
// 设置Toast 持续时间 debugger
// Toast.setDefaultOptions({ duration: 1000000 })


const { env } = getUrlParamObj()
const isMinip = env === 'minip'

const OSS_URL = url.BaseConfigrationOssHost

export default {
  name: 'ConfigrationInterior',
  components: { CommonFooter, ShowModelButton, DescriptionText },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      isMinip: isMinip,
      pageStartTime: 0
    }
  },
  computed: {
    ...mapGetters([
      'currentSeriesName',
      'pageSibArray',
      'pageEihArray',
      'paramSibDependOptionList',
      'currentCarType'
    ]),
    ...mapState({
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentExterior: (state) => state.configration.currentExterior,
      currentHub: (state) => state.configration.currentHub,
      selectedOptions: (state) => state.configration.selectedOptions,
      personalOptions: (state) => state.configration.personalOptions,
      vosArray: (state) => state.configration.vosArray,
      currentSib: (state) => state.configration.currentSib,
      currentEih: (state) => state.configration.currentEih,
      currentVos: (state) => state.configration.currentVos,
      configrationActiveTab: (state) => state.configration.configrationActiveTab,
      currentVersion: (state) => state.configration.currentVersion,
      carIdx: (state) => state.configration.carIdx,
      referConfigrationActiveTab: (state) => state.configration.referConfigrationActiveTab,

      localEihName: (state) => {
        const { pageEihArray } = state.configration
        const eih = pageEihArray.find((i) => i.selected)
        return eih?.optionName ?? ''
      }
    }),

    // 本地UI选中的内饰面料数据,只在UI表示状态
    localCurrentSib() {
      const sib = this.pageSibArray.find((i) => i.selected)
      return sib || this.currentSib
    },

    // 本地UI选中的内饰面料数据,只在UI表示状态
    localCurrentEih() {
      const eih = this.pageEihArray.find((i) => i.selected)
      return eih || this.currentEih
    },

    currentA7MrMainImgUrl() {
      // 内饰图命名规则：黑顶棚-座椅形态-面料色-面料材质-饰板：
      // https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a7l/tmp/interieur/20241/6NQ-Q1D-MP-N6L-5TG.png
      const seriesName = this.currentSeriesName
      if (!seriesName) return null
      let balckTopCode = ''
      // 黑车顶
      const mrItem = A7MR.find((i) => i.code === this.currentModelLineData.modelLineCode)
      if (!mrItem) {
        return
      }
      const option6NQ = this.selectedOptions.map((i) => i.optionCode).includes('6NQ')
      if (mrItem.blackTop || option6NQ || DONGGAN_CODE.includes(this.currentModelLineData.optionCode)) {
        balckTopCode = '6NQ-'
      }
      // 座椅形态
      let chairCode = mrItem.vos
      const vosOption = this.paramSibDependOptionList.find((item) => item.optionRelateCode === A7_VOS_OPTION.option)
      if (vosOption) {
        chairCode = A7_VOS_OPTION.vos // Q2J
      }
      // 面料色
      const interieurCode = this.currentSib.interieurOptionCode
      // 面料材质
      const sibCode = this.currentSib.sibOptionCode
      // 饰板
      const eihCode = this.currentEih.optionCode
      if (!eihCode) return
      // https:// sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a7l/tmp/interieur/20241/Q1D-ZW-N6L-5TG.png
      // return 'https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/a7l/tmp/interieur/20241/6NQ-Q1D-MP-N6L-5TG.png'
      return `${OSS_URL}/ccpro-backend/${seriesName}/tmp/interieur/20241/${balckTopCode}${chairCode}-${interieurCode}-${sibCode}-${eihCode}.png`
    },

    currnetMainImgUrl() {
      const seriesName = this.currentSeriesName
      if (!seriesName) return null

      const modelLineCode = this.currentModelLineData.modelLineCode

      /**
       * 如果是A7MR车型,则使用A7MR的内饰图
       */
      if (A7MR.find((i) => i.code === modelLineCode)) {
        return this.currentA7MrMainImgUrl
      }

      if (this.currentVos && seriesName !== 'q5e') {
        let arr = []
        const CHAIR_CODE = 'Q4Q' // A7L 超级运动座椅  | Q6 前排运动座椅带一体式运动头枕
        const SPECIAL_MODEL_CODES = ['498B2Y004', '498B2Y003'] // 奥迪A7L 境远型 曜黑套装 , 奥迪A7L 境远型 流晶套装
        // 座椅
        if (this.currentVos.optionCode === CHAIR_CODE && SPECIAL_MODEL_CODES.includes(modelLineCode)) {
          return `${OSS_URL}/ccpro-backend/${seriesName}/tmp/interieur/${CHAIR_CODE}.png`
        }
        // 选装
        const OPTION_CODE = '6NQ' // A7L 黑色车内顶棚
        const hsdp = this.selectedOptions.find((item) => item.optionCode === OPTION_CODE)
        if (hsdp) {
          arr.push(OPTION_CODE)
        }

        if (BLACK_A7L.includes(modelLineCode)) { // 奥迪A7L 见远型 曜黑套装
          arr.push(OPTION_CODE)
        }
        if (Object.keys(this.currentSib).length > 0 && Object.keys(this.currentVos).length > 0 && Object.keys(this.currentEih).length > 0) {
          let imgName = ''
          if (seriesName === 'q6') {
            // 座椅+面料+内饰颜色+饰条
            // https://sx-audi.oss-cn-shanghai.aliyuncs.com/ccpro-backend/q6/tmp/interieur/G6IBAY001/Q2J-N5D-TT-7TL.png
            arr = [
              this.currentVos.optionCode,
              this.currentSib.sibInterieurCode,
              this.currentEih.optionCode
            ]
            imgName = arr.join('-')
            return `${OSS_URL}/ccpro-backend/${seriesName}/tmp/interieur/${modelLineCode}/${imgName}.png`
          }
          arr = [
            ...arr,
            this.currentSib.interieurOptionCode,
            this.currentVos.optionCode,
            this.currentEih.optionCode
          ]
          imgName = arr.join('-')

          if (modelLineCode === '498BZG004') { // 筑梦新生版
            imgName = `N5J-${imgName}`
          }
          return `${OSS_URL}/ccpro-backend/${seriesName}/tmp/interieur/${imgName}.png`
        }
        return null
      }


      if (seriesName === 'q5e') {
        let arr = []
        let imgName = ''
        if (Object.keys(this.currentSib).length > 0 && Object.keys(this.currentEih).length > 0) {
          arr = [...arr, this.currentSib.sibInterieurCode, this.currentEih.optionCode]

          imgName = arr.join('-')
          // q5e车型 内饰大图 区分锦衣和机甲 锦衣的2D图是白顶棚，机甲是黑色
          // const JIJIA_MODEL_CODE = ['G4ICC3001', 'G4ICF3001', 'G4ICC3007', 'G4ICF3007']

          const JIJIA_MODEL_LINE = this.currentModelLineData.modelLineName.includes('机甲')

          if (JIJIA_MODEL_LINE) {
            imgName = `6NQ-${imgName}`
          }

          return `${OSS_URL}/ccpro-backend/${seriesName}/tmp/interieur/${imgName}.png`
        }
      }
      return null
    }
  },
  watch: {
    currentSib(sib) {
      // 更新饰板
      this.resetEihStatus()
      this.checkRelatePacket()
    },

    currentExterior(outColor) {
      // this.resetSibStatus()
    },

    configrationActiveTab(value) {
      if (value === 'interior') {
        this.pageStartTime = Date.now()
      }
    }

  },
  mounted() {
    this.pageStartTime = Date.now()
    this.checkRelatePacket()
  },

  methods: {
    // 饰板
    setDefaultEih() {
      const availableList = this.pageEihArray.filter((i) => !i.disabled)
      // console.log('可选的饰板', this.pageEihArray, availableList)
      const res = availableList.find((i) => i.status === 1) || availableList[0]
      if (!res) {
        return console.error('没有可选的饰板')
      }
      this.toSelectEih(res)
    },

    /**
     * 选择面料
     * 新的点击置灰逻辑:
     * UI要变为选中状态,且不再置灰
     * 此时点击下一步或者 tab 无法切换提示.
     */
    async toSelectSib(item) {
      if (item.sibInterieurCode === this.localCurrentSib.sibInterieurCode) {
        return
      }

      this.pageSibArray.forEach((i) => {
        i.selected = i.sibInterieurCode === item.sibInterieurCode
      })

      if (item.disabled) {
        Toast('您选择的装备需您先更换外饰颜色')
        this.$store.commit('updateFooterDesc', {
          desc: '您选择的装备需您先更换外饰颜色'
        })
        return
      }
      this.$store.dispatch('clearOptionData') // 清理选装的数据
      this.$store.dispatch('resetPageAllOptionState')

      this.$store.commit('updateCurrentSib', item)

      this.$store.dispatch('setSibRelatePacket') // 选中关联选装包
      this.$store.dispatch('setSibConflictPacket') // 删除互斥的选装包

      this.$store.commit('showLoading')
      await this.$store.dispatch('setTimeAndPrice')
      this.$store.commit('hideLoading')
      // 检查当前面料与选装包的关联
      this.checkRelatePacket()
    },

    // 选择饰板
    toSelectEih(item) {
      // console.log('选择饰板', item);
      if (item.selected && item.optionCode === this.currentEih.optionCode) {
        return
      }

      this.pageEihArray.forEach((i) => {
        i.selected = i.optionCode === item.optionCode
      })

      if (item.disabled) {
        Toast('您选择的装备需您先更换内饰颜色')
        this.$store.commit('updateFooterDesc', {
          desc: '您选择的装备需您先更换内饰颜色'
        })
        return
      }

      // 饰板关联选装包则提示
      const depends = getFilterRelateList(item.optionRelates, 'depend')
      const eihDepends = depends.filter((i) => i.optionRelateCategory === 'PACKET')
      if (eihDepends.length > 0) {
        Toast('当前饰板包含组合的选装套装')
        this.$store.commit('updateFooterDesc', {
          desc: '当前饰板包含组合的选装套装，点击下一步查看'
        })
      }

      this.$store.dispatch('clearOptionData') // 清理选装的数据
      this.$store.dispatch('resetPageAllOptionState')

      this.$store.dispatch('setSibRelatePacket') // 选中关联选装包
      this.$store.commit('updateCurrentEih', item)
      this.$store.dispatch('setTimeAndPrice')
    },

    // 内饰面料的disabled状态
    resetSibStatus() {
      const conflicts = getFilterRelateList(this.currentExterior.optionRelates, 'conflict')

      for (const sib of this.pageSibArray) {
        sib.disabled = false
      }

      for (const item of conflicts) {
        for (const sib of this.pageSibArray) {
          if (sib.sibInterieurCode === item.optionRelateCode) {
            sib.disabled = true
          }
        }
      }

      // 兼容只开A类的车型,当前面料列表动态更新后,与当前已选面料不匹配的情况
      const isExistSib = this.pageSibArray.find((i) => i.sibInterieurCode === this.currentSib.sibInterieurCode)

      // 如果当前已选中的面料被禁用了, 则重置为默认的面料
      const disabledSib = this.pageSibArray.find((sib) => sib.sibInterieurCode === this.localCurrentSib.sibInterieurCode)
      if (disabledSib?.disabled || !isExistSib) {
        const availableSib = this.pageSibArray.find((i) => !i.disabled)
        this.$store.commit('updateCurrentSib', availableSib)
      }
    },

    // 设置饰板的禁用状态
    resetEihStatus() {
      const relates = this.currentSib.sibInterieurRelates
      const conflicts = getFilterRelateList(relates, 'conflict')
      const disabledEihs = conflicts.filter((i) => i.optionRelateCategory === 'EIH')
      if (disabledEihs.length === 0) {
        return
      }

      // 设置饰板的禁用状态
      for (const eih of this.pageEihArray) {
        eih.disabled = false
        for (const item of disabledEihs) {
          if (eih.optionCode === item.optionRelateCode) {
            eih.disabled = true
          }
        }
      }

      //
      /**
       * 重新选择饰板的逻辑:
       * 1. 当前已选中的饰板被禁用了
       * 2. 当前的饰板被过滤了
       */
      const isExist = this.pageEihArray.find((eih) => eih.optionCode === this.currentEih.optionCode)
      if (!isExist || isExist.disabled) {
        this.setDefaultEih()
      }
    },

    // 检查关联的选装包
    checkRelatePacket() {
      const packetDepends = this.paramSibDependOptionList // 当前内饰依赖的选装列表

      if (packetDepends.length > 0) {
        Toast('当前内饰包含组合的选装套装')

        this.$store.commit('updateFooterDesc', {
          desc: '当前内饰包含组合的选装套装，点击下一步查看'
        })

        // 更新当前选装包关联的vos座椅信息
        const defaultDepend = packetDepends.find((i) => i.defaultDepend === 1) || packetDepends[0]
        const option = this.personalOptions.find((i) => i.optionCode === defaultDepend.optionRelateCode)
        if (option) {
          const vos = option.packetItems.find((i) => i.category === 'VOS')
          if (vos) {
            this.$store.commit('updateCurrentVos', vos)
          }
        }
      } else {
        // 恢复座椅
        const defaultVos = this.vosArray.find((i) => i.status === 1) ?? this.vosArray[0] ?? {}
        this.$store.commit('updateCurrentVos', defaultVos)
      }
    },

    // 下一步
    nextPage() {
      if (this.localCurrentEih.disabled) {
        return Toast('您选择的装备需您先更换内饰颜色')
      }
      if (this.localCurrentSib.disabled) {
        return Toast('您选择的装备需您先更换外饰颜色')
      }
      this.clickInteriorSensors()// 埋点
      this.$store.commit('updateConfigrationActiveTab', 'option')
    },

    // 埋点
    clickInteriorSensors() {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const tabMap = {
        exterior: '外观',
        interior: '内饰',
        option: '选装',
        equity: '权益'
      }
      const { engine, customSeriesName } = this.currentModelLineData
      const param = {
        source_module: 'H5',
        refer_tab_name: tabMap[this.referConfigrationActiveTab],
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        select_fabric: this.currentSib.description,
        select_panel: this.currentEih.optionName,
        button_name: '下一步',
        $event_duration: Math.floor((Date.now() - this.pageStartTime) / 1000)
      }
      console.log('CC_CarConfiguration_Trim_BtnClick:', param)
      this.$sensors.track('CC_CarConfiguration_Trim_BtnClick', param)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

.disabled {
  opacity: .5;
}
.collapse-wrapper {
  padding-bottom: @FooterHeight;
}

//滚动区域
@InteriorHeight: 240px;
.wrapper-scroll {
  overflow-y: auto;
  height: calc( 100vh - @HeaderHeight - @TabHeight - @InteriorHeight - @FooterHeight);
}

.mainimg-wrapper {
  position: relative;
  height: 240px;
  margin-top: 8px;

  img {
    height: 100%;
  }
  .interior-3dIcon {
    position: absolute;
    width: 52px;
    height: 52px;
    top: 18px;
    left: 24px;
    z-index: 2;
  }
}

.content-wrapper {
  padding: 0 15px;
  margin-top: 16px;
}

.desc-wrapper {
  text-align: center;
  color: #333333;
  >.desc {
    font-size: 12px;
    margin-top: 4px;
  }
}

.sib-wrapper,
.eih-wrapper {
  overflow: auto;
  padding-bottom: 15px;
  margin-bottom: 33px;
  .wrapper {
    width: max-content;
    margin-top: 12px;

    >.item {
      margin-left: 4px;
      display: inline-block;
      width: 70px;
      height: 70px;
      transform: scale(0.7);

      img {
        height: 100%;
        object-fit: cover;
      }

      &.selected {
        transform: scale(1);
        border: 1px solid #000;
        padding: 1px;
        opacity: 1;
      }
    }
  }
}
</style>
