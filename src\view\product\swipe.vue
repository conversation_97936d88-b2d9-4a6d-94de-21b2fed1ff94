<!-- 资讯轮播图组-->
<template>
  <div class="find-swipe">
    <van-swipe class="my-swipe" :autoplay="5000" ref="vanSwipe" indicator-color="white" @change="onChange">
      <van-swipe-item v-for='(item,index) in swipeList' :key='index'>
        <img :height="height" @click="onClick(item)" :src="item.imageUrl" class="swipe-img"/>
      </van-swipe-item>
      <template #indicator>
        <div class="swipe-indicator" v-if="swipeList.length>1">
          <div v-for="(item,index) in swipeList" class="swipe-item-indicator" :key='item.id' @click="onChange2(index)" :style="{opacity: swipe_current==index?'0.8':'0.1'}"></div>
        </div>
      </template>
    </van-swipe>
  </div>
</template>

<script>
  import Vue from 'vue';
  import { Swipe, SwipeItem } from 'vant';
  Vue.use(Swipe);
  Vue.use(SwipeItem);
  export default {
    data() {
      return {
        swipe_current: 0,
      }
    },
    props: {
      swipeList: { //传值参数
        type: Array,
        default: () => { return [] }
      },
      height: { //传值参数
        type: String,
        default: '200'
      },
    },
    mounted() {

    },
    methods: {
      onChange(index) {
        this.swipe_current = index;
      },
      onChange2(index) {
        this.$refs.vanSwipe.swipeTo(index);
      },
      onClick(item){
        this.$emit('change',item)
      },
    }
  }
</script>

<style lang="less" scoped>
  div {
    box-sizing: border-box;
  }

  .my-swipe {
    .swipe-img {
      width: 100%;
      // height: px2rem(562);
      object-fit: cover;
      margin: 0 auto;
    }

    .swipe-indicator {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 10px;
      display: flex;
      justify-content: center;
      align-items: center;

      .swipe-item-indicator {
        width: 20px;
        height: 8px;
        background: #FFFFFF;
        margin-left: 2px;
        margin-right: 2px;
      }
    }
  }
</style>
