<template>
  <div
    class="modelline-wrapper"
    v-if="modelLineList && modelLineList.length"
    ref="lineModel"
  >
    <div
      class="line"
      v-for="(line, index) in modelLineList"
      :key="index"
      :class="[
        'line',
        ((selectCarInfo.modelLineCode === line.modelLineCode && line.modelLineCode) || (line.styleId && selectCarInfo.styleId === line.styleId)) ? 'active' : '',
      ]"
      @click.prevent="selectModel(line)"
    >
      <div
        class="name"
        v-if="!line.styleName"
      >
        {{ formatName(line.modelLineName)[0] }}<br>
        {{ formatName(line.modelLineName)[1] }}
      </div>
      <div
        class="name"
        v-if="line.styleName"
      >
        {{ line.styleName }}
      </div>
      <div
        class="detail"
        v-if="!line.styleName"
        @click.stop="showModelDetail(line)"
      >
        查看详情 >
      </div>
      <div class="price">
        <span v-if="!isNaN(line.price)">￥</span>
        {{ (line.price || '价格待定') | formatPrice }}
        {{ line.styleName ? '起' : '' }}
      </div>
      <div class="img">
        <img
          :src="BaseConfigrationOssHost + line.imageUrl | imgFix(600)"
          alt="车辆配置线图"
        >
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Toast } from 'vant'
import { mapState, mapMutations, mapGetters } from 'vuex'
import { canBuyA7LEdtionOne2 } from '@/configratorApi/index'
import url from '@/config/url'

Vue.use(Toast)

export default {
  computed: {
    ...mapState([
      'idx',
      'selectCarInfo',
      'modelLineList',
      'ccConfigration',
      'modelScrollTop',
      'currentExColor',
      'currentModelHub',
      'currentInteriorChair',
      'currentSibColorInterieur',
      'currentInteriorEih',
      'currentOptionsList',
      'currentPrice'
    ]),
    ...mapGetters(['getterCcid'])
  },
  props: {
    ccid: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      currentCar: {}
    }
  },
  created() {
  },
  watch: {
    modelLineList(val) {
      setTimeout(() => {
        if (document.getElementById('pane-0')) {
          document.getElementById('pane-0').scrollTo({ top: val || 0 })
        }
      })
    }
  },
  activated() {
    document
      .getElementById('pane-0')
      ?.scrollTo({ top: this.modelScrollTop || 0 })
  },
  methods: {
    // ...mapMutations(['setSelectCarIfo']),
    isNaN(val) {
      return isNaN(val)
    },
    formatName(val) {
      const { seriesName } = this.selectCarInfo
      if (!seriesName) return
      const series = seriesName.toLowerCase()
      let index = val.length
      if (series === 'q5e' || series == 'q6') {
        for (let i = 0, l = val.length; i < l; i++) {
          if (val.charCodeAt(i) > 255) {
            index = i
            break
          }
        }
      }
      const str1 = val.substring(0, index)
      const str2 = val.substring(index)
      return [str1, str2]
    },
    async selectModel(line) {
      // 如果选择的是A7L先行版，先判断是否购买过A7L先行版
      if (line.modelLineCode === '498B2Y005') {
        this.$store.commit('showLoading')
        const ccid = this.ccid || this.getterCcid
        const { data: canData } = await canBuyA7LEdtionOne2(ccid)
        this.$store.commit('hideLoading')
        if (canData.code === '00' && canData.data.status !== 1) {
          Toast({
            type: 'fail',
            message: canData.data.msg,
            icon: require('../../assets/img/error.png')
          })
          return
        }
      }
      if (this.idx === '1' && this.$storage.getPlus('semi-definite') === '个性定制') {
        this.$store.commit('setSelectCarIfo', line)
      } else {
        this.$store.commit('setSelectCarIfo', {
          styleId: line.styleId,
          styleName: line.styleName,
          price: line.price
        })
        this.$store.commit('setCurrentPrice', line.price)
      }
    },
    showModelDetail(line) {
      this.$store.commit(
        'setScrollTop',
        document.getElementById('pane-0').scrollTop
      )
      const { seriesName, modelLineCode } = this.selectCarInfo
      const series = seriesName.toLowerCase()
      if (series === 'a7l') {
        let arr = this.currentOptionsList
          && this.currentOptionsList.map((res) => res.optionName)
        arr = arr.join('/')
        this.$sensors.track('viewDetails', {
          model_name: line.modelLineName,
          price: this.currentPrice,
          cars_appearance: this.currentExColor.optionName,
          hub_model: this.currentModelHub.optionName,
          interior_type: `${this.currentInteriorChair.optionName}/${this.currentSibColorInterieur?.sibName}/${this.currentSibColorInterieur.interieurName}/${this.currentInteriorEih.optionName}`,
          personal_tailor: arr
        })
      }
      this.$store.commit('updateCarSeries', { seriesCode: this.selectCarInfo.seriesCode })
      this.$router.push({
        path: '/car-config-table',
        query: {
          modellineId: line.modelLineId,
          carModelName: line.modelLineName,
          from: 1
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.modelline-wrapper {
  background: #ffffff;
  padding: 16px;
  overflow-x: hidden;

  .line {
    position: relative;
    // width: 100%;
    height: 180px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
    margin-bottom: 16px;
    overflow: hidden;

    padding: 14px;

    &.active {
      border: 1px solid #000000;
    }

    .name {
      font-size: 16px;
      font-family: 'Audi-WideBold';
      z-index: 2;
      position: absolute;
      top: 14px;
      left: 14px;
    }

    .detail {
      padding-top: 18px;
      font-size: 14px;
      display: inline-block;
      position: absolute;
      z-index: 2;
      position: absolute;
      top: 40px;
      left: 14px;
    }

    .price {
      position: absolute;
      left: 16px;
      bottom: 16px;
      font-family: 'Audi-WideBold';
      z-index: 2;
      position: absolute;
      bottom: 14px;
      left: 14px;
    }

    .img {
      position: absolute;
      z-index: 1;
      right: -45px;
      bottom: 0;
      position: absolute;

      img {
        height: 200px;
        width: auto;
        object-fit: contain;
      }
    }
  }
}
</style>
