<template>
  <div class="wrapper" @touchend="onTouchend">
    <div>
      <div class="name">车型 | {{ modelName }}</div>
      <div>{{ modelPrice | finalFormatPriceDesc }}</div>
    </div>

    <div>
      <div class="name">外饰 | {{ outColorName }}</div>
      <div>{{ outColorPrice | finalFormatPriceDesc }}</div>
    </div>

    <div>
      <div class="name">外饰 | {{ hubName }}</div>
      <div>{{ hubPrice | finalFormatPriceDesc}}</div>
    </div>

    <div v-show="sibName">
      <div class="name">内饰 | {{ sibName}}</div>
      <div>{{ sibPrice | finalFormatPriceDesc}}</div>
    </div>

    <div v-show="eihName">
      <div class="name">内饰 | {{ eihName }}</div>
      <div>{{ eihPrice | finalFormatPriceDesc }}</div>
    </div>

    <div v-for="item in currentOptions" :key="item.optionCode">
      <div class="name">选装 | {{ item.optionName }}</div>
      <div class="relative">
        {{ item.price | finalFormatPriceDesc }}
        <div v-if="A7L_INTELLIGENT_AUDIO.optionCode.length === currentOptions.map(i => i.optionCode).length && A7L_INTELLIGENT_AUDIO.optionCode.every(code => currentOptions.map(i=>i.optionCode).includes(code)) && item.optionCode === A7L_INTELLIGENT_AUDIO.optionCode[1]" class="down-price">
          ¥{{ A7L_INTELLIGENT_AUDIO.price9wp | formatPrice}}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { A7L_INTELLIGENT_AUDIO } from '../util/carModelSeatData'

export default {
  data() {
    return {
      A7L_INTELLIGENT_AUDIO
    }
  },
  computed: {
    ...mapGetters(['currentCarType']),
    ...mapState({
      currentModelLineData: (state) => state.configration.currentModelLineData,
      carIdx: (state) => state.configration.carIdx,
      currentVersion: (state) => state.configration.currentVersion,

      modelName: (state) => state.configration.currentModelLineData?.modelLineName ?? '',
      modelPrice: (state) => state.configration.currentModelLineData?.price ?? 0,

      outColorName: (state) => state.configration.currentExterior?.optionName ?? '',
      outColorPrice: (state) => state.configration.currentExterior?.price ?? 0,

      hubName: (state) => state.configration.currentHub?.optionName ?? '',
      hubPrice: (state) => state.configration.currentHub?.price ?? 0,

      sibName: (state) => state.configration.currentSib?.interieurName ?? '',
      sibPrice: (state) => state.configration.currentSib?.price ?? 0,

      eihName: (state) => state.configration.currentEih?.optionName ?? '',
      eihPrice: (state) => state.configration.currentEih?.price ?? 0,

      currentOptions: (state) => {
        const composeName = state.configration.currentComposeName // 是否选中的推荐组合
        if (composeName) {
          const compose = state.configration.personalOptionComposes.find((i) => i.composeName === composeName)
          return compose.composePersonalOptions
        }
        return state.configration.selectedOptions
      }
    })
  },

  methods: {
    onTouchend() {
      // 埋点
      this.clickPopupSensors('滑动价格明细')
    },

    // 埋点
    clickPopupSensors(operationType) {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const {
        engine, customSeriesName
      } = this.currentModelLineData
      const param = {
        source_module: 'H5',
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        operation_type: operationType
      }

      console.log(param)
      this.$sensors.track('CC_CarConfiguration_Operate', param)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";
.wrapper {
  margin-top: 16px;
  max-height: 230px;
  overflow-y: auto;
  >div {
    line-height: 20px;
    margin-bottom: 12px;
    .c-flex-between;
  }
}

.name {
  color: #000000;
  opacity: 0.5;
  max-width: 72%;
  .c-font12 ;
}

.relative {
  position: relative;
  >.down-price {
    position: absolute;
    right: 130%;
    top: 50%;
    transform: translateY(-50%);
    color: #CCCCCC;
    font-size: 12px;
    text-decoration:line-through;
  }
}

</style>
