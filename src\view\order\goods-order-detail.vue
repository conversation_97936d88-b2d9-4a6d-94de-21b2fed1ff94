<template>
  <div class="goods-order-detail">
    <!-- 订单状态 -->
    <div class="order-status-box">
      <div class="order-status">
        待收货
      </div>
      <div class="tracking-info">
        <span>物流单号：<span class="tracking-number">123123123123123</span>。如有疑问请联系客服。</span>
      </div>
      <div class="order-time">
        2020-09-07 11:01:57
      </div>
      <img
        class="order-status-arrow"
        src="@/assets/img/icon-arrow.png"
        alt="arrow"
      >
    </div>
    <main>
      <!-- 订单联系人信息以及地址 -->
      <div class="order-user-box">
        <img
          class="order-user-localtion"
          src="@/assets/img/localtion.png"
          alt="localtion"
        >
        <div class="order-user-info">
          <span class="order-user-name">夏天</span>
          <span class="order-user-phone">18555555555</span>
        </div>
        <div class="order-user-address">
          上海 上海市 嘉定区 安亭镇 盛世景庭 35号楼上海嘉定区安亭镇于安亭镇于安亭镇于安亭镇于安亭镇于安亭镇于安亭镇于安亭镇于
        </div>
      </div>
      <!-- 商品信息&客服 -->
      <div class="order-goods-box">
        <div class="order-goods-info">
          <img
            class="goods-img"
            src=""
            alt="goods"
          >
          <div>
            <div class="goods-name">
              迪奥联名手提包
            </div>
            <div class="goods-color">
              颜色：<span>白色</span>
            </div>
            <div class="goods-price">
              ¥5,800
            </div>
          </div>
          <div class="goods-count">
            x1
          </div>
        </div>
        <div class="order-goods-footer">
          <div class="order-cancel-button">
            取消订单
          </div>
          <div class="order-goods-kefu">
            <img
              src="@/assets/img/icon-kefu.png"
              alt="kefu"
            >
            <span>客服</span>
          </div>
        </div>
      </div>
      <!-- 单号，运费等其他信息 -->
      <div class="order-other-box">
        <div>
          <div class="order-other-normal">
            <span>商品金额</span>
            <span>¥5800</span>
          </div>
          <div class="order-other-normal">
            <span>运费</span>
            <span>免运费</span>
          </div>
          <div class="order-other-big">
            <span>需付款</span>
            <span>¥5800</span>
          </div>
        </div>
        <div>
          <div class="order-other-normal order-orderid">
            <span>订单编号：123123123123123</span>
            <img
              src="@/assets/img/icon-copy.png"
              alt="copy"
            >
          </div>
          <div class="order-other-normal order-ordertime">
            <span>下单时间：2020-09-03 17:04</span>
          </div>
        </div>
      </div>
      <!-- 总价 -->
      <div class="order-totalprice">
        <span>总价</span>
        <span>¥5,800</span>
      </div>
    </main>
    <footer>
      <div>去支付</div>
    </footer>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  created() {},
  mounted() {}
}
</script>

<style scoped lang="less">
.goods-order-detail {
  border-top: 1px solid #E5E5E5;
  color: #000000;
  font-weight: 400;
  height: calc(100vh - 50px - 88px);
  overflow: scroll;
  .order-status-box {
    width: 100%;
    height: 114px;
    border-bottom: 1px solid #F2F2F2;
    padding: 16px;
    box-sizing: border-box;
    position: relative;
    .order-status {
      font-size: 16px;
      line-height: 24px;
    }
    .tracking-info{
      font-size: 12px;
      line-height: 18px;
      margin-top: 4px;
    }
    .order-time{
      font-size: 10px;
      line-height: 15px;
      position:absolute;
      bottom: 16px;
    }
    .order-status-arrow {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translate(0,-50%);
      width: 35px;
    }
  }
  main {
    padding: 0 16px;
    box-sizing: border-box;
  }
  .order-user-box {
    width: 100%;
    height: 107px;
    border-bottom: 1px solid #F2F2F2;
    padding: 24px 16px 16px 27px;
    box-sizing: border-box;
    position: relative;
    font-size: 14px;
    line-height: 21px;
    .order-user-localtion {
      position: absolute;
      width: 13px;
      top: 26px;
      left:0px;
    }
    .order-user-info {
      .order-user-phone {
        color: #333333;
        margin-left: 12px;
      }
    }
    .order-user-address {
      margin-top: 4px;
      overflow: hidden;
      text-overflow: -o-ellipsis-lastline;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-clamp: 2;
      -webkit-line-clamp: 2;
    }
  }
  .order-goods-box {
    height: 174px;
    width: 100%;
    padding: 24px 0 24px 0;
    border-bottom: 1px solid #F2F2F2;
    box-sizing: border-box;
    .order-goods-info{
      display: flex;
      font-size: 16px;
      line-height: 24px;
      position: relative;
      .goods-img {
        width: 84px;
        height: 84px;
        border: 1px solid red;
        margin-right: 12px;
      }
      .goods-color {
        font-size: 12px;
        color: #999999;
        line-height: 18px;
        margin-top: 2px;
      }
      .goods-price {
        margin-top: 11px;
      }
      .goods-count {
        position: absolute;
        right: 0;
        bottom: 7px;
        font-size: 12px;
        color: #999999;
        line-height: 18px;
      }
    }
    .order-goods-footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 8px;
      .order-cancel-button {
        width: 77px;
        height: 27px;
        border: 1px solid #000000;
        line-height: 27px;
        text-align: center;
        font-size: 14px;
        margin-right: 16px;
      }
      .order-goods-kefu {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      img {
        width: 23px;
        height: 23px;
      }
      span {
        font-size: 12px;
        line-height: 15px;
        margin-top: 4px;
      }
    }
  }
  .order-other-box {
    > div {
      padding: 16px 0;
      box-sizing: border-box;
      border-bottom: 1px solid #F2F2F2;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &:first-of-type {
        width: 100%;
        height: 128px;
      }
      &:last-of-type {
        width: 100%;
        height: 84px;
      }
      img {
        width: 24px;
      }
      .order-other-normal {
        font-size: 12px;
        color: #333333;
        line-height: 18px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span{
          &:last-of-type {
            color: #000000;
          }
        }
      }
      .order-other-big {
        font-size: 16px;
        color: #000000;
        line-height: 24px;
        display: flex;
        justify-content: space-between;
        span{
          &:last-of-type {
            font-family: Audi-WideBold;
            color: #000000;
          }
        }
      }
    }
  }
  .order-totalprice {
    padding: 16px 0;
    display: flex;
    justify-content: flex-end;
    font-size: 14px;
    color: #000000;
    line-height: 18px;
    span {
      &:last-of-type {
        font-family: Audi-WideBold;
        margin-left: 8px;
      }
    }
  }
  footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 16px;
    box-sizing: border-box;

    div {
      width: 100%;
      height: 56px;
      background: #1A1A1A;
      color: #fff;
      font-size: 16px;
      line-height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
