<template>
  <div class="informationCause box">
    <div class="warp">
      <!-- <div
        v-for="(item, index) in causeList"
        :key="index"
        class="item"
        @click="checked = item.code"
      >
        <span>{{ item.desc }}</span>
        <div :class="checked == item.code ? 'checked' : 'noChecked'"></div>
      </div> -->
      <van-field
        v-model="message"
        rows="10"
        autosize
        border
        type="textarea"

        placeholder="请填写退订原因"
      />
    </div>

    <div class="btnWarp">
      <div
        class="buttons"
        @click="unsubscribe"
      >
        确认退订
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  RadioGroup, Radio, Field, Toast
} from 'vant'
import {
  getCancelReasons,
  unsubscribe
} from '../../api/information-cause.js'
// import { getLimitedNumber } from '../../api/money-detail'

Vue.use(Radio).use(Field).use(RadioGroup).use(Toast)

const codeType = ['00', '200']
export default {
  data() {
    return {
      causeList: [],
      checked: '39',
      radio: '',
      message: ''
    }
  },


  methods: {
    unsubscribe() {
      if (this.message === '' || this.message === null) {
        Toast({
          type: 'fail',
          message: '请填写退订原因',
          icon: require('../../assets/img/error.png')
        })
      } else {
        // 埋点
        const params = {
          // is_rice_expensive: this.checked == "30" ? true : false,
          // is_Plan_change: this.checked == "31" ? true : false,
          // other_information: this.checked == "39" ? true : false,
          is_rice_expensive: false,
          is_Plan_change: false,
          other_information: true,
          refund_information: this.message,
          cars_appearance: 1,
          cars_interior: 1,
          $event_duration: new Date().getTime()
        }
        this.$sensors.track('confirmUnsubscribe', params)
        const m = async () => {
          this.$store.commit('showLoading')
          const param = {
            orderId: this.$route.query.orderId,
            remark: this.message,
            refundReasonType: this.checked,
            boolOfflineRefund: false
          }
          console.log('退订原因：param', param)
          const { data } = await unsubscribe(param)
          this.$store.commit('hideLoading')
          if (codeType.includes(data.code)) {
            const valid = this.$route.query?.valid
            const updateFlag = this.$route.query?.updateFlag
            this.$router.push({
              path: '/order/unsubscribe-succeed',
              query: {
                valid: valid == 1 ? '' : valid,
                updateFlag: updateFlag,
                seriesCode: this.$route.query?.seriesCode
              }
            })
            // let data1 = await getLimitedNumber({
            //   orderId: this.$route.query.orderId || "",
            // });

            // if (codeType.includes(data1.data.code)) {
            //   let data2 = await recycle({
            //     id: data1.data.data.id || "",
            //   });
            //   if (codeType.includes(data2.data.code)) {
          } else {
            // Toast({
            //   type: "fail",
            //   message: data2.data.message,
            //   icon: require("../../assets/img/error.png"),
            // });
            //   }
            // }
          }
        }
        if (this.checked === '39') {
          if (this.message) {
            m()
          } else {
            Toast({
              type: 'fail',
              message: '请填写退订原因',
              icon: require('../../assets/img/error.png')
            })
          }
        } else {
          m()
        }
      }
    },
    async getCancelReasons() {
      const { data } = await getCancelReasons()
      if (codeType.includes(data.code)) {
        this.causeList = data.data
        this.checked = data.data[0].code
      }
    }
  },
  mounted() {
    const { valid, updateFlag } = this.$route.query
    if (valid == 0 || updateFlag == 1) this.message = '配置更新或失效'
    this.getCancelReasons()
  }
}
</script>

<style scoped lang="less">
@import url("../../assets/style/buttons.less");
@import url("../../assets/style/scroll.less");
@import url("../../assets/style/checked.less");
.informationCause {
  margin-top: 15px;
  .warp {
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 46px;
      border-bottom: 1px solid #eeeeee;
      span {
        font-size: 14px;
        color: #333333;
      }
    }
    /deep/.van-field {
      padding: 0;
      .van-field__body {
        border: 1px solid #eee;
        textarea {
          padding: 8px 10px;
          &::placeholder {
            color: #999999;
          }
        }
      }
    }
  }
}
</style>
