import request from '@/router/axios'
import api from '@/config/url'
import store from '@/store/index'
import URL from './url'

const baseUrl = api.BaseApiUrl
const consentByTypeUrl = api.consentByTypeUrl

export const sendCode = (data) => request({
  url: `${baseUrl + URL.sendCode}`,
  method: 'post',
  data: {
    ...data
  }
})

export const getprivacy = (params) => request({
  url: `${baseUrl + URL.privacy}`,
  method: 'get',
  params
})

export const getpolicy = (params) => request({
  url: `${baseUrl + URL.policy}`,
  method: 'get',
  params
})

export const getDealerListByTestDrive = (params) => request({
  url: `${baseUrl + URL.getDealerListByTestDriveUrl}`,
  method: 'get',
  params
})

// 通过相似车配车
export const simicarConfig = (data) => request({
  url: `${baseUrl + URL.estimateConfig}`,
  method: 'post',
  data: {
    ...data
  }
})

// 配置单相似车
export const getSimilarityModel = (params) => request({
  url: `${baseUrl + URL.getSimilarityModel}`,
  method: 'get',
  params
})

// 13、行驶证信息绑定
export const bindDrivingLicense = (data) => request({
  url: `${baseUrl + URL.defUrl}bindDrivingLicense`,
  method: 'post',
  data: {
    ...data
  }
})

// 3、上传行驶证认证车辆
export const postCarRecog = (params) => request({
  url: `${baseUrl + URL.defUrl}vehicleLicenseRecognition`,
  method: 'POST',
  data: params
})

// 12、行驶证信息查询
export const drivingLicenseInfo = (params) => request({
  url: `${baseUrl + URL.defUrl}drivingLicenseInfo`,
  method: 'get',
  params
})

export const getUserInfo = (data) => request({
  url: `${baseUrl + URL.getUserInfo}`,
  method: 'post',
  data: {
    ...data
  }
})

export const getUserTestDriveStatus = (params) => request({
  url: `${baseUrl + URL.getTryCarUrl}`,
  method: 'get',
  params
})

export const getCheckNowBuyStatus = (data) => request({
  url: `${baseUrl + URL.getCheckNowBuyStatus}`,
  method: 'get',
  data
})

// 车型
export const getCarModelData = (params) => request({
  url: baseUrl + URL.getCarModelData,
  method: 'get',
  params
})
// 获取订单列表
export const getOrderList = (data) => request({
  url: `${
    baseUrl + URL.getOrderList
  }?current=1&size=999&category=01&orderStatus=00,01,30,31,32,301,98,99,90`,
  method: 'get',
  data
})
// 获取订单列表 (E-shop)
export const getEShopOrderList = (data) => request({
  url: `${
    baseUrl + URL.getEShopOrderList
  }?current=1&size=999&category=01&orderStatus=00,01,30,31,32,301,98,99,90`,
  method: 'get',
  data
})
// 获取整车订单H5页面详情地址
export const getCarOrderInfoH5 = (data) => request({
  url: `${baseUrl + URL.getCarOrderInfoH5}`,
  method: 'get',
  data
})
// 购物车-整车接口
export const getCarShoppingCartList = (data) => request({
  url: `${baseUrl + URL.getCarShoppingCartList}`,
  method: 'get',
  data
})
// 删除购物车 整车
export const postDelCarShoppingCart = (data) => request({
  url: `${baseUrl + URL.postDelCarShoppingCart}`,
  method: 'post',
  data: {
    ...data
  }
})
// 颜色，含内饰
export const getColorData = (data) => request({
  url: baseUrl + URL.getColorData,
  method: 'get',
  data
})
// 轮毂
export const getHubData = (data) => request({
  url: baseUrl + URL.getHubData,
  method: 'get',
  data
})
// 装备
export const getEquipmentData = (data) => request({
  url: baseUrl + URL.getEquipmentData,
  method: 'get',
  data
})

export const getFirstEquityDetail = (params) => request({
  url: baseUrl + URL.getFirstEquityDetail,
  method: 'get',
  params
})

// 贷款规则，即各贷款机构费率
export const getLoanRule = (params) => request({
  url: baseUrl + URL.getLoanRule,
  method: 'get',
  params
})
// 贷款计算规则
export const getLoanCompute = (params) => request({
  url: baseUrl + URL.getLoanCompute,
  method: 'get',
  params
})
// 融资租赁计算
export const getrentCompute = (params) => request({
  url: baseUrl + URL.getrentCompute,
  method: 'get',
  params
})
// 获取商品详情
export const getProductDetail = (params) => request({
  url: `${baseUrl + URL.getProductDetail}/${params.id}`,
  method: 'get'
})

export const getContractInfo = (params, customHeader = {}) => request({
  url: `${baseUrl + URL.getContractInfo}`,
  method: 'post',
  params,
  customHeader
})

export const getContractImageCount = (params) => request({
  url: `${baseUrl + URL.getContractImageCount}`,
  method: 'get',
  params
})

export const verifyFadada = (params) => request({
  url: `${baseUrl + URL.verifyFadada}`,
  method: 'get',
  params
})

export const loginFadada = (params) => request({
  url: `${baseUrl + URL.verifyFadada}`,
  method: 'post',
  params
})

export const enterpriseVerifyFadada = (params) => request({
  url: `${baseUrl + URL.enterpriseVerifyFadada}`,
  method: 'get',
  params
})

export const enterpriseLoginFadada = (params) => request({
  url: `${baseUrl + URL.enterpriseVerifyFadada}`,
  method: 'post',
  params
})

export const signContract = (params) => request({
  url: `${baseUrl + URL.signContract}`,
  method: 'post',
  params
})

export const getCarConfig = (params) => request({
  url: `${baseUrl + URL.getConfigTable}`,
  method: 'get',
  params,
  channel: 'oneapp'
})
export const getCarConfig1 = (params) => request({
  url: `${baseUrl + URL.getCarConfig}?customColorId=${
    params.customColorId
  }&customModelId=${params.customModelId}`,
  method: 'post'
  // data: {
  //   ...params
  // }
  // params
})
export const getCarConfigcc = (params) => request({
  url: `${baseUrl + URL.getCarConfigcc}`,
  method: 'get',
  params
})

export const getA7MrConfigcc = (params) => request({
  url: `${baseUrl + URL.getA7MrConfigcc}`,
  method: 'get',
  params
})

export const getQ5EConfigcc = (params) => request({
  url: `${baseUrl + URL.getQ5EConfigcc}`,
  method: 'get',
  params
})

export const getQ6cc = (params) => request({
  url: `${baseUrl + URL.getQ6cc}`,
  method: 'get',
  params
})

export const getA5Lcc = (params) => request({
  url: `${baseUrl + URL.getA5Lcc}`,
  method: 'get',
  params
})

export const getNearDealers = (params) => request({
  url: `${baseUrl + URL.getNearDealers}`,
  method: 'get',
  params
})

export const addCarShoppingCart = (params) => request({
  url: `${baseUrl + URL.addCarShoppingCart}`,
  method: 'post',
  params
})
export const getOtds = (params) => request({
  url: `${baseUrl + URL.getOtds}?useType=1`,
  method: 'get',
  params
})
export const getLimitnumberRule = (params) => request({
  url: `${baseUrl + URL.getLimitnumberRule}`,
  method: 'get',
  params
})
export const getFloors = (params) => request({
  url: `${baseUrl + URL.getFloors}`,
  method: 'get',
  params
})
export const getFloors1 = (params) => request({
  url: `${baseUrl + URL.getFloors1}`,
  method: 'get',
  params
})
export const getFloors2 = (params) => request({
  url: `${baseUrl + URL.getFloors2}`,
  method: 'get',
  params
})

export const getMyOrders = (params) => request({
  url: `${baseUrl}${URL.getOrderDetail}/${params.orderId}`,
  method: 'get'
})

export const getCarParamTable = () => request({
  url: `${baseUrl}${URL.getCarParamTable}`,
  method: 'get'
})

export const getDigitalPayStatus = (params) => request({
  url: `${baseUrl}${URL.getDigitalPayStatus}`,
  method: 'get',
  params
})

export const getDigitalPayInfo = (params) => request({
  url: `${baseUrl}${URL.getDigitalPayInfo}`,
  method: 'get',
  params
})

export const createOfflineContract = (params, customHeader = {}) => request({
  url: `${baseUrl}${URL.createOfflineContract}`,
  method: 'get',
  customHeader,
  params
})

export const getLimitedNumber = (params) => request({
  url: `${baseUrl}${URL.getLimitedNumber}`,
  method: 'get',
  params
})

export const getHasLimitedNumber = (params) => request({
  url: `${baseUrl}${URL.getHasLimitedNumber}`,
  method: 'get',
  params
})

// 线下预览
export const getOfflineContractImage = (params) => baseUrl + URL.getOfflineContractImage + params

export const getSignedContractImage = (params) => baseUrl + URL.getSignedContractImage + params

export const downloadContractPdf = (params) => request({
  url: `${baseUrl}${URL.downloadContractPdf}`,
  method: 'get',
  params
})

export const getOnlineContractStatus = (params) => request({
  url: `${baseUrl}${URL.getOnlineContractStatus}`,
  method: 'get',
  params
})

export const getContractPreviewImgs = (params) => request({
  url: `${baseUrl}${URL.getContractPreviewImgs}`,
  method: 'get',
  params
})

export const getFinancePlatformList = (params) => request({
  url: `${baseUrl}${URL.getFinancePlatformList}`,
  method: 'get',
  params
})

export const getModelLine = (params) => request({
  url: `${baseUrl}${URL.getModelLine}`,
  method: 'get',
  params
})
export const getCalculatorModelLine = (params) => request({
  url: `${baseUrl}${URL.getCalculatorModelLine}`,
  method: 'get',
  params
})

export const getCustomSeries2 = (params) => request({
  url: `${baseUrl + URL.getCustomSeries}`,
  method: 'GET',
  params,
  channel: 'oneapp'
})
export const applyFinanceServer = (params) => request({
  url: `${baseUrl}${URL.applyFinanceServer}`,
  method: 'post',
  data: params
})

export const daDingToRefund = (params) => request({
  url: `${baseUrl}${URL.daDingToRefund}/${params.orderId}`,
  method: 'put'
})

export const uploadContractFile = (params, submitToken, param) => request({
  url: `${baseUrl}${URL.uploadContractFile}?contractId=${param.contractId}&remark=${param.remark}`,
  method: 'post',
  data: params,
  submitToken
})

export const getDealerList = (params) => request({
  url: `${baseUrl}${URL.getDealerList}`,
  method: 'get',
  params
})

export const getOverseasStudentMessage = (params) => request({
  url: `${baseUrl}${URL.getOverseasStudentMessage}`,
  method: 'get',
  params
})
export const getBuyingOrderApproach = (params) => request({
  url: `${baseUrl}${URL.getBuyingOrderApproach}`,
  method: 'get',
  params
})

export const postAddOverseasStudentMessage = (params) => request({
  url: `${baseUrl}${URL.postAddOverseasStudentMessage}`,
  method: 'post',
  data: params
})

export const putUpdateOverseasStudentMessage = (params) => request({
  url: `${baseUrl}${URL.putUpdateOverseasStudentMessage}`,
  method: 'put',
  data: params
})

export const getChargingPileCarOwnerInfo = (params) => request({
  url: `${baseUrl}${URL.getChargingPileCarOwnerInfo}`,
  method: 'get',
  params
})
export const getVeryLongReservationOrderList = (params) => request({
  url: `${baseUrl}${URL.getVeryLongReservationOrderList}`,
  method: 'get',
  params
})

export const postChargingPileCancel = (params) => request({
  url: `${baseUrl}${URL.postChargingPileCancel}`,
  method: 'post',
  data: params
})

export const updateNgaOrderInfo = (params) => request({
  url: `${baseUrl}${URL.updateNgaOrderInfo}`,
  method: 'put',
  data: params
})
export const getInviteUser = (params) => request({
  url: `${baseUrl}${URL.getInviteUser}`,
  method: 'get',
  params
})
export const getUserRightsBySkuId = (params) => request({
  url: `${baseUrl}${URL.getUserRightsBySkuId}`,
  method: 'get',
  params
})
export const getUserRightsByCarModelId = (params) => request({
  url: `${baseUrl}${URL.getUserRightsByCarModelId}`,
  method: 'get',
  params
})

export const getDealerByCode = (params) => request({
  url: `${baseUrl}${URL.getDealerByCode}`,
  method: 'get',
  params
})

export const getContractState = (params) => request({
  url: `${baseUrl}${URL.getContractState}`,
  method: 'get',
  params
})

export const judgeReservationClient = (params) => request({
  url: `${baseUrl}${URL.judgeReservationClient}`,
  method: 'get',
  params
})

export const getModelCompatibleList = (params) => request({
  url: `${baseUrl}${URL.getModelCompatibleList}`,
  method: 'get',
  params
})

export const updateCarConfigByCcid = (params) => request({
  url: `${baseUrl}${URL.updateCarConfigByCcid}/${params.ccid}`,
  method: 'put',
  params: {
    ...params,
    sourceId: store.state.sourceId
  }
})

export const autoSaveCarShoppingCart = (params) => request({
  url: `${baseUrl}${URL.autoSaveCarShoppingCart}`,
  method: 'post',
  params
})

export const getModelLineConfigs = (params) => request({
  url: `${baseUrl}${URL.getModelLineConfigs}`,
  method: 'get',
  params
})

export const refreshCcid = (params) => request({
  url: `${baseUrl}${URL.refreshCcid}`,
  method: 'get',
  params
})

export const getBuyCarAgreement = () => request({
  url: `${baseUrl}${URL.getBuyCarAgreement}`,
  method: 'get'
})

export const getLoanStatus = (params) => request({
  url: `${baseUrl}${URL.getLoanStatus}`,
  method: 'get',
  params
})

export const getCarConfigDetail = (params) => request({
  url: `${baseUrl}${URL.getCarConfigDetail}`,
  method: 'get',
  params
})

export const minipPay = (params) => request({
  url: `${baseUrl}${URL.minipPay}`,
  method: 'post',
  params
})

export const getShareUrl = () => request({
  url: `${baseUrl}${URL.getShareUrl}`,
  method: 'get'
})
export const getCityList = (params) => request({
  url: `${baseUrl}${URL.getCityList}`,
  method: 'post',
  params
})
export const getProvinceCityList = (params) => request({
  url: `${baseUrl}${URL.getProvinceCityList}`,
  method: 'post',
  params
})

export const getAfterSalesList = (params) => request({
  url: `${baseUrl}${URL.getAfterSalesList}`,
  method: 'get',
  params
})

export const getAfterSalesByVin = (params) => request({
  url: `${baseUrl}${URL.getAfterSalesByVin}`,
  method: 'get',
  params
})

export const getAgentCityList = (params) => request({
  url: `${baseUrl}${URL.getAgentCityList}`,
  method: 'get',
  params
})

export const getProvinceList = (params) => request({
  url: `${baseUrl}${URL.getProvinceList}`,
  method: 'get',
  params
})

export const getOrgList = (params) => request({
  url: `${baseUrl}${URL.getOrgList}`,
  method: 'get',
  params
})

export const getChargingPileInfo = (params) => request({
  url: `${baseUrl}${URL.getChargingPileInfo}`,
  method: 'get',
  params
})
export const postAfterSaleCreate = (params) => request({
  url: `${baseUrl}${URL.postAfterSaleCreate}`,
  method: 'post',
  data: params,
  channelid: '2'
})
export const postChargingPileSubmit = (params) => request({
  url: `${baseUrl}${URL.postChargingPileSubmit}`,
  method: 'post',
  data: params
})

export const postCreateChargingPile = (params) => request({
  url: `${baseUrl}${URL.postCreateChargingPile}`,
  method: 'post',
  data: params,
  channelid: '2'
})

export const postAfterSaleUpdate = (params) => request({
  url: `${baseUrl}${URL.postAfterSaleUpdate}`,
  method: 'post',
  data: params,
  channelid: '2'
})
export const postCancelDeliverCarOrder = (params) => request({
  url: `${baseUrl}${URL.postCancelDeliverCarOrder}`,
  method: 'post',
  data: params,
  channelid: '2'
})

export const postCreateDeliverCar = (params) => request({
  url: `${baseUrl}${URL.postCreateDeliverCar}`,
  method: 'post',
  data: params,
  channelid: '2'
})

export const getAfterSaleFindByPage = (params) => request({
  url: `${baseUrl}${URL.getAfterSaleFindByPage}?page=1&size=999`,
  method: 'get',
  params
})

// 获取insaic服务订单列表
export const getInsaicServiceList = (params) => request({
  url: `${baseUrl}${URL.getInsaicServiceList}`,
  method: 'post',
  data: params,
  channelid: '2'
})

// 获取道路救援服务订单列表
export const getRoadSaveServiceList = (params) => request({
  url: `${baseUrl}${URL.getRoadSaveServiceList}`,
  method: 'post',
  data: params,
  channelid: '2'
})

export const getInsaicServiceDetail = (params) => request({
  url: `${baseUrl}${URL.getInsaicServiceDetail}`,
  method: 'get',
  params,
  channelid: '2'
})

export const getRoadSaveOrderDetail = (params) => request({
  url: `${baseUrl}${URL.getRoadSaveOrderDetail}`,
  method: 'get',
  params,
  channelid: '2'
})

export const getAfterSaleCancel = (params) => request({
  url: `${baseUrl}${URL.getAfterSaleCancel}`,
  method: 'get',
  params
})

export const getAfterSaleDetail = (params) => request({
  url: `${baseUrl}${URL.getAfterSaleDetail}`,
  method: 'get',
  params
})
export const getPackageMainData = (params) => request({
  url: `${baseUrl}${URL.getPackageMainData}`,
  method: 'get',
  params
})
export const getAfterSaleListResource = (params) => request({
  url: `${baseUrl}${URL.getAfterSaleListResource}`,
  method: 'get',
  params
})
export const getAfterSaleListResourceNew = (params) => request({
  url: `${baseUrl}${URL.getAfterSaleListResourceNew}`,
  method: 'get',
  params
})
export const getDeliverCarQueryCityList = (params) => request({
  url: `${baseUrl}${URL.getDeliverCarQueryCityList}`,
  method: 'get',
  params
})
export const getDeliverCarQueryDealer = (params) => request({
  url: `${baseUrl}${URL.getDeliverCarQueryDealer}`,
  method: 'get',
  params
})
export const getDeliverCarOrderDetail = (params) => request({
  url: `${baseUrl}${URL.getDeliverCarOrderDetail}`,
  method: 'get',
  params
})

export const postBindCardInfo = (params) => request({
  url: `${baseUrl}${URL.postBindCardInfo}`,
  method: 'post',
  params
})

export const getWXConfig = (params) => request({
  url: `${baseUrl}${URL.getWXConfig}`,
  method: 'get',
  params
})
export const getCarCoupon = (params) => request({
  url: `${baseUrl}${URL.getCarCoupon}`,
  method: 'get',
  params
})
export const getOrderStatusList = (params) => request({
  url: `${baseUrl}${URL.getOrderStatusList}`,
  method: 'get',
  params
})
// 获取取车订单红点阅读状态
export const getUnreadDeliverCarOrderStatusCount = (params) => request({
  url: `${baseUrl}${URL.getUnreadDeliverCarOrderStatusCount}`,
  method: 'get',
  params
})

export const getReportList = (params) => request({
  url: `${baseUrl}${URL.getReportList}`,
  method: 'get',
  params
})

export const getCodelLineConfigsCompare = (params) => request({
  url: `${baseUrl}${URL.getCodelLineConfigsCompare}`,
  method: 'get',
  params
})

export const getCanRefund = (params) => request({
  url: `${baseUrl}${URL.getCanRefund}`,
  method: 'post',
  params
})

export const canUpdateCCConfig = (params) => request({
  url: `${baseUrl}${URL.canUpdateCCConfig}`,
  method: 'post',
  params
})
export const postPreorders = (params) => request({
  url: `${baseUrl}${URL.postPreorders}`,
  method: 'post',
  params
})

export const getLimitedNumberLineByOrderId = (params) => request({
  url: `${baseUrl}${URL.getLimitedNumberLineByOrderId}`,
  method: 'get',
  params
})
export const getUserAddress = (params) => request({
  url: `${baseUrl}${URL.getUserAddress}`,
  method: 'get',
  params
})

export const isConfirmed = (params) => request({
  url: `${baseUrl}${URL.isConfirmed}`,
  method: 'post',
  params
})

export const confirmPayRest = (params) => request({
  url: `${baseUrl}${URL.confirmPayRest}`,
  method: 'post',
  params
})

export const getCarPaymentInfo = (params) => request({
  url: `${baseUrl}${URL.getCarPaymentInfo}/${params.orderId}`,
  method: 'get'
})

export const getCoordsList = (params) => request({
  url: `${baseUrl}${URL.getCoordsList}`,
  method: 'get',
  params
})
export const getCarImages = (params) => request({
  url: `${baseUrl}${URL.getCarImages}`,
  method: 'get',
  params
})

export const getBalanceQrcode = (params) => request({
  url: `${baseUrl}${URL.getBalanceQrcode}`,
  method: 'post',
  params
})

export const refreshBalanceQrcode = (params) => request({
  url: `${baseUrl}${URL.refreshBalanceQrcode}/${params.paysn}`,
  method: 'get'
})

export const addOrderBalancePaymentCertificate = (params) => request({
  url: `${baseUrl}${URL.addOrderBalancePaymentCertificate}`,
  method: 'post',
  params
})

export const getDetailsByOrderId = (params) => request({
  url: `${baseUrl}${URL.getDetailsByOrderId}`,
  method: 'get',
  params
})

export const amsConfCheckCar = (params) => request({
  url: `${baseUrl}${URL.amsConfCheckCar}`,
  method: 'post',
  params
})

export const updateAmsDeliverType = (params) => request({
  url: `${baseUrl}${URL.updateAmsDeliverType}`,
  method: 'post',
  params
})

export const amsConfDeliverCarSign = (params) => request({
  url: `${baseUrl}${URL.amsConfDeliverCarSign}`,
  method: 'post',
  params
})

export const getFloorslist = (params) => request({
  url: `${baseUrl}${URL.getFloorslist}`,
  method: 'get',
  params
})

export const getNearestDealerList = (params) => request({
  url: `${baseUrl}${URL.getNearestDealerList}`,
  method: 'get',
  params
})

export const getMyPointsV2 = (params) => request({
  url: `${baseUrl}${URL.getMyPointsV2}`,
  method: 'get',
  params
})

export const isFavorited = (params) => request({
  url: `${baseUrl}${URL.isFavorited}`,
  method: 'get',
  params
})

export const updateFavorite = (params) => request({
  url: `${baseUrl}${URL.updateFavorite}`,
  method: 'put',
  data: params
})

export const getAfterSalesByOrderId = (params) => request({
  url: `${baseUrl}${URL.getAfterSalesByOrderId}`,
  method: 'get',
  params
})

export const amsConfDeliverCar = (params) => request({
  url: `${baseUrl}${URL.amsConfDeliverCar}`,
  method: 'post',
  params
})

export const postOrcIdentifyIdCard = (params) => request({
  url: `${baseUrl + URL.postOrcIdentifyIdCard}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})
export const postImageEncryption = (params) => request({
  url: `${baseUrl + URL.postImageEncryption}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})

export const postOcrDrivingLicence = (params) => request({
  url: `${baseUrl + URL.postOcrDrivingLicence}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})

export const postOcrVehiclelicense = (params) => request({
  url: `${baseUrl + URL.postOcrVehiclelicense}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})

export const postOcrInvoiceVehiclelicense = (params) => request({
  url: `${baseUrl + URL.postOcrInvoiceVehiclelicense}`,
  method: 'POST',
  data: params,
  channel: 'oneapp'
})

export const getManCarMemberInfo = (params) => request({
  url: `${baseUrl}${URL.getManCarMemberInfo}`,
  method: 'get',
  params
})

export const getFindCustomerCarList = (params) => request({
  url: `${baseUrl}${URL.getFindCustomerCarList}`,
  method: 'get',
  params
})

export const postManCarJoinClub = (params) => request({
  url: `${baseUrl + URL.postManCarJoinClub}`,
  method: 'POST',
  data: params
})

export const postIdAndDrivingLicenseRecognition = (params) => request({
  url: `${baseUrl + URL.postIdAndDrivingLicenseRecognition}`,
  method: 'POST',
  data: params
})

export const postOcrImageUpload = (params) => request({
  url: `${baseUrl + URL.postOcrImageUpload}`,
  method: 'POST',
  data: params
})

export const postOcrIdDrivingSubmit = (params) => request({
  url: `${baseUrl + URL.postOcrIdDrivingSubmit}`,
  method: 'POST',
  data: params
})
export const getCaptchaImage = (params) => request({
  url: `${baseUrl + URL.getCaptchaImage}`,
  method: 'get',
  data: params
})

export const postCarUnbinding = (params) => request({
  url: `${baseUrl + URL.postCarUnbinding}`,
  method: 'POST',
  data: params
})

export const postSendSMS = (params) => request({
  url: `${baseUrl + URL.postSendSMS}`,
  method: 'get',
  params
})


export const postVehicleInvoiceRecognition = (params) => request({
  url: `${baseUrl + URL.postVehicleInvoiceRecognition}`,
  method: 'POST',
  data: params
})
export const postVehicleLicenseRecognition = (params) => request({
  url: `${baseUrl + URL.postVehicleLicenseRecognition}`,
  method: 'POST',
  data: params
})

export const postBindCarNumber = (params) => request({
  url: `${baseUrl + URL.postBindCarNumber}`,
  method: 'POST',
  data: params
})
export const addRemark = (params) => request({
  url: `${baseUrl}${URL.addRemark}`,
  method: 'post',
  params
})

export const getNgaDetailsOrder = (params) => request({
  url: `${baseUrl}${URL.getNgaDetailsOrder}`,
  method: 'get',
  params
})

export const getTraceInfo = (params) => request({
  url: `${baseUrl}${URL.getTraceInfo}`,
  method: 'get',
  params
})

export const getCCIDbyVin = (params) => request({
  url: `${baseUrl}${URL.getCCIDbyVin}`,
  method: 'get',
  params
})

export const getGenerateMessage = (params) => request({
  url: `${baseUrl}${URL.getGenerateMessage}`,
  method: 'get',
  params
})

export const getOrderDetails = (params) => request({
  url: `${baseUrl}${URL.getOrderDetails}/${params.orderId}`,
  method: 'get'
})

export const downloadPdfGenerate = (params) => request({
  url: `${baseUrl}${URL.downloadPdfGenerate}`,
  method: 'get',
  params,
  responseType: 'blob'
})
export const getVeryLongReservationGeneratePDF = (params) => request({
  url: `${baseUrl}${URL.getVeryLongReservationGeneratePDF}`,
  method: 'get',
  params,
  responseType: 'blob'
})
export const getVeryLongReservationValidAuth = (params) => request({
  url: `${baseUrl}${URL.getVeryLongReservationValidAuth}`,
  method: 'get',
  params
})

export const getOrgBankList = (params) => request({
  url: `${baseUrl}${URL.getOrgBankList}`,
  method: 'get',
  params
})
export const getOrgBankListV2 = (params) => request({
  url: `${baseUrl}${URL.getOrgBankListV2}`,
  method: 'get',
  params
})

export const createVeryLongReservation = (params) => request({
  url: `${baseUrl}${URL.createVeryLongReservation}`,
  method: 'post',
  params
})

export const getAudiCarUrl = (params) => request({
  url: `${baseUrl}${URL.getAudiCarUrl}`,
  method: 'get',
  params
})

export const getAudiCar2Url = (params) => request({
  url: `${baseUrl}${URL.getAudiCar2Url}`,
  method: 'get',
  params
})

export const getEquityChargingPile = (params) => request({
  url: `${baseUrl}${URL.getEquityChargingPile}`,
  method: 'get',
  params
})

export const getNgaConfirmCanDdPay = (params) => request({
  url: `${baseUrl}${URL.getNgaConfirmCanDdPay}`,
  method: 'get',
  params
})

export const getAudiMinipUrl = (params) => request({
  url: `${baseUrl}${URL.getAudiMinipUrl}`,
  method: 'get',
  params
})

export const getOrderBalancePaymentCertificate = (params) => request({
  url: `${baseUrl}${URL.getOrderBalancePaymentCertificate}`,
  method: 'get',
  params
})

export const myApplyInfo = (params) => request({
  url: `${baseUrl}${URL.myApplyInfo}`,
  method: 'get',
  params
})

export const cancelEnroll = (params) => request({
  url: `${baseUrl}${URL.cancelEnroll}`,
  method: 'get',
  params
})

export const getAfterSalesListForReservation = (params) => request({
  url: `${baseUrl}${URL.getAfterSalesListForReservation}`,
  method: 'get',
  params
})

export const getChargingPileStatusList = (params) => request({
  url: `${baseUrl}${URL.getChargingPileStatusList}`,
  method: 'get',
  params
})

export const canXdUpdateInfo = (params) => request({
  url: `${baseUrl}${URL.canXdUpdateInfo}`,
  method: 'get',
  params
})
export const getqueryRelativeInformation = (params) => request({
  url: `${baseUrl}${URL.getqueryRelativeInformation}`,
  method: 'get',
  params
})

export const getqueryRelativeMaterials = (params) => request({
  url: `${baseUrl}${URL.getqueryRelativeMaterials}`,
  method: 'get',
  params
})

export const saveRelativeInformation = (data) => request({
  url: `${baseUrl}${URL.saveRelativeInformation}`,
  method: 'post',
  data
})
export const getqueryRelativeInformationById = (params) => request({
  url: `${baseUrl}${URL.queryRelativeInformationById}`,
  method: 'get',
  params
})


export const uploadOSSFile = (params, data) => request({
  url: `${baseUrl}${URL.uploadOSSFile}/?materialType=${params.materialType}`,
  method: 'post',
  data
})


export const updateToFullPayment = (params) => request({
  url: `${baseUrl}${URL.updateToFullPayment}`,
  method: 'post',
  params
})
export const getSourceId = (params) => request({
  url: `${baseUrl}${URL.getSourceId}`,
  method: 'post',
  params
})

export const getShoppingCartDetail = (params) => request({
  url: `${baseUrl}${URL.getShoppingCartDetail}`,
  method: 'get',
  params
})

export const getInviteUserInfo = (params) => request({
  url: `${baseUrl}${URL.getInviteUserInfo}`,
  method: 'get',
  params
})

export const clickLoanAgent = (params) => request({
  url: `${baseUrl}${URL.clickLoanAgent}`,
  method: 'post',
  params
})

export const getXdLoanAgentStatus = (params) => request({
  url: `${baseUrl}${URL.getXdLoanAgentStatus}`,
  method: 'get',
  params
})

export const getQueryLicenseStatus = (params) => request({
  url: `${baseUrl}${URL.getQueryLicenseStatus}`,
  method: 'get',
  params
})
export const getRevokeRefund = (id) => request({
  url: `${baseUrl}/api-wap/audi-eshop/api/v1/returns/nga/${id}/cancel`,
  method: 'put'
})

export const getBlessedPackInfo = (params) => request({
  url: `${baseUrl}${URL.getBlessedPackInfo}`,
  method: 'get',
  params
})

export const blessedPackConfirmCcid = (params) => request({
  url: `${baseUrl}${URL.blessedPackConfirmCcid}`,
  method: 'get',
  params
})

export const getCarSpecPrice = (params) => request({
  url: `${baseUrl}${URL.getCarSpecPrice}`,
  method: 'get',
  params
})

export const getCarInsurance = (data) => request({
  url: `${baseUrl}${URL.getCarInsurance}`,
  method: 'POST',
  data
})


export const queryNgaOrderInfoConfirm = (data) => request({
  url: `${baseUrl}${URL.queryNgaOrderInfoConfirm}`,
  method: 'get',
  params: data
})

export const confirmUpdate = (data) => request({
  url: `${baseUrl}${URL.confirmUpdate}`,
  method: 'get',
  params: data
})

export const getNgaPrice = (params) => request({
  url: `${baseUrl}${URL.getNgaPrice}`,
  method: 'GET',
  params
})

export const getCheckChangeConfiguration = (params) => request({
  url: `${baseUrl}${URL.getCheckChangeConfiguration}`,
  method: 'GET',
  params
})

export const setConfirmChangeConfiguration = (data) => request({
  url: `${baseUrl}${URL.setConfirmChangeConfiguration}`,
  method: 'POST',
  data
})

export const checkOrderModifiedInfo = (params) => request({
  url: `${baseUrl}${URL.checkOrderModifiedInfo}`,
  method: 'GET',
  params
})

export const checkOrderHasSalesCoupons = (params) => request({
  url: `${baseUrl}${URL.checkOrderHasSalesCoupons}`,
  method: 'GET',
  params
})

export const setSaleETicketTransact = (params) => request({
  url: `${baseUrl}${URL.setSaleETicketTransact}`,
  method: 'GET',
  params
})

export const getOrderEquities = (params) => request({
  url: `${baseUrl}${URL.getOrderEquities}`,
  method: 'GET',
  params
})

export const getMineInsuranceVoucher = (params) => request({
  url: `${baseUrl}${URL.getMineInsuranceVoucher}`,
  method: 'GET',
  params
})

export const getZMRightsProdId = (data) => request({
  url: `${baseUrl}${URL.getZMRightsProdId}`,
  method: 'GET',
  data
})

export const getAudiMallH5Url = (data) => request({
  url: `${baseUrl}${URL.getAudiMallH5Url}`,
  method: 'GET',
  data
})

export const setFinanceReplacement = (params) => request({
  url: `${baseUrl}${URL.setFinanceReplacement}`,
  method: 'GET',
  params
})

export const postUseMpCoupon = (data) => request({
  url: `${baseUrl}${URL.useMpCoupon}`,
  method: 'POST',
  data
})


export const getqueryOverseasStudentsInformation = (params) => request({
  url: `${baseUrl}${URL.getqueryOverseasStudentsInformation}`,
  method: 'get',
  params
})

export const getqueryOverseasStudentsMaterial = (params) => request({
  url: `${baseUrl}${URL.getqueryOverseasStudentsMaterial}`,
  method: 'get',
  params
})

export const getqueryOverseasStudentsInformationById = (params) => request({
  url: `${baseUrl}${URL.getqueryOverseasStudentsInformationById}`,
  method: 'get',
  params
})

export const overseasStudentsMaterial = (params, data) => request({
  url: `${baseUrl}${URL.overseasStudentsMaterial}/?materialType=${params.materialType}`,
  method: 'post',
  data
})

export const saveOverseasStudentsInformationn = (data) => request({
  url: `${baseUrl}${URL.saveOverseasStudentsInformationn}`,
  method: 'post',
  data
})
export const checkRefreshStockCar = (params) => request({
  url: `${baseUrl}${URL.refreshStockCar}`,
  method: 'GET',
  params
})

export const getStockCarConfigDetail = (params) => request({
  url: `${baseUrl}${URL.getStockCarConfigDetail}`,
  method: 'GET',
  params
})

export const checkStockCar = (params) => request({
  url: `${baseUrl}${URL.checkStockCar}`,
  method: 'GET',
  params
})

export const unBindDrivingLicense = (params) => request({
  url: `${baseUrl + URL.unBindDrivingLicense}`,
  method: 'DELETE',
  data: params
})

export const postusedCarVehicleInvoice = (params) => request({
  url: `${baseUrl + URL.postusedCarVehicleInvoice}`,
  method: 'POST',
  data: params
})
export const queryEnterprisingEliteInformation = (params) => request({
  url: `${baseUrl}${URL.queryEnterprisingEliteInformation}`,
  method: 'get',
  params
})

export const queryEnterprisingEliteMaterials = (params) => request({
  url: `${baseUrl}${URL.queryEnterprisingEliteMaterials}`,
  method: 'get',
  params
})

export const saveEnterprisingEliteInformation = (data) => request({
  url: `${baseUrl}${URL.saveEnterprisingEliteInformation}`,
  method: 'post',
  data
})
export const queryEnterprisingEliteInformationById = (params) => request({
  url: `${baseUrl}${URL.queryEnterprisingEliteInformationById}`,
  method: 'get',
  params
})


export const enterprisingEliteMaterialUploadOSSFile = (params, data) => request({
  url: `${baseUrl}${URL.enterprisingEliteMaterialUploadOSSFile}/?materialType=${params.materialType}`,
  method: 'post',
  data
})

export const checkPGCData = (params) => request({
  url: `${baseUrl}${URL.checkPGCData}`,
  method: 'GET',
  params
})

export const checkOrderTraceInfoStatus = (params) => request({
  url: `${baseUrl}${URL.checkOrderTraceInfoStatus}`,
  method: 'GET',
  params
})

export const updateOrderTraceInfoStatus = (params) => request({
  url: `${baseUrl}${URL.updateOrderTraceInfoStatus}`,
  method: 'PUT',
  params
})

export const getOrderMniStatus = (params) => request({
  url: `${baseUrl}${URL.getOrderMniStatus}`,
  method: 'GET',
  params
})

export const sendEnterpriseWeChatMessage = (params) => request({
  url: `${baseUrl}${URL.sendEnterpriseWeChatMessage}`,
  method: 'GET',
  params
})
// offlineSignContract, changeDownLoadContractStatus
export const setEnterpriseChannelSign = (
  params,
  sign = 'offlineSignContract'
) => request({
  url: `${baseUrl}${URL.setEnterpriseChannelSign}${sign}`,
  method: 'GET',
  params
})

export const getOrderManCarVerify = (data) => request({
  url: `${baseUrl}${URL.getOrderManCarVerify}`,
  method: 'post',
  data
})
export const sendingEMailContract = (data) => request({
  url: `${baseUrl}${URL.sendingEMailContract}`,
  method: 'POST',
  data
})

export const downloadContractFile = (params, onDownloadProgress) => request({
  url: `${baseUrl}${URL.downloadContractFile}`,
  method: 'GET',
  responseType: 'blob',
  params

  // onDownloadProgress(onDownloadProgress) {
  //   const { loaded, total } = onDownloadProgress
  //   store.dispatch('SetDownloadLivingData', { loaded, total })
  // }
})

export const getContractFileType = (params) => request({
  url: `${baseUrl}${URL.getContractFileType}`,
  method: 'GET',
  params
})

// 供应商
export const querySupplierEmployeesInformation = (params) => request({
  url: `${baseUrl}${URL.querySupplierEmployeesInformation}`,
  method: 'get',
  params
})

export const querySupplierEmployeesInformationById = (params) => request({
  url: `${baseUrl}${URL.querySupplierEmployeesInformationById}`,
  method: 'get',
  params
})

export const querySupplierEmployeesMaterials = (params) => request({
  url: `${baseUrl}${URL.querySupplierEmployeesMaterials}`,
  method: 'get',
  params
})

export const supplierEmployeesMaterialUploadOSSFile = (params, data) => request({
  url: `${baseUrl}${URL.supplierEmployeesMaterialUploadOSSFile}/?materialType=${params.materialType}`,
  method: 'post',
  data
})

export const saveSupplierEmployeesInformation = (data) => request({
  url: `${baseUrl}${URL.saveSupplierEmployeesInformation}`,
  method: 'post',
  data
})

export const getSupplierCheckSupplierEmployees = (params) => request({
  url: `${baseUrl}${URL.getSupplierCheckSupplierEmployees}`,
  method: 'get',
  params
})

// 特定政府机构员工
export const queryGovernmentApparatusInformation = (params) => request({
  url: `${baseUrl}${URL.queryGovernmentApparatusInformation}`,
  method: 'get',
  params
})

export const queryGovernmentEmployeesInformationById = (params) => request({
  url: `${baseUrl}${URL.queryGovernmentEmployeesInformationById}`,
  method: 'get',
  params
})

export const queryGovernmentApparatusMaterial = (params) => request({
  url: `${baseUrl}${URL.queryGovernmentApparatusMaterial}`,
  method: 'get',
  params
})

export const governmentEmployeesMaterialUploadOSSFile = (params, data) => request({
  url: `${baseUrl}${URL.governmentEmployeesMaterialUploadOSSFile}/?materialType=${params.materialType}`,
  method: 'post',
  data
})

export const saveGovernmentApparatusInformation = (data) => request({
  url: `${baseUrl}${URL.saveGovernmentApparatusInformation}`,
  method: 'post',
  data
})
export const digitCollectionJumpURL = (params) => request({
  url: `${baseUrl}${URL.digitCollectionJumpURL}`,
  method: 'GET',
  params
})

// 归国精英
export const queryReturnedEliteInformation = (params) => request({
  url: `${baseUrl}${URL.queryReturnedEliteInformation}`,
  method: 'get',
  params
})

export const queryReturnedEliteInformationById = (params) => request({
  url: `${baseUrl}${URL.queryReturnedEliteInformationById}`,
  method: 'get',
  params
})

export const queryReturnedEliteHandlingMethods = (params) => request({
  url: `${baseUrl}${URL.queryReturnedEliteHandlingMethods}`,
  method: 'get',
  params
})

export const queryReturnedEliteMaterials = (params) => request({
  url: `${baseUrl}${URL.queryReturnedEliteMaterials}`,
  method: 'get',
  params
})

export const returnedEliteMaterialUploadOSSFile = (params, data) => request({
  url: `${baseUrl}${URL.returnedEliteMaterialUploadOSSFile}/?materialType=${params.materialType}`,
  method: 'post',
  data
})

export const saveReturnedEliteInformation = (data) => request({
  url: `${baseUrl}${URL.saveReturnedEliteInformation}`,
  method: 'post',
  data
})

// 政企
export const queryGovernmentEnterpriseInformation = (params) => request({
  url: `${baseUrl}${URL.queryGovernmentEnterpriseInformation}`,
  method: 'get',
  params
})

export const queryGovernmentEnterpriseInformationById = (params) => request({
  url: `${baseUrl}${URL.queryGovernmentEnterpriseInformationById}`,
  method: 'get',
  params
})

export const queryGovernmentEnterpriseMaterials = (params) => request({
  url: `${baseUrl}${URL.queryGovernmentEnterpriseMaterials}`,
  method: 'get',
  params
})

export const governmentEnterpriseMaterialUploadOSSFile = (params, data) => request({
  url: `${baseUrl}${URL.governmentEnterpriseMaterialUploadOSSFile}/?materialType=${params.materialType}`,
  method: 'post',
  data
})

export const saveGovernmentEnterpriseInformation = (data) => request({
  url: `${baseUrl}${URL.saveGovernmentEnterpriseInformation}`,
  method: 'post',
  data
})

export const checkEmployerName = (params) => request({
  url: `${baseUrl}${URL.checkEmployerName}`,
  method: 'get',
  params
})

export const postCheckVehicleInvoice = (params) => request({
  url: `${baseUrl + URL.postCheckVehicleInvoice}`,
  method: 'POST',
  data: params
})

export const getAPITimeOut = () => request({
  url: `${baseUrl}${URL.getAPITimeOut}`,
  method: 'get'
})

export const getAPITimeOutTesting = (params) => request({
  url: `${baseUrl}${URL.getAPITimeOutTesting}`,
  method: 'get',
  params
})

export const queryCheckCustomizeTagInformation = (params) => request({
  url: `${baseUrl}${URL.queryCheckCustomizeTagInformation}`,
  method: 'get',
  params
})

export const queryCustomizeTagInformationById = (params) => request({
  url: `${baseUrl}${URL.queryCustomizeTagInformationById}`,
  method: 'get',
  params
})

export const queryCustomizeTagMaterials = (params) => request({
  url: `${baseUrl}${URL.queryCustomizeTagMaterials}`,
  method: 'get',
  params
})

export const customizeTagMaterialuploadOSSFile = (params, data) => request({
  url: `${baseUrl}${URL.customizeTagMaterialuploadOSSFile}/?materialType=${params.materialType}`,
  method: 'post',
  data
})

export const saveCustomizeTagInformation = (data) => request({
  url: `${baseUrl}${URL.saveCustomizeTagInformation}`,
  method: 'post',
  data
})

export const queryCheckCustomizeTagInformationHis = (params) => request({
  url: `${baseUrl}${URL.queryCheckCustomizeTagInformationHis}`,
  method: 'get',
  params
})

export const getBindCarInfoList = (params) => request({
  url: `${baseUrl}${URL.getBindCarInfoList}`,
  method: 'get',
  params
})

export const getFavoriteCarMiniprogramData = ({ type, code }) => request({
  url: `${baseUrl}${URL.getFavoriteCarData}?pageAccessType=${
    type || 2
  }&pageFrontCode=${code || 'audi-favoritecar-share-miniprogram'}`,
  method: 'get'
})

// 获客助手
// 申请获客助手URL或小程序二维码
export const getCustomerHelper = (data, submitToken) => request({
  url: `${baseUrl}${URL.getCustomerHelper}`,
  method: 'post',
  data,
  headers: {
    'x-access-token': localStorage.getItem('token'),
    'x-submit-token': submitToken
  }
})
// 点击获客助手wx 发送通知
export const customerHelperSendNotice = (data, submitToken) => request({
  url: `${baseUrl}${URL.getCustomerHelperClick}`,
  method: 'post',
  data,
  headers: {
    'x-access-token': localStorage.getItem('token'),
    'x-submit-token': submitToken
  }
})


/** *
/***
 *    @param data Object
 *    @param data.key     //说明描述  200字符
 *    @param data.value   //value    500字符
 *    @param data.source  //来源     20字符
 *
 * * */

export const saveKfLog = (data) => request({
  url: `${baseUrl}/api-wap/audi-bbs/api/member/user/saveKfLog`,
  method: 'post',
  data
})

export const getConsentByType = (data = {}, headers) => request({
  url: `${consentByTypeUrl}${URL.getConsentByType}`,
  method: 'GET',
  data,
  headers
})

export const getNewConsentByType = (data = {}, headers) => request({
  url: `${consentByTypeUrl}${URL.getNewConsentByType}`,
  method: 'GET',
  data,
  headers
})

// 高德服务
export const afterServiceScGeocodeGeo = (data) => request({
  url: `${baseUrl}${URL.afterServiceScGeocodeGeo}`,
  method: 'post',
  data
})

export const afterServiceScGeo = (data) => request({
  url: `${baseUrl}${URL.afterServiceScGeo}`,
  method: 'post',
  data
})

export const afterServiceScPlaceText = (data) => request({
  url: `${baseUrl}${URL.afterServiceScPlaceText}`,
  method: 'post',
  data
})
