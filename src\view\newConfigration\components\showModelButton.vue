<template>
  <div v-if="!isMinip" class="btn-showmodel">
    <img src="../../../assets/img/icon02.png" @click="showModel3d" alt="">
  </div>
</template>

<script setup>
import { defineProps, ref, getCurrentInstance } from 'vue'
import { useStore } from '@/view/newConfigration/util/vueApi'
import { callNative, getUrlParamObj } from '@/utils'
import {
  getCarConfigcc, getQ5EConfigcc, getQ6cc, getA7MrConfigcc
} from '@/api/api'
import { BLACK_A7L } from '../util/carModelSeatData'
import { A7MR } from '../car/a7mr'

const { env } = getUrlParamObj()
const instance = getCurrentInstance()
const sensors = instance.proxy.$sensors
const store = useStore()

const isMinip = ref(env === 'minip')

// 获取 props
const props = defineProps({
  type: {
    type: String,
    required: true,
    default: 'out' // out | in
  }
})


// 获取车型的模型展示url
const get3dModelUrl = async (name) => {
  const { currentModelLineData } = store.state.configration

  let res
  if (name === 'a7l') {
    if (A7MR.map((i) => i.code).includes(currentModelLineData.modelLineCode)) {
      console.log('mark', 'A7MR')
      res = await getA7MrConfigcc()
    } else {
      res = await getCarConfigcc()
      console.log('mark', 'A7L')
    }
  } else if (name === 'q5e') {
    res = await getQ5EConfigcc()
  } else if (name === 'q6') {
    res = await getQ6cc()
  }

  const { data } = res
  if (data.code !== '00') return console.error('获取url出错')
  return data.data.configValue
}

/**
 * 外观的内饰的区别就是 in | out 的区别
 */
const showModel3d = async () => {
  clickPopupSensors('切换为3D') // 埋点

  store.commit('showLoading')
  const {
    currentModelLineData, currentExterior, currentVos, currentSib, selectedOptions, currentHub, currentEih, currentSeat,
    pageOptionComposes, currentComposeName, personalOptions
  } = store.state.configration

  const currentModelCode = currentModelLineData.modelLineCode
  const currentExCode = currentExterior.optionCode

  let currentInCode = []

  const series = store.getters.currentSeriesName
  const q5e6Seat = 'WE8'
  if (series === 'a7l') {
    currentVos && currentInCode.push(currentVos.optionCode)
    currentSib && currentInCode.push(currentSib.sibOptionCode)
    currentSib && currentInCode.push(currentSib.interieurOptionCode)
  } else {
    currentSib && currentInCode.push(currentSib.interieurOptionCode)
    currentSib && currentInCode.push(currentSib.sibOptionCode)

    // q5e的六七座逻辑
    if (series === 'q5e') {
      if (!currentSeat.tagCode.includes(q5e6Seat)) {
        currentInCode.push('seat7')
      }
    }
  }
  currentInCode = currentInCode.join('+')

  const currentOptions = selectedOptions.map((item) => item.optionCode) || []
  if (series === 'a7l') {
    // 6NQ 黑色顶棚
    const BLACK_TOP = '6NQ'
    const optionExist = selectedOptions.find((i) => i.optionCode === BLACK_TOP) // 选装包数据
    //
    const privateExist = selectedOptions.find((i) => i.optionCode === BLACK_TOP && i.status === 1) // 查找私人订制数据,如果有6NQ的标装,则添加

    if (!optionExist) {
      // BLACK_A7L:a7l 耀黑套装
      if (BLACK_A7L.includes(currentModelCode) || privateExist) {
        currentOptions.push(BLACK_TOP) // 曜黑配置,默认添加黑顶选装
      } else {
        currentOptions.push('N6NQ') // 若没有黑色顶棚,则手动添加自创白色顶棚的code
      }
    }
  }
  if (currentEih.optionCode) {
    currentOptions.push(currentEih.optionCode)
  }

  currentOptions.push('HSP') // iframe隐藏热点

  if (series === 'q6') {
    // q6的六七座逻辑
    if (currentSeat.tagCode.includes('7SEAT')) {
      currentOptions.push('seat7')
    } else {
      currentOptions.push('seat6')
    }

    // q6的红色卡钳逻辑
    const hubPC2 = currentHub.optionCode.includes('PC2')

    // 推荐组合里是否包含红色卡钳
    let composePc2 = ''
    if (currentComposeName) {
      const composes = store.getters.pageOptionComposes
      for (const item of composes) {
        const hasPc2 = item.composePersonalOptions.find((i) => i.optionCode === 'PC2')
        composePc2 = !!hasPc2
      }
    }
    const Q6warrior24Year = currentModelLineData.modelLineName.includes('武士') && currentModelLineData.modelYear === '2024'
    const isQ6RS = ['G6ICAY006', 'G6ICAY015'].includes(currentModelLineData.modelLineCode)// q6rs

    const optionPC2 = personalOptions.find((option) => option.optionCode === 'PC2' && option.condition === 1)

    if (hubPC2 || composePc2 || Q6warrior24Year || isQ6RS || optionPC2) {
      currentOptions.push('PC2')
    } else {
      currentOptions.push('NPC2')
    }
  }

  const currentCar = {
    currentModelCode,
    currentExCode,
    currentInCode,
    currentHub: currentHub.optionCode,
    currentOptions: currentOptions.filter((code) => code !== q5e6Seat),
    platform: 'app',
    showCloseBtn: true,
    showInnerHotspot: '0',
    currentView: props.type // out | in
    // currentScene: 'chengshi' // 当前场景，值为“shanghai”和“chengshi”和“guangzhou”
  }

  let url = 'https://testcc.fontre.com/audi-a7l0.0.6/index.html'
  url = await get3dModelUrl(series)

  const currentCarBase64 = window.btoa(JSON.stringify(currentCar))
  const str = url.includes('?') ? '&' : '?'
  const iframeUrl = `${url + str}hidepanel=1&currentCar=${currentCarBase64}`

  console.log('iframeUrl', iframeUrl, currentCar)
  store.commit('hideLoading')

  await callNative('audiOpen', {
    path: iframeUrl,
    params: 'app',
    pageOrientation: 'landscape'
  })
}

// 埋点
const clickPopupSensors = (operationType) => {
  const carMap = {
    0: 'A7L',
    1: 'Q5 e-tron',
    2: 'Q6'
  }

  const { currentModelLineData, carIdx, currentVersion } = store.state.configration
  const { engine, customSeriesName } = currentModelLineData
  const param = {
    source_module: 'H5',
    car_series: carMap[carIdx],
    car_type: store.getters.currentCarType,
    power_type: `${customSeriesName} ${engine}`,
    car_version: currentVersion.styleName,
    delivery_type: '定制交付', // 快速交付|定制交付
    operation_type: operationType
  }

  // console.log(param)
  sensors.track('CC_CarConfiguration_Operate', param)
}

</script>

<style lang="less" scoped>

.btn-showmodel {
  position: absolute;
  top: 18px;
  left: 18px;
  width: 52px;
  height: 52px;
  z-index: 2;
}

</style>
