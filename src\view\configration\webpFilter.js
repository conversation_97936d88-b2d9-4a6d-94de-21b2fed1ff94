/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-23 22:47:00
 * @Last Modified by: z<PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-04-11 16:09:25
 *
 * oss 图片处理
 */

import Vue from 'vue';

/**
 * 检测当前环境是否支持 webp
 */
(() => {
  const img = new Image()
  img.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA'

  return new Promise((res) => {
    img.onload = () => {
      const result = img.width > 0 && img.height > 0
      res(!!result)
      window.supportsWebp = true
    }
    img.onerror = () => {
      res(false)
      window.supportsWebp = false
    }
  })
})()

// 处理 oss 图片
const OSS_POSTFIX = '?x-oss-process=image'
const OSS_CONFIG = {
  jpg: '/interlace,1/format,jpg', // jpg
  webp: '/format,webp' // jpg
}
// /interlace,1/format,jpg
// 价格过滤的filter
Vue.filter('imgFix', (val, width, useJpg) => {
  if (!val) return ''
  /**
   * 优先用webp格式
   * 不支持webp的情况下 检查是否用jpg
   */
  let lastName = ''
  if (window.supportsWebp) {
    lastName = OSS_CONFIG.webp
  } else {
    lastName = useJpg ? OSS_CONFIG.jpg : ''
  }
  if(window.audiios){
    val = val.replace('https://', 'customschema://')
    val = val.replace('http://', 'customschema://')
  }
  return `${val}${OSS_POSTFIX}/resize,w_${width}${lastName}`
})
