<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2023-01-30 11:04:33
 * @LastEditors: 姚达威 <EMAIL>
 * @LastEditTime: 2024-11-30 11:07:53
 * @FilePath     : \src\view\contract\enterprise\knows.vue
 * @Descripttion :
-->
<template>
  <div
    :class="
      ['contract-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      @handleLeftBack="handleLeftBack"
    />
    <div
      class="main-wrapper"
      :style="{'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`, height: `calc(100vh - ${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px)`}"
    >
      <div
        class="contract-box"
        data-flex="dir:top"
      >
        <dl
          class="dl"
          data-flex="dir:top"
          data-block
        >
          <dd class="dd">
            <ul>
              <li
                class="li"
                style="margin: 0;"
              >
                <p class="p">
                  {{ `接下来您（${mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}）号码将接收到上汽奥迪工作人员发起的合同签署短信链接。` }}
                </p>
              </li>
              <li class="li">
                <p class="p">
                  请在与上汽奥迪代理商的奥迪管家确认链接信息准确后，点击该链接至第三方合同签署平台（法大大SAAS平台）开始线上签暑购车合同流程。
                </p>
              </li>
            </ul>
          </dd>
        </dl>
        <div
          class="affirm-order-info-box"
          data-flex="cross:bottom main:justify"
        >
          <div class="lan-button-box black-button ghost-button line-two-cols">
            <van-button
              class="lan-button"
              @click="$router.back()"
            >
              再考虑一下
            </van-button>
          </div>
          <div
            class="lan-button-box black-button line-two-cols"
          >
            <van-button
              @click="handleOnlineContract"
              :loading="loading"
              :loading-text="loading ? '跳转中...' : ''"
              class="lan-button"
            >
              已知悉
            </van-button>
          </div>
        </div>

        <!-- <van-loading
          class="lan-loading-custom lan-custom-center lan-loading-darkly enable-masking-out"
          size="24px"
          vertical
          v-if="loading"
        >
          发起签署中…
        </van-loading> -->
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import {
  Button, Loading, Toast, Popup
} from 'vant'
import HeaderCustom from '@/components/header-custom.vue'
import { sendEnterpriseWeChatMessage } from '@/api/api'

Vue.use(Button).use(Loading).use(Toast).use(Popup)
export default {
  components: {
    'header-custom': HeaderCustom
  },
  data() {
    return {
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      orderId: '',
      seriesCode: '',
      mobile: '',
      loading: false,
      orderType:''
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      const { orderId, mobile,orderType } = this.$route.query
      orderId && (this.orderId = orderId)
      mobile && (this.mobile = mobile)
      orderType && (this.orderType = orderType)
    },
    async handleOnlineContract() {
      const { orderId,orderType, $router } = this
      this.loading = true
      const { data: { code, data } } = await sendEnterpriseWeChatMessage({ orderId }).finally((s) => {
        this.loading = false
      })
      Toast({
        message: code === '00' ? '发起签署流程成功' : '发起签署流程失败',
        className: 'toast-dark-mini toast-pos-middle',
        forbidClick: true,
        duration: 800,
        onClose() {
          if (code === '00') {
            $router.push({
              name: 'contract-enterprise-finished',
              query: {
                orderId,
                orderType
              }
            })
          }
        }
      })
    },
    handleLeftBack() {}
  }
}
</script>
<style lang="less" scoped>
.contract-wrapper {
  .contract-box {
    height: 100%;
    font-size: 14px;
    color: #333;
    line-height: 22px;
    .dl {
      margin: 0;
      padding: 16px;
      .dd {
        margin: 0;
        .li {
          margin: 24px 0 0 0;
          .h4, .p {
            margin: 0 0 4px 0;
          }
          .h4 {
            font-family: 'Audi-WideBold';
            font-weight: normal;
            .spu {
              color: #999;
              font-size: 12px;
            }
          }
          // &:last-child {
          //   margin-top: 24px;
          // }
        }
      }
    }
  }
  .affirm-order-info-box {
    padding: 16px 16px 50px 16px;
  }
}
</style>
