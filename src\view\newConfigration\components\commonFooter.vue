<template>
  <div class="footer-wrapper">
    <div class="footer c-footer-shadow c-flex-between c-font14">
      <div class="delivery-text-wrapper c-font14" v-show="!popupVisible">
        {{ footerDesc.desc }}
      </div>

      <div class="left">
        <div class="price-wrapper c-bold c-font16"  @click="showPriceDetail" >
          ￥{{ totalPrice | formatPrice }} <van-icon name="arrow-up" class=""/>
        </div>

        <div class="deposit-wrapper  c-font14 c-lh24">
          定金: ¥ {{ currentEarnestMoney | formatPrice }}
        </div>
      </div>

      <div class="right">
        <div class="next-btn" @click="commonNextPage">
          下一步
        </div>
      </div>
    </div>

    <!-- 价格明细弹窗-->
    <van-popup v-model="popupVisible" position="bottom" closeable @close="closePopup">
      <div class="pop-wrapper c-font12">

        <div class="c-bold c-font16 price-title">价格明细</div>
        <div>
          <PriceList/>
        </div>

        <div class="line price">
          <div class="c-lh20">总价</div>
          <div class="c-bold">￥{{ totalPrice | formatPrice }}</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import { Popup, DropdownMenu, DropdownItem } from 'vant'
import { mapState, mapGetters } from 'vuex'
import PriceList from './priceList.vue'

Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Popup)

export default {
  components: { PriceList },
  data() {
    return {
      popupVisible: false
    }
  },
  computed: {
    ...mapGetters(['currentEarnestMoney', 'currentCarType']),
    ...mapState({
      footerDesc: (state) => state.configration.footerDesc,
      totalPrice: (state) => state.configration.totalPrice,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentVersion: (state) => state.configration.currentVersion,
      carIdx: (state) => state.configration.carIdx
    })
  },
  props: {

  },
  created() {
    // this.active  = this.props.active
  },
  mounted() {
  },
  methods: {
    // 显示隐藏价格弹窗
    showPriceDetail() {
      this.popupVisible = !this.popupVisible

      // 埋点
      const operation = this.popupVisible ? '展开价格明细' : '收起价格明细'
      this.clickPopupSensors(operation)
    },

    // 下一步的一些公共逻辑
    commonNextPage() {
      this.popupVisible = false
      this.$emit('click')
    },
    // 收起的埋点
    closePopup() {
      this.clickPopupSensors('收起价格明细')
    },

    // 埋点
    clickPopupSensors(operationType) {
      const carMap = {
        0: 'A7L',
        1: 'Q5 e-tron',
        2: 'Q6'
      }
      const {
        engine, customSeriesName
      } = this.currentModelLineData
      const param = {
        source_module: 'H5',
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: '定制交付', // 快速交付|定制交付
        operation_type: operationType
      }

      // console.log(param)
      this.$sensors.track('CC_CarConfiguration_Operate', param)
    }
  }
}
</script>

<style scoped lang="less">
@import "../../../assets/style/common.less";

@HEIGHT: 100px;
.footer {
  background: #fff;
  position: fixed;
  z-index: 20;
  bottom: 0;
  left: 0;
  width: 100%;
  height: @HEIGHT;
  padding: 5px 13px 0 13px;
  box-sizing: border-box;

  .left {
    height: min-content;

    .price-wrapper {
      margin-top: 10px;
    }

    .deposit-wrapper {
      color: #999999;
      margin-top: 4px;
    }
  }

  .right {
    border: 1px solid;
    height: min-content;

    >.next-btn {
      &:extend(.c-font16);
      width: 140px;
      line-height: 56px;
      background-color: #000;
      color: #fff;
      text-align: center;
    }
  }

  .delivery-text-wrapper {
    position: absolute;
    left: 0;
    bottom: 100%;
    width: 100%;
    line-height: 32px;
    background-color: #F2F2F2;
    padding-left: 16px;
    box-sizing: border-box;
  }
}


.pop-wrapper {
  padding: 16px 16px;
  >.price-title{
    line-height: 26px;
  }
}


.line {
  .c-flex-between;
}

/deep/.van-icon-arrow-up{
  &::before{
  vertical-align: middle;
  }
}

/deep/.van-overlay {
  z-index: 10 !important;
}

.van-popup {
  z-index: 15 !important;
  bottom: @HEIGHT;
}

/deep/.van-popup__close-icon--top-right {
  color:#000;
}
</style>
