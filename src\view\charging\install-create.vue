<template>
  <div class="_container">
    <div class="_heardImg">
      <img src="../../assets/img/charging-pile-install-img.png">
    </div>

    <div class="line">
      <div class="title">
        安装地址
      </div>
      <div
        class="btn-change"
        @click="cityModelStatus = true"
      >
        <div
          class="change-name"
          v-if="!cityName"
          style="color: #999; font-size: 15px"
        >
          {{ "请选择" }}
        </div>
        <div
          class="change-name"
          v-if="cityName"
        >
          {{ cityName }}
        </div>
        <img
          class="btn-icon"
          src="../../assets/img/icon11.png"
          size="16px"
        >
      </div>
    </div>

    <van-field
      v-model="address"
      input-align="right"
      label="详细地址"
      placeholder="请输入"
    />
    <!-- <div class="line">
      <div class="title">电源类型</div>
      <div class="btn-change" >

        <div class="change-name" v-if="chargePileModel">
          {{ chargePileModel }}
        </div>
      </div>
    </div> -->

    <div class="line">
      <div class="title">
        购车人姓名
      </div>
      <div class="btn-change">
        <div class="change-name">
          {{ buyCarName }}
        </div>
      </div>
    </div>
    <div class="line">
      <div class="title">
        购车人联系方式
      </div>
      <div class="btn-change">
        <div class="change-name">
          {{ buyCarMobile }}
        </div>
      </div>
    </div>


    <div class="line">
      <div class="title">
        车主姓名
      </div>
      <div class="btn-change">
        <div
          class="change-name"
          v-if="clientName"
        >
          {{ clientName }}
        </div>
      </div>
    </div>

    <div class="line">
      <div class="title">
        车主联系方式
      </div>
      <div class="btn-change">
        <div
          class="change-name"
          v-if="clientPhone"
        >
          {{ clientPhone }}
        </div>
      </div>
    </div>

    <div
      class="line"
      style="justify-content: space-between"
    >
      <div class="title">
        车主与安装人信息相同
      </div>
      <div
        class="btn-change"
        style="
          width: 27px;
          height: 27px;
          align-items: right;
          margin-right: 16px;
        "
      >
        <van-checkbox
          class="btn-icon"
          v-model="isCheckbox"
          disabled="disabled"
          @click="onCheckbox"
        >
          <img
            style="width: 17px; height: 17px"
            slot="icon"
            :src="isCheckbox ? activeIcon : inactiveIcon"
          >
        </van-checkbox>
      </div>
    </div>

    <van-field
      v-model="contact"
      input-align="right"
      label="安装人姓名"
      placeholder="请输入"
    />

    <van-field
      v-model="mobile"
      input-align="right"
      maxlength="11"
      type="number"
      label="安装人联系方式"
      placeholder="请输入"
    />
    <van-field
      v-model="estateMobile"
      input-align="right"
      label="物业联系方式(选填)"
      placeholder="请输入"
    />
    <div class="btn-delete-height" />
    <div>
      <div class="bottom_style">
        <div class="btn-delete-wrapper">
          <AudiButton
            @click="onSubmit"
            :text="'确认'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
      </div>
    </div>

    <van-action-sheet
      v-model="show"
      :actions="actions"
      @select="onSelect"
    />
    <van-popup
      v-model="cityModelStatus"
      class="lan-popup-area"
      position="bottom"
    >
      <van-area
        title="选择城市"
        class="cancel-icon cross-icon"
        :area-list="areaList"
        @cancel="cityModelStatus = false"
        @confirm="getCityName"
      />
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import { areaList } from '@vant/area-data'
import {
  Form,
  Field,
  Button,
  ActionSheet,
  Cell,
  Popup,
  Area,
  Toast,
  Checkbox,
  CheckboxGroup
} from 'vant'
import AudiButton from '@/components/audi-button'
import { getChargingPileCarOwnerInfo, postChargingPileSubmit } from '@/api/api'
import { callNative, getLocationProvince } from '@/utils'

Vue.use(Form)
  .use(Field)
  .use(Button)
  .use(ActionSheet)
  .use(Cell)
  .use(Popup)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Area)
  .use(Toast)
export default {
  components: {
    AudiButton
  },
  data() {
    return {
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      isCheckbox: false,
      orderId: '',

      province: '', // 安装省份
      city: '', // 安装计市
      county: '', // 安装计县
      address: '', // 安装详细地址
      chargePileModel: '', // 充电桩规格(型号)

      cityName: '', // 显示的省市区
      estateMobile: '', // 物业联系方式

      clientName: '', // 客户名称
      clientPhone: '', // 客户电话

      contact: '', // 联系人
      mobile: '', // 手机

      clientRemark: '', // 备注
      esaleNumber: '', // 销售订单号
      buyCarName: '',
      buyCarMobile: '',

      show: false,
      cityModelStatus: false,
      areaList,
      actions: [{ name: '7kw' }, { name: '11KW' }]

    }
  },

  async mounted() {
    this.orderId = this.$route.query.orderId
    this.chargePileModel = this.$route.query.chargePileModel
    this.getChargingPileCarOwnerInfo()
    this.initAddress()
  },
  methods: {
    async initAddress() {
      const data = await callNative('getLocationCity', {})
      const result = await getLocationProvince([data.location.split(',')[1], data.location.split(',')[0]])
      this.province = result.regeocode.addressComponent.province
      this.city = result.regeocode.addressComponent.city

      this.county = result.regeocode.addressComponent.district
      this.cityName = this.province + this.city + this.county
    },

    async getChargingPileCarOwnerInfo() {
      const { data } = await getChargingPileCarOwnerInfo({
        appoId: this.orderId
      })
      if (data.code === '00') {
        this.clientName = data.data.carOwnerName
        this.clientPhone = data.data.carOwnerMobile

        this.buyCarName = data.data.buyCarName
        this.buyCarMobile = data.data.buyCarMobile
      }
    },
    onSelect(item) {
      this.show = false
      this.chargePileModel = item.name
      console.log(item)
    },

    getCityName(e) {
      console.log(e)
      this.cityModelStatus = false
      this.province = e[0].name
      this.city = e[1].name
      this.county = e[2].name
      this.cityName = this.province + this.city + this.county
    },
    onCheckbox() {
      this.isCheckbox = !this.isCheckbox
      if (this.isCheckbox) {
        this.contact = this.clientName
        this.mobile = this.clientPhone
      } else {
        this.contact = ''
        this.mobile = ''
      }
    },
    async onSubmit() {
      const { province, city, county } = this
      let x = 0
      if (![province, city].every((i, k) => {
        x = k
        return i
      })) {
        const area = ['省份', '城市', '区县']
        return Toast({
          className: 'toast-dark-mini toast-pos-middle',
          message: `安装地址[${area[x]}]选择失败，请重新选择`,
          forbidClick: true,
          duration: 800
        })
      }
      if (this.cityName === '') {
        callNative('toast', { type: 'fail', message: '请选择安装地址' })
        return
      }
      if (this.address === '') {
        callNative('toast', { type: 'fail', message: '请输入详细地址' })
        return
      }
      // if (this.chargePileModel === '') {
      //   callNative('toast', { type: 'fail', message: '请选择电源类型' })
      //   return
      // }
      if (this.contact === '') {
        callNative('toast', { type: 'fail', message: '请输入联系姓名' })
        return
      }
      if (this.mobile === '' || this.mobile.length < 11) {
        callNative('toast', { type: 'fail', message: '请输入正确的联系人联系方式' })
        return
      }
      // if (this.clientName === '') {
      //     callNative('toast', { type: 'fail', message: '请输入车主姓名' })
      //     return
      //   }
      //   if (this.clientPhone === '') {
      //     callNative('toast', { type: 'fail', message: '请输入车主联系方式' })
      //     return
      //   }

      const param = {
        appoId: this.orderId,
        province: this.province,
        city: this.city,
        county: this.county,
        address: this.address,
        chargePileModel: this.chargePileModel,
        clientName: this.clientName,
        clientPhone: this.clientPhone,
        contact: this.contact,
        mobile: this.mobile,
        estateMobile: this.estateMobile

      }
      const pa = {
        appoId: '6898834243284242432',
        province: '北京市',
        city: '北京市',
        county: '东城区',
        address: '详细地址',
        chargePileModel: '7kw',
        clientName: '测试1',
        clientPhone: '15000876169',
        contact: '测试的',
        mobile: '15000876168',
        estateMobile: '15000876167'
      }
      this.$store.commit('showLoading')
      const { data } = await postChargingPileSubmit(param)
      this.$store.commit('hideLoading')
      if (data.code === '00') {
        this.$router.back(-1)
      } else {
        callNative('toast', { type: 'fail', message: data.message })
      }
    }


  }
}
</script>

<style lang='less' scoped>
::v-deep .van-cell {
  padding: 16px;
}

::v-deep .van-cell::after {
  border-bottom: 1px #e5e5e5 solid;
}

::v-deep .van-button {
  border-radius: 0;
}

::v-deep .van-field__label {
  font-size: 16px;
  color: #000;
  width: fit-content;
}

::v-deep .van-field {
  .van-field__body {
    textarea {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }

    input {
      &::placeholder {
        color: #999;
        font-size: 15px !important;
      }
    }
  }
}

._heardImg {
  width: 100%;
}

._content {
  width: 100%;
  padding: 8px;
}

.line {
  display: flex;
  align-items: center;
  margin: 16px 16px 0;

  padding-bottom: 16px;
  border-bottom: 1px solid #f2f2f2;
  white-space: nowrap;

  .title {
    font-size: 16px;
    color: #000;
    // margin: 5px 5px;
    white-space: nowrap;
  }

  .btn-change {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .change-name {
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 100%;
      font-size: 14px;
      color: #333;
    }

    .btn-icon {
      width: 20px;
      height: 20px;
      padding-left: 8px;
    }
  }
}

.btn-delete-height {
  height: 80px;
}

.btn-delete-wrapper {
  margin: 16px;
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: absolute;
  bottom: 0px;
  // padding: 16px;
  left: 0px;
}
</style>
