
.pop-coupons-box {
  .pop-coupons-main {
    padding-bottom: 40px;
    box-sizing: border-box;
    overflow-y: auto;
    .coupons {
      .van-cell {
        &.list {
          position: relative;
          width: auto;
          margin: 16px;
          padding: 11px;
          border: 1px solid #E5E5E5;
          min-height: 64px;
          overflow: visible;
          &::before, &::after {
            display: block;
            z-index: 9;
            position: absolute;
            content: '';
            right: 84px;
            width: 6px;
            height: 3px;
            border: 1px solid #E5E5E5;
            background-color: #fff;
            transition: all .3s;
          }
          &::before {
            top: -1px;
            border-radius: 0 0 7px 7px;
            border-top: 0;
          }
          &::after {
            left: auto;
            bottom: -1px;
            border-radius: 7px 7px 0 0 ;
            border-bottom: 0;
            -webkit-transform: scaleY(1);
            transform: scaleY(1);
            box-sizing: content-box;
          }
        }
      }
      .exception-tips {
        padding: 0 16px;
        font-size: 14px;
        line-height: 24px;
        color: #F50537;
      }
      .coupon-box {
        .coupon-media {
          .media-box, .img {
            height: 70px;
          }
          .media-box {
            width: 70px;
            margin-right: 12px;
            text-align: center;
            overflow: hidden;
          }
        }
        .coupon-status {
          width: 77px;
          color: rgba(#000, .6);
          text-align: center;
          font-weight: 600;
          margin-right: -6px;
          &::after {
            margin: -22px 0 -22px -14px;
          }
        }
        .coupon-info {
          box-sizing: border-box;
          padding-right: 15px;
          width: calc(100vw - 205px);
        }
        .h4, p {
          margin: 0;
        }
        .h4 {
          font-size: 14px;
          line-height: 16px;
        }
        p {
          margin-top: 8px;
          height: 16px;
          line-height: 16px;
          color: rgba(0, 0, 0, .4);
          font-size: 10px;
          &.text-price {
            color: #000;
            font-size: 12px;
            line-height: 15px;
          }
        }
      }
    }
  }
}