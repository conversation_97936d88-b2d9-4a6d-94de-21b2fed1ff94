<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta http-equiv="cache-control" content="no-cache, must-revalidate" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
  <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
  <title>上汽奥迪</title>
  <link rel="stylesheet" href="<%= BASE_URL %>swiper/swiper-bundle.min.css" />
  <style>
      body[data-bg-color='dark'] {
          background: #191919 !important;
      }
      body[data-bg-color='white'] {
          background: #ffffff;
      }
  </style>

  <!-- cop页面重定向 -->
  <script>
    let copOrigin =
      location.origin === 'https://audi-embedded-wap.saic-audi.mobi'
        ? 'https://audi-embedded-wap.saic-audi.mobi'
        : 'https://uat-audi.saic-audi.cn'
    const currentRouter = window.location.hash.split('?')[0]

    if (currentRouter === '#/certification/my-certification') {
      location.replace(
        copOrigin + '/club/bindingCar/#/certification/my-certification'
      )
    } else if (currentRouter === '#/certification/identity-certification') {
      location.replace(copOrigin + '/club/myCertification')
    }
  </script>

  <script>
    const regex = /[?&]([^=#]+)=([^&#]*)/g
    const params = {}
    let match
    while ((match = regex.exec(window.location.href)) !== null) {
      params[match[1]] = match[2]
    }
    if (params.fromType && params.fromType === 'fromPurple') {
      document.body.setAttribute('data-bg-color', 'dark')
    } else {
      document.body.setAttribute('data-bg-color', 'white')
    }
  </script>
  <script src="<%= BASE_URL %>swiper/swiper-bundle.min.js"></script>

  <% if (process.env.VUE_APP_ENV === 'pre' ) { %>
  <script>
    console.log('测试环境')
    window._AMapSecurityConfig = {
      serviceHost: 'https://uataudi-embedded-wap.saic-audi.mobi/_AMapService'
    }
  </script>
  <% } else {%>
  <script>
    console.log('正式环境')
    window._AMapSecurityConfig = {
      serviceHost: 'https://audi-embedded-wap.saic-audi.mobi/_AMapService'
    }
  </script>
  <% } %>
</head>
<body>
<div id="app"></div>
<script
  src="https://webapi.amap.com/maps?v=2.0&key=14e5933fd8cfbc2be9b2ab98e74feefc&plugin=AMap.PlaceSearch,AMap.CitySearch,AMap.Geocoder,AMap.Geolocation,AMap.Autocomplete,AMap.MarkerAMap.Driving"
  type="text/javascript"
></script>
<script src="https://webapi.amap.com/ui/1.1/main.js"></script>
</body>
</html>
