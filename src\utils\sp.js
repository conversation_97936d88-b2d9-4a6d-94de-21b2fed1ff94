/*
 * @Author: <PERSON>
 * @Date: 2021-10-18 16:59:43
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2021-11-01 14:16:43
 * 互斥处理方法
 */

/**
 * 处理冲突，选中选装包时候弹出框使用
 * 输出：
 * tempdependList: 依赖列表,
 * tempconflictList: 冲突列表,
 * price: 价格
 */
export const resolveConflict = (obj) => {
  // clickOption  当前点击的选装包
  // allOptionsItems  当前选装包中的选装件
  // selectCarInfo  当前选中的配置线
  // currentOptionsList  当前的选中的选装包（件）列表
  // currentInteriorChair： 当前选中的座椅
  // currentSibColorInterieur： 当前选中的面料
  // price ： 价格
  const _clickOption = obj.clickOption
  const _allOptionsItems = obj.allOptionsItems
  const _selectCarInfo = obj.selectCarInfo
  const _currentOptionsList = obj.currentOptionsList
  const _currentInteriorChair = obj.currentInteriorChair
  const _currentSibColorInterieur = obj.currentSibColorInterieur
  const _currentInteriorEih = obj.currentInteriorEih
  const q6 = obj.q6

  let _price = obj.price
  // console.log(_allOptionsItems,6666)
  const tempdependList = [] // 需要显示的依赖列表
  const tempconflictList = [] // 需要显示的互斥列表
  if (_clickOption.optionRelates) {
    console.log('配置项:>>', _clickOption.optionRelates)
    _clickOption.optionRelates.forEach((clickOptionItem) => {
      // optionRelates中只有配置项的id, 需要用id在当前所有可选配置项列表中查出相应的数据
      // 依赖项
      const tempdepend = _allOptionsItems.find((optItem, i) => {
        const condition = clickOptionItem.relateType === 'depend' && ((optItem.optionId === clickOptionItem.optionRelateId) || (optItem.sibInterieurId === clickOptionItem.optionRelateId))
        if (condition) {
          console.log(clickOptionItem, 'optItem***', optItem, i)
        }
        return condition
      })
      console.log('_allOptionsItems', _allOptionsItems)
      console.log('@依赖项:', tempdepend)

      // 互斥项
      const tempconflict = _allOptionsItems.find(((optItem) => (clickOptionItem.relateType === 'conflict') && ((optItem.optionId === clickOptionItem.optionRelateId))))
      console.log("tempconflict--------------", tempconflict);
      // 若查询出依赖项,将该依赖项存进可视依赖列表
      // 曜黑款默认黑车顶，其他车选装6NQ黑车顶的时候，依赖项去除白色的那个面料
      const thevehicles = '498B2Y004,498B2Y002,498B2Y005,498BZY002'.includes(_selectCarInfo.modelLineCode)
      if (thevehicles) {
        if (tempdepend && (tempdepend.interieurOptionCode !== 'FH' && tempdepend.interieurOptionCode !== 'FF')) {
          tempdependList.push(tempdepend)
        }
      } else {
        if (_currentOptionsList.find((res) => res.optionCode === '6NQ')) {
          if (tempdepend && (tempdepend.interieurOptionCode !== 'FH' && tempdepend.interieurOptionCode !== 'FF')) {
            tempdependList.push(tempdepend)
          }
        } else {
          tempdepend && tempdependList.push(tempdepend)
        }
      }
      // 若查询出互斥项且互斥项为选装包,将该互斥项存进可视依赖列表
      if (tempconflict) {
        const currentOptionPrice = _currentOptionsList.find((item) => item.optionId === tempconflict.optionId)
        // 8I6,4D3单独处理
        if ('G4IBF3001,G4ICF3001,G4ICF3007,G4IBF3003'.includes(_selectCarInfo.modelLineCode) && '8I6,4D3'.includes(_clickOption.optionCode)) {
          tempconflictList.push(tempconflict)
        } else if(q6){
          if (tempconflict.sibInterieurCode == "N5D-AW") {
          }
          tempconflictList.push(tempconflict)
        } else if (currentOptionPrice) {
          _price -= currentOptionPrice.price
          tempconflictList.push(tempconflict)
        }
      }
    })
  }
  if (_clickOption.sibInterieurRelates) {
    _clickOption.sibInterieurRelates.forEach((clickOptionItem) => {
      // optionRelates中只有配置项的id,需要用id在当前所有可选配置项列表中查出相应的数据
      // 依赖项
      const tempdepend = _allOptionsItems.find(((optItem) => (clickOptionItem.relateType === 'depend') && ((optItem.optionId === clickOptionItem.optionRelateId) || (optItem.sibInterieurId === clickOptionItem.optionRelateId))))
      // 互斥项
      const tempconflict = _allOptionsItems.find(((optItem) => (clickOptionItem.relateType === 'conflict') && ((optItem.optionId === clickOptionItem.optionRelateId))))
      tempdepend && tempdependList.push(tempdepend)
      // 若查询出互斥项且互斥项为选装包,将该互斥项存进可视依赖列表
      if (tempconflict) {
        const currentOptionPrice = _currentOptionsList.find((item) => item.optionId === tempconflict.optionId)
        if (currentOptionPrice) {
          _price -= currentOptionPrice.price
          tempconflictList.push(tempconflict)
          console.log('tempconflictList3333333333333333333', tempconflict, tempconflictList)
        }
      }
    })
  }

  // 处理轮毂 座椅 面料 饰条互斥时的逻辑
  // this.clickOption.packetItems为当前选装包包括的所有选装项
  // eslint-disable-next-line no-undef
  _clickOption.packetItems?.forEach((item) => {
    // 遍历当前装备包中的装备,如果装备包中存在座椅,饰条,内饰,面料,则在互斥列表中添加当前选中的相应配置
    // VOS代表是座椅
    if (item.category === 'VOS') {
      if (!(tempconflictList.find((item) => item.category === 'VOS'))) {
        if (item.optionCode !== _currentInteriorChair.optionCode) {
          tempconflictList.push(_currentInteriorChair)
        }
      }
    } else if (['F_SIB_COLOR_INTERIEUR', 'COLOR_INTERIEUR', 'SIB'].includes(item.category)) {
      if (!(tempconflictList.find((item) => 'F_SIB_COLOR_INTERIEUR,COLOR_INTERIEUR,SIB'.includes(item.sibInterieurCategory)))) {
        if (item.sibInterieurCode !== _currentSibColorInterieur.sibInterieurCode) {
          if (!q6) {
            tempconflictList.push(_currentSibColorInterieur)
          }
          console.log(2222, item, '111', ['F_SIB_COLOR_INTERIEUR', 'COLOR_INTERIEUR', 'SIB'].includes(item.category))
        }
      }
    }

    // else if (item.category === 'EIH') {
    //   if (!(tempconflictList.find((item) => item.category === 'EIH'))) {
    //     tempconflictList.push(_currentInteriorEih)
    //   }
    // }
    // else if (item.category === 'RAD') {
    //   if (!(tempconflictList.find((item) => item.category === 'VOS'))) {
    //     tempconflictList.push(this.currentModelHub)
    //   }
  })
  if (_clickOption.optionRelates?.find((item) => item.relateType === 'depend' && item.optionRelateCategory === 'EIH')) {
    tempconflictList.push(_currentInteriorEih)
  }
  return {
    tempdependList: tempdependList,
    tempconflictList: tempconflictList,
    price: _price
  }
}

/**
 * 选装包依赖项冲突项确认后筛选逻辑，选装包弹出框点确认时候使用
 * 输出：
 * PackagesListEvent ：选装包列表
 * VosListEvent ：面料列表
 * SelectedVosEvent : 选中的面料
 * SibColorListEvent ：座椅列表
 * SelectedSibColorEvent ： 选中的座椅
 * SelectedPackageEquipmentEvent ：选中的选装包
 */
export function outputData(obj) {
  // equipment  当前点击的选装包
  // conflicts  冲突列表
  // depends  依赖列表
  // selected  当前选中面料+颜色
  // selectedPackageEquipment： 选中的选装包
  // packagesList： 选装包列表
  // composeVosList ： 原始座椅列表
  // composeSibColorList  原始面料+颜色列表
  const equipment = obj.equipment
  const conflicts = obj.conflicts
  const depends = obj.depends
  const sibSelected = obj.sibSelected
  const eihSelected = obj.eihSelected
  const vosSelected = obj.vosSelected
  let selectedPackets = obj.selectedPackageEquipment
  const packets = obj.packagesList
  const composeVosList = obj.composeVosList
  const composeSibColorList = obj.composeSibColorList
  const voslist = []
  const eihlist = []
  const sibColorList = []
  let returnObj = {}
  const packetItems = equipment.packetItems
  selectedPackets.push(equipment)
  if (Array.isArray(conflicts)) {
    for (let i = 0; i < conflicts.length; i++) {
      const conflict = conflicts[i]
      console.log('%c conflictconflict', 'font-size:25px;color:green;', conflict)

      if ('PACKET,AUD,null'.includes(conflict.category)) {
        const some = selectedPackets.some((selected) => conflict.optionCode === selected.optionCode)
        if (some) {
          returnObj = deleteOptionPacket(conflict, composeVosList, composeSibColorList)
          selectedPackets = arrayRemove(conflict, selectedPackets)
        }
      }
      console.log('<<<<<<<<<<<<<<selectedPackets', selectedPackets)
    }
  }
  if (Array.isArray(packetItems)) {
    for (let i = 0; i < packetItems.length; i++) {
      const packetItem = packetItems[i]
      if (packetItem.category === 'VOS') {
        const some = voslist.some((vos) => vos.optionCode === packetItem.optionCode)
        if (!some) { voslist.push(packetItem) }
      }
    }
  }

  if (Array.isArray(depends)) {
    for (let i = 0; i < depends.length; i++) {
      const depend = depends[i]
      if (depend.category === 'VOS') {
        const some = voslist.some((vos) => vos.optionCode === depend.optionCode)
        if (!some) { voslist.push(depend) }
      }
      if (depend.category === 'EIH') {
        const some = eihlist.some((eih) => eih.optionCode === depend.optionCode)
        if (!some) { eihlist.push(depend) }
      }
      if (depend.sibInterieurCategory === 'F_SIB_COLOR_INTERIEUR') {
        const some = sibColorList.some((sibColor) => sibColor.sibInterieurCode === depend.sibInterieurCode)
        if (!some) { sibColorList.push(depend) }
      }
      if ('HIM,null'.includes(depend.category) && !selectedPackets.find((item) => item.optionCode === depend.optionCode)) {
        selectedPackets.push(depend)
      }
    }
    console.log('<<<<<<<<<<<<<<selectedPackets22222222', selectedPackets)
  }
  if (Array.isArray(packets)) {
    console.log('<<<<<<<<<<<<<<packets', packets)
    const newPackets = []
    for (let i = 0; i < packets.length; i++) {
      const packet = packets[i]
      packet.selected = selectedPackets.some((value) => value.optionCode === packet.optionCode)
      newPackets.push(packet)
    }
    returnObj.PackagesListEvent = newPackets
  }
  if (Array.isArray(voslist) && voslist.length > 0) {
    returnObj.VosListEvent = voslist
    if (vosSelected && vosSelected.category === 'VOS') {
      returnObj.SelectedVosEvent = vosSelected
    } else {
      returnObj.SelectedVosEvent = voslist[0]
    }
  }

  if (Array.isArray(eihlist) && eihlist.length > 0) {
    returnObj.EihListEvent = eihlist
    if (eihSelected && eihSelected.category === 'EIH') {
      returnObj.SelectedEihEvent = eihSelected
    } else {
      returnObj.SelectedEihEvent = eihlist[0]
    }
  }
  if (Array.isArray(sibColorList) && sibColorList.length > 0) {
    returnObj.SibColorListEvent = sibColorList
    if (sibSelected && sibSelected.sibInterieurCategory === 'F_SIB_COLOR_INTERIEUR') {
      returnObj.SelectedSibColorEvent = sibSelected
    } else {
      returnObj.SelectedSibColorEvent = sibColorList[0]
    }
  }
  returnObj.SelectedPackageEquipmentEvent = selectedPackets

  return returnObj
}

/**
 * 删除某个选装包逻辑
 * 输出：
 * VosListEvent ：座椅列表
 * SelectedVosEvent ：选中的座椅
 * SibColorListEvent : 面料+颜色列表
 * SelectedSibColorEvent ：选中的面料+颜色
 */
export function deleteOptionPacket(equipment, composeVosList, composeSibColorList) {
  // equipment  当前点击的选装包
  // composeVosList ： 原始座椅列表
  // composeSibColorList  原始面料+颜色列表
  const packetItems = equipment.packetItems
  const optionRelates = equipment.optionRelates
  const returnObj = {}
  if (Array.isArray(packetItems)) {
    for (let i = 0; i < packetItems.length; i++) {
      const value = packetItems[i]
      if (value.category === 'VOS') {
        returnObj.VosListEvent = composeVosList.filter((obj) => obj.status === 1 || obj.status === 2)
        const array = composeVosList.filter((obj) => obj.defaultConfig === 1)
        if (array.length > 0) { returnObj.SelectedVosEvent = array[0] }
        if (array.length === 0) { returnObj.SelectedVosEvent = composeVosList[0] }
      }
    }
  }
  if (Array.isArray(optionRelates)) {
    for (let i = 0; i < optionRelates.length; i++) {
      const value = optionRelates[i]
      if (value.optionRelateCategory === 'F_SIB_COLOR_INTERIEUR' && value.relateType === 'depend') {
        const array = composeSibColorList.filter((obj) => obj.status === 1 || obj.status === 2)
        if (array.length > 0) {
          const arrays = array.filter((obj) => obj.defaultConfig === 1)
          if (arrays.length > 0) { returnObj.SelectedSibColorEvent = arrays[0] }
          if (arrays.length === 0) { returnObj.SelectedSibColorEvent = array[0] }
          returnObj.SibColorListEvent = array
        }
      }
    }
  }
  return returnObj
}

/**
 * 删除数组中的某个对象
 */
export const arrayRemove = (value, array) => {
  const index = arrayIndexOf(value, array)
  if (index > -1) {
    array.splice(index, 1)
  }
  return array
}

/**
 * 查找对象在某个数组中的下标
 */
export const arrayIndexOf = (value, array) => {
  for (let i = 0; i < array.length; i++) {
    const obj = array[i]
    if (obj.optionId === value.optionId) return i
  }
  return -1
}

// 选择选装包以后对其他数据的影响，已经失效
export const outputChangeList = (changeList, dependList, clickOption) => {
  const changeListTemp = JSON.parse(JSON.stringify(changeList))
  // 某些装备包中带必选装备 遍历当前装备包中的装备 与依赖装备进行对比 如果依赖项中已有该配置项,则无视,若没有,则选中
  clickOption.packetItems.forEach((item) => {
    if (item.category === 'VOS') {
      if (!dependList.find((val) => val.category === item.category) && Object.keys(changeListTemp.vos) <= 0) {
        const tempItem = item
        // this.interiorChairList.find((val) => val.optionCode === item.optionCode)
        changeListTemp.vos = tempItem
      }
    } else if (['F_SIB_COLOR_INTERIEUR', 'COLOR_INTERIEUR', 'SIB'].includes(item.category) && Object.keys(changeListTemp.sib) <= 0) {
      if (!dependList.find((val) => val.sibInterieurCategory === 'F_SIB_COLOR_INTERIEUR') && !changeListTemp.sib) {
        const tempItem = item
        // this.sibColorInterieurList.find((val) => val.optionCode === item.optionCode)
        changeListTemp.sib = tempItem
      }
    }
    // else if (item.category === 'RAD') {
    //   if (!dependList.find((val) => val.category === item.category) && Object.keys(changeListTemp.rad) <= 0) {
    //     const tempItem = this.modelHubList.find((val) => val.optionCode === item.optionCode)
    //     changeListTemp.rad = tempItem
    //   }
    // } else if (item.category === 'EIH') {
    //   if (!dependList.find((val) => val.category === item.category) && Object.keys(changeListTemp.eih) <= 0) {
    //     const tempItem = this.interiorEihList.find((val) => val.optionCode === item.optionCode)
    //     changeListTemp.eih = tempItem
    //   }
    // }
  })
  return changeListTemp
}
export const outputCurrentOptionsList = (conflictList, currentOptionsList, clickOption) => {
  // 从选装列表中吧互斥的选装包删掉
  conflictList.forEach((item) => {
    const delIndex = currentOptionsList.findIndex((val) => val.optionId === item.optionId)
    if (delIndex !== -1) {
      currentOptionsList.splice(delIndex, 1)
    }
  })
  return [...currentOptionsList, clickOption]
}
