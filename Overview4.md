# 奥迪汽车订购H5项目 - 用户旅程分析

## 1. 用户旅程概览

### 1.1 主要用户角色
- **潜在购车用户**: 浏览和了解奥迪车型的用户
- **意向购车用户**: 有明确购车意向，进行车辆配置的用户
- **订购用户**: 完成车辆配置并下单购买的用户
- **试驾用户**: 预约试驾体验的用户

### 1.2 核心用户旅程
```mermaid
journey
    title 奥迪汽车购买用户旅程
    section 发现阶段
      打开应用: 5: 用户
      浏览车型: 4: 用户
      了解价格: 4: 用户
    section 配置阶段
      选择车系: 5: 用户
      配置外观: 5: 用户
      配置内饰: 4: 用户
      选择选装: 3: 用户
    section 体验阶段
      预约试驾: 4: 用户
      到店试驾: 5: 用户
      确认配置: 5: 用户
    section 购买阶段
      提交订单: 4: 用户
      完成支付: 3: 用户
      等待交付: 2: 用户
```

## 2. 详细用户旅程分析

### 2.1 应用启动与首次体验

**用户目标**: 了解奥迪车型和服务

**关键触点**:
1. **应用启动**
   - 加载启动页面
   - 检查网络连接
   - 初始化用户状态

2. **身份识别**
   ```javascript
   // 检查用户登录状态
   async checkUserStatus() {
     const { env, token } = getUrlParamObj();
     if (env === "minip") {
       // 小程序环境处理
       const isLogin = +!!token;
       store.commit("setIsLogin", isLogin)
     } else {
       // APP环境处理
       const userInfo = await getUserInfo({'sync': 'login'})
       store.commit("setIsLogin", +userInfo.isLogin)
     }
   }
   ```

3. **首页导航**
   - 默认跳转到订单详情页 (`path: '*', redirect: '/order/detail'`)
   - 根据用户状态显示不同内容

**用户情感**: 好奇 → 期待 → 探索

### 2.2 车辆配置旅程

**用户目标**: 配置理想的车辆

**详细步骤**:

1. **选择车系**
   ```javascript
   // 车系选择逻辑
   async toSelectModel(item) {
     if (this.currentModelLineData.suit === item.suit) {
       return
     }
     
     // A7MR车型特殊处理
     if (this.isA7MrCar) {
       if (DONGGAN_CODE.includes(item.optionCode)) {
         // 动感套装选择
         option.selected = true
         this.$store.commit('updateSelectedOptions', [option])
       } else {
         // 新潮套装清空选装包
         this.$store.commit('updateSelectedOptions', [])
       }
     }
     
     this.$store.commit('updateCurrentModelLineData', item)
   }
   ```

2. **外观配置**
   - 选择车身颜色
   - 选择轮毂样式
   - 实时预览效果

3. **内饰配置**
   - 选择内饰颜色
   - 选择座椅材质
   - 选择饰板样式

4. **选装配置**
   - 浏览选装包
   - 查看装备详情
   - 计算价格影响

**关键代码示例**:
```javascript
// 外观配置选择
async toSelectExerior(item) {
  if (item.featureCode === this.currentExterior.featureCode) {
    return;
  }
  this.$store.commit("showLoadingNospinner");
  await this.$store.commit("clearSelectedConfig", ["interior", "option"]);
  await this.$store.commit("updateCurrentExterior", item);
  await this.resizeCar(item, "AADD");
  this.$store.dispatch("computeA5LTotalPriceAndEquityPrice");
  this.$store.commit("hideLoadingNospinner");
}
```

**用户情感**: 兴奋 → 纠结 → 满意

### 2.3 试驾预约旅程

**用户目标**: 体验心仪车型

**关键步骤**:

1. **试驾入口**
   ```javascript
   // 从报价页跳转试驾
   tryCar() {
     this.clickQuotationSensors('预约试驾') // 埋点
     const seriesCode = this.carSeries.seriesCode
     this.$router.push({
       path: '/testdrive/create',
       query: {
         idx: ['49', 'G4', 'G6', 'F0'].findIndex((e) => e == seriesCode)
       }
     })
   }
   ```

2. **选择经销商**
   - 基于地理位置推荐
   - 查看经销商详情
   - 确认服务时间

3. **时间选择**
   - 查看可预约时段
   - 选择合适时间
   - 确认预约信息

4. **信息填写**
   - 个人基本信息
   - 联系方式
   - 特殊需求

**用户情感**: 期待 → 谨慎 → 确定

### 2.4 订单创建旅程

**用户目标**: 完成车辆订购

**核心流程**:

1. **登录验证**
   ```javascript
   // 检查登录状态
   async submit() {
     if (!this.checkLoginFn()) return // 检查登录状态
     
     // 验证订单信息完整性
     if (this.checked) {
       this.$refs.form.submit()
     } else {
       Toast({
         message: '请阅读并勾选定金协议',
         duration: 800
       })
     }
   }
   ```

2. **信息完善**
   - 个人信息确认
   - 联系方式验证
   - 交付地址选择

3. **经销商选择**
   - 就近经销商推荐
   - 服务评价查看
   - 特殊服务确认

4. **支付方式选择**
   - 全款支付
   - 贷款方案
   - 优惠券使用

**用户情感**: 紧张 → 谨慎 → 决心

### 2.5 支付处理旅程

**用户目标**: 安全完成支付

**支付流程**:

1. **支付方式选择**
   ```javascript
   // 支付方式处理
   async handlePayment(paymentType) {
     switch(paymentType) {
       case 'wechat':
         await this.callWechatPay()
         break
       case 'alipay':
         await this.callAlipay()
         break
       case 'bank':
         await this.callBankPay()
         break
       case 'digital':
         this.showDigitalPayInfo()
         break
     }
   }
   ```

2. **支付执行**
   ```javascript
   // 创建订单并支付
   export const createOrder = async (params, goods, spare = {}, mobile) => {
     const { data } = (await carPlaceOrder(params)) || ''
     
     if (data?.code && RES_SUCCEED_CODE.includes(data.code)) {
       const { orderId } = data.data || ''
       if (orderId && ORDER_PASSING_CODE.includes(orderStatus)) {
         payOrder(orderId, { ...goods, orderStatus }, spare, mobile)
         return true
       }
     }
   }
   ```

3. **支付结果确认**
   - 轮询支付状态
   - 处理支付结果
   - 跳转结果页面

**用户情感**: 焦虑 → 等待 → 释然

### 2.6 售后服务旅程

**用户目标**: 获得持续服务

**服务内容**:
1. **订单跟踪**
   - 生产进度查询
   - 交付时间更新
   - 物流信息跟踪

2. **交付准备**
   - 交付通知
   - 文档准备
   - 验车安排

3. **售后支持**
   - 保养提醒
   - 服务预约
   - 问题反馈

## 3. 关键用户体验优化点

### 3.1 性能优化
- **路由懒加载**: 减少首屏加载时间
- **图片懒加载**: 优化页面滚动体验
- **组件缓存**: 提升页面切换速度

### 3.2 交互优化
- **实时预览**: 配置变更即时反馈
- **价格计算**: 透明的价格展示
- **进度提示**: 清晰的操作进度

### 3.3 错误处理
```javascript
// 统一错误处理
handleError(error) {
  switch(error.type) {
    case 'network':
      this.showNetworkError()
      break
    case 'auth':
      this.redirectToLogin()
      break
    case 'business':
      this.showBusinessError(error.message)
      break
    default:
      this.showGenericError()
  }
}
```

## 4. 用户满意度关键指标

### 4.1 体验指标
- **页面加载时间**: < 3秒
- **配置完成率**: > 80%
- **支付成功率**: > 95%
- **试驾转化率**: > 60%

### 4.2 业务指标
- **订单完成率**: > 70%
- **用户留存率**: > 50%
- **客户满意度**: > 4.5/5
- **推荐意愿**: > 80%

## 5. 持续优化建议

### 5.1 用户反馈收集
- 页面满意度调研
- 功能使用情况分析
- 用户行为路径优化

### 5.2 技术优化
- 性能监控完善
- 错误日志分析
- 用户体验数据收集

### 5.3 业务优化
- 个性化推荐
- 智能客服集成
- 多渠道服务整合

## 总结

奥迪汽车订购H5项目通过精心设计的用户旅程，为用户提供了从车辆了解、配置、试驾到购买的完整体验。每个环节都注重用户体验优化，通过技术手段确保流程的顺畅性和可靠性。持续的用户反馈收集和数据分析将帮助进一步优化用户旅程，提升整体满意度。
