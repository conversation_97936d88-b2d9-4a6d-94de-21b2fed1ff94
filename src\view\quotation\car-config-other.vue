<template>
  <div style="padding-bottom: 30px;">
    <navigation
      v-if="navstatus"
      back-type="app"
    />
    <div
      class="_heard"
      v-if="carModel"
    >
      <div class="_title">
        {{ carModel.modelNameCn }}
      </div>
      <div class="_price">
        ¥ {{ carModel.totalPrice | formatPrice }}
      </div>
      <img :src="baseOssHost + carModel.imageUrl">
    </div>

    <div class="config-item-wrapper">
      <div
        class="config-item c-flex-center sc-u-line"
        :class="[index===0?'sc-t-line':'']"
        v-for="(item,index) in standardConfigData"
        :key="index"
      >
        <div
          class="sc-left"
          style="width: 50px;height: 50px;"
        >
          <img
            :src="item.img"
            style="object-fit: cover;width: 50px;height: 50px;"
            alt=""
          >
        </div>
        <div class="sc-height">
          <div class="c-font12 small-font justify-between">
            <div>{{ item.type }}</div>
            <div>{{ isNumber(item.price)?'¥':'' }}{{ item.price | formatPrice }}</div>
          </div>
          <div class="c-font12">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div style="position: fixed;bottom: 30px;width: 90%;left: 5%;">
      <AudiButton
        @click="modalshow = true"
        text="在App内打开"
        color="black"
        height="45px"
        font-size="16px"
      />
    </div>
    <model
      @onConfirm="onConfirm"
      :modalshow.sync="modalshow"
      title="在App内打开"
      confirm-text="确定"
      content=""
    />
  </div>
</template>

<script>
import { getCarConfigDetail } from '@/api/api'
import confiUrl from '@/config/url'
import navigation from '@/components/navigation.vue'
import AudiButton from '../../components/audi-button.vue'
import model from '../../components/model.vue'

const baseOssHost = confiUrl.BaseOssHost

export default {
  components: { navigation, AudiButton, model },
  data() {
    return {
      standardConfigData: [],
      carModel: null,
      baseOssHost,
      navstatus: false,
      modalshow: false
    }
  },
  async mounted() {
    const u = navigator.userAgent
    const obj = {
      trident: u.indexOf('Trident') > -1, // IE内核
      presto: u.indexOf('Presto') > -1, // opera内核
      webKit: u.indexOf('AppleWebKit') > -1, // 苹果、谷歌内核
      gecko: u.indexOf('Firefox') > -1, // 火狐内核Gecko
      mobile: !!u.match(/AppleWebKit.*Mobile.*/), // 是否为移动终端
      ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // ios
      android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, // android
      iPhone: u.indexOf('iPhone') > -1, // iPhone
      iPad: u.indexOf('iPad') > -1, // iPad
      webApp: u.indexOf('Safari') > -1 // Safari
    }
    console.log(obj, '内核参数')
    if (obj.webKit) {
      this.navstatus = true
    }

    const { ccid } = this.$route.query
    const { data } = await getCarConfigDetail({ ccid: ccid })
    const param = data.data
    console.log(param)
    // 生成标准配置表

    const carModelobj = {
      name: param.configDetail.carModel.modelNameCn,
      img: baseOssHost + param.configDetail.carModel.imageUrl,
      type: '车型',
      price: param.configDetail.carModel.modelPrice || '价格已包含'
    }

    const outSideColor = {
      name: param.configDetail.outsideColor.colorNameCn,
      img: baseOssHost + param.configDetail.outsideColor.imageUrl,
      type: '颜色',
      price: param.configDetail.outsideColor.price || '价格已包含'
    }

    const options = param.configDetail.optionList.map((i) => ({
      name: i.optionNameCn,
      optionClassification: i.optionClassification,
      img: baseOssHost + i.imageUrl,
      price: i.optionPrice || '价格已包含'
    }))
    const optionscopy = options.map((i) => {
      switch (i.optionClassification) {
        case 'COLOR_EXTERIEUR':
          return { ...i, type: '外饰颜色' }
        case 'COLOR_INTERIEUR':
          return { ...i, type: '内饰颜色' }
        case 'VOS':
          return { ...i, type: '座椅' }
        case 'RAD':
          return { ...i, type: '轮毂' }
        case 'EIH':
          return { ...i, type: '饰条' }
        case 'SIB':
          return { ...i, type: '面料' }
        case 'PACKET':
          return { ...i, type: '选装包' }
        default:
          return i
      }
    })
    console.log(optionscopy)
    this.standardConfigData = [carModelobj, outSideColor, ...optionscopy]
    this.carModel = param.configDetail.carModel
    this.carModel.totalPrice = param.configDetail.totalPrice
    window.document.title = this.carModel.modelNameCn
    console.log(this.standardConfigData)
  },
  methods: {
    isNumber(value) {
      return typeof value === 'number' && !isNaN(value)
    },
    onConfirm() {
      this.modalshow = false
    }
  }
}
</script>

<style lang='less' scoped>
  @import "../../assets/style/common.less";
  @leftWith: 18vw;
  @rightMargin: 15px;

  div img {
    box-sizing: border-box;
  }

  ._heard {
    padding-top: 20px;
    height: 200px;
    width: 100%;
    position: relative;

    img {
      width: 100%;
      height: 200px;
      object-fit: cover;
      position: absolute;
      bottom: 0;
    }

    ._title {
      font-size: 20px;
      font-family: 'Audi-WideBold';
      color: #000000;
      line-height: 20px;
      padding: 0 20px;
    }

    ._price {
      font-size: 16px;
      font-family: 'Audi-WideBold';
      color: #999999;
      line-height: 14px;
      margin-top: 14px;
      padding: 0 20px;
    }
  }

  .sc-left {
    margin-right: @rightMargin;
  }

  .sc-u-line {
    border-bottom: 1px solid #e5e5e5;
  }

  .sc-t-line {
    border-top: 1px solid #e5e5e5;
  }

  .small-font {
    .c-font12;
    color: #999;
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    word-break: break-all;
  }

  .sc-height {
    .c-flex-between;
    flex-direction: column;
    flex: 1;
    height: @leftWith;
    padding: 12px 0;
    box-sizing: border-box;
  }

  .config-item-wrapper {
    padding: 0 30px;
  }

  .config-item {
    position: relative;
    padding: 10px 0;
  }

  .justify-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
