<template>
  <div
    class="SwipeBox"
    v-if="arrSwipe.length > 0"
  >
    <div class="similar">
      <div class="title">
        相似车型
      </div>
      <van-swipe
        @change="swipeOnChange"
        class="my-swipe"
        :autoplay="3000"
        indicator-color="white"
      >
        <van-swipe-item
          v-for="(item, index) in arrSwipe"
          :key="index"
        >
          <div class="swiper">
            <div
              class="left"
              style="padding-top: 30px;"
            >
              <div
                class="name-box mb"
                @click="goDetails(item)"
                style="position: absolute;top: 20px;"
              >
                {{ item.modelLine.modelLineName || "" }}
              </div>
              <div
                class="time-box mb"
                @click="goDetails(item)"
              >
                4周内交付
              </div>
              <div
                class="xx_box mb"
                @click="goDetails(item)"
              >
                相似度{{ item.similarity || "" }}
              </div>
              <div
                class="price-box mb"
                @click="goDetails(item)"
              >
                ¥{{ item.totalPrice | formatPrice }}
              </div>
              <div
                class="arrow-box"
                @click="goDetails(item, 1)"
              >
                查看详情<van-icon
                  name="arrow"
                  color="#999"
                />
              </div>
            </div>
            <div
              class="right"
              @click="goDetails(item)"
            >
              <img :src="BaseConfigrationOssHost + item.imageUrl">
            </div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
    <div class="swipeIndicators">
      <div
        :class="active == index ? 'bg' : '' "
        v-for="(item, index) in arrSwipe"
        :key="index"
      />
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import {
  Swipe, SwipeItem, Icon, Toast
} from 'vant'
import { getSimilarityModel, simicarConfig, getSourceId } from '@/api/api'
import url from '@/config/url'
import storage from '@/utils/storage'
import { getUrlParamObj } from '@/utils/index'

Vue.use(Icon).use(Swipe).use(SwipeItem)


export default {
  props: {
    objSwipeBox: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      arrSwipe: [],
      defQuery: {},
      active: 0,
      sourceId: ''
    }
  },
  computed: {
    dealerCode() {
      return this.$store.state.dealerInfo.dealerCode
    }
  },
  created() {
    this.defQuery = this.$route.query || {}
    console.log('defQuery》》》', this.$route.query)
    this.getSimilarityModel()
  },
  mounted() {
    this.getSourceIdFn()
  },
  methods: {
    async getSourceIdFn() {
      const { env } = getUrlParamObj()
      let param = {}
      if (env === 'minip') {
        param = {
          app: 'MiniAPP',
          dealerCode: 'MiniAPP',
          dealerName: 'MiniAPP',
          name: 'MiniAPP'
        }
      } else {
        param = {
          app: 'OneAPP',
          dealerCode: 'OneAPP',
          dealerName: 'OneAPP',
          name: 'OneAPP'
        }
      }
      const { data } = await getSourceId(param)
      this.sourceId = data.data.sourceId
      console.log('getSourceIdFn sourceId', data.data.sourceId)
      this.simicarConfigFn()
    },
    // 获取相似车ccid
    async simicarConfigFn(e = {}) {
      const { sibInterieur, options } = e // storage.getPlus('ccproData')
      const optionIds = []
      options && options.forEach((element) => {
        optionIds.push(element.optionId)
      })
      optionIds.push(sibInterieur.sibOptionId)
      optionIds.push(sibInterieur.interieurOptionId)
      const { ccid, skuid } = this.$route.query
      const para = {
        fromCcid: ccid,
        modelLineId: e.modelLine.modelLineId,
        optionIds: optionIds || [],
        sibInterieurId: e.sibInterieur.sibInterieurId,
        sourceId: this.sourceId,
        uniqueCode: e.uniqueCode,
        entryPoint: 'ONEAPP_ESTIMATE'
      }

      const res = await simicarConfig(para)
      console.log('获取ccid res:', res)
      console.log('获取ccid para', para)
      const tmpId = res?.data?.data?.ccId || ccid
      const { dealerCode } = this.objSwipeBox
      tmpId && this.$router.push({
        path: '/quotation',
        query: {
          ccid: tmpId, skuid: skuid, dealerCode: dealerCode, isBigOrder: 1
        }
      })
    },
    async getSimilarityModel() {
      const { ccid, skuid } = this.defQuery
      const { dealerCode } = this.objSwipeBox
      console.log('dealerCode', dealerCode)
      const p = {
        ccid: ccid,
        dealerNetCode: dealerCode || ''
      }
      console.log('=====================P', p)
      const res = await getSimilarityModel(p)
      console.log('相似车 res:', res, this.arrSwipe)
      if (res.data && res.data.data) this.arrSwipe = res.data.data.ccSimilarityModelList || []
      this.arrSwipe.forEach((e) => {
        const hubs = e.options.filter((option) => option.category === 'RAD')
        e.hub = hubs[0]
        const colors = e.options.filter((option) => option.category === 'COLOR_EXTERIEUR')
        e.outsideColor = colors[0]
        e.imageUrl = `/ccpro-backend/storebest/${e.modelLine.modelLineCode}_${e.hub.optionCode}_${e.outsideColor.optionCode}.png` + '?x-oss-process=image/resize,h_512'
      })
    },
    swipeOnChange(e) {
      console.log(e)
      this.active = e
    },
    // 点击某个相似车 或 “查看详情”，则直接进入该相似车的报价单页面
    goDetails(e, num = '') {
      // num == 1 跳转到参数表
      if (num) {
        this.$router.push({
          path: '/car-config-table',
          query: {
            modellineId: e.modelLine.modelLineId,
            carModelName: e.modelLine.modelLineName,
            seriesName: e.modelLine.customSeriesCode,
            special: false
          }
        })
        return
      }
      if (!num) {
        const minip = this.$route.query.env == 'minip'
        this.$storage.setPlus('entryPoint', 'ONEAPP_ESTIMATE')
        this.simicarConfigFn(e)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.SwipeBox {
  .swipeIndicators {
    display: flex;
    justify-content: center;
    padding-top: 24px;
    & > div {
      width: 20px;
      height: 8px;
      background: #f2f2f2; //#f2f2f2
      margin: 0 2px;
    }
    .bg {
      background: #000; //#f2f2f2
    }
  }

  .similar {
    margin: 0 16px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin: 16px 0;
    }
    .my-swipe {
      border: solid 1px #999999;
    }
    .swiper {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 0;
      .left {
        flex: 1;
        margin-left: 20px;
        .name-box {
          font-size: 14px;
          font-family: AudiTypeGB-Normal, AudiTypeGB;
          font-weight: 400;
          color: #000000;
        }
        .time-box {
          font-size: 14px;
          font-family: AudiTypeGB-WideBold, AudiTypeGB;
          font-weight: 600;
          color: #000000;
        }
        .xx_box {
          font-size: 14px;
          font-family: AudiTypeGB-WideBold, AudiTypeGB;
          font-weight: 600;
          color: #000000;
        }
        .price-box {
          font-size: 14px;
          font-family: AudiTypeGB-Normal, AudiTypeGB;
          font-weight: 400;
          color: #000000;
        }
        .arrow-box {
          font-size: 12px;
          font-family: AudiTypeGB-Normal, AudiTypeGB;
          color: #999999;
        }
        .mb {
          margin-bottom: 8px;
        }
      }
      .right {
        width: 218px;
        img {
          width: 100%;
          max-height: 100%;
        }
      }
    }
  }
}
</style>
