<template>
  <div
    class="loading"
    v-if="$store.state.loadingNospinner && !fromType"
  >
    <van-loading
      color="#000"
      class="loadingVant"
      vertical
      type="spinner"
    >
    </van-loading>
  </div>
</template>

<script>
export default {
  name: "LoadingNospinner",
  data() {
    return {
      fromType: this.$route.query?.fromType === "fromPurple",//全局loading - audi时候不显示
    };
  },
  mounted() {
    console.log("this.$route.query?.fromType ------ loading", this.$route.query?.fromType, "---", this.$store.state.noSpinnerLoading);
  }
};
</script>
<style scoped lang="less">
.loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  width: 100%;
  height: 100%;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}

/deep/ .van-loading {
  display: none;
}
</style>
