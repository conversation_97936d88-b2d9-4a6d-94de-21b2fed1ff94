<template>
  <div class="container">
    <div class="title-bold">
      确认并预约
    </div>
    <div
      class="c-font12"
      style="color: #000; font-size: 14px; margin-top: 24px"
    >
      服务时间
    </div>
    <div
      class="title-bold"
      style="font-size: 18px; margin-top: 8px; margin-bottom: 16px"
    >
      {{ appointmentTime }}
    </div>

    <div class="item-store">
      <div>
        <img
          class="img-wrapper"
          :src="'https://audi-oss.saic-audi.mobi/audicc/app/order/afterservice/agent.png' | audiwebp "
          alt=""
        >
      </div>

      <div class="content-wrapper flex1">
        <div class="c-font14 c-bold">
          {{ dealerName }}
        </div>
        <div style="margin-top: 4px" />
        <div
          class="c-font14"
          style="color: #666; font-size: 14px"
        >
          {{ dealerAdrress }}
        </div>
      </div>

      <div class="navgation-wrapper">
        <div>
          <img
            class="nav-icon"
            :src="require('@/assets/img/call-phone.png')"
            alt=""
            @click="callPhone"
          >
        </div>
      </div>
    </div>

    <div
      class="buyMess box"
      style="font-size: 18px; margin-top: 12px"
    >
      <van-form
        ref="form"
        @failed="failed"
        @submit="confirmVisible = true"
      >
        <div
          class="box-field"
          @click.stop="animation('driveName')"
          id="driveName"
        >
          <label class="box-label" />
          <van-field
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="submitParam.driveName"
            ref="driveName"
            type="text"
            @blur="handlerBlur('driveName')"
            @focus="handlerFocus('driveName')"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写姓名！',
              },
            ]"
          />
          <div
            :class="
              animations.driveName
                ? ['posi-label', 'aniName']
                : ['posi-label', 'noAniName']
            "
          >
            联系人
          </div>
        </div>

        <div
          class="box-field"
          @click.stop="animation('driveTel')"
          id="driveTel"
        >
          <label class="box-label" />
          <van-field
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="submitParam.driveTel"
            ref="driveTel"
            maxlength="11"
            type="number"
            on-key-up="value=value.replace(/[\W]/g,'')"
            @blur="handlerBlur('driveTel')"
            @focus="handlerFocus('driveTel')"
            :rules="[
              {
                trigger: 'onBlur',
                required: true,
                message: '请填写联系方式！',
              },
              {
                pattern: /\w{11}/,
                message: '请输入正确的联系方式',
              },
            ]"
          />
          <div
            :class="
              animations.driveTel
                ? ['posi-label', 'aniName']
                : ['posi-label', 'noAniName']
            "
          >
            联系方式
          </div>
        </div>
      </van-form>
    </div>

    <div class="chat-input">
      <textarea
        type="textarea"
        placeholder="请输入其它服务要求"
        v-model="Remark"
        maxlength="130"
        rows="5"
      />
    </div>
    <div
      class="item-wrapper"
      v-for="(item, idx) in dealerList"
      :key="idx"
    >
      <div class="content-wrapper flex1">
        <div class="c-font16">
          {{ item.packageName }}
        </div>
        <div style="margin-top: 4px" />
      </div>

      <div
        class="navgation-wrapper"
        style="color: #000; font-size: 14px"
      >
        <div class="c-font16">
          {{
            "￥" +
              item.saleAmount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
          }}
        </div>
      </div>
    </div>

    <!-- <div
      class="c-font12"
      style="color: #000; font-size: 14px; margin-top: 24px"
    >
      您的服务卡券请在进站后联系服务商使用
    </div> -->
    <span
      style="color: #000; font-size: 14px;"
    >
      价格仅供参考，实际价格请以服务商报价为准。
    </span>
    <div class="btn-delete-height" />
    <div>
      <div class="bottom_style">
        <div class="checkbox_style">
          <!-- <van-checkbox
            v-model="ischecked"
            disabled="disabled"
            @click="checkAll"
          >
            <img
              class="checkbox_button"
              slot="icon"
              :src="ischecked ? activeIcon : inactiveIcon"
            >
            <span style="color: #666666; font-size: 12px"> 我已阅读 </span>
            <span
              @click="onImportantNote"
              style="color: #000; font-size: 12px"
            >《重要提示》
            </span>
          </van-checkbox> -->
          <span
            style="color: #000; font-size: 10px;margin-left:16px"
          />
          <div
            class="checkbox_styleRight"
            v-if="totalAmount() > 0"
          >
            <span style="color: #666666; font-size: 16px">预估价格</span>
            <span>{{
              "￥" +
                totalAmount()
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
            }}</span>
          </div>
        </div>

        <div class="btn-delete-wrapper">
          <AudiButton
            @click="onAffirm"
            :text="'确认'"
            color="black"
            font-size="16px"
            height="50px"
          />
        </div>
      </div>
    </div>
    <popup-custom-action-btn
      v-if="CarsService.conf.enabled || false"
      :btn-conf.sync="CarsService.conf"
      :btn-items="CarsService.items"
      @emitGetBackBtn="$router.go(-1)"
      @emitGetActionBtn="$router.push({name: 'afterservice-order-list', query: { type: 1 }})"
    >
      <template #popup-custom-main>
        <div class="popup-content-text custom-align-center">
          已存在未结束的服务预约订单，请前往<br>
          “我的订单-服务-服务预约”，查看订单或<br>
          结束订单后重新发起
        </div>
      </template>
    </popup-custom-action-btn>
  </div>
</template>

<script>
import Vue from 'vue'

import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Toast,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader
} from 'vant'
import { mapState } from 'vuex'
import {
  postAfterSaleCreate,
  postAfterSaleUpdate,
  getAfterSaleDetail
} from '@/api/api'
import baseUrl from '@/config/url'
import { callNative } from '@/utils'
import AudiButton from '@/components/audi-button'
import storage from '../../utils/storage'

import popupCustomActionBtn from '@/components/popup-custom-action-btn.vue'
import POPUP_CUSTOM from '@/config/popup-custom.data'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
export default {
  components: {
    AudiButton,
    'popup-custom-action-btn': popupCustomActionBtn
  },
  data() {
    return {
      CarsService: { ...POPUP_CUSTOM.CARS_SERVICE },
      labelWidth: 120,
      labelAlign: 'left',
      animations: {
        driveName: false,
        driveTel: false
      },

      submitParam: {
        driveName: '', // 姓名
        driveTel: '' // 联系人电话
      },

      // inputRemark: '',
      // ischecked: false,
      activeIcon: require('../../assets/img/checkbox_checked.png'),
      inactiveIcon: require('../../assets/img/checkbox_normal.png'),
      ossUrl: baseUrl.BaseOssHost,

      license: '', // 车牌
      vin: '', // VIN码
      mileage: 0, // 里程
      ownerName: '', // 车主姓名
      ownerTel: '', // 车主电话
      dealerCode: '', // 服务商编号
      dealerName: '', // 服务商名称
      dealerAdrress: '', // 服务商地址
      dealerPhone: '', // 服务商电话
      modelCode: '', // 车型code
      modelName: '', // 车型code
      seriesCode: '', // 车系代码
      seriesName: '', // 车系名称

      Remark: ''
    }
  },
  computed: {
    ...mapState({

      serviceType: (state) => state.selectServiceType.serviceType || 1, // 服务类型
      serviceName: (state) => state.selectServiceType.serviceName || '保养', // 服务类型
      inputRemark: (state) => state.selectServiceType.inputServiceRemark || '',

      dealerList: (state) => state.selectServiceShop || [], // 服务类型

      appointmentTime: (state) => state.selectServiceTime.appointmentTime || '', // 预约时间
      timeCode: (state) => state.selectServiceTime.timeCode || null, // 预约时间code
    })
  },
  async mounted() {
    // query: {ownerName:this.submitParam.ownerName,driveTel:this.submitParam.driveTel},
    this.Remark = this.inputRemark
    const {
      ownerName, ownerTel, license, vin, mileage
    } = this.$route.query
    this.ownerName = ownerName
    this.ownerTel = ownerTel
    this.license = license
    this.vin = vin
    this.mileage = mileage

    this.submitParam.driveName = ownerName
    this.submitParam.driveTel = ownerTel
    this.changeEdit()

    const selectModel = storage.get('ownerVinInfo') || '{}'
    this.modelName = JSON.parse(selectModel).modelLineName || ''
    this.modelCode = JSON.parse(selectModel).modelCode || ''
    this.seriesCode = JSON.parse(selectModel).seriesCode
    this.seriesName = JSON.parse(selectModel).seriesName

    const dealerModel = storage.get('dealerModel') || '{}'
    this.dealerCode = JSON.parse(dealerModel).serviceCode || ''
    this.dealerName = JSON.parse(dealerModel).dealerName
    this.dealerAdrress = JSON.parse(dealerModel).dealerAdrress
    this.dealerPhone = JSON.parse(dealerModel).dealerPhone


    const { appoId } = this.$route.query
    this.appoId = appoId
    if (appoId !== undefined) {
      this.getAfterSaleDetail(appoId)
    }
  },

  methods: {
    async getAfterSaleDetail(appoId) {
      const { data } = await getAfterSaleDetail({ appoId: appoId, type: 1 })

      if (data.code === '200') {
        // this.submitParam.driveName = data.data.driveName
        // this.submitParam.driveTel = data.data.driveTel
        this.Remark = data.data.remark
        this.changeEdit()
      }
    },
    changeEdit() {
      if (this.submitParam.driveTel !== '') {
        this.animations.driveTel = true
      }
      if (this.submitParam.driveName !== '') {
        this.animations.driveName = true
      }
    },
    animation(ref) {
      this.animations[ref] = true
      this.$refs[ref].focus()
      if (this.blur) {
        this.$refs[ref].blur()
      }
    },
    handlerFocus(prop) {
      setTimeout(() => {
        const pannel = document.getElementById(prop)

        // 让当前的元素滚动到浏览器窗口的可视区域内
        pannel.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
        // 此方法是标准的scrollIntoView()方法的专有变体
        // pannel.scrollIntoViewIfNeeded();
      }, 300)

      if (!this.animations[prop]) {
        this.animations[prop] = true
      }
    },
    handlerBlur(prop) {
      this.animations[prop] = !!this.submitParam[prop]
    },
    failed(err) {
      console.error('failed', err)
    },

    callPhone() {
      window.location.href = `tel:${this.dealerPhone}`
    },

    // checkAll() {
    //   this.ischecked = !this.ischecked
    // },

    totalAmount() {
      let total = 0
      for (let i = 0; i < this.dealerList.length; i++) {
        total += parseFloat(this.dealerList[i].saleAmount)
      }
      return total
    },

    // 确认
    async onAffirm() {
      const codeItems = [] // 服务商品code列表
      for (let i = 0; i < this.dealerList.length; i++) {
        codeItems.push({
          packageId: this.dealerList[i].packageId,
          price: this.dealerList[i].saleAmount
        })
      }

      if (this.appointmentTime === '') {
        callNative('toast', { type: 'fail', message: '请选择预约时间' })
        return
      }

      if (this.submitParam.driveName === '') {
        callNative('toast', { type: 'fail', message: '请填写联系人' })
        return
      }
      if (this.submitParam.driveTel === '') {
        callNative('toast', { type: 'fail', message: '请填写联系方式' })
        return
      }

      storage.set('ownerName', this.submitParam.driveName)
      storage.set('ownerTel', this.submitParam.driveTel)

      const mobile = storage.get('serviceUserMobile') || ''
      if (!mobile) {
        storage.set('serviceUserMobile', this.submitParam.driveTel)
      } else {
        const tel = this.submitParam.driveTel
        if (mobile.indexOf(tel) === -1) {
          storage.set('serviceUserMobile', `${this.submitParam.driveTel},${mobile}`)
        }
      }

      const param = {
        appointmentTime: new Date(this.appointmentTime.replace(/-/g, '/')).getTime(), // 1000 1636167600000, //
        clueType: 10,
        packageItems: codeItems, // 用户选择的服务项目列表
        dealerCode: this.dealerCode, // , ,//

        driveName: this.submitParam.driveName,
        driveTel: this.submitParam.driveTel,
        license: this.license,
        mileage: parseInt(this.mileage),
        modelCode: this.modelCode,
        modelName: this.modelName,
        ownerName: this.submitParam.driveName,
        ownerTel: this.submitParam.driveTel,
        remark: this.Remark || ' ',
        seriesCode: this.seriesCode,
        seriesName: this.seriesName,
        serviceType: this.serviceType, // ,
        type: 1,
        vin: this.vin, // 'LSVNF45EXJN010052'
        channel: 1,
        appoId: this.appoId,
        version:'v2',
        timeCode:this.timeCode
      }

      // 判断当前时间是否在9-15点时间以内下单
      const hoursDate = new Date().getHours()
      console.log('小时==', hoursDate)

      if (hoursDate > 8 && hoursDate < 18) {
        this.confirmOrder(param)
      } else {
        // 调用APP Dialog
        callNative('popup', {
          type: 'alert',
          alertparams: {
            title: '',
            desc: '当前服务商处于非营业时间，暂无法及时获悉您的服务需求，服务商将在上班后与您尽快取得联系，请您保持手机畅通，感谢您的理解。',
            actions: [
              {
                type: 'fill',
                title: '确定'
              }
            ]
          }
        }).then((data) => {
          if (data.type === 'fill') {
            // 点击确定
            this.confirmOrder(param)
          }
        })
      }
    },
    async confirmOrder(param) {
      this.$store.commit('showLoading')
      if (this.appoId !== undefined) {
        // 修改
        const { data } = await postAfterSaleUpdate(param)
        this.$store.commit('hideLoading')
        if (data.code === '200') {
          // 创建成功
          this.onRouter()
        } else {
          if (this.handleError(data)) { return }
          callNative('toast', { type: 'fail', message: data.message })
        }
      } else {
        const { data } = await postAfterSaleCreate(param)
        this.$store.commit('hideLoading')
        if (data.code === '200') {
          // 创建成功
          this.onRouter()
        } else {
          if (this.handleError(data)) { return }
          callNative('toast', { type: 'fail', message: data.message })
        }
      }
    },
    handleError(data) {
      // 当前接口无法区分错误CODE，故使用字符串匹配方法
      const { message } = data
      if (message.includes('已存在未结束的服务预约订单请前往')) {
        const { CarsService: { conf, desc } } = this
        desc[0] = message
        this.CarsService.conf = { ...conf, ...{ show: true } }
        return true
      }
      return false
    },
    // 重要提醒
    // onImportantNote() {},
    onRouter() {
      // 清空保存的数据
      //   dealerModel: {}, // 选择的服务商
      // selectModel: {}, // 服务预约选择的车型
      // selectServiceType: {},// 服务类型
      // selectServiceTime: {},// 服务预约选择的时间
      // this.$store.commit('dealerModel', {})
      // this.$store.commit('saveDealer', {})
      // this.$store.commit('selectServiceType', {})
      this.$store.commit('saveSelectServiceTime', {})
      this.$store.commit('saveSelectServiceShop', [])

      this.$router.push({
        path: '/aftersales/service-success',
        query: { type: 0 }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import url("../../assets/style/scroll.less");

@import "../../assets/style/common.less";
@import url("../../assets/style/buttons.less");
@import url("../../assets/style/animation.less");
@import url("../../assets/style/cell.less");
.container {
  padding: 16px;
}

.flex1 {
  flex: 1;
}
.title-bold {
  font-size: 16px;
  color: #000;
  font-weight: normal;
  font-family: "Audi-WideBold";
}
.item-title-normal {
  font-size: 16px;
  color: #000;
  font-weight: normal;
  font-family: "Audi-Normal";
  padding-bottom: 12px;
  padding-top: 10px;
  border-bottom: 1px solid #000;
}
.item-store {
  .c-flex-between;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  padding: 16px 0;
}
.item-wrapper {
  .c-flex-between;
  padding: 8px 0;
}

.phone-icon {
  width: 12px;
  vertical-align: baseline;
}

.img-wrapper {
  width: 60px;
  height: 60px;
  margin-right: 16px;
}
.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  padding: 5px 0;
}
.navgation-wrapper {
  .c-font14;
  .c-flex-center;
  color: #b2b2b2;
  text-align: center;
  .nav-icon {
    width: 24px;
    height: 24px;
    margin: 0 auto 5px auto;
  }
}

.buyMess {
  padding: 0;
  /deep/.van-form {
    & > .box-field {
      display: flex;
      flex-direction: column;
      position: relative;
      height: 72px;
      width: 100%;
      .box-label {
        width: 100%;
        font-size: 12px;
        color: #646566;
      }

      .van-cell {
        position: relative;
        height: 100%;

        .van-cell__value {
          .van-field__body {
            min-height: calc(100% - 25px);
            border-bottom: 1px solid #000;
            font-size: 16px;
            overflow: visible;
            flex-direction: column;
            justify-content: flex-end;
            align-items: flex-start;

            input {
              margin-bottom: 6px;
              font-size: 14px;
              line-height: 20px;
              height: 20px;
            }

            textarea {
              margin-top: 16px;
              line-height: 16px;
              // padding-top: 25px;
              min-height: 16px;
            }
          }
        }
      }

      .van-field--error {
        .van-field__body {
          border-bottom: 1px solid #9e1f32 !important;
        }

        .van-field__error-message {
          color: #9e1f32;
        }
      }
    }
  }
}

/deep/.van-field__error-message {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 16px;

  &::before {
    content: "";
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url("../../assets/img/error.png") no-repeat 0 0;
    background-size: 14px 14px;
    margin-right: 4px;
  }
}
.chat-input {
  width: 100%;
  display: flex;
  padding-top: 10px;

  textarea {
    width: 100%;
    height: 70px;
    background: #ffffff;
    border: 1px solid #e5e5e5;
    padding: 8px;
    font-size: 12sp;
    color: #000000;
    border-radius: 0;
    -webkit-appearance: none;
  }
}

.bottom_style {
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0px;
  border-top: 2px #f2f2f2 solid;
  padding-top: 16px;
  padding-bottom: 16px;
  left: 0px;
  .checkbox_button {
    margin-left: 16px;
    margin-bottom: 5px;
    width: 18px;
    height: 18px;
    color: #000;
  }
  .checkbox_style {
    display: flex;
    height: 20px;
    justify-content: space-between;
    font-family: "Audi-Normal";
    color: #999999;
    width: 100%;
    font-size: 16px;
    margin-bottom: 16px;
    span {
      font-size: 16px;
      color: #000;
      font-family: "Audi-Normal";
    }
    .checkbox_styleRight {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #000;
      margin-right: 16px;
    }
  }
}
.btn-delete-wrapper {
  margin: 0 16px;
}

.btn-delete-height {
  height: 130px;
}
.custom-align-left {
  padding-left: 35px;
  line-height: 160%;
}
</style>
