<template>
  <div id="detail">
    <div class="box_head">
      <img src="../../assets/img/bg10.png">
    </div>
    <div class="buyMess box">
      <van-form ref="form" @failed="failed" @submit="onSubmit">
        <div class="box-field" id="fullName">
          <label class="box-label">姓名</label>
          <van-field type="textarea" cols="42" input-align="right" placeholder="请输入姓名" :rows="rows.fullName" :disabled="editStatus" autosize max-witdh :label-align="labelAlign"
            :label-width="labelWidth" v-model="formData.fullName" ref="fullName" :maxlength="maxlength.fullName"
            :rules="[ { trigger: 'onBlur', required: true, message: `请输入姓名！`, }, ]" />
        </div>
        <div class="box-field" id="mobile">
          <label class="box-label">联系方式</label>
          <van-field type="textarea" cols="42" :rows="rows.mobile" input-align="right" placeholder="联系方式" :disabled="editStatus" autosize max-witdh :label-align="labelAlign"
            :label-width="labelWidth" v-model="formData.mobile" ref="mobile" :maxlength="11" on-key-up="value = value.replace(/[^\d]/g, '')" :rules="[ { trigger: 'onBlur', required: true, message: `请填写联系方式！`, },
                      { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号！' }]" />
        </div>
        <div class="box-field jt">
          <label class="box-label">试驾城市</label>
          <van-field :label-align="labelAlign" input-align="right" :label-width="labelWidth" :disabled="editStatus" clickable readonly placeholder="请选择城市"
            v-model="formData.carLicenseCityName" :error-message="error_message" @click="doShowCarCity" />
          <van-icon name="arrow" />
          <van-popup v-model="formDataShow.showCarCity" round position="bottom">
            <div class="carCitypicker">
              <div>省份</div>
              <div>城市</div>
            </div>
            <van-picker ref="vanPicker" v-model="cascaderValue" title="选择城市" show-toolbar :default-index="cityDefultIndex" :columns="areaList"
              @cancel="onCancel" @confirm="cityConfirm" />
          </van-popup>
        </div>
        <div class="box-field jt">
          <label class="box-label">代理商</label>
          <van-field :label-align="labelAlign" input-align="right" :disabled="editStatus" :label-width="labelWidth" readonly clickable placeholder="请选择代理商"
            v-model="formData.agent.dealerName" @click="doShowPicker2" :rules="[{ required: true, message: '请选择代理商！' }]" />
          <van-icon name="arrow" />
          <van-popup v-model="formDataShow.showPicker2" :lazy-render="false" round position="bottom">
            <van-picker ref="agent" show-toolbar :columns="columns2" value-key="dealerName" @confirm="onConfirm({value:$event,label:'agent'})"
              @cancel="onCancel" :default-index="defaultIndex.agent" />
          </van-popup>
        </div>
      </van-form>
      <div class="btnWarp">
        <div class="buttons2" style="position: relative;" @click="cancelEnrollShow = true" v-if="editStatus">
          取消报名
        </div>
        <div class="buttons" style="position: relative;" @click="submit" v-else>
          确认
        </div>
      </div>
      <model :modalshow.sync="cancelEnrollShow" @update:modalshow="cancelEnroll" title="确定要取消报名吗？" />
      <model :modalshow.sync="modalshow" @update:modalshow="submitTestDriverOrder" title="确定提交试驾？" />
    </div>
  </div>
</template>

<script>
  import Vue from 'vue'
  import dayjs from 'dayjs'
  import { Form, Field, Icon, Picker, Popup, Toast } from 'vant'
  import { mapState } from 'vuex'
  import wx from 'weixin-js-sdk'
  import { Base64 } from 'js-base64'
  import { getCity } from '../../api/detail'
  import { getUserInfo, getDealerList, createVeryLongReservation, myApplyInfo, cancelEnroll } from '@/api/api'
  import { callNative } from '@/utils'
  import { getModelList, getListResource } from '../../api/test-driver'
  import api from '../../config/url'
  import model from '@/components/model.vue'

  Vue.use(Form).use(Field).use(Icon).use(Picker).use(Popup).use(Toast)
  const codeType = ['00', '200']
  export default {
    name: 'TestdriverCreate',
    inject: ['reload'],
    components: { model },
    data() {
      return {
        fromType:this.$route.query?.fromType === 'fromPurple',
        cityDefultIndex: 1,
        BaseOssHost: api.BaseOssHost,
        modalshow: false,
        formDataShow: {
          showCarCity: false,
          showPicker2: false,
        },
        cancelEnrollShow:false,
        cascaderValue: '',
        error_message: '',
        editStatus:false,
        columns2: [{ dealerCode: '76631019', dealerName: 'Audi house of progress SH', dealerAdrress: '上海市' }],
        formData: {
          agent: '', // 代理商
          fullName: '',
          mobile: '',
          carLicenseCityName: '' // 省市区
        },
        defaultIndex: {
          agent: 0
        },
        labelWidth: 120,
        labelAlign: 'left',
        areaList: [],
        indexCity: '', //定位城市
        animations: {
          fullName: false,
          mobile: false
        },
        rows: {
          fullName: '1',
          mobile: '1',
        },
        maxlength: {
          fullName: '32',
        },
        dealerList: [],
        locationCity:null,
      }
    },
    computed: {
      ...mapState({
        env: 'env',
        wxLocation: 'wxLocation'
      }),
    },
    
    methods: {
      doShowCarCity() {
        if(this.editStatus === false){
          this.formDataShow.showCarCity = true
        }
      },
      doShowPicker2() {
        if(this.editStatus === false){
          if (this.columns2.length > 0) {
            this.formDataShow.showPicker2 = true
          } else {
            
            Toast({
              type: 'fail',
              message: '暂无可预约代理商',
              icon: require('../../assets/img/error.png')
            })
          }
        }
      },
      //取消报名
      async onCancel(){
        Toast({
          type: 'fail',
          message: '正在开发中',
          icon: require('../../assets/img/error.png')
        })
      },
      // 表单提交
      submit() {
        this.$refs.form.submit()
      },
      onSubmit() {
        this.modalshow = true
      },
      submitTestDriverOrder(isSubmit) {
        if (isSubmit) {
          this.$store.commit('showLoading')
          const { activityId } = this.$route.query
          const data = {
            appoName: this.formData.fullName,
            mobile: this.formData.mobile,
            orgCode: this.formData.agent.dealerCode,
            activityId: activityId,
            channel: 2,
          }
          console.log('提交表单1', this.formData, data)

          createVeryLongReservation(data).then((res) => {
            if (res.status === 200 && res.data.code === '200') {
              this.$router.push({
                path: this.fromType ? '/registration-success?fromType=fromPurple' : '/registration-success',
                query: {}
              })
            } else {
              res.data.message && Toast({
                type: 'fail',
                message: res.data.message || '请求错误',
                icon: require('../../assets/img/error.png')
              })
            }
            this.$store.commit('hideLoading')
          })
        }
      },
      failed(err) {
        console.log(err)
      },
      cityConfirm(value, index) {
        console.log(value, index)
        this.formDataShow.showCarCity = false
        this.formData.carLicenseCityName = `${value[0]}/${value[1]}`
        const cityCode = this.areaList[index[0]].children[index[1]].code
        this.columns2 = this.dealerList.filter((e) => cityCode === e.cityCode)
        this.formData.agent = ''
        this.error_message = ''
      },
      onConfirm(res, index) {
        if (res.label === 'agent') {
          this.formData.agent = res.value
          this.formDataShow.showPicker2 = false
        }
      },
      onCancel() {
        this.formDataShow.showCarCity = false
        this.formDataShow.showPicker2 = false
      },
      async cancelEnroll(event){
        if(event){
          const { activityId } = this.$route.query
          const { data } = await cancelEnroll({activityId})
          this.cancelEnrollShow = false
          console.log(data)
          if(data.code === '200' || data.code === '00'){
            Toast({
              type: 'success',
              message: '取消成功',
              icon: require('../../assets/img/success.png')
            })
            const data = await callNative('prepage', { times: 1 })
          }
        }else{
          this.cancelEnrollShow = false
        }
        
      },

      async getListResource() {
        const data = {
          orgCode: this.formData.agent.dealerCode || this.columns2[0].dealerCode,
          seriesCode: this.formData.testdriveSeries.seriesCode || this.columns[0].seriesCode,
          appoType: '90030',
          testType: this.formData.testdriveMode.id || 1
        }
        this.$store.commit('showLoading')
        await getListResource(data).then((res) => {
          this.$store.commit('hideLoading')
          if (res.status === 200 && res.data.code === '200') {
            this.listResource = res.data.data
            this.formData.monthDay = ''
            if (this.listResource.length > 0) {
              this.meishijian = false
              const tmp = []
              this.listResource.forEach((e) => {
                tmp.push(e.appoDate)
              })
              this.columns4 = tmp
              const l = this.columns4.length
              this.minDate = new Date(this.columns4[0])
              this.maxDate = new Date(this.columns4[l - 1])
              this.columns5 = this.trasfromTimeInfos(this.listResource[this.defaultIndex.columns4].appoTimeInfos) || []
            } else {
              this.meishijian = true
              this.columns5 = []
            }
          }
        })
      },
      async getModelList() {
        await getModelList().then((res) => {
          if (res.status === 200 && res.data.code === '00') {
            this.columns = res.data.data
          }
        })
      },
      getDealerList() {
        getDealerList().then((res) => {
          // 过滤掉上汽奥迪总部
          let data = res.data.data
          data = data.filter((e) => e.dealerCode !== '76600019')
          this.dealerList = data
          this.getUserInfo()
        })
      },
      setCityIndex() {
        console.log(this.areaList)
        console.log(this.dealerList)
        var cityArray = null,
          indexArray = null
        this.areaList.forEach((i, index) => i.children.forEach((j, index2) => {
          if (j.text === this.indexCity) {
            this.columns2 = this.dealerList.filter(i=> i.cityCode === j.code)
            cityArray = [i.text, j.text]
            indexArray = [index, index2]
          }
        }))
        if (cityArray && indexArray) {
          this.cityConfirm(cityArray, indexArray)
        } else {
          this.formData.agent = ''
          this.formData.carLicenseCityName = this.indexCity
          this.formData.dealerName = ''
          this.error_message = '该城市暂无代理商，建议您更换试驾城市'
        }
      },
      async getCity(data) {
        const that = this
        const setCity = (data) => {
          const cc = that.areaList.find((item) => {
            const a = item.children.find((it) => (it.text === data))
            return a !== undefined
          })
          if (cc) {
            const ies = cc.children.findIndex((item) => item.text === data)
            that.formData.carLicenseCityName = `${cc.text}/${cc.children[ies].text}`
            that.formData.carLicenseCityCode = cc.children[ies].code
            const a = that.areaList.filter((item) => item.children.find((it) => it.text === data))[0]

            const i = that.areaList.findIndex((item) => (
              item.children.findIndex((it) => it.text === data) !== -1
            ))
            that.cityDefultIndex = i
            const index = a.children.findIndex((item) => item.text === data)
            that.areaList[i + 1].defaultIndex = 1 //index
          }

          that.columns2 = that.dealerList.filter((e) => that.formData.carLicenseCityCode === e.cityCode)
          that.setCityIndex()
        }
        if (data) { setCity(data)  }
        else {
          if (this.locationCity.city) {
            setCity(this.locationCity.city)
          }
        }
      },
      async getData() {

        const { data } = await getCity({ code: '' })
        if (codeType.includes(data.code)) {
          const province = data.data[0]?.children
          const city = []

          const arr = province.map(async (item, index) => {
            const data2 = await getCity({ code: item.code })
            return data2.data.data[0].children
          })
          Promise.all(arr).then((res) => {
            const list = province.map((item, index) => {
              res[index].forEach((it, i) => {
                delete res[index][i].children
              })
              province[index].children = res[index]
              return province[index]
            })
            // list是全部的省市列表，再用dealerList中的城市筛选一遍
            if (list.length > 0 && this.dealerList.length > 0) {
              this.areaList = list.filter((a) => {
                const children = a.children
                const tmp = []
                let flag = false
                const codetmp = []
                children.forEach((b) => {
                  this.dealerList.forEach((c) => {
                    if (c.cityCode === b.code && codetmp.indexOf(b.code) < 0) {
                      tmp.push(b)
                      flag = true
                      if (codetmp.indexOf(b.code) < 0) { // 去重
                        codetmp.push(b.code)
                      }
                    }
                  })
                })
                a.children = tmp
                return flag
              })
            }
            // this.areaList = list
            if (sessionStorage.getItem('isFormDetail') === '1') {
              const newFormData = JSON.parse(localStorage.getItem('formDetail'))
              const str = newFormData.carLicenseCityName
              const index = str.indexOf('/')
              if (index !== -1) {
                this.getCity(str.slice(index + 1))
              } else {
                this.getCity(str)
              }
            } else {
              this.getCity()
            }
          })
        }
      },
      async getUserInfo() {
        // const { data } = await getUserInfo()
        // if (codeType.includes(data.code)) {
        //   this.formData.mobile = data.data.userInfo.mobile
        //   this.formData.fullName = data.data.userInfo.displayName
        //   localStorage.setItem('userInfo', JSON.stringify(data.data.userInfo))
        // }
      },
      filterInput(val) {
        return val.replace(/[^-_a-zA-Z0-9\u4e00-\u9fa5]/, '')
      },
      
      //获取用户报名信息
      async getMyApplyInfo(){
        const { activityId } = this.$route.query
        const { data } = await myApplyInfo({activityId})
        console.log(data.data)
        if(data.data && data.data.status === 100){
          this.formData.agent = {
            dealerName:data.data.dealerName,
            dealerCode:data.data.dealerCode,
          }
          this.formData.fullName = data.data.name
          this.formData.mobile = data.data.mobile
          this.formData.carLicenseCityName = data.data.city
          this.editStatus = true
        }else{
          const data = await callNative('getLocationCity', {})
          this.locationCity = data
          this.indexCity = data.city
          console.log(data.city)
          this.getDealerList()
          this.getData()
        }
      },
    },
    async mounted() {
      this.getMyApplyInfo()
    },
    watch: {
      'formData.agent'(val) {
        const index = this.columns2.findIndex((item) => item === val)
        this.defaultIndex.agent = index
        if (this.formData.agent && this.formData.testdriveSeries) {
          this.getListResource()
        }
      },
      'formData.fullName'(val) {
        // this.formData.fullName = this.filterInput(val)
        this.formData.fullName = val
      },
      'formData.mobile'(val) {
        this.formData.mobile = this.filterInput(val)
      },
      'formData.carLicenseCityName'(val) {

      },
    }
  }
</script>

<style lang="less" scoped>
  @import url("../../assets/style/cell.less");
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/dialog.less");
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/nor.less");

  div,
  input,
  textarea,
  i {
    box-sizing: border-box;
  }
  
  .btnWarp{
    width: 100vw;
    background-color: #fff;
    z-index: 9999 !important;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 20px 0;
    height: 92px;
  }

  .box_head {
    width: 100%;

    img {
      width: 100%;
    }
  }

  #detail {
    padding-bottom: 20px !important;

    .box {
      margin: 16px 15px 80px 17px;
      padding: 0;
    }

    padding-bottom: 40px;

    .toolimg {
      width: 100%;
    }

    .buyMess {
      /deep/.van-form {
        & > .box-field {
          display: flex;
          flex-direction: column;
          position: relative;
          height: 56px;

          .box-label {
            width: 25%;
            font-size: 16px;
            color: #000;
            position: absolute;
            line-height: 20px;
            left: 0;
            top: -2px;
            z-index: 999;
          }

          .van-cell {
            position: relative;
            height: 100%;

            &:active {
              background-color: #fff;
            }

            .van-cell__value {
              .van-field__body {
                // min-height: calc(100% - 25px);
                border-bottom: 1px solid #e5e5e5;
                font-size: 16px;
                overflow: visible;
                flex-direction: column;
                // justify-content: flex-end;
                align-items: flex-start;
                position: relative;
                top: 6px;

                &.border-none {
                  border: none;
                }

                input {
                  min-height: 20px !important;
                  line-height: 20px;
                  padding-top: 1px;
                  width: 100%;
                  position: relative;
                  top: -8px;
                }

                input::-webkit-input-placeholder {
                  font-size: 16px;
                }

                textarea {
                  line-height: 20px;
                  min-height: 30px;
                  width: 100%;
                  position: relative;
                  top: -6px;
                }

                textarea::-webkit-input-placeholder {
                  font-size: 16px;
                }
              }
            }
          }

          ._remarks {
            .van-cell__value {
              .van-field__body {
                border-bottom: none !important;
              }
            }
          }

          .van-field--error {
            .van-field__body {
              border-bottom: 1px solid #9e1f32 !important;
            }

            .van-field__error-message {
              color: #9e1f32;
              position: relative;
              top: 10px;
            }
          }
        }

        .jt {
          position: relative;

          i {
            position: absolute;
            right: 0;
            top: 2px;
            color: #999;
          }

          input {
            font-size: 16px;
            padding-right: 20px;
          }
        }
      }

      .wanshan {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        height: 56px;
        top: -25px;
        border-bottom: solid 1px #000;
      }
    }
  }

  /deep/.van-field__error-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 18px;
    justify-content: flex-end;
    color: #9e1f32;
    position: relative;
    top: 10px;

    &::before {
      content: "";
      display: inline-block;
      width: 14px;
      height: 14px;
      background: url("../../assets/img/error.png") no-repeat 0 0px !important;
      background-size: 14px 14px;
      margin-right: 4px;
    }
  }

  /deep/.van-dialog {
    overflow-y: auto;
    max-height: 80%;
    padding: 15px 16px 16px;
    top: 52% !important;
    z-index: 33336;

    h3 {
      margin: 0;
    }

    .item {
      color: #000;
      font-size: 14px;
      text-align: left;
      margin-bottom: 24px;

      .title {
        line-height: 24px;
      }

      .itemCotent {
        display: flex;
        line-height: 17px;

        div {
          margin-top: 8px;
        }
      }
    }
  }

  /deep/.van-popup {
    border-radius: 0;
    // height: 400px;
    font-size: 14px;

    .carCitypicker {
      position: relative;
      z-index: 2333;
      transform: translateY(80px);
      text-align: center;
      width: 100%;
      display: flex;
      background-color: #fff;
      top: -20px;

      div {
        flex: 1;
      }
    }

    .van-picker__columns {
      // top: 80px;

      .van-hairline-unset--top-bottom {
        border-bottom: 1px solid #eee;
        border-top: 1px solid #eee;
      }

      .van-picker-column {
        font-size: 14px;
      }
    }
  }

  /deep/ .van-field__control {
    &:disabled {
      color: #000;
      -webkit-text-fill-color: #000;
    }
  }

  /deep/ .van-field__control::placeholder {
    color: #999;
    -webkit-text-fill-color: #999;
    font-size: 16px;
  }
</style>
