<template>
  <div>
    <div class="collapse-wrapper">
      <van-collapse v-model="activeNames" accordion>
        <!-- 全部选装 -->
        <van-collapse-item name="ALL">
          <!-- 选装项列表 -->
          <div v-for="item in pageAllOptionList" :key="item.featureCode">
            <!-- 选装项卡片：选中时显示黑色边框 -->
            <div
              class="card-wrapper"
              :class="{
                'selected': item.selected && !item.disabled,
                'disabled': item.disabled
              }"
              @click="clickCheckBox(item)"
            >
              <!-- 标题和价格 -->
              <div class="title-wrapper c-flex-between c-bold c-font16">
                <div class="name">{{ item.externalFeatureNameZh || item.labelValueNameZh }}</div>
                <div v-if="item.equipmentRights && item.equipmentRights == 1 && isCouponValid" class="presale-tag">预售权益</div>
                <span :class="{ 'price-text': !item.featurePrice,'line-through': item.equipmentRights && item.equipmentRights == 1 && isCouponValid }">
                  {{ item.featurePrice | finalFormatPriceDesc }}
                </span>
                <span v-if="item.equipmentRights && item.equipmentRights == 1 && isCouponValid">￥0</span>
              </div>

              <!-- 内容区域 -->
              <div class="content-wrapper c-font12">
                <!-- <div v-if="item.labelNameZh">{{ item.labelNameZh }}</div> -->
              </div>

              <!-- 操作按钮 -->
              <div class="btn-wrapper c-font12">
                <div @click.stop="toOptionDetail(item)">查看详情</div>
              </div>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>

    <CommonFooter @click="nextPage" />
  </div>
</template>

<script>
import Vue from "vue";
import { mapGetters, mapState } from "vuex";
import { Checkbox, CheckboxGroup, Collapse, CollapseItem, Divider, Toast } from "vant";
import wx from "weixin-js-sdk";
import CommonFooter from "../components/commonFooter.vue";
import { getFilterRelateList } from "@/view/a5lConfigration/util/helper";
import ConflictDialog from "./conflictDialog.vue";
import { getUrlParamObj, isEmptyObj } from "@/utils";
import { postOmdModelConfig } from "@/configratorApi";

const { env } = getUrlParamObj();
Vue.use(Toast);
Vue.use(Checkbox);
Vue.use(CheckboxGroup);
Vue.use(Collapse);
Vue.use(CollapseItem);
Vue.use(Divider);

let firstToastTag = true;
export default {
  name: "ConfigrationOption",
  components: { CommonFooter, ConflictDialog },
  data() {
    return {
      activeNames: "ALL",
      pageStartTime: 0,
      pageAllOptionList: []
    };
  },
  computed: {
    ...mapGetters([
      "pageOptionComposes",
      "currentCarType",
      "paramSibDependOptionList",
      "paramSibConflictOptionList",
      "currentSeriesName",
      "paramEihDependsOptionList"
    ]),
    ...mapState({
      userCoupon: (state) => state.configration.userCoupon,
      ccid: (state) => state.ccid,
      skuid: (state) => state.skuid,
      defaultCarConfigList: (state) => state.configration.defaultCarConfigList,
      currentSeat: (state) => state.configration.currentSeat,
      currentHub: (state) => state.configration.currentHub,
      currentComposeName: (state) => state.configration.currentComposeName,
      currentModelLineData: (state) => state.configration.currentModelLineData,
      currentSib: (state) => state.configration.currentSib,
      personalOptionComposes: (state) => state.configration.personalOptionComposes,
      deliveryTimeData: (state) => state.configration.deliveryTimeData,
      selectedOptions: (state) => state.configration.selectedOptions,
      carIdx: (state) => state.configration.carIdx,
      referConfigrationActiveTab: (state) => state.configration.referConfigrationActiveTab,
      currentVersion: (state) => state.configration.currentVersion,
      configrationActiveTab: (state) => state.configration.configrationActiveTab,
      currentPacketMap: (state) => state.configration.currentPacketMap,
      requiredRadPackets: (state) => state.configration.requiredRadPackets,
    }),
    isCouponValid() {
      if (!this.userCoupon) {
        return false;
      }
      // 获取两个字段的值
      const { activityCode, mpEquityNo } = this.userCoupon;

      // 检查两个字段是否都存在且有实际值（非空字符串、非null、非undefined）
      return Boolean(
        activityCode !== undefined &&
        activityCode !== null &&
        activityCode !== "" &&
        mpEquityNo !== undefined &&
        mpEquityNo !== null &&
        mpEquityNo !== ""
      );
    },

    // 当前选中项数组（从Vuex获取）
    selectedItems() {
      return this.selectedOptions;
    },

  },
  watch: {
    // 监听Vuex中选中项变化，同步更新页面显示
    selectedOptions: {
      deep: true,
      handler(val) {
        this.syncPageSelection(val);
      }
    },
    configrationActiveTab(val) {
      if (val === "option") {
        this.setUITitle();
        this.pageStartTime = new Date().getTime();
      }
    }
  },

  mounted() {
    this.setUITitle();
    this.doFromDetailAction();
    this.pageStartTime = new Date().getTime();
    this.handlePersonalOptions();
  },

  methods: {
    isEmptyObj,
    // 同步页面选中状态与Vuex中的选中项
    // syncPageSelection(selectedOptions) {
    //   console.log("%c A5L selectedOptions:", "font-size:16px;color:green;", selectedOptions);
    //   this.pageAllOptionList.forEach(item => {
    //     // 检查当前项是否在选中列表中（通过featureCode唯一标识）
    //     const isSelected = selectedOptions.some(
    //       opt => opt.featureCode === item.featureCode
    //     );

    //     // 只有状态不一致时才更新，避免不必要的重渲染
    //     if (item.selected !== isSelected) {
    //       this.$set(item, "selected", isSelected);
    //     }
    //   });
    // },
    // 同步页面选中状态与Vuex中的选中项（包括disabled）
    syncPageSelection(selectedOptions) {
      this.pageAllOptionList.forEach(item => {
        // 1. 找到Vuex中对应的选中项
        const matchedOption = selectedOptions.find(
          opt => opt.featureCode === item.featureCode
        );

        // 2. 同步选中状态
        const isSelected = !!matchedOption;
        if (item.selected !== isSelected) {
          this.$set(item, "selected", isSelected);
        }

        // 3. 同步disabled状态（关键修改）
        // 如果在选中项中存在，使用选中项的disabled；否则使用本地item的disabled
        const isDisabled = matchedOption ? matchedOption.disabled : item.disabled || false;
        if (item.disabled !== isDisabled) {
          this.$set(item, "disabled", isDisabled);
        }
      });
    },

    // 标题栏的隐藏设置
    setUITitle() {
      this.activeNames = "ALL";
      const composeDom = document.querySelector(".van-collapse-item__title");
      if (composeDom) {
        this.$nextTick(() => {
          composeDom.style.display = "none";
        });
      }
    },

    // 从详情页返回时的操作
    doFromDetailAction() {
      const { optionCode } = this.$route.query;
      if (optionCode) {
        const item = this.pageAllOptionList.find((item) => item.optionCode === optionCode);
        if (item) this.clickCheckBox(item);
      }
    },
    async resizeCar(item, key, alterationType = 1) {
      const paramDto = await this.$store.dispatch("getOmdModelConfigParams", { item, key, alterationType });
      const carConfig = await postOmdModelConfig(paramDto);
      if (carConfig.data.data.code !== "20000") return this.$toast.fail(carConfig.data.data.message);
      const prompt = carConfig.data.data.result[0]?.prompt;
      if (prompt && Array.isArray(prompt) && prompt.length > 0 && prompt[0].labelCode === "PACKET") {
        return false;
      }
      await this.$store.commit("updateCurrentVersion", carConfig.data.data.result[0]);
      await this.$store.dispatch("setRadEquipmentGroup");
      await this.$store.dispatch("setIIEquipmentGroup");
      return true;
    },

    // 点击单个选装项：只修改当前项的选中状态
    async clickCheckBox(item) {
      // 跳过禁用项
      if (item.disabled) return;

      if (this.isRequired(item)) return;

      this.$store.commit("showLoadingNospinner");
      await this.setConflictOption(item);

      // 计算当前项的新选中状态
      const newSelectedState = !item.selected;

      // 2. 再更新Vuex中的选中项数组
      let newSelectedOptions;
      if (newSelectedState) {
        const labelConfig = await this.resizeCar(item, "PACKET");
        if(!labelConfig) {
          return;
        }
        // 选中：添加当前项到选中列表（深拷贝避免引用问题）
        let isExist = false;
        for (const opt of this.selectedOptions) {
          if (opt.featureCode === item.featureCode) {
            this.$store.commit("hideLoadingNospinner");
            isExist = true;
          }
        }
        if (!isExist) {
          newSelectedOptions = [...this.selectedOptions, { ...item, selected: true }];
        } else {
          newSelectedOptions = [...this.selectedOptions];
        }
      } else {
        const labelConfig = await this.resizeCar(item, "PACKET", 2);
        if(!labelConfig) {
          this.$store.commit("hideLoadingNospinner");
          return;
        }
        // 取消选中：从列表中移除当前项
        newSelectedOptions = this.selectedOptions.filter(
          opt => opt.featureCode !== item.featureCode
        );
      }

      // 1. 先更新页面状态（确保UI及时反馈）
      this.$set(item, "selected", newSelectedState);

      // 提交到Vuex
      this.$store.commit("updateSelectedOptions", newSelectedOptions);
      // 处理同级互斥（保持原有逻辑）

      // 选中选装后清空推荐组合（保持原有逻辑）
      if (newSelectedState && this.currentComposeName) {
        this.firstToastFromOptionComposes();
        this.$store.commit("updateCurrentComposeName", "");
      }

      this.$store.dispatch("computeA5LTotalPriceAndEquityPrice", { isCouponValid: this.isCouponValid });
      this.$store.commit("hideLoadingNospinner");
    },

    // 当前选装包,同级互斥处理（保持原有逻辑）
    setConflictOption(option) {
      const conflicts = getFilterRelateList(option.optionRelates, "conflict");

      for (const optionItem of this.pageAllOptionList) {
        if (optionItem.status === 1 || optionItem.manualStatus === 1) continue;

        const paramSibConflictOptionList = this.paramSibConflictOptionList.map((i) => i.optionRelateCode);
        for (const conflict of conflicts) {
          const isSibDisabled = paramSibConflictOptionList.includes(conflict.optionRelateCode);
          if (!isSibDisabled) {
            if (!optionItem.initDisable) {
              this.$set(optionItem, "disabled", false);

              if (optionItem.optionCode === conflict.optionRelateCode) {
                this.$set(optionItem, "disabled", option.selected);
                this.$set(optionItem, "peerDisabled", option.selected);
                break;
              }
            }
          }
        }
      }
    },

    // 初始化选装项列表及选中状态
    handlePersonalOptions() {
      const packets = this.currentVersion.packets || [];
      const optionalEquips = this.currentVersion.optionalEquips || [];
      const originalAllOptions = [...packets, ...optionalEquips];
      const originalSupOptions = this.currentPacketMap.get("PACKET") || [];

      console.log(originalAllOptions);
      console.log(originalSupOptions);

      // 1. 深拷贝数据，避免影响原数据
      const copyAllOptions = JSON.parse(JSON.stringify(originalAllOptions));
      const copySupOptions = JSON.parse(JSON.stringify(originalSupOptions));

      // 2. 收集需要排除的featureCode
      const excludeFeatureCodes = new Set();
      copySupOptions.forEach(supItem => {
        // 确保labelChildren存在且是数组
        if (Array.isArray(supItem.labelChildren)) {
          supItem.labelChildren.forEach(child => {
            if (child.featureCode) {
              excludeFeatureCodes.add(child.featureCode);
            }
          });
        }
      });

      // 3. 过滤allOptions，去除需要排除的元素
      const filteredAllOptions = copyAllOptions.filter(option => {
        return !excludeFeatureCodes.has(option.featureCode);
      });

      // 4. 合并过滤后的allOptions和supOptions
      const mergedOptions = [...filteredAllOptions, ...copySupOptions];
      console.log(mergedOptions, "mergedOptions");

      // 5. 初始化页面列表（全部设为未选中）
      this.pageAllOptionList = mergedOptions.map(option => ({
        ...option,
        selected: false
      }));

      // 6. 同步Vuex中已有的选中状态
      this.syncPageSelection(this.selectedOptions);
    },

    isRequired(item) {
      if (isEmptyObj(this.requiredRadPackets)) {
        return false;
      }

      const packet = this.requiredRadPackets[this.currentHub.featureCode];
      if (isEmptyObj(packet)) {
        return false;
      }

      return packet.featureCode === item.featureCode;
    },

    // 跳转到下一页（保持原有逻辑）
    async nextPage() {
      this.clickOptionSensors("下一步");// 埋点
      this.toNextPage();
    },

    // 跳转到报价单页面（保持原有逻辑）
    async toNextPage() {
      this.$store.commit("showLoading");
      const { orderStatus, action, orderId } = this.$route.query;
      console.log(this.$route.query, "this.$route.query");

      if (action === "modelDetailModify") {
        console.log(orderId, "orderId");
        console.log(this.carIdx, "this.carIdx");
        console.log(this.ccid, "this.ccid");
        console.log(orderStatus, "orderStatus");

        this.$router.push({
          path: "/a5lmodelDetailMofidy",
          query: { orderId, idx: this.carIdx, ccid: this.ccid, orderStatus }
        });
        return;
      }

      await Promise.all([
        this.$store.dispatch("getSkuId"),
        this.$store.dispatch("getCCid", { orderStatus })
      ]);
      this.$store.commit("hideLoading");

      // if (['30', '00'].includes(orderStatus)) {
      //   this.toMoneyDetailPage()
      //   return
      // }

      if (env === "minip" && !this.$storage.get("token")) {
        this.openWebViewMinip();
      } else {
        this.toQuotationPage();
      }
    },

    // 跳转到报价单页面（保持原有逻辑）
    async toQuotationPage() {
      const { orderStatus, orderId, dealerCode, shoppingCartId } = this.$route.query;

      this.$router.push({
        path: "/quotation",
        query: {
          ccid: this.ccid,
          skuid: this.skuid,
          orderStatus,
          orderId,
          dealerCode,
          shoppingCartId,
          idx: this.carIdx,
          customBack: "newConfigration"
        }
      });
    },

    // 跳转小程序页面（保持原有逻辑）
    async openWebViewMinip() {
      const { orderStatus, orderId, dealerCode, shoppingCartId } = this.$route.query;
      const ccid = this.ccid;
      const skuid = this.skuid;
      const idx = this.carIdx;
      const query = { ccid, skuid, orderStatus, orderId, dealerCode, shoppingCartId, idx, customBack: "newConfigration" };
      const { origin, pathname } = window.location;
      const string = Object.keys(query).reduce((i, n) => i + (query[n] ? (`&${n}=${query[n]}`) : ""), "");
      const strp = string.substring(1, string.length);
      const url = encodeURIComponent(`${origin}${pathname}#/quotation?${strp}`);
      wx.miniProgram.navigateTo({ url: `/pages/web/index?idx=${idx}&url=${url}&skuid=${skuid}&dealerCode=${dealerCode}` });
    },

    // 进入详情页（保持原有逻辑）
    toMoneyDetailPage() {
      const { orderId } = this.$route.query;
      this.$router.push({ path: "/order/money-detail", query: { orderId } });
    },

    // 进入选装详情页面（保持原有逻辑）
    toOptionDetail(item) {
      let disabledRights = false;
      if (item?.equipmentRights && item?.equipmentRights == 1 && this.isCouponValid) {
        disabledRights = true;
      }
      this.$store.commit("updateCurrentOptionDetail", item);
      // const query = item.disabled ? { disabled: item.disabled } : {}
      // this.$router.push({
      //   path: '/a5lConfigrationOptionDetail',
      //   query: { customBack: 'newConfigration', ...query }
      // })
      this.$router.push({
        path: "/a5lConfigrationOptionDetail", // 确保路径与详情页路由一致
        query: {
          customBack: "a5lConfigration",
          disabledRights: disabledRights.toString() // 转为字符串，避免路由参数类型问题
        }
      });
      this.clickOptionSensors("查看详情");// 埋点
    },

    // 首次点击全部选装提示（保持原有逻辑）
    firstToastFromOptionComposes() {
      if (firstToastTag) {
        const text = "签订合同后六个月内交付,定金五万,定金不可退";
        Toast(text);
        this.$store.commit("updateFooterDesc", { desc: text });
        firstToastTag = false;
      }
    },
    // 埋点
    clickOptionSensors(buttonName) {
      const carMap = { 0: "A7L", 1: "Q5 e-tron", 2: "Q6", 3: "A5L" };
      const tabMap = { exterior: "外观", interior: "内饰", option: "选装", equity: "权益" };
      const { engine, customSeriesName } = this.currentModelLineData;

      let param = {
        source_module: "H5",
        refer_tab_name: tabMap[this.referConfigrationActiveTab],
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: "定制交付",
        select_package: this.selectedItems.map((i) => i.optionName).join(),
        package_type: this.currentComposeName ? "组合包" : "选装包",
        select_part: "",
        button_name: buttonName
      };

      if (buttonName === "下一步") {
        param = { ...param, $event_duration: Math.floor((Date.now() - this.pageStartTime) / 1000) };
      }

      console.log("CC_CarConfiguration_OptionalPackage_BtnClick:", param);
      this.$sensors.track("CC_CarConfiguration_OptionalPackage_BtnClick", param);
    },

    // 埋点
    clickOptionPopupSensors(buttonName, windowName) {
      const carMap = { 0: "A7L", 1: "Q5 e-tron", 2: "Q6", 3: "A5L" };
      const { engine, customSeriesName } = this.currentModelLineData;

      const param = {
        source_module: "H5",
        window_name: windowName,
        car_series: carMap[this.carIdx],
        car_type: this.currentCarType,
        power_type: `${customSeriesName} ${engine}`,
        car_version: this.currentVersion.styleName,
        delivery_type: "定制交付",
        select_package: this.selectedItems.map((i) => i.optionName).join(),
        package_type: "选装包",
        select_part: "",
        button_name: buttonName
      };

      console.log("CC_CarConfiguration_OptionalPackage_PopupClick", windowName);
      this.$sensors.track("CC_CarConfiguration_OptionalPackage_PopupClick", param);
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../assets/style/common.less";

.collapse-wrapper {
  overflow-y: auto;
  height: calc(100vh - @HeaderHeight - @TabHeight - @FooterHeight);
  padding-bottom: @FooterHeight;
}

.margin-top {
  margin-top: 32px;
}

// 选装项卡片样式
.card-wrapper {
  position: relative;
  border-radius: 4px;
  border: 1px solid #E5E5E5; /* 默认边框 */
  padding: 14px 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
  cursor: pointer; /* 鼠标指针提示可点击 */
  transition: border-color 0.2s ease; /* 边框颜色过渡动画 */

  // 选中状态：2px黑色边框
  &.selected {
    border: 1px solid #000 !important; /* 黑色边框，!important确保优先级 */
  }

  // 禁用状态：半透明+灰色边框
  &.disabled {
    //opacity: 0.5;
    //cursor: not-allowed;
    border: 1px solid #000 !important; /* 黑色边框，!important确保优先级 */
  }

  > .title-wrapper {
    color: #333;
    line-height: 22px;

    > .name {
      max-width: 65%;
    }

    > .price-text {
      font-size: 12px;
      color: #999;
      font-weight: normal;
    }

    .relative {
      position: relative;

      .discount {
        position: absolute;
        left: 103%;
        top: 50%;
        transform: translateY(-50%);
        background: #EB0D3F;
        color: #fff;
        padding: 0 5px;
        line-height: 16px;
        width: max-content;
      }
    }

    .summer-price {
      position: absolute;
      right: 16px;
      top: 40px;
      text-decoration: line-through;
      color: #ccc;
      font-size: 14px;
    }
  }

  > .content-wrapper {
    overflow: auto;
    height: 66px;
    margin-top: 6px;
    max-width: 75%;

    > div {
      line-height: 20px;
    }
  }

  > .btn-wrapper {
    position: relative;
    width: 100%;
    color: #000;
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    > .depend-desc {
      position: absolute;
      bottom: 100%;
      left: 0;
      color: #8b8b8b;
    }
  }

  > .tag-wrapper {
    position: absolute;
    right: 0;
    bottom: 0;
    background: #EB0D3F;
    color: #fff;
    font-weight: normal;
    font-size: 12px;
    line-height: 20px;
    box-sizing: border-box;
    text-align: right;
    padding: 0 12px 0 18px;
    border-radius: 0 0 3px 0;

    &::before {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      background-color: #fff;
      border-top: 10px solid transparent;
      border-bottom: 10px solid #EB0D3F;
      border-left: 8px solid transparent;
      border-right: 8px solid #EB0D3F;
    }
  }
}

// 覆盖vant组件样式
/deep/ .collapse-wrapper {
  .van-collapse-item__title {
    font-size: 16px;
  }

  .van-hairline--top-bottom:after {
    border: none;
  }

  // 确保选中样式穿透scoped生效
  .card-wrapper.selected {
    border: 1px solid #000 !important;
  }
}

.line-through {
  text-decoration: line-through;
  color: gray;
}

.presale-tag {
  position: absolute;
  top: -5px;
  left: -5px;
  background-color: #EB0D3F; // 红色背景
  color: white; // 白色文字
  font-size: 10px;
  padding: 2px 12px 2px 5px;
  line-height: 16px;
  // 上尖下缩，右侧带弧度的形状
  clip-path: polygon(0 0, 100% 0, 85% 100%, 0 100%);
  z-index: 2; // 确保在图片上方显示
  white-space: nowrap;
}
</style>