<template>
  <div class="order-info-forms-box">
    <van-form
      ref="form"
      @failed="failed"
      @submit="confirmVisible = true"
    >
      <van-cell-group>
        <div class="cell-title van-hairline--bottom">
          <div class="h2">
            购车信息
          </div>
        </div>
        <van-field
          :label-align="labelAlign"
          :label-width="labelWidth"
          label="购买类型"
          data-flex="cross:center"
        >
          <template
            #input
          >
            <van-radio-group
              class="lan-radio-group"
              v-model="submitParam.buyType"
              data-flex="main:left"
              :disabled="!!orderId"
            >
              <van-radio
                v-for="t in columns"
                :key="t.type"
                :name="t.type"
              >
                {{ t.text }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>

        <!-- <van-field
          :label-align="labelAlign"
          :label-width="labelWidth"
          label="付款方式"
          data-flex="cross:center"
        >
          <template
            #input
          >
            <van-radio-group
              class="lan-radio-group"
              v-model="submitParam.paymentMethod"
              data-flex="main:left"
            >
              <van-radio
                v-for="t in paymentMethod"
                :key="t.type"
                :name="t.type"
              >
                {{ t.text }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field> -->
        <van-field
          :is-link="!$route.query.dealerCode"
          :disabled="$route.query.dealerCode"
          readonly
          :label-align="labelAlign"
          :label-width="labelWidth"
          label="代理商"
          v-model="submitParam.dealerName"
          :rules="[ { required: true, message: '请选择代理商', }, ]"
          placeholder="请选择代理商"
        >
          <template
            #input
          >
            <input
              type="text"
              placeholder="请点击选择代理商"
              v-model="submitParam.dealerName"
              :disabled="$route.query.dealerCode"
              readonly
              @click="toDealerList"
              class="van-field__control"
            >
          </template>
        </van-field>
      </van-cell-group>
      <van-cell-group>
        <div class="cell-title van-hairline--bottom">
          <div class="h2">
            购车信息
          </div>
        </div>
        <van-field
          type="textarea"
          cols="42"
          id="fullName"
          :rows="rows.fullName"
          autosize
          max-width
          :label-align="labelAlign"
          :label-width="labelWidth"
          v-model="submitParam.fullName"
          :disabled="formNotEditable.fullName"
          ref="fullName"
          label="下单姓名"
          placeholder="请输入下单人姓名"
          @blur="handlerBlur('fullName')"
          @focus="handlerFocus('fullName')"
          @input="formFormatterName('fullName', submitParam.fullName)"
          :maxlength="maxlength.fullName"
          :rules="[ { trigger: 'onBlur', validator: formValidatorName, message: `请输入正确的下单人姓名！`, } ]"
        />
        <van-field
          disabled
          id="mobile"
          :label-align="labelAlign"
          :label-width="labelWidth"
          name="asyncValidator"
          v-model="submitParam.mobile"
          maxlength="11"
          type="number"
          label="联系电话"
          placeholder="请输入下单人联系电话"
          on-key-up="value = value.replace(/[^\d]/g, '')"
          :rules="[ { trigger: 'onBlur', validator: formValidatorMobile, message: '请输入正确的联系电话', } ]"
        />
        <van-field
          :label-align="labelAlign"
          :label-width="labelWidth"
          readonly
          clickable
          is-link
          placeholder="请点击选择证件类型"
          label="证件类型"
          :disabled="formNotEditable.certificateCode"
          v-model="certificateTypeText"
          @click="formDataShow.showType = !formNotEditable.certificateCode"
          :rules="[{ trigger: 'change', required: true, message: '请选择证件类型' }]"
        />
        <van-popup
          v-model="formDataShow.showType"
          :lazy-render="false"
          position="bottom"
        >
          <van-picker
            title="证件类型"
            show-toolbar
            ref="type"
            :default-index="defaultIndex.type"
            :columns="columns1"
            @confirm="onConfirm2"
            @cancel="onCancel"
          />
        </van-popup>
        <van-field
          id="certificateCode"
          :label-align="labelAlign"
          :label-width="labelWidth"
          v-model="submitParam.certificateCode"
          ref="certificateCode"
          :maxlength="maxlength.code"
          :disabled="formNotEditable.certificateCode"
          label="证件号码"
          placeholder="请输入证件号码"
          on-key-up="value = value.replace(/[^\w]/g, '')"
          @blur="handlerBlur('certificateCode')"
          @focus="handlerFocus('certificateCode')"
          @input="formFormatterName('certificateCode', submitParam.certificateCode)"
          :rules="[ { trigger: 'onBlur', validator: formValidatorIDCode, type: submitParam.certificateTypeVal, message: '请输入正确的证件号码！', } ]"
        />
        <template v-if="submitParam.buyType === '01'">
          <div class="cell-title van-hairline--bottom">
            <h2 class="h2 mtb">
              车主信息
            </h2>
            <p class="sub">
              车主信息将用以签署购车合同及开具新车购车发票，请确保车主姓名及证件信息真实有效，且手机号为车主本人手机号，否则后续将无法签署合同。
            </p>
          </div>
          <van-cell
            title="与购车人信息保持一致"
            class="lan-cell-switch"
          >
            <template #default>
              <van-switch
                :size="28"
                :active-color="'#0da20d'"
                :inactive-color="'#d9d9d9'"
                class="lan-switch"
                v-model="isIdentical"
              />
            </template>
          </van-cell>
          <template v-if="!isIdentical">
            <van-field
              type="textarea"
              id="carOwnerName"
              cols="42"
              autosize
              :rows="rows.carOwnerName"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="submitParam.carOwnerName"
              ref="carOwnerName"
              :disabled="formNotEditable.carOwnerName"
              @input="formFormatterName('carOwnerName', submitParam.carOwnerName)"
              @blur="handlerBlur('carOwnerName')"
              @focus="handlerFocus('carOwnerName')"
              label="车主姓名"
              placeholder="请输入车主姓名"
              :maxlength="maxlength.carOwnerName"
              :rules="[ { trigger: 'onBlur', validator: formValidatorName, message: '请输入正确的车主姓名!', }]"
            />
            <van-field
              id="carOwnerMobile"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="submitParam.carOwnerMobile"
              :disabled="formNotEditable.carOwnerMobile"
              ref="carOwnerMobile"
              type="number"
              label="联系电话"
              placeholder="请输入车主联系电话"
              @blur="handlerBlur('carOwnerMobile')"
              @focus="handlerFocus('carOwnerMobile')"
              on-key-up="value = value.replace(/[^\d]/g, '')"
              maxlength="11"
              :rules="[ { trigger: 'onBlur', validator: formValidatorMobile, name: 'carOwnerMobile', message: '请输入正确的车主联系电话，且必须与购车人电话不一致！', }]"
            />
            <van-field
              :label-align="labelAlign"
              :label-width="labelWidth"
              readonly
              clickable
              is-link
              placeholder="请点击选择证件类型"
              label="证件类型"
              :disabled="formNotEditable.carOwnerCertificateNumber"
              v-model="carownerCertificateTypeText"
              @click="formDataShow.showIDType = !formNotEditable.carOwnerCertificateNumber"
              :rules="[{ trigger: 'change', required: true, message: '请点击选择证件类型' }]"
            />
            <van-popup
              v-model="formDataShow.showIDType"
              :lazy-render="false"
              position="bottom"
            >
              <van-picker
                :default-index="defaultIndex.carOwnerCertificateType"
                title="证件类型"
                show-toolbar
                ref="carOwnerCertificateType"
                :columns="columns1"
                @confirm="onConfirm1"
                @cancel="onCancel"
              />
            </van-popup>
            <van-field
              id="carOwnerCertificateNumber"
              label="证件号码"
              placeholder="请输入车主证件号码"
              :label-align="labelAlign"
              :label-width="labelWidth"
              v-model="submitParam.carOwnerCertificateNumber"
              :maxlength="maxlength.carOwnerCertificateNumber"
              :disabled="formNotEditable.carOwnerCertificateNumber"
              on-key-up="value = value.replace(/[^\w]/g, '')"
              ref="carOwnerCertificateNumber"
              @blur="handlerBlur('carOwnerCertificateNumber')"
              @focus="handlerFocus('carOwnerCertificateNumber')"
              @input="formFormatterName('carOwnerCertificateType', submitParam.carOwnerCertificateType)"
              :rules="[ { trigger: 'onBlur', validator: formValidatorIDCode, type: submitParam.carOwnerCertificateType, message: '请输入正确的车主证件号码！', }]"
            />
          </template>
        </template>
        <template v-else>
          <van-field
            type="textarea"
            cols="24"
            autosize
            id="enterpriseName"
            :label-align="labelAlign"
            :label-width="labelWidth"
            v-model="submitParam.enterpriseName"
            ref="enterpriseName"
            label="企业名称"
            placeholder="请输入企业名称"
            :rows="rows.enterpriseName"
            :disabled="formNotEditable.enterpriseName"
            @blur="handlerBlur('enterpriseName')"
            @focus="handlerFocus('enterpriseName')"
            @input="formFormatterEnterpriseName('enterpriseName', submitParam.enterpriseName)"
            :rules="[ { trigger: 'onBlur', validator: enterpriseName, message: '请输入正确的企业名称！'}] "
          />
          <van-field
            id="enterpriseCode"
            :label-align="labelAlign"
            :label-width="labelWidth"
            :disabled="formNotEditable.enterpriseCode"
            v-model="submitParam.enterpriseCode"
            ref="enterpriseCode"
            maxlength="18"
            label="企业代码"
            placeholder="请输入统一社会信用代码"
            on-key-up="value=value.replace(/[\W]/g,'')"
            @blur="handlerBlur('enterpriseCode')"
            @focus="handlerFocus('enterpriseCode')"
            @input="formFormatterName('enterpriseCode', submitParam.enterpriseCode)"
            :rules="[ { trigger: 'onBlur', validator: enterpriseCode, message: '请输入正确的组织机构代码！'}] "
          />
        </template>
      </van-cell-group>
    </van-form>

    <div class="btn-wrapper">
      <AudiButton
        text="确认"
        font-size="16px"
        color="black"
        height="56px"
        @click="openSubmit"
      />
    </div>


    <van-popup
      v-model="confirmVisible"
      class="popup-custom"
    >
      <div class="popup-custom-box">
        <div class="popup-custom-main">
          <div class="text align-center">
            确定修改购买信息吗?
          </div>
        </div>
        <div
          class="popup-custom-btn"
          data-flex="main:justify"
        >
          <div class="line-two-cols lan-button-box">
            <AudiButton
              text="取消"
              font-size="16px"
              height="56px"
              @click="confirmVisible = false"
            />
          </div>
          <div class="line-two-cols lan-button-box">
            <AudiButton
              text="确认"
              font-size="16px"
              color="black"
              height="56px"
              @click="confirmUpdate"
            />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Form,
  Field,
  Icon,
  Button,
  Picker,
  Popup,
  Toast,
  RadioGroup,
  Radio,
  Checkbox,
  CheckboxGroup,
  Dialog,
  AddressEdit,
  Area,
  Cell,
  CellGroup,
  Cascader,
  Switch
} from 'vant'
import { getMyOrders, getUserInfo, updateNgaOrderInfo } from '@/api/api'
import {
  buyType, modifyBuyType, certificateType, paymentMethod
} from '@/config/constant'
import AudiButton from '@/components/audi-button'
import validateIdCard from '@/utils/idcard'
import checkSocialCreditCode from '@/utils/enterprise.code'
import {
  paramsStrict
} from '@/utils/'
import store from '@/store/index'
import { mapState } from 'vuex'

Vue.use(Form)
  .use(Button)
  .use(Field)
  .use(Icon)
  .use(Picker)
  .use(RadioGroup)
  .use(Radio)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Dialog)
  .use(Popup)
  .use(Area)
  .use(Cell)
  .use(CellGroup)
  .use(AddressEdit)
  .use(Cascader)
  .use(Switch)

export default {
  components: { AudiButton },
  data() {
    return {
      labelWidth: 72,
      labelAlign: 'left',
      paymentMethod,
      unOrderInfo: {},
      defaultIndex: {
        buyType: 0,
        type: '0',
        carOwnerCertificateType: '0'
      },
      orderId: '',
      formDataShow: {
        showPicker: false,
        showCarOwnerCertificateType: false,
        showCarCity: false,
        showType: false,
        showIDType: false
      },
      columns: modifyBuyType,

      columns1: certificateType,
      animations: {
        fullName: false,
        certificateCode: false,
        carOwnerName: false,
        carOwnerMobile: false,
        carOwnerCertificateNumber: false,
        enterpriseName: false,
        enterpriseCode: false,
        institutional: false
      },
      rows: {
        fullName: '1',
        carOwnerName: '1',
        enterpriseName: '1'
      },
      maxlength: {
        carOwnerName: '32',
        fullNmae: '32',
        code: 18,
        carOwnerCertificateNumber: 18
      },
      cityDefultIndex: 0,

      formData: {
        buyType: '',
        fullName: '',
        carOwnerCertificateNumber: '',
        code: '',
        carOwnerMobile: '',

        orderId: '',
        carBuyerInfo: {
          buyType: '01',
          fullName: '',
          mobile: '',
          moreContact: [],
          carOwnerCertificateNumber: '',
          carOwnerCertificateType: '',
          carOwnerMobile: '',
          carOwnerName: '',
          enterpriseName: '',
          enterpriseCode: ''
        },
        confirmReturn: {
          applyType: ''
        },

        carCustomInfo: {
          carLicenseCityName: '',
          carLicenseCityCode: ''
        },
        orderItemList: [{
          reserveOrderAmount: 0,
          imageUrl: '',
          status: ''
        }],
        payList: [{
          payTime: ''
        }],
        orderStatus: '',
        orderStatusDesc: '',
        orderTime: ''
      },
      cascaderValue: '',
      areaList: [],
      isIdentical: true,
      orderStatus: '',

      confirmVisible: false,
      // 新添加的提交的参数
      submitParam: {
        // ::: certificateTypeVal +  certificateCode == moreContact
        certificateTypeVal: '', // 购车人证件类型,
        certificateCode: '', // 购车人证件号码
        // paymentMethod: '',
        // origin data
        buyType: '', // 购买类型
        fullName: '', // 购买人名字
        mobile: '', // 购买人手机
        moreContact: '', // 购车人证件类型+证件号
        carOwnerCertificateNumber: '', // 车主证件号
        carOwnerCertificateType: '', // 车主证件类型
        carOwnerMobile: '', // 车主手机号
        carOwnerName: '', // 车主名字
        enterpriseName: '', // 企业名称
        enterpriseCode: '' // 企业组织机构
      },
      formNotEditable: {
        fullName: false,
        certificateCode: false,
        carOwnerName: false,
        carOwnerMobile: false,
        carOwnerCertificateNumber: false,
        enterpriseName: false,
        enterpriseCode: false
      },
      unalterable: {
        dealerCity: false,
        dealerName: false
      },
    }
  },

  computed: {
    buyTypeText() {
      return modifyBuyType.find((i) => i.type === this.submitParam.buyType)?.text
    },

    // 购车人证件类型
    certificateTypeText() {
      return certificateType.find((i) => i.value === this.submitParam.certificateTypeVal)?.text
    },
    // 车主证件类型
    carownerCertificateTypeText() {
      return certificateType.find((i) => i.value === this.submitParam.carOwnerCertificateType)?.text
    },

    // 是否为待支付意向金状态
    isPayXiaoDing() {
      // 本来按照需求小订未付款的状态是支持修改类型的, 但是接口可能有点问题
      // 所以这里先强制使用false全部禁止修改类型
      // return this.orderStatus === '00'
      return false
    },
    ...mapState({
      dealerName: (state) => state.dealerInfo.dealerName,
      isReserveCard: 'isReserveCard',
      reserveCardInfo: 'reserveCardInfo',
    })
  },
  watch: {
    reserveCardInfo(card) {
      const { orgCode, orgName } = card
      this.dealerName = orgName
      this.unalterable.dealerName = true
      // this.getHeadquartersDealerInfo(orgCode)
    },
  },

  mounted() {
    const { orderId, fields } = paramsStrict(this.$route.query)

    if (orderId) {
      this.orderId = orderId
      this.getUserInfo()
    }
    this.getOrderDetail(orderId, fields)
  },

  methods: {
    // filterInput(val) {
    //   return val.replace(/[^-_a-zA-Z0-9\u4e00-\u9fa5]/, '')
    // },
    handleFormNotEditable(fields) {
      if (!fields.length) {
        return
      }
      const { formNotEditable } = this
      for (const key in formNotEditable) {
        if (Object.hasOwnProperty.call(formNotEditable, key)) {
          this.formNotEditable[key] = fields === 'all' ? false : !fields.split(',').includes(key)
        }
      }
      console.log('%c [ fields ]-756', 'font-size:14px; background:#cf222e; color:#fff;', fields, this.formNotEditable)
    },
    openSubmit() {
      this.syncOwnerInfo()
      this.$refs.form.submit()
    },

    syncOwnerInfo() {
      // 更新车主信息
      console.log('update owner info ', this.isIdentical)
      if (this.isIdentical) {
        this.submitParam.carOwnerName = this.submitParam.fullName
        this.submitParam.carOwnerCertificateNumber = this.submitParam.certificateCode
        this.submitParam.carOwnerCertificateType = this.submitParam.certificateTypeVal
        this.submitParam.carOwnerMobile = this.submitParam.mobile
        this.submitParam.carownerCertificateTypeText = this.submitParam.certificateTypeText
      }
    },

    // 设置手机号
    async getUserInfo() {
      const { data } = await getUserInfo()
      this.formData.mobile = data.data.userInfo?.mobile
      this.submitParam.mobile = data.data.userInfo?.mobile
    },

    async getOrderDetail(orderId, fields) {
      this.handleFormNotEditable(fields)
      let carBuyerInfo = {}
      let orderStatus = ''
      if (orderId) {
        const { data } = await getMyOrders({ orderId })
        carBuyerInfo = data.data.carBuyerInfo
        this.submitParam = carBuyerInfo

        this.submitParam.dealerName = data.data.dealerInfo.dealerName
        // this.$set(this.submitParam, 'paymentMethod', data.data.extInfo.paymentMethod)
        this.$set(this.submitParam, 'certificateTypeVal', carBuyerInfo.moreContact.split(',')[0])
        this.$set(this.submitParam, 'certificateCode', carBuyerInfo.moreContact.split(',')[1])

        orderStatus = data.data.orderStatus
      } else {
        const unOrderInfo = await store.dispatch('GetOrder')
        const { state: { dealerInfo } } = store

        this.unOrderInfo = unOrderInfo
        const {
          carBuyerInfo: { moreContact }, dealerInfo: { dealerName, dealerCode, isReserveCard },
          carLicenseCityName
        } = unOrderInfo
        carBuyerInfo = unOrderInfo.carBuyerInfo
        const [IDType, IDNumber] = moreContact?.split(',') || ['', '']

        this.submitParam = {
          ...carBuyerInfo,
          dealerName: dealerInfo?.dealerCode && dealerInfo.dealerCode !== dealerCode ? dealerInfo.dealerName : dealerName,
          // paymentMethod,
          certificateTypeVal: IDType,
          certificateCode: IDNumber,
          carLicenseCityName: dealerInfo?.dealerCode && dealerInfo.dealerCode !== dealerCode ? `${dealerInfo.provinceName}/${dealerInfo.cityName}` : carLicenseCityName
        }

        if (isReserveCard) {
          this.submitParam.isReserveCard = isReserveCard
        }

        console.log('%c [ GetOrder ]-682', 'font-size:14px; background:#cf222e; color:#fff;', carBuyerInfo, this.submitParam.carLicenseCityName)
      }
      const { dealerCode } = this.$route.query
      if (dealerCode) {
        // 代理商门店里的车 默认不允许选择城市/代理商信息
        this.unalterable.dealerName = true
      }

      this.handlerFocus('fullName')
      this.handlerFocus('certificateCode')
      this.handlerFocus('carOwnerName')
      this.handlerFocus('carOwnerMobile')
      this.handlerFocus('carOwnerCertificateNumber')
      this.handlerFocus('enterpriseName')
      this.handlerFocus('enterpriseCode')

      console.log('%c [ carBuyerInfo.mobile === carBuyerInfo.carOwnerMobile && carBuyerInfo.fullName === carBuyerInfo.carOwnerName ]-737', 'font-size:14px; background:#cf222e; color:#fff;', carBuyerInfo.mobile, carBuyerInfo.carOwnerMobile, carBuyerInfo.fullName, carBuyerInfo.carOwnerName)
      // 初始化勾选状态
      this.isIdentical = carBuyerInfo.mobile === carBuyerInfo.carOwnerMobile && carBuyerInfo.fullName === carBuyerInfo.carOwnerName

      //  购车类型
      const buyTypeName = modifyBuyType.find((i) => i.type === carBuyerInfo.buyType)?.text
      this.formData.buyType = buyTypeName

      console.log('%c [ formData, submitParam ]-686', 'font-size:14px; background:#cf222e; color:#fff;', this.formData, this.submitParam)
      // 订单状态(待支付意向金的时候可以修改类型, 支付意向金之后就无法修改)
      this.orderStatus = orderStatus
    },

    inputValue(val) {
      this.submitParam[val] = this.limitstr(this.submitParam[val], 32)
    },

    limitstr(strval, strnum) {
      let re = ''
      const strleng = strval.length
      // 返回字符串的总字节数
      // eslint-disable-next-line no-control-regex
      const byteleng = strval.replace(/[^\x00-\xff]/g, '**').length
      if (byteleng <= strnum) return strval
      for (let i = 0, bytenum = 0; i < strleng; i++) {
        const byte = strval.charAt(i)
        // eslint-disable-next-line no-control-regex
        if (/[\x00-\xff]/.test(byte)) {
          bytenum++ // 单字节字符累加1
        } else {
          bytenum += 2 // 非单字节字符累加2
        }
        if (bytenum <= strnum) {
          re += byte
        } else {
          return re
        }
      }
    },
    failed(err) {
      console.error('failed', err)
    },

    cityConfirm(value, index) {
      this.formDataShow.showCarCity = false
      this.formData.carLicenseCityName = `${value[0]}/${value[1]}`
      this.formData.carLicenseCityCode = this.areaList[index[0]].children[index[1]].code
    },
    onCancel() {
      this.formDataShow.showPicker = false
      this.formDataShow.showType = false
      this.formDataShow.showIDType = false
    },

    onConfirm1(value, index) {
      this.submitParam.carOwnerCertificateType = value.value
      this.formDataShow.showIDType = false
    },
    onConfirm2(value, index) {
      this.submitParam.certificateTypeVal = value.value
      this.formDataShow.showType = false
    },

    // 证件号校验
    validator(val) {
      const rules = this.columns1.find((item) => item.value === this.submitParam.certificateTypeVal)?.rules
      return rules.test(val)
    },

    // 验证购买类型
    onConfirm(data) {
      console.log(data)
      this.formDataShow.showPicker = false
      this.submitParam.buyType = data.type

      // if (this.submitParam.buyType !== data.text) {
      //   this.submitParam.buyType = data.text
      //   this.$refs.form.resetValidation();

      //   [
      //     'fullName',
      //     'code',
      //     'carOwnerName',
      //     'carOwnerMobile',
      //     'carOwnerCertificateNumber',
      //     'enterpriseName',
      //     'enterpriseCode',
      //     'institutional'
      //   ].forEach((item) => {
      //     this.formData[item] = ''
      //     if (this.animations[item]) {
      //       this.animations[item] = false
      //     }
      //   })
      // }
    },
    animation(ref) {
      this.animations[ref] = true
      this.$refs[ref].focus()
      if (this.blur) {
        this.$refs[ref].blur()
      }
    },

    validator2(val) {
      const rules = this.columns1.find((item) => item.value === this.submitParam.carOwnerCertificateType).rules
      return rules.test(val)
    },

    handlerFocus(prop) {
      const pannel = document.getElementById(prop)

      if (pannel) {
        // 让当前的元素滚动到浏览器窗口的可视区域内
        pannel.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }

      // 此方法是标准的scrollIntoView()方法的专有变体
      // pannel.scrollIntoViewIfNeeded();

      // if (!this.animations[prop]) {
      //   this.animations[prop] = true
      // }
    },
    handlerBlur(prop) {
      this.animations[prop] = !!this.submitParam[prop]
    },
    updateForm() {
      this.formData.carOwnerName = this.formData.fullName
      this.formData.carOwnerMobile = this.formData.mobile
      this.formData.carOwnerCertificateType = this.formData.type
      this.formData.carOwnerCertificateNumber = this.formData.code
    },

    async confirmUpdate() {
      const {
        certificateTypeVal,
        certificateCode,
        buyType,
        fullName,
        carOwnerCertificateNumber,
        carOwnerCertificateType,
        carOwnerMobile,
        carOwnerName,
        enterpriseName,
        enterpriseCode,
        // paymentMethod
      } = this.submitParam

      console.log('%c [ confirmUpdate ]-826', 'font-size:14px; background:#cf222e; color:#fff;', this.submitParam)
      if (!this.orderId) {
        const orderInfo = this.handleUnOrderInfo()
        store.dispatch('SetOrder', orderInfo)
        Toast({
          message: '更换成功',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800,
          onClose: () => this.$router.back()
        })
        return
      }

      // submit
      const param = {
        moreContact: `${certificateTypeVal},${certificateCode}`,
        buyType,
        carBuyerName: fullName,
        carOwnerCertificateNumber,
        carOwnerCertificateType,
        carOwnerMobile,
        carOwnerName,
        enterpriseName,
        enterpriseCode,
        // paymentMethod
      }
      const { data } = await updateNgaOrderInfo({
        orderId: this.$route.query.orderId,
        ...param
      })

      if (data.code === '00') {
        Toast({
          message: '更换成功',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800,
          onClose: () => this.$router.go(-1)
        })
      } else {
        Toast({
          message: data?.message || '更新失败',
          className: 'toast-dark-mini toast-pos-middle',
          forbidClick: true,
          duration: 800
        })
      }
      this.confirmVisible = false
    },
    filterInput(val) {
      return val.replace(/[^-_·a-zA-Z0-9\u4e00-\u9fa5\u439a]/, '').replace(/\s+/g, '')
    },
    formFormatterName(name, value) {
      const strToUpperCase = (string) => string.replace(/[a-z]/g, (s) => s.toUpperCase())
      this.submitParam[name] = strToUpperCase(value).replace(/\s+/g, '')
    },
    formFormatterEnterpriseName(name, value) {
      this.submitParam[name] = value.replace(/\s*/g, '').replace(/\(/g, '（').replace(/\)/g, '）').replace(/\s+/g, '')
    },
    formValidatorName: (name) => /^(?!·)(?:[\u4e00-\u9fa5·\-A-Z]{1,50})([\u4e00-\u9fa5\-A-Z])$/.test(name),
    formValidatorMobile(mobile, { name }) {
      const { submitParam } = this
      const validator = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(mobile)
      return name === 'carOwnerMobile' ? validator && submitParam.mobile !== mobile : validator
    },
    // eslint-disable-next-line no-nested-ternary
    formValidatorIDCode: (code, { type }) => {
      if (type === '1') {
        const [result, msg] = validateIdCard(code)
        return result
      }
      return /^.+$/.test(code)
    },
    enterpriseName: (name) => /^(?:[\u4e00-\u9fa5()（）\-a-zA-Z]{5,80})$/.test(name),
    enterpriseCode: (code) => checkSocialCreditCode(code),
    toDealerList() {
      const { orderId } = this
      const { isReserveCard, unalterable: { dealerName }, carLicenseCityName } = this.submitParam || ''
      if (orderId || isReserveCard || dealerName) {
        if (dealerName) {
          // Toast({
          //   className: 'toast-dark-mini toast-pos-middle',
          //   message: '如需变更代理商请通过APP重新选择配置进入',
          //   forbidClick: true
          // })
          return
        }
        return
      }

      // 埋点
      this.$sensors.track('changeAgent', {
        page_name: '完善购买信息页面'
      })

      const orderInfo = this.handleUnOrderInfo(false)
      store.dispatch('SetOrder', orderInfo)
      const city = carLicenseCityName.split('/')[1] || ''
      this.$router.push({
        name: 'dealer-list',
        query: { city }
      })
    },
    handleUnOrderInfo(saveDealerInfo = true) {
      const { orderId } = this
      const {
        certificateTypeVal,
        certificateCode,
        buyType,
        fullName,
        carOwnerCertificateNumber,
        carOwnerCertificateType,
        carOwnerMobile,
        carOwnerName,
        enterpriseName,
        enterpriseCode,
        // paymentMethod: payMethod
      } = this.submitParam

      const {
        carBuyerInfo, dealerInfo: { dealerCode }, itemList
      } = this.unOrderInfo || ''

      const { state: { dealerInfo } } = store
      const itemListData = [{ ...itemList[0], ...(!orderId && saveDealerInfo && dealerInfo?.dealerCode && dealerCode && dealerInfo.dealerCode !== dealerCode ? { dealerId: dealerInfo.dealerCode } : {}) }]
      const carBuyerInfoData = {
        ...carBuyerInfo,
        buyType,
        moreContact: `${certificateTypeVal},${certificateCode}`,
        fullName,
        ...(
          buyType === '01' ? {
            carOwnerName,
            carOwnerMobile,
            carOwnerCertificateType,
            carOwnerCertificateNumber
          } : {
            enterpriseName,
            enterpriseCode
          }
        )
      }

      const orderInfo = {
        ...this.unOrderInfo,
        ...{ carBuyerInfo: carBuyerInfoData },
        ...{ itemList: itemListData },
        // ...{ extInfo: { paymentMethod: payMethod } },
        ...(!orderId && saveDealerInfo && dealerInfo?.dealerCode && dealerCode && dealerInfo.dealerCode !== dealerCode ? { carLicenseCityName: `${dealerInfo.provinceName}/${dealerInfo.cityName}`, carLicenseCityCode: dealerInfo.cityCode, dealerInfo } : {})
      }

      if (buyType === '01') {
        delete orderInfo.carBuyerInfo.enterpriseName
        delete orderInfo.carBuyerInfo.enterpriseCode
      } else {
        delete orderInfo.carBuyerInfo.carOwnerName
        delete orderInfo.carBuyerInfo.carOwnerMobile
        delete orderInfo.carBuyerInfo.carOwnerCertificateType
        delete orderInfo.carBuyerInfo.carOwnerCertificateNumber
      }

      return orderInfo
    }
  }

}
</script>

<style lang="less" scoped>
  // @import url("../../assets/style/cell.less");
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/dialog.less");
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/animation.less");
  @import url("../../assets/style/nor.less");

  .btn-wrapper {
    position: fixed;
    z-index: 99;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 5px 16px 30px;
    box-sizing: border-box;
    background: #fff;
    box-shadow: 0px -1px 16px 0px rgba(0,0,0,0.08);
  }

  .comfirm-wrapper {
    text-align: center;
    width: 80vw;
    padding: 20px;
  }

  .buyMess {
    margin-top: 20px;
    padding-bottom: 100px;

    h3 {
      height: 48px;
      line-height: 48px;
      font-size: 16px;
      font-family: "Audi-WideBold";
      font-weight: 500;
      color: #1a1a1a;
      margin: 0;
    }

    .paymentDetailMoney {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 46px;
      line-height: 46px;
      font-size: 14px;
      font-weight: 400;
      color: #000;
      border-bottom: 1px solid #e5e5e5;

      span {
        font-size: 14px;
        font-weight: 400;
        color: #000;

        &:first-child {
          font-family: "Audi-ExtendedBold";
        }

        &:last-child {
          font-weight: bold;
          font-family: "Audi-WideBold";
        }
      }
    }

    img {
      margin-top: 16px;
      width: 100%;
      display: block;
    }
  }

  .buyMess {
    /deep/.van-form {
      & > .box-field {
        display: flex;
        flex-direction: column;
        position: relative;
        height: 72px;

        .box-label {
          width: 100%;
          font-size: 12px;
          color: #646566;
        }

        .van-cell {
          position: relative;
          height: 100%;

          .van-cell__value {
            .van-field__body {
              min-height: calc(100% - 25px);
              border-bottom: 1px solid #000;
              font-size: 16px;
              overflow: visible;
              flex-direction: column;
              justify-content: flex-end;
              align-items: flex-start;

              input {
                margin-bottom: 6px;
                font-size: 14px;
                line-height: 20px;
                height: 20px;
              }

              textarea {
                margin-top: 16px;
                line-height: 16px;
                // padding-top: 25px;
                min-height: 16px;
              }
            }
          }
        }

        // .van-field--error {
        //   .van-field__body {
        //     border-bottom: 1px solid #9e1f32 !important;
        //   }

        //   .van-field__error-message {
        //     color: #9e1f32;
        //   }
        // }
      }

      .jt {
        position: relative;

        i {
          position: absolute;
          right: 0;
          top: 25px;
        }

        input {
          margin-top: 6px !important;
        }
      }

      .check {
        height: 32px;
        min-height: 0;
        margin-bottom: 26px;

        span {
          color: #000;
          font-size: 14px;
        }

        .van-field {
          position: relative;
          width: 100%;
          border-bottom: 1px solid #000;

          .van-cell__title {
            font-size: 14px;
            line-height: 14px;
            min-width: 200px;
            margin: 0;
          }

          .van-field__value {
            position: absolute;
            right: 0;
            top: -4px;
            width: 16px;
            height: 16px;

            .van-field__control {
              width: 100%;
              height: 100%;

              .van-checkbox {
                width: 16px;
                height: 16px;

                .van-checkbox__icon--square {
                  width: 100%;
                  height: 100%;

                  i {
                    position: relative;
                    width: 16px;
                    height: 16px;

                    &::before {
                      position: absolute;
                      top: -2px;
                      left: -1px;
                    }
                  }
                }
              }
            }

            .van-field__body {
              border: 0;
            }
          }
        }
      }

      .ns {
        min-height: 72px;
        height: auto;

        .van-cell {
          .van-field__body {
            margin-bottom: 16px;
          }

          textarea {
            height: 16px;
            // margin-top: 26px;
            margin-bottom: 4px;
            font-size: 14px;
            padding-top: 10px;
            box-sizing: border-box;
            caret-color: #000;
          }
        }

        .van-field--error {
          .van-field__body {
            margin-bottom: 0;
          }
        }

        .noAniName {
          top: 23px;
        }
      }
    }
  }

  // /deep/.van-field__error-message {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   height: 16px;

  //   &::before {
  //     content: "";
  //     display: inline-block;
  //     width: 14px;
  //     height: 14px;
  //     background: url("../../assets/img/error.png") no-repeat 0 0;
  //     background-size: 14px 14px;
  //     margin-right: 4px;
  //   }
  // }

  // /deep/.van-checkbox__icon--checked {
  //   .van-icon {
  //     background: #fff;
  //     color: #666;
  //     border-color: #666;
  //   }
  // }

  /deep/.van-dialog {
    overflow-y: auto;
    max-height: 80%;
    padding: 15px 16px 16px;
    top: 52% !important;
    z-index: 33336;

    h3 {
      margin: 0;
    }

    .item {
      color: #000;
      font-size: 14px;
      text-align: left;
      margin-bottom: 24px;

      .title {
        line-height: 24px;
      }

      .itemCotent {
        display: flex;
        line-height: 17px;

        div {
          margin-top: 8px;
        }
      }
    }
  }

  // /deep/.van-popup {
  //   border-radius: 0;
  // }

</style>
<style lang="less">
// .view-wrapper {
//   height: calc(100vh - 60px) !important;
// }
.order-info-forms-box {
  margin-bottom: 100px;
  .van-field__value{
    overflow: unset !important;
  }
  .van-cell-group {
      border-top: 8px solid #f2f2f2;
      // padding-bottom: 24px;
      &:first-child {
        border-top-width: 0;
      }
      &.van-hairline--top-bottom {
        &::after {
          display: none;
        }
      }
      .van-hairline--bottom:after, .van-cell::after {
        border-color: #e5e5e5;
      }
      .cell-title {
        box-sizing: content-box;
        margin: 0 16px;
        padding: 16px 0;
        .h2 {
          margin: 0;
          font-size: 16px;
          line-height: 24px;
          font-family: AudiTypeGB-WideBold, AudiTypeGB;
          &.car-owner {
            margin-top: 26px;
          }
          &.mtb {
            margin-top: 8px;
          }
        }
        .sub {
          margin: 0;
          font-size: 10px;
          line-height: 20px;
          color: rgba(#000, .4);
        }
      }
      .van-cell {
        padding: 20px 16px 16px;
        min-height: 24px;
        font-size: 16px;
        &.van-field--error {
          padding-bottom: 36px;
          &::after {
            bottom: 20px;
          }
        }
        .van-cell__title {
          flex:none;
          margin-right: 20px;
          color: #000;
        }
        .van-field__control {
          height: 24px;
          line-height: 24px;
          &:disabled {
            color: #000;
            -webkit-text-fill-color:#000
          }
        }
        .van-radio-group {
          .van-radio {
            margin-right: 24px;
          }
        }
        &:last-child::after {
          display: block;
        }
        &.van-field--error {
          .van-field__control {
            color: #333;
            &::placeholder {
              color: #ccc;
            }
          }
          .van-field__error-message {
            z-index: 99;
            position: absolute;
            bottom: -38px;
            margin-left: -90px;
            color: #EB0D3F;
            &::before {
              content: "";
              position: relative;
              display: inline-block;
              width: 14px;
              height: 14px;
              background: url("~@/assets/img/error-icon.png") no-repeat 0 0;
              background-size: 14px 14px;
              margin-right: 4px;
              top: 2px;
            }
          }
          &::after { border-color: #EB0D3F;}
        }
        &.lan-cell-switch {
          .van-cell__value {
            overflow: visible;
          }
          .lan-switch {
            position: absolute;
            top: -3px;
            right: 0;
            .van-switch__node {
              width: .86em;
              height: .86em;
              margin: .07em;
            }
            &::before {
              content: '';
              position: absolute;
              right: 11px;
              top: 50%;
              width: 4px;
              height: 4px;
              border: solid 2px #8b8b8b;
              border-radius: 50%;
              transform: translateY(-50%);
            }
            &.van-switch--on {
              &::before {
                right: auto;
                left: 15px;
                width: 1px;
                height: 8px;
                border: none;
                border-radius: 0;
                border-left: solid 2px #fff;
              }
            }
          }
        }

      }
    }

}
</style>
