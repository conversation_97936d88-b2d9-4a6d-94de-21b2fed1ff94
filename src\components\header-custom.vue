<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-04-22 14:24:02
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2023-06-02 00:31:28
 * @FilePath     : \src\components\header-custom.vue
 * @Descripttion : 自定义 header
 * @readme       : 目前组件可设置为 fixed 效果，需要在组件腹肌添加以下 class 名
                   1.magic-head-fixed 开启效果(非fixed实现)
                   2. fixed
-->
<template>
  <div
    v-if="env !== 'minip'"
    :class="[
      'header-wrapper',
      headerFixed.enable && headerFixed.effect === 'fixed' ? 'fixed' : ''
    ]"
  >
    <div
      data-flex="main:justify cross:center"
      :class="['header-custom', border ? 'van-hairline--bottom' : '']"
      :style="{
        height: navigationBarHeight + 'px',
        'background-color': backgroundColor,
        'border-width': statusBarHeight + 'px',
        'border-color': statusBarColor
      }"
    >
      <div class="header-box header-left">
        <slot name="left">
          <div
            :class="['back btn-icon', headerLeftIconColor]"
            @click="handleLeftBackBtn"
          />
        </slot>
      </div>
      <div class="header-box header-middle text-one-hid">
        <slot name="middle">
          {{ $store.state.title }}
        </slot>
      </div>
      <div class="header-box header-right">
        <slot name="right" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import {
  callNative,
  delay,
  backFromCrossDomain, backFromUseDomain
} from '@/utils/index'


export default {
  name: 'HeaderCustom',
  props: {
    headerLeftIconColor: {
      type: String,
      default: ''
    },
    backRefresh: {
      type: Boolean,
      default: false
    },
    historyLength: {
      type: Number,
      default: 1
    },
    statusBarColor: {
      type: String,
      default: 'transparent'
    },
    backgroundColor: {
      type: String,
      default: '#fff'
    },
    border: {
      type: Boolean,
      default: false
    },
    headerFixed: {
      type: Object,
      default: () => {}
    },
    headerOutHeight: {
      type: Number,
      default: 0
    },
    // native
    listeningNativeBack: {
      type: String,
      default: ''
    },
    listeningEmitBack: {
      type: Boolean,
      default: false
    },
    destroyNativeBack: {
      type: Boolean,
      default: false
    },
    // backNativeGoto: {
    //   type: Object,
    //   default: () => ({
    //     type: '', //  => h5, native 页面跳转类型
    //     goto: '', // goto =>路由名称 （h5, native）依据不同类型对应路由名称
    //     params: {} // 参数
    //   })
    // },
    setDeviceInfo: {
      type: Object,
      default: () => ({
        // statusBarTextColor: '#000',
        // statusBarBgColor: 'transparent'
      })
    }
  },
  data() {
    return {
      nativeApp: false,
      statusBarHeight: 0,
      navigationBarHeight: 44
    }
  },
  computed: {
    ...mapState({
      env: (state) => state.env,
      isShowHeader: (state) => state.isShowHeader
    })
  },
  watch: {
    async destroyNativeBack(destroy) {
      const { nativeApp, $route } = this
      if (destroy && nativeApp) {
        const backtrack = await callNative('listeningNativeBacktrack', { from: 'header-back', page: $route.name, action: 'destroy' })
        console.log('action: destroy', ['backtrack'], backtrack)
      }
    },
    async listeningNativeBack(action) {
      const { nativeApp, $route } = this
      if (action && nativeApp) {
        const backtrack = await callNative('listeningNativeBacktrack', { from: 'header-back', page: $route.name, action })
        console.log('action: destroy', ['backtrack'], backtrack)
      }
    }
    // setDeviceInfo: {
    //   handler(newValue) {
    //     newValue && this.handleSetDeviceInfo(newValue)
    //   },
    //   deep: true
    // }
  },
  created() {
    this.handleComputeHeader()
    // 注册事件
    this.listeningNativeBack && this.handleListeningNativeBack()
    // this.handleSetDeviceInfo()
  },
  methods: {
    ...mapMutations(['setHeaderVisible']),
    ...mapGetters(['getDevice']),
    async handleSetDeviceInfo(info) {
      const { nativeApp, setDeviceInfo } = this
      if (nativeApp && (info || setDeviceInfo)) {
        const statusBarData = info || setDeviceInfo
        const data = await callNative('setDeviceInfo', statusBarData) || {}

        console.log('%c [ nativeApp setDeviceInfo ]-145', 'font-size:13px; background:pink; color:#bf2c9f;', data, statusBarData)
      }
    },
    handleComputeHeader() {
      const { env } = this
      const { statusBarHeight, navigationBarHeight, nativeApp } = this.getDevice() || ''
      let headerOutHeight = this.navigationBarHeight
      if (nativeApp) {
        this.statusBarHeight = statusBarHeight
        this.navigationBarHeight = navigationBarHeight
        this.nativeApp = nativeApp
        headerOutHeight = statusBarHeight + navigationBarHeight
      }
      // 默认小程序环境下影藏 header
      if (env === 'minip') {
        headerOutHeight = 0
      }
      this.setHeaderVisible(false)
      // 同步父级响应数据
      this.$emit('update:headerOutHeight', headerOutHeight)
      console.log('[header-custom] fixed', this.headerFixed.enable, this.headerFixed.effect)
    },
    listeningBacktrack() {
      // const { data: { from, action } } = JSON.parse(data) || {}
      // if (from === 'header-back' && action === 'stop') {
      this.handleLeftBackBtn()
      // }
      console.log('listeningBacktrack:', 999999)
    },
    // 注册 Native 监听手势(返回)等操作
    async handleListeningNativeBack() {
      const {
        $route, nativeApp, listeningNativeBack
      } = this
      const action = listeningNativeBack || 'stop'
      if (nativeApp) {
        if (action === 'stop' && !window.listeningBacktrack) {
          // 原生调用Vue的方法，需要把方法挂在Window下面
          window.listeningBacktrack = this.listeningBacktrack
        }
        const backtrack = await callNative('listeningNativeBacktrack', { from: 'header-back', page: $route.name, action })
        console.log('%c [ action, backtrack ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', action, backtrack)
        console.log('9999', 99999)
      }
    },
    async handleLeftBackBtn(data = null) {
      const {
        historyLength, $router, $route, nativeApp, listeningEmitBack, backRefresh, listeningNativeBack
      } = this

      if ((listeningNativeBack && listeningNativeBack === 'stop') || listeningEmitBack) {
        console.log('emit handleLeftBack')
        return this.$emit('handleLeftBack', data, 8989898)
      }

      const { query } = $route
      if (query?.from) {
        const {
          from, path, param, name, sign
        } = query
        if (from && name) {
          const back = backFromUseDomain({
            from, path, param, name, sign
          })
          if (back && sign !== 'native' && Object.keys(back).length) {
            const { params } = back || {}
            if (from === 'order-list') {
              this.$store.commit('setPageFrom', from)
            }
            this.$router.push({
              name, query: params
            })
          }
          return
        }
        if (from && path) {
          const { nativeApp } = this.getDevice() || {}
          backFromCrossDomain(from, path, param, nativeApp ? 'native' : '')
          return
        }
      }

      console.log('%c [ window.history.length <= historyLength ]-218', 'font-size:14px; background:#cf222e; color:#fff;', window.history, historyLength)
      if (window.history.length <= historyLength) {
        if (nativeApp) {
          const backtrack = await callNative('prepage', {
            from: 'header-back', page: $route.name, times: 1
          })
          console.log('%c [ native backtrack ]-213', 'font-size:13px; background:pink; color:#bf2c9f;', backtrack)
        }
      } else {
        if (backRefresh) {
          $router.go(-1)
        } else {
          $router.back()
        }
        console.log('%c [ router.back: ]-223', 'font-size:13px; background:pink; color:#bf2c9f;', $route.name, 'backRefresh', backRefresh)
      }
    }
  },
  destroyed() {}
}
</script>

<style lang="less">
.header-wrapper {
  &.fixed {
    position: fixed;
    z-index: 2000;
    left: 0;
    right: 0;
    top: 0;
  }

  .header-custom {
    padding: 0 16px;
    text-align: center;
    border-top: 0px solid transparent;
    border-color: var(--header-custom-statusBarColor);
    // background: #fff;
    box-sizing: content-box;
  }

  .header-box {
    width: 16%;
    position: relative;

    &.header-middle {
      width: 58%;
      font-weight: 600;
    }

    &.header-left {
      .btn-icon {
        margin-left: -8px;
      }

      text-align: left;
    }

    &.header-right {
      text-align: right;
    }

    .btn-icon {
      width: 20px;
      height: 20px;

      &.back {
        background: url("../assets/img/icon03.png") center/contain no-repeat;
        &.white {
          background: url("../assets/img/icon03_white.png") center/contain no-repeat;
        }
      }

      &.close {
        left: 40px;
        background: url("../assets/img/icon05.png") center/contain no-repeat;
      }
    }
  }
.header-left {}
}
</style>
