<template>
  <div
    name="navigation"
    class="navigation"
    style="z-index: 2000;"
  >
    <div
      class="nav-box"
      :class="[ background , background === 'white'&&isBorder ?'border-bottom' : '']"
      :style="{ 'padding-top': statusBarHeight + 'px', 'height':statusBarHeight + navigationBarHeight+'px'}"
    >
      <div
        class="nav-content"
        :style="{'height': + navigationBarHeight+'px'}"
      >
        <div
          @click="routeBack"
          class="nav-back-box"
        >
          <img
            class="nav-back"
            v-if="background === 'white'"
            style="width: 24px;height: 24px;"
            src="../assets/img/back_black1.png"
          >
          <img
            class="nav-back"
            v-else
            src="../assets/img/back_white.png"
          >
        </div>
        <div class="nav-title" 
             :style="{ 'font-size': titleSize + 'px','fontFamily':isFamily }"
        >
          {{ title || $store.state.title }}
        </div>
        <div class="nav_right">
          <slot />
        </div>
      </div>
    </div>
    <div
      v-if="background!=='transparent' "
      :style="{ 'height': statusBarHeight + navigationBarHeight + 'px' }"
      style="width: 100%;"
    />
  </div>
</template>

<script>
import { callNative } from '@/utils/index'

export default {
  data() {
    return {
      statusBarHeight: 0,
      navigationBarHeight: 50
    }
  },
  props: {
    title: { // 标题
      type: String,
      default: ''
    },
    backType: {
      type: String,
      default: 'h5'
    },
    background: {
      type: String,
      default: 'white'
    },
    custom: {//是否自定义回退事件
      type: Boolean,
      default: false
    },
    titleSize :{
      type: String,
      default: '18'
    },
    isBorder:{
      type: Boolean,
      default: true
    },
    isFamily: {
      type: String,
      default: ''
    }
  },
  created() {
    this.getHeight()
  },
  methods: {
    // 获取导航栏高度??
    async getHeight() {
      const data = await callNative('navigationBarHeight', {})
      this.statusBarHeight = data.statusBarHeight
      this.navigationBarHeight = data.navigationBarHeight
    },

    // 点击返回键
    async appGoBack() {
      const data = await callNative('prepage', { times: 1 })
    },
    routeBack() {
      console.log('111', this.backType)
      if(this.custom){
        this.$emit('onBack', true)
      }else{
        this.appGoBack()
      }
      // if (this.backType === 'h5') { // 内部回退
      //   this.$router.go(-1)
      // }
      // if (this.backType === 'app') { // 回退到APP
      //   this.appGoBack()
      // }
    }
  }
}
</script>

<style lang='less' scoped>
   div {
    box-sizing: border-box;
  }

  .border-bottom {
    border-bottom: 1px solid #e5e5e5;
  }

  .transparent {
    background-color: transparent;
    color: white;
  }
  getShareReport
  .white {
    background-color: #FFFFFF;
    color: black;
  }

  .black {
    background-color: black;
    color: white;
  }

  .nav-box {
    position: fixed;
    top: 0;
    display: flex;
    width: 100%;
    text-align: center;
    z-index: 2000;

    .nav-content {
      display: flex;
      align-items: center;
      position: relative;
      width: 100vw;
    }

    .nav-back-box {
      width: 10vw;
      position: absolute;
      left: 0;
      bottom: 0;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1025;
    }
    .nav-back {
      width: 20px;
      height: 20px;
      margin-left: 10px;
    }


    .nav-title {
      font-size: 18px;
      font-weight: 500;
      line-height: 20px;
      width: 100vw;
      position: absolute;
      bottom: 0;
      overflow: hidden;
      text-align: center;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav_right {
      width: 40vw;
      text-align: right;
      position: absolute;
      right: 0;
      line-height: 20px;
      bottom: 0;
      padding-right: 15px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
</style>
