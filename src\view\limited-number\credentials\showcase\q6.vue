<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-09-29 17:11:53
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2022-10-26 19:08:37
 * @FilePath     : \src\view\limited-number\credentials\showcase\q6.vue
 * @Descripttion :
-->
<template>
  <div
    class="limit-number-box q6"
  >
    <div
      class="logo-box"
      data-flex="main:justify cross:center"
    >
      <img
        src="@/assets/img/audi-white-logo.png"
      >
      <h2 class="h2">
        <img
          src="@/assets/img/audi-white-font-logo.png"
        >
        <!-- 上汽奥迪 -->
      </h2>
    </div>
    <div class="title-box">
      恭喜您成为<br><span class="en">{{ modelShortName }}</span><span class="numbers">{{ numbers }}</span>号车主
    </div>
    <div class="mission">
      独一无二的限定<span class="en">Lucky Number</span> 幸运号<br>
      篆刻传承千年的祥瑞祝福<br>
      伴您在硬悍向前的路上<br>
      邂逅“麒”遇  焕发“麟”感<br>
      让每段旅程<br>
      自在新生<br>
    </div>
    <div class="card-pic" />
    <div class="card-info">
      <h4 class="h4">
        上汽奥迪 <font style="font-size: 21px">
          x
        </font> 携程旅行
      </h4>
      Audi Q6 edition one<br>
      <span class="name">专属限定麒麟卡</span>
    </div>
    <!-- <div class="share-btn" /> -->
  </div>
</template>
<script>
export default {
  props: {
    numbers: {
      type: String,
      default: ''
    },
    modelShortName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="less" scoped>
  .limit-number-box {
    color: #fff;
    text-align: center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    // height: 100%;
    padding: 20px 30px;
    font-family: "Audi-ExtendedBold";
    box-sizing: border-box;
    overflow: hidden;
    &.q6 {
      background-image: url("../../../../assets/limitNumber/q6-bg.jpg");
      .logo-box {
        img {
          height: 30px;
          width: auto;
        }
        .h2 {
          font-size: 16px;
          img {
            height: 18px;
            width: auto;
          }
        }
      }
      .title-box {
        margin-top: 50px;
        font-size: 18px;
        line-height: 160%;
        letter-spacing: 4px;
        .en {
          font-size: 20px;
          letter-spacing: 1px;
        }
        .numbers {
          display: inline-block;
          padding: 0 6px;
          margin: 0 6px;
          border-bottom: 1px solid rgba(255, 255, 255, .9);
          font-size: 21px;
          line-height: 110%;
          vertical-align: text-bottom;
          color: #e50a22;
          text-shadow: 1px 1px 3px rgba(0, 0, 0, .5);
          letter-spacing: 1px;
          overflow: hidden;
        }
      }
      .mission {
        margin-top: 60px;
        line-height: 200%;
        font-family: 'Audi-Normal';
        font-weight: 400;
        font-size: 12px;
        letter-spacing: 6px;
        color: rgba(255, 255, 255, .8);
        .en {
          letter-spacing: 1px;
        }
      }
      .card-pic {
        margin: 40px 26px 0 -30px;
        height: 200px;
        background: url("../../../../assets/limitNumber/q6-card-pic.png") no-repeat 50%;
        background-size: cover;
      }
      .card-info {
        font-size: 13px;
        .h4 {
          font-size: 15px;
          letter-spacing: 4px;
          margin: 10px 0 10px;
        }
        .name {
          font-family: 'Audi-Normal';
          line-height: 220%;
        }
      }
      .share-btn {
        margin: 35px auto;
        width: 175px;
        height: 45px;
        background: url("../../../../assets/limitNumber/q6-share-btn.png") no-repeat 50%;
        background-size: contain;
      }
    }
  }
  @media screen and (max-height: 700px) {
    .limit-number-box {
      &.q6 {
        .title-box {
          margin-top: 30px;
        }
        .mission {
          margin-top: 20px;
        }
        .card-pic {
          margin-top: 0;
        }
        .share-btn {
          margin-top: 15px;
        }
      }
    }
  }
</style>
