<template>
  <div class="unsubscribeSucceed">
    <div>
      <img :src="context[type].img" />
    </div>

    <p v-html="`${context[type].title}`" />

    <div class="content">{{ context[type].tip }}</div>
    <div class="btnWarp">
      <div class="buttons" @click="onCertification">{{ btnName }}</div>
    </div>
  </div>
</template>

<script>
import { callNative } from '@/utils'

export default {
  data() {
    return {
      type: '0',
      context: [
        {
          title: '认证成功',
          tip: '恭喜您已经完成身份认证，完成车辆认证后可以即可使用相关功能。',
          img: require('../../assets/img/contract-success.png')
        },
        {
          title: '认证失败',
          tip: '您的身份认证失败，您可以尝试请重新上传或者联系客服。',
          img: require('../../assets/img/contract-fail2.png')
        }
      ],
      btnName: '',
      pageFromScene: ''
    }
  },
  mounted() {
    this.type = this.$route.query.type
    this.pageFromScene = this.$store.state.pageFromScene

    if (this.type === '0') {
      this.btnName = this.pageFromScene ? '前往车辆认证' : '完成'
    } else if (this.type === '1') {
      this.btnName = '重新认证'
    }
  },
  methods: {
    onCertification() {
      if (this.type === '0') {
        if (this.pageFromScene) {
          this.$router.replace({
            path: '/certification/my-certification',
            query: {}
          })
        } else {
          callNative('close', {})
        }
      } else if (this.type === '1') {
        this.$router.push({
          path: '/certification/identity-certification',
          query: {}
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
@import url('../../assets/style/scroll.less');
@import url('../../assets/style/buttons.less');

.unsubscribeSucceed {
  padding: 16px;
  text-align: center;
  img {
    margin-top: 110px;
    width: 62px;
    height: 62px;
  }

  p {
    text-align: center;
    font-size: 16px;
    color: #000000;
    line-height: 22px;
    margin-top: 25px;
  }
  .content {
    text-align: center;
    font-size: 16px;
    color: #666;
    line-height: 22px;
    margin-top: 16px;
  }
  .btnWarp {
    width: 100%;
    background-color: #ffffff;
    // display: flex;
    // flex-direction: column;
    // justify-content: space-between;
    position: fixed;
    bottom: 0;
    padding-bottom: 38px;
    left: 16px;
    .checkbox_button {
      margin-left: 16px;
      margin-bottom: 5px;
      width: 18px;
      height: 18px;
      color: #000;
    }
  }
}
</style>
