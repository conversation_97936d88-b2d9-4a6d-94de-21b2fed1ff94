<template>
  <div class="unsubscribeSucceed">
    <div class="circle">
     <img
        src="../../assets/img/contract-success.png"
      >
    </div>
    <p style="text-align: center;">{{ '预约试驾成功' }}</p>
  
      <div style="font-size: 14px;
            color: #000000;margin-top: 80px;">{{ '即将前往第三方“安吉汽车租赁”提供的页面' }}</div>


   <p style="font-size: 12px;
            color: #666;margin-top: 10px;" v-html="
        `1、第三方在提供服务的过程中向您做出的任何承诺、声明或行为仅适用于第三方与您之间的服务，不视为上汽奥迪的承诺、声明或行为。<br/>
	2、如您因未遵守第三方相关授权文件的规定或要求，造成您的任何损失，上汽奥迪不承担任何责任。<br/>`
      "
    />
    <div class="btn-height"></div>
    <div class="btnWarp">
      <div
        class="buttons"
        @click="onConfirm"
      >
        确定
      </div>
      <div class="bt" />
    </div>

  </div>
</template>

<script>
import Vue from 'vue'
import { mapState } from 'vuex'
import { Icon, Toast } from 'vant'
import {
  getVeryLongReservationUrl
} from "../../api/test-driver";
import model from '@/components/model.vue'
import storage from "../../utils/storage";

Vue.use(Icon)
Vue.use(Toast)
export default {
  components: {
    model
  },
  data() {
    return {
      payUrl:'',
      modalshow: false,
      appoId:'',
      
    }
  },
  mounted() {
    var model = storage.get("saveLongTestdrivePayUrl") || "{}";
    // this.payUrl = JSON.parse(model).payUrl;
    this.appoId = JSON.parse(model).appoId;
   
  },
 
  methods: {
    //https://dtd-h5-sit.saicmobility.com/h5/#/view/identityCard?preOrderNo=1508331230146990080
    async onConfirm() {
      this.$store.commit("showLoading");
      await getVeryLongReservationUrl({
        appoId: this.appoId,
        businessType:1,
        }).then((res) => {
          if (res.data.code === "200") {
            window.location.href = res.data.data
          }
        this.$store.commit("hideLoading");
      });
    },

  
  }
}
</script>

<style scoped lang="less">
    @import url("../../assets/style/scroll.less");
    @import url("../../assets/style/buttons.less");

    .unsubscribeSucceed {
        padding: 16px;

        .circle {
            position: relative;
            width: 72px;
            height: 72px;
            margin: 100px auto auto auto;

        }

        p {
            line-height: 24px;
            font-size: 18px;
            color: #000000;
            margin-top: 25px;
        }
        .tips{
          text-align: left;
          font-size: 14px;
          line-height: normal;
          margin: 80px auto;
          width: 60vw;
        }
	.btn-height {
	height: 90px;
	}
        .btnWarp {
            position: fixed;
            z-index: 2;
            height: 56px;
            width: 100%;
            bottom: 0;
            padding-bottom: 50px;
            background: #fff;

            .buttons2 {
                top: 60px;
            }
        }
    }
</style>
