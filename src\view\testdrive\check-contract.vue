<template>
  <div class="_container">
    <!-- <iframe class="prism-player" :src="urlPath"></iframe> -->
    <div id="app">
      <div id="demo"></div>
    </div>
    <div class="btn-wrapper">
      <AudiButton text="一键下载" color="black" height="56px" font-size="16px" @click="downloadConfigpdf" />
    </div>
  </div>
</template>

<script>
import { callNative } from '@/utils'
import { Toast } from 'vant'
import { BaseApiUrl } from '../../config/url.js'
import { getVeryLongReservationGeneratePDF } from '../../api/api.js'
import AudiButton from '@/components/audi-button'
import api from '@/config/url'
import dayjs from 'dayjs'
import Vue from 'vue'
import storage from "../../utils/storage";

import Pdfh5 from 'pdfh5'
import 'pdfh5/css/pdfh5.css'

Vue.use(Toast)
export default {
  components: { AudiButton },
  name: 'App',
  data() {
    return {
      detail: null,
      dayjs,
      urlPath: '',
      downloadPdfUrl: '',
      pdfh5: null,
      appoId: '',
    }
  },
  async mounted() {
    this.appoId = this.$route.query.appoId;
    var model = storage.get("saveLongItem") || "{}";
    this.downloadPdfUrl = JSON.parse(model).files[0];
    this.getPdfFile()
  },

  methods: {

    async getPdfFile() {
      callNative('toggleLoading', { show: '1' })
      const res = await getVeryLongReservationGeneratePDF({ appoId: this.appoId }) //:'218974782684254'
      this.pdfh5 = new Pdfh5("#demo", {
        pdfurl: window.URL.createObjectURL(new Blob([res.data], { type: 'application/pdf' }))
      });
      callNative('toggleLoading', { show: '0' })

    },

    // 一键下载(调用native)
    async downloadConfigpdf() {
      const fileName = `超长试驾${new Date().getTime()}.pdf`
      callNative('downloadFile', { url: this.downloadPdfUrl, fileName, type: 'pdf' }).then((res) => {
        Toast({
          type: 'success',
          message: '下载成功',
          icon: require('../../assets/img/success.png')
        })
      })
    },
  }
}
</script>

<style lang='less' scoped>
.btn-wrapper {
  position: fixed;
  bottom: 15px;
  width: 100%;
  padding: 0 15px;
  left: 0;
  box-sizing: border-box;
}

.border-bottom {
  border-bottom: 1px #E5E5E5 solid;
}

.margin-bottom {
  margin-bottom: 8px;
}

div p {
  box-sizing: border-box;
  font-family: "Audi-Normal";
}

p {
  margin: 0;
}

.font-WideBold {
  font-family: "Audi-WideBold";
}

._container {

  /* padding: 0 16px;
    padding-bottom: 120px;
    display: flex;
    flex-flow: column;
    font-size: 16px;
    color: #000; */
  .prism-player {
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
  }

  ._title {
    line-height: 52px;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
  }

  ._item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 33px;
  }
}
</style>
