<template>
  <div
    :class="
      ['limited-number-wrapper',
       'page-wrapper',
       headerFixed.enable && headerFixed.effect !== 'fixed' ? 'magic-head-fixed' : ''
      ]"
  >
    <header-custom
      ref="headerCustom"
      :border="true"
      :header-fixed="headerFixed"
      :header-out-height.sync="headerOutHeight"
      listening-emit-back
      @handleLeftBack="handleLeftBack"
    >
      <template
        #right
        v-if="modelLineCode === XIAN_XING_VERSION_Q5"
      >
        <p
          class="btn"
          @click="$router.push({ name: 'limited-number-rule-note', query: { seriesCode } })"
        >
          规则
        </p>
      </template>
    </header-custom>
    <div
      class="main-wrapper"
      :style="{
        'padding-top': `${!headerFixed.enable || headerFixed.effect !== 'fixed' ? 0 : headerOutHeight}px`
      }"
    >
      <div
        class="limitNumber"
        v-if="($route.query.seriesCode || carSeries.seriesCode) === '49'"
      >
        <div class="headimg">
          <div class="imgwarp">
            <img
              :src=" require(`@/assets/img/${ typeof numbers == 'string' ? numbers[0] : String(numbers)[0] }.png`) "
              alt=""
            > <img
              :src=" require(`@/assets/img/${ typeof numbers == 'string' ? numbers[1] : String(numbers)[1] }.png`) "
              alt=""
            > <img
              :src=" require(`@/assets/img/${ typeof numbers == 'string' ? numbers[2] : String(numbers)[2] }.png`) "
              alt=""
            >
            <img
              src="../../assets/img/xg.png"
              alt=""
            >
            <img
              src="../../assets/img/9.png"
              alt=""
            >
            <img
              src="../../assets/img/9.png"
              alt=""
            >
            <img
              src="../../assets/img/9.png"
              alt=""
            >
          </div>
        </div>
        <div
          class="head"
          :style="{
            marginTop: cipCampaign ? '7px' : '18px',
            marginBottom: cipCampaign ? '93px' : '60px',
          }"
        >
          <div
            class="headhead"
            id="limitNumber"
          >
            <p class="p1">
              青山远黛，星河梦蓝
            </p>
            <p class="p1">
              恭喜您获得专属限量号
            </p>
            <p class="p2">
              {{ numbers || "" }}
            </p>
            <p class="p1">
              与A7L一起领跑未来
            </p>
            <van-popover
              :close-on-click-action="false"
              :close-on-click-outside="false"
              get-container="#limitNumber"
              placement="top"
              v-model="showPopover"
            >
              <div
                v-if="numbers.includes('4')"
                class="p3"
                @click="replaceA"
                :style="{ padding: '20px' }"
              >
                可选择将“4”更换为“A”
                <van-icon name="arrow" />
              </div>
              <template #reference />
            </van-popover>
          </div>
          <div
            class="body"
            v-if="cipCampaign"
          >
            <img
              :src="cipCampaign"
              alt=""
            >
            <p class="p4">
              微信扫一扫添加您的专属顾问
            </p>
          </div>
        </div>
        <div class="carBox">
          <img
            class="posiCar"
            src="../../assets/img/car.png"
            alt=""
          >
        </div>
        <van-dialog
          v-model="isChange"
          overlay
          :show-cancel-button="true"
        >
          <div class="title">
            请选择您想要的限量号，确认后不可更改
          </div>
          <ul>
            <li
              v-for="(item, index) in numbers"
              :class="item == 4 ? 'kuang' : ''"
              :key="index"
              @click="changeA(index)"
            >
              {{ item }}
            </li>
          </ul>
          <div
            class="D-buttons"
            @click="confim"
          >
            确认
          </div>
          <div
            class="D-buttons2"
            @click="cancel"
          >
            取消
          </div>
        </van-dialog>
      </div>

      <div
        class="limitNumber2"
        v-else
      >
        <div class="_text1">
          上汽奥迪 {{ modelBadgeName }} 专属认证标徽
        </div>
        <div class="_img1">
          <img src="../../assets/img/icon-q5e1.png">
        </div>
        <div class="_title">
          尊敬的 {{ modelShortName }} <p>{{ numbers }}</p> 号车主
        </div>
        <div class="_text2">
          <p>欢迎您加入上汽奥迪大家庭</p>
          <p>让我们以科技重构艺术 以艺术再造生活</p>
          <p class="_text-bold">
            艺创未来
          </p>
        </div>
        <div style="display: flex;justify-content: center;">
          <div class="img_box">
            <div class="_number">
              <img
                v-for="(item,index) in numbers"
                :key="index"
                :src="require(`../../assets/limitNumber/number${item}.png`)"
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Dialog, Toast, Popover } from 'vant'
import { mapState } from 'vuex'
import { getHasLimitedNumber } from '@/api/api'
import HeaderCustom from '@/components/header-custom.vue'
import {
  XIAN_XING_VERSION_Q5
} from '@/config/constant'
import {
  LIMIT_NUMBERS
} from '@/config/conf.data'
import {
  getCipCampaign,
  putLimitedNumbers,
  limitedNumbers
} from '../../api/payment-success'
import api from '../../config/url'

Vue.use(Dialog).use(Popover).use(Toast)
const codeType = ['00', '200']
export default {
  components: {
    'header-custom': HeaderCustom
  },
  beforeRouteEnter(to, from, next) {
    if (['limit-number'].includes(to.name)) {
      next((vm) => {
        vm.$emit('set-bottom', true)
      })
    } else {
      next()
    }
  },
  beforeRouteLeave(to, form, next) {
    if (['money-detail'].includes(to.name)) {
      this.$emit('set-bottom', false)
      next()
    } else {
      next()
    }
  },
  data() {
    return {
      imgUrl: '../../assets/img/bgImg.png',
      BaseOssHost: api.BaseOssHost,
      cipCampaign: '',
      colorNameCn: '',
      numbers: '',
      modelBadgeName: '',
      modelShortName: '',
      isChange: false,
      indexList: [],
      data: {
        number: '000',
        carType: '',
        customerTel: null,
        id: '',
        numberDisplay: '000',
        orderNumber: null,
        reserved: false,
        reservedTel: null,
        stage: 1,
        status: 1
      },
      headerFixed: {
        enable: true,
        effect: 'fixed'
      },
      headerOutHeight: 0,
      XIAN_XING_VERSION_Q5,
      seriesCode: ''
    }
  },
  methods: {
    handleLeftBack() {
      const { orderId } = this.$route.query
      this.$router.push({ name: 'money-detail', query: { orderId } })
    },
    async getCipCampaign() {
      const seriesCode = this.$route.query.seriesCode || this.carSeries.seriesCode
      if (!this.carSeries.seriesCode) {
        this.carSeries.seriesCode = seriesCode
      }
      const param = {
        seriesCode
      }
      const { data } = await getCipCampaign(param)
      if (codeType.includes(data.code)) {
        this.cipCampaign = data.data.data
      }
    },

    replaceA() {
      this.isChange = true
      this.indexList = []
      const numbers = this.numbers
      numbers.split('').forEach((item, index) => {
        if (Number(item) === 4) {
          this.indexList.push(index)
        }
      })
    },
    changeA(index) {
      if (this.indexList.includes(index)) {
        const arr = this.numbers.split('')
        if (+this.numbers[index] === 4) {
          arr.splice(index, 1, 'A')
          let str = ''
          for (let i = 0; i < arr.length; i++) {
            str += arr[i]
          }
          this.numbers = str
        } else if (this.numbers[index] === 'A') {
          arr.splice(index, 1, '4')
          let str = ''
          for (let i = 0; i < arr.length; i++) {
            str += arr[i]
          }
          this.numbers = str
        }
      }
    },
    confim() {
      this.$store.commit('showLoading')
      this.putLimitedNumbers()
    },
    cancel() {
      this.isChange = false
      this.numbers = this.data.numberDisplay
    },
    async putLimitedNumbers() {
      const param = this.data
      param.numberDisplay = this.numbers
      this.$store.commit('hideLoading')
      const { data } = await putLimitedNumbers(param)

      if (codeType.includes(data.code)) {
        this.isChange = false
        if (this.numbers !== data.data.numberDisplay) {
          Toast({
            type: 'success',
            message: '更换成功',
            icon: require('../../assets/img/success.png')
          })
        }
        this.numbers = data.data.numberDisplay
        this.data = data.data
      } else {
        Toast({
          type: 'fail',
          message: '更新失败',
          icon: require('../../assets/img/error.png')
        })
      }
    },
    async limitedNumbers() {
      const { orderId, modelLineCode, seriesCode } = this.$route.query
      const { modelBadgeName, modelShortName } = LIMIT_NUMBERS.filter((i) => i.seriesCode === seriesCode)[0] || {
        modelBadgeName: '',
        modelShortName: ''
      }
      this.modelBadgeName = modelBadgeName
      this.modelShortName = modelShortName
      this.modelLineCode = modelLineCode
      this.seriesCode = seriesCode
      if (seriesCode === '49') {
        const { data } = await limitedNumbers({ orderId })
        if (codeType.includes(data.code)) {
          this.data = data.data
          this.numbers = this.data.numberDisplay
        } else {
          this.isNumber = false
        }
      } else {
        const { data } = await getHasLimitedNumber({ orderId }) // 查询q5e限量号
        if (codeType.includes(data.code)) {
          this.numbers = data.data
        }
      }
    }
  },
  computed: {
    ...mapState({
      carSeries: (state) => state.carSeries
    }),
    showPopover: {
      get() {
        return this.numbers.includes('4')
      },
      set(val) {
        return val
      }
    }
  },
  created() {
    this.getCipCampaign()
    this.limitedNumbers()
  },
  destroyed() {}
}
</script>

<style lang="less" scoped>
  @import url("../../assets/style/buttons.less");
  @import url("../../assets/style/scroll.less");
  @import url("../../assets/style/background.less");
  @import url("../../assets/style/dialog.less");

  .limitNumber {
    color: #fff;
    text-align: center;
    background-image: url("../../assets/img/bgImg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;

    .headimg {
      height: 227px;
    }

    .carBox {
      // position: absolute;
      // left: 0;
      // right: 0;
      // top: 360px;
      // margin-top: 20px;
      padding-bottom: 72px;

      .posiCar {
        width: 50%;
      }
    }

    .haveImg {
      top: 420px;
    }

    .head {
      padding-bottom: 22px;
      background: rgba(87, 87, 87, 0.32);
      margin: 0px 16px 0 16px;
      position: relative;
      z-index: 1000;

      h2 {
        font-size: 42px;
        color: #f50537;
        margin: 0;
        line-height: 62px;
      }

      .el {
        color: #ffffff;
        font-size: 14px;
        line-height: 36px;
      }

      .he {
        width: 36px;
        height: 4px;
        background: #f50537;
        margin: 28px auto;
      }

      p {
        margin: 0;
      }

      .p1 {
        font-size: 16px;
        line-height: 22px;
        color: #fff;
      }

      .p2 {
        color: #fff;
        font-size: 24px;
        line-height: 32px;
      }

      .px {
        padding-top: 24px;
        margin-bottom: 16px;
        font-size: 28px;
        font-family: "Audi-WideBold";
      }

      .p4 {
        color: #fff;
        font-size: 10px;
      }

      .headhead {
        letter-spacing: 3px;
        padding-top: 24px;
      }

      .body {
        margin-top: 24px;

        img {
          width: 80px;
          height: 80px;
          margin: 0 auto 16px;
        }
      }
    }

    /deep/.van-dialog__content {
      color: #000;

      ul {
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 60px;
        margin-bottom: 14px;

        li {
          font-size: 22px;
          width: 34px;
          height: 34px;
          line-height: 34px;
          font-family: "Audi-ExtendedBold";
        }

        .kuang {
          border: 1px solid;
          border-color: #000;
        }
      }

      .title {
        font-size: 18px;
        font-family: "Audi-ExtendedBold";
        margin-bottom: 14px;
      }

      .span {
        font-size: 12px;
        color: #fff;
        text-align: left;
        margin-left: 16px;
      }
    }

    /deep/.van-popup {
      width: 300px;
      left: 0 !important;
      right: 0 !important;
      top: -24px !important;
      margin: 0 auto !important;

      .van-popover__content {
        border-radius: 0 !important;
      }

      .p3 {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px 20px !important;
        background: #fff;
      }

      .van-popover__arrow {
        border-top-color: #fff;
        bottom: 1px;
      }
    }
  }

  .limitNumber2 {
    color: #fff;
    text-align: center;
    background-image: url("../../assets/img/q5e-back.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    height: 100%;
    overflow-x: hidden;

    ._text1 {
      font-size: 8px;
      margin-top: 13vh;
      font-family: "Audi-ExtendedBold";
      color: #333;
      letter-spacing: 0.5px;
      width: 100%;
      text-align: center;
    }

    ._img1 {
      width: 100%;
      text-align: center;
      margin-top: 20px;

      img {
        width: 30vw;
      }
    }

    ._text2 {
      font-size: 14px;
      font-family: "Audi-Normal";
      font-weight: 300;
      color: #616161;
      letter-spacing: 1px;
      width: 100%;
      text-align: center;

      ._text-bold{
        color: #464646;
        font-family: "Audi-WideBold";
      }
    }

    ._title {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      font-family: "Audi-ExtendedBold";
      letter-spacing: 0.5px;
      font-size: 14px;
      margin: 9vh 0;

      p {
        display: inline-block;
        margin: 0 5px;
        padding: 0 5px;
        border-bottom: 2px solid #333;
        background: linear-gradient(to bottom, #63390c, #ca8b38);
        -webkit-background-clip: text;
        color: transparent;
      }
    }

    .img_box {
      background-image: url("../../assets/img/icon-q5e2.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 210px;
      height: 81px;
      position: relative;
      margin-top: 35px;

      ._number {
        position: absolute;
        top: 48px;
        right: 72px;
        display: flex;
        align-items: center;

        img {
          width: 10px;
          height: 13px;
          object-fit: contain;
        }
      }
    }
  }
.page-wrapper , .main-wrapper{
  height: 100%;
  overflow: hidden;
}
</style>
