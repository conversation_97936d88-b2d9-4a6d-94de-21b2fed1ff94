<template>
  <div
    name="orderList"
    class="orderList"
  >
    <page-load
      @onRefresh="onRefresh"
      :pulldown.sync="pulldown"
      :pullupstate="false"
    >
      <div
        class="order_container"
        v-for="(item, index) in orderList"
        :key="index"
      >
        <div
          class="order_content"
          @click="toOrderDetail(item)"
        >
          <div class="order_content_left">
            <img  :src="item.imageUrl ? item.imageUrl:serviceDict[item.serviceType].img"  alt=''  />
          </div>
          <div class="order_content_right">
            <div class="order_content_right_top">
              <span class="order_name">{{ item.serviceName ? item.serviceName : serviceDict[item.serviceType].title}}</span>
              <span class="order_state">{{ item.statusDesc }}</span>
            </div>
            <div class="order_content_right_mid">
              {{ item.orderTime | formatDate }}
            </div>
          </div>
        </div>
      </div>
      <div   v-show="showNotData"   class="not_data" >
        <div>
          <img
            :src="fromType ? require('@/assets/img/wenjuanNotDataa.png')  :  require('@/assets/img/empty-new-img.png')"
            alt=""
          >
        </div>
        <p>暂无内容</p>
      </div>
    </page-load>
  </div>
</template>

<script>
import PageLoad from '@/components/page-load.vue'
import {
  postBindCardInfo,
  getInsaicServiceDetail,
  getInsaicServiceList
} from '@/api/api'
import { callNative } from '@/utils'
import url from '@/config/url'
import { formatTime } from '@/utils/date'

export default {
  name: 'OrderList',
  components: { PageLoad },
  filters: {
    formatDate(timestamp) {
      return formatTime(parseInt(timestamp))
    }
  },
  data() {
    return {
      fromType:this.$route.query?.fromType === 'fromPurple',
      pulldown: false, // 下拉
      showNotData: false,
      serviceDict: {
        "01": { title: "代驾服务", img: "https://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/after-insaic/valet-service.png" },
        "02": { title: "洗车服务", img: "https://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/after-insaic/car-washing-service.png" },
        "03": { title: "机场贵宾厅", img: "https://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/after-insaic/airport-vip-lounge.png" },
        "04": { title: "机场接送", img: "https://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/after-insaic/airport-transfer.png" },
        "05": { title: "租车服务", img: "ttps://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/after-insaic/car-rental-service.png" },
        "06": { title: "机场周边泊车", img: "https://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/after-insaic/airport-parking-around.png" },
        "07": { title: "一键加电", img: "https://sad-audi-cop-prod.oss-cn-shanghai.aliyuncs.com/after-insaic/airport-parking-around.png" }
      },
      orderList: [],
      BaseConfigrationOssHost: url.BaseConfigrationOssHost
    }
  },

  mounted() {
    this.getCarOrderList()
    // this.appCallJs()
  },
  watch: {},
  methods: {
    // APP调用刷新
    // appCallJs() {
    //   this.onRefresh()
    // },
    // 下拉刷新
    async onRefresh() {
      await this.getCarOrderList()
      this.pulldown = !this.pulldown
    },

    async getInsaicServiceData() {
      // 获取vin码遍历
      const { data } = await postBindCardInfo({})
      const emptyObj = []
      if (data.code === '200') {
        if (data.data.length > 0) {
          const apis = data.data.map((item) => getInsaicServiceList({
            vin: item.vin,
            serviceType: '',
            page: 1,
            size: 999
          }).then((data) => {
            if (data.data.data != null) {
                return data.data.data.orderList;
            }
            return emptyObj;
          }))
          const promise = Promise.all(apis).then((datas) => datas.flat())
          return promise
        }
        return new Promise((res) => {
          res(emptyObj)
        })
      }
      return new Promise((res) => {
        res(emptyObj)
      })
    },

    // 获取车订单
    async getCarOrderList() {
      this.$store.commit('showLoading')
      const datas = await this.getInsaicServiceData()

      //   const { data } = await getAfterSaleFindByPage()
      this.$store.commit('hideLoading')

      this.orderList = datas || []

      if (this.orderList.length > 0) {
        this.showNotData = false
      } else {
        this.showNotData = true
      }
    },

    async toOrderDetail(item) {
      // this.$router.push({
      //   path: '/aftersales/service-appointment-order-detail',
      //   query: {
      //     appoId: item.appoId
      //   }
      // })

      const { data } = await getInsaicServiceDetail({
        insaicOrderNo: item.insaicOrderNo,
        serviceType: item.serviceType
      })
      // 调用audiOpen打开
      const url = data.data
      // 跳转详情
      callNative('audiOpen', { path: url, showHeader: false })

      // const { origin, pathname } = window.location;
      // const url = `${origin}${pathname}#/aftersales/service-appointment-order-detail?appoId=${item.appoId}`;
      // // 跳转详情
      // callNative("audiOpen", { path: url });
    },
    setStatusDesc(item) {
      if (item.status === 1) {
        return '已提交'
      }
      if (item.status === 2) {
        return '已取消'
      }
      if (item.status === 3) {
        return '已完成'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.not_data {
  text-align: center;
  padding-top: 150px;
  img {
    width: 160px;
    height: 160px;
  }
  p{
    height: 20px;
    font-size: 13px;
    margin: 8px 0 0;
    font-weight: 400;
    color: #B3B3B3;
    line-height: 20px;
  }
}
.order_container {
  padding: 16px;
  font-family: "Audi-Normal";
  border-bottom: 1px solid #eee;
  .order_header {
    font-size: 16px;
    font-family: "Audi-WideBold";
    overflow: hidden;
    height: 44px;
    line-height: 44px;
    span {
      float: left;
    }
    img {
      float: right;
      width: 30px;
      height: 100%;
      object-fit: scale-down;
      margin-right: -10px;
    }
  }

  .order_content {
    display: flex;
    flex-direction: row;
    &_left {
      width: 122px;
      height: 122px;
      img {
          object-fit: cover;
          width: 100%;
          height: 100%;
      }
    }
    &_right {
      flex: 1;
      margin-left: 10px;
      margin-top: 5px;
      &_top {
        overflow: hidden;
        .order_name {
          font-family: "Audi-WideBold";
          float: left;
        }
        .order_state {
          float: right;
          font-size: 12px;
          color: #666666;
        }
      }

      &_mid {
        font-size: 12px;
        color: #666666;
        margin-top: 8px;
      }

      &_bottom {
          display: flex;
          flex-direction: row-reverse;
          margin-top: 38px;
      }
    }
  }
}
</style>
