<template>
  <div class="equipmentBox" v-show="equipmentVis">
    <div class="private-order" ref="private">
      <div class="private-order-main">
        <div v-for="item in privateOrderListTemp" :key="item.optionId" class="private-order-cardbox">
          <div>
            <div class="option-card">
              <div class="option-card-main">
                <div class="option-card-imgcard" @click="goOptionDetail(item)">
                  <img :src="$loadWebpImage(BaseConfigrationOssHost + item.imageUrl)" alt="">
                </div>
                <div class="option-card-info">
                  <div class="option-card-info-left">
                    <word-detail :title="item.optionName || ''" :fontsize="14"  :show="currentIndex === '4'" :id="'option_' + item.optionId" />
                    <div style="font-size: 12px;
                    font-weight: 400;
                    color: #333333;
                    line-height: 20px;
                    margin-top: 7px;">{{ item.price | finalFormatPrice }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import WordDetail from '@/components/word-detail.vue'
import optionDetail from '@/view/configration/optionDetailPlus.vue'

import { mapState } from 'vuex'
import url from '@/config/url'
import {
  checkV2
} from '@/utils'

export default {
  data() {
    return {
      visi: false,
      BaseConfigrationOssHost: url.BaseConfigrationOssHost,
      arrCode: [],
      privateOrderListTemp: [],
      equipmentVis: true,
      currentIndex: '',
    }
  },
  components: { WordDetail, optionDetail },
  mounted() {
    let  {
      selectCarInfo,
      currentExColor,
      currentModelHub,
      currentInteriorChair,
      currentInteriorEih,
      currentSibColorInterieur,
      currentV2popupItem,
      currentOptionsList,
      currentMeasureConfigCode
    } = this.$store.state
    let p = {
      selectCarInfo,
      currentExColor,
      currentModelHub,
      currentInteriorChair,
      currentInteriorEih,
      currentSibColorInterieur,
      currentV2popupItem,
      currentOptionsList,
      currentMeasureConfigCode
    }
    this.$storage.setPlus("cc_data", p)
    // debugger
    console.log(p);
    // debugger
    let code = this.$route.query.code
    if (code) {
      this.arrCode = code.split(',')
      let privateOrderList = this.$store.state.privateOrderList
//             privateOrderList = [{
//   "optionId": "5f634206-4ca3-49be-b8b4-1b34983df0f6",
//   "optionName": "黑色车内顶棚",
//   "optionCode": "6NQ",
//   "category": "HIM",
//   "optionType": "personal",
//   "optionTypeName": "个性化选装",
//   "imageUrl": "/ccpro-backend/a7l/option/oneapp/6NQ.png",
//   "imageUrlDetail": "/ccpro-backend/a7l/option/oneapp/6NQ_detail.png",
//   "imageUrlList": "/ccpro-backend/a7l/option/oneapp/6NQ_list.png",
//   "description": null,
//   "remark": null,
//   "defaultConfig": 0,
//   "status": 2,
//   "price": 0,
//   "detailPageHidden": null,
//   "optionDetailList": [],
//   "packetItems": null,
//   "optionRelates": null
// }]
      this.privateOrderListTemp = privateOrderList.filter(e => {
        return this.arrCode.includes(e.optionCode)
      })
      console.log("装备组合数据列表：", this.privateOrderListTemp);
    }
  },
  computed: {
    ...mapState([
      'env',
      'selectCarInfo',
      'currentExColor',
      'currentModelHub',
      'currentInteriorChair',
      'currentSibColorInterieur',
      'currentInteriorEih',
      'currentOptionsList',
      'currentPrice',
      'measureConfigCodeList',
      'currentMeasureConfigCode',
      'showV2Popup',
      'currentV2popupItem',
      'privateOrderList'
    ]),
  },  
  methods: {
    toBack() {
      this.equipmentVis = false
       this.$store.commit('setHeaderVisible', true)
      // this.$store.commit('setTitle',  this.$storage.getPlus('semi-definite') )
    },
    init(e, currentIndex) {
      this.currentIndex = currentIndex
      this.arrCode = e
      let privateOrderList = this.$store.state.privateOrderList
      this.privateOrderListTemp = privateOrderList.filter(e => {
        return this.arrCode.includes(e.optionCode)
      })
      console.log("装备组合数据列表：", this.privateOrderListTemp);
    },
    async goOptionDetail(option) {
      // if (option.optionName.indexOf('数字钥匙') !== -1) {
      //   return
      // }
      this.$store.commit('setClickOption', option)
      if (option.optionType === 'packet') {
        // await this.$store.dispatch('getPacketItemInfo')
      }
      // this.$emit('setTop')
      this.$router.push({
        path: '/optiondetailPlus'
      })
    },
  },
}
</script>

<style lang="less" scoped>
.equipmentBox {
  background: white;
  width: 100%;
  height: 100vh;
}

.private-order-cardbox {
  margin-bottom: 16px;
}

.private-order {
  height: calc(100vh - 190px);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  .private-order-main {
    padding: 16px;
  }

  .perwin {
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.3);

    .main {
      width: calc(100vw - 32px);
      background-color: #fff;
      padding: 16px;
      box-sizing: border-box;
      position: relative;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      .main_title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 16px;
      }

      .main_content {
        overflow-y: auto;
        max-height: calc(100vh - 360px);
        font-size: 14px;
        border-top: solid 1px #e5e5e5;
        border-bottom: solid 1px #e5e5e5;
        box-sizing: border-box;

        .main_item {
          border-bottom: solid 1px #e5e5e5;
          display: flex;
          align-items: center;
          padding: 16px 0;

          .img {
            display: block;
            width: 16vw;
            height: 16vw;
            object-fit: cover;
          }

          .name {
            flex: 1;
            margin-left: 10px;
          }

          .price {
            margin-left: 10px;
            font-size: 12px;
          }
        }
      }

      .main_ok {
        margin: 32px auto 0 auto;
        background-color: #000;
        color: #fff;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.option-card {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);

  .option-card-main {
    width: 100%;
    min-height: 190px;

    .option-card-imgcard {
      height: 130px;
      width: 100%;

      img {
        height: 130px;
        min-width: 100%;
        object-fit: cover;
      }
    }

    .option-card-info {
      padding: 8px 11px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .option-card-info-left {
        max-width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        >span {
          font-size: 12px;
          font-weight: 400;
          color: #333333;
          line-height: 20px;
          margin-top: 7px;
        }

        ._tips {
          color: #999999;
          font-size: 12px;
          display: flex;
          align-items: center;
          margin-top: 10px;

          p {
            color: #1a1a1a;
            font-weight: bold;
            margin: 0 3px;
          }
        }
      }

      .option-card-info-right {
        >img {
          width: 16px;
          height: 16px;
        }

        .uncheck {
          opacity: 0.7;
        }
      }
    }
  }
}

.ex-popup {
  width: calc(100vw - 32px);
  // height: 200px;
  background-color: #fff;
  padding: 32px;
  box-sizing: border-box;
  position: relative;

  main {
    font-size: 16px;
    color: #333333;
    line-height: 25px;
    text-align: center;
  }

  footer {
    width: 100%;
    height: 56px;
    background: #000000;
    border: 1px solid #000000;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    // position: absolute;
    margin-top: 32px;
  }
}
</style>
<style scoped lang="less">
@import '../../assets/style/common.less';

.sc-padding {
  padding: 8px 5px;
}

.header {
  text-align: center;
  // border-bottom: 1px solid #e5e5e5;
  background: #fff;

  .content {
    .c-font18;
    position: relative;
    margin: 0 8px;

    // 旧版后退icon样式
    // > .btn-back {
    //   .c-p-v-center;
    //   .c-flex-center;
    //   left: 0;
    //   z-index: 10100;
    // }
    > .header-btn {
      position: absolute;
      width: 24px;
      height: 24px;
      top: 50%;
      transform: translate(0, -50%);
      &.btn-back {
        background: url('../../assets/img/icon03.png') center/contain no-repeat;
      }
      &.btn-close {
        left: 40px;
        background: url('../../assets/img/icon05.png') center/contain no-repeat;
      }
    }

    > .layout-right {
      .c-p-v-center;
      right: 0;
      z-index: 10100;
    }

    .title {
      .c-font18;
      .c-wideBold;
      font-weight: 400;
      max-width: 85%;
      padding-left: 30px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // font-family: 'AudiTypeGB-Normal';
    }
  }
}
.bor {
  background: transparent;
  border-color: transparent;
}
/deep/.dialog {
  border-radius: 0;
  text-align: center;
  padding: 20px;
  .van-dialog__content {
    min-height: 0;
    .van-dialog__message {
      font-size: 16px;
      font-family: 'Audi-ExtendedBold';
      line-height: 36px;
      padding: 0;
    }
  }

  .van-dialog__footer {
    display: flex;
    flex-direction: column;
  }
  .van-button {
    width: 90%;
    height: 56px;
    left: 0;
    right: 0;
    margin: 8px auto 8px;
    background: #000;
    cursor: pointer;
    color: #fff;
    line-height: 56px;
    text-align: center;
  }
  .van-dialog__confirm {
    color: #000;
    background: #fff;
    border: 1px solid #000;
    box-sizing: border-box;
  }
}
</style>
