.van-toast {
    // width: 80% !important;
    // min-height: 0 !important;
    border-radius: 0 !important;
    // background: rgb(235, 235, 235) !important;
    // color: #000 !important;
    // // top: 15%;
    // flex-direction: row !important;
    // align-items: center;

    i {
        width: 16px;
        height: 16px;
        margin-right: 8px;

        &::before,
        img {
            font-size: 18px;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: auto;
        }
    }

    div {
        flex: 1;
        text-align: left;
        line-height: 20px;
        margin: 0 !important;
    }
}



.audi-loading-style {
	position: fixed;
    top: 50% !important;
    left: 50% !important;
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -webkit-flex-direction: column !important;
    flex-direction: column !important;
    -webkit-box-align: center !important;
    -webkit-align-items: center !important;
    align-items: center !important;
    -webkit-box-pack: center !important;
    -webkit-justify-content: center !important;
    justify-content: center !important;
    box-sizing: content-box !important;
    width: 88px !important;
    max-width: 70% !important;
    min-height: 84px !important;
    padding: 16px !important;
    color: #fff !important;
    font-size: 14px !important;
    line-height: 20px !important;
    white-space: pre-wrap !important;
    text-align: center !important;
    word-wrap: break-word !important;
    background-color: rgba(0,0,0,.7) !important;
    border-radius: 8px !important;
    -webkit-transform: translate3d(-50%,-50%,0) !important;
    transform: translate3d(-50%,-50%,0) !important;
    padding: 10px !important;

    .van-loading__spinner {
        width: 48px;
        height: 48px;
    }

    .van-toast__loading {
        padding: 0px;
        circle {
            stroke: #EB0D3F;
        }
    } 

    .van-toast__text {
        font-size: 12px;
        margin-top: 8px !important;
    }

}

//pruple toast样式
.audiPurple-loading-style{
    .van-toast__loading {
        background: transparent !important;
        .van-loading,.van-loading__spinner{
            background: transparent !important;
        }
        padding: 0px;
        circle:first-of-type {
            stroke: #606372 !important;
        }

        circle:last-of-type {
            stroke: #fff !important;
        }

    } 
    .van-toast__text{
        background: transparent !important;
    }
}