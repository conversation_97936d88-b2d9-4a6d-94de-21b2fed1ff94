export const formatTime = (time) => {
  const Dates = new Date(time)
  const Y = Dates.getFullYear()
  const M = Dates.getMonth() + 1
  const D = Dates.getDate()
  const H = Dates.getHours() >= 10 ? Dates.getHours() : (`0${Dates.getHours()}`)
  const m = Dates.getMinutes() >= 10 ? Dates.getMinutes() : (`0${Dates.getMinutes()}`)
  const S = Dates.getSeconds() >= 10 ? Dates.getSeconds() : (`0${Dates.getSeconds()}`)
  const times = `${Y + (M < 10 ? '-0' : '-') + M + (D < 10 ? '-0' : '-') + D} ${H}:${m}:${S}`
  return times
}
