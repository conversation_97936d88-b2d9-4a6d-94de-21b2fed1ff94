import Vue from 'vue'
import Vuex from 'vuex'
import { createVuexPersistedState } from 'vue-persistedstate'
import confiUrl from '@/config/url'
import storage from '@/utils/storage'
import { checkV2 } from '@/utils'
import actions from './actions'
import modules from './modules'

const baseOssHost = confiUrl.BaseOssHost

Vue.use(Vuex)

const store = new Vuex.Store({
  state: {
    loginCheck: '',
    isLogin: false,
    // optionsSelected: [],
    fromPath: '',
    allMeasureCCList: [],
    ccData: {},
    semiDefinite: storage.getPlus('semi-definite'),
    certificationType: 0,
    env: '',
    idx: 0,
    sourceId: '',
    wxLocation: '',
    nowTabIndex: '',
    showBottomButton: true,
    title: '',
    backFromOptionDetail: false, // 是否从装备详情页返回私人定制tab
    orderId: '',
    loading: true,
    loadingNospinner: false,
    readed: 0, // 1代表阅读过
    dadingFormData: null,
    iframeUrl: '',
    allowBackVal: '0',
    currentTabIndex: '0',
    allPrivateOrderList: [],
    deliveryPattern: null, // 交车方式数据
    //
    payment: '10', // 贷款方式, 10:全款, 20:贷款
    isShowHeader: true, // 是否显示header
    expectPayTime: '', // 预期交付时间
    dealerInfo: {}, // 经销商数据
    serviceProvider: null, // 服务商数据
    skuid: '',
    ccid: '',
    // dealerCode: '',
    isReserveCard: false,
    reserveCardInfo: null, // 创世卡信息
    bestRecommandDealerCode: '',
    cityOnlyHeader: true, // 城市下面是否只有总部
    outsideColorInfo: {}, // 外观颜色数据
    selectCarInfo: {
      modelLineCode: '',
      price: 0
    },
    ccConfigration: null,
    showOptionPopupPlus: {
      bool: false,
      code: ''
    },
    showOptionPopup: false,
    currentPrice: 0,
    invoiceInfo: null, // 商品电子发票信息
    ccInfo: null,
    packetItem: {}, // 装备包详情
    modelLineList: [],
    exteriorList: [], // 接口获取的外饰列表
    interiorList: [], // 接口获取的内饰列表
    currentExColor: {}, // 当前选中的外饰信息
    currentInColor: {}, // 当前选中的内饰信息
    modelHubList: [], // 接口获取的轮毂列表
    currentModelHub: {}, // 当前选中的轮毂信息
    finishRequestInterior: false,
    allIteriorChairList: [], // 当前配置线所有座椅列表
    allInteriorEihList: [], // 当前配置线所有饰条列表
    allSibColorInterieurList: [], // 当前配置线所有内饰面料列表
    interiorChairList: [], // 当前配置可选座椅列表
    interiorEihList: [], // 当前配置可选面料列表
    interiorSibList: [], // 当前配置可选饰条列表
    sibColorInterieurList: [], // 内饰+面料列表
    currentSibColorInterieur: {}, // 当前选中内饰+面料
    currentInteriorChair: {}, // 当前选中座椅
    currentInteriorEih: {}, // 当前选中饰条
    privateOrderList: [], // 私人订制列表
    currentOptionsList: [], // 当前选中的私人订制列表
    clickOption: {}, // 当前点击的option,供私人订制的装备详情页使用
    otherClickOption: {},
    packetOptionsInfoList: [],
    changeList: [],
    carSeries: {}, // 通过ccid解析的当前车辆车系详情
    carDetail: {}, // 通过ccid解析的当前车辆详情
    standardConfigData: [], // 车辆详情组合的标准配置列表
    modelScrollTop: 0, // 配置线页面在查看详情前滚动的距离
    hasV2Car: true, // 判断半定制化车是否还有库存
    showPerwin: false,
    measureConfigCodeList: [], // 变种车数据
    currentMeasureConfigCode: {},
    showV2Popup: false,
    currentV2popupItem: -1,

    dealerModel: {}, // 选择的服务商
    selectModel: {}, // 服务预约选择的车型
    selectServiceType: {}, // 服务类型
    selectServiceTime: {}, // 服务预约选择的时间
    selectServiceShop: [], // 选择的服务商品
    takeDatetime: {},
    sendDatetime: {},
    carSeriesList: [], // 车系列表
    isLoadedGDMap: false, // 是否加载完成高德地图

    takeAddress1: {}, // 保存取车地址信息
    sendAddress1: {}, // 保存送车地址信息

    bestRecommendCarList: [], // 限定列表
    appUserAddress: null, // 提交订单页的用户地址
    isInit: true, // 是否是第一次进入确认订单页面
    userLocation: '',
    inviteBuyCarCode: '',
    device: {}, // 设备信息,
    idCardPositive: {}, // 国资委身份证
    idCardReverse: {}, // 国资委身份证
    idCardPositiveRelatives: {}, // 国资委亲属身份证
    idCardReverseRelatives: {}, // 国资委亲属身份证
    fileListThree: [], // 国资委关联公司
    fileList: [], // 国资委单位证明
    fileListFour: [], // 国资委亲属材料
    oldtakeAddress: '', // 旧取车地址
    oldsendAddress: '', // 旧送车地址
    idCardPositiveElite: {}, // 进取精英身份证
    idCardReverseElite: {}, // 进取精英身份证
    idCardPositiveRelativesElite: {}, // 进取精英亲属身份证
    idCardReverseRelativesElite: {}, // 进取精英亲属身份证
    fileListThreeElite: [], // 进取精英关联公司
    fileListElite: [], // 进取精英单位证明
    fileListFourElite: [], // 进取精英亲属材料
    fileListSix: [],

    overseasidCardPositive: {}, // 留学生亲属身份证
    overseasidCardReverse: {}, // 留学生亲属身份证
    overseasfileList: [], // 留学生关联公司
    overseasfileListThree: [], // 留学生单位证明
    overseasFileListFour: [], // 留学生亲属材料

    initTimeout: false,
    pageFrom: '',
    pageFromScene: '', // 标记SC进去认证页面的场景
    pageSource: 'sa'
  },
  mutations: {
    setIsLogin(state, isLogin) {
      state.isLogin = isLogin
    },
    setLogin(state, param) {
      state.loginCheck = param
    },
    showShare(state, param) {
      state.isQuotation = param
    },
    // setOptionsSelected(state, param) {
    //   if (state.idx == 2) {
    //     const arr = [...state.optionsSelected, param]
    //     state.optionsSelected = Array.from(new Set(arr))
    //   }
    // },
    setFromPath(state, param) {
      state.fromPath = param
    },
    setCcData(state, param) {
      state = { ...state, ...param }
    },
    setSemiDefinite(state, param) {
      state.semiDefinite = storage.getPlus('semi-definite')
    },
    setCertificationType(state, param) {
      state.certificationType = param
    },
    setInviteBuyCarCode(state, param) {
      state.inviteBuyCarCode = param
    },
    setWxLocation(state, param) {
      state.wxLocation = param
    },
    setIdx(state, param) {
      state.idx = param
    },
    setSourceId(state, param) {
      state.sourceId = param
    },
    setUserLocation(state, param) {
      state.userLocation = param
    },
    updateNowTabIndex(state, param) {
      state.nowTabIndex = param
    },
    updateCityOnlyHeader(state, param) {
      state.cityOnlyHeader = param
    },
    updateBestRecommandDealerCode(state, param) {
      console.log('更新虎头车代理商', param)
      state.bestRecommandDealerCode = param
    },
    saveisInit(state, param) {
      state.isInit = param
    },
    saveappUserAddress(state, param) {
      state.appUserAddress = param
    },
    saveSendDAddress(state, param) {
      state.sendAddress1 = param
    },
    saveTakeAddress(state, param) {
      state.takeAddress1 = param
    },

    saveSendDatetime(state, param) {
      state.sendDatetime = param
    },
    saveTakeDatetime(state, param) {
      state.takeDatetime = param
    },
    saveSelectServiceShop(state, param) {
      state.selectServiceShop = param
    },
    saveSelectServiceTime(state, param) {
      state.selectServiceTime = param
    },
    saveSelectServiceType(state, param) {
      state.selectServiceType = param
    },
    saveSelectModel(state, param) {
      state.selectModel = param
    },
    saveDealer(state, param) {
      state.dealerModel = param
    },
    setScrollTop(state, top) {
      state.modelScrollTop = top
      console.log('%ctop', 'font-size:40px;color:blue;', top)
    },
    setShowBottomButton(state, flag) {
      state.showBottomButton = flag
    },
    setEnv(state, val) {
      state.env = val
    },
    setHasV2Car(state, flag) {
      state.hasV2Car = flag
    },
    setShowPerwin(state, flag) {
      state.showPerwin = flag
    },
    setMeasureConfigCodeList(state, list) {
      state.measureConfigCodeList = list
    },
    setAllMeasureCCList(state, list) {
      state.allMeasureCCList = list
    },
    setCurrentMeasureConfigCode(state, obj) {
      state.currentMeasureConfigCode = obj
    },
    setShowV2Popup(state, flag) {
      state.showV2Popup = flag
    },
    setCurrentV2popupItem(state, flag) {
      state.currentV2popupItem = flag
    },
    setTitle(state, title) {
      state.title = title
    },
    setTitleFn(state, title) {
      state.title = title
    },
    setOrderId(state, orderId) {
      state.orderId = orderId
    },
    setBackFromOptionDetail(state, backFromOptionDetail) {
      state.backFromOptionDetail = backFromOptionDetail
    },
    allowBack(state, allowBackVal) {
      state.allowBackVal = allowBackVal
    },
    showLoading(state) {
      state.loading = true
    },
    hideLoading(state) {
      state.loading = false
    },
    showLoadingNospinner(state) {
      state.loadingNospinner = true
    },
    hideLoadingNospinner(state) {
      state.loadingNospinner = false
    },
    setHeaderVisible(state, visible) {
      state.isShowHeader = visible
    },
    updateDealerInfo(state, param) {
      state.dealerInfo = param
    },
    updateServiceProvider(state, param) {
      console.log('服务商数据', param)
      state.serviceProvider = param
    },
    saveSkuId(state, param) {
      state.skuid = param
    },
    updateDeliveryPattern(state, param) {
      state.deliveryPattern = param
    },
    saveOutsideColorInfo(state, param) {
      const outsideColorInfo = {
        code: param.configDetail.outsideColor.colorCode,
        name: param.configDetail.outsideColor.colorNameCn
      }
      state.outsideColorInfo = outsideColorInfo
    },
    updatePayment(state, param) {
      state.payment = param
    },
    updateCcid(state, param) {
      state.ccid = param
    },
    updateReaded(state, param) {
      state.readed = param
    },
    updateReserveCard(state, param) {
      // console.warn(`update isReserveCard code ${param}`)
      state.isReserveCard = param
    },
    updateReserveCardInfo(state, param) {
      state.reserveCardInfo = param
    },
    setSelectCarIfo(state, obj) {
      state.selectCarInfo = { ...state.selectCarInfo, ...obj }
      storage.setPlus('selectCarInfo', state.selectCarInfo)
    },
    setLineList(state, list) {
      state.modelLineList = list
    },
    setExteriorList(state, list) {
      state.exteriorList = list
    },
    setSelectExColor(state, colorItem) {
      if (!colorItem) {
        return console.error('setSelectExColor 颜色数据为空')
      }
      state.currentExColor = colorItem
    },
    setModelHubList(state, list) {
      state.modelHubList = list
    },
    setCurrentModelHub(state, hub) {
      state.currentModelHub = hub
    },
    setInteriorList(state, list) {
      state.interiorList = list
    },
    setSelectInColor(state, colorItem) {
      state.currentInColor = colorItem
    },

    // 座椅
    setInteriorChairList(state, list) {
      state.interiorChairList = list
    },
    setAllInteriorChairList(state, list) {
      state.allInteriorChairList = list
    },
    setCurrentInteriorChair(state, chair) {
      state.currentInteriorChair = chair
    },
    // 饰条
    setInteriorEihList(state, list) {
      state.interiorEihList = list
    },
    setAllInteriorEihList(state, list) {
      state.allInteriorEihList = list
    },
    setCurrentInteriorEih(state, eih) {
      state.currentInteriorEih = eih
    },
    // 内饰+面料
    setSibColorInterieurList(state, list) {
      state.sibColorInterieurList = list
    },
    setAllSibColorInterieurList(state, list) {
      state.allSibColorInterieurList = list
    },
    setCurrentSibColorInterieur(state, sibColorInterieur) {
      state.currentSibColorInterieur = sibColorInterieur
    },
    // 面料
    setInteriorSibList(state, list) {
      state.interiorSibList = list
    },
    setPrivateOrderList(state, list) {
      // 去重
      function uniqueFunc(arr, uniId) {
        const res = new Map()
        return arr.filter(
          (item) => !res.has(item[uniId]) && res.set(item[uniId], 1)
        )
      }
      const temp = uniqueFunc(list, 'optionName')
      state.privateOrderList = temp
    },
    setClickOption(state, option) {
      state.clickOption = option
    },
    setOtherClickOption(state, option) {
      state.otherClickOption = option
    },
    setPacketItem(state, option) {
      state.packetItem = option
    },
    setCurrentOptionsList(state, option) {
      // 去重
      function uniqueFunc(arr, uniId) {
        const res = new Map()
        return arr.filter(
          (item) => !res.has(item[uniId]) && res.set(item[uniId], 1)
        )
      }
      // debugger
      state.currentOptionsList = uniqueFunc(option, 'optionId')
      // state.currentOptionsList = option
    },
    setDelOption(state, index) {
      state.currentOptionsList.splice(index, 1)
    },
    setCurrentPrice(state, price) {
      state.currentPrice = price
    },
    setCcInfo(state, item) {
      state.ccInfo = item
    },
    setShowOptionPopup(state, bool) {
      state.showOptionPopup = bool
    },
    setShowOptionPopupPlus(state, obj) {
      state.showOptionPopupPlus = JSON.parse(JSON.stringify(obj))
    },
    setPacketOptionsInfoList(state, list) {
      state.packetOptionsInfoList = list
    },
    setChangeList(state, list) {
      state.changeList = list
    },
    setOptionPopupInit(state) {
      state.changeList = []
      state.packetItem = {}
      state.clickOption = {}
      state.packetOptionsInfoList = []
    },
    setIframeUrl(state, url) {
      state.iframeUrl = url
    },
    setCcConfigration(state, info) {
      state.ccConfigration = info
    },
    setFinishRequestInterior(state, bool) {
      state.finishRequestInterior = bool
    },
    setCurrentTabIndex(state, index) {
      state.currentTabIndex = index
    },
    setAllPrivateOrderList(state, list) {
      state.allPrivateOrderList = list
    },
    updateCarSeries(state, info) {
      state.carSeries = info
    },
    setInvoiceInfo(state, info) {
      state.invoiceInfo = info
    },
    setDadingFormData(state, info) {
      state.dadingFormData = info
    },

    // 更新车辆详情
    updateCarDetail(state, param) {
      if (!param) {
        console.warn('当前车辆详情', param)
      }
      state.carDetail = param
    },
    // param: 解析ccid生成的数据
    // standardConfigData(state, param) {
    //   // 生成标准配置表
    //   const outSideColor = {
    //     modelLineCode: param.configDetail?.carModel?.modelLineCode,
    //     name: param.configDetail.outsideColor.colorNameCn,
    //     img: baseOssHost + param.configDetail.outsideColor.imageUrl,
    //     price: param.configDetail.outsideColor.price || '价格已包含',
    //     discount: param.configDetail.outsideColor?.discount
    //   }

    //   const options = param.configDetail.optionList.map((i) => ({
    //     optionClassification: i.optionClassification,
    //     optionCode: i.optionCode,
    //     name: i.optionNameCn,
    //     img: baseOssHost + i.imageUrl,
    //     price: i.optionPrice || '价格已包含',
    //     discount: i?.discount
    //   }))

    //   let inSideColor = {
    //     name: param.configDetail.insideColor.colorNameCn,
    //     img: baseOssHost + param.configDetail.insideColor.imageUrl,
    //     price: param.configDetail.insideColor.price || '价格已包含',
    //     discount: param.configDetail.insideColor.discount
    //   }

    //   if (param.bestRecommendId) {
    //     inSideColor = {
    //       name: param.configDetail.insideColor.colorNameCn,
    //       img: baseOssHost + param.configDetail.insideColor.imageUrl,
    //       price: param.configDetail.insideColor.price || '价格已包含',
    //       discount: param.configDetail.insideColor?.discount
    //     }

    //     state.standardConfigData = [inSideColor, outSideColor, ...options]
    //   } else {
    //     state.standardConfigData = [outSideColor, inSideColor, ...options]
    //   }
    //   console.log('standardConfigData:::', state.standardConfigData)
    // },
    standardConfigData(state, param) {
      // 图片URL处理工具函数
      const getImageUrl = (imageUrl) => {
        if (!imageUrl) return ''; // 处理可能的空值
        return imageUrl.includes('https://') ? imageUrl : baseOssHost + imageUrl;
      };

      // 生成标准配置表
      const outSideColor = {
        modelLineCode: param.configDetail?.carModel?.modelLineCode,
        name: param.configDetail.outsideColor.colorNameCn,
        img: getImageUrl(param.configDetail.outsideColor.imageUrl),
        price: param.configDetail.outsideColor.price || '价格已包含',
        discount: param.configDetail.outsideColor?.discount
      };

      const options = param.configDetail.optionList.map((i) => ({
        optionClassification: i.optionClassification,
        optionCode: i.optionCode,
        name: i.optionNameCn,
        img: getImageUrl(i.imageUrl),
        price: i.optionPrice || '价格已包含',
        discount: i?.discount
      }));

      let inSideColor = {
        name: param.configDetail.insideColor.colorNameCn,
        img: getImageUrl(param.configDetail.insideColor.imageUrl),
        price: param.configDetail.insideColor.price || '价格已包含',
        discount: param.configDetail.insideColor.discount
      };

      if (param.bestRecommendId) {
        // 这里注意：原代码中这个分支的inSideColor与上面定义完全相同，可以简化
        inSideColor = {
          name: param.configDetail.insideColor.colorNameCn,
          img: getImageUrl(param.configDetail.insideColor.imageUrl),
          price: param.configDetail.insideColor.price || '价格已包含',
          discount: param.configDetail.insideColor?.discount
        };

        state.standardConfigData = [inSideColor, outSideColor, ...options];
      } else {
        state.standardConfigData = [outSideColor, inSideColor, ...options];
      }
      console.log('standardConfigData:::', state.standardConfigData);
    },

    updateIsLoadedGDMap(state, param) {
      state.isLoadedGDMap = param
    },

    setBestRecommendCarList(state, data) {
      state.bestRecommendCarList = data
    },
    setDevice(state, device) {
      state.device = { ...state.device, ...device }
      localStorage.setItem('device', JSON.stringify(state.device))
    },
    setIdCardPositive(state, param) {
      state.idCardPositive = { ...param }
    },
    setIdCardReverse(state, param) {
      state.idCardReverse = { ...param }
    },
    setIdCardPositiveRelatives(state, param) {
      state.idCardPositiveRelatives = { ...param }
    },
    setIdCardReverseRelatives(state, param) {
      state.idCardReverseRelatives = { ...param }
    },
    setFileListThree(state, param) {
      state.fileListThree = [...param]
    },
    setFileList(state, param) {
      state.fileList = [...param]
    },
    setFileListFour(state, param) {
      state.fileListFour = [...param]
    },
    // 进取精英
    setIdCardPositiveElite(state, param) {
      state.idCardPositiveElite = { ...param }
    },
    setIdCardReverseElite(state, param) {
      state.idCardReverseElite = { ...param }
    },
    setIdCardPositiveRelativesElite(state, param) {
      state.idCardPositiveRelativesElite = { ...param }
    },
    setIdCardReverseRelativesElite(state, param) {
      state.idCardReverseRelativesElite = { ...param }
    },
    setFileListThreeElite(state, param) {
      state.fileListThreeElite = [...param]
    },
    setFileListElite(state, param) {
      state.fileListElite = [...param]
    },
    setFileListFourElite(state, param) {
      state.fileListFourElite = [...param]
    },
    setfileListSix(state, param) {
      state.fileListSix = [...param]
    },

    // 留学生
    setoverseasidCardPositive(state, param) {
      state.overseasidCardPositive = { ...param }
    },
    setoverseasidCardReverse(state, param) {
      state.overseasidCardReverse = { ...param }
    },
    setoverseasfileList(state, param) {
      state.overseasfileList = { ...param }
    },
    setoverseasfileListThree(state, param) {
      state.overseasfileListThree = { ...param }
    },
    setoverseasFileListFour(state, param) {
      state.overseasFileListFour = { ...param }
    },
    setInitTimeout(state, val) {
      state.initTimeout = val
    },
    setPageFrom(state, page) {
      state.pageFrom = page
    },
    setPageFromScene(state, scene) {
      state.pageFromScene = scene
    },
    setPageSource(state, pageSource) {
      state.pageSource = pageSource
    }
  },
  getters: {
    interiorChairList: (state) =>
      state.interiorChairList.filter(
        (item) => item.status === 2 || item.status === 1 || item.show === true
      ),
    // sibColorInterieurList: (state) => state.sibColorInterieurList.filter(
    //   (item) => item.status === 2 || item.status === 1 || item.show === true
    // ),
    interiorEihList: (state) =>
      state.interiorEihList.filter(
        (item) => item.status === 2 || item.status === 1 || item.show === true
      ),
    modelHubList: (state) =>
      state.modelHubList.filter(
        (item) => item.status === 2 || item.status === 1 || item.show === true
      ),
    packetItem: (state) => state.clickOption.packetItems,
    allOptionsItems: (state) => [
      ...state.interiorChairList,
      ...state.allSibColorInterieurList,
      ...state.interiorEihList,
      ...state.modelHubList,
      ...state.exteriorList,
      ...state.privateOrderList
    ],
    getterCcid: (state) => state.ccid,
    getDevice: (state) =>
      Object.keys(state.device)?.length
        ? state.device
        : JSON.parse(localStorage.getItem('device')),
    getIdCardPositive: (state) => state.idCardPositive,
    getIdCardReverse: (state) => state.idCardReverse,
    getIdCardPositiveRelatives: (state) => state.idCardPositiveRelatives,
    getIdCardReverseRelatives: (state) => state.idCardReverseRelatives,
    getFileListThree: (state) => state.fileListThree,
    getFileList: (state) => state.fileList,
    getFileListFour: (state) => state.fileListFour,
    // 进取精英
    getIdCardPositiveElite: (state) => state.idCardPositiveElite,
    getIdCardReverseElite: (state) => state.idCardReverseElite,
    getIdCardPositiveRelativesElite: (state) =>
      state.idCardPositiveRelativesElite,
    getIdCardReverseRelativesElite: (state) =>
      state.idCardReverseRelativesElite,
    getFileListThreeElite: (state) => state.fileListThreeElite,
    getFileListElite: (state) => state.fileListElite,
    getFileListFourElite: (state) => state.fileListFourElite,
    getfileListSix: (state) => state.fileListSix,

    // 留学生
    getoverseasidCardPositive: (state) => state.overseasidCardPositive,
    getoverseasidCardReverse: (state) => state.overseasidCardReverse,
    getoverseasfileListThree: (state) => state.overseasfileListThree,
    getoverseasfileList: (state) => state.overseasfileList,
    getoverseasFileListFour: (state) => state.overseasFileListFour,

    getPageFrom: (state) => state.pageFrom
  },
  actions,
  modules,
  plugins: [
    // veux持久化配置
    // createVuexPersistedState({
    // paths: ['setCcData']
    // })
  ]
})

export default store
