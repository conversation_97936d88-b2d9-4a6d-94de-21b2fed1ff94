<template>
  <div class="store_map">
    <div
      class="map_container"
      ref="container"
    >
      <div
        class="map_box"
        :style="{height: '334px'}"
      >
        <el-amap
          vid="amapDemo"
          ref="map"
          :amap-manager="amapManager"
          :center="center"
          :zoom="mapObj.zoom"
          :events="mapObj.events"
          :plugin="mapObj.plugin"
        >
          <!-- 标记点 -->
          <template v-for="(m, index) in markers">
            <el-amap-marker
              v-if="m.show"
              :key="index"
              z-index="100"
              :position="m.position"
              :offset="offset"
              :events="m.events"
            >
              <div class="markerIcon">
                <img src="@/assets/img/marker_icon1.png">
                <span>{{ idx>9?idx:'0'+idx }}</span>
              </div>
            </el-amap-marker>
          </template>
        </el-amap>
      </div>
      <div
        class="store_info"
        ref="store"
      >
        <div
          class="title"
          v-if="infoWindow.content.dealerType == 1"
        >
          {{ idx>9?idx:'0'+idx }} {{
            dealerOne(
              infoWindow.content.dealerType,
              infoWindow.content.dealerName,
              infoWindow.content.dealerFullName,
              "one"
            )
          }}
        </div>
        <div
          @click="tostoreDetail"
          class="title"
          v-else
        >
          {{ idx>9?idx:'0'+idx }} {{
            dealerOne(
              infoWindow.content.dealerType,
              infoWindow.content.dealerName,
              infoWindow.content.dealerFullName,
              "one"
            )
          }}
          <img
            src="@/assets/img/icon-arrow.png"
            alt=""
          >
        </div>
        <div class="company_name">
          {{
            dealerOne(
              infoWindow.content.dealerType,
              infoWindow.content.dealerName,
              infoWindow.content.dealerFullName,
              "two"
            )
          }}
        </div>
        <div
          class="lables"
          v-if="infoWindow.content.dealerType == 1"
        >
          <span>服务中心</span>
          <span v-if="infoWindow.content.saicAudiMaintenanceLevel == 'A'">新能源维修</span>
          <span
            v-if="infoWindow.content.fawAudiBatteryMaintenanceCenter == '1'"
          >高压电池维修</span>
        </div>
        <div
          class="lables"
          v-else-if="['0', '1,2'].includes(infoWindow.content.dealerType)"
        >
          <span>零售交付</span>
          <span v-if="infoWindow.content.dealerType === '1,2' ? 1 : infoWindow.content.controlStatus === '有'" style="background: #000000;color: #ffffff;">服务中心</span>
          <span>试驾</span>
          <span>充电</span>
          <span v-if="infoWindow.content.exhibitionHallForm == '3'">品牌体验</span>
          <span v-if="infoWindow.content.dealerType === '1,2' ? 1 : infoWindow.content.controlStatus === '有' && infoWindow.content.saicAudiMaintenanceLevel == 'A'">新能源维修</span>
          <span v-if="infoWindow.content.dealerType === '1,2' ? 1 : infoWindow.content.controlStatus === '有' && infoWindow.content.fawAudiBatteryMaintenanceCenter == '1'">高压电池维修</span>
        </div>
        <div
          class="lables"
          v-else-if="infoWindow.content.dealerType === '4'"
        >
          <span>零售交付</span>
          <span v-if="infoWindow.content.controlStatus === '有'" style="background: #000000;color: #ffffff;">服务中心</span>
          <span>试驾</span>
          <span>充电</span>
          <span v-if="infoWindow.content.exhibitionHallForm == '3'">品牌体验</span>
          <span v-if="infoWindow.content.controlStatus === '有' && infoWindow.content.saicAudiMaintenanceLevel == 'A'">新能源维修</span>
          <span v-if="infoWindow.content.controlStatus === '有' && infoWindow.content.fawAudiBatteryMaintenanceCenter == '1'">高压电池维修</span>
        </div>
        <div
          class="lables"
          v-else
        >
          <span>零售交付</span>
          <span>试驾</span>
          <span>充电</span>
          <span v-if="infoWindow.content.exhibitionHallForm == '3'">品牌体验</span>
        </div>
        <div class="addres">
          <div>
            <div class="serve_item">
              <div class="serve_titt">
                营业时间:
              </div>
              <div class="item_info" v-if="['0', '1,2', '4'].includes(infoWindow.content.dealerType)">
                {{ infoWindow.content.workingDay }}（工作日）{{ infoWindow.content.nonWorkingDay}}（周末）
              </div>
              <div class="item_info" v-else>
               {{ infoWindow.content.workingDay }}
              </div>
            </div>
            <div
              class="serve_item"
              v-if="infoWindow.content.dealerAdrress&&['0', '1,2', '4'].includes(infoWindow.content.dealerType)"
            >
              <div class="serve_titt">

                {{ infoWindow.content.dealerAdrress!=infoWindow.content.afterSalesAdrress?'销售地址:':'门店地址' }}
              </div>
              <div class="item_info">
                {{ infoWindow.content.dealerAdrress || "暂无" }}
              </div>
            </div>
            <div
              class="serve_item"
              v-if="infoWindow.content.afterSalesAdrress&& (infoWindow.content.dealerType === '1' || infoWindow.content.dealerType === '1,2' || ['0', '4'].includes(infoWindow.content.dealerType) && infoWindow.content.controlStatus === '有')&&infoWindow.content.dealerAdrress!=infoWindow.content.afterSalesAdrress"
            >
              <div class="serve_titt">
                售后地址:
              </div>
              <div class="item_info">
                {{ infoWindow.content.afterSalesAdrress || "暂无" }}
              </div>
            </div>
            <div
              class="serve_item"
              v-if="infoWindow.content.dealerAdrress&&!['0', '1,2', '4'].includes(infoWindow.content.dealerType)"
            >
              <div class="serve_titt">
                门店地址:
              </div>
              <div class="item_info">
                {{ infoWindow.content.dealerAdrress || "暂无" }}
              </div>
            </div>
            <div
              class="serve_item"
              v-if="infoWindow.content.dealerType != 1"
            >
              <div class="serve_titt">
                销售热线:
              </div>
              <div
                class="item_info item_tel item_tel_o"
                @click="call(infoWindow.content.dealerPhone)"
              >
                {{ infoWindow.content.dealerPhone || "暂无" }}

                <img
                  v-if="infoWindow.content.dealerPhone"
                  src="@/assets/img/callphone.png"
                  alt=""
                >
              </div>
            </div>
            <div
              class="serve_item"
              v-if="infoWindow.content.dealerType == 1||(infoWindow.content.dealerType === '1,2' || ['0', '4'].includes(infoWindow.content.dealerType) && infoWindow.content.controlStatus === '有')"
            >
              <div class="serve_titt">
                售后电话:
              </div>
              <div
                class="item_info item_tel item_tel_o"
                @click="call(infoWindow.content.servicePhone)"
              >
                {{ infoWindow.content.servicePhone || "暂无" }}
                <img
                  v-if="infoWindow.content.servicePhone"
                  src="@/assets/img/callphone.png"
                  alt=""
                >
              </div>
            </div>
            <div
              class="serve_item"
              v-if="infoWindow.content.dealerType == 1||(infoWindow.content.dealerType === '1,2' || ['0', '4'].includes(infoWindow.content.dealerType) && infoWindow.content.controlStatus === '有')"
            >
              <div class="serve_titt">
                24H救援:
              </div>
              <div
                class="item_info item_tel item_tel_o"
                @click="call(infoWindow.content.hotPhone24)"
              >
                {{ infoWindow.content.hotPhone24 || "暂无" }}
                <img
                  v-if="infoWindow.content.hotPhone24"
                  src="@/assets/img/callphone.png"
                  alt=""
                >
              </div>
            </div>
          </div>
        </div>
        <div
          class="Sale"
          v-if="infoWindow.content.dealerType == 2||['0', '1,2', '4'].includes(infoWindow.content.dealerType)"
        >
          <div
            class="to_store"
            @click="urlPath(infoWindow.content.longitude, infoWindow.content.latitude,infoWindow.content.dealerName)"
          >
            到这儿去
          </div>
          <div
            class="reserve_btn"
            @click="
              testDriveUrl(infoWindow.content)
            "
          >
            预约试驾
          </div>
        </div>
        <div
          class="Sale_go_serve"
          v-else
        >
          <div
            class="go_serve"
            @click="
              urlPath(infoWindow.content.longitude, infoWindow.content.latitude,infoWindow.content.dealerName)
            "
          >
            到这儿去
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model="showAddress"
      closeable
      close-icon-position="top-left"
      position="bottom"
      :style="{ height: '47%' }"
    >
      <div class="address-list">
        <div
          class="address-list-title"
        >
         选择地址
        </div>
        <div class="address-list-content" @click="whereToGo(1)">
           <div class="address-list-content-title">
            销售地址
            <p>
             {{ infoWindow.content.dealerAdrress }}
            </p>
           </div>
           <van-icon v-if="isWhereToGo==1" name="success" color="#999999"/>
        </div>
        <div class="address-list-content" @click="whereToGo(2)" v-if="infoWindow.content.dealerType === '1' || ['0', '4'].includes(infoWindow.content.dealerType) && infoWindow.content.controlStatus === '有'">
           <div class="address-list-content-title">
            售后地址
            <p>
              {{ infoWindow.content.afterSalesAdrress }}
            </p>
           </div>
           <van-icon  v-if="isWhereToGo==2" name="success" color="#999999"/>
        </div>
        <div
            class="go_serve"
            @click="
              whereToGoUrlPath()
            "
          >
            到这儿去
          </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { callNative, getUrlParamObj } from '@/utils'
import Vue from 'vue'
import { NavBar, Toast, Popup } from 'vant'
import { AMapManager } from 'vue-amap'
import {
  getCarOrderInfoH5,
  getAudiCarUrl, getAudiCar2Url
} from '@/api/api'
import { mapGetters } from 'vuex'
import { minipOpenLocation } from '@/utils/weixin-js-sdk'

import store from '../../store/index'

Vue.use(NavBar)
  .use(Popup)
const amapManager = new AMapManager()

export default {
  data() {
    return {
      amapManager,
      // 地图初始化
      mapObj: {
        zoom: 16,
        events: {
          click: (e) => {
            this.markerShowIndex = null
          }
        },
        plugin: []
      },
      offset: [-21, -40],
      markers: [],
      // 信息框
      infoWindow: {
        offset: [0, -40],
        position: [121.45348, 31.236372],
        indexNumber: '',
        content: {}
      },

      center: [121.473701, 31.230416],
      parentInfoWindow: {},
      name: '渠道商',
      storeList: [],
      current: {
        longitude: '',
        latitude: ''
      },
      mapBoxHeight: '',
      idx: '',
      device: {},
      audiCarUrl: '',
      showAddress: false,
      isWhereToGo: '1',
      result: []
    }
  },
  computed: {
    dealerOne() {
      return function (Listtype, name, fullname, line) {
        console.log(Listtype, name, fullname, line, 'Listtype, name, fullname, line')
        if (line == 'one') {
          if (Listtype == 1) {
            return `上汽奥迪授权服务商${name}`
          }
          return name
        }
        return fullname
      }
    }
    // thumbnailUrl() {
    //   return function (e) {
    //     if (e != undefined) {
    //       var newStr = e.indexOf("http");
    //       if (newStr == 0) {
    //         console.log("字符串是以http开头的");
    //         return e;
    //       } else {
    //         return require("@/assets/img/defaultImg.png");
    //       }
    //     }
    //   };
    // },
  },
  async created() {
    const { env } = this.$route.query
    // 小程序
    if (env === 'minip') {
      this.device.sign = env
    } else {
      this.device = this.getDevice() || {}
      // app / h5
      this.device.sign = this.device?.nativeApp ? 'native' : 'html5'
    }
    const { data } = await (env === 'minip' ? getAudiCar2Url() : getAudiCarUrl())
    if (data?.data?.configValue) {
      this.audiCarUrl = data.data.configValue
    }
    console.log('%c [ this.device.sign ]-302', 'font-size:14px; background:#cf222e; color:#fff;', this.device.sign)
  },
  mounted() {
    console.log('this.$route.params', this.$route)
    this.$nextTick(() => {
      this.storeList = [this.$route.query.store]
      this.result = [...this.$route.query.result]
      console.log(this.result, 'this.result')
      this.idx = 1 + parseInt(this.$route.query.idx)
      this.setMarkers(this.storeList)
    })
  },
  methods: {
    ...mapGetters(['getDevice']),
    mapHeight111() {
      console.log(this.$refs.container.offsetHeight, this.$refs.store.offsetHeight)
      this.mapBoxHeight = `${this.$refs.container.offsetHeight - this.$refs.store.offsetHeight - 55}px`
      this.$forceUpdate()
    },
    async tostoreDetail() {
      console.log('openRoutePath', this.$route.query.store.dealerCode)
      const params = getUrlParamObj()
      const string = Object.keys(params).reduce((i, n) => i + (params[n] ? (`&${n}=${params[n]}`) : ''), '')
      // const { data } = await getAudiCarUrl()
      const url = `${this.audiCarUrl}dealerDetail?dealerCode=${this.$route.query.store.dealerCode}`
      if (this.device.sign === 'native') {
        callNative('audiOpen', { path: url })
      } else {
        window.location.href = url + string
      }
    },
    call(phone) {
      if (phone) {
        window.location.href = `tel:${phone}`
      }
    },
    // 试驾
    async testDriveUrl(obj) {
      const { data } = await getCarOrderInfoH5()
      const orderh5BaseUrl = data.data.configValue
      console.log(obj)
      const dealerAdrress = `${obj.provinceName}/${obj.cityName}`
      const path = `${
        orderh5BaseUrl
      }testdrive/create?from=DealerDetail&dealerCode=${
        obj.dealerCode || ''
      }&dealerName=${obj.dealerName || ''}&dealerAdrress=${
        dealerAdrress || ''
      }&addr=${obj.dealerAdrress || ''}`
      console.log('跳转路径', path)
      if (this.device.sign === 'native') {
        await callNative('audiOpen', {
          path: path,
          params: ''
        })
      } else {
        window.location.href = path
      }
    },
    // 路线
    urlPath(lng, lat, address) {
      if ( ( (['0', '4'].includes(this.infoWindow.content.dealerType) && this.infoWindow.content.controlStatus == "有") || this.infoWindow.content.dealerType == "1")
        && this.infoWindow.content.afterSalesAdrress
      ) {
        this.showAddress = true
        return
      }
      if (
        ['1,2', '1'].includes(this.infoWindow.content.dealerType)
      ) {
        lng = this.infoWindow.content.afterSalesLongitude
        lat = this.infoWindow.content.afterSalesLatitude
        address = this.infoWindow.content.dealerName
      }
      if (lng && lat && address) {
        console.log('%c [ lng && lat && address ]-351', 'font-size:14px; background:#cf222e; color:#fff;', lng, lat, address)
        if (this.device.sign === 'native') {
          callNative('navigationMap', {
            lat: lat.toString(),
            long: lng.toString(),
            des: address.toString()
          })
        } else if (this.device.sign === 'minip') {
          minipOpenLocation({ latitude: lat, longitude: lng, name: address })
        }
        return
      }
      Toast({
        message: '坐标地址信息不完善',
        className: 'toast-dark-mini toast-pos-middle',
        forbidClick: true,
        duration: 800
      })
      // window.webViewJavascriptBridge.callHandler(
      //   "navigationMap",
      //   {
      //     lat: lat.toString(),
      //     long: lng.toString(),
      //     des: address.toString(),
      //   },
      //   function (err, data) {
      //     console.log("window.webViewJavascriptBridge.callHandler",data)
      //     if (err) {
      //       console.log(err);
      //       return;
      //     }
      //   }
      // );
    },
    daohang(longitude, latitude, address) {

    },
    goBack() {
      this.$router.back(-1)
    },
    // 整理显示数据
    setMarkers(list) {
      var that = this
      this.markerShowIndex = null
      const [info] = list
      let latitude = ''
      let longitude = ''
      if (info && Object.keys(info)?.length) {
        latitude = info?.latitude || ''
        longitude = info?.longitude || ''
      }
      console.log(info, 'infoinfo')
      if (['0', '1,2', '4'].includes(info.dealerType)
        && (that.result.indexOf('服务中心') > -1
        || that.result.indexOf('新能源维修') > -1
        || that.result.indexOf('高压电池维修') > -1)
        && that.result.indexOf('零售交付') == -1
        && that.result.indexOf('试驾') == -1
        && that.result.indexOf('充电') == -1
        && that.result.indexOf('品牌体验') == -1
        && that.result.indexOf('全部') == -1
      ) {
        longitude = info?.afterSalesLongitude || ''
        latitude = info?.afterSalesLatitude || ''
      }
      console.log('%c [ info ]-409', 'font-size:14px; background:#cf222e; color:#fff;', info.dealerType, info, latitude, longitude)
      const arr = []
      if (latitude && longitude) {
        var obj = {
          position: [longitude, latitude],
          show: true,
          events: {
            click() {
              that.placeholderCode = false
              that.checked = false
              that.placeholderPhone = false
              that.codeCorrect = false
              that.phone = ''
              that.verificationCode = ''

              that.$nextTick(() => {
                that.markerShowIndex = 0
              })
            }
          }
        }
      } else {
        var obj = {
          position: [],
          show: true,
          events: {}
        }
      }
      that.infoWindow.content = list[0]
      that.infoWindow.indexNumber = 1
      if (
        ['0', '1,2', '4'].includes(info.dealerType)
        && (that.result.indexOf('服务中心') > -1
        || that.result.indexOf('新能源维修') > -1
        || that.result.indexOf('高压电池维修') > -1)
        && that.result.indexOf('零售交付') == -1
        && that.result.indexOf('试驾') == -1
        && that.result.indexOf('充电') == -1
        && that.result.indexOf('品牌体验') == -1
        && that.result.indexOf('全部') == -1
      ) {
        that.infoWindow.position = [list[0].afterSalesLongitude, list[0].afterSalesLatitude]
      } else {
        that.infoWindow.position = [list[0].longitude, list[0].latitude]
      }
      // that.infoWindow.position = [list[0].longitude, list[0].latitude]

      if (that.infoWindow.content.dealerAdrress) {
        that.infoWindow.content.dealerAdrress = that.infoWindow.content.dealerAdrress.trim()
      }
      arr.push(obj)
      this.markers = arr
      var that = this
      this.$nextTick(() => {
        setTimeout(() => {
          that.$refs.map.$$getInstance().setFitView()
          this.mapHeight111()
        }, 30)
      })
    },

    whereToGoUrlPath(lng, lat, address) {
      if (this.isWhereToGo == 1) {
        lng = this.infoWindow.content.longitude
        lat = this.infoWindow.content.latitude
        address = this.infoWindow.content.dealerName
      } else {
        lng = this.infoWindow.content.afterSalesLongitude
        lat = this.infoWindow.content.afterSalesLatitude
        address = this.infoWindow.content.dealerName
      }
      if (lng && lat && address) {
        console.log('%c [ lng && lat && address ]-351', 'font-size:14px; background:#cf222e; color:#fff;', lng, lat, address)
        if (this.device.sign === 'native') {
          callNative('navigationMap', {
            lat: lat.toString(),
            long: lng.toString(),
            des: address.toString()
          })
        } else if (this.device.sign === 'minip') {
          minipOpenLocation({ latitude: lat, longitude: lng, name: address })
        }
        this.showAddress = false
        return
      }
      Toast({
        message: '坐标地址信息不完善',
        className: 'toast-dark-mini toast-pos-middle',
        forbidClick: true,
        duration: 800
      })
      this.showAddress = false
      // window.webViewJavascriptBridge.callHandler(
      //   "navigationMap",
      //   {
      //     lat: lat.toString(),
      //     long: lng.toString(),
      //     des: address.toString(),
      //   },
      //   function (err, data) {
      //     console.log("window.webViewJavascriptBridge.callHandler",data)
      //     if (err) {
      //       console.log(err);
      //       return;
      //     }
      //   }
      // );
    },
    whereToGo(val) {
      this.isWhereToGo = val
    }
  }
}
</script>

<style lang='less' scoped>
.map_container {
  width: 100%;
  height: calc(100vh - 168px);
  overflow-y: scroll;
  .map_box {
    width: 100%;
    // height: calc(100vh - 380px);
  }
  .store_info {
    padding: 0 16px;
    height:auto;
    .title {
      font-size: 16px;
      font-weight: normal;
      color: #000000;
      line-height: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-top: 22px;
      img{
        width: 24px;
        height: 24px;
      }
    }
    .company_name{
      font-size: 12px;
      font-weight: 400;
      color: #000000;
      padding:8px 0;
      line-height: 20px;
    }
    .addres {
      width: 100%;
      display: flex;
      padding:0 0 0.16rem 0;
      align-items: flex-start;
      padding-bottom: 8px;
      border-top: 1px solid #E5E5E5;
      margin-top: 14px;
      > div {
        padding-left: 0.213333rem;
        width: 100%;
        font-size: 0.16rem;
        line-height: 0.266667rem;
        // padding: 0 10px;
      }
      > img {
        width: 60px;
        height: 60px;
        object-fit: cover;
      }
      .serve_item {
        display: flex;
        justify-content: flex-start;
        margin: 10px 0;
        .serve_titt {
          font-size: 12px;
          font-weight: 00;
          line-height: 20px;
          margin-right: 10px;
          width: 60px;
          color: #000000;
          opacity: 0.5;

        }

        .item_info {
          font-size: 12px;
          font-weight: normal;
          color: #000000;
          line-height: 20px;
          width: 279px;
          opacity: 0.5;
        }
        .item_tel_o{
          opacity: 1;
        }
        .item_tel{
          font-weight: bold;
          display: flex;
          align-items: center;
          img{
            width: 20px;
            height: 20px;
            margin-left: 8px;
          }
        }
      }
    }
    .Sale {
      display: flex;
      justify-content: space-between;
      position: fixed;
      left: 0;
      bottom: 0;
      width:calc(100% - 32px) ;
      background: #FFFFFF;
      height: 100px;
      padding: 0 16px;
      > div {
        display: inline-block;
        width:calc(50% - 4px);
        height: 56px;
        line-height: 56px;
        text-align: center;
        cursor: pointer;
        font-size: 16px;
        // margin: 6px;
      }

      .to_store {
        color: #000000;
        border: 1px solid #000000;
        // margin-right: 7px;
      }

      .reserve_btn {
        background: #000000;
        color: #fff;
        text-align: center;
        border: 1px solid #000000;

        cursor: pointer;
      }
    }
    .Sale_go_serve{
      position: fixed;
      left: 0;
      bottom: 0;
      width:calc(100% - 32px) ;
      background: #FFFFFF;
      height: 100px;
      padding: 0 16px;
    }
    .go_serve {
      width: 100%;
      height: 56px;
      line-height: 56px;
      background: #000000;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      border: 1px solid #000000;
      text-align: center;
      cursor: pointer;
      font-size: 16px;
      color: #ffffff;
    }
  }
}
::v-deep .amap-info-close {
  display: none;
  cursor: pointer;
}
.lables {
  font-weight: normal;
  color: #000000;
  line-height: 16px;
  margin-top: 2px;
  span {
    background-color: #e5e5e5;
    font-size: 10px;
    padding: 2px 6px;
    margin-right: 8px;
    &:nth-child(1){
      background: #000000;
      color:#ffffff;
    }
  }
}
.markerIcon {
  // width: .533333rem;
  // height: .533333rem;
  width:45px;
  height: 45px;
  img {
    width: 100%;
    height: 100%;
  }
  span {
    color: #ffffff;
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 16px;
    transform: translate(-50%, -70%);
  }
}
::v-deep .van-nav-bar__left {
  .van-nav-bar__text {
    color: #000000 ;
  }
  .van-icon {
    color: #000000 ;
  }
}

.address-list{
  width: 100%;
  height: 100%;
  .address-list-title{
    padding: 16px 0px;
    text-align: center;
    border-bottom: 1px solid #E5E5E5;
    font-size: 16px;
  }
  .address-list-content{
    display: flex;
    padding: 16px 0;
    margin:0 16px ;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E5E5E5;

    .address-list-content-title{
       color: #333333;
       font-size: 16px;
       line-height: 24px;
      p{
        margin: 0;
        font-size: 12px;
        line-height: 16px;
        color: #999999;
      }
    }

  }
  .go_serve {
      // width: 100%;
      height: 56px;
      line-height: 56px;
      background: #000000;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      border: 1px solid #000000;
      text-align: center;
      cursor: pointer;
      font-size: 16px;
      color: #ffffff;
      margin:  0 16px;
      margin-top: 99px;
    }
}
</style>
