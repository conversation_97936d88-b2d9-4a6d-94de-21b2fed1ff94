<template>
  <div>
    <div
      class="myContainer"
      ref="container"
      id="_container"
      v-show="tableData.length"
    >
      <div
        id="here0"
        class="c-bold fixed-box"
      >
        <div class="fixed-title">
          <div>基本参数</div>
          <div
            class="c-font14 filter-box"
          >
            <img
              @click="showPop = !showPop"
              :src="BaseOssHostCC + 'assets/img/filter-small.png'"
            >
            <div
              @click="toConfigpdf"
              v-if="isShowMore"
            >
              查看更多
            </div>
          </div>
        </div>
        <div
          class="c-flex pop-wrapper"
          v-show="showPop"
        >
          <div class="btn-wrapper">
            <AudiButton
              @click="doWhellToTag('base', 'here0')"
              text="基本参数"
              :color=" currentTag ==='base'?'black' :'white'"
              height="30px"
            />
          </div>
          <div class="btn-wrapper">
            <AudiButton
              @click="doWhellToTag('color', 'here1')"
              text="标准配置"
              :color=" currentTag ==='color'?'black' :'white'"
              height="30px"
            />
          </div>
        </div>
        <div
          class="black-cover"
          :style="{top:statusBarHeight+navigationBarHeight-1 + 'px'}"
          v-show="showPop"
          @click="showPop= false"
        />
      </div>

      <div>
        <div
          v-for="(item,index) in tableData"
          :key="index"
          class="c-flex c-font12 item"
        >
          <div class="name">
            {{ item.parameterName }}
          </div>
          <div class="desc">
            {{ item.parameterValue }}
          </div>
        </div>
      </div>
    </div>
    <div
      style="display: flex;
      justify-content: flex-start;"
    >
      <van-tabs
        :style="{'width': $route.query.modellineId == 'd9150277-7dbe-4840-aac3-2b7e010ead7f'? '215px': '110px'}"
        v-model="activeTab"
        @click="onClickTab"
      >
        <van-tab title="标准配置" />
        <!-- modelLineId: "d9150277-7dbe-4840-aac3-2b7e010ead7f"
        modelLineName: "筑梦青春版" 当前车型：selectCarInfo-->
        <van-tab
          v-if="$route.query.modellineId == 'd9150277-7dbe-4840-aac3-2b7e010ead7f'"
          title="筑梦青春选装包"
        />
      </van-tabs>
    </div>
    <div
      id="here1"
      ref="color"
      v-if="series === 'a7l' || series === '49' || series === 'q6'"
    >
      <div class="standard-box ">
        <div class="_title underline c-bold" />
        <div
          v-for="item in mockData"
          :key="item.optionId"
        >
          <div
            class="_item underline"
            v-if="item.status === 1"
          >
            <div class="_img">
              <img
                :src="$loadWebpImage(ossUrl + item.imageUrlList)"
                @click="imagePreview($loadWebpImage(ossUrl + item.imageUrlList))"
              >
            </div>
            <div class="_desc">
              <p class="_text1">
                {{ item.optionName }} {{ item.optionCode ===numberKey?'*':"" }}
              </p>
              <p class="_text2">
                {{ isNumber(item.price)?'¥':'' }}{{ item.price === null ? '价格已包含' : item.price }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- <div ref="color">
        <StandardConfig :visible="true" />
      </div> -->


      <div
        class="text-wrapper"
        v-if="isInculdeNumberKey"
      >
        * 选配该装备时请查看兼容的手机机型
      </div>

      <div class="text-wrapper">
        若出现以下情况（包括但不限于）：国家相关法律法规的强制要求、技术进步、产品更新和零部件供货商调整等，上汽大众汽车有限公司负责对车辆外观颜色、技术参数、配置装备和车辆代号等信息作进一步修改和说明。
      </div>
    </div>
    <div
      id="here1"
      ref="color"
      v-if="series === 'f0'"
    >
      <div class="standard-box ">
        <div class="_title underline c-bold" />
        <div
          v-for="item,index in mockData"
          :key="index"
        >
          <div
            class="_item underline"
          >
            <div class="_img">
              <img  :src="item?.materialList?.[0]?.materialUrl" @click="imagePreview(item?.materialList?.[0]?.materialUrl)">
            </div>
            <div class="_desc">
              <p class="_text1">
                {{ item.externalFeatureNameZh }}
              </p>
              <p class="_text2">
                {{ isNumber(item.featurePrice)?'¥':'' }}{{ item.featurePrice === '0' ? '价格已包含' : item.featurePrice }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- <div ref="color">
        <StandardConfig :visible="true" />
      </div> -->


      <div
        class="text-wrapper"
        v-if="isInculdeNumberKey"
      >
        * 选配该装备时请查看兼容的手机机型
      </div>

      <div class="text-wrapper">
        若出现以下情况（包括但不限于）：国家相关法律法规的强制要求、技术进步、产品更新和零部件供货商调整等，上汽大众汽车有限公司负责对车辆外观颜色、技术参数、配置装备和车辆代号等信息作进一步修改和说明。
      </div>
    </div>
    <div
      ref="color"
      v-if="series === 'q5e' || series === 'g4' || carSeries.seriesCode === 'G4'"
    >
      <div class="q5e-box">
        <div
          v-for="m in mockData2"
          :key="m.optionId"
        >
          <div class="_title underline c-bold">
            <div>{{ m.name }}</div><img
              @click="m.show = !m.show"
              src="../../assets/img/icon03.png"
              :class="[m.show?'r90':'']"
            >
          </div>
          <template v-if="m.show">
            <div
              v-for="item in m.data"
              :key="item.optionId"
              class="c-flex c-font12 item underline"
            >
              <div
                class="_item"
                v-if="item.status === 1"
              >
                <div class="_img">
                  <img
                    :src="$loadWebpImage(ossUrl + item.imageUrlList)"
                    @click="imagePreview($loadWebpImage(ossUrl + item.imageUrlList))"
                  >
                </div>
                <div class="_desc">
                  <p class="_text1">
                    {{ item.optionName }} {{ item.optionCode ===numberKey?'*':"" }}
                  </p>
                  <p class="_text2">
                    {{ isNumber(item.price)?'¥':'' }}{{ item.price === null ? '价格已包含' : item.price }}
                  </p>
                  <div v-if="item.optionCode == '4D3' && modelLineCode == 'G4IBF3001'">
                    如果您在装备页卡选择了前排座椅按摩，前排座椅通风将被移除
                  </div>
                  <div v-if="item.optionCode == '8I6' && modelLineCode == 'G4ICF3001'">
                    如果您在装备页卡选择了前排座椅通风，前排座椅按摩将被移除
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div
        class="text-wrapper"
        v-if="isInculdeNumberKey2"
      >
        * 选配该装备时请查看兼容的手机机型
      </div>

      <div class="text-wrapper">
        若出现以下情况（包括但不限于）：国家相关法律法规的强制要求、技术进步、产品更新和零部件供货商调整等，上汽大众汽车有限公司负责对车辆外观颜色、技术参数、配置装备和车辆代号等信息作进一步修改和说明。
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Vue from 'vue'
import {
  ImagePreview, Popup, Tab, Tabs
} from 'vant'

import { getModelLineConfigs } from '@/api/api'
import { getOmdHighLight } from '@/configratorApi'
import AudiButton from '@/components/audi-button'
import url from '@/config/url'
import { callNative } from '../../utils'
import {
  getModelLineQuery
} from '@/configratorApi'
  // import StandardConfig from '@/components/standard-config'
console.log('url:', url)
Vue.use(Popup)
Vue.use(Tab)
Vue.use(Tabs)

const NUMBER_KEY = '2F1' // 数字钥匙的 optionCode

export default {
  name: 'CarConfigTable',
  components: { AudiButton, Tab, Tabs },
  data() {
    return {
      activeTab: 0,
      tableData: [],
      tableDataCar: [],
      tabDataCar: [],
      showPop: false,
      currentTag: 'base', // 不知道这是什么sd交互..
      ossUrl: url.BaseConfigrationOssHost,
      BaseOssHostCC: url.BaseOssHostCC,
      mockData: [],
      mockData2: [],
      numberKey: NUMBER_KEY,
      isInculdeNumberKey: false,
      isShowMore: false,
      isInculdeNumberKey2: false,
      statusBarHeight: 0,
      navigationBarHeight: 50,
      series: '',
      defPackage: [],
      modelLineCode: '',
      fromText: ['报价单', '车辆推荐', '版本选择']
    }
  },
  computed: {
    ...mapState({
      carModelName: (state) => state.carDetail.configDetail?.carModel?.modelNameCn,
      carSeries: (state) => state.carSeries,
      modellineId: (state) => state.carDetail.configDetail?.carModel?.modellineId,
      selectCarInfo: (state) => state.selectCarInfo,
      allPrivateOrderList: (state) => state.allPrivateOrderList
    })
  },
  created() {
    this.getModelLineQuery()
  },
  mounted() {
    // modelUniqueCode
    console.log('当前车型：selectCarInfo', this.selectCarInfo)
    console.log('当前车型所有装备：allPrivateOrderList', this.allPrivateOrderList)
    const { carModelName, seriesName, seriesCode } = this.$route.query
    console.log('this.$route.query', this.$route.query)
    this.$store.commit('setTitle', `${carModelName ?? this.carModelName}参数表`)
    this.getTableData()
    this.getHeight()
    const sname = seriesName || this.selectCarInfo.seriesName || this.carSeries.seriesCode
    console.log('sname===', sname)
    this.SetSensorsTrackData('H5_CarConfiguration_PageView')
    if (seriesCode) {
      if (seriesCode == 'G4') this.series = 'q5e'
      if (seriesCode == 'G6') this.series = 'q6'
      if (seriesCode == '49') this.series = 'a7l'
      if (seriesCode == 'F0') this.series = 'a5l'
      console.log('this.series ', this.series)
    } else {
      if (!sname) return
      this.series = sname.toLowerCase()
      if (this.series == 'G6' || this.series == 'g6') this.series = 'q6'
      console.log('series===', sname)
    }
  },
  methods: {
    async getModelLineQuery() {
      const p = this.$route.query.modellineId
      const res = await getModelLineQuery(p)
      const data = res?.data?.data[0]
      this.modelLineCode = data.modelLineCode
      if(data.omdModelUnicode){
        getOmdHighLight(data.omdModelUnicode).then((res)=>{
          console.log(res.data.data.result.children);
          if (this.series === 'f0') {
            this.mockData = res.data.data.result.children
            // const hasNumberKey = listF0.find(i => i.optionCode === this.numberKey);
            // this.isInculdeNumberKey = !!hasNumberKey;
            return
          }
          return
        })
      }

      if (this.$route.query.carModelName != '筑梦青春版') {
        this.isShowMore = true
      }
      // const q5eHYB = 'G4ICC3003,G4ICC3005,G4ICF3004,G4ICF3006,G4ICC3004,G4ICF3005'
      // const q5e2023 = 'G4IBC3004,G4IBF3003,G4ICC3007,G4ICC3006,G4IBC3003,G4ICF3007'
      // if (q5e2023.includes(this.modelLineCode)) {
      //   this.isShowMore = false
      // }
      // if (q5eHYB.includes(this.modelLineCode)) {
      //   this.isShowMore = true
      // }
      console.log('getModelLineQuery', res)
    },
    onClickTab(e) {
      // 筑梦青春选装包
      // optionName                     optionCode
      // 奥迪手机无线充电盒	             	9ZE
      // 信号放大器		                   GB3
      // 舒适钥匙，无钥匙进入及启动	    	 4I3
      // 19吋 多幅星云轮毂		            40V
      // 贯穿式LED尾灯带动态转向灯	    	 PHR
      console.log(' this.mockData >>>>>>>>>', this.mockData)
      let arr = []
      if (this.$route.query.modellineId === 'd9150277-7dbe-4840-aac3-2b7e010ead7f') {
        arr = this.tableDataCar[1].filter((e) => '9ZE,GB3,4I3,40V,PHR'.includes(e.optionCode))
      }
      this.mockData = !this.activeTab ? this.tableDataCar[0] : arr
    },
    imagePreview(url) {
      ImagePreview([url])
    },
    toConfigpdf() {
      const { carModelName, special } = this.$route.query

      // if (carModelName.includes('Q6')) {
      //   return
      // }
      this.$router.push({
        path: '/config-pdf',
        query: {
          modelLineCode: this.modelLineCode,
          carModelName,
          special
        }
      })
    },
    isNumber(value) {
      return typeof value === 'number' && !isNaN(value)
    },
    async getTableData() {
      const { modellineId } = this.$route.query
      const params = {
        modelLineId: modellineId ?? this.modellineId
      }
      const { data } = await getModelLineConfigs(params)
      console.log('getModelLineConfigs', data)
      this.tableData = data.data.parameter.filter((i) => i.parameterValue)
      console.log(' this.tableData', this.tableData, this.series)
      // a7l
      if (this.series === 'a7l' || this.series === '49' || this.series === 'q6') {
        const res = data.data.modelLineOption.filter((i) => i.status === 1 && i.imageUrlList && i.optionType != 'packet' && i.optionType != 'personal')
        console.log('getTableData res', res)
        const arr = res.filter((e) => e.category != 'SIB')


        this.$route.query.modellineId === 'd9150277-7dbe-4840-aac3-2b7e010ead7f' && arr.filter((e) => {
          console.log('9ZE,GB3,4I3,40V,PHR'.includes(e.optionCode))
          if ('9ZE,GB3,4I3,40V,PHR'.includes(e.optionCode)) {
            console.log('============r', e)
          }
          return !'9ZE,GB3,4I3,40V,PHR'.includes(e.optionCode)
        })
        console.log(" this.$route.query.modellineId === 'd9150277-7dbe-4840-aac3-2b7e010ead7f'", this.$route.query.modellineId === 'd9150277-7dbe-4840-aac3-2b7e010ead7f')
        let temp = []
        if (this.$route.query.modellineId === 'd9150277-7dbe-4840-aac3-2b7e010ead7f') {
          temp = arr.filter((e) => !'9ZE,GB3,4I3,40V,PHR'.includes(e.optionCode))
        }
        console.log(temp, 'arrrrrr', arr)
        const list = this.$route.query.modellineId === 'd9150277-7dbe-4840-aac3-2b7e010ead7f' && !this.activeTab ? temp : arr
        this.tableDataCar = [list, arr]
        this.mockData = list
        console.log('this.mockData = ', this.mockData)
        this.defPackage = res.reduce((pre, current) => {
          const bool = pre.find((j) => j.optionCode === current.optionCode)
          if (!bool) pre.push(current)
          return pre
        }, [])

        // https://project.fontre.com/issues/21956
        if (this.series === 'q6') {
          const hideOptionCodes = ['Q2J','5ZC', '7P1', '8T6'];
          this.defPackage = this.defPackage.filter((e) => !hideOptionCodes.includes(e.optionCode))
        }

        const bool = this.mockData.find((i) => i.optionCode === this.numberKey)
        this.isInculdeNumberKey = !!bool
      }
      // q5e
      if (this.series === 'q5e' || this.series === 'g4') {
        const res2 = data.data.modelLineOption.filter((i) => i.status === 1 && i.optionTypeName)
        // let _mockData2 = res2.reduce((pre, current) => {
        //   const bool = pre.find((j) => j.optionCode === current.optionCode && current.optionCode !== '')
        //   if (!bool) pre.push(current)
        //   return pre
        // }, [])
        const _mockData2 = res2.filter((e) => e.optionTypeName.startsWith('e-tron'))
        _mockData2.sort((a, b) => a.optionTypeName - b.optionTypeName)
        console.log('_mockData2', _mockData2)
        let _obj = {
          name: _mockData2[0].optionTypeName,
          data: [],
          show: true
        }
        for (const val of _mockData2) {
          if (val.optionTypeName === _obj.name) {
            _obj.data.push(val)
          } else {
            this.mockData2.push(_obj)
            _obj = {
              name: val.optionTypeName,
              data: [],
              show: false
            }
            _obj.data.push(val)
          }
        }
        this.mockData2.push(_obj)
        console.log('_mockData22233', this.mockData2)
        const bool2 = this.mockData.find((i) => i.optionCode === this.numberKey)
        this.isInculdeNumberKey2 = !!bool2
      }
    },
    doWhellToTag(tag, str) {
      this.showPop = false
      this.currentTag = tag
      if (document.getElementById(str)) document.getElementById(str).scrollIntoView()
      const container = document.getElementById('common-view-wrapper')
      const target = this.$refs[tag]?.offsetTop ?? 0
      container.scrollTo({
        top: target - 160,
        behavior: 'smooth'
      })
    },
    // 获取导航栏高度??
    async getHeight() {
      const data = await callNative('navigationBarHeight', {})
      if (!data) return
      const that = this
      console.log('navigationBarHeight', data)
      that.statusBarHeight = data.statusBarHeight
      // 导航栏高度
      that.navigationBarHeight = data.navigationBarHeight
    },
    SetSensorsTrackData(name = '') {
      const { fromText } = this
      const { carModelName, from } = this.$route.query
      // eslint-disable-next-line camelcase
      const car_model = `${carModelName || this.carModelName}`
      // eslint-disable-next-line camelcase
      const H5_CarConfiguration = {
        source_module: 'H5',
        refer_page: fromText[from] || '报价单',
        car_model
      }

      console.log(`%c [ ${name} ]-212`, 'font-size:14px; background:#cf222e; color:#fff;', H5_CarConfiguration)
      this.$sensors.track(name, H5_CarConfiguration)
    }
  },
  beforeRouteLeave(to, from, next) {
    console.log('%c [ beforeRouteLeave ]-411', 'font-size:14px; background:#cf222e; color:#fff;', from)
    if (from.name === 'carConfigTable') {
      this.SetSensorsTrackData('H5_CarConfiguration_Exit')
    }
    next()
  }
}
</script>

  <style scoped lang="less">
    @import "../../assets/style/common.less";

    .fixed-box {
      // position: fixed;
      // left: 0;
      // right: 0;
      // top: 0;
      display: flex;
      justify-content: space-between;
      flex-flow: column;
      align-items: center;
      width: 100%;
      box-sizing: border-box;
      background: white;

      .fixed-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding: 26px 16px 16px;
        width: 100%;
        z-index: 1024;
        background: #fff;

        .filter-box {
          display: flex;
          align-items: center;

          img {
            width: 22px;
            height: 22px;
            margin-right: 15px;
          }
        }
      }

      .pop-wrapper {
        z-index: 200;
        width: 100%;
        padding: 16px 0;
        background: #fff;
        box-sizing: border-box;

        .btn-wrapper {
          margin: 0 20px;
          width: 100px;
        }
      }

    }

    .underline {
      border-bottom: 1px solid #e5e5e5;
    }

    .text-wrapper {
      .c-font14;
      color: #999999;
      padding: 10px 16px;
    }

    .black-cover {
      position: fixed;
      left: 0;
      bottom: 0;
      right: 0;
      z-index: 100;
      background: rgba(0, 0, 0, .5)
    }

    .myContainer {
      position: relative;
      overflow-y: auto;
    }

    .item {
      .c-flex-center;

      // height: 40px;
      > .name {
        width: 30%;
        border-right: 1px solid #e5e5e5;
        padding: 10px 0;
        padding-left: 20px;
      }

      .desc {
        flex: 1;
        text-align: center;
      }

      &:nth-of-type(odd) {
        background-color: #f2f2f2;
      }
    }

    .circle {
      height: 10px;
      width: 10px;
      border: 1px solid;
      box-sizing: border-box;
      border-radius: 50%;
      margin: 0 auto;

      &.selected {
        background-color: #000;
      }
    }

    .standard-box {
      width: 100%;
      padding: 0 18px 18px;
      display: flex;
      flex-flow: column;
      box-sizing: border-box;

      ._title {
        width: 100%;
        padding: 5px 0;
        font-size: 16px;
        font-family: Audi-WideBold;
        line-height: 24px;
      }

      ._item {
        padding: 12px 0;
        width: 100%;
        display: flex;
        align-items: center;
        box-sizing: border-box;

        ._img {
          width: 50px;

          img {
            width: 50px;
            height: 50px;
            object-fit: cover;
          }
        }

        ._desc {
          width: calc(100% - 50px);
          // height: 50px;
          padding: 5px 17px;
          box-sizing: border-box;
          display: flex;
          flex-flow: column;

          ._text1 {
            width: 100%;
            font-size: 14px;
            font-family: Audi-Normal;
            color: #000000;
            line-height: 14px;
            box-sizing: border-box;
            margin: 0;
          }

          ._text2 {
            font-size: 12px;
            color: #999999;
            line-height: 14px;
            box-sizing: border-box;
          }
        }
      }
    }

    .q5e-box{
      padding: 0 18px 18px 18px;
      .item {
        background-color: #FFFFFF;
        // &:nth-of-type(odd) {
        //   background-color: #FFFFFF;
        // }

        // &:nth-of-type(even) {
        //   background-color: #f2f2f2;
        // }
      }

      ._title {
        padding: 18px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        img{
          display: block;
          width: 24px;height: 24px;
          transform: rotate(-90deg);
          &.r90{
            transform: rotate(90deg);
          }
        }
      }

      ._item {
        padding: 12px 0;
        width: 100%;
        display: flex;
        align-items: center;
        box-sizing: border-box;

        ._img {
          width: 50px;

          img {
            width: 50px;
            height: 50px;
            object-fit: cover;
          }
        }

        ._desc {
          width: calc(100% - 50px);
          // height: 50px;
          padding: 5px 17px;
          box-sizing: border-box;
          display: flex;
          flex-flow: column;

          ._text1 {
            width: 100%;
            font-size: 14px;
            font-family: Audi-Normal;
            color: #000000;
            line-height: 14px;
            box-sizing: border-box;
            margin: 0;
          }

          ._text2 {
            font-size: 12px;
            color: #999999;
            line-height: 14px;
            box-sizing: border-box;
          }
        }
      }
    }
    /deep/ .width1 {
    width: 110px;
  }
  /deep/ .width0 {
    width: 215px;
  }
  </style>
  <style lang="less" scoped>
  /deep/ .van-tabs__line {
    background-color: black
  }
  </style>
