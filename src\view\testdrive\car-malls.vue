<template>
  <div
    class="container"
    style="position: relative"
  >
    <!-- <navigation :back-type="backType"> </navigation> -->
    <van-search
      v-model.trim="dealerName"
      placeholder="请输入门店名称"
      @search="onSearch"
      @clear="onCancel"
    />
    <div>
      <div class="box-field jt">
        <label class="box-label">省份城市</label>
        <van-field
          label-align="left"
          input-align="center"
          :label-width="120"
          clickable
          readonly
          placeholder="请选择城市"
          v-model="formData.carLicenseCityName"
          @click="doShowCarCity"
        />
        <van-icon name="arrow" />
        <van-popup
          v-model="formDataShow.showCarCity"
          round
          position="bottom"
        >
          <van-picker
            ref="vanPicker"
            v-model="cascaderValue"
            title="省份城市"
            show-toolbar
            :default-index="cityDefultIndex"
            :columns="provinceList"
            @cancel="cityCancel"
            @confirm="cityConfirm"
          />
        </van-popup>
      </div>
    </div>


    <div class="line">
      <div
        class="title"
        style="font-weight: bold"
      >
        {{ "共找到" + addressAllList.length + "条结果" }}
      </div>
    </div>

    <div v-if="visible">
      <div
        v-for="(item, idx) in addressAllList"
        @click="onChange(item, idx)"
        :key="idx"
      >
        <div class="item-wrapper">
          <div class="content-wrapper">
            <div
              class="c-font16 c-bold"
              v-html="`0${idx + 1 + ' ' + showDealerName(item)}`"
            />
            <div
              class="c-font12"
              style="margin-top: 10px;line-height: 20px; color: #666"
            >
              <span
                style="padding-right: 3px"
                v-if="item.distance"
              >
                {{ `${(item.distance / 1000).toFixed(1)}km  | ` }}
              </span>
              {{ item.dealerAdrress }}
            </div>
          </div>
        </div>
      </div>
      <!-- 增加一个view 防止门店类型展开时被遮挡问题-->
      <div
        v-if="addressAllList.length === 0"
        style="height: 224px"
      >
        {{ " " }}
      </div>
    </div>
  </div>
</template>

<script>
import { Promise } from 'q'
import Vue from 'vue'
import {
  ActionSheet, Picker, Popup, Search, Field
} from 'vant'
import {
  getOrgList, getProvinceList, getAgentCityList, getNearestDealerList, getDealerListByTestDrive
} from '@/api/api'
import baseUrl from '@/config/url'
import {
  getdiscount2, callNative, getLocationCityName, delay
} from '@/utils'
import { mapState } from 'vuex'
import navigation from '../../components/navigation.vue'
import { getCity } from '../../api/detail'


Vue.use(ActionSheet).use(Picker).use(Popup).use(Search)
  .use(Field)

const latitude = '31.2304'
const longitude = '121.4737'
const codeType = ['00', '200']

export default {
  components: { navigation },
  name: 'Name',
  data() {
    return {
      fromType: this.$route.query?.fromType === 'fromPurple',
      visible: true,
      areaList: [],
      cityDefultIndex: 0,
      cascaderValue: '',
      formDataShow: {
        inclinedModelPicker: false,
        showPicker: false,
        showCarCity: false,
        showPicker1: false,
        showPicker2: false,
        showPicker3: false,
        monthDay: false,
        hourMinute: false
      },
      formData: {
        inclinedModel: '2.0T',
        testdriveSeries: '', // 试驾车系 试驾车型
        testdriveMode: { text: '试乘试驾', id: 1 }, // 试驾方式 columns1
        testdrivePlace: { id: '76631019', text: '展厅' }, // 预约地点
        agent: '', // 代理商
        fullName: '',
        remarks: '',
        monthDay: '',
        monthDayValue: 0,
        hourMinute: '',
        mobile: '',
        carLicenseCityName: '', // 省市区
        inviteUserMobile: ''
      },
      dealerName: '', // 搜索内容
      ossUrl: baseUrl.BaseOssHost,
      addressAllList: [], // 全部地址
      location: '', // 模拟本地的经纬度信息.
      map: null,

      backType: 'app',
      isShowMap: true,

      initCityName: '', // 初始进来的城市

      provinceList: [], // 省份列表
      provinceCode: '', // 省份代码
      provinceName: '', // 省份名称
      cityList: [], // 城市列表
      cityCode: '', // 城市代码
      cityName: '', // 城市名称
      isShowCityList: false, // 显示城市选择
      isShowProvinceList: false, // 显示省份选择
      storeTypeList: [
        { searchType: 1, searchName: '授权服务商' },
        { searchType: 2, searchName: '授权代理商' }
      ],
      searchType: 0, // 授权类型（0:全部 1:服务商 2:代理商
      searchName: '',
      isShowStoreTypeList: false,
      markerLists: [],
      env: '',
      cityInfo: [],
      cityBox: {}
    }
  },
  computed: {
    ...mapState({
      wxLocation: 'wxLocation',
      userLocation: 'userLocation'
    })
  },

  async mounted() {
    const { env, positioning } = this.$route.query
    this.env = env
    let isLoadingDealerList = false
    if (env != 'minip') {
      if (positioning?.length && positioning.indexOf(',') !== -1) {
        const [location, city] = positioning.split('|')
        this.location = positioning
        this.cityBox = { city, location }
        this.location = location
        isLoadingDealerList = true
        console.log('%c [ this.location ]-200', 'font-size:14px; background:#cf222e; color:#fff;', this.location, this.cityBox)
      } else {
        const LOCATION_TIMEOUT_MS = 3000
        const city = ''
        const location = '0.0,0.0'
        delay(() => {
          if (!city || !location) {
            console.log(`%c [ 获取用户定位超时 => ${LOCATION_TIMEOUT_MS} MS ]-94`, 'font-size:14px; background:#fc011a; color:#fff;')
          }
          if (!isLoadingDealerList) {
            this.getDealerListByTestDriveFn()
            isLoadingDealerList = true
          }
        }, LOCATION_TIMEOUT_MS)
        const data = await callNative('getLocationCity', {})

        console.log('%c [ getLocationCity ]-196', 'font-size:14px; background:#cf222e; color:#fff;', data)
        if (!isLoadingDealerList) {
          this.cityBox = data
          this.location = data.location
        }
      }
    } else {
      this.location = this.wxLocation
    }

    console.log('定位信息car-malls页面：', this.location)
    // if (this.location !== '' && this.location !== ',' && this.location.length > 3) {
    isLoadingDealerList && this.getDealerListByTestDriveFn()
    // }


    this.getAllCityList()
  },
  methods: {
    async getDealerListByTestDriveFn(e = '') {
      let p = {
        defaultHeadquarters: 0, // 是否默认返回总部（1:是 0:否）
        provinceCode: this.provinceCode,
        cityCode: this.cityCode,
        searchType: this.searchType,
        dealerName: this.dealerName.trim()
      }
      console.log('this.location', this.location)
      if (this.location && this.location?.length > 3) {
        const location = this.location?.split(',')
        console.log('location', location)
        const params = {}
        // params.latitude = location[0]
        // params.longitude = location[1]

        if (this.env != 'minip') {
          params.latitude = location[0]
          params.longitude = location[1]
        } else {
          params.latitude = location[1]
          params.longitude = location[0]
        }

        p = {
          defaultHeadquarters: 0,
          latitude: params.latitude,
          longitude: params.longitude, // 经度
          provinceCode: this.provinceCode,
          cityCode: this.cityCode,
          searchType: this.searchType,
          dealerName: this.dealerName.trim()
        }
      }

      console.log('代理商列表入参：', p)
      const res = await getDealerListByTestDrive(p)
      if (this.location?.length > 7 && res.data) {
        const data = {}
        let city = ''
        if (this.env != 'minip') {
          city = this.cityBox?.city
        } else {
          const cityName = await getLocationCityName(this.wxLocation.split(','))
          console.log('minip cityName', cityName)
          if (cityName) city = cityName
        }


        console.log('malls定位信息', city)
        const arr = res.data.data.filter((e) => city == e.cityName)
        console.log('<<<< arr', arr)
        if (arr.length > 0) {
          this.addressAllList = arr
        } else {
          this.addressAllList = res?.data?.data || []
        }
        console.log('获取定位相关的代理商', this.addressAllList)
      } else {
        this.addressAllList = res?.data?.data || []
      }

      if ((this.location && this.location !== '0.0,0.0') && this.addressAllList[0]) {
        const { provinceName, cityName } = this.addressAllList[0]
        this.formData.carLicenseCityName = `${provinceName}/${cityName}`
      }
    },
    cityConfirm(value, index) {
      console.log('value', value, index)
      this.formDataShow.showCarCity = false
      this.formData.carLicenseCityName = `${value[0]}/${value[1]}`
      this.cityInfo = [this.areaList[index[0]].code, this.areaList[index[0]].children[index[1]].code]
      this.provinceCode = this.cityInfo[0]
      this.cityCode = this.cityInfo[1]
      this.dealerName = ' '
      this.getDealerListByTestDriveFn(1)
      // this.getStoreList()
      console.log(this.cityInfo)
    },
    cityCancel() {
      this.formDataShow.showCarCity = false
    },
    doShowCarCity() {
      this.formDataShow.showCarCity = true
    },
    // 显示门店名称
    showDealerName(item) {
      if (this.dealerName && this.dealerName.length > 0) {
        // 匹配关键字正则
        const replaceReg = new RegExp(this.dealerName, 'g')
        console.log('replaceReg', replaceReg)
        // 高亮替换v-html值
        const replaceString = `<span style="color: #F50537; font-size: 16px">${
          this.dealerName
        }</span>`
        console.log('replaceString', replaceString)

        return item.dealerName.replace(replaceReg, replaceString)
      }
      return item.dealerName
    },
    // 搜索
    onSearch() {
      this.provinceCode = ''
      this.cityCode = ''
      // this.getSearchStoreList();
      this.getDealerListByTestDriveFn()
    },
    async getSearchStoreList() {
      this.$store.commit('showLoading')
      const { data } = await getOrgList({
        provinceCode: this.dealerName ? '' : this.provinceCode,
        cityCode: this.dealerName ? '' : this.cityCode,
        searchType: this.dealerName ? '' : this.searchType,
        dealerName: this.dealerName
      })
      this.setData(data)
    },
    onCancel() {
      this.dealerName = ''
      this.getDealerListByTestDriveFn()
    },
    // 初始化进来获取全部城市
    async getAllCityList() {
      const { data } = await getAgentCityList({ searchAll: 3 })
      this.cityList = data.data
      console.log('初始化进来获取全部城市', data)
      this.getProvinceList()
    },
    // 获取省份
    async getProvinceList() {
      const { data } = await getProvinceList({ searchAll: 3 })
      this.provinceList = data.data


      this.provinceList.forEach((item) => {
        item.children = []
      })
      this.provinceList.forEach((item) => {
        item.text = item.name
        item.code = item.provinceCode
        this.cityList.forEach((e) => {
          e.text = e.name
          e.code = e.cityCode
          if (e.provinceCode == item.provinceCode) {
            item.children.push(e)
          }
        })
      })
      this.areaList = this.provinceList
      console.log('默认查询存在代理商的省市数据', this.provinceList)
    },
    onChange(item, i) {
      const { dealerCode, dealerName } = item
      console.log('111111111111', item)
      if (!dealerName) {
        return
      }
      this.$router.push({
        path: this.fromType ? '/testdrive/create?fromType=fromPurple' : '/testdrive/create',
        query: {
          dealerCodeBox: dealerCode,
          dealerNameBox: dealerName,
          idx: this.$route.query.idx,
          seriesCode: this.$route.query.seriesCode,
          form: 'carMalls',
          jumpType:this.$route.query.jumpType
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/common.less";

/deep/ .van-search__content {
  background-color: #f2f2f2;
}

/deep/ .van-cell::after {
  border: none;
}
.container {
  width: 100vw;
}
.item_btn {
  text-align: center;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 8px;
  padding-left: 3px;
  padding-right: 3px;
  height: 16px;
  background: #000;
  margin-top: 5px;
  margin-right: 5px;
}

.item-wrapper {
  .c-flex-between;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 10px;
  padding-top: 10px;
}

.content-wrapper {
  .c-flex-between;
  flex-direction: column;
  margin: 0 15px;
  padding: 5px 0;
}

.navgation-wrapper {
  .c-font14;
  .c-flex-center;
  color: #b2b2b2;
  text-align: center;

  .nav-icon {
    width: 24px;
    margin: 0 auto 5px auto;
  }
}

.line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f2f2f2;

  .btn-change {
    display: flex;
    align-items: center;

    .title {
      width: 90px;
      font-size: 16px;
      color: #000;
      margin: 5px 5px;
    }

    .change-name {
      font-size: 16px;
      color: #000;
      margin-right: 10px;
    }
  }

  .btn-icon {
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #000;
  }
}

._scroll {
  overflow: scroll;
  max-height: 184px;
}

.actionSheet {
  position: absolute;
  box-sizing: border-box;
  z-index: 9999;
  width: 100%;

  left: 0;
  background-color: #f2f2f2;
  //   border: 1px solid #000000;
  padding: 0 10px;
  display: flex;
  flex-flow: column;

  .text1 {
    font-size: 16px;
    color: #333;
    padding-left: 10px;
    line-height: 40px;
    border-bottom: 1px solid #e5e5e5;
  }
}

/deep/.my-svg-marker .amap-simple-marker-label {
  color: #fff;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/deep/.van-popup {
  border-radius: 0;
  // height: 400px;
  font-size: 14px;

  .carCitypicker {
    position: relative;
    z-index: 2333;
    transform: translateY(80px);
    text-align: center;
    width: 100%;
    display: flex;
    background-color: #fff;
    top: -20px;
    div {
      flex: 1;
    }
  }

  .van-picker__columns {
    // top: 80px;

    .van-hairline-unset--top-bottom {
      border-bottom: 1px solid #eee;
      border-top: 1px solid #eee;
    }

    .van-picker-column {
      font-size: 14px;
    }
  }
}

/deep/.box-field {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 56px;

    .box-label {
      width: 25%;
      font-size: 16px;
      color: #000;
      position: absolute;
      line-height: 20px;
      left: 12px;
      top: 8px;
      z-index: 999;
    }

    .van-cell {
      position: relative;
      height: 100%;
      &:active{
        background-color: #fff;
      }

      .van-cell__value {
        .van-field__body {
          // min-height: calc(100% - 25px);
          border-bottom: 1px solid #e5e5e5;
          font-size: 16px;
          overflow: visible;
          flex-direction: column;
          // justify-content: flex-end;
          align-items: flex-start;
          position: relative;
          top: 6px;

          &.border-none {
            border: none;
          }

          input {
            min-height: 20px !important;
            line-height: 20px;
            padding-top: 1px;
            width: 100%;
            position: relative;
            top: -8px;
          }

          input::-webkit-input-placeholder {
            font-size: 16px;
          }

          textarea {
            line-height: 20px;
            min-height: 30px;
            width: 100%;
            position: relative;
            top: -6px;
          }

          textarea::-webkit-input-placeholder {
            font-size: 16px;
          }
        }
      }
    }

    ._remarks {
      .van-cell__value {
        .van-field__body {
          border-bottom: none !important;
        }
      }
    }

    .van-field--error {
      .van-field__body {
        border-bottom: 1px solid #9e1f32 !important;
      }

      .van-field__error-message {
        color: #9e1f32;
        position: relative;
        top: 6px;
      }
    }
  }

  .jt {
    position: relative;

    i {
      position: absolute;
      right: 10px;
      top: 12px;
      color: #999;
    }

    input {
      font-size: 16px;
      padding-right: 20px;
    }
  }
</style>
